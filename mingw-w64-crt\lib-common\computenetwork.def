;
; Definition file of computenetwork.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "computenetwork.dll"
EXPORTS
HcnCloseGuestNetworkService
HcnCloseSdnRoute
HcnCreateGuestNetworkService
HcnCreateSdnRoute
HcnDeleteGuestNetworkService
HcnDeleteSdnRoute
HcnEnumerateGuestNetworkServices
HcnEnumerateSdnRoutes
HcnModifyGuestNetworkService
HcnModifySdnRoute
HcnOpenGuestNetworkService
HcnOpenSdnRoute
HcnQueryGuestNetworkServiceProperties
HcnQuerySdnRouteProperties
HcnRegisterGuestNetworkServiceCallback
HcnRegisterNetworkCallback
HcnUnregisterGuestNetworkServiceCallback
HcnUnregisterNetworkCallback
HcnCloseEndpoint
HcnCloseLoadBalancer
HcnCloseNamespace
HcnCloseNetwork
HcnCreateEndpoint
HcnCreate<PERSON>alancer
HcnCreateNamespace
HcnCreateNetwork
HcnDeleteEndpoint
HcnDeleteLoadBalancer
HcnDeleteNamespace
HcnDeleteNetwork
HcnEnumerateEndpoints
HcnEnumerateGuestNetworkPortReservations
HcnEnumerateLoadBalancers
HcnEnumerateNamespaces
HcnEnumerateNetworks
HcnFreeGuestNetworkPortReservations
HcnModifyEndpoint
HcnModifyLoadBalancer
HcnModifyNamespace
HcnModifyNetwork
HcnOpenEndpoint
HcnOpenLoadBalancer
HcnOpenNamespace
HcnOpenNetwork
HcnQueryEndpointAddresses
HcnQueryEndpointProperties
HcnQueryEndpointStats
HcnQueryLoadBalancerProperties
HcnQueryNamespaceProperties
HcnQueryNetworkProperties
HcnRegisterServiceCallback
HcnReleaseGuestNetworkServicePortReservationHandle
HcnReserveGuestNetworkServicePort
HcnReserveGuestNetworkServicePortRange
HcnUnregisterServiceCallback
