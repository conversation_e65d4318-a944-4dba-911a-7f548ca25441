/*
 * Background Intelligent Transfer Service (BITS) interface
 *
 * Copyright 2007 Google (<PERSON>)
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
#endif

cpp_quote("#include \"bitsmsg.h\"")
cpp_quote("#define BG_SIZE_UNKNOWN (UINT64)(-1)")
cpp_quote("#define BG_NOTIFY_JOB_TRANSFERRED 0x0001")
cpp_quote("#define BG_NOTIFY_JOB_ERROR 0x0002")
cpp_quote("#define BG_NOTIFY_DISABLE 0x0004")
cpp_quote("#define BG_NOTIFY_JOB_MODIFICATION 0x0008")
cpp_quote("#define BG_NOTIFY_FILE_TRANSFERRED 0x0010")

cpp_quote("#ifdef WINE_NO_UNICODE_MACROS")
cpp_quote("#undef EnumJobs")
cpp_quote("#undef GetJob")
cpp_quote("#endif")

#define BG_ENUM_SIZEIS(maxcount) maxcount
#define BG_ENUM_LENGTHIS(maxcount,lengthptr) lengthptr ? *lengthptr : maxcount

[
    uuid(01b7bd23-fb88-4a77-8490-5891d3e4653a),
    odl
]
interface IBackgroundCopyFile : IUnknown
{
    typedef struct _BG_FILE_PROGRESS {
        UINT64 BytesTotal;
        UINT64 BytesTransferred;
        BOOL Completed;
    } BG_FILE_PROGRESS;

    HRESULT GetRemoteName(
            [out] LPWSTR *pVal
    );

    HRESULT GetLocalName(
            [out] LPWSTR *pVal
    );

    HRESULT GetProgress(
            [out] BG_FILE_PROGRESS *pVal
    );
}


[
    uuid(ca51e165-c365-424c-8d41-24aaa4ff3c40),
    odl
]
interface IEnumBackgroundCopyFiles: IUnknown
{
    HRESULT Next(
            [in] ULONG celt,
            [out,size_is(BG_ENUM_SIZEIS(celt)),length_is(BG_ENUM_LENGTHIS(celt,pceltFetched))] IBackgroundCopyFile **rgelt,
            [in,out,unique] ULONG *pceltFetched
    );

    HRESULT Skip(
            [in] ULONG celt
    );

    HRESULT Reset();

    HRESULT Clone(
            [out] IEnumBackgroundCopyFiles **ppenum
    );

    HRESULT GetCount(
            [out] ULONG *puCount
    );
}


[
    uuid(19c613a0-fcb8-4f28-81ae-897c3d078f81),
    odl
]
interface IBackgroundCopyError : IUnknown
{
    typedef enum {
        BG_ERROR_CONTEXT_NONE = 0,
        BG_ERROR_CONTEXT_UNKNOWN = 1,
        BG_ERROR_CONTEXT_GENERAL_QUEUE_MANAGER = 2,
        BG_ERROR_CONTEXT_QUEUE_MANAGER_NOTIFICATION =3,
        BG_ERROR_CONTEXT_LOCAL_FILE = 4,
        BG_ERROR_CONTEXT_REMOTE_FILE = 5,
        BG_ERROR_CONTEXT_GENERAL_TRANSPORT = 6,
        BG_ERROR_CONTEXT_REMOTE_APPLICATION =7,
    } BG_ERROR_CONTEXT;

    HRESULT GetError(
            [out,ref] BG_ERROR_CONTEXT *pContext,
            [out,ref] HRESULT *pCode
    );

    HRESULT GetFile(
            [out] IBackgroundCopyFile **pVal
    );

    HRESULT GetErrorDescription(
            [in] DWORD LanguageId,
            [out,ref] LPWSTR *pErrorDescription
    );

    HRESULT GetErrorContextDescription(
            [in] DWORD LanguageId,
            [out,ref] LPWSTR *pContextDescription
    );

    HRESULT GetProtocol(
            [out,ref] LPWSTR *pProtocol
    );
}



[
    uuid(37668d37-507e-4160-9316-26306d150b12),
    odl
]
interface IBackgroundCopyJob : IUnknown
{
    typedef struct _BG_FILE_INFO {
        LPWSTR RemoteName;
        LPWSTR LocalName;
    } BG_FILE_INFO;

    typedef struct _BG_JOB_PROGRESS {
        UINT64 BytesTotal;
        UINT64 BytesTransferred;
        ULONG FilesTotal;
        ULONG FilesTransferred;
    } BG_JOB_PROGRESS;

    typedef struct _BG_JOB_TIMES {
        FILETIME CreationTime;
        FILETIME ModificationTime;
        FILETIME TransferCompletionTime;
    } BG_JOB_TIMES;

    typedef enum {
        BG_JOB_PRIORITY_FOREGROUND = 0,
        BG_JOB_PRIORITY_HIGH = 1,
        BG_JOB_PRIORITY_NORMAL = 2,
        BG_JOB_PRIORITY_LOW = 3
    } BG_JOB_PRIORITY;

    typedef enum {
        BG_JOB_STATE_QUEUED = 0,
        BG_JOB_STATE_CONNECTING = 1,
        BG_JOB_STATE_TRANSFERRING = 2,
        BG_JOB_STATE_SUSPENDED = 3,
        BG_JOB_STATE_ERROR = 4,
        BG_JOB_STATE_TRANSIENT_ERROR = 5,
        BG_JOB_STATE_TRANSFERRED = 6,
        BG_JOB_STATE_ACKNOWLEDGED = 7,
        BG_JOB_STATE_CANCELLED = 8
    } BG_JOB_STATE;

    typedef enum {
       BG_JOB_TYPE_DOWNLOAD = 0,
       BG_JOB_TYPE_UPLOAD = 1,
       BG_JOB_TYPE_UPLOAD_REPLY = 2
    } BG_JOB_TYPE;

    typedef enum {
        BG_JOB_PROXY_USAGE_PRECONFIG,
        BG_JOB_PROXY_USAGE_NO_PROXY,
        BG_JOB_PROXY_USAGE_OVERRIDE,
        BG_JOB_PROXY_USAGE_AUTODETECT
    } BG_JOB_PROXY_USAGE;


    HRESULT AddFileSet(
            [in] ULONG cFileCount,
            [in,size_is(cFileCount)] BG_FILE_INFO *pFileSet
    );

    HRESULT AddFile(
            [in] LPCWSTR RemoteUrl,
            [in] LPCWSTR LocalName
    );

    HRESULT EnumFiles(
            [out] IEnumBackgroundCopyFiles **pEnum
    );

    HRESULT Suspend();

    HRESULT Resume();

    HRESULT Cancel();

    HRESULT Complete();

    HRESULT GetId(
            [out] GUID *pVal
    );

    HRESULT GetType(
            [out] BG_JOB_TYPE *pVal
    );

    HRESULT GetProgress(
            [out] BG_JOB_PROGRESS *pVal
    );

    HRESULT GetTimes(
            [out] BG_JOB_TIMES *pVal
    );

    HRESULT GetState(
            [out] BG_JOB_STATE *pVal
    );

    HRESULT GetError(
            [out] IBackgroundCopyError **ppError
    );

    HRESULT GetOwner(
            [out] LPWSTR *pVal
    );

    HRESULT SetDisplayName(
            [in] LPCWSTR Val
    );

    HRESULT GetDisplayName(
            [out] LPWSTR *pVal
    );

    HRESULT SetDescription(
            [in] LPCWSTR Val
    );

    HRESULT GetDescription(
            [out] LPWSTR *pVal
    );

    HRESULT SetPriority(
            [in] BG_JOB_PRIORITY Val
    );

    HRESULT GetPriority(
            [out] BG_JOB_PRIORITY *pVal
    );

    HRESULT SetNotifyFlags(
            [in] ULONG Val
    );

    HRESULT GetNotifyFlags(
            [out] ULONG *pVal
    );

    HRESULT SetNotifyInterface(
            [in] IUnknown *Val
    );

    HRESULT GetNotifyInterface(
            [out] IUnknown ** pVal
    );

    HRESULT SetMinimumRetryDelay(
            [in] ULONG Seconds
    );

    HRESULT GetMinimumRetryDelay(
            [out] ULONG *Seconds
    );

    HRESULT SetNoProgressTimeout(
            [in] ULONG Seconds
    );

    HRESULT GetNoProgressTimeout(
            [out] ULONG *Seconds
    );

    HRESULT GetErrorCount(
            [out] ULONG *Errors
    );

    HRESULT SetProxySettings(
            [in] BG_JOB_PROXY_USAGE ProxyUsage,
            [in,string,unique] const WCHAR *ProxyList,
            [in,string,unique] const WCHAR *ProxyBypassList
    );

    HRESULT GetProxySettings(
            [out] BG_JOB_PROXY_USAGE *pProxyUsage,
            [out] LPWSTR *pProxyList,
            [out] LPWSTR *pProxyBypassList
    );

    HRESULT TakeOwnership();
}

[
    uuid(1af4f612-3b71-466f-8f58-7b6f73ac57ad),
    odl
]
interface IEnumBackgroundCopyJobs : IUnknown
{
    HRESULT Next(
            [in] ULONG celt,
            [out,size_is(BG_ENUM_SIZEIS(celt)),length_is(BG_ENUM_LENGTHIS(celt,pceltFetched))] IBackgroundCopyJob **rgelt,
            [in,out,unique] ULONG *pceltFetched
    );

    HRESULT Skip(
            [in] ULONG celt
    );

    HRESULT Reset();

    HRESULT Clone(
            [out] IEnumBackgroundCopyJobs **ppenum
    );

    HRESULT GetCount(
            [out] ULONG *puCount
    );
}


[
    uuid(97ea99c7-0186-4ad4-8df9-c5b4e0ed6b22),
    odl
]
interface IBackgroundCopyCallback : IUnknown
{
    HRESULT JobTransferred(
            [in] IBackgroundCopyJob *pJob
    );

    HRESULT JobError(
            [in] IBackgroundCopyJob *pJob,
            [in] IBackgroundCopyError *pError
    );

    HRESULT JobModification(
            [in] IBackgroundCopyJob *pJob,
            [in] DWORD dwReserved
    );
}

[
    uuid(5ce34c0d-0dc9-4c1f-897c-daa1b78cee7c),
    odl
]
interface IBackgroundCopyManager : IUnknown
{
    cpp_quote("#define BG_JOB_ENUM_ALL_USERS 0x0001")

    HRESULT CreateJob(
            [in] LPCWSTR DisplayName,
            [in] BG_JOB_TYPE Type,
            [out] GUID *pJobId,
            [out] IBackgroundCopyJob **ppJob
    );

    HRESULT GetJob(
            [in] REFGUID jobID,
            [out] IBackgroundCopyJob **ppJob
    );

    HRESULT EnumJobs(
            [in] DWORD dwFlags,
            [out] IEnumBackgroundCopyJobs **ppEnum
    );
    HRESULT GetErrorDescription(
            [in] HRESULT hResult,
            [in] DWORD LanguageId,
            [out] LPWSTR *pErrorDescription);
}


[
    uuid(1deeb74f-7915-4560-b558-918c83f176a6),
    version(1.0)
]
library BackgroundCopyManager
{
    [
        uuid(4991d34b-80a1-4291-83b6-3328366b9097),
    ]
    coclass BackgroundCopyManager
    {
        [default] interface IBackgroundCopyManager;
    };

    interface IBackgroundCopyCallback;
}

cpp_quote("#include \"bits1_5.h\"")
