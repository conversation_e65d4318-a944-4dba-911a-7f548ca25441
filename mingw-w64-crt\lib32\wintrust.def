;
; Definition file of WINTRUST.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WINTRUST.dll"
EXPORTS
CryptCATVerifyMember@12
CryptSIPGetInfo@4
CryptSIPGetRegWorkingFlags@4
GenericChainCertificateTrust@4
GenericChainFinal<PERSON>rov@4
HTTPSCertificateTrust@4
SoftpubDefCertInit@4
SoftpubFreeDefUsageCallData@8
SoftpubLoadDefUsageCallData@8
WTHelperCertFindIssuerCertificate@28
AddPersonalTrustDBPages@12
CatalogCompactHashDatabase@16
CryptCATAdminAcquireContext@12
CryptCATAdminAddCatalog@16
CryptCATAdminCalcHashFromFileHandle@16
CryptCATAdminEnumCatalogFromHash@20
CryptCATAdminPauseServiceForBackup@8
CryptCATAdminReleaseCatalogContext@12
CryptCATAdminRelease<PERSON><PERSON><PERSON><PERSON>@8
<PERSON>ptCATAdminRemoveCatalog@12
CryptCATAdminResolveCatalogPath@16
CryptCATAllocSortedMemberInfo@8
CryptCATCDFClose@4
CryptCATCDFEnumAttributes@16
CryptCATCDFEnumAttributesWithCDFTag@20
CryptCATCDFEnumCatAttributes@12
CryptCATCDFEnumMembers@12
CryptCATCDFEnumMembersByCDFTag@16
CryptCATCDFEnumMembersByCDFTagEx@24
CryptCATCDFOpen@8
CryptCATCatalogInfoFromContext@12
CryptCATClose@4
CryptCATEnumerateAttr@12
CryptCATEnumerateCatAttr@8
CryptCATEnumerateMember@8
CryptCATFreeSortedMemberInfo@8
CryptCATGetAttrInfo@12
CryptCATGetCatAttrInfo@8
CryptCATGetMemberInfo@8
CryptCATHandleFromStore@4
CryptCATOpen@20
CryptCATPersistStore@4
CryptCATPutAttrInfo@24
CryptCATPutCatAttrInfo@20
CryptCATPutMemberInfo@28
CryptCATStoreFromHandle@4
CryptSIPCreateIndirectData@12
CryptSIPGetSignedDataMsg@20
CryptSIPPutSignedDataMsg@20
CryptSIPRemoveSignedDataMsg@8
CryptSIPVerifyIndirectData@8
DllRegisterServer
DllUnregisterServer
DriverCleanupPolicy@4
DriverFinalPolicy@4
DriverInitializePolicy@4
FindCertsByIssuer@28
HTTPSFinalProv@4
IsCatalogFile@8
MsCatConstructHashTag@12
MsCatFreeHashTag@4
OfficeCleanupPolicy@4
OfficeInitializePolicy@4
OpenPersonalTrustDBDialog@4
OpenPersonalTrustDBDialogEx@12
SoftpubAuthenticode@4
SoftpubCheckCert@16
SoftpubCleanup@4
SoftpubDllRegisterServer
SoftpubDllUnregisterServer
SoftpubDumpStructure@4
SoftpubInitialize@4
SoftpubLoadMessage@4
SoftpubLoadSignature@4
TrustDecode@36
TrustFindIssuerCertificate@32
TrustFreeDecode@8
TrustIsCertificateSelfSigned@12
TrustOpenStores@16
WTHelperCertCheckValidSignature@4
WTHelperCertIsSelfSigned@8
WTHelperCheckCertUsage@8
WTHelperGetAgencyInfo@12
WTHelperGetFileHandle@4
WTHelperGetFileHash@24
WTHelperGetFileName@4
WTHelperGetKnownUsages@8
WTHelperGetProvCertFromChain@8
WTHelperGetProvPrivateDataFromChain@8
WTHelperGetProvSignerFromChain@16
WTHelperIsInRootStore@8
WTHelperOpenKnownStores@4
WTHelperProvDataFromStateData@4
WVTAsn1CatMemberInfoDecode@28
WVTAsn1CatMemberInfoEncode@20
WVTAsn1CatNameValueDecode@28
WVTAsn1CatNameValueEncode@20
WVTAsn1SpcFinancialCriteriaInfoDecode@28
WVTAsn1SpcFinancialCriteriaInfoEncode@20
WVTAsn1SpcIndirectDataContentDecode@28
WVTAsn1SpcIndirectDataContentEncode@20
WVTAsn1SpcLinkDecode@28
WVTAsn1SpcLinkEncode@20
WVTAsn1SpcMinimalCriteriaInfoDecode@28
WVTAsn1SpcMinimalCriteriaInfoEncode@20
WVTAsn1SpcPeImageDataDecode@28
WVTAsn1SpcPeImageDataEncode@20
WVTAsn1SpcSigInfoDecode@28
WVTAsn1SpcSigInfoEncode@20
WVTAsn1SpcSpAgencyInfoDecode@28
WVTAsn1SpcSpAgencyInfoEncode@20
WVTAsn1SpcSpOpusInfoDecode@28
WVTAsn1SpcSpOpusInfoEncode@20
WVTAsn1SpcStatementTypeDecode@28
WVTAsn1SpcStatementTypeEncode@20
WinVerifyTrust@12
WinVerifyTrustEx@12
WintrustAddActionID@12
WintrustAddDefaultForUsage@8
WintrustCertificateTrust@4
WintrustGetDefaultForUsage@12
WintrustGetRegPolicyFlags@4
WintrustLoadFunctionPointers@8
WintrustRemoveActionID@4
WintrustSetDefaultIncludePEPageHashes@4
WintrustSetRegPolicyFlags@4
mscat32DllRegisterServer
mscat32DllUnregisterServer
mssip32DllRegisterServer
mssip32DllUnregisterServer
