LIBRARY "ksecdd.sys"
EXPORTS
SystemPrng
AcceptSecurityContext
AcquireCredentialsHandleW
AddCredentialsW
ApplyControlToken
BCryptCloseAlgorithmProvider
BCryptCreateHash
BCryptDecrypt
BCryptDeriveKey
BCryptDeriveKeyCapi
BCryptDeriveKeyPBKDF2
BCryptDestroyHash
BCryptDestroyKey
BCryptDestroySecret
BCryptDuplicateHash
BCryptDuplicateKey
BCryptEncrypt
BCryptEnumAlgorithms
BCryptEnumProviders
BCryptExportKey
BCryptFinalizeKeyPair
BCryptFinishHash
BCryptFreeBuffer
BCryptGenRandom
BCryptGenerateKeyPair
BCryptGenerateSymmetricKey
BCryptGetFipsAlgorithmMode
BCryptGetProperty
BCryptHashData
BCryptImportKey
BCryptImportKeyPair
BCryptKeyDerivation
BCryptOpenAlgorithmProvider
BCryptRegisterConfigChangeNotify
BCryptResolveProviders
BCryptSecretAgreement
BCryptSetProperty
BCryptSignHash
BCryptUnregisterConfigChangeNotify
BCryptVerifySignature
CompleteAuthToken
CredMarshalTargetInfo
DeleteSecurityContext
EnumerateSecurityPackagesW
ExportSecurityContext
FreeContextBuffer
FreeCredentialsHandle
GetSecurityUserInfo
ImpersonateSecurityContext
ImportSecurityContextW
InitSecurityInterfaceW
InitializeSecurityContextW
KSecRegisterSecurityProvider
KSecValidateBuffer
LsaEnumerateLogonSessions
LsaGetLogonSessionData
MakeSignature
MapSecurityError
QueryContextAttributesW
QueryCredentialsAttributesW
QuerySecurityContextToken
QuerySecurityPackageInfoW
RevertSecurityContext
SealMessage
SecLookupAccountName
SecLookupAccountSid
SecLookupWellKnownSid
SecMakeSPN
SecMakeSPNEx
SecMakeSPNEx2
SecSetPagingMode
SetCredentialsAttributesW
SslDecryptPacket
SslEncryptPacket
SslExportKey
SslFreeObject
SslGetExtensions
SslGetServerIdentity
SslImportKey
SslLookupCipherSuiteInfo
SslOpenProvider
SspiAcceptSecurityContextAsync
SspiAcquireCredentialsHandleAsyncW
SspiCompareAuthIdentities
SspiCopyAuthIdentity
SspiCreateAsyncContext
SspiDeleteSecurityContextAsync
SspiEncodeAuthIdentityAsStrings
SspiEncodeStringsAsAuthIdentity
SspiFreeAsyncContext
SspiFreeAuthIdentity
SspiFreeCredentialsHandleAsync
SspiGetAsyncCallStatus
SspiInitializeSecurityContextAsyncW
SspiLocalFree
SspiMarshalAuthIdentity
SspiReinitAsyncContext
SspiSetAsyncNotifyCallback
SspiUnmarshalAuthIdentity
SspiValidateAuthIdentity
SspiZeroAuthIdentity
TokenBindingGetHighestSupportedVersion
TokenBindingGetKeyTypesServer
TokenBindingVerifyMessage
UnsealMessage
VerifySignature
