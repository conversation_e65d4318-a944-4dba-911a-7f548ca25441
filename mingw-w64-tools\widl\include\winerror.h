/*
 * Copyright (C) the Wine project
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef __WINE_WINERROR_H
#define __WINE_WINERROR_H

#include <specstrings.h>

#define FACILITY_NULL                         0
#define FACILITY_RPC                          1
#define FACILITY_DISPATCH                     2
#define FACILITY_STORAGE                      3
#define FACILITY_ITF                          4
#define FACILITY_WIN32                        7
#define FACILITY_WINDOWS                      8
#define FACILITY_SSPI                         9
#define FACILITY_SECURITY                     FACILITY_SSPI
#define FACILITY_CONTROL                     10
#define FACILITY_CERT                        11
#define FACILITY_INTERNET                    12
#define FACILITY_MEDIASERVER                 13
#define FACILITY_MSMQ                        14
#define FACILITY_SETUPAPI                    15
#define FACILITY_SCARD                       16
#define FACILITY_COMPLUS                     17
#define FACILITY_AAF                         18
#define FACILITY_URT                         19
#define FACILITY_ACS                         20
#define FACILITY_DPLAY                       21
#define FACILITY_UMI                         22
#define FACILITY_SXS                         23
#define FACILITY_WINDOWS_CE                  24
#define FACILITY_HTTP                        25
#define FACILITY_COMMONLOG                   26
#define FACILITY_USERMODE_FILTER_MANAGER     31
#define FACILITY_BACKGROUNDCOPY              32
#define FACILITY_CONFIGURATION               33
#define FACILITY_STATE_MANAGEMENT            34
#define FACILITY_METADIRECTORY               35
#define FACILITY_WINDOWSUPDATE               36
#define FACILITY_DIRECTORYSERVICE            37
#define FACILITY_GRAPHICS                    38
#define FACILITY_SHELL                       39
#define FACILITY_TPM_SERVICES                40
#define FACILITY_TPM_SOFTWARE                41
#define FACILITY_PLA                         48
#define FACILITY_FVE                         49
#define FACILITY_WINDOWS_DEFENDER            80
#define FACILITY_OPC                         81
#define FACILITY_DIRECT3D11                  0x87c
#define FACILITY_AUDCLNT                     0x889

#define SEVERITY_SUCCESS    0
#define SEVERITY_ERROR      1


#define MAKE_HRESULT(sev,fac,code) \
    ((HRESULT) (((unsigned int)(sev)<<31) | ((unsigned int)(fac)<<16) | ((unsigned int)(code))) )
#define MAKE_SCODE(sev,fac,code) \
        ((SCODE) (((unsigned int)(sev)<<31) | ((unsigned int)(fac)<<16) | ((unsigned int)(code))) )
#define SUCCEEDED(stat) ((HRESULT)(stat)>=0)
#define FAILED(stat) ((HRESULT)(stat)<0)
#define IS_ERROR(stat) (((unsigned int)(stat)>>31) == SEVERITY_ERROR)

#define HRESULT_CODE(hr) ((hr) & 0xFFFF)
#define SCODE_CODE(sc)   ((sc) & 0xFFFF)

#define HRESULT_FACILITY(hr)  (((hr) >> 16) & 0x1FFF)
#define SCODE_FACILITY(sc)  (((sc) >> 16) & 0x1FFF)

#define HRESULT_SEVERITY(hr)    (((hr) >> 31) & 0x1)
#define SCODE_SEVERITY(sc)      (((sc) >> 31) & 0x1)

#define __HRESULT_FROM_WIN32(x)   ((HRESULT)(x) > 0 ? ((HRESULT) (((x) & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000)) : (HRESULT)(x) )
#ifndef _HRESULT_DEFINED
#define _HRESULT_DEFINED
#if !defined(__LP64__) && !defined(WINE_NO_LONG_TYPES)
typedef long HRESULT;
#else
typedef int HRESULT;
#endif
#endif
static inline HRESULT HRESULT_FROM_WIN32(unsigned int x)
{
    return (HRESULT)x > 0 ? ((HRESULT) ((x & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000)) : (HRESULT)x;
}
#define FACILITY_NT_BIT         0x10000000
#define HRESULT_FROM_NT(x)      ((HRESULT) ((x) | FACILITY_NT_BIT))

/* SCODE <-> HRESULT functions */
/* This macros is obsolete and should not be used in new apps. */
#define GetScode(hr)         ((SCODE)(hr))
/* This macros is obsolete and should not be used in new apps. */
#define ResultFromScode(sc)  ((HRESULT)(sc))

#define NO_ERROR                                           0
#define ERROR_SUCCESS                                      0
#define ERROR_INVALID_FUNCTION                             1
#define ERROR_FILE_NOT_FOUND                               2
#define ERROR_PATH_NOT_FOUND                               3
#define ERROR_TOO_MANY_OPEN_FILES                          4
#define ERROR_ACCESS_DENIED                                5
#define ERROR_INVALID_HANDLE                               6
#define ERROR_ARENA_TRASHED                                7
#define ERROR_NOT_ENOUGH_MEMORY                            8
#define ERROR_INVALID_BLOCK                                9
#define ERROR_BAD_ENVIRONMENT                              10
#define ERROR_BAD_FORMAT                                   11
#define ERROR_INVALID_ACCESS                               12
#define ERROR_INVALID_DATA                                 13
#define ERROR_OUTOFMEMORY                                  14
#define ERROR_INVALID_DRIVE                                15
#define ERROR_CURRENT_DIRECTORY                            16
#define ERROR_NOT_SAME_DEVICE                              17
#define ERROR_NO_MORE_FILES                                18
#define ERROR_WRITE_PROTECT                                19
#define ERROR_BAD_UNIT                                     20
#define ERROR_NOT_READY                                    21
#define ERROR_BAD_COMMAND                                  22
#define ERROR_CRC                                          23
#define ERROR_BAD_LENGTH                                   24
#define ERROR_SEEK                                         25
#define ERROR_NOT_DOS_DISK                                 26
#define ERROR_SECTOR_NOT_FOUND                             27
#define ERROR_OUT_OF_PAPER                                 28
#define ERROR_WRITE_FAULT                                  29
#define ERROR_READ_FAULT                                   30
#define ERROR_GEN_FAILURE                                  31
#define ERROR_SHARING_VIOLATION                            32
#define ERROR_LOCK_VIOLATION                               33
#define ERROR_WRONG_DISK                                   34
#define ERROR_FCB_UNAVAILABLE                              35
#define ERROR_SHARING_BUFFER_EXCEEDED                      36
#define ERROR_HANDLE_EOF                                   38
#define ERROR_HANDLE_DISK_FULL                             39
#define ERROR_NOT_SUPPORTED                                50
#define ERROR_REM_NOT_LIST                                 51
#define ERROR_DUP_NAME                                     52
#define ERROR_BAD_NETPATH                                  53
#define ERROR_NETWORK_BUSY                                 54
#define ERROR_DEV_NOT_EXIST                                55
#define ERROR_TOO_MANY_CMDS                                56
#define ERROR_ADAP_HDW_ERR                                 57
#define ERROR_BAD_NET_RESP                                 58
#define ERROR_UNEXP_NET_ERR                                59
#define ERROR_BAD_REM_ADAP                                 60
#define ERROR_PRINTQ_FULL                                  61
#define ERROR_NO_SPOOL_SPACE                               62
#define ERROR_PRINT_CANCELLED                              63
#define ERROR_NETNAME_DELETED                              64
#define ERROR_NETWORK_ACCESS_DENIED                        65
#define ERROR_BAD_DEV_TYPE                                 66
#define ERROR_BAD_NET_NAME                                 67
#define ERROR_TOO_MANY_NAMES                               68
#define ERROR_TOO_MANY_SESS                                69
#define ERROR_SHARING_PAUSED                               70
#define ERROR_REQ_NOT_ACCEP                                71
#define ERROR_REDIR_PAUSED                                 72
#define ERROR_FILE_EXISTS                                  80
#define ERROR_CANNOT_MAKE                                  82
#define ERROR_FAIL_I24                                     83
#define ERROR_OUT_OF_STRUCTURES                            84
#define ERROR_ALREADY_ASSIGNED                             85
#define ERROR_INVALID_PASSWORD                             86
#define ERROR_INVALID_PARAMETER                            87
#define ERROR_NET_WRITE_FAULT                              88
#define ERROR_NO_PROC_SLOTS                                89
#define ERROR_TOO_MANY_SEMAPHORES                          100
#define ERROR_EXCL_SEM_ALREADY_OWNED                       101
#define ERROR_SEM_IS_SET                                   102
#define ERROR_TOO_MANY_SEM_REQUESTS                        103
#define ERROR_INVALID_AT_INTERRUPT_TIME                    104
#define ERROR_SEM_OWNER_DIED                               105
#define ERROR_SEM_USER_LIMIT                               106
#define ERROR_DISK_CHANGE                                  107
#define ERROR_DRIVE_LOCKED                                 108
#define ERROR_BROKEN_PIPE                                  109
#define ERROR_OPEN_FAILED                                  110
#define ERROR_BUFFER_OVERFLOW                              111
#define ERROR_DISK_FULL                                    112
#define ERROR_NO_MORE_SEARCH_HANDLES                       113
#define ERROR_INVALID_TARGET_HANDLE                        114
#define ERROR_INVALID_CATEGORY                             117
#define ERROR_INVALID_VERIFY_SWITCH                        118
#define ERROR_BAD_DRIVER_LEVEL                             119
#define ERROR_CALL_NOT_IMPLEMENTED                         120
#define ERROR_SEM_TIMEOUT                                  121
#define ERROR_INSUFFICIENT_BUFFER                          122
#define ERROR_INVALID_NAME                                 123
#define ERROR_INVALID_LEVEL                                124
#define ERROR_NO_VOLUME_LABEL                              125
#define ERROR_MOD_NOT_FOUND                                126
#define ERROR_PROC_NOT_FOUND                               127
#define ERROR_WAIT_NO_CHILDREN                             128
#define ERROR_CHILD_NOT_COMPLETE                           129
#define ERROR_DIRECT_ACCESS_HANDLE                         130
#define ERROR_NEGATIVE_SEEK                                131
#define ERROR_SEEK_ON_DEVICE                               132
#define ERROR_IS_JOIN_TARGET                               133
#define ERROR_IS_JOINED                                    134
#define ERROR_IS_SUBSTED                                   135
#define ERROR_NOT_JOINED                                   136
#define ERROR_NOT_SUBSTED                                  137
#define ERROR_JOIN_TO_JOIN                                 138
#define ERROR_SUBST_TO_SUBST                               139
#define ERROR_JOIN_TO_SUBST                                140
#define ERROR_SUBST_TO_JOIN                                141
#define ERROR_BUSY_DRIVE                                   142
#define ERROR_SAME_DRIVE                                   143
#define ERROR_DIR_NOT_ROOT                                 144
#define ERROR_DIR_NOT_EMPTY                                145
#define ERROR_IS_SUBST_PATH                                146
#define ERROR_IS_JOIN_PATH                                 147
#define ERROR_PATH_BUSY                                    148
#define ERROR_IS_SUBST_TARGET                              149
#define ERROR_SYSTEM_TRACE                                 150
#define ERROR_INVALID_EVENT_COUNT                          151
#define ERROR_TOO_MANY_MUXWAITERS                          152
#define ERROR_INVALID_LIST_FORMAT                          153
#define ERROR_LABEL_TOO_LONG                               154
#define ERROR_TOO_MANY_TCBS                                155
#define ERROR_SIGNAL_REFUSED                               156
#define ERROR_DISCARDED                                    157
#define ERROR_NOT_LOCKED                                   158
#define ERROR_BAD_THREADID_ADDR                            159
#define ERROR_BAD_ARGUMENTS                                160
#define ERROR_BAD_PATHNAME                                 161
#define ERROR_SIGNAL_PENDING                               162
#define ERROR_MAX_THRDS_REACHED                            164
#define ERROR_LOCK_FAILED                                  167
#define ERROR_BUSY                                         170
#define ERROR_DEVICE_SUPPORT_IN_PROGRESS                   171
#define ERROR_CANCEL_VIOLATION                             173
#define ERROR_ATOMIC_LOCKS_NOT_SUPPORTED                   174
#define ERROR_INVALID_SEGMENT_NUMBER                       180
#define ERROR_INVALID_ORDINAL                              182
#define ERROR_ALREADY_EXISTS                               183
#define ERROR_INVALID_FLAG_NUMBER                          186
#define ERROR_SEM_NOT_FOUND                                187
#define ERROR_INVALID_STARTING_CODESEG                     188
#define ERROR_INVALID_STACKSEG                             189
#define ERROR_INVALID_MODULETYPE                           190
#define ERROR_INVALID_EXE_SIGNATURE                        191
#define ERROR_EXE_MARKED_INVALID                           192
#define ERROR_BAD_EXE_FORMAT                               193
#define ERROR_ITERATED_DATA_EXCEEDS_64k                    194
#define ERROR_INVALID_MINALLOCSIZE                         195
#define ERROR_DYNLINK_FROM_INVALID_RING                    196
#define ERROR_IOPL_NOT_ENABLED                             197
#define ERROR_INVALID_SEGDPL                               198
#define ERROR_AUTODATASEG_EXCEEDS_64k                      199
#define ERROR_RING2SEG_MUST_BE_MOVABLE                     200
#define ERROR_RELOC_CHAIN_XEEDS_SEGLIM                     201
#define ERROR_INFLOOP_IN_RELOC_CHAIN                       202
#define ERROR_ENVVAR_NOT_FOUND                             203
#define ERROR_NO_SIGNAL_SENT                               205
#define ERROR_FILENAME_EXCED_RANGE                         206
#define ERROR_RING2_STACK_IN_USE                           207
#define ERROR_META_EXPANSION_TOO_LONG                      208
#define ERROR_INVALID_SIGNAL_NUMBER                        209
#define ERROR_THREAD_1_INACTIVE                            210
#define ERROR_LOCKED                                       212
#define ERROR_TOO_MANY_MODULES                             214
#define ERROR_NESTING_NOT_ALLOWED                          215
#define ERROR_EXE_MACHINE_TYPE_MISMATCH                    216
#define ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY              217
#define ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY       218
#define ERROR_FILE_CHECKED_OUT                             220
#define ERROR_CHECKOUT_REQUIRED                            221
#define ERROR_BAD_FILE_TYPE                                222
#define ERROR_FILE_TOO_LARGE                               223
#define ERROR_FORMS_AUTH_REQUIRED                          224
#define ERROR_VIRUS_INFECTED                               225
#define ERROR_VIRUS_DELETED                                226
#define ERROR_PIPE_LOCAL                                   229
#define ERROR_BAD_PIPE                                     230
#define ERROR_PIPE_BUSY                                    231
#define ERROR_NO_DATA                                      232
#define ERROR_PIPE_NOT_CONNECTED                           233
#define ERROR_MORE_DATA                                    234
#define ERROR_NO_WORK_DONE                                 235
#define ERROR_VC_DISCONNECTED                              240
#define ERROR_INVALID_EA_NAME                              254
#define ERROR_EA_LIST_INCONSISTENT                         255
#define WAIT_TIMEOUT                                       258
#define ERROR_NO_MORE_ITEMS                                259
#define ERROR_CANNOT_COPY                                  266
#define ERROR_DIRECTORY                                    267
#define ERROR_EAS_DIDNT_FIT                                275
#define ERROR_EA_FILE_CORRUPT                              276
#define ERROR_EA_TABLE_FULL                                277
#define ERROR_INVALID_EA_HANDLE                            278
#define ERROR_EAS_NOT_SUPPORTED                            282
#define ERROR_NOT_OWNER                                    288
#define ERROR_TOO_MANY_POSTS                               298
#define ERROR_PARTIAL_COPY                                 299
#define ERROR_OPLOCK_NOT_GRANTED                           300
#define ERROR_INVALID_OPLOCK_PROTOCOL                      301
#define ERROR_DISK_TOO_FRAGMENTED                          302
#define ERROR_DELETE_PENDING                               303
#define ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING 304
#define ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME            305
#define ERROR_SECURITY_STREAM_IS_INCONSISTENT              306
#define ERROR_INVALID_LOCK_RANGE                           307
#define ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT                  308
#define ERROR_NOTIFICATION_GUID_ALREADY_DEFINED            309
#define ERROR_INVALID_EXCEPTION_HANDLER                    310
#define ERROR_DUPLICATE_PRIVILEGES                         311
#define ERROR_NO_RANGES_PROCESSED                          312
#define ERROR_NOT_ALLOWED_ON_SYSTEM_FILE                   313
#define ERROR_DISK_RESOURCES_EXHAUSTED                     314
#define ERROR_INVALID_TOKEN                                315
#define ERROR_DEVICE_FEATURE_NOT_SUPPORTED                 316
#define ERROR_MR_MID_NOT_FOUND                             317
#define ERROR_SCOPE_NOT_FOUND                              318
#define ERROR_UNDEFINED_SCOPE                              319
#define ERROR_INVALID_CAP                                  320
#define ERROR_DEVICE_UNREACHABLE                           321
#define ERROR_DEVICE_NO_RESOURCES                          322
#define ERROR_DATA_CHECKSUM_ERROR                          323
#define ERROR_INTERMIXED_KERNEL_EA_OPERATION               324
#define ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED                326
#define ERROR_OFFSET_ALIGNMENT_VIOLATION                   327
#define ERROR_INVALID_FIELD_IN_PARAMETER_LIST              328
#define ERROR_OPERATION_IN_PROGRESS                        329
#define ERROR_BAD_DEVICE_PATH                              330
#define ERROR_TOO_MANY_DESCRIPTORS                         331
#define ERROR_SCRUB_DATA_DISABLED                          332
#define ERROR_NOT_REDUNDANT_STORAGE                        333
#define ERROR_RESIDENT_FILE_NOT_SUPPORTED                  334
#define ERROR_COMPRESSED_FILE_NOT_SUPPORTED                335
#define ERROR_DIRECTORY_NOT_SUPPORTED                      336
#define ERROR_NOT_READ_FROM_COPY                           337
#define ERROR_FT_WRITE_FAILURE                             338
#define ERROR_FT_DI_SCAN_REQUIRED                          339
#define ERROR_INVALID_KERNEL_INFO_VERSION                  340
#define ERROR_INVALID_PEP_INFO_VERSION                     341
#define ERROR_OBJECT_NOT_EXTERNALLY_BACKED                 342
#define ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN            343
#define ERROR_COMPRESSION_NOT_BENEFICIAL                   344
#define ERROR_STORAGE_TOPOLOGY_ID_MISMATCH                 345
#define ERROR_BLOCKED_BY_PARENTAL_CONTROLS                 346
#define ERROR_BLOCK_TOO_MANY_REFERENCES                    347
#define ERROR_MARKED_TO_DISALLOW_WRITES                    348
#define ERROR_ENCLAVE_FAILURE                              349
#define ERROR_FAIL_NOACTION_REBOOT                         350
#define ERROR_FAIL_SHUTDOWN                                351
#define ERROR_FAIL_RESTART                                 352
#define ERROR_MAX_SESSIONS_REACHED                         353
#define ERROR_NETWORK_ACCESS_DENIED_EDP                    354
#define ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL            355
#define ERROR_EDP_POLICY_DENIES_OPERATION                  356
#define ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED             357
#define ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT        358
#define ERROR_DEVICE_IN_MAINTENANCE                        359
#define ERROR_NOT_SUPPORTED_ON_DAX                         360
#define ERROR_DAX_MAPPING_EXISTS                           361
#define ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING              362
#define ERROR_CLOUD_FILE_METADATA_CORRUPT                  363
#define ERROR_CLOUD_FILE_METADATA_TOO_LARGE                364
#define ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE           365
#define ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH   366
#define ERROR_CHILD_PROCESS_BLOCKED                        367
#define ERROR_STORAGE_LOST_DATA_PERSISTENCE                368
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE       369
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT  370
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY              371
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN  372
#define ERROR_GDI_HANDLE_LEAK                              373
#define ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS           374
#define ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED    375
#define ERROR_NOT_A_CLOUD_FILE                             376
#define ERROR_CLOUD_FILE_NOT_IN_SYNC                       377
#define ERROR_CLOUD_FILE_ALREADY_CONNECTED                 378
#define ERROR_CLOUD_FILE_NOT_SUPPORTED                     379
#define ERROR_CLOUD_FILE_INVALID_REQUEST                   380
#define ERROR_CLOUD_FILE_READ_ONLY_VOLUME                  381
#define ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY           382
#define ERROR_CLOUD_FILE_VALIDATION_FAILED                 383
#define ERROR_SMB1_NOT_AVAILABLE                           384
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION 385
#define ERROR_CLOUD_FILE_AUTHENTICATION_FAILED             386
#define ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES            387
#define ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE               388
#define ERROR_CLOUD_FILE_UNSUCCESSFUL                      389
#define ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT               390
#define ERROR_CLOUD_FILE_IN_USE                            391
#define ERROR_CLOUD_FILE_PINNED                            392
#define ERROR_CLOUD_FILE_REQUEST_ABORTED                   393
#define ERROR_CLOUD_FILE_PROPERTY_CORRUPT                  394
#define ERROR_CLOUD_FILE_ACCESS_DENIED                     395
#define ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS            396
#define ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT            397
#define ERROR_CLOUD_FILE_REQUEST_CANCELED                  398
#define ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED                399
#define ERROR_THREAD_MODE_ALREADY_BACKGROUND               400
#define ERROR_THREAD_MODE_NOT_BACKGROUND                   401
#define ERROR_PROCESS_MODE_ALREADY_BACKGROUND              402
#define ERROR_PROCESS_MODE_NOT_BACKGROUND                  403
#define ERROR_NO_SUCH_DEVICE                               433
#define ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT              480
#define ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT      481
#define ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT    482
#define ERROR_DEVICE_HARDWARE_ERROR                        483
#define ERROR_INVALID_ADDRESS                              487
#define ERROR_USER_PROFILE_LOAD                            500
#define ERROR_ARITHMETIC_OVERFLOW                          534
#define ERROR_PIPE_CONNECTED                               535
#define ERROR_PIPE_LISTENING                               536
#define ERROR_VERIFIER_STOP                                537
#define ERROR_ABIOS_ERROR                                  538
#define ERROR_WX86_WARNING                                 539
#define ERROR_WX86_ERROR                                   540
#define ERROR_TIMER_NOT_CANCELED                           541
#define ERROR_UNWIND                                       542
#define ERROR_BAD_STACK                                    543
#define ERROR_INVALID_UNWIND_TARGET                        544
#define ERROR_INVALID_PORT_ATTRIBUTES                      545
#define ERROR_PORT_MESSAGE_TOO_LONG                        546
#define ERROR_INVALID_QUOTA_LOWER                          547
#define ERROR_DEVICE_ALREADY_ATTACHED                      548
#define ERROR_INSTRUCTION_MISALIGNMENT                     549
#define ERROR_PROFILING_NOT_STARTED                        550
#define ERROR_PROFILING_NOT_STOPPED                        551
#define ERROR_COULD_NOT_INTERPRET                          552
#define ERROR_PROFILING_AT_LIMIT                           553
#define ERROR_CANT_WAIT                                    554
#define ERROR_CANT_TERMINATE_SELF                          555
#define ERROR_UNEXPECTED_MM_CREATE_ERR                     556
#define ERROR_UNEXPECTED_MM_MAP_ERROR                      557
#define ERROR_UNEXPECTED_MM_EXTEND_ERR                     558
#define ERROR_BAD_FUNCTION_TABLE                           559
#define ERROR_NO_GUID_TRANSLATION                          560
#define ERROR_INVALID_LDT_SIZE                             561
#define ERROR_INVALID_LDT_OFFSET                           563
#define ERROR_INVALID_LDT_DESCRIPTOR                       564
#define ERROR_TOO_MANY_THREADS                             565
#define ERROR_THREAD_NOT_IN_PROCESS                        566
#define ERROR_PAGEFILE_QUOTA_EXCEEDED                      567
#define ERROR_LOGON_SERVER_CONFLICT                        568
#define ERROR_SYNCHRONIZATION_REQUIRED                     569
#define ERROR_NET_OPEN_FAILED                              570
#define ERROR_IO_PRIVILEGE_FAILED                          571
#define ERROR_CONTROL_C_EXIT                               572
#define ERROR_MISSING_SYSTEMFILE                           573
#define ERROR_UNHANDLED_EXCEPTION                          574
#define ERROR_APP_INIT_FAILURE                             575
#define ERROR_PAGEFILE_CREATE_FAILED                       576
#define ERROR_INVALID_IMAGE_HASH                           577
#define ERROR_NO_PAGEFILE                                  578
#define ERROR_ILLEGAL_FLOAT_CONTEXT                        579
#define ERROR_NO_EVENT_PAIR                                580
#define ERROR_DOMAIN_CTRLR_CONFIG_ERROR                    581
#define ERROR_ILLEGAL_CHARACTER                            582
#define ERROR_UNDEFINED_CHARACTER                          583
#define ERROR_FLOPPY_VOLUME                                584
#define ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT             585
#define ERROR_BACKUP_CONTROLLER                            586
#define ERROR_MUTANT_LIMIT_EXCEEDED                        587
#define ERROR_FS_DRIVER_REQUIRED                           588
#define ERROR_CANNOT_LOAD_REGISTRY_FILE                    589
#define ERROR_DEBUG_ATTACH_FAILED                          590
#define ERROR_SYSTEM_PROCESS_TERMINATED                    591
#define ERROR_DATA_NOT_ACCEPTED                            592
#define ERROR_VDM_HARD_ERROR                               593
#define ERROR_DRIVER_CANCEL_TIMEOUT                        594
#define ERROR_REPLY_MESSAGE_MISMATCH                       595
#define ERROR_LOST_WRITEBEHIND_DATA                        596
#define ERROR_CLIENT_SERVER_PARAMETERS_INVALID             597
#define ERROR_NOT_TINY_STREAM                              598
#define ERROR_STACK_OVERFLOW_READ                          599
#define ERROR_CONVERT_TO_LARGE                             600
#define ERROR_FOUND_OUT_OF_SCOPE                           601
#define ERROR_ALLOCATE_BUCKET                              602
#define ERROR_MARSHALL_OVERFLOW                            603
#define ERROR_INVALID_VARIANT                              604
#define ERROR_BAD_COMPRESSION_BUFFER                       605
#define ERROR_AUDIT_FAILED                                 606
#define ERROR_TIMER_RESOLUTION_NOT_SET                     607
#define ERROR_INSUFFICIENT_LOGON_INFO                      608
#define ERROR_BAD_DLL_ENTRYPOINT                           609
#define ERROR_BAD_SERVICE_ENTRYPOINT                       610
#define ERROR_IP_ADDRESS_CONFLICT1                         611
#define ERROR_IP_ADDRESS_CONFLICT2                         612
#define ERROR_REGISTRY_QUOTA_LIMIT                         613
#define ERROR_NO_CALLBACK_ACTIVE                           614
#define ERROR_PWD_TOO_SHORT                                615
#define ERROR_PWD_TOO_RECENT                               616
#define ERROR_PWD_HISTORY_CONFLICT                         617
#define ERROR_UNSUPPORTED_COMPRESSION                      618
#define ERROR_INVALID_HW_PROFILE                           619
#define ERROR_INVALID_PLUGPLAY_DEVICE_PATH                 620
#define ERROR_QUOTA_LIST_INCONSISTENT                      621
#define ERROR_EVALUATION_EXPIRATION                        622
#define ERROR_ILLEGAL_DLL_RELOCATION                       623
#define ERROR_DLL_INIT_FAILED_LOGOFF                       624
#define ERROR_VALIDATE_CONTINUE                            625
#define ERROR_NO_MORE_MATCHES                              626
#define ERROR_RANGE_LIST_CONFLICT                          627
#define ERROR_SERVER_SID_MISMATCH                          628
#define ERROR_CANT_ENABLE_DENY_ONLY                        629
#define ERROR_FLOAT_MULTIPLE_FAULTS                        630
#define ERROR_FLOAT_MULTIPLE_TRAPS                         631
#define ERROR_NOINTERFACE                                  632
#define ERROR_DRIVER_FAILED_SLEEP                          633
#define ERROR_CORRUPT_SYSTEM_FILE                          634
#define ERROR_COMMITMENT_MINIMUM                           635
#define ERROR_PNP_RESTART_ENUMERATION                      636
#define ERROR_SYSTEM_IMAGE_BAD_SIGNATURE                   637
#define ERROR_PNP_REBOOT_REQUIRED                          638
#define ERROR_INSUFFICIENT_POWER                           639
#define ERROR_MULTIPLE_FAULT_VIOLATION                     640
#define ERROR_SYSTEM_SHUTDOWN                              641
#define ERROR_PORT_NOT_SET                                 642
#define ERROR_DS_VERSION_CHECK_FAILURE                     643
#define ERROR_RANGE_NOT_FOUND                              644
#define ERROR_NOT_SAFE_MODE_DRIVER                         646
#define ERROR_FAILED_DRIVER_ENTRY                          647
#define ERROR_DEVICE_ENUMERATION_ERROR                     648
#define ERROR_MOUNT_POINT_NOT_RESOLVED                     649
#define ERROR_INVALID_DEVICE_OBJECT_PARAMETER              650
#define ERROR_MCA_OCCURED                                  651
#define ERROR_DRIVER_DATABASE_ERROR                        652
#define ERROR_SYSTEM_HIVE_TOO_LARGE                        653
#define ERROR_DRIVER_FAILED_PRIOR_UNLOAD                   654
#define ERROR_VOLSNAP_PREPARE_HIBERNATE                    655
#define ERROR_HIBERNATION_FAILURE                          656
#define ERROR_PWD_TOO_LONG                                 657
#define ERROR_FILE_SYSTEM_LIMITATION                       665
#define ERROR_ASSERTION_FAILURE                            668
#define ERROR_ACPI_ERROR                                   669
#define ERROR_WOW_ASSERTION                                670
#define ERROR_PNP_BAD_MPS_TABLE                            671
#define ERROR_PNP_TRANSLATION_FAILED                       672
#define ERROR_PNP_IRQ_TRANSLATION_FAILED                   673
#define ERROR_PNP_INVALID_ID                               674
#define ERROR_WAKE_SYSTEM_DEBUGGER                         675
#define ERROR_HANDLES_CLOSED                               676
#define ERROR_EXTRANEOUS_INFORMATION                       677
#define ERROR_RXACT_COMMIT_NECESSARY                       678
#define ERROR_MEDIA_CHECK                                  679
#define ERROR_GUID_SUBSTITUTION_MADE                       680
#define ERROR_STOPPED_ON_SYMLINK                           681
#define ERROR_LONGJUMP                                     682
#define ERROR_PLUGPLAY_QUERY_VETOED                        683
#define ERROR_UNWIND_CONSOLIDATE                           684
#define ERROR_REGISTRY_HIVE_RECOVERED                      685
#define ERROR_DLL_MIGHT_BE_INSECURE                        686
#define ERROR_DLL_MIGHT_BE_INCOMPATIBLE                    687
#define ERROR_DBG_EXCEPTION_NOT_HANDLED                    688
#define ERROR_DBG_REPLY_LATER                              689
#define ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE                 690
#define ERROR_DBG_TERMINATE_THREAD                         691
#define ERROR_DBG_TERMINATE_PROCESS                        692
#define ERROR_DBG_CONTROL_C                                693
#define ERROR_DBG_PRINTEXCEPTION_C                         694
#define ERROR_DBG_RIPEXCEPTION                             695
#define ERROR_DBG_CONTROL_BREAK                            696
#define ERROR_DBG_COMMAND_EXCEPTION                        697
#define ERROR_OBJECT_NAME_EXISTS                           698
#define ERROR_THREAD_WAS_SUSPENDED                         699
#define ERROR_IMAGE_NOT_AT_BASE                            700
#define ERROR_RXACT_STATE_CREATED                          701
#define ERROR_SEGMENT_NOTIFICATION                         702
#define ERROR_BAD_CURRENT_DIRECTORY                        703
#define ERROR_FT_READ_RECOVERY_FROM_BACKUP                 704
#define ERROR_FT_WRITE_RECOVERY                            705
#define ERROR_IMAGE_MACHINE_TYPE_MISMATCH                  706
#define ERROR_RECEIVE_PARTIAL                              707
#define ERROR_RECEIVE_EXPEDITED                            708
#define ERROR_RECEIVE_PARTIAL_EXPEDITED                    709
#define ERROR_EVENT_DONE                                   710
#define ERROR_EVENT_PENDING                                711
#define ERROR_CHECKING_FILE_SYSTEM                         712
#define ERROR_FATAL_APP_EXIT                               713
#define ERROR_PREDEFINED_HANDLE                            714
#define ERROR_WAS_UNLOCKED                                 715
#define ERROR_SERVICE_NOTIFICATION                         716
#define ERROR_WAS_LOCKED                                   717
#define ERROR_LOG_HARD_ERROR                               718
#define ERROR_ALREADY_WIN32                                719
#define ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE              720
#define ERROR_NO_YIELD_PERFORMED                           721
#define ERROR_TIMER_RESUME_IGNORED                         722
#define ERROR_ARBITRATION_UNHANDLED                        723
#define ERROR_CARDBUS_NOT_SUPPORTED                        724
#define ERROR_MP_PROCESSOR_MISMATCH                        725
#define ERROR_HIBERNATED                                   726
#define ERROR_RESUME_HIBERNATION                           727
#define ERROR_FIRMWARE_UPDATED                             728
#define ERROR_DRIVERS_LEAKING_LOCKED_PAGES                 729
#define ERROR_WAKE_SYSTEM                                  730
#define ERROR_WAIT_1                                       731
#define ERROR_WAIT_2                                       732
#define ERROR_WAIT_3                                       733
#define ERROR_WAIT_63                                      734
#define ERROR_ABANDONED_WAIT_0                             735
#define ERROR_ABANDONED_WAIT_63                            736
#define ERROR_USER_APC                                     737
#define ERROR_KERNEL_APC                                   738
#define ERROR_ALERTED                                      739
#define ERROR_ELEVATION_REQUIRED                           740
#define ERROR_REPARSE                                      741
#define ERROR_OPLOCK_BREAK_IN_PROGRESS                     742
#define ERROR_VOLUME_MOUNTED                               743
#define ERROR_RXACT_COMMITTED                              744
#define ERROR_NOTIFY_CLEANUP                               745
#define ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED             746
#define ERROR_PAGE_FAULT_TRANSITION                        747
#define ERROR_PAGE_FAULT_DEMAND_ZERO                       748
#define ERROR_PAGE_FAULT_COPY_ON_WRITE                     749
#define ERROR_PAGE_FAULT_GUARD_PAGE                        750
#define ERROR_PAGE_FAULT_PAGING_FILE                       751
#define ERROR_CACHE_PAGE_LOCKED                            752
#define ERROR_CRASH_DUMP                                   753
#define ERROR_BUFFER_ALL_ZEROS                             754
#define ERROR_REPARSE_OBJECT                               755
#define ERROR_RESOURCE_REQUIREMENTS_CHANGED                756
#define ERROR_TRANSLATION_COMPLETE                         757
#define ERROR_NOTHING_TO_TERMINATE                         758
#define ERROR_PROCESS_NOT_IN_JOB                           759
#define ERROR_PROCESS_IN_JOB                               760
#define ERROR_VOLSNAP_HIBERNATE_READY                      761
#define ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY           762
#define ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED           763
#define ERROR_INTERRUPT_STILL_CONNECTED                    764
#define ERROR_WAIT_FOR_OPLOCK                              765
#define ERROR_DBG_EXCEPTION_HANDLED                        766
#define ERROR_DBG_CONTINUE                                 767
#define ERROR_CALLBACK_POP_STACK                           768
#define ERROR_COMPRESSION_DISABLED                         769
#define ERROR_CANTFETCHBACKWARDS                           770
#define ERROR_CANTSCROLLBACKWARDS                          771
#define ERROR_ROWSNOTRELEASED                              772
#define ERROR_BAD_ACCESSOR_FLAGS                           773
#define ERROR_ERRORS_ENCOUNTERED                           774
#define ERROR_NOT_CAPABLE                                  775
#define ERROR_REQUEST_OUT_OF_SEQUENCE                      776
#define ERROR_VERSION_PARSE_ERROR                          777
#define ERROR_BADSTARTPOSITION                             778
#define ERROR_MEMORY_HARDWARE                              779
#define ERROR_DISK_REPAIR_DISABLED                         780
#define ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE 781
#define ERROR_SYSTEM_POWERSTATE_TRANSITION                 782
#define ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION         783
#define ERROR_MCA_EXCEPTION                                784
#define ERROR_ACCESS_AUDIT_BY_POLICY                       785
#define ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY        786
#define ERROR_ABANDON_HIBERFILE                            787
#define ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED   788
#define ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR   789
#define ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR       790
#define ERROR_BAD_MCFG_TABLE                               791
#define ERROR_DISK_REPAIR_REDIRECTED                       792
#define ERROR_DISK_REPAIR_UNSUCCESSFUL                     793
#define ERROR_CORRUPT_LOG_OVERFULL                         794
#define ERROR_CORRUPT_LOG_CORRUPTED                        795
#define ERROR_CORRUPT_LOG_UNAVAILABLE                      796
#define ERROR_CORRUPT_LOG_DELETED_FULL                     797
#define ERROR_CORRUPT_LOG_CLEARED                          798
#define ERROR_ORPHAN_NAME_EXHAUSTED                        799
#define ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE                800
#define ERROR_CANNOT_GRANT_REQUESTED_OPLOCK                801
#define ERROR_CANNOT_BREAK_OPLOCK                          802
#define ERROR_OPLOCK_HANDLE_CLOSED                         803
#define ERROR_NO_ACE_CONDITION                             804
#define ERROR_INVALID_ACE_CONDITION                        805
#define ERROR_FILE_HANDLE_REVOKED                          806
#define ERROR_IMAGE_AT_DIFFERENT_BASE                      807
#define ERROR_ENCRYPTED_IO_NOT_POSSIBLE                    808
#define ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS       809
#define ERROR_QUOTA_ACTIVITY                               810
#define ERROR_HANDLE_REVOKED                               811
#define ERROR_CALLBACK_INVOKE_INLINE                       812
#define ERROR_CPU_SET_INVALID                              813
#define ERROR_ENCLAVE_NOT_TERMINATED                       814
#define ERROR_EA_ACCESS_DENIED                             994
#define ERROR_OPERATION_ABORTED                            995
#define ERROR_IO_INCOMPLETE                                996
#define ERROR_IO_PENDING                                   997
#define ERROR_NOACCESS                                     998
#define ERROR_SWAPERROR                                    999
#define ERROR_STACK_OVERFLOW                               1001
#define ERROR_INVALID_MESSAGE                              1002
#define ERROR_CAN_NOT_COMPLETE                             1003
#define ERROR_INVALID_FLAGS                                1004
#define ERROR_UNRECOGNIZED_VOLUME                          1005
#define ERROR_FILE_INVALID                                 1006
#define ERROR_FULLSCREEN_MODE                              1007
#define ERROR_NO_TOKEN                                     1008
#define ERROR_BADDB                                        1009
#define ERROR_BADKEY                                       1010
#define ERROR_CANTOPEN                                     1011
#define ERROR_CANTREAD                                     1012
#define ERROR_CANTWRITE                                    1013
#define ERROR_REGISTRY_RECOVERED                           1014
#define ERROR_REGISTRY_CORRUPT                             1015
#define ERROR_REGISTRY_IO_FAILED                           1016
#define ERROR_NOT_REGISTRY_FILE                            1017
#define ERROR_KEY_DELETED                                  1018
#define ERROR_NO_LOG_SPACE                                 1019
#define ERROR_KEY_HAS_CHILDREN                             1020
#define ERROR_CHILD_MUST_BE_VOLATILE                       1021
#define ERROR_NOTIFY_ENUM_DIR                              1022
#define ERROR_DEPENDENT_SERVICES_RUNNING                   1051
#define ERROR_INVALID_SERVICE_CONTROL                      1052
#define ERROR_SERVICE_REQUEST_TIMEOUT                      1053
#define ERROR_SERVICE_NO_THREAD                            1054
#define ERROR_SERVICE_DATABASE_LOCKED                      1055
#define ERROR_SERVICE_ALREADY_RUNNING                      1056
#define ERROR_INVALID_SERVICE_ACCOUNT                      1057
#define ERROR_SERVICE_DISABLED                             1058
#define ERROR_CIRCULAR_DEPENDENCY                          1059
#define ERROR_SERVICE_DOES_NOT_EXIST                       1060
#define ERROR_SERVICE_CANNOT_ACCEPT_CTRL                   1061
#define ERROR_SERVICE_NOT_ACTIVE                           1062
#define ERROR_FAILED_SERVICE_CONTROLLER_CONNECT            1063
#define ERROR_EXCEPTION_IN_SERVICE                         1064
#define ERROR_DATABASE_DOES_NOT_EXIST                      1065
#define ERROR_SERVICE_SPECIFIC_ERROR                       1066
#define ERROR_PROCESS_ABORTED                              1067
#define ERROR_SERVICE_DEPENDENCY_FAIL                      1068
#define ERROR_SERVICE_LOGON_FAILED                         1069
#define ERROR_SERVICE_START_HANG                           1070
#define ERROR_INVALID_SERVICE_LOCK                         1071
#define ERROR_SERVICE_MARKED_FOR_DELETE                    1072
#define ERROR_SERVICE_EXISTS                               1073
#define ERROR_ALREADY_RUNNING_LKG                          1074
#define ERROR_SERVICE_DEPENDENCY_DELETED                   1075
#define ERROR_BOOT_ALREADY_ACCEPTED                        1076
#define ERROR_SERVICE_NEVER_STARTED                        1077
#define ERROR_DUPLICATE_SERVICE_NAME                       1078
#define ERROR_DIFFERENT_SERVICE_ACCOUNT                    1079
#define ERROR_CANNOT_DETECT_DRIVER_FAILURE                 1080
#define ERROR_CANNOT_DETECT_PROCESS_ABORT                  1081
#define ERROR_NO_RECOVERY_PROGRAM                          1082
#define ERROR_SERVICE_NOT_IN_EXE                           1083
#define ERROR_END_OF_MEDIA                                 1100
#define ERROR_FILEMARK_DETECTED                            1101
#define ERROR_BEGINNING_OF_MEDIA                           1102
#define ERROR_SETMARK_DETECTED                             1103
#define ERROR_NO_DATA_DETECTED                             1104
#define ERROR_PARTITION_FAILURE                            1105
#define ERROR_INVALID_BLOCK_LENGTH                         1106
#define ERROR_DEVICE_NOT_PARTITIONED                       1107
#define ERROR_UNABLE_TO_LOCK_MEDIA                         1108
#define ERROR_UNABLE_TO_UNLOAD_MEDIA                       1109
#define ERROR_MEDIA_CHANGED                                1110
#define ERROR_BUS_RESET                                    1111
#define ERROR_NO_MEDIA_IN_DRIVE                            1112
#define ERROR_NO_UNICODE_TRANSLATION                       1113
#define ERROR_DLL_INIT_FAILED                              1114
#define ERROR_SHUTDOWN_IN_PROGRESS                         1115
#define ERROR_NO_SHUTDOWN_IN_PROGRESS                      1116
#define ERROR_IO_DEVICE                                    1117
#define ERROR_SERIAL_NO_DEVICE                             1118
#define ERROR_IRQ_BUSY                                     1119
#define ERROR_MORE_WRITES                                  1120
#define ERROR_COUNTER_TIMEOUT                              1121
#define ERROR_FLOPPY_ID_MARK_NOT_FOUND                     1122
#define ERROR_FLOPPY_WRONG_CYLINDER                        1123
#define ERROR_FLOPPY_UNKNOWN_ERROR                         1124
#define ERROR_FLOPPY_BAD_REGISTERS                         1125
#define ERROR_DISK_RECALIBRATE_FAILED                      1126
#define ERROR_DISK_OPERATION_FAILED                        1127
#define ERROR_DISK_RESET_FAILED                            1128
#define ERROR_EOM_OVERFLOW                                 1129
#define ERROR_NOT_ENOUGH_SERVER_MEMORY                     1130
#define ERROR_POSSIBLE_DEADLOCK                            1131
#define ERROR_MAPPED_ALIGNMENT                             1132
#define ERROR_SET_POWER_STATE_VETOED                       1140
#define ERROR_SET_POWER_STATE_FAILED                       1141
#define ERROR_TOO_MANY_LINKS                               1142
#define ERROR_OLD_WIN_VERSION                              1150
#define ERROR_APP_WRONG_OS                                 1151
#define ERROR_SINGLE_INSTANCE_APP                          1152
#define ERROR_RMODE_APP                                    1153
#define ERROR_INVALID_DLL                                  1154
#define ERROR_NO_ASSOCIATION                               1155
#define ERROR_DDE_FAIL                                     1156
#define ERROR_DLL_NOT_FOUND                                1157
#define ERROR_NO_MORE_USER_HANDLES                         1158
#define ERROR_MESSAGE_SYNC_ONLY                            1159
#define ERROR_SOURCE_ELEMENT_EMPTY                         1160
#define ERROR_DESTINATION_ELEMENT_FULL                     1161
#define ERROR_ILLEGAL_ELEMENT_ADDRESS                      1162
#define ERROR_MAGAZINE_NOT_PRESENT                         1163
#define ERROR_DEVICE_REINITIALIZATION_NEEDED               1164
#define ERROR_DEVICE_REQUIRES_CLEANING                     1165
#define ERROR_DEVICE_DOOR_OPEN                             1166
#define ERROR_DEVICE_NOT_CONNECTED                         1167
#define ERROR_NOT_FOUND                                    1168
#define ERROR_NO_MATCH                                     1169
#define ERROR_SET_NOT_FOUND                                1170
#define ERROR_POINT_NOT_FOUND                              1171
#define ERROR_NO_TRACKING_SERVICE                          1172
#define ERROR_NO_VOLUME_ID                                 1173
#define ERROR_UNABLE_TO_REMOVE_REPLACED                    1175
#define ERROR_UNABLE_TO_MOVE_REPLACEMENT                   1176
#define ERROR_UNABLE_TO_MOVE_REPLACEMENT_2                 1177
#define ERROR_JOURNAL_DELETE_IN_PROGRESS                   1178
#define ERROR_JOURNAL_NOT_ACTIVE                           1179
#define ERROR_POTENTIAL_FILE_FOUND                         1180
#define ERROR_JOURNAL_ENTRY_DELETED                        1181
#define ERROR_VRF_CFG_ENABLED                              1183
#define ERROR_PARTITION_TERMINATING                        1184
#define ERROR_BAD_DEVICE                                   1200
#define ERROR_CONNECTION_UNAVAIL                           1201
#define ERROR_DEVICE_ALREADY_REMEMBERED                    1202
#define ERROR_NO_NET_OR_BAD_PATH                           1203
#define ERROR_BAD_PROVIDER                                 1204
#define ERROR_CANNOT_OPEN_PROFILE                          1205
#define ERROR_BAD_PROFILE                                  1206
#define ERROR_NOT_CONTAINER                                1207
#define ERROR_EXTENDED_ERROR                               1208
#define ERROR_INVALID_GROUPNAME                            1209
#define ERROR_INVALID_COMPUTERNAME                         1210
#define ERROR_INVALID_EVENTNAME                            1211
#define ERROR_INVALID_DOMAINNAME                           1212
#define ERROR_INVALID_SERVICENAME                          1213
#define ERROR_INVALID_NETNAME                              1214
#define ERROR_INVALID_SHARENAME                            1215
#define ERROR_INVALID_PASSWORDNAME                         1216
#define ERROR_INVALID_MESSAGENAME                          1217
#define ERROR_INVALID_MESSAGEDEST                          1218
#define ERROR_SESSION_CREDENTIAL_CONFLICT                  1219
#define ERROR_REMOTE_SESSION_LIMIT_EXCEEDED                1220
#define ERROR_DUP_DOMAINNAME                               1221
#define ERROR_NO_NETWORK                                   1222
#define ERROR_CANCELLED                                    1223
#define ERROR_USER_MAPPED_FILE                             1224
#define ERROR_CONNECTION_REFUSED                           1225
#define ERROR_GRACEFUL_DISCONNECT                          1226
#define ERROR_ADDRESS_ALREADY_ASSOCIATED                   1227
#define ERROR_ADDRESS_NOT_ASSOCIATED                       1228
#define ERROR_CONNECTION_INVALID                           1229
#define ERROR_CONNECTION_ACTIVE                            1230
#define ERROR_NETWORK_UNREACHABLE                          1231
#define ERROR_HOST_UNREACHABLE                             1232
#define ERROR_PROTOCOL_UNREACHABLE                         1233
#define ERROR_PORT_UNREACHABLE                             1234
#define ERROR_REQUEST_ABORTED                              1235
#define ERROR_CONNECTION_ABORTED                           1236
#define ERROR_RETRY                                        1237
#define ERROR_CONNECTION_COUNT_LIMIT                       1238
#define ERROR_LOGIN_TIME_RESTRICTION                       1239
#define ERROR_LOGIN_WKSTA_RESTRICTION                      1240
#define ERROR_INCORRECT_ADDRESS                            1241
#define ERROR_ALREADY_REGISTERED                           1242
#define ERROR_SERVICE_NOT_FOUND                            1243
#define ERROR_NOT_AUTHENTICATED                            1244
#define ERROR_NOT_LOGGED_ON                                1245
#define ERROR_CONTINUE                                     1246
#define ERROR_ALREADY_INITIALIZED                          1247
#define ERROR_NO_MORE_DEVICES                              1248
#define ERROR_NO_SUCH_SITE                                 1249
#define ERROR_DOMAIN_CONTROLLER_EXISTS                     1250
#define ERROR_ONLY_IF_CONNECTED                            1251
#define ERROR_OVERRIDE_NOCHANGES                           1252
#define ERROR_BAD_USER_PROFILE                             1253
#define ERROR_NOT_SUPPORTED_ON_SBS                         1254
#define ERROR_SERVER_SHUTDOWN_IN_PROGRESS                  1255
#define ERROR_HOST_DOWN                                    1256
#define ERROR_NON_ACCOUNT_SID                              1257
#define ERROR_NON_DOMAIN_SID                               1258
#define ERROR_APPHELP_BLOCK                                1259
#define ERROR_ACCESS_DISABLED_BY_POLICY                    1260
#define ERROR_REG_NAT_CONSUMPTION                          1261
#define ERROR_PKINIT_FAILURE                               1263
#define ERROR_SMARTCARD_SUBSYSTEM_FAILURE                  1264
#define ERROR_DOWNGRADE_DETECTED                           1265
#define ERROR_MACHINE_LOCKED                               1271
#define ERROR_CALLBACK_SUPPLIED_INVALID_DATA               1273
#define ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED             1274
#define ERROR_DRIVER_BLOCKED                               1275
#define ERROR_INVALID_IMPORT_OF_NON_DLL                    1276
#define ERROR_ACCESS_DISABLED_WEBBLADE                     1277
#define ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER              1278
#define ERROR_RECOVERY_FAILURE                             1279
#define ERROR_ALREADY_FIBER                                1280
#define ERROR_ALREADY_THREAD                               1281
#define ERROR_STACK_BUFFER_OVERRUN                         1282
#define ERROR_PARAMETER_QUOTA_EXCEEDED                     1283
#define ERROR_DEBUGGER_INACTIVE                            1284
#define ERROR_DELAY_LOAD_FAILED                            1285
#define ERROR_VDM_DISALLOWED                               1286
#define ERROR_UNIDENTIFIED_ERROR                           1287
#define ERROR_INVALID_CRUNTIME_PARAMETER                   1288
#define ERROR_BEYOND_VDL                                   1289
#define ERROR_INCOMPATIBLE_SERVICE_SID_TYPE                1290
#define ERROR_DRIVER_PROCESS_TERMINATED                    1291
#define ERROR_IMPLEMENTATION_LIMIT                         1292
#define ERROR_PROCESS_IS_PROTECTED                         1293
#define ERROR_SERVICE_NOTIFY_CLIENT_LAGGING                1294
#define ERROR_DISK_QUOTA_EXCEEDED                          1295
#define ERROR_CONTENT_BLOCKED                              1296
#define ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE               1297
#define ERROR_APP_HANG                                     1298
#define ERROR_INVALID_LABEL                                1299
#define ERROR_NOT_ALL_ASSIGNED                             1300
#define ERROR_SOME_NOT_MAPPED                              1301
#define ERROR_NO_QUOTAS_FOR_ACCOUNT                        1302
#define ERROR_LOCAL_USER_SESSION_KEY                       1303
#define ERROR_NULL_LM_PASSWORD                             1304
#define ERROR_UNKNOWN_REVISION                             1305
#define ERROR_REVISION_MISMATCH                            1306
#define ERROR_INVALID_OWNER                                1307
#define ERROR_INVALID_PRIMARY_GROUP                        1308
#define ERROR_NO_IMPERSONATION_TOKEN                       1309
#define ERROR_CANT_DISABLE_MANDATORY                       1310
#define ERROR_NO_LOGON_SERVERS                             1311
#define ERROR_NO_SUCH_LOGON_SESSION                        1312
#define ERROR_NO_SUCH_PRIVILEGE                            1313
#define ERROR_PRIVILEGE_NOT_HELD                           1314
#define ERROR_INVALID_ACCOUNT_NAME                         1315
#define ERROR_USER_EXISTS                                  1316
#define ERROR_NO_SUCH_USER                                 1317
#define ERROR_GROUP_EXISTS                                 1318
#define ERROR_NO_SUCH_GROUP                                1319
#define ERROR_MEMBER_IN_GROUP                              1320
#define ERROR_MEMBER_NOT_IN_GROUP                          1321
#define ERROR_LAST_ADMIN                                   1322
#define ERROR_WRONG_PASSWORD                               1323
#define ERROR_ILL_FORMED_PASSWORD                          1324
#define ERROR_PASSWORD_RESTRICTION                         1325
#define ERROR_LOGON_FAILURE                                1326
#define ERROR_ACCOUNT_RESTRICTION                          1327
#define ERROR_INVALID_LOGON_HOURS                          1328
#define ERROR_INVALID_WORKSTATION                          1329
#define ERROR_PASSWORD_EXPIRED                             1330
#define ERROR_ACCOUNT_DISABLED                             1331
#define ERROR_NONE_MAPPED                                  1332
#define ERROR_TOO_MANY_LUIDS_REQUESTED                     1333
#define ERROR_LUIDS_EXHAUSTED                              1334
#define ERROR_INVALID_SUB_AUTHORITY                        1335
#define ERROR_INVALID_ACL                                  1336
#define ERROR_INVALID_SID                                  1337
#define ERROR_INVALID_SECURITY_DESCR                       1338
#define ERROR_BAD_INHERITANCE_ACL                          1340
#define ERROR_SERVER_DISABLED                              1341
#define ERROR_SERVER_NOT_DISABLED                          1342
#define ERROR_INVALID_ID_AUTHORITY                         1343
#define ERROR_ALLOTTED_SPACE_EXCEEDED                      1344
#define ERROR_INVALID_GROUP_ATTRIBUTES                     1345
#define ERROR_BAD_IMPERSONATION_LEVEL                      1346
#define ERROR_CANT_OPEN_ANONYMOUS                          1347
#define ERROR_BAD_VALIDATION_CLASS                         1348
#define ERROR_BAD_TOKEN_TYPE                               1349
#define ERROR_NO_SECURITY_ON_OBJECT                        1350
#define ERROR_CANT_ACCESS_DOMAIN_INFO                      1351
#define ERROR_INVALID_SERVER_STATE                         1352
#define ERROR_INVALID_DOMAIN_STATE                         1353
#define ERROR_INVALID_DOMAIN_ROLE                          1354
#define ERROR_NO_SUCH_DOMAIN                               1355
#define ERROR_DOMAIN_EXISTS                                1356
#define ERROR_DOMAIN_LIMIT_EXCEEDED                        1357
#define ERROR_INTERNAL_DB_CORRUPTION                       1358
#define ERROR_INTERNAL_ERROR                               1359
#define ERROR_GENERIC_NOT_MAPPED                           1360
#define ERROR_BAD_DESCRIPTOR_FORMAT                        1361
#define ERROR_NOT_LOGON_PROCESS                            1362
#define ERROR_LOGON_SESSION_EXISTS                         1363
#define ERROR_NO_SUCH_PACKAGE                              1364
#define ERROR_BAD_LOGON_SESSION_STATE                      1365
#define ERROR_LOGON_SESSION_COLLISION                      1366
#define ERROR_INVALID_LOGON_TYPE                           1367
#define ERROR_CANNOT_IMPERSONATE                           1368
#define ERROR_RXACT_INVALID_STATE                          1369
#define ERROR_RXACT_COMMIT_FAILURE                         1370
#define ERROR_SPECIAL_ACCOUNT                              1371
#define ERROR_SPECIAL_GROUP                                1372
#define ERROR_SPECIAL_USER                                 1373
#define ERROR_MEMBERS_PRIMARY_GROUP                        1374
#define ERROR_TOKEN_ALREADY_IN_USE                         1375
#define ERROR_NO_SUCH_ALIAS                                1376
#define ERROR_MEMBER_NOT_IN_ALIAS                          1377
#define ERROR_MEMBER_IN_ALIAS                              1378
#define ERROR_ALIAS_EXISTS                                 1379
#define ERROR_LOGON_NOT_GRANTED                            1380
#define ERROR_TOO_MANY_SECRETS                             1381
#define ERROR_SECRET_TOO_LONG                              1382
#define ERROR_INTERNAL_DB_ERROR                            1383
#define ERROR_TOO_MANY_CONTEXT_IDS                         1384
#define ERROR_LOGON_TYPE_NOT_GRANTED                       1385
#define ERROR_NT_CROSS_ENCRYPTION_REQUIRED                 1386
#define ERROR_NO_SUCH_MEMBER                               1387
#define ERROR_INVALID_MEMBER                               1388
#define ERROR_TOO_MANY_SIDS                                1389
#define ERROR_LM_CROSS_ENCRYPTION_REQUIRED                 1390
#define ERROR_NO_INHERITANCE                               1391
#define ERROR_FILE_CORRUPT                                 1392
#define ERROR_DISK_CORRUPT                                 1393
#define ERROR_NO_USER_SESSION_KEY                          1394
#define ERROR_LICENSE_QUOTA_EXCEEDED                       1395
#define ERROR_WRONG_TARGET_NAME                            1396
#define ERROR_MUTUAL_AUTH_FAILED                           1397
#define ERROR_TIME_SKEW                                    1398
#define ERROR_CURRENT_DOMAIN_NOT_ALLOWED                   1399
#define ERROR_INVALID_WINDOW_HANDLE                        1400
#define ERROR_INVALID_MENU_HANDLE                          1401
#define ERROR_INVALID_CURSOR_HANDLE                        1402
#define ERROR_INVALID_ACCEL_HANDLE                         1403
#define ERROR_INVALID_HOOK_HANDLE                          1404
#define ERROR_INVALID_DWP_HANDLE                           1405
#define ERROR_TLW_WITH_WSCHILD                             1406
#define ERROR_CANNOT_FIND_WND_CLASS                        1407
#define ERROR_WINDOW_OF_OTHER_THREAD                       1408
#define ERROR_HOTKEY_ALREADY_REGISTERED                    1409
#define ERROR_CLASS_ALREADY_EXISTS                         1410
#define ERROR_CLASS_DOES_NOT_EXIST                         1411
#define ERROR_CLASS_HAS_WINDOWS                            1412
#define ERROR_INVALID_INDEX                                1413
#define ERROR_INVALID_ICON_HANDLE                          1414
#define ERROR_PRIVATE_DIALOG_INDEX                         1415
#define ERROR_LISTBOX_ID_NOT_FOUND                         1416
#define ERROR_NO_WILDCARD_CHARACTERS                       1417
#define ERROR_CLIPBOARD_NOT_OPEN                           1418
#define ERROR_HOTKEY_NOT_REGISTERED                        1419
#define ERROR_WINDOW_NOT_DIALOG                            1420
#define ERROR_CONTROL_ID_NOT_FOUND                         1421
#define ERROR_INVALID_COMBOBOX_MESSAGE                     1422
#define ERROR_WINDOW_NOT_COMBOBOX                          1423
#define ERROR_INVALID_EDIT_HEIGHT                          1424
#define ERROR_DC_NOT_FOUND                                 1425
#define ERROR_INVALID_HOOK_FILTER                          1426
#define ERROR_INVALID_FILTER_PROC                          1427
#define ERROR_HOOK_NEEDS_HMOD                              1428
#define ERROR_GLOBAL_ONLY_HOOK                             1429
#define ERROR_JOURNAL_HOOK_SET                             1430
#define ERROR_HOOK_NOT_INSTALLED                           1431
#define ERROR_INVALID_LB_MESSAGE                           1432
#define ERROR_SETCOUNT_ON_BAD_LB                           1433
#define ERROR_LB_WITHOUT_TABSTOPS                          1434
#define ERROR_DESTROY_OBJECT_OF_OTHER_THREAD               1435
#define ERROR_CHILD_WINDOW_MENU                            1436
#define ERROR_NO_SYSTEM_MENU                               1437
#define ERROR_INVALID_MSGBOX_STYLE                         1438
#define ERROR_INVALID_SPI_VALUE                            1439
#define ERROR_SCREEN_ALREADY_LOCKED                        1440
#define ERROR_HWNDS_HAVE_DIFF_PARENT                       1441
#define ERROR_NOT_CHILD_WINDOW                             1442
#define ERROR_INVALID_GW_COMMAND                           1443
#define ERROR_INVALID_THREAD_ID                            1444
#define ERROR_NON_MDICHILD_WINDOW                          1445
#define ERROR_POPUP_ALREADY_ACTIVE                         1446
#define ERROR_NO_SCROLLBARS                                1447
#define ERROR_INVALID_SCROLLBAR_RANGE                      1448
#define ERROR_INVALID_SHOWWIN_COMMAND                      1449
#define ERROR_NO_SYSTEM_RESOURCES                          1450
#define ERROR_NONPAGED_SYSTEM_RESOURCES                    1451
#define ERROR_PAGED_SYSTEM_RESOURCES                       1452
#define ERROR_WORKING_SET_QUOTA                            1453
#define ERROR_PAGEFILE_QUOTA                               1454
#define ERROR_COMMITMENT_LIMIT                             1455
#define ERROR_MENU_ITEM_NOT_FOUND                          1456
#define ERROR_INVALID_KEYBOARD_HANDLE                      1457
#define ERROR_HOOK_TYPE_NOT_ALLOWED                        1458
#define ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION           1459
#define ERROR_TIMEOUT                                      1460
#define ERROR_INVALID_MONITOR_HANDLE                       1461
#define ERROR_INCORRECT_SIZE                               1462
#define ERROR_SYMLINK_CLASS_DISABLED                       1463
#define ERROR_SYMLINK_NOT_SUPPORTED                        1464
#define ERROR_XML_PARSE_ERROR                              1465
#define ERROR_XMLDSIG_ERROR                                1466
#define ERROR_RESTART_APPLICATION                          1467
#define ERROR_WRONG_COMPARTMENT                            1468
#define ERROR_AUTHIP_FAILURE                               1469
#define ERROR_NO_NVRAM_RESOURCES                           1470
#define ERROR_NOT_GUI_PROCESS                              1471
#define ERROR_EVENTLOG_FILE_CORRUPT                        1500
#define ERROR_EVENTLOG_CANT_START                          1501
#define ERROR_LOG_FILE_FULL                                1502
#define ERROR_EVENTLOG_FILE_CHANGED                        1503
#define ERROR_CONTAINER_ASSIGNED                           1504
#define ERROR_JOB_NO_CONTAINER                             1505
#define ERROR_INVALID_TASK_NAME                            1550
#define ERROR_INVALID_TASK_INDEX                           1551
#define ERROR_THREAD_ALREADY_IN_TASK                       1552
#define ERROR_INSTALL_SERVICE_FAILURE                      1601
#define ERROR_INSTALL_USEREXIT                             1602
#define ERROR_INSTALL_FAILURE                              1603
#define ERROR_INSTALL_SUSPEND                              1604
#define ERROR_UNKNOWN_PRODUCT                              1605
#define ERROR_UNKNOWN_FEATURE                              1606
#define ERROR_UNKNOWN_COMPONENT                            1607
#define ERROR_UNKNOWN_PROPERTY                             1608
#define ERROR_INVALID_HANDLE_STATE                         1609
#define ERROR_BAD_CONFIGURATION                            1610
#define ERROR_INDEX_ABSENT                                 1611
#define ERROR_INSTALL_SOURCE_ABSENT                        1612
#define ERROR_INSTALL_PACKAGE_VERSION                      1613
#define ERROR_PRODUCT_UNINSTALLED                          1614
#define ERROR_BAD_QUERY_SYNTAX                             1615
#define ERROR_INVALID_FIELD                                1616
#define ERROR_DEVICE_REMOVED                               1617
#define ERROR_INSTALL_ALREADY_RUNNING                      1618
#define ERROR_INSTALL_PACKAGE_OPEN_FAILED                  1619
#define ERROR_INSTALL_PACKAGE_INVALID                      1620
#define ERROR_INSTALL_UI_FAILURE                           1621
#define ERROR_INSTALL_LOG_FAILURE                          1622
#define ERROR_INSTALL_LANGUAGE_UNSUPPORTED                 1623
#define ERROR_INSTALL_TRANSFORM_FAILURE                    1624
#define ERROR_INSTALL_PACKAGE_REJECTED                     1625
#define ERROR_FUNCTION_NOT_CALLED                          1626
#define ERROR_FUNCTION_FAILED                              1627
#define ERROR_INVALID_TABLE                                1628
#define ERROR_DATATYPE_MISMATCH                            1629
#define ERROR_UNSUPPORTED_TYPE                             1630
#define ERROR_CREATE_FAILED                                1631
#define ERROR_INSTALL_TEMP_UNWRITABLE                      1632
#define ERROR_INSTALL_PLATFORM_UNSUPPORTED                 1633
#define ERROR_INSTALL_NOTUSED                              1634
#define ERROR_PATCH_PACKAGE_OPEN_FAILED                    1635
#define ERROR_PATCH_PACKAGE_INVALID                        1636
#define ERROR_PATCH_PACKAGE_UNSUPPORTED                    1637
#define ERROR_PRODUCT_VERSION                              1638
#define ERROR_INVALID_COMMAND_LINE                         1639
#define ERROR_INSTALL_REMOTE_DISALLOWED                    1640
#define ERROR_SUCCESS_REBOOT_INITIATED                     1641
#define ERROR_PATCH_TARGET_NOT_FOUND                       1642
#define ERROR_PATCH_PACKAGE_REJECTED                       1643
#define ERROR_INSTALL_TRANSFORM_REJECTED                   1644
#define ERROR_INSTALL_REMOTE_PROHIBITED                    1645
#define ERROR_PATCH_REMOVAL_UNSUPPORTED                    1646
#define ERROR_UNKNOWN_PATCH                                1647
#define ERROR_PATCH_NO_SEQUENCE                            1648
#define ERROR_PATCH_REMOVAL_DISALLOWED                     1649
#define ERROR_INVALID_PATCH_XML                            1650
#define ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT             1651
#define ERROR_INSTALL_SERVICE_SAFEBOOT                     1652
#define ERROR_FAIL_FAST_EXCEPTION                          1653
#define ERROR_INSTALL_REJECTED                             1654
#define ERROR_DYNAMIC_CODE_BLOCKED                         1655
#define ERROR_NOT_SAME_OBJECT                              1656
#define ERROR_STRICT_CFG_VIOLATION                         1657
#define ERROR_SET_CONTEXT_DENIED                           1660
#define ERROR_CROSS_PARTITION_VIOLATION                    1661
#define RPC_S_INVALID_STRING_BINDING                       1700
#define RPC_S_WRONG_KIND_OF_BINDING                        1701
#define RPC_S_INVALID_BINDING                              1702
#define RPC_S_PROTSEQ_NOT_SUPPORTED                        1703
#define RPC_S_INVALID_RPC_PROTSEQ                          1704
#define RPC_S_INVALID_STRING_UUID                          1705
#define RPC_S_INVALID_ENDPOINT_FORMAT                      1706
#define RPC_S_INVALID_NET_ADDR                             1707
#define RPC_S_NO_ENDPOINT_FOUND                            1708
#define RPC_S_INVALID_TIMEOUT                              1709
#define RPC_S_OBJECT_NOT_FOUND                             1710
#define RPC_S_ALREADY_REGISTERED                           1711
#define RPC_S_TYPE_ALREADY_REGISTERED                      1712
#define RPC_S_ALREADY_LISTENING                            1713
#define RPC_S_NO_PROTSEQS_REGISTERED                       1714
#define RPC_S_NOT_LISTENING                                1715
#define RPC_S_UNKNOWN_MGR_TYPE                             1716
#define RPC_S_UNKNOWN_IF                                   1717
#define RPC_S_NO_BINDINGS                                  1718
#define RPC_S_NO_PROTSEQS                                  1719
#define RPC_S_CANT_CREATE_ENDPOINT                         1720
#define RPC_S_OUT_OF_RESOURCES                             1721
#define RPC_S_SERVER_UNAVAILABLE                           1722
#define RPC_S_SERVER_TOO_BUSY                              1723
#define RPC_S_INVALID_NETWORK_OPTIONS                      1724
#define RPC_S_NO_CALL_ACTIVE                               1725
#define RPC_S_CALL_FAILED                                  1726
#define RPC_S_CALL_FAILED_DNE                              1727
#define RPC_S_PROTOCOL_ERROR                               1728
#define RPC_S_PROXY_ACCESS_DENIED                          1729
#define RPC_S_UNSUPPORTED_TRANS_SYN                        1730
#define RPC_S_UNSUPPORTED_TYPE                             1732
#define RPC_S_INVALID_TAG                                  1733
#define RPC_S_INVALID_BOUND                                1734
#define RPC_S_NO_ENTRY_NAME                                1735
#define RPC_S_INVALID_NAME_SYNTAX                          1736
#define RPC_S_UNSUPPORTED_NAME_SYNTAX                      1737
#define RPC_S_UUID_NO_ADDRESS                              1739
#define RPC_S_DUPLICATE_ENDPOINT                           1740
#define RPC_S_UNKNOWN_AUTHN_TYPE                           1741
#define RPC_S_MAX_CALLS_TOO_SMALL                          1742
#define RPC_S_STRING_TOO_LONG                              1743
#define RPC_S_PROTSEQ_NOT_FOUND                            1744
#define RPC_S_PROCNUM_OUT_OF_RANGE                         1745
#define RPC_S_BINDING_HAS_NO_AUTH                          1746
#define RPC_S_UNKNOWN_AUTHN_SERVICE                        1747
#define RPC_S_UNKNOWN_AUTHN_LEVEL                          1748
#define RPC_S_INVALID_AUTH_IDENTITY                        1749
#define RPC_S_UNKNOWN_AUTHZ_SERVICE                        1750
#define EPT_S_INVALID_ENTRY                                1751
#define EPT_S_CANT_PERFORM_OP                              1752
#define EPT_S_NOT_REGISTERED                               1753
#define RPC_S_NOTHING_TO_EXPORT                            1754
#define RPC_S_INCOMPLETE_NAME                              1755
#define RPC_S_INVALID_VERS_OPTION                          1756
#define RPC_S_NO_MORE_MEMBERS                              1757
#define RPC_S_NOT_ALL_OBJS_UNEXPORTED                      1758
#define RPC_S_INTERFACE_NOT_FOUND                          1759
#define RPC_S_ENTRY_ALREADY_EXISTS                         1760
#define RPC_S_ENTRY_NOT_FOUND                              1761
#define RPC_S_NAME_SERVICE_UNAVAILABLE                     1762
#define RPC_S_INVALID_NAF_ID                               1763
#define RPC_S_CANNOT_SUPPORT                               1764
#define RPC_S_NO_CONTEXT_AVAILABLE                         1765
#define RPC_S_INTERNAL_ERROR                               1766
#define RPC_S_ZERO_DIVIDE                                  1767
#define RPC_S_ADDRESS_ERROR                                1768
#define RPC_S_FP_DIV_ZERO                                  1769
#define RPC_S_FP_UNDERFLOW                                 1770
#define RPC_S_FP_OVERFLOW                                  1771
#define RPC_X_NO_MORE_ENTRIES                              1772
#define RPC_X_SS_CHAR_TRANS_OPEN_FAIL                      1773
#define RPC_X_SS_CHAR_TRANS_SHORT_FILE                     1774
#define RPC_X_SS_IN_NULL_CONTEXT                           1775
#define RPC_X_SS_CONTEXT_DAMAGED                           1777
#define RPC_X_SS_HANDLES_MISMATCH                          1778
#define RPC_X_SS_CANNOT_GET_CALL_HANDLE                    1779
#define RPC_X_NULL_REF_POINTER                             1780
#define RPC_X_ENUM_VALUE_OUT_OF_RANGE                      1781
#define RPC_X_BYTE_COUNT_TOO_SMALL                         1782
#define RPC_X_BAD_STUB_DATA                                1783
#define ERROR_INVALID_USER_BUFFER                          1784
#define ERROR_UNRECOGNIZED_MEDIA                           1785
#define ERROR_NO_TRUST_LSA_SECRET                          1786
#define ERROR_NO_TRUST_SAM_ACCOUNT                         1787
#define ERROR_TRUSTED_DOMAIN_FAILURE                       1788
#define ERROR_TRUSTED_RELATIONSHIP_FAILURE                 1789
#define ERROR_TRUST_FAILURE                                1790
#define RPC_S_CALL_IN_PROGRESS                             1791
#define ERROR_NETLOGON_NOT_STARTED                         1792
#define ERROR_ACCOUNT_EXPIRED                              1793
#define ERROR_REDIRECTOR_HAS_OPEN_HANDLES                  1794
#define ERROR_PRINTER_DRIVER_ALREADY_INSTALLED             1795
#define ERROR_UNKNOWN_PORT                                 1796
#define ERROR_UNKNOWN_PRINTER_DRIVER                       1797
#define ERROR_UNKNOWN_PRINTPROCESSOR                       1798
#define ERROR_INVALID_SEPARATOR_FILE                       1799
#define ERROR_INVALID_PRIORITY                             1800
#define ERROR_INVALID_PRINTER_NAME                         1801
#define ERROR_PRINTER_ALREADY_EXISTS                       1802
#define ERROR_INVALID_PRINTER_COMMAND                      1803
#define ERROR_INVALID_DATATYPE                             1804
#define ERROR_INVALID_ENVIRONMENT                          1805
#define RPC_S_NO_MORE_BINDINGS                             1806
#define ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT            1807
#define ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT            1808
#define ERROR_NOLOGON_SERVER_TRUST_ACCOUNT                 1809
#define ERROR_DOMAIN_TRUST_INCONSISTENT                    1810
#define ERROR_SERVER_HAS_OPEN_HANDLES                      1811
#define ERROR_RESOURCE_DATA_NOT_FOUND                      1812
#define ERROR_RESOURCE_TYPE_NOT_FOUND                      1813
#define ERROR_RESOURCE_NAME_NOT_FOUND                      1814
#define ERROR_RESOURCE_LANG_NOT_FOUND                      1815
#define ERROR_NOT_ENOUGH_QUOTA                             1816
#define RPC_S_NO_INTERFACES                                1817
#define RPC_S_CALL_CANCELLED                               1818
#define RPC_S_BINDING_INCOMPLETE                           1819
#define RPC_S_COMM_FAILURE                                 1820
#define RPC_S_UNSUPPORTED_AUTHN_LEVEL                      1821
#define RPC_S_NO_PRINC_NAME                                1822
#define RPC_S_NOT_RPC_ERROR                                1823
#define RPC_S_UUID_LOCAL_ONLY                              1824
#define RPC_S_SEC_PKG_ERROR                                1825
#define RPC_S_NOT_CANCELLED                                1826
#define RPC_X_INVALID_ES_ACTION                            1827
#define RPC_X_WRONG_ES_VERSION                             1828
#define RPC_X_WRONG_STUB_VERSION                           1829
#define RPC_X_INVALID_PIPE_OBJECT                          1830
#define RPC_X_WRONG_PIPE_ORDER                             1831
#define RPC_X_WRONG_PIPE_VERSION                           1832
#define RPC_S_COOKIE_AUTH_FAILED                           1833
#define RPC_S_DO_NOT_DISTURB                               1834
#define RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED                 1835
#define RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH                  1836
#define RPC_S_GROUP_MEMBER_NOT_FOUND                       1898
#define EPT_S_CANT_CREATE                                  1899
#define RPC_S_INVALID_OBJECT                               1900
#define ERROR_INVALID_TIME                                 1901
#define ERROR_INVALID_FORM_NAME                            1902
#define ERROR_INVALID_FORM_SIZE                            1903
#define ERROR_ALREADY_WAITING                              1904
#define ERROR_PRINTER_DELETED                              1905
#define ERROR_INVALID_PRINTER_STATE                        1906
#define ERROR_PASSWORD_MUST_CHANGE                         1907
#define ERROR_DOMAIN_CONTROLLER_NOT_FOUND                  1908
#define ERROR_ACCOUNT_LOCKED_OUT                           1909
#define OR_INVALID_OXID                                    1910
#define OR_INVALID_OID                                     1911
#define OR_INVALID_SET                                     1912
#define RPC_S_SEND_INCOMPLETE                              1913
#define RPC_S_INVALID_ASYNC_HANDLE                         1914
#define RPC_S_INVALID_ASYNC_CALL                           1915
#define RPC_X_PIPE_CLOSED                                  1916
#define RPC_X_PIPE_DISCIPLINE_ERROR                        1917
#define RPC_X_PIPE_EMPTY                                   1918
#define ERROR_NO_SITENAME                                  1919
#define ERROR_CANT_ACCESS_FILE                             1920
#define ERROR_CANT_RESOLVE_FILENAME                        1921
#define RPC_S_ENTRY_TYPE_MISMATCH                          1922
#define RPC_S_NOT_ALL_OBJS_EXPORTED                        1923
#define RPC_S_INTERFACE_NOT_EXPORTED                       1924
#define RPC_S_PROFILE_NOT_ADDED                            1925
#define RPC_S_PRF_ELT_NOT_ADDED                            1926
#define RPC_S_PRF_ELT_NOT_REMOVED                          1927
#define RPC_S_GRP_ELT_NOT_ADDED                            1928
#define RPC_S_GRP_ELT_NOT_REMOVED                          1929
#define ERROR_KM_DRIVER_BLOCKED                            1930
#define ERROR_CONTEXT_EXPIRED                              1931
#define ERROR_PER_USER_TRUST_QUOTA_EXCEEDED                1932
#define ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED                1933
#define ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED             1934
#define ERROR_AUTHENTICATION_FIREWALL_FAILED               1935
#define ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED             1936
#define ERROR_NTLM_BLOCKED                                 1937
#define ERROR_PASSWORD_CHANGE_REQUIRED                     1938
#define ERROR_LOST_MODE_LOGON_RESTRICTION                  1939
#define ERROR_INVALID_PIXEL_FORMAT                         2000
#define ERROR_BAD_DRIVER                                   2001
#define ERROR_INVALID_WINDOW_STYLE                         2002
#define ERROR_METAFILE_NOT_SUPPORTED                       2003
#define ERROR_TRANSFORM_NOT_SUPPORTED                      2004
#define ERROR_CLIPPING_NOT_SUPPORTED                       2005
#define ERROR_INVALID_CMM                                  2010
#define ERROR_INVALID_PROFILE                              2011
#define ERROR_TAG_NOT_FOUND                                2012
#define ERROR_TAG_NOT_PRESENT                              2013
#define ERROR_DUPLICATE_TAG                                2014
#define ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE           2015
#define ERROR_PROFILE_NOT_FOUND                            2016
#define ERROR_INVALID_COLORSPACE                           2017
#define ERROR_ICM_NOT_ENABLED                              2018
#define ERROR_DELETING_ICM_XFORM                           2019
#define ERROR_INVALID_TRANSFORM                            2020
#define ERROR_COLORSPACE_MISMATCH                          2021
#define ERROR_INVALID_COLORINDEX                           2022
#define ERROR_CONNECTED_OTHER_PASSWORD                     2108
#define ERROR_BAD_USERNAME                                 2202
#define ERROR_NOT_CONNECTED                                2250
#define ERROR_OPEN_FILES                                   2401
#define ERROR_ACTIVE_CONNECTIONS                           2402
#define ERROR_DEVICE_IN_USE                                2404
#define ERROR_UNKNOWN_PRINT_MONITOR                        3000
#define ERROR_PRINTER_DRIVER_IN_USE                        3001
#define ERROR_SPOOL_FILE_NOT_FOUND                         3002
#define ERROR_SPL_NO_STARTDOC                              3003
#define ERROR_SPL_NO_ADDJOB                                3004
#define ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED            3005
#define ERROR_PRINT_MONITOR_ALREADY_INSTALLED              3006
#define ERROR_INVALID_PRINT_MONITOR                        3007
#define ERROR_PRINT_MONITOR_IN_USE                         3008
#define ERROR_PRINTER_HAS_JOBS_QUEUED                      3009
#define ERROR_SUCCESS_REBOOT_REQUIRED                      3010
#define ERROR_SUCCESS_RESTART_REQUIRED                     3011
#define ERROR_PRINTER_NOT_FOUND                            3012
#define ERROR_PRINTER_DRIVER_WARNED                        3013
#define ERROR_PRINTER_DRIVER_BLOCKED                       3014
#define ERROR_REQUEST_PAUSED                               3050
#define ERROR_WINS_INTERNAL                                4000
#define ERROR_CAN_NOT_DEL_LOCAL_WINS                       4001
#define ERROR_STATIC_INIT                                  4002
#define ERROR_INC_BACKUP                                   4003
#define ERROR_FULL_BACKUP                                  4004
#define ERROR_REC_NON_EXISTENT                             4005
#define ERROR_RPL_NOT_ALLOWED                              4006
#define ERROR_DHCP_ADDRESS_CONFLICT                        4100
#define ERROR_WMI_GUID_NOT_FOUND                           4200
#define ERROR_WMI_INSTANCE_NOT_FOUND                       4201
#define ERROR_WMI_ITEMID_NOT_FOUND                         4202
#define ERROR_WMI_TRY_AGAIN                                4203
#define ERROR_WMI_DP_NOT_FOUND                             4204
#define ERROR_WMI_UNRESOLVED_INSTANCE_REF                  4205
#define ERROR_WMI_ALREADY_ENABLED                          4206
#define ERROR_WMI_GUID_DISCONNECTED                        4207
#define ERROR_WMI_SERVER_UNAVAILABLE                       4208
#define ERROR_WMI_DP_FAILED                                4209
#define ERROR_WMI_INVALID_MOF                              4210
#define ERROR_WMI_INVALID_REGINFO                          4211
#define ERROR_WMI_ALREADY_DISABLED                         4212
#define ERROR_WMI_READ_ONLY                                4213
#define ERROR_WMI_SET_FAILURE                              4214
#define ERROR_INVALID_MEDIA                                4300
#define ERROR_INVALID_LIBRARY                              4301
#define ERROR_INVALID_MEDIA_POOL                           4302
#define ERROR_DRIVE_MEDIA_MISMATCH                         4303
#define ERROR_MEDIA_OFFLINE                                4304
#define ERROR_LIBRARY_OFFLINE                              4305
#define ERROR_EMPTY                                        4306
#define ERROR_NOT_EMPTY                                    4307
#define ERROR_MEDIA_UNAVAILABLE                            4308
#define ERROR_RESOURCE_DISABLED                            4309
#define ERROR_INVALID_CLEANER                              4310
#define ERROR_UNABLE_TO_CLEAN                              4311
#define ERROR_OBJECT_NOT_FOUND                             4312
#define ERROR_DATABASE_FAILURE                             4313
#define ERROR_DATABASE_FULL                                4314
#define ERROR_MEDIA_INCOMPATIBLE                           4315
#define ERROR_RESOURCE_NOT_PRESENT                         4316
#define ERROR_INVALID_OPERATION                            4317
#define ERROR_MEDIA_NOT_AVAILABLE                          4318
#define ERROR_DEVICE_NOT_AVAILABLE                         4319
#define ERROR_REQUEST_REFUSED                              4320
#define ERROR_INVALID_DRIVE_OBJECT                         4321
#define ERROR_LIBRARY_FULL                                 4322
#define ERROR_MEDIUM_NOT_ACCESSIBLE                        4323
#define ERROR_UNABLE_TO_LOAD_MEDIUM                        4324
#define ERROR_UNABLE_TO_INVENTORY_DRIVE                    4325
#define ERROR_UNABLE_TO_INVENTORY_SLOT                     4326
#define ERROR_UNABLE_TO_INVENTORY_TRANSPORT                4327
#define ERROR_TRANSPORT_FULL                               4328
#define ERROR_CONTROLLING_IEPORT                           4329
#define ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA                4330
#define ERROR_CLEANER_SLOT_SET                             4331
#define ERROR_CLEANER_SLOT_NOT_SET                         4332
#define ERROR_CLEANER_CARTRIDGE_SPENT                      4333
#define ERROR_UNEXPECTED_OMID                              4334
#define ERROR_CANT_DELETE_LAST_ITEM                        4335
#define ERROR_MESSAGE_EXCEEDS_MAX_SIZE                     4336
#define ERROR_VOLUME_CONTAINS_SYS_FILES                    4337
#define ERROR_INDIGENOUS_TYPE                              4338
#define ERROR_NO_SUPPORTING_DRIVES                         4339
#define ERROR_CLEANER_CARTRIDGE_INSTALLED                  4340
#define ERROR_FILE_OFFLINE                                 4350
#define ERROR_REMOTE_STORAGE_NOT_ACTIVE                    4351
#define ERROR_REMOTE_STORAGE_MEDIA_ERROR                   4352
#define ERROR_NOT_A_REPARSE_POINT                          4390
#define ERROR_REPARSE_ATTRIBUTE_CONFLICT                   4391
#define ERROR_INVALID_REPARSE_DATA                         4392
#define ERROR_REPARSE_TAG_INVALID                          4393
#define ERROR_REPARSE_TAG_MISMATCH                         4394
#define ERROR_REPARSE_POINT_ENCOUNTERED                    4395
#define ERROR_APP_DATA_NOT_FOUND                           4400
#define ERROR_APP_DATA_EXPIRED                             4401
#define ERROR_APP_DATA_CORRUPT                             4402
#define ERROR_APP_DATA_LIMIT_EXCEEDED                      4403
#define ERROR_APP_DATA_REBOOT_REQUIRED                     4404
#define ERROR_SECUREBOOT_ROLLBACK_DETECTED                 4420
#define ERROR_SECUREBOOT_POLICY_VIOLATION                  4421
#define ERROR_SECUREBOOT_INVALID_POLICY                    4422
#define ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND        4423
#define ERROR_SECUREBOOT_POLICY_NOT_SIGNED                 4424
#define ERROR_SECUREBOOT_NOT_ENABLED                       4425
#define ERROR_SECUREBOOT_FILE_REPLACED                     4426
#define ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED             4427
#define ERROR_SECUREBOOT_POLICY_UNKNOWN                    4428
#define ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION 4429
#define ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH              4430
#define ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED          4431
#define ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH           4432
#define ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING      4433
#define ERROR_SECUREBOOT_NOT_BASE_POLICY                   4434
#define ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY           4435
#define ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED               4440
#define ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED              4441
#define ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED              4442
#define ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED             4443
#define ERROR_ALREADY_HAS_STREAM_ID                        4444
#define ERROR_SMR_GARBAGE_COLLECTION_REQUIRED              4445
#define ERROR_VOLUME_NOT_SIS_ENABLED                       4500
#define ERROR_DEPENDENT_RESOURCE_EXISTS                    5001
#define ERROR_DEPENDENCY_NOT_FOUND                         5002
#define ERROR_DEPENDENCY_ALREADY_EXISTS                    5003
#define ERROR_RESOURCE_NOT_ONLINE                          5004
#define ERROR_HOST_NODE_NOT_AVAILABLE                      5005
#define ERROR_RESOURCE_NOT_AVAILABLE                       5006
#define ERROR_RESOURCE_NOT_FOUND                           5007
#define ERROR_SHUTDOWN_CLUSTER                             5008
#define ERROR_CANT_EVICT_ACTIVE_NODE                       5009
#define ERROR_OBJECT_ALREADY_EXISTS                        5010
#define ERROR_OBJECT_IN_LIST                               5011
#define ERROR_GROUP_NOT_AVAILABLE                          5012
#define ERROR_GROUP_NOT_FOUND                              5013
#define ERROR_GROUP_NOT_ONLINE                             5014
#define ERROR_HOST_NODE_NOT_RESOURCE_OWNER                 5015
#define ERROR_HOST_NODE_NOT_GROUP_OWNER                    5016
#define ERROR_RESMON_CREATE_FAILED                         5017
#define ERROR_RESMON_ONLINE_FAILED                         5018
#define ERROR_RESOURCE_ONLINE                              5019
#define ERROR_QUORUM_RESOURCE                              5020
#define ERROR_NOT_QUORUM_CAPABLE                           5021
#define ERROR_CLUSTER_SHUTTING_DOWN                        5022
#define ERROR_INVALID_STATE                                5023
#define ERROR_RESOURCE_PROPERTIES_STORED                   5024
#define ERROR_NOT_QUORUM_CLASS                             5025
#define ERROR_CORE_RESOURCE                                5026
#define ERROR_QUORUM_RESOURCE_ONLINE_FAILED                5027
#define ERROR_QUORUMLOG_OPEN_FAILED                        5028
#define ERROR_CLUSTERLOG_CORRUPT                           5029
#define ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE            5030
#define ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE                   5031
#define ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND                5032
#define ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE                  5033
#define ERROR_QUORUM_OWNER_ALIVE                           5034
#define ERROR_NETWORK_NOT_AVAILABLE                        5035
#define ERROR_NODE_NOT_AVAILABLE                           5036
#define ERROR_ALL_NODES_NOT_AVAILABLE                      5037
#define ERROR_RESOURCE_FAILED                              5038
#define ERROR_CLUSTER_INVALID_NODE                         5039
#define ERROR_CLUSTER_NODE_EXISTS                          5040
#define ERROR_CLUSTER_JOIN_IN_PROGRESS                     5041
#define ERROR_CLUSTER_NODE_NOT_FOUND                       5042
#define ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND                 5043
#define ERROR_CLUSTER_NETWORK_EXISTS                       5044
#define ERROR_CLUSTER_NETWORK_NOT_FOUND                    5045
#define ERROR_CLUSTER_NETINTERFACE_EXISTS                  5046
#define ERROR_CLUSTER_NETINTERFACE_NOT_FOUND               5047
#define ERROR_CLUSTER_INVALID_REQUEST                      5048
#define ERROR_CLUSTER_INVALID_NETWORK_PROVIDER             5049
#define ERROR_CLUSTER_NODE_DOWN                            5050
#define ERROR_CLUSTER_NODE_UNREACHABLE                     5051
#define ERROR_CLUSTER_NODE_NOT_MEMBER                      5052
#define ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS                 5053
#define ERROR_CLUSTER_INVALID_NETWORK                      5054
#define ERROR_CLUSTER_NODE_UP                              5056
#define ERROR_CLUSTER_IPADDR_IN_USE                        5057
#define ERROR_CLUSTER_NODE_NOT_PAUSED                      5058
#define ERROR_CLUSTER_NO_SECURITY_CONTEXT                  5059
#define ERROR_CLUSTER_NETWORK_NOT_INTERNAL                 5060
#define ERROR_CLUSTER_NODE_ALREADY_UP                      5061
#define ERROR_CLUSTER_NODE_ALREADY_DOWN                    5062
#define ERROR_CLUSTER_NETWORK_ALREADY_ONLINE               5063
#define ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE              5064
#define ERROR_CLUSTER_NODE_ALREADY_MEMBER                  5065
#define ERROR_CLUSTER_LAST_INTERNAL_NETWORK                5066
#define ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS               5067
#define ERROR_INVALID_OPERATION_ON_QUORUM                  5068
#define ERROR_DEPENDENCY_NOT_ALLOWED                       5069
#define ERROR_CLUSTER_NODE_PAUSED                          5070
#define ERROR_NODE_CANT_HOST_RESOURCE                      5071
#define ERROR_CLUSTER_NODE_NOT_READY                       5072
#define ERROR_CLUSTER_NODE_SHUTTING_DOWN                   5073
#define ERROR_CLUSTER_JOIN_ABORTED                         5074
#define ERROR_CLUSTER_INCOMPATIBLE_VERSIONS                5075
#define ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED         5076
#define ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED                5077
#define ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND              5078
#define ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED                5079
#define ERROR_CLUSTER_RESNAME_NOT_FOUND                    5080
#define ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED           5081
#define ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST                5082
#define ERROR_CLUSTER_DATABASE_SEQMISMATCH                 5083
#define ERROR_RESMON_INVALID_STATE                         5084
#define ERROR_CLUSTER_GUM_NOT_LOCKER                       5085
#define ERROR_QUORUM_DISK_NOT_FOUND                        5086
#define ERROR_DATABASE_BACKUP_CORRUPT                      5087
#define ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT            5088
#define ERROR_RESOURCE_PROPERTY_UNCHANGEABLE               5089
#define ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE             5890
#define ERROR_CLUSTER_QUORUMLOG_NOT_FOUND                  5891
#define ERROR_CLUSTER_MEMBERSHIP_HALT                      5892
#define ERROR_CLUSTER_INSTANCE_ID_MISMATCH                 5893
#define ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP             5894
#define ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH          5895
#define ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP                5896
#define ERROR_CLUSTER_PARAMETER_MISMATCH                   5897
#define ERROR_NODE_CANNOT_BE_CLUSTERED                     5898
#define ERROR_CLUSTER_WRONG_OS_VERSION                     5899
#define ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME         5900
#define ERROR_CLUSCFG_ALREADY_COMMITTED                    5901
#define ERROR_CLUSCFG_ROLLBACK_FAILED                      5902
#define ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT    5903
#define ERROR_CLUSTER_OLD_VERSION                          5904
#define ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME        5905
#define ERROR_CLUSTER_NO_NET_ADAPTERS                      5906
#define ERROR_CLUSTER_POISONED                             5907
#define ERROR_CLUSTER_GROUP_MOVING                         5908
#define ERROR_CLUSTER_RESOURCE_TYPE_BUSY                   5909
#define ERROR_RESOURCE_CALL_TIMED_OUT                      5910
#define ERROR_INVALID_CLUSTER_IPV6_ADDRESS                 5911
#define ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION            5912
#define ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS              5913
#define ERROR_CLUSTER_PARTIAL_SEND                         5914
#define ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION            5915
#define ERROR_CLUSTER_INVALID_STRING_TERMINATION           5916
#define ERROR_CLUSTER_INVALID_STRING_FORMAT                5917
#define ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS     5918
#define ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS 5919
#define ERROR_CLUSTER_NULL_DATA                            5920
#define ERROR_CLUSTER_PARTIAL_READ                         5921
#define ERROR_CLUSTER_PARTIAL_WRITE                        5922
#define ERROR_CLUSTER_CANT_DESERIALIZE_DATA                5923
#define ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT         5924
#define ERROR_CLUSTER_NO_QUORUM                            5925
#define ERROR_CLUSTER_INVALID_IPV6_NETWORK                 5926
#define ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK          5927
#define ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP             5928
#define ERROR_DEPENDENCY_TREE_TOO_COMPLEX                  5929
#define ERROR_EXCEPTION_IN_RESOURCE_CALL                   5930
#define ERROR_CLUSTER_RHS_FAILED_INITIALIZATION            5931
#define ERROR_CLUSTER_NOT_INSTALLED                        5932
#define ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE 5933
#define ERROR_CLUSTER_MAX_NODES_IN_CLUSTER                 5934
#define ERROR_CLUSTER_TOO_MANY_NODES                       5935
#define ERROR_CLUSTER_OBJECT_ALREADY_USED                  5936
#define ERROR_NONCORE_GROUPS_FOUND                         5937
#define ERROR_FILE_SHARE_RESOURCE_CONFLICT                 5938
#define ERROR_CLUSTER_EVICT_INVALID_REQUEST                5939
#define ERROR_CLUSTER_SINGLETON_RESOURCE                   5940
#define ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE             5941
#define ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED             5942
#define ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR         5943
#define ERROR_CLUSTER_GROUP_BUSY                           5944
#define ERROR_CLUSTER_NOT_SHARED_VOLUME                    5945
#define ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR          5946
#define ERROR_CLUSTER_SHARED_VOLUMES_IN_USE                5947
#define ERROR_CLUSTER_USE_SHARED_VOLUMES_API               5948
#define ERROR_CLUSTER_BACKUP_IN_PROGRESS                   5949
#define ERROR_NON_CSV_PATH                                 5950
#define ERROR_CSV_VOLUME_NOT_LOCAL                         5951
#define ERROR_CLUSTER_WATCHDOG_TERMINATING                 5952
#define ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES 5953
#define ERROR_CLUSTER_INVALID_NODE_WEIGHT                  5954
#define ERROR_CLUSTER_RESOURCE_VETOED_CALL                 5955
#define ERROR_RESMON_SYSTEM_RESOURCES_LACKING              5956
#define ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION 5957
#define ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE 5958
#define ERROR_CLUSTER_GROUP_QUEUED                         5959
#define ERROR_CLUSTER_RESOURCE_LOCKED_STATUS               5960
#define ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED   5961
#define ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS               5962
#define ERROR_CLUSTER_DISK_NOT_CONNECTED                   5963
#define ERROR_DISK_NOT_CSV_CAPABLE                         5964
#define ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE            5965
#define ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED             5966
#define ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED         5967
#define ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES             5968
#define ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES 5969
#define ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE      5970
#define ERROR_CLUSTER_AFFINITY_CONFLICT                    5971
#define ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE  5972
#define ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS        5973
#define ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED     5974
#define ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED             5975
#define ERROR_CLUSTER_UPGRADE_IN_PROGRESS                  5976
#define ERROR_CLUSTER_UPGRADE_INCOMPLETE                   5977
#define ERROR_CLUSTER_NODE_IN_GRACE_PERIOD                 5978
#define ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT                 5979
#define ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER               5980
#define ERROR_CLUSTER_RESOURCE_NOT_MONITORED               5981
#define ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED 5982
#define ERROR_CLUSTER_RESOURCE_IS_REPLICATED               5983
#define ERROR_CLUSTER_NODE_ISOLATED                        5984
#define ERROR_CLUSTER_NODE_QUARANTINED                     5985
#define ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED     5986
#define ERROR_CLUSTER_SPACE_DEGRADED                       5987
#define ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED       5988
#define ERROR_CLUSTER_CSV_INVALID_HANDLE                   5989
#define ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR    5990
#define ERROR_GROUPSET_NOT_AVAILABLE                       5991
#define ERROR_GROUPSET_NOT_FOUND                           5992
#define ERROR_GROUPSET_CANT_PROVIDE                        5993
#define ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND        5994
#define ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY       5995
#define ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION   5996
#define ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS   5997
#define ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME 5998
#define ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE    5999
#define ERROR_ENCRYPTION_FAILED                            6000
#define ERROR_DECRYPTION_FAILED                            6001
#define ERROR_FILE_ENCRYPTED                               6002
#define ERROR_NO_RECOVERY_POLICY                           6003
#define ERROR_NO_EFS                                       6004
#define ERROR_WRONG_EFS                                    6005
#define ERROR_NO_USER_KEYS                                 6006
#define ERROR_FILE_NOT_ENCRYPTED                           6007
#define ERROR_NOT_EXPORT_FORMAT                            6008
#define ERROR_FILE_READ_ONLY                               6009
#define ERROR_DIR_EFS_DISALLOWED                           6010
#define ERROR_EFS_SERVER_NOT_TRUSTED                       6011
#define ERROR_EFS_ALG_BLOB_TOO_BIG                         6013
#define ERROR_VOLUME_NOT_SUPPORT_EFS                       6014
#define ERROR_EFS_DISABLED                                 6015
#define ERROR_EFS_VERSION_NOT_SUPPORT                      6016
#define ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE        6017
#define ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER             6018
#define ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE        6019
#define ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE             6020
#define ERROR_CS_ENCRYPTION_FILE_NOT_CSE                   6021
#define ERROR_ENCRYPTION_POLICY_DENIES_OPERATION           6022
#define ERROR_NO_BROWSER_SERVERS_FOUND                     6118
#define SCHED_E_SERVICE_NOT_LOCALSYSTEM                    6200
#define ERROR_TRANSACTIONAL_CONFLICT                       6800
#define ERROR_RM_NOT_ACTIVE                                6801
#define ERROR_RM_METADATA_CORRUPT                          6802
#define ERROR_DIRECTORY_NOT_RM                             6803
#define ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE              6805
#define ERROR_LOG_RESIZE_INVALID_SIZE                      6806
#define ERROR_OBJECT_NO_LONGER_EXISTS                      6807
#define ERROR_STREAM_MINIVERSION_NOT_FOUND                 6808
#define ERROR_STREAM_MINIVERSION_NOT_VALID                 6809
#define ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION 6810
#define ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT     6811
#define ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS         6812
#define ERROR_REMOTE_FILE_VERSION_MISMATCH                 6814
#define ERROR_HANDLE_NO_LONGER_VALID                       6815
#define ERROR_NO_TXF_METADATA                              6816
#define ERROR_LOG_CORRUPTION_DETECTED                      6817
#define ERROR_CANT_RECOVER_WITH_HANDLE_OPEN                6818
#define ERROR_RM_DISCONNECTED                              6819
#define ERROR_ENLISTMENT_NOT_SUPERIOR                      6820
#define ERROR_RECOVERY_NOT_NEEDED                          6821
#define ERROR_RM_ALREADY_STARTED                           6822
#define ERROR_FILE_IDENTITY_NOT_PERSISTENT                 6823
#define ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY          6824
#define ERROR_CANT_CROSS_RM_BOUNDARY                       6825
#define ERROR_TXF_DIR_NOT_EMPTY                            6826
#define ERROR_INDOUBT_TRANSACTIONS_EXIST                   6827
#define ERROR_TM_VOLATILE                                  6828
#define ERROR_ROLLBACK_TIMER_EXPIRED                       6829
#define ERROR_TXF_ATTRIBUTE_CORRUPT                        6830
#define ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION               6831
#define ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED               6832
#define ERROR_LOG_GROWTH_FAILED                            6833
#define ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE        6834
#define ERROR_TXF_METADATA_ALREADY_PRESENT                 6835
#define ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET          6836
#define ERROR_TRANSACTION_REQUIRED_PROMOTION               6837
#define ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION           6838
#define ERROR_TRANSACTIONS_NOT_FROZEN                      6839
#define ERROR_TRANSACTION_FREEZE_IN_PROGRESS               6840
#define ERROR_NOT_SNAPSHOT_VOLUME                          6841
#define ERROR_NO_SAVEPOINT_WITH_OPEN_FILES                 6842
#define ERROR_DATA_LOST_REPAIR                             6843
#define ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION            6844
#define ERROR_TM_IDENTITY_MISMATCH                         6845
#define ERROR_FLOATED_SECTION                              6846
#define ERROR_CANNOT_ACCEPT_TRANSACTED_WORK                6847
#define ERROR_CANNOT_ABORT_TRANSACTIONS                    6848
#define ERROR_BAD_CLUSTERS                                 6849
#define ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION       6850
#define ERROR_VOLUME_DIRTY                                 6851
#define ERROR_NO_LINK_TRACKING_IN_TRANSACTION              6852
#define ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION       6853
#define ERROR_EXPIRED_HANDLE                               6854
#define ERROR_TRANSACTION_NOT_ENLISTED                     6855
#define ERROR_CTX_WINSTATION_NAME_INVALID                  7001
#define ERROR_CTX_INVALID_PD                               7002
#define ERROR_CTX_PD_NOT_FOUND                             7003
#define ERROR_CTX_WD_NOT_FOUND                             7004
#define ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY               7005
#define ERROR_CTX_SERVICE_NAME_COLLISION                   7006
#define ERROR_CTX_CLOSE_PENDING                            7007
#define ERROR_CTX_NO_OUTBUF                                7008
#define ERROR_CTX_MODEM_INF_NOT_FOUND                      7009
#define ERROR_CTX_INVALID_MODEMNAME                        7010
#define ERROR_CTX_MODEM_RESPONSE_ERROR                     7011
#define ERROR_CTX_MODEM_RESPONSE_TIMEOUT                   7012
#define ERROR_CTX_MODEM_RESPONSE_NO_CARRIER                7013
#define ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE               7014
#define ERROR_CTX_MODEM_RESPONSE_BUSY                      7015
#define ERROR_CTX_MODEM_RESPONSE_VOICE                     7016
#define ERROR_CTX_TD_ERROR                                 7017
#define ERROR_CTX_WINSTATION_NOT_FOUND                     7022
#define ERROR_CTX_WINSTATION_ALREADY_EXISTS                7023
#define ERROR_CTX_WINSTATION_BUSY                          7024
#define ERROR_CTX_BAD_VIDEO_MODE                           7025
#define ERROR_CTX_GRAPHICS_INVALID                         7035
#define ERROR_CTX_LOGON_DISABLED                           7037
#define ERROR_CTX_NOT_CONSOLE                              7038
#define ERROR_CTX_CLIENT_QUERY_TIMEOUT                     7040
#define ERROR_CTX_CONSOLE_DISCONNECT                       7041
#define ERROR_CTX_CONSOLE_CONNECT                          7042
#define ERROR_CTX_SHADOW_DENIED                            7044
#define ERROR_CTX_WINSTATION_ACCESS_DENIED                 7045
#define ERROR_CTX_INVALID_WD                               7049
#define ERROR_CTX_SHADOW_INVALID                           7050
#define ERROR_CTX_SHADOW_DISABLED                          7051
#define ERROR_CTX_CLIENT_LICENSE_IN_USE                    7052
#define ERROR_CTX_CLIENT_LICENSE_NOT_SET                   7053
#define ERROR_CTX_LICENSE_NOT_AVAILABLE                    7054
#define ERROR_CTX_LICENSE_CLIENT_INVALID                   7055
#define ERROR_CTX_LICENSE_EXPIRED                          7056
#define ERROR_CTX_SHADOW_NOT_RUNNING                       7057
#define ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE              7058
#define ERROR_ACTIVATION_COUNT_EXCEEDED                    7059
#define ERROR_CTX_WINSTATIONS_DISABLED                     7060
#define ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED                7061
#define ERROR_CTX_SESSION_IN_USE                           7062
#define ERROR_CTX_NO_FORCE_LOGOFF                          7063
#define ERROR_CTX_ACCOUNT_RESTRICTION                      7064
#define ERROR_RDP_PROTOCOL_ERROR                           7065
#define ERROR_CTX_CDM_CONNECT                              7066
#define ERROR_CTX_CDM_DISCONNECT                           7067
#define ERROR_CTX_SECURITY_LAYER_ERROR                     7068
#define ERROR_TS_INCOMPATIBLE_SESSIONS                     7069
#define ERROR_TS_VIDEO_SUBSYSTEM_ERROR                     7070
#define FRS_ERR_INVALID_API_SEQUENCE                       8001
#define FRS_ERR_STARTING_SERVICE                           8002
#define FRS_ERR_STOPPING_SERVICE                           8003
#define FRS_ERR_INTERNAL_API                               8004
#define FRS_ERR_INTERNAL                                   8005
#define FRS_ERR_SERVICE_COMM                               8006
#define FRS_ERR_INSUFFICIENT_PRIV                          8007
#define FRS_ERR_AUTHENTICATION                             8008
#define FRS_ERR_PARENT_INSUFFICIENT_PRIV                   8009
#define FRS_ERR_PARENT_AUTHENTICATION                      8010
#define FRS_ERR_CHILD_TO_PARENT_COMM                       8011
#define FRS_ERR_PARENT_TO_CHILD_COMM                       8012
#define FRS_ERR_SYSVOL_POPULATE                            8013
#define FRS_ERR_SYSVOL_POPULATE_TIMEOUT                    8014
#define FRS_ERR_SYSVOL_IS_BUSY                             8015
#define FRS_ERR_SYSVOL_DEMOTE                              8016
#define FRS_ERR_INVALID_SERVICE_PARAMETER                  8017
#define ERROR_DS_NOT_INSTALLED                             8200
#define ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY              8201
#define ERROR_DS_NO_ATTRIBUTE_OR_VALUE                     8202
#define ERROR_DS_INVALID_ATTRIBUTE_SYNTAX                  8203
#define ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED                  8204
#define ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS                 8205
#define ERROR_DS_BUSY                                      8206
#define ERROR_DS_UNAVAILABLE                               8207
#define ERROR_DS_NO_RIDS_ALLOCATED                         8208
#define ERROR_DS_NO_MORE_RIDS                              8209
#define ERROR_DS_INCORRECT_ROLE_OWNER                      8210
#define ERROR_DS_RIDMGR_INIT_ERROR                         8211
#define ERROR_DS_OBJ_CLASS_VIOLATION                       8212
#define ERROR_DS_CANT_ON_NON_LEAF                          8213
#define ERROR_DS_CANT_ON_RDN                               8214
#define ERROR_DS_CANT_MOD_OBJ_CLASS                        8215
#define ERROR_DS_CROSS_DOM_MOVE_ERROR                      8216
#define ERROR_DS_GC_NOT_AVAILABLE                          8217
#define ERROR_SHARED_POLICY                                8218
#define ERROR_POLICY_OBJECT_NOT_FOUND                      8219
#define ERROR_POLICY_ONLY_IN_DS                            8220
#define ERROR_PROMOTION_ACTIVE                             8221
#define ERROR_NO_PROMOTION_ACTIVE                          8222
#define ERROR_DS_OPERATIONS_ERROR                          8224
#define ERROR_DS_PROTOCOL_ERROR                            8225
#define ERROR_DS_TIMELIMIT_EXCEEDED                        8226
#define ERROR_DS_SIZELIMIT_EXCEEDED                        8227
#define ERROR_DS_ADMIN_LIMIT_EXCEEDED                      8228
#define ERROR_DS_COMPARE_FALSE                             8229
#define ERROR_DS_COMPARE_TRUE                              8230
#define ERROR_DS_AUTH_METHOD_NOT_SUPPORTED                 8231
#define ERROR_DS_STRONG_AUTH_REQUIRED                      8232
#define ERROR_DS_INAPPROPRIATE_AUTH                        8233
#define ERROR_DS_AUTH_UNKNOWN                              8234
#define ERROR_DS_REFERRAL                                  8235
#define ERROR_DS_UNAVAILABLE_CRIT_EXTENSION                8236
#define ERROR_DS_CONFIDENTIALITY_REQUIRED                  8237
#define ERROR_DS_INAPPROPRIATE_MATCHING                    8238
#define ERROR_DS_CONSTRAINT_VIOLATION                      8239
#define ERROR_DS_NO_SUCH_OBJECT                            8240
#define ERROR_DS_ALIAS_PROBLEM                             8241
#define ERROR_DS_INVALID_DN_SYNTAX                         8242
#define ERROR_DS_IS_LEAF                                   8243
#define ERROR_DS_ALIAS_DEREF_PROBLEM                       8244
#define ERROR_DS_UNWILLING_TO_PERFORM                      8245
#define ERROR_DS_LOOP_DETECT                               8246
#define ERROR_DS_NAMING_VIOLATION                          8247
#define ERROR_DS_OBJECT_RESULTS_TOO_LARGE                  8248
#define ERROR_DS_AFFECTS_MULTIPLE_DSAS                     8249
#define ERROR_DS_SERVER_DOWN                               8250
#define ERROR_DS_LOCAL_ERROR                               8251
#define ERROR_DS_ENCODING_ERROR                            8252
#define ERROR_DS_DECODING_ERROR                            8253
#define ERROR_DS_FILTER_UNKNOWN                            8254
#define ERROR_DS_PARAM_ERROR                               8255
#define ERROR_DS_NOT_SUPPORTED                             8256
#define ERROR_DS_NO_RESULTS_RETURNED                       8257
#define ERROR_DS_CONTROL_NOT_FOUND                         8258
#define ERROR_DS_CLIENT_LOOP                               8259
#define ERROR_DS_REFERRAL_LIMIT_EXCEEDED                   8260
#define ERROR_DS_SORT_CONTROL_MISSING                      8261
#define ERROR_DS_OFFSET_RANGE_ERROR                        8262
#define ERROR_DS_RIDMGR_DISABLED                           8263
#define ERROR_DS_ROOT_MUST_BE_NC                           8301
#define ERROR_DS_ADD_REPLICA_INHIBITED                     8302
#define ERROR_DS_ATT_NOT_DEF_IN_SCHEMA                     8303
#define ERROR_DS_MAX_OBJ_SIZE_EXCEEDED                     8304
#define ERROR_DS_OBJ_STRING_NAME_EXISTS                    8305
#define ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA                  8306
#define ERROR_DS_RDN_DOESNT_MATCH_SCHEMA                   8307
#define ERROR_DS_NO_REQUESTED_ATTS_FOUND                   8308
#define ERROR_DS_USER_BUFFER_TO_SMALL                      8309
#define ERROR_DS_ATT_IS_NOT_ON_OBJ                         8310
#define ERROR_DS_ILLEGAL_MOD_OPERATION                     8311
#define ERROR_DS_OBJ_TOO_LARGE                             8312
#define ERROR_DS_BAD_INSTANCE_TYPE                         8313
#define ERROR_DS_MASTERDSA_REQUIRED                        8314
#define ERROR_DS_OBJECT_CLASS_REQUIRED                     8315
#define ERROR_DS_MISSING_REQUIRED_ATT                      8316
#define ERROR_DS_ATT_NOT_DEF_FOR_CLASS                     8317
#define ERROR_DS_ATT_ALREADY_EXISTS                        8318
#define ERROR_DS_CANT_ADD_ATT_VALUES                       8320
#define ERROR_DS_SINGLE_VALUE_CONSTRAINT                   8321
#define ERROR_DS_RANGE_CONSTRAINT                          8322
#define ERROR_DS_ATT_VAL_ALREADY_EXISTS                    8323
#define ERROR_DS_CANT_REM_MISSING_ATT                      8324
#define ERROR_DS_CANT_REM_MISSING_ATT_VAL                  8325
#define ERROR_DS_ROOT_CANT_BE_SUBREF                       8326
#define ERROR_DS_NO_CHAINING                               8327
#define ERROR_DS_NO_CHAINED_EVAL                           8328
#define ERROR_DS_NO_PARENT_OBJECT                          8329
#define ERROR_DS_PARENT_IS_AN_ALIAS                        8330
#define ERROR_DS_CANT_MIX_MASTER_AND_REPS                  8331
#define ERROR_DS_CHILDREN_EXIST                            8332
#define ERROR_DS_OBJ_NOT_FOUND                             8333
#define ERROR_DS_ALIASED_OBJ_MISSING                       8334
#define ERROR_DS_BAD_NAME_SYNTAX                           8335
#define ERROR_DS_ALIAS_POINTS_TO_ALIAS                     8336
#define ERROR_DS_CANT_DEREF_ALIAS                          8337
#define ERROR_DS_OUT_OF_SCOPE                              8338
#define ERROR_DS_CANT_DELETE_DSA_OBJ                       8340
#define ERROR_DS_GENERIC_ERROR                             8341
#define ERROR_DS_DSA_MUST_BE_INT_MASTER                    8342
#define ERROR_DS_CLASS_NOT_DSA                             8343
#define ERROR_DS_INSUFF_ACCESS_RIGHTS                      8344
#define ERROR_DS_ILLEGAL_SUPERIOR                          8345
#define ERROR_DS_ATTRIBUTE_OWNED_BY_SAM                    8346
#define ERROR_DS_NAME_TOO_MANY_PARTS                       8347
#define ERROR_DS_NAME_TOO_LONG                             8348
#define ERROR_DS_NAME_VALUE_TOO_LONG                       8349
#define ERROR_DS_NAME_UNPARSEABLE                          8350
#define ERROR_DS_NAME_TYPE_UNKNOWN                         8351
#define ERROR_DS_NOT_AN_OBJECT                             8352
#define ERROR_DS_SEC_DESC_TOO_SHORT                        8353
#define ERROR_DS_SEC_DESC_INVALID                          8354
#define ERROR_DS_NO_DELETED_NAME                           8355
#define ERROR_DS_SUBREF_MUST_HAVE_PARENT                   8356
#define ERROR_DS_NCNAME_MUST_BE_NC                         8357
#define ERROR_DS_CANT_ADD_SYSTEM_ONLY                      8358
#define ERROR_DS_CLASS_MUST_BE_CONCRETE                    8359
#define ERROR_DS_INVALID_DMD                               8360
#define ERROR_DS_OBJ_GUID_EXISTS                           8361
#define ERROR_DS_NOT_ON_BACKLINK                           8362
#define ERROR_DS_NO_CROSSREF_FOR_NC                        8363
#define ERROR_DS_SHUTTING_DOWN                             8364
#define ERROR_DS_UNKNOWN_OPERATION                         8365
#define ERROR_DS_INVALID_ROLE_OWNER                        8366
#define ERROR_DS_COULDNT_CONTACT_FSMO                      8367
#define ERROR_DS_CROSS_NC_DN_RENAME                        8368
#define ERROR_DS_CANT_MOD_SYSTEM_ONLY                      8369
#define ERROR_DS_REPLICATOR_ONLY                           8370
#define ERROR_DS_OBJ_CLASS_NOT_DEFINED                     8371
#define ERROR_DS_OBJ_CLASS_NOT_SUBCLASS                    8372
#define ERROR_DS_NAME_REFERENCE_INVALID                    8373
#define ERROR_DS_CROSS_REF_EXISTS                          8374
#define ERROR_DS_CANT_DEL_MASTER_CROSSREF                  8375
#define ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD                8376
#define ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX                 8377
#define ERROR_DS_DUP_RDN                                   8378
#define ERROR_DS_DUP_OID                                   8379
#define ERROR_DS_DUP_MAPI_ID                               8380
#define ERROR_DS_DUP_SCHEMA_ID_GUID                        8381
#define ERROR_DS_DUP_LDAP_DISPLAY_NAME                     8382
#define ERROR_DS_SEMANTIC_ATT_TEST                         8383
#define ERROR_DS_SYNTAX_MISMATCH                           8384
#define ERROR_DS_EXISTS_IN_MUST_HAVE                       8385
#define ERROR_DS_EXISTS_IN_MAY_HAVE                        8386
#define ERROR_DS_NONEXISTENT_MAY_HAVE                      8387
#define ERROR_DS_NONEXISTENT_MUST_HAVE                     8388
#define ERROR_DS_AUX_CLS_TEST_FAIL                         8389
#define ERROR_DS_NONEXISTENT_POSS_SUP                      8390
#define ERROR_DS_SUB_CLS_TEST_FAIL                         8391
#define ERROR_DS_BAD_RDN_ATT_ID_SYNTAX                     8392
#define ERROR_DS_EXISTS_IN_AUX_CLS                         8393
#define ERROR_DS_EXISTS_IN_SUB_CLS                         8394
#define ERROR_DS_EXISTS_IN_POSS_SUP                        8395
#define ERROR_DS_RECALCSCHEMA_FAILED                       8396
#define ERROR_DS_TREE_DELETE_NOT_FINISHED                  8397
#define ERROR_DS_CANT_DELETE                               8398
#define ERROR_DS_ATT_SCHEMA_REQ_ID                         8399
#define ERROR_DS_BAD_ATT_SCHEMA_SYNTAX                     8400
#define ERROR_DS_CANT_CACHE_ATT                            8401
#define ERROR_DS_CANT_CACHE_CLASS                          8402
#define ERROR_DS_CANT_REMOVE_ATT_CACHE                     8403
#define ERROR_DS_CANT_REMOVE_CLASS_CACHE                   8404
#define ERROR_DS_CANT_RETRIEVE_DN                          8405
#define ERROR_DS_MISSING_SUPREF                            8406
#define ERROR_DS_CANT_RETRIEVE_INSTANCE                    8407
#define ERROR_DS_CODE_INCONSISTENCY                        8408
#define ERROR_DS_DATABASE_ERROR                            8409
#define ERROR_DS_GOVERNSID_MISSING                         8410
#define ERROR_DS_MISSING_EXPECTED_ATT                      8411
#define ERROR_DS_NCNAME_MISSING_CR_REF                     8412
#define ERROR_DS_SECURITY_CHECKING_ERROR                   8413
#define ERROR_DS_SCHEMA_NOT_LOADED                         8414
#define ERROR_DS_SCHEMA_ALLOC_FAILED                       8415
#define ERROR_DS_ATT_SCHEMA_REQ_SYNTAX                     8416
#define ERROR_DS_GCVERIFY_ERROR                            8417
#define ERROR_DS_DRA_SCHEMA_MISMATCH                       8418
#define ERROR_DS_CANT_FIND_DSA_OBJ                         8419
#define ERROR_DS_CANT_FIND_EXPECTED_NC                     8420
#define ERROR_DS_CANT_FIND_NC_IN_CACHE                     8421
#define ERROR_DS_CANT_RETRIEVE_CHILD                       8422
#define ERROR_DS_SECURITY_ILLEGAL_MODIFY                   8423
#define ERROR_DS_CANT_REPLACE_HIDDEN_REC                   8424
#define ERROR_DS_BAD_HIERARCHY_FILE                        8425
#define ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED              8426
#define ERROR_DS_CONFIG_PARAM_MISSING                      8427
#define ERROR_DS_COUNTING_AB_INDICES_FAILED                8428
#define ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED             8429
#define ERROR_DS_INTERNAL_FAILURE                          8430
#define ERROR_DS_UNKNOWN_ERROR                             8431
#define ERROR_DS_ROOT_REQUIRES_CLASS_TOP                   8432
#define ERROR_DS_REFUSING_FSMO_ROLES                       8433
#define ERROR_DS_MISSING_FSMO_SETTINGS                     8434
#define ERROR_DS_UNABLE_TO_SURRENDER_ROLES                 8435
#define ERROR_DS_DRA_GENERIC                               8436
#define ERROR_DS_DRA_INVALID_PARAMETER                     8437
#define ERROR_DS_DRA_BUSY                                  8438
#define ERROR_DS_DRA_BAD_DN                                8439
#define ERROR_DS_DRA_BAD_NC                                8440
#define ERROR_DS_DRA_DN_EXISTS                             8441
#define ERROR_DS_DRA_INTERNAL_ERROR                        8442
#define ERROR_DS_DRA_INCONSISTENT_DIT                      8443
#define ERROR_DS_DRA_CONNECTION_FAILED                     8444
#define ERROR_DS_DRA_BAD_INSTANCE_TYPE                     8445
#define ERROR_DS_DRA_OUT_OF_MEM                            8446
#define ERROR_DS_DRA_MAIL_PROBLEM                          8447
#define ERROR_DS_DRA_REF_ALREADY_EXISTS                    8448
#define ERROR_DS_DRA_REF_NOT_FOUND                         8449
#define ERROR_DS_DRA_OBJ_IS_REP_SOURCE                     8450
#define ERROR_DS_DRA_DB_ERROR                              8451
#define ERROR_DS_DRA_NO_REPLICA                            8452
#define ERROR_DS_DRA_ACCESS_DENIED                         8453
#define ERROR_DS_DRA_NOT_SUPPORTED                         8454
#define ERROR_DS_DRA_RPC_CANCELLED                         8455
#define ERROR_DS_DRA_SOURCE_DISABLED                       8456
#define ERROR_DS_DRA_SINK_DISABLED                         8457
#define ERROR_DS_DRA_NAME_COLLISION                        8458
#define ERROR_DS_DRA_SOURCE_REINSTALLED                    8459
#define ERROR_DS_DRA_MISSING_PARENT                        8460
#define ERROR_DS_DRA_PREEMPTED                             8461
#define ERROR_DS_DRA_ABANDON_SYNC                          8462
#define ERROR_DS_DRA_SHUTDOWN                              8463
#define ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET              8464
#define ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA             8465
#define ERROR_DS_DRA_EXTN_CONNECTION_FAILED                8466
#define ERROR_DS_INSTALL_SCHEMA_MISMATCH                   8467
#define ERROR_DS_DUP_LINK_ID                               8468
#define ERROR_DS_NAME_ERROR_RESOLVING                      8469
#define ERROR_DS_NAME_ERROR_NOT_FOUND                      8470
#define ERROR_DS_NAME_ERROR_NOT_UNIQUE                     8471
#define ERROR_DS_NAME_ERROR_NO_MAPPING                     8472
#define ERROR_DS_NAME_ERROR_DOMAIN_ONLY                    8473
#define ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING         8474
#define ERROR_DS_CONSTRUCTED_ATT_MOD                       8475
#define ERROR_DS_WRONG_OM_OBJ_CLASS                        8476
#define ERROR_DS_DRA_REPL_PENDING                          8477
#define ERROR_DS_DS_REQUIRED                               8478
#define ERROR_DS_INVALID_LDAP_DISPLAY_NAME                 8479
#define ERROR_DS_NON_BASE_SEARCH                           8480
#define ERROR_DS_CANT_RETRIEVE_ATTS                        8481
#define ERROR_DS_BACKLINK_WITHOUT_LINK                     8482
#define ERROR_DS_EPOCH_MISMATCH                            8483
#define ERROR_DS_SRC_NAME_MISMATCH                         8484
#define ERROR_DS_SRC_AND_DST_NC_IDENTICAL                  8485
#define ERROR_DS_DST_NC_MISMATCH                           8486
#define ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC                8487
#define ERROR_DS_SRC_GUID_MISMATCH                         8488
#define ERROR_DS_CANT_MOVE_DELETED_OBJECT                  8489
#define ERROR_DS_PDC_OPERATION_IN_PROGRESS                 8490
#define ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD                 8491
#define ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION               8492
#define ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS           8493
#define ERROR_DS_NC_MUST_HAVE_NC_PARENT                    8494
#define ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE                 8495
#define ERROR_DS_DST_DOMAIN_NOT_NATIVE                     8496
#define ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER          8497
#define ERROR_DS_CANT_MOVE_ACCOUNT_GROUP                   8498
#define ERROR_DS_CANT_MOVE_RESOURCE_GROUP                  8499
#define ERROR_DS_INVALID_SEARCH_FLAG                       8500
#define ERROR_DS_NO_TREE_DELETE_ABOVE_NC                   8501
#define ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE              8502
#define ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE  8503
#define ERROR_DS_SAM_INIT_FAILURE                          8504
#define ERROR_DS_SENSITIVE_GROUP_VIOLATION                 8505
#define ERROR_DS_CANT_MOD_PRIMARYGROUPID                   8506
#define ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD                   8507
#define ERROR_DS_NONSAFE_SCHEMA_CHANGE                     8508
#define ERROR_DS_SCHEMA_UPDATE_DISALLOWED                  8509
#define ERROR_DS_CANT_CREATE_UNDER_SCHEMA                  8510
#define ERROR_DS_INSTALL_NO_SRC_SCH_VERSION                8511
#define ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE         8512
#define ERROR_DS_INVALID_GROUP_TYPE                        8513
#define ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN        8514
#define ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN         8515
#define ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER             8516
#define ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER         8517
#define ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER          8518
#define ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER       8519
#define ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER  8520
#define ERROR_DS_HAVE_PRIMARY_MEMBERS                      8521
#define ERROR_DS_STRING_SD_CONVERSION_FAILED               8522
#define ERROR_DS_NAMING_MASTER_GC                          8523
#define ERROR_DS_LOOKUP_FAILURE                            8524
#define ERROR_DS_COULDNT_UPDATE_SPNS                       8525
#define ERROR_DS_CANT_RETRIEVE_SD                          8526
#define ERROR_DS_KEY_NOT_UNIQUE                            8527
#define ERROR_DS_WRONG_LINKED_ATT_SYNTAX                   8528
#define ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD                 8529
#define ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY                   8530
#define ERROR_DS_CANT_START                                8531
#define ERROR_DS_INIT_FAILURE                              8532
#define ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION              8533
#define ERROR_DS_SOURCE_DOMAIN_IN_FOREST                   8534
#define ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST          8535
#define ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED          8536
#define ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN               8537
#define ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER                 8538
#define ERROR_DS_SRC_SID_EXISTS_IN_FOREST                  8539
#define ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH         8540
#define ERROR_SAM_INIT_FAILURE                             8541
#define ERROR_DS_DRA_SCHEMA_INFO_SHIP                      8542
#define ERROR_DS_DRA_SCHEMA_CONFLICT                       8543
#define ERROR_DS_DRA_EARLIER_SCHEMA_CONLICT                8544
#define ERROR_DS_DRA_OBJ_NC_MISMATCH                       8545
#define ERROR_DS_NC_STILL_HAS_DSAS                         8546
#define ERROR_DS_GC_REQUIRED                               8547
#define ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY                8548
#define ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS                8549
#define ERROR_DS_CANT_ADD_TO_GC                            8550
#define ERROR_DS_NO_CHECKPOINT_WITH_PDC                    8551
#define ERROR_DS_SOURCE_AUDITING_NOT_ENABLED               8552
#define ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC               8553
#define ERROR_DS_INVALID_NAME_FOR_SPN                      8554
#define ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS              8555
#define ERROR_DS_UNICODEPWD_NOT_IN_QUOTES                  8556
#define ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED            8557
#define ERROR_DS_MUST_BE_RUN_ON_DST_DC                     8558
#define ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER             8559
#define ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ             8560
#define ERROR_DS_INIT_FAILURE_CONSOLE                      8561
#define ERROR_DS_SAM_INIT_FAILURE_CONSOLE                  8562
#define ERROR_DS_FOREST_VERSION_TOO_HIGH                   8563
#define ERROR_DS_DOMAIN_VERSION_TOO_HIGH                   8564
#define ERROR_DS_FOREST_VERSION_TOO_LOW                    8565
#define ERROR_DS_DOMAIN_VERSION_TOO_LOW                    8566
#define ERROR_DS_INCOMPATIBLE_VERSION                      8567
#define ERROR_DS_LOW_DSA_VERSION                           8568
#define ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN        8569
#define ERROR_DS_NOT_SUPPORTED_SORT_ORDER                  8570
#define ERROR_DS_NAME_NOT_UNIQUE                           8571
#define ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4            8572
#define ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER             8578
#define ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE      8579
#define ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC               8580
#define ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG               8581
#define ERROR_DS_MODIFYDN_WRONG_GRANDPARENT                8582
#define ERROR_DS_NAME_ERROR_TRUST_REFERRAL                 8583
#define ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER             8584
#define ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD             8585
#define ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2              8586
#define ERROR_DS_THREAD_LIMIT_EXCEEDED                     8587
#define ERROR_DS_NOT_CLOSEST                               8588
#define ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF        8589
#define ERROR_DS_SINGLE_USER_MODE_FAILED                   8590
#define ERROR_DS_NTDSCRIPT_SYNTAX_ERROR                    8591
#define ERROR_DS_NTDSCRIPT_PROCESS_ERROR                   8592
#define ERROR_DS_DIFFERENT_REPL_EPOCHS                     8593
#define ERROR_DS_DRS_EXTENSIONS_CHANGED                    8594
#define ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR 8595
#define ERROR_DS_NO_MSDS_INTID                             8596
#define ERROR_DS_DUP_MSDS_INTID                            8597
#define ERROR_DS_EXISTS_IN_RDNATTID                        8598
#define ERROR_DS_AUTHORIZATION_FAILED                      8599
#define ERROR_DS_INVALID_SCRIPT                            8600
#define ERROR_DS_REMOTE_CROSSREF_OP_FAILED                 8601
#define ERROR_DS_CROSS_REF_BUSY                            8602
#define ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN        8603
#define ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC             8604
#define ERROR_DS_DUPLICATE_ID_FOUND                        8605
#define ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT        8606
#define ERROR_DS_GROUP_CONVERSION_ERROR                    8607
#define ERROR_DS_CANT_MOVE_APP_BASIC_GROUP                 8608
#define ERROR_DS_CANT_MOVE_APP_QUERY_GROUP                 8609
#define ERROR_DS_ROLE_NOT_VERIFIED                         8610
#define ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL           8611
#define ERROR_DS_DOMAIN_RENAME_IN_PROGRESS                 8612
#define ERROR_DS_EXISTING_AD_CHILD_NC                      8613
#define ERROR_DS_REPL_LIFETIME_EXCEEDED                    8614
#define ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER            8615
#define ERROR_DS_LDAP_SEND_QUEUE_FULL                      8616
#define ERROR_DS_DRA_OUT_SCHEDULE_WINDOW                   8617
#define ERROR_DS_POLICY_NOT_KNOWN                          8618
#define ERROR_NO_SITE_SETTINGS_OBJECT                      8619
#define ERROR_NO_SECRETS                                   8620
#define ERROR_NO_WRITABLE_DC_FOUND                         8621
#define ERROR_DS_NO_SERVER_OBJECT                          8622
#define ERROR_DS_NO_NTDSA_OBJECT                           8623
#define ERROR_DS_NON_ASQ_SEARCH                            8624
#define ERROR_DS_AUDIT_FAILURE                             8625
#define ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE               8626
#define ERROR_DS_INVALID_SEARCH_FLAG_TUPLE                 8627
#define ERROR_DS_HIERARCHY_TABLE_TOO_DEEP                  8628
#define ERROR_DS_DRA_CORRUPT_UTD_VECTOR                    8629
#define ERROR_DS_DRA_SECRETS_DENIED                        8630
#define ERROR_DS_RESERVED_MAPI_ID                          8631
#define ERROR_DS_MAPI_ID_NOT_AVAILABLE                     8632
#define ERROR_DS_DRA_MISSING_KRBTGT_SECRET                 8633
#define ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST              8634
#define ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST                8635
#define ERROR_INVALID_USER_PRINCIPAL_NAME                  8636
#define ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS        8637
#define ERROR_DS_OID_NOT_FOUND                             8638
#define ERROR_DS_DRA_RECYCLED_TARGET                       8639
#define ERROR_DS_DISALLOWED_NC_REDIRECT                    8640
#define ERROR_DS_HIGH_ADLDS_FFL                            8641
#define ERROR_DS_HIGH_DSA_VERSION                          8642
#define ERROR_DS_LOW_ADLDS_FFL                             8643
#define ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION         8644
#define ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED            8645
#define ERROR_INCORRECT_ACCOUNT_TYPE                       8646
#define ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST            8647
#define ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST            8648
#define ERROR_DS_MISSING_FOREST_TRUST                      8649
#define ERROR_DS_VALUE_KEY_NOT_UNIQUE                      8650
#define DNS_ERROR_RCODE_FORMAT_ERROR                       9001
#define DNS_ERROR_RCODE_SERVER_FAILURE                     9002
#define DNS_ERROR_RCODE_NAME_ERROR                         9003
#define DNS_ERROR_RCODE_NOT_IMPLEMENTED                    9004
#define DNS_ERROR_RCODE_REFUSED                            9005
#define DNS_ERROR_RCODE_YXDOMAIN                           9006
#define DNS_ERROR_RCODE_YXRRSET                            9007
#define DNS_ERROR_RCODE_NXRRSET                            9008
#define DNS_ERROR_RCODE_NOTAUTH                            9009
#define DNS_ERROR_RCODE_NOTZONE                            9010
#define DNS_ERROR_RCODE_BADSIG                             9016
#define DNS_ERROR_RCODE_BADKEY                             9017
#define DNS_ERROR_RCODE_BADTIME                            9018
#define DNS_INFO_NO_RECORDS                                9501
#define DNS_ERROR_BAD_PACKET                               9502
#define DNS_ERROR_NO_PACKET                                9503
#define DNS_ERROR_RCODE                                    9504
#define DNS_ERROR_UNSECURE_PACKET                          9505
#define DNS_REQUEST_PENDING                                9506
#define DNS_ERROR_INVALID_TYPE                             9551
#define DNS_ERROR_INVALID_IP_ADDRESS                       9552
#define DNS_ERROR_INVALID_PROPERTY                         9553
#define DNS_ERROR_TRY_AGAIN_LATER                          9554
#define DNS_ERROR_NOT_UNIQUE                               9555
#define DNS_ERROR_NON_RFC_NAME                             9556
#define DNS_STATUS_FQDN                                    9557
#define DNS_STATUS_DOTTED_NAME                             9558
#define DNS_STATUS_SINGLE_PART_NAME                        9559
#define DNS_ERROR_INVALID_NAME_CHAR                        9560
#define DNS_ERROR_NUMERIC_NAME                             9561
#define DNS_ERROR_ZONE_DOES_NOT_EXIST                      9601
#define DNS_ERROR_NO_ZONE_INFO                             9602
#define DNS_ERROR_INVALID_ZONE_OPERATION                   9603
#define DNS_ERROR_ZONE_CONFIGURATION_ERROR                 9604
#define DNS_ERROR_ZONE_HAS_NO_SOA_RECORD                   9605
#define DNS_ERROR_ZONE_HAS_NO_NS_RECORDS                   9606
#define DNS_ERROR_ZONE_LOCKED                              9607
#define DNS_ERROR_ZONE_CREATION_FAILED                     9608
#define DNS_ERROR_ZONE_ALREADY_EXISTS                      9609
#define DNS_ERROR_AUTOZONE_ALREADY_EXISTS                  9610
#define DNS_ERROR_INVALID_ZONE_TYPE                        9611
#define DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP             9612
#define DNS_ERROR_ZONE_NOT_SECONDARY                       9613
#define DNS_ERROR_NEED_SECONDARY_ADDRESSES                 9614
#define DNS_ERROR_WINS_INIT_FAILED                         9615
#define DNS_ERROR_NEED_WINS_SERVERS                        9616
#define DNS_ERROR_NBSTAT_INIT_FAILED                       9617
#define DNS_ERROR_SOA_DELETE_INVALID                       9618
#define DNS_ERROR_PRIMARY_REQUIRES_DATAFILE                9651
#define DNS_ERROR_INVALID_DATAFILE_NAME                    9652
#define DNS_ERROR_DATAFILE_OPEN_FAILURE                    9653
#define DNS_ERROR_FILE_WRITEBACK_FAILED                    9654
#define DNS_ERROR_DATAFILE_PARSING                         9655
#define DNS_ERROR_RECORD_DOES_NOT_EXIST                    9701
#define DNS_ERROR_RECORD_FORMAT                            9702
#define DNS_ERROR_NODE_CREATION_FAILED                     9703
#define DNS_ERROR_UNKNOWN_RECORD_TYPE                      9704
#define DNS_ERROR_RECORD_TIMED_OUT                         9705
#define DNS_ERROR_NAME_NOT_IN_ZONE                         9706
#define DNS_ERROR_CNAME_LOOP                               9707
#define DNS_ERROR_NODE_IS_CNAME                            9708
#define DNS_ERROR_CNAME_COLLISION                          9709
#define DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT                 9710
#define DNS_ERROR_RECORD_ALREADY_EXISTS                    9711
#define DNS_ERROR_SECONDARY_DATA                           9712
#define DNS_ERROR_NO_CREATE_CACHE_DATA                     9713
#define DNS_ERROR_NAME_DOES_NOT_EXIST                      9714
#define DNS_WARNING_PTR_CREATE_FAILED                      9715
#define DNS_WARNING_DOMAIN_UNDELETED                       9716
#define DNS_ERROR_DS_UNAVAILABLE                           9717
#define DNS_ERROR_DS_ZONE_ALREADY_EXISTS                   9718
#define DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE                   9719
#define DNS_INFO_AXFR_COMPLETE                             9751
#define DNS_ERROR_AXFR                                     9752
#define DNS_INFO_ADDED_LOCAL_WINS                          9753
#define DNS_STATUS_CONTINUE_NEEDED                         9801
#define DNS_ERROR_NO_TCPIP                                 9851
#define DNS_ERROR_NO_DNS_SERVERS                           9852

/*
 * Also defined in winsock.h.
 *
 * All Windows Sockets error constants are biased by WSABASEERR from
 * the "normal"
 */
#define WSABASEERR                 10000
/*
 * Windows Sockets definitions of regular Microsoft C error constants
 */
#define WSAEINTR                   (WSABASEERR+4)
#define WSAEBADF                   (WSABASEERR+9)
#define WSAEACCES                  (WSABASEERR+13)
#define WSAEFAULT                  (WSABASEERR+14)
#define WSAEINVAL                  (WSABASEERR+22)
#define WSAEMFILE                  (WSABASEERR+24)

/*
 * Windows Sockets definitions of regular Berkeley error constants
 */
#define WSAEWOULDBLOCK             (WSABASEERR+35)
#define WSAEINPROGRESS             (WSABASEERR+36)
#define WSAEALREADY                (WSABASEERR+37)
#define WSAENOTSOCK                (WSABASEERR+38)
#define WSAEDESTADDRREQ            (WSABASEERR+39)
#define WSAEMSGSIZE                (WSABASEERR+40)
#define WSAEPROTOTYPE              (WSABASEERR+41)
#define WSAENOPROTOOPT             (WSABASEERR+42)
#define WSAEPROTONOSUPPORT         (WSABASEERR+43)
#define WSAESOCKTNOSUPPORT         (WSABASEERR+44)
#define WSAEOPNOTSUPP              (WSABASEERR+45)
#define WSAEPFNOSUPPORT            (WSABASEERR+46)
#define WSAEAFNOSUPPORT            (WSABASEERR+47)
#define WSAEADDRINUSE              (WSABASEERR+48)
#define WSAEADDRNOTAVAIL           (WSABASEERR+49)
#define WSAENETDOWN                (WSABASEERR+50)
#define WSAENETUNREACH             (WSABASEERR+51)
#define WSAENETRESET               (WSABASEERR+52)
#define WSAECONNABORTED            (WSABASEERR+53)
#define WSAECONNRESET              (WSABASEERR+54)
#define WSAENOBUFS                 (WSABASEERR+55)
#define WSAEISCONN                 (WSABASEERR+56)
#define WSAENOTCONN                (WSABASEERR+57)
#define WSAESHUTDOWN               (WSABASEERR+58)
#define WSAETOOMANYREFS            (WSABASEERR+59)
#define WSAETIMEDOUT               (WSABASEERR+60)
#define WSAECONNREFUSED            (WSABASEERR+61)
#define WSAELOOP                   (WSABASEERR+62)
#define WSAENAMETOOLONG            (WSABASEERR+63)
#define WSAEHOSTDOWN               (WSABASEERR+64)
#define WSAEHOSTUNREACH            (WSABASEERR+65)
#define WSAENOTEMPTY               (WSABASEERR+66)
#define WSAEPROCLIM                (WSABASEERR+67)
#define WSAEUSERS                  (WSABASEERR+68)
#define WSAEDQUOT                  (WSABASEERR+69)
#define WSAESTALE                  (WSABASEERR+70)
#define WSAEREMOTE                 (WSABASEERR+71)

/*
 * Extended Windows Sockets error constant definitions
 */
#define WSASYSNOTREADY             (WSABASEERR+91)
#define WSAVERNOTSUPPORTED         (WSABASEERR+92)
#define WSANOTINITIALISED          (WSABASEERR+93)
#define WSAEDISCON                 (WSABASEERR+101)
#define WSAENOMORE                 (WSABASEERR+102)
#define WSAECANCELLED              (WSABASEERR+103)
#define WSAEINVALIDPROCTABLE       (WSABASEERR+104)
#define WSAEINVALIDPROVIDER        (WSABASEERR+105)
#define WSAEPROVIDERFAILEDINIT     (WSABASEERR+106)
#define WSASYSCALLFAILURE          (WSABASEERR+107)
#define WSASERVICE_NOT_FOUND       (WSABASEERR+108)
#define WSATYPE_NOT_FOUND          (WSABASEERR+109)
#define WSA_E_NO_MORE              (WSABASEERR+110)
#define WSA_E_CANCELLED            (WSABASEERR+111)
#define WSAEREFUSED                (WSABASEERR+112)


#define ERROR_SXS_SECTION_NOT_FOUND                        14000
#define ERROR_SXS_CANT_GEN_ACTCTX                          14001
#define ERROR_SXS_INVALID_ACTCTXDATA_FORMAT                14002
#define ERROR_SXS_ASSEMBLY_NOT_FOUND                       14003
#define ERROR_SXS_MANIFEST_FORMAT_ERROR                    14004
#define ERROR_SXS_MANIFEST_PARSE_ERROR                     14005
#define ERROR_SXS_ACTIVATION_CONTEXT_DISABLED              14006
#define ERROR_SXS_KEY_NOT_FOUND                            14007
#define ERROR_SXS_VERSION_CONFLICT                         14008
#define ERROR_SXS_WRONG_SECTION_TYPE                       14009
#define ERROR_SXS_THREAD_QUERIES_DISABLED                  14010
#define ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET              14011
#define ERROR_SXS_UNKNOWN_ENCODING_GROUP                   14012
#define ERROR_SXS_UNKNOWN_ENCODING                         14013
#define ERROR_SXS_INVALID_XML_NAMESPACE_URI                14014
#define ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED   14015
#define ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED   14016
#define ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE      14017
#define ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE       14018
#define ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE       14019
#define ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT    14020
#define ERROR_SXS_DUPLICATE_DLL_NAME                       14021
#define ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME               14022
#define ERROR_SXS_DUPLICATE_CLSID                          14023
#define ERROR_SXS_DUPLICATE_IID                            14024
#define ERROR_SXS_DUPLICATE_TLBID                          14025
#define ERROR_SXS_DUPLICATE_PROGID                         14026
#define ERROR_SXS_DUPLICATE_ASSEMBLY_NAME                  14027
#define ERROR_SXS_FILE_HASH_MISMATCH                       14028
#define ERROR_SXS_POLICY_PARSE_ERROR                       14029
#define ERROR_SXS_PROTECTION_RECOVERY_FAILED               14074
#define ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT          14075
#define ERROR_SXS_PROTECTION_CATALOG_NOT_VALID             14076
#define ERROR_SXS_UNTRANSLATABLE_HRESULT                   14077
#define ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING          14078
#define ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE      14079
#define ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME 14080
#define ERROR_SXS_ASSEMBLY_MISSING                         14081
#define ERROR_SXS_CORRUPT_ACTIVATION_STACK                 14082
#define ERROR_SXS_CORRUPTION                               14083
#define ERROR_SXS_EARLY_DEACTIVATION                       14084
#define ERROR_SXS_INVALID_DEACTIVATION                     14085
#define ERROR_SXS_MULTIPLE_DEACTIVATION                    14086
#define ERROR_SXS_PROCESS_TERMINATION_REQUESTED            14087
#define ERROR_SXS_RELEASE_ACTIVATION_CONTEXT               14088
#define ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY  14089
#define ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE         14090
#define ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME          14091
#define ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE             14092
#define ERROR_SXS_IDENTITY_PARSE_ERROR                     14093
#define ERROR_SXS_IDENTITY_PARSE_ERROR                     14093
#define ERROR_MALFORMED_SUBSTITUTION_STRING                14094
#define ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN               14095
#define ERROR_UNMAPPED_SUBSTITUTION_STRING                 14096
#define ERROR_SXS_ASSEMBLY_NOT_LOCKED                      14097
#define ERROR_SXS_COMPONENT_STORE_CORRUPT                  14098
#define ERROR_ADVANCED_INSTALLER_FAILED                    14099
#define ERROR_XML_ENCODING_MISMATCH                        14100
#define ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT     14101
#define ERROR_SXS_IDENTITIES_DIFFERENT                     14102
#define ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT             14103
#define ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY                14104
#define ERROR_SXS_MANIFEST_TOO_BIG                         14105
#define ERROR_SXS_SETTING_NOT_REGISTERED                   14106
#define ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE           14107
#define ERROR_SMI_PRIMITIVE_INSTALLER_FAILED               14108
#define ERROR_GENERIC_COMMAND_FAILED                       14109
#define ERROR_SXS_FILE_HASH_MISSING                        14110
#define ERROR_MUI_FILE_NOT_FOUND                           15100
#define ERROR_MUI_INVALID_FILE                             15101
#define ERROR_MUI_INVALID_RC_CONFIG                        15102
#define ERROR_MUI_INVALID_LOCALE_NAME                      15103
#define ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME            15104
#define ERROR_MUI_FILE_NOT_LOADED                          15105
#define ERROR_RESOURCE_ENUM_USER_STOP                      15106
#define ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED        15107
#define ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME         15108
#define ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE   15110
#define ERROR_MRM_INVALID_PRICONFIG                        15111
#define ERROR_MRM_INVALID_FILE_TYPE                        15112
#define ERROR_MRM_UNKNOWN_QUALIFIER                        15113
#define ERROR_MRM_INVALID_QUALIFIER_VALUE                  15114
#define ERROR_MRM_NO_CANDIDATE                             15115
#define ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE            15116
#define ERROR_MRM_RESOURCE_TYPE_MISMATCH                   15117
#define ERROR_MRM_DUPLICATE_MAP_NAME                       15118
#define ERROR_MRM_DUPLICATE_ENTRY                          15119
#define ERROR_MRM_INVALID_RESOURCE_IDENTIFIER              15120
#define ERROR_MRM_FILEPATH_TOO_LONG                        15121
#define ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE               15122
#define ERROR_MRM_INVALID_PRI_FILE                         15126
#define ERROR_MRM_NAMED_RESOURCE_NOT_FOUND                 15127
#define ERROR_MRM_MAP_NOT_FOUND                            15135
#define ERROR_MRM_UNSUPPORTED_PROFILE_TYPE                 15136
#define ERROR_MRM_INVALID_QUALIFIER_OPERATOR               15137
#define ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE            15138
#define ERROR_MRM_AUTOMERGE_ENABLED                        15139
#define ERROR_MRM_TOO_MANY_RESOURCES                       15140
#define ERROR_MCA_INVALID_CAPABILITIES_STRING              15200
#define ERROR_MCA_INVALID_VCP_VERSION                      15201
#define ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION      15202
#define ERROR_MCA_MCCS_VERSION_MISMATCH                    15203
#define ERROR_MCA_UNSUPPORTED_MCCS_VERSION                 15204
#define ERROR_MCA_INTERNAL_ERROR                           15205
#define ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED         15206
#define ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE            15207
#define ERROR_AMBIGUOUS_SYSTEM_DEVICE                      15250
#define ERROR_SYSTEM_DEVICE_NOT_FOUND                      15299
#define ERROR_HASH_NOT_SUPPORTED                           15300
#define ERROR_HASH_NOT_PRESENT                             15301
#define ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED         15321
#define ERROR_GPIO_CLIENT_INFORMATION_INVALID              15322
#define ERROR_GPIO_VERSION_NOT_SUPPORTED                   15323
#define ERROR_GPIO_INVALID_REGISTRATION_PACKET             15324
#define ERROR_GPIO_OPERATION_DENIED                        15325
#define ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE               15326
#define ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED              15327
#define ERROR_CANNOT_SWITCH_RUNLEVEL                       15400
#define ERROR_INVALID_RUNLEVEL_SETTING                     15401
#define ERROR_RUNLEVEL_SWITCH_TIMEOUT                      15402
#define ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT                15403
#define ERROR_RUNLEVEL_SWITCH_IN_PROGRESS                  15404
#define ERROR_SERVICES_FAILED_AUTOSTART                    15405
#define ERROR_COM_TASK_STOP_PENDING                        15501
#define ERROR_INSTALL_OPEN_PACKAGE_FAILED                  15600
#define ERROR_INSTALL_PACKAGE_NOT_FOUND                    15601
#define ERROR_INSTALL_INVALID_PACKAGE                      15602
#define ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED            15603
#define ERROR_INSTALL_OUT_OF_DISK_SPACE                    15604
#define ERROR_INSTALL_NETWORK_FAILURE                      15605
#define ERROR_INSTALL_REGISTRATION_FAILURE                 15606
#define ERROR_INSTALL_DEREGISTRATION_FAILURE               15607
#define ERROR_INSTALL_CANCEL                               15608
#define ERROR_INSTALL_FAILED                               15609
#define ERROR_REMOVE_FAILED                                15610
#define ERROR_PACKAGE_ALREADY_EXISTS                       15611
#define ERROR_NEEDS_REMEDIATION                            15612
#define ERROR_INSTALL_PREREQUISITE_FAILED                  15613
#define ERROR_PACKAGE_REPOSITORY_CORRUPTED                 15614
#define ERROR_INSTALL_POLICY_FAILURE                       15615
#define ERROR_PACKAGE_UPDATING                             15616
#define ERROR_DEPLOYMENT_BLOCKED_BY_POLICY                 15617
#define ERROR_PACKAGES_IN_USE                              15618
#define ERROR_RECOVERY_FILE_CORRUPT                        15619
#define ERROR_INVALID_STAGED_SIGNATURE                     15620
#define ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED 15621
#define ERROR_INSTALL_PACKAGE_DOWNGRADE                    15622
#define ERROR_SYSTEM_NEEDS_REMEDIATION                     15623
#define ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN              15624
#define ERROR_RESILIENCY_FILE_CORRUPT                      15625
#define ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING         15626
#define ERROR_PACKAGE_MOVE_FAILED                          15627
#define ERROR_INSTALL_VOLUME_NOT_EMPTY                     15628
#define ERROR_INSTALL_VOLUME_OFFLINE                       15629
#define ERROR_INSTALL_VOLUME_CORRUPT                       15630
#define ERROR_NEEDS_REGISTRATION                           15631
#define ERROR_INSTALL_WRONG_PROCESSOR_ARCHITECTURE         15632
#define ERROR_DEV_SIDELOAD_LIMIT_EXCEEDED                  15633
#define ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE 15634
#define ERROR_PACKAGE_NOT_SUPPORTED_ON_FILESYSTEM          15635
#define ERROR_PACKAGE_MOVE_BLOCKED_BY_STREAMING            15636
#define ERROR_INSTALL_OPTIONAL_PACKAGE_APPLICATIONID_NOT_UNIQUE 15637
#define ERROR_PACKAGE_STAGING_ONHOLD                       15638
#define ERROR_INSTALL_INVALID_RELATED_SET_UPDATE           15639
#define ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY 15640
#define ERROR_DEPLOYMENT_BLOCKED_BY_USER_LOG_OFF           15641
#define ERROR_PROVISION_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_PROVISIONED 15642
#define ERROR_PACKAGES_REPUTATION_CHECK_FAILED             15643
#define ERROR_PACKAGES_REPUTATION_CHECK_TIMEDOUT           15644
#define APPMODEL_ERROR_NO_PACKAGE                          15700
#define APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT             15701
#define APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT            15702
#define APPMODEL_ERROR_NO_APPLICATION                      15703
#define APPMODEL_ERROR_DYNAMIC_PROPERTY_READ_FAILED        15704
#define APPMODEL_ERROR_DYNAMIC_PROPERTY_INVALID            15705
#define APPMODEL_ERROR_PACKAGE_NOT_AVAILABLE               15706

/* HRESULT values for OLE, SHELL and other Interface stuff */
/* the codes 4000-40ff are reserved for OLE */
#undef NOERROR  /* arpa/nameser_compat.h defines this */

#define E_NOT_SUFFICIENT_BUFFER                            HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER)
#define E_NOT_VALID_STATE                                  HRESULT_FROM_WIN32(ERROR_INVALID_STATE)

#ifdef RC_INVOKED
#define _HRESULT_TYPEDEF_(x) (x)
#define _NDIS_ERROR_TYPEDEF_(x) (x)
#else
#define _HRESULT_TYPEDEF_(x) ((HRESULT)x)
#define _NDIS_ERROR_TYPEDEF_(x) ((DWORD)x)
#endif

#define NOERROR                                            _HRESULT_TYPEDEF_(0)
#define S_OK                                               _HRESULT_TYPEDEF_(0)
#define SEC_E_OK                                           _HRESULT_TYPEDEF_(0)
#define S_FALSE                                            _HRESULT_TYPEDEF_(1)

#define E_PENDING                                          _HRESULT_TYPEDEF_(0x8000000A)
#define E_BOUNDS                                           _HRESULT_TYPEDEF_(0x8000000B)
#define E_CHANGED_STATE                                    _HRESULT_TYPEDEF_(0x8000000C)
#define E_ILLEGAL_STATE_CHANGE                             _HRESULT_TYPEDEF_(0x8000000D)
#define E_ILLEGAL_METHOD_CALL                              _HRESULT_TYPEDEF_(0x8000000E)
#define RO_E_METADATA_NAME_NOT_FOUND                       _HRESULT_TYPEDEF_(0x8000000F)
#define RO_E_METADATA_NAME_IS_NAMESPACE                    _HRESULT_TYPEDEF_(0x80000010)
#define RO_E_METADATA_INVALID_TYPE_FORMAT                  _HRESULT_TYPEDEF_(0x80000011)
#define RO_E_INVALID_METADATA_FILE                         _HRESULT_TYPEDEF_(0x80000012)
#define RO_E_CLOSED                                        _HRESULT_TYPEDEF_(0x80000013)
#define RO_E_EXCLUSIVE_WRITE                               _HRESULT_TYPEDEF_(0x80000014)
#define RO_E_CHANGE_NOTIFICATION_IN_PROGRESS               _HRESULT_TYPEDEF_(0x80000015)
#define RO_E_ERROR_STRING_NOT_FOUND                        _HRESULT_TYPEDEF_(0x80000016)
#define E_STRING_NOT_NULL_TERMINATED                       _HRESULT_TYPEDEF_(0x80000017)
#define E_ILLEGAL_DELEGATE_ASSIGNMENT                      _HRESULT_TYPEDEF_(0x80000018)
#define E_ASYNC_OPERATION_NOT_STARTED                      _HRESULT_TYPEDEF_(0x80000019)
#define E_APPLICATION_EXITING                              _HRESULT_TYPEDEF_(0x8000001A)
#define E_APPLICATION_VIEW_EXITING                         _HRESULT_TYPEDEF_(0x8000001B)
#define RO_E_MUST_BE_AGILE                                 _HRESULT_TYPEDEF_(0x8000001C)
#define RO_E_UNSUPPORTED_FROM_MTA                          _HRESULT_TYPEDEF_(0x8000001D)
#define RO_E_COMMITTED                                     _HRESULT_TYPEDEF_(0x8000001E)
#define RO_E_BLOCKED_CROSS_ASTA_CALL                       _HRESULT_TYPEDEF_(0x8000001F)
#define RO_E_CANNOT_ACTIVATE_FULL_TRUST_SERVER             _HRESULT_TYPEDEF_(0x80000020)
#define RO_E_CANNOT_ACTIVATE_UNIVERSAL_APPLICATION_SERVER  _HRESULT_TYPEDEF_(0x80000021)

#define E_NOTIMPL                                          _HRESULT_TYPEDEF_(0x80004001)
#define E_NOINTERFACE                                      _HRESULT_TYPEDEF_(0x80004002)
#define E_POINTER                                          _HRESULT_TYPEDEF_(0x80004003)
#define E_ABORT                                            _HRESULT_TYPEDEF_(0x80004004)
#define E_FAIL                                             _HRESULT_TYPEDEF_(0x80004005)


#define CO_E_INIT_TLS                                      _HRESULT_TYPEDEF_(0x80004006)
#define CO_E_INIT_SHARED_ALLOCATOR                         _HRESULT_TYPEDEF_(0x80004007)
#define CO_E_INIT_MEMORY_ALLOCATOR                         _HRESULT_TYPEDEF_(0x80004008)
#define CO_E_INIT_CLASS_CACHE                              _HRESULT_TYPEDEF_(0x80004009)
#define CO_E_INIT_RPC_CHANNEL                              _HRESULT_TYPEDEF_(0x8000400A)
#define CO_E_INIT_TLS_SET_CHANNEL_CONTROL                  _HRESULT_TYPEDEF_(0x8000400B)
#define CO_E_INIT_TLS_CHANNEL_CONTROL                      _HRESULT_TYPEDEF_(0x8000400C)
#define CO_E_INIT_UNACCEPTED_USER_ALLOCATOR                _HRESULT_TYPEDEF_(0x8000400D)
#define CO_E_INIT_SCM_MUTEX_EXISTS                         _HRESULT_TYPEDEF_(0x8000400E)
#define CO_E_INIT_SCM_FILE_MAPPING_EXISTS                  _HRESULT_TYPEDEF_(0x8000400F)
#define CO_E_INIT_SCM_MAP_VIEW_OF_FILE                     _HRESULT_TYPEDEF_(0x80004010)
#define CO_E_INIT_SCM_EXEC_FAILURE                         _HRESULT_TYPEDEF_(0x80004011)
#define CO_E_INIT_ONLY_SINGLE_THREADED                     _HRESULT_TYPEDEF_(0x80004012)
#define CO_E_CANT_REMOTE                                   _HRESULT_TYPEDEF_(0x80004013)
#define CO_E_BAD_SERVER_NAME                               _HRESULT_TYPEDEF_(0x80004014)
#define CO_E_WRONG_SERVER_IDENTITY                         _HRESULT_TYPEDEF_(0x80004015)
#define CO_E_OLE1DDE_DISABLED                              _HRESULT_TYPEDEF_(0x80004016)
#define CO_E_RUNAS_SYNTAX                                  _HRESULT_TYPEDEF_(0x80004017)
#define CO_E_CREATEPROCESS_FAILURE                         _HRESULT_TYPEDEF_(0x80004018)
#define CO_E_RUNAS_CREATEPROCESS_FAILURE                   _HRESULT_TYPEDEF_(0x80004019)
#define CO_E_RUNAS_LOGON_FAILURE                           _HRESULT_TYPEDEF_(0x8000401A)
#define CO_E_LAUNCH_PERMISSION_DENIED                      _HRESULT_TYPEDEF_(0x8000401B)
#define CO_E_START_SERVICE_FAILURE                         _HRESULT_TYPEDEF_(0x8000401C)
#define CO_E_REMOTE_COMMUNICATION_FAILURE                  _HRESULT_TYPEDEF_(0x8000401D)
#define CO_E_SERVER_START_TIMEOUT                          _HRESULT_TYPEDEF_(0x8000401E)
#define CO_E_CLSREG_INCONSISTENT                           _HRESULT_TYPEDEF_(0x8000401F)
#define CO_E_IIDREG_INCONSISTENT                           _HRESULT_TYPEDEF_(0x80004020)
#define CO_E_NOT_SUPPORTED                                 _HRESULT_TYPEDEF_(0x80004021)
#define CO_E_RELOAD_DLL                                    _HRESULT_TYPEDEF_(0x80004022)
#define CO_E_MSI_ERROR                                     _HRESULT_TYPEDEF_(0x80004023)
#define CO_E_ATTEMPT_TO_CREATE_OUTSIDE_CLIENT_CONTEXT      _HRESULT_TYPEDEF_(0x80004024)
#define CO_E_SERVER_PAUSED                                 _HRESULT_TYPEDEF_(0x80004025)
#define CO_E_SERVER_NOT_PAUSED                             _HRESULT_TYPEDEF_(0x80004026)
#define CO_E_CLASS_DISABLED                                _HRESULT_TYPEDEF_(0x80004027)
#define CO_E_CLRNOTAVAILABLE                               _HRESULT_TYPEDEF_(0x80004028)
#define CO_E_ASYNC_WORK_REJECTED                           _HRESULT_TYPEDEF_(0x80004029)
#define CO_E_SERVER_INIT_TIMEOUT                           _HRESULT_TYPEDEF_(0x8000402A)
#define CO_E_NO_SECCTX_IN_ACTIVATE                         _HRESULT_TYPEDEF_(0x8000402B)
#define CO_E_TRACKER_CONFIG                                _HRESULT_TYPEDEF_(0x80004030)
#define CO_E_THREADPOOL_CONFIG                             _HRESULT_TYPEDEF_(0x80004031)
#define CO_E_SXS_CONFIG                                    _HRESULT_TYPEDEF_(0x80004032)
#define CO_E_MALFORMED_SPN                                 _HRESULT_TYPEDEF_(0x80004033)

#define E_UNEXPECTED                                       _HRESULT_TYPEDEF_(0x8000FFFF)

#define RPC_E_CALL_REJECTED                                _HRESULT_TYPEDEF_(0x80010001)
#define RPC_E_CALL_CANCELED                                _HRESULT_TYPEDEF_(0x80010002)
#define RPC_E_CANTPOST_INSENDCALL                          _HRESULT_TYPEDEF_(0x80010003)
#define RPC_E_CANTCALLOUT_INASYNCCALL                      _HRESULT_TYPEDEF_(0x80010004)
#define RPC_E_CANTCALLOUT_INEXTERNALCALL                   _HRESULT_TYPEDEF_(0x80010005)
#define RPC_E_CONNECTION_TERMINATED                        _HRESULT_TYPEDEF_(0x80010006)
#define RPC_E_SERVER_DIED                                  _HRESULT_TYPEDEF_(0x80010007)
#define RPC_E_CLIENT_DIED                                  _HRESULT_TYPEDEF_(0x80010008)
#define RPC_E_INVALID_DATAPACKET                           _HRESULT_TYPEDEF_(0x80010009)
#define RPC_E_CANTTRANSMIT_CALL                            _HRESULT_TYPEDEF_(0x8001000A)
#define RPC_E_CLIENT_CANTMARSHAL_DATA                      _HRESULT_TYPEDEF_(0x8001000B)
#define RPC_E_CLIENT_CANTUNMARSHAL_DATA                    _HRESULT_TYPEDEF_(0x8001000C)
#define RPC_E_SERVER_CANTMARSHAL_DATA                      _HRESULT_TYPEDEF_(0x8001000D)
#define RPC_E_SERVER_CANTUNMARSHAL_DATA                    _HRESULT_TYPEDEF_(0x8001000E)
#define RPC_E_INVALID_DATA                                 _HRESULT_TYPEDEF_(0x8001000F)
#define RPC_E_INVALID_PARAMETER                            _HRESULT_TYPEDEF_(0x80010010)
#define RPC_E_CANTCALLOUT_AGAIN                            _HRESULT_TYPEDEF_(0x80010011)
#define RPC_E_SERVER_DIED_DNE                              _HRESULT_TYPEDEF_(0x80010012)
#define RPC_E_SYS_CALL_FAILED                              _HRESULT_TYPEDEF_(0x80010100)
#define RPC_E_OUT_OF_RESOURCES                             _HRESULT_TYPEDEF_(0x80010101)
#define RPC_E_ATTEMPTED_MULTITHREAD                        _HRESULT_TYPEDEF_(0x80010102)
#define RPC_E_NOT_REGISTERED                               _HRESULT_TYPEDEF_(0x80010103)
#define RPC_E_FAULT                                        _HRESULT_TYPEDEF_(0x80010104)
#define RPC_E_SERVERFAULT                                  _HRESULT_TYPEDEF_(0x80010105)
#define RPC_E_CHANGED_MODE                                 _HRESULT_TYPEDEF_(0x80010106)
#define RPC_E_INVALIDMETHOD                                _HRESULT_TYPEDEF_(0x80010107)
#define RPC_E_DISCONNECTED                                 _HRESULT_TYPEDEF_(0x80010108)
#define RPC_E_RETRY                                        _HRESULT_TYPEDEF_(0x80010109)
#define RPC_E_SERVERCALL_RETRYLATER                        _HRESULT_TYPEDEF_(0x8001010A)
#define RPC_E_SERVERCALL_REJECTED                          _HRESULT_TYPEDEF_(0x8001010B)
#define RPC_E_INVALID_CALLDATA                             _HRESULT_TYPEDEF_(0x8001010C)
#define RPC_E_CANTCALLOUT_ININPUTSYNCCALL                  _HRESULT_TYPEDEF_(0x8001010D)
#define RPC_E_WRONG_THREAD                                 _HRESULT_TYPEDEF_(0x8001010E)
#define RPC_E_THREAD_NOT_INIT                              _HRESULT_TYPEDEF_(0x8001010F)
#define RPC_E_VERSION_MISMATCH                             _HRESULT_TYPEDEF_(0x80010110)
#define RPC_E_INVALID_HEADER                               _HRESULT_TYPEDEF_(0x80010111)
#define RPC_E_INVALID_EXTENSION                            _HRESULT_TYPEDEF_(0x80010112)
#define RPC_E_INVALID_IPID                                 _HRESULT_TYPEDEF_(0x80010113)
#define RPC_E_INVALID_OBJECT                               _HRESULT_TYPEDEF_(0x80010114)
#define RPC_S_CALLPENDING                                  _HRESULT_TYPEDEF_(0x80010115)
#define RPC_S_WAITONTIMER                                  _HRESULT_TYPEDEF_(0x80010116)
#define RPC_E_CALL_COMPLETE                                _HRESULT_TYPEDEF_(0x80010117)
#define RPC_E_UNSECURE_CALL                                _HRESULT_TYPEDEF_(0x80010118)
#define RPC_E_TOO_LATE                                     _HRESULT_TYPEDEF_(0x80010119)
#define RPC_E_NO_GOOD_SECURITY_PACKAGES                    _HRESULT_TYPEDEF_(0x8001011A)
#define RPC_E_ACCESS_DENIED                                _HRESULT_TYPEDEF_(0x8001011B)
#define RPC_E_REMOTE_DISABLED                              _HRESULT_TYPEDEF_(0x8001011C)
#define RPC_E_INVALID_OBJREF                               _HRESULT_TYPEDEF_(0x8001011D)
#define RPC_E_NO_CONTEXT                                   _HRESULT_TYPEDEF_(0x8001011E)
#define RPC_E_TIMEOUT                                      _HRESULT_TYPEDEF_(0x8001011F)
#define RPC_E_NO_SYNC                                      _HRESULT_TYPEDEF_(0x80010120)
#define CO_E_CANCEL_DISABLED                               _HRESULT_TYPEDEF_(0x80010140)
#define RPC_E_UNEXPECTED                                   _HRESULT_TYPEDEF_(0x8001FFFF)

#define DISP_E_UNKNOWNINTERFACE                            _HRESULT_TYPEDEF_(0x80020001)
#define DISP_E_MEMBERNOTFOUND                              _HRESULT_TYPEDEF_(0x80020003)
#define DISP_E_PARAMNOTFOUND                               _HRESULT_TYPEDEF_(0x80020004)
#define DISP_E_TYPEMISMATCH                                _HRESULT_TYPEDEF_(0x80020005)
#define DISP_E_UNKNOWNNAME                                 _HRESULT_TYPEDEF_(0x80020006)
#define DISP_E_NONAMEDARGS                                 _HRESULT_TYPEDEF_(0x80020007)
#define DISP_E_BADVARTYPE                                  _HRESULT_TYPEDEF_(0x80020008)
#define DISP_E_EXCEPTION                                   _HRESULT_TYPEDEF_(0x80020009)
#define DISP_E_OVERFLOW                                    _HRESULT_TYPEDEF_(0x8002000A)
#define DISP_E_BADINDEX                                    _HRESULT_TYPEDEF_(0x8002000B)
#define DISP_E_UNKNOWNLCID                                 _HRESULT_TYPEDEF_(0x8002000C)
#define DISP_E_ARRAYISLOCKED                               _HRESULT_TYPEDEF_(0x8002000D)
#define DISP_E_BADPARAMCOUNT                               _HRESULT_TYPEDEF_(0x8002000E)
#define DISP_E_PARAMNOTOPTIONAL                            _HRESULT_TYPEDEF_(0x8002000F)
#define DISP_E_BADCALLEE                                   _HRESULT_TYPEDEF_(0x80020010)
#define DISP_E_NOTACOLLECTION                              _HRESULT_TYPEDEF_(0x80020011)
#define DISP_E_DIVBYZERO                                   _HRESULT_TYPEDEF_(0x80020012)

#define TYPE_E_BUFFERTOOSMALL                              _HRESULT_TYPEDEF_(0x80028016)
#define TYPE_E_FIELDNOTFOUND                               _HRESULT_TYPEDEF_(0x80028017)
#define TYPE_E_INVDATAREAD                                 _HRESULT_TYPEDEF_(0x80028018)
#define TYPE_E_UNSUPFORMAT                                 _HRESULT_TYPEDEF_(0x80028019)
#define TYPE_E_REGISTRYACCESS                              _HRESULT_TYPEDEF_(0x8002801C)
#define TYPE_E_LIBNOTREGISTERED                            _HRESULT_TYPEDEF_(0x8002801D)
#define TYPE_E_UNDEFINEDTYPE                               _HRESULT_TYPEDEF_(0x80028027)
#define TYPE_E_QUALIFIEDNAMEDISALLOWED                     _HRESULT_TYPEDEF_(0x80028028)
#define TYPE_E_INVALIDSTATE                                _HRESULT_TYPEDEF_(0x80028029)
#define TYPE_E_WRONGTYPEKIND                               _HRESULT_TYPEDEF_(0x8002802A)
#define TYPE_E_ELEMENTNOTFOUND                             _HRESULT_TYPEDEF_(0x8002802B)
#define TYPE_E_AMBIGUOUSNAME                               _HRESULT_TYPEDEF_(0x8002802C)
#define TYPE_E_NAMECONFLICT                                _HRESULT_TYPEDEF_(0x8002802D)
#define TYPE_E_UNKNOWNLCID                                 _HRESULT_TYPEDEF_(0x8002802E)
#define TYPE_E_DLLFUNCTIONNOTFOUND                         _HRESULT_TYPEDEF_(0x8002802F)
#define TYPE_E_BADMODULEKIND                               _HRESULT_TYPEDEF_(0x800288BD)
#define TYPE_E_SIZETOOBIG                                  _HRESULT_TYPEDEF_(0x800288C5)
#define TYPE_E_DUPLICATEID                                 _HRESULT_TYPEDEF_(0x800288C6)
#define TYPE_E_INVALIDID                                   _HRESULT_TYPEDEF_(0x800288CF)
#define TYPE_E_TYPEMISMATCH                                _HRESULT_TYPEDEF_(0x80028CA0)
#define TYPE_E_OUTOFBOUNDS                                 _HRESULT_TYPEDEF_(0x80028CA1)
#define TYPE_E_IOERROR                                     _HRESULT_TYPEDEF_(0x80028CA2)
#define TYPE_E_CANTCREATETMPFILE                           _HRESULT_TYPEDEF_(0x80028CA3)
#define TYPE_E_CANTLOADLIBRARY                             _HRESULT_TYPEDEF_(0x80029C4A)
#define TYPE_E_INCONSISTENTPROPFUNCS                       _HRESULT_TYPEDEF_(0x80029C83)
#define TYPE_E_CIRCULARTYPE                                _HRESULT_TYPEDEF_(0x80029C84)

#define STG_S_CONVERTED                                    _HRESULT_TYPEDEF_(0x00030200)
#define STG_S_BLOCK                                        _HRESULT_TYPEDEF_(0x00030201)
#define STG_S_RETRYNOW                                     _HRESULT_TYPEDEF_(0x00030202)
#define STG_S_MONITORING                                   _HRESULT_TYPEDEF_(0x00030203)
#define STG_S_MULTIPLEOPENS                                _HRESULT_TYPEDEF_(0x00030204)
#define STG_S_CONSOLIDATIONFAILED                          _HRESULT_TYPEDEF_(0x00030205)
#define STG_S_CANNOTCONSOLIDATE                            _HRESULT_TYPEDEF_(0x00030206)

#define STG_E_INVALIDFUNCTION                              _HRESULT_TYPEDEF_(0x80030001)
#define STG_E_FILENOTFOUND                                 _HRESULT_TYPEDEF_(0x80030002)
#define STG_E_PATHNOTFOUND                                 _HRESULT_TYPEDEF_(0x80030003)
#define STG_E_TOOMANYOPENFILES                             _HRESULT_TYPEDEF_(0x80030004)
#define STG_E_ACCESSDENIED                                 _HRESULT_TYPEDEF_(0x80030005)
#define STG_E_INVALIDHANDLE                                _HRESULT_TYPEDEF_(0x80030006)
#define STG_E_INSUFFICIENTMEMORY                           _HRESULT_TYPEDEF_(0x80030008)
#define STG_E_INVALIDPOINTER                               _HRESULT_TYPEDEF_(0x80030009)
#define STG_E_NOMOREFILES                                  _HRESULT_TYPEDEF_(0x80030012)
#define STG_E_DISKISWRITEPROTECTED                         _HRESULT_TYPEDEF_(0x80030013)
#define STG_E_SEEKERROR                                    _HRESULT_TYPEDEF_(0x80030019)
#define STG_E_WRITEFAULT                                   _HRESULT_TYPEDEF_(0x8003001D)
#define STG_E_READFAULT                                    _HRESULT_TYPEDEF_(0x8003001E)
#define STG_E_SHAREVIOLATION                               _HRESULT_TYPEDEF_(0x80030020)
#define STG_E_LOCKVIOLATION                                _HRESULT_TYPEDEF_(0x80030021)
#define STG_E_FILEALREADYEXISTS                            _HRESULT_TYPEDEF_(0x80030050)
#define STG_E_INVALIDPARAMETER                             _HRESULT_TYPEDEF_(0x80030057)
#define STG_E_MEDIUMFULL                                   _HRESULT_TYPEDEF_(0x80030070)
#define STG_E_ABNORMALAPIEXIT                              _HRESULT_TYPEDEF_(0x800300FA)
#define STG_E_INVALIDHEADER                                _HRESULT_TYPEDEF_(0x800300FB)
#define STG_E_INVALIDNAME                                  _HRESULT_TYPEDEF_(0x800300FC)
#define STG_E_UNKNOWN                                      _HRESULT_TYPEDEF_(0x800300FD)
#define STG_E_UNIMPLEMENTEDFUNCTION                        _HRESULT_TYPEDEF_(0x800300FE)
#define STG_E_INVALIDFLAG                                  _HRESULT_TYPEDEF_(0x800300FF)
#define STG_E_INUSE                                        _HRESULT_TYPEDEF_(0x80030100)
#define STG_E_NOTCURRENT                                   _HRESULT_TYPEDEF_(0x80030101)
#define STG_E_REVERTED                                     _HRESULT_TYPEDEF_(0x80030102)
#define STG_E_CANTSAVE                                     _HRESULT_TYPEDEF_(0x80030103)
#define STG_E_OLDFORMAT                                    _HRESULT_TYPEDEF_(0x80030104)
#define STG_E_OLDDLL                                       _HRESULT_TYPEDEF_(0x80030105)
#define STG_E_SHAREREQUIRED                                _HRESULT_TYPEDEF_(0x80030106)
#define STG_E_NOTFILEBASEDSTORAGE                          _HRESULT_TYPEDEF_(0x80030107)
#define STG_E_EXTANTMARSHALLINGS                           _HRESULT_TYPEDEF_(0x80030108)
#define STG_E_DOCFILECORRUPT                               _HRESULT_TYPEDEF_(0x80030109)
#define STG_E_BADBASEADDRESS                               _HRESULT_TYPEDEF_(0x80030110)
#define STG_E_DOCFILETOOLARGE                              _HRESULT_TYPEDEF_(0x80030111)
#define STG_E_NOTSIMPLEFORMAT                              _HRESULT_TYPEDEF_(0x80030112)

#define STG_E_INCOMPLETE                                   _HRESULT_TYPEDEF_(0x80030201)
#define STG_E_TERMINATED                                   _HRESULT_TYPEDEF_(0x80030202)
#define STG_S_CONVERTED                                    _HRESULT_TYPEDEF_(0x00030200)
#define STG_S_BLOCK                                        _HRESULT_TYPEDEF_(0x00030201)
#define STG_S_RETRYNOW                                     _HRESULT_TYPEDEF_(0x00030202)
#define STG_S_MONITORING                                   _HRESULT_TYPEDEF_(0x00030203)
#define STG_S_MULTIPLEOPENS                                _HRESULT_TYPEDEF_(0x00030204)
#define STG_S_CONSOLIDATIONFAILED                          _HRESULT_TYPEDEF_(0x00030205)
#define STG_S_CANNOTCONSOLIDATE                            _HRESULT_TYPEDEF_(0x00030206)
#define STG_S_POWER_CYCLE_REQUIRED                         _HRESULT_TYPEDEF_(0x00030207)
#define STG_E_FIRMWARE_SLOT_INVALID                        _HRESULT_TYPEDEF_(0x80030208)
#define STG_E_FIRMWARE_IMAGE_INVALID                       _HRESULT_TYPEDEF_(0x80030209)
#define STG_E_DEVICE_UNRESPONSIVE                          _HRESULT_TYPEDEF_(0x8003020A)

#define STG_E_STATUS_COPY_PROTECTION_FAILURE               _HRESULT_TYPEDEF_(0x80030305)
#define STG_E_CSS_AUTHENTICATION_FAILURE                   _HRESULT_TYPEDEF_(0x80030306)
#define STG_E_CSS_KEY_NOT_PRESENT                          _HRESULT_TYPEDEF_(0x80030307)
#define STG_E_CSS_KEY_NOT_ESTABLISHED                      _HRESULT_TYPEDEF_(0x80030308)
#define STG_E_CSS_SCRAMBLED_SECTOR                         _HRESULT_TYPEDEF_(0x80030309)
#define STG_E_CSS_REGION_MISMATCH                          _HRESULT_TYPEDEF_(0x8003030A)
#define STG_E_RESETS_EXHAUSTED                             _HRESULT_TYPEDEF_(0x8003030B)

#define OLE_S_FIRST                                        _HRESULT_TYPEDEF_(0x00040000)
#define OLE_S_USEREG                                       _HRESULT_TYPEDEF_(0x00040000)
#define OLE_S_STATIC                                       _HRESULT_TYPEDEF_(0x00040001)
#define OLE_S_MAC_CLIPFORMAT                               _HRESULT_TYPEDEF_(0x00040002)
#define OLE_S_LAST                                         _HRESULT_TYPEDEF_(0x000400FF)

#define OLE_E_FIRST                                        _HRESULT_TYPEDEF_(0x80040000)
#define OLE_E_OLEVERB                                      _HRESULT_TYPEDEF_(0x80040000)
#define OLE_E_ADVF                                         _HRESULT_TYPEDEF_(0x80040001)
#define OLE_E_ENUM_NOMORE                                  _HRESULT_TYPEDEF_(0x80040002)
#define OLE_E_ADVISENOTSUPPORTED                           _HRESULT_TYPEDEF_(0x80040003)
#define OLE_E_NOCONNECTION                                 _HRESULT_TYPEDEF_(0x80040004)
#define OLE_E_NOTRUNNING                                   _HRESULT_TYPEDEF_(0x80040005)
#define OLE_E_NOCACHE                                      _HRESULT_TYPEDEF_(0x80040006)
#define OLE_E_BLANK                                        _HRESULT_TYPEDEF_(0x80040007)
#define OLE_E_CLASSDIFF                                    _HRESULT_TYPEDEF_(0x80040008)
#define OLE_E_CANT_GETMONIKER                              _HRESULT_TYPEDEF_(0x80040009)
#define OLE_E_CANT_BINDTOSOURCE                            _HRESULT_TYPEDEF_(0x8004000A)
#define OLE_E_STATIC                                       _HRESULT_TYPEDEF_(0x8004000B)
#define OLE_E_PROMPTSAVECANCELLED                          _HRESULT_TYPEDEF_(0x8004000C)
#define OLE_E_INVALIDRECT                                  _HRESULT_TYPEDEF_(0x8004000D)
#define OLE_E_WRONGCOMPOBJ                                 _HRESULT_TYPEDEF_(0x8004000E)
#define OLE_E_INVALIDHWND                                  _HRESULT_TYPEDEF_(0x8004000F)
#define OLE_E_NOT_INPLACEACTIVE                            _HRESULT_TYPEDEF_(0x80040010)
#define OLE_E_CANTCONVERT                                  _HRESULT_TYPEDEF_(0x80040011)
#define OLE_E_NOSTORAGE                                    _HRESULT_TYPEDEF_(0x80040012)
#define DV_E_FORMATETC                                     _HRESULT_TYPEDEF_(0x80040064)
#define DV_E_DVTARGETDEVICE                                _HRESULT_TYPEDEF_(0x80040065)
#define DV_E_STGMEDIUM                                     _HRESULT_TYPEDEF_(0x80040066)
#define DV_E_STATDATA                                      _HRESULT_TYPEDEF_(0x80040067)
#define DV_E_LINDEX                                        _HRESULT_TYPEDEF_(0x80040068)
#define DV_E_TYMED                                         _HRESULT_TYPEDEF_(0x80040069)
#define DV_E_CLIPFORMAT                                    _HRESULT_TYPEDEF_(0x8004006A)
#define DV_E_DVASPECT                                      _HRESULT_TYPEDEF_(0x8004006B)
#define DV_E_DVTARGETDEVICE_SIZE                           _HRESULT_TYPEDEF_(0x8004006C)
#define DV_E_NOIVIEWOBJECT                                 _HRESULT_TYPEDEF_(0x8004006D)
#define OLE_E_LAST                                         _HRESULT_TYPEDEF_(0x800400FF)

#define DRAGDROP_S_FIRST                                   _HRESULT_TYPEDEF_(0x00040100)
#define DRAGDROP_S_DROP                                    _HRESULT_TYPEDEF_(0x00040100)
#define DRAGDROP_S_CANCEL                                  _HRESULT_TYPEDEF_(0x00040101)
#define DRAGDROP_S_USEDEFAULTCURSORS                       _HRESULT_TYPEDEF_(0x00040102)
#define DRAGDROP_S_LAST                                    _HRESULT_TYPEDEF_(0x0004010F)

#define DRAGDROP_E_FIRST                                   _HRESULT_TYPEDEF_(0x80040100)
#define DRAGDROP_E_NOTREGISTERED                           _HRESULT_TYPEDEF_(0x80040100)
#define DRAGDROP_E_ALREADYREGISTERED                       _HRESULT_TYPEDEF_(0x80040101)
#define DRAGDROP_E_INVALIDHWND                             _HRESULT_TYPEDEF_(0x80040102)
#define DRAGDROP_E_LAST                                    _HRESULT_TYPEDEF_(0x8004010F)


#define CLASSFACTORY_S_FIRST                               _HRESULT_TYPEDEF_(0x00040110)
#define CLASSFACTORY_S_LAST                                _HRESULT_TYPEDEF_(0x0004011F)

#define CLASSFACTORY_E_FIRST                               _HRESULT_TYPEDEF_(0x80040110)
#define CLASS_E_NOAGGREGATION                              _HRESULT_TYPEDEF_(0x80040110)
#define CLASS_E_CLASSNOTAVAILABLE                          _HRESULT_TYPEDEF_(0x80040111)
#define CLASS_E_NOTLICENSED                                _HRESULT_TYPEDEF_(0x80040112)
#define CLASSFACTORY_E_LAST                                _HRESULT_TYPEDEF_(0x8004011F)

#define MARSHAL_S_FIRST                                    _HRESULT_TYPEDEF_(0x00040120)
#define MARSHAL_S_LAST                                     _HRESULT_TYPEDEF_(0x0004012F)

#define MARSHAL_E_FIRST                                    _HRESULT_TYPEDEF_(0x80040120)
#define MARSHAL_E_LAST                                     _HRESULT_TYPEDEF_(0x8004012F)

#define DATA_S_FIRST                                       _HRESULT_TYPEDEF_(0x00040130)
#define DATA_S_SAMEFORMATETC                               _HRESULT_TYPEDEF_(0x00040130)
#define DATA_S_LAST                                        _HRESULT_TYPEDEF_(0x0004013F)

#define DATA_E_FIRST                                       _HRESULT_TYPEDEF_(0x80040130)
#define DATA_E_LAST                                        _HRESULT_TYPEDEF_(0x8004013F)

#define VIEW_S_FIRST                                       _HRESULT_TYPEDEF_(0x00040140)
#define VIEW_S_ALREADY_FROZEN                              _HRESULT_TYPEDEF_(0x00040140)
#define VIEW_S_LAST                                        _HRESULT_TYPEDEF_(0x0004014F)

#define VIEW_E_FIRST                                       _HRESULT_TYPEDEF_(0x80040140)
#define VIEW_E_DRAW                                        _HRESULT_TYPEDEF_(0x80040140)
#define VIEW_E_LAST                                        _HRESULT_TYPEDEF_(0x8004014F)

#define REGDB_S_FIRST                                      _HRESULT_TYPEDEF_(0x00040150)
#define REGDB_S_LAST                                       _HRESULT_TYPEDEF_(0x0004015F)

#define REGDB_E_FIRST                                      _HRESULT_TYPEDEF_(0x80040150)
#define REGDB_E_READREGDB                                  _HRESULT_TYPEDEF_(0x80040150)
#define REGDB_E_WRITEREGDB                                 _HRESULT_TYPEDEF_(0x80040151)
#define REGDB_E_KEYMISSING                                 _HRESULT_TYPEDEF_(0x80040152)
#define REGDB_E_INVALIDVALUE                               _HRESULT_TYPEDEF_(0x80040153)
#define REGDB_E_CLASSNOTREG                                _HRESULT_TYPEDEF_(0x80040154)
#define REGDB_E_IIDNOTREG                                  _HRESULT_TYPEDEF_(0x80040155)
#define REGDB_E_LAST                                       _HRESULT_TYPEDEF_(0x8004015F)

#define CAT_E_FIRST                                        _HRESULT_TYPEDEF_(0x80040160)
#define CAT_E_CATIDNOEXIST                                 _HRESULT_TYPEDEF_(0x80040160)
#define CAT_E_NODESCRIPTION                                _HRESULT_TYPEDEF_(0x80040161)
#define CAT_E_LAST                                         _HRESULT_TYPEDEF_(0x80040161)

#define CACHE_S_FIRST                                      _HRESULT_TYPEDEF_(0x00040170)
#define CACHE_S_FORMATETC_NOTSUPPORTED                     _HRESULT_TYPEDEF_(0x00040170)
#define CACHE_S_SAMECACHE                                  _HRESULT_TYPEDEF_(0x00040171)
#define CACHE_S_SOMECACHES_NOTUPDATED                      _HRESULT_TYPEDEF_(0x00040172)
#define CACHE_S_LAST                                       _HRESULT_TYPEDEF_(0x0004017F)

#define CACHE_E_FIRST                                      _HRESULT_TYPEDEF_(0x80040170)
#define CACHE_E_NOCACHE_UPDATED                            _HRESULT_TYPEDEF_(0x80040170)
#define CACHE_E_LAST                                       _HRESULT_TYPEDEF_(0x8004017F)

#define OLEOBJ_S_FIRST                                     _HRESULT_TYPEDEF_(0x00040180)
#define OLEOBJ_S_INVALIDVERB                               _HRESULT_TYPEDEF_(0x00040180)
#define OLEOBJ_S_CANNOT_DOVERB_NOW                         _HRESULT_TYPEDEF_(0x00040181)
#define OLEOBJ_S_INVALIDHWND                               _HRESULT_TYPEDEF_(0x00040182)
#define OLEOBJ_S_LAST                                      _HRESULT_TYPEDEF_(0x0004018F)

#define OLEOBJ_E_FIRST                                     _HRESULT_TYPEDEF_(0x80040180)
#define OLEOBJ_E_NOVERBS                                   _HRESULT_TYPEDEF_(0x80040180)
#define OLEOBJ_E_INVALIDVERB                               _HRESULT_TYPEDEF_(0x80040181)
#define OLEOBJ_E_LAST                                      _HRESULT_TYPEDEF_(0x8004018F)

#define CLIENTSITE_S_FIRST                                 _HRESULT_TYPEDEF_(0x00040190)
#define CLIENTSITE_S_LAST                                  _HRESULT_TYPEDEF_(0x0004019F)

#define CLIENTSITE_E_FIRST                                 _HRESULT_TYPEDEF_(0x80040190)
#define CLIENTSITE_E_LAST                                  _HRESULT_TYPEDEF_(0x8004019F)

#define INPLACE_S_FIRST                                    _HRESULT_TYPEDEF_(0x000401A0)
#define INPLACE_S_TRUNCATED                                _HRESULT_TYPEDEF_(0x000401A0)
#define INPLACE_S_LAST                                     _HRESULT_TYPEDEF_(0x000401AF)

#define INPLACE_E_FIRST                                    _HRESULT_TYPEDEF_(0x800401A0)
#define INPLACE_E_NOTUNDOABLE                              _HRESULT_TYPEDEF_(0x800401A0)
#define INPLACE_E_NOTOOLSPACE                              _HRESULT_TYPEDEF_(0x800401A1)
#define INPLACE_E_LAST                                     _HRESULT_TYPEDEF_(0x800401AF)

#define ENUM_S_FIRST                                       _HRESULT_TYPEDEF_(0x000401B0)
#define ENUM_S_LAST                                        _HRESULT_TYPEDEF_(0x000401BF)

#define ENUM_E_FIRST                                       _HRESULT_TYPEDEF_(0x800401B0)
#define ENUM_E_LAST                                        _HRESULT_TYPEDEF_(0x800401BF)

#define CONVERT10_S_FIRST                                  _HRESULT_TYPEDEF_(0x000401C0)
#define CONVERT10_S_NO_PRESENTATION                        _HRESULT_TYPEDEF_(0x000401C0)
#define CONVERT10_S_LAST                                   _HRESULT_TYPEDEF_(0x000401CF)

#define CONVERT10_E_FIRST                                  _HRESULT_TYPEDEF_(0x800401C0)
#define CONVERT10_E_OLESTREAM_GET                          _HRESULT_TYPEDEF_(0x800401C0)
#define CONVERT10_E_OLESTREAM_PUT                          _HRESULT_TYPEDEF_(0x800401C1)
#define CONVERT10_E_OLESTREAM_FMT                          _HRESULT_TYPEDEF_(0x800401C2)
#define CONVERT10_E_OLESTREAM_BITMAP_TO_DIB                _HRESULT_TYPEDEF_(0x800401C3)
#define CONVERT10_E_STG_FMT                                _HRESULT_TYPEDEF_(0x800401C4)
#define CONVERT10_E_STG_NO_STD_STREAM                      _HRESULT_TYPEDEF_(0x800401C5)
#define CONVERT10_E_STG_DIB_TO_BITMAP                      _HRESULT_TYPEDEF_(0x800401C6)
#define CONVERT10_E_LAST                                   _HRESULT_TYPEDEF_(0x800401CF)

#define CLIPBRD_S_FIRST                                    _HRESULT_TYPEDEF_(0x000401D0)
#define CLIPBRD_S_LAST                                     _HRESULT_TYPEDEF_(0x000401DF)

#define CLIPBRD_E_FIRST                                    _HRESULT_TYPEDEF_(0x800401D0)
#define CLIPBRD_E_LAST                                     _HRESULT_TYPEDEF_(0x800401DF)
#define CLIPBRD_E_CANT_OPEN                                _HRESULT_TYPEDEF_(0x800401D0)
#define CLIPBRD_E_CANT_EMPTY                               _HRESULT_TYPEDEF_(0x800401D1)
#define CLIPBRD_E_CANT_SET                                 _HRESULT_TYPEDEF_(0x800401D2)
#define CLIPBRD_E_BAD_DATA                                 _HRESULT_TYPEDEF_(0x800401D3)
#define CLIPBRD_E_CANT_CLOSE                               _HRESULT_TYPEDEF_(0x800401D4)

#define MK_S_FIRST                                         _HRESULT_TYPEDEF_(0x000401E0)
#define MK_S_REDUCED_TO_SELF                               _HRESULT_TYPEDEF_(0x000401E2)
#define MK_S_ME                                            _HRESULT_TYPEDEF_(0x000401E4)
#define MK_S_HIM                                           _HRESULT_TYPEDEF_(0x000401E5)
#define MK_S_US                                            _HRESULT_TYPEDEF_(0x000401E6)
#define MK_S_MONIKERALREADYREGISTERED                      _HRESULT_TYPEDEF_(0x000401E7)
#define MK_S_LAST                                          _HRESULT_TYPEDEF_(0x000401EF)

#define MK_E_FIRST                                         _HRESULT_TYPEDEF_(0x800401E0)
#define MK_E_CONNECTMANUALLY                               _HRESULT_TYPEDEF_(0x800401E0)
#define MK_E_EXCEEDEDDEADLINE                              _HRESULT_TYPEDEF_(0x800401E1)
#define MK_E_NEEDGENERIC                                   _HRESULT_TYPEDEF_(0x800401E2)
#define MK_E_UNAVAILABLE                                   _HRESULT_TYPEDEF_(0x800401E3)
#define MK_E_SYNTAX                                        _HRESULT_TYPEDEF_(0x800401E4)
#define MK_E_NOOBJECT                                      _HRESULT_TYPEDEF_(0x800401E5)
#define MK_E_INVALIDEXTENSION                              _HRESULT_TYPEDEF_(0x800401E6)
#define MK_E_INTERMEDIATEINTERFACENOTSUPPORTED             _HRESULT_TYPEDEF_(0x800401E7)
#define MK_E_NOTBINDABLE                                   _HRESULT_TYPEDEF_(0x800401E8)
#define MK_E_NOTBOUND                                      _HRESULT_TYPEDEF_(0x800401E9)
#define MK_E_CANTOPENFILE                                  _HRESULT_TYPEDEF_(0x800401EA)
#define MK_E_MUSTBOTHERUSER                                _HRESULT_TYPEDEF_(0x800401EB)
#define MK_E_NOINVERSE                                     _HRESULT_TYPEDEF_(0x800401EC)
#define MK_E_NOSTORAGE                                     _HRESULT_TYPEDEF_(0x800401ED)
#define MK_E_NOPREFIX                                      _HRESULT_TYPEDEF_(0x800401EE)
#define MK_E_ENUMERATION_FAILED                            _HRESULT_TYPEDEF_(0x800401EF)
#define MK_E_LAST                                          _HRESULT_TYPEDEF_(0x800401EF)

#define CO_S_FIRST                                         _HRESULT_TYPEDEF_(0x000401F0)
#define CO_S_LAST                                          _HRESULT_TYPEDEF_(0x000401FF)

#define CO_E_FIRST                                         _HRESULT_TYPEDEF_(0x800401F0)
#define CO_E_NOTINITIALIZED                                _HRESULT_TYPEDEF_(0x800401F0)
#define CO_E_ALREADYINITIALIZED                            _HRESULT_TYPEDEF_(0x800401F1)
#define CO_E_CANTDETERMINECLASS                            _HRESULT_TYPEDEF_(0x800401F2)
#define CO_E_CLASSSTRING                                   _HRESULT_TYPEDEF_(0x800401F3)
#define CO_E_IIDSTRING                                     _HRESULT_TYPEDEF_(0x800401F4)
#define CO_E_APPNOTFOUND                                   _HRESULT_TYPEDEF_(0x800401F5)
#define CO_E_APPSINGLEUSE                                  _HRESULT_TYPEDEF_(0x800401F6)
#define CO_E_ERRORINAPP                                    _HRESULT_TYPEDEF_(0x800401F7)
#define CO_E_DLLNOTFOUND                                   _HRESULT_TYPEDEF_(0x800401F8)
#define CO_E_ERRORINDLL                                    _HRESULT_TYPEDEF_(0x800401F9)
#define CO_E_WRONGOSFORAPP                                 _HRESULT_TYPEDEF_(0x800401FA)
#define CO_E_OBJNOTREG                                     _HRESULT_TYPEDEF_(0x800401FB)
#define CO_E_OBJISREG                                      _HRESULT_TYPEDEF_(0x800401FC)
#define CO_E_OBJNOTCONNECTED                               _HRESULT_TYPEDEF_(0x800401FD)
#define CO_E_APPDIDNTREG                                   _HRESULT_TYPEDEF_(0x800401FE)
#define CO_E_RELEASED                                      _HRESULT_TYPEDEF_(0x800401FF)
#define CO_E_LAST                                          _HRESULT_TYPEDEF_(0x800401FF)
#define CO_E_FAILEDTOIMPERSONATE                           _HRESULT_TYPEDEF_(0x80040200)
#define CO_E_FAILEDTOGETSECCTX                             _HRESULT_TYPEDEF_(0x80040201)
#define CO_E_FAILEDTOOPENTHREADTOKEN                       _HRESULT_TYPEDEF_(0x80040202)
#define CO_E_FAILEDTOGETTOKENINFO                          _HRESULT_TYPEDEF_(0x80040203)
#define CO_E_TRUSTEEDOESNTMATCHCLIENT                      _HRESULT_TYPEDEF_(0x80040204)
#define CO_E_FAILEDTOQUERYCLIENTBLANKET                    _HRESULT_TYPEDEF_(0x80040205)
#define CO_E_FAILEDTOSETDACL                               _HRESULT_TYPEDEF_(0x80040206)
#define CO_E_ACCESSCHECKFAILED                             _HRESULT_TYPEDEF_(0x80040207)
#define CO_E_NETACCESSAPIFAILED                            _HRESULT_TYPEDEF_(0x80040208)
#define CO_E_WRONGTRUSTEENAMESYNTAX                        _HRESULT_TYPEDEF_(0x80040209)
#define CO_E_INVALIDSID                                    _HRESULT_TYPEDEF_(0x8004020A)
#define CO_E_CONVERSIONFAILED                              _HRESULT_TYPEDEF_(0x8004020B)
#define CO_E_NOMATCHINGSIDFOUND                            _HRESULT_TYPEDEF_(0x8004020C)
#define CO_E_LOOKUPACCSIDFAILED                            _HRESULT_TYPEDEF_(0x8004020D)
#define CO_E_NOMATCHINGNAMEFOUND                           _HRESULT_TYPEDEF_(0x8004020E)
#define CO_E_LOOKUPACCNAMEFAILED                           _HRESULT_TYPEDEF_(0x8004020F)
#define CO_E_SETSERLHNDLFAILED                             _HRESULT_TYPEDEF_(0x80040210)
#define CO_E_FAILEDTOGETWINDIR                             _HRESULT_TYPEDEF_(0x80040211)
#define CO_E_PATHTOOLONG                                   _HRESULT_TYPEDEF_(0x80040212)
#define CO_E_FAILEDTOGENUUID                               _HRESULT_TYPEDEF_(0x80040213)
#define CO_E_FAILEDTOCREATEFILE                            _HRESULT_TYPEDEF_(0x80040214)
#define CO_E_FAILEDTOCLOSEHANDLE                           _HRESULT_TYPEDEF_(0x80040215)
#define CO_E_EXCEEDSYSACLLIMIT                             _HRESULT_TYPEDEF_(0x80040216)
#define CO_E_ACESINWRONGORDER                              _HRESULT_TYPEDEF_(0x80040217)
#define CO_E_INCOMPATIBLESTREAMVERSION                     _HRESULT_TYPEDEF_(0x80040218)
#define CO_E_FAILEDTOOPENPROCESSTOKEN                      _HRESULT_TYPEDEF_(0x80040219)
#define CO_E_DECODEFAILED                                  _HRESULT_TYPEDEF_(0x8004021A)
#define CO_E_ACNOTINITIALIZED                              _HRESULT_TYPEDEF_(0x8004021B)

#define CONTEXT_S_FIRST                                    _HRESULT_TYPEDEF_(0x0004e000)
#define CONTEXT_S_LAST                                     _HRESULT_TYPEDEF_(0x0004e02f)
#define CONTEXT_E_FIRST                                    _HRESULT_TYPEDEF_(0x8004e000)
#define CONTEXT_E_ABORTED                                  _HRESULT_TYPEDEF_(0x8004e002)
#define CONTEXT_E_ABORTING                                 _HRESULT_TYPEDEF_(0x8004e003)
#define CONTEXT_E_NOCONTEXT                                _HRESULT_TYPEDEF_(0x8004e004)
#define CONTEXT_E_WOULD_DEADLOCK                           _HRESULT_TYPEDEF_(0x8004e005)
#define CONTEXT_E_SYNCH_TIMEOUT                            _HRESULT_TYPEDEF_(0x8004e006)
#define CONTEXT_E_OLDREF                                   _HRESULT_TYPEDEF_(0x8004e007)
#define CONTEXT_E_ROLENOTFOUND                             _HRESULT_TYPEDEF_(0x8004e00c)
#define CONTEXT_E_TMNOTAVAILABLE                           _HRESULT_TYPEDEF_(0x8004e00f)
#define CONTEXT_E_NOJIT                                    _HRESULT_TYPEDEF_(0x8004e026)
#define CONTEXT_E_NOTRANSACTION                            _HRESULT_TYPEDEF_(0x8004e027)
#define CONTEXT_E_LAST                                     _HRESULT_TYPEDEF_(0x8004e02f)

/* Task Scheduler Service Error Codes */
#define SCHED_S_TASK_READY                                 _HRESULT_TYPEDEF_(0x00041300)
#define SCHED_S_TASK_RUNNING                               _HRESULT_TYPEDEF_(0x00041301)
#define SCHED_S_TASK_DISABLED                              _HRESULT_TYPEDEF_(0x00041302)
#define SCHED_S_TASK_HAS_NOT_RUN                           _HRESULT_TYPEDEF_(0x00041303)
#define SCHED_S_TASK_NO_MORE_RUNS                          _HRESULT_TYPEDEF_(0x00041304)
#define SCHED_S_TASK_NOT_SCHEDULED                         _HRESULT_TYPEDEF_(0x00041305)
#define SCHED_S_TASK_TERMINATED                            _HRESULT_TYPEDEF_(0x00041306)
#define SCHED_S_TASK_NO_VALID_TRIGGERS                     _HRESULT_TYPEDEF_(0x00041307)
#define SCHED_S_EVENT_TRIGGER                              _HRESULT_TYPEDEF_(0x00041308)
#define SCHED_E_TRIGGER_NOT_FOUND                          _HRESULT_TYPEDEF_(0x80041309)
#define SCHED_E_TASK_NOT_READY                             _HRESULT_TYPEDEF_(0x8004130A)
#define SCHED_E_TASK_NOT_RUNNING                           _HRESULT_TYPEDEF_(0x8004130B)
#define SCHED_E_SERVICE_NOT_INSTALLED                      _HRESULT_TYPEDEF_(0x8004130C)
#define SCHED_E_CANNOT_OPEN_TASK                           _HRESULT_TYPEDEF_(0x8004130D)
#define SCHED_E_INVALID_TASK                               _HRESULT_TYPEDEF_(0x8004130E)
#define SCHED_E_ACCOUNT_INFORMATION_NOT_SET                _HRESULT_TYPEDEF_(0x8004130F)
#define SCHED_E_ACCOUNT_NAME_NOT_FOUND                     _HRESULT_TYPEDEF_(0x80041310)
#define SCHED_E_ACCOUNT_DBASE_CORRUPT                      _HRESULT_TYPEDEF_(0x80041311)
#define SCHED_E_NO_SECURITY_SERVICES                       _HRESULT_TYPEDEF_(0x80041312)
#define SCHED_E_UNKNOWN_OBJECT_VERSION                     _HRESULT_TYPEDEF_(0x80041313)
#define SCHED_E_UNSUPPORTED_ACCOUNT_OPTION                 _HRESULT_TYPEDEF_(0x80041314)
#define SCHED_E_SERVICE_NOT_RUNNING                        _HRESULT_TYPEDEF_(0x80041315)
#define SCHED_E_UNEXPECTEDNODE                             _HRESULT_TYPEDEF_(0x80041316)
#define SCHED_E_NAMESPACE                                  _HRESULT_TYPEDEF_(0x80041317)
#define SCHED_E_INVALIDVALUE                               _HRESULT_TYPEDEF_(0x80041318)
#define SCHED_E_MISSINGNODE                                _HRESULT_TYPEDEF_(0x80041319)
#define SCHED_E_MALFORMEDXML                               _HRESULT_TYPEDEF_(0x8004131A)
#define SCHED_S_SOME_TRIGGERS_FAILED                       _HRESULT_TYPEDEF_(0x0004131B)
#define SCHED_S_BATCH_LOGON_PROBLEM                        _HRESULT_TYPEDEF_(0x0004131C)
#define SCHED_E_TOO_MANY_NODES                             _HRESULT_TYPEDEF_(0x8004131D)
#define SCHED_E_PAST_END_BOUNDARY                          _HRESULT_TYPEDEF_(0x8004131E)
#define SCHED_E_ALREADY_RUNNING                            _HRESULT_TYPEDEF_(0x8004131F)
#define SCHED_E_USER_NOT_LOGGED_ON                         _HRESULT_TYPEDEF_(0x80041320)
#define SCHED_E_INVALID_TASK_HASH                          _HRESULT_TYPEDEF_(0x80041321)
#define SCHED_E_SERVICE_NOT_AVAILABLE                      _HRESULT_TYPEDEF_(0x80041322)
#define SCHED_E_SERVICE_TOO_BUSY                           _HRESULT_TYPEDEF_(0x80041323)
#define SCHED_E_TASK_ATTEMPTED                             _HRESULT_TYPEDEF_(0x80041324)
#define SCHED_S_TASK_QUEUED                                _HRESULT_TYPEDEF_(0x00041325)
#define SCHED_E_TASK_DISABLED                              _HRESULT_TYPEDEF_(0x80041326)
#define SCHED_E_TASK_NOT_V1_COMPAT                         _HRESULT_TYPEDEF_(0x80041327)
#define SCHED_E_START_ON_DEMAND                            _HRESULT_TYPEDEF_(0x80041328)

#define E_ACCESSDENIED                                     _HRESULT_TYPEDEF_(0x80070005)
#define E_HANDLE                                           _HRESULT_TYPEDEF_(0x80070006)
#define E_OUTOFMEMORY                                      _HRESULT_TYPEDEF_(0x8007000E)
#define E_INVALIDARG                                       _HRESULT_TYPEDEF_(0x80070057)

#define CO_E_CLASS_CREATE_FAILED                           _HRESULT_TYPEDEF_(0x80080001)
#define CO_E_SCM_ERROR                                     _HRESULT_TYPEDEF_(0x80080002)
#define CO_E_SCM_RPC_FAILURE                               _HRESULT_TYPEDEF_(0x80080003)
#define CO_E_BAD_PATH                                      _HRESULT_TYPEDEF_(0x80080004)
#define CO_E_SERVER_EXEC_FAILURE                           _HRESULT_TYPEDEF_(0x80080005)
#define CO_E_OBJSRV_RPC_FAILURE                            _HRESULT_TYPEDEF_(0x80080006)
#define MK_E_NO_NORMALIZED                                 _HRESULT_TYPEDEF_(0x80080007)
#define CO_E_SERVER_STOPPING                               _HRESULT_TYPEDEF_(0x80080008)
#define MEM_E_INVALID_ROOT                                 _HRESULT_TYPEDEF_(0x80080009)
#define MEM_E_INVALID_LINK                                 _HRESULT_TYPEDEF_(0x80080010)
#define MEM_E_INVALID_SIZE                                 _HRESULT_TYPEDEF_(0x80080011)
#define CO_S_NOTALLINTERFACES                              _HRESULT_TYPEDEF_(0x00080012)

#define ERROR_AUDITING_DISABLED                            _HRESULT_TYPEDEF_(0xC0090001)
#define ERROR_ALL_SIDS_FILTERED                            _HRESULT_TYPEDEF_(0xC0090002)
#define ERROR_BIZRULES_NOT_ENABLED                         _HRESULT_TYPEDEF_(0xC0090003)

/*Cryptographic Error Codes */
#define NTE_BAD_UID                                        _HRESULT_TYPEDEF_(0x80090001)
#define NTE_BAD_HASH                                       _HRESULT_TYPEDEF_(0x80090002)
#define NTE_BAD_KEY                                        _HRESULT_TYPEDEF_(0x80090003)
#define NTE_BAD_LEN                                        _HRESULT_TYPEDEF_(0x80090004)
#define NTE_BAD_DATA                                       _HRESULT_TYPEDEF_(0x80090005)
#define NTE_BAD_SIGNATURE                                  _HRESULT_TYPEDEF_(0x80090006)
#define NTE_BAD_VER                                        _HRESULT_TYPEDEF_(0x80090007)
#define NTE_BAD_ALGID                                      _HRESULT_TYPEDEF_(0x80090008)
#define NTE_BAD_FLAGS                                      _HRESULT_TYPEDEF_(0x80090009)
#define NTE_BAD_TYPE                                       _HRESULT_TYPEDEF_(0x8009000A)
#define NTE_BAD_KEY_STATE                                  _HRESULT_TYPEDEF_(0x8009000B)
#define NTE_BAD_HASH_STATE                                 _HRESULT_TYPEDEF_(0x8009000C)
#define NTE_NO_KEY                                         _HRESULT_TYPEDEF_(0x8009000D)
#define NTE_NO_MEMORY                                      _HRESULT_TYPEDEF_(0x8009000E)
#define NTE_EXISTS                                         _HRESULT_TYPEDEF_(0x8009000F)
#define NTE_PERM                                           _HRESULT_TYPEDEF_(0x80090010)
#define NTE_NOT_FOUND                                      _HRESULT_TYPEDEF_(0x80090011)
#define NTE_DOUBLE_ENCRYPT                                 _HRESULT_TYPEDEF_(0x80090012)
#define NTE_BAD_PROVIDER                                   _HRESULT_TYPEDEF_(0x80090013)
#define NTE_BAD_PROV_TYPE                                  _HRESULT_TYPEDEF_(0x80090014)
#define NTE_BAD_PUBLIC_KEY                                 _HRESULT_TYPEDEF_(0x80090015)
#define NTE_BAD_KEYSET                                     _HRESULT_TYPEDEF_(0x80090016)
#define NTE_PROV_TYPE_NOT_DEF                              _HRESULT_TYPEDEF_(0x80090017)
#define NTE_PROV_TYPE_ENTRY_BAD                            _HRESULT_TYPEDEF_(0x80090018)
#define NTE_KEYSET_NOT_DEF                                 _HRESULT_TYPEDEF_(0x80090019)
#define NTE_KEYSET_ENTRY_BAD                               _HRESULT_TYPEDEF_(0x8009001A)
#define NTE_PROV_TYPE_NO_MATCH                             _HRESULT_TYPEDEF_(0x8009001B)
#define NTE_SIGNATURE_FILE_BAD                             _HRESULT_TYPEDEF_(0x8009001C)
#define NTE_PROVIDER_DLL_FAIL                              _HRESULT_TYPEDEF_(0x8009001D)
#define NTE_PROV_DLL_NOT_FOUND                             _HRESULT_TYPEDEF_(0x8009001E)
#define NTE_BAD_KEYSET_PARAM                               _HRESULT_TYPEDEF_(0x8009001F)
#define NTE_FAIL                                           _HRESULT_TYPEDEF_(0x80090020)
#define NTE_SYS_ERR                                        _HRESULT_TYPEDEF_(0x80090021)
#define NTE_SILENT_CONTEXT                                 _HRESULT_TYPEDEF_(0x80090022)
#define NTE_TOKEN_KEYSET_STORAGE_FULL                      _HRESULT_TYPEDEF_(0x80090023)
#define NTE_TEMPORARY_PROFILE                              _HRESULT_TYPEDEF_(0x80090024)
#define NTE_FIXEDPARAMETER                                 _HRESULT_TYPEDEF_(0x80090025)
#define NTE_INVALID_HANDLE                                 _HRESULT_TYPEDEF_(0x80090026)
#define NTE_INVALID_PARAMETER                              _HRESULT_TYPEDEF_(0x80090027)
#define NTE_BUFFER_TOO_SMALL                               _HRESULT_TYPEDEF_(0x80090028)
#define NTE_NOT_SUPPORTED                                  _HRESULT_TYPEDEF_(0x80090029)
#define NTE_NO_MORE_ITEMS                                  _HRESULT_TYPEDEF_(0x8009002A)
#define NTE_BUFFERS_OVERLAP                                _HRESULT_TYPEDEF_(0x8009002B)
#define NTE_DECRYPTION_FAILURE                             _HRESULT_TYPEDEF_(0x8009002C)
#define NTE_INTERNAL_ERROR                                 _HRESULT_TYPEDEF_(0x8009002D)
#define NTE_UI_REQUIRED                                    _HRESULT_TYPEDEF_(0x8009002E)
#define NTE_HMAC_NOT_SUPPORTED                             _HRESULT_TYPEDEF_(0x8009002F)
#define NTE_OP_OK                                          _HRESULT_TYPEDEF_(0)

#define SEC_E_INSUFFICIENT_MEMORY                          _HRESULT_TYPEDEF_(0x80090300)
#define SEC_E_INVALID_HANDLE                               _HRESULT_TYPEDEF_(0x80090301)
#define SEC_E_UNSUPPORTED_FUNCTION                         _HRESULT_TYPEDEF_(0x80090302)
#define SEC_E_TARGET_UNKNOWN                               _HRESULT_TYPEDEF_(0x80090303)
#define SEC_E_INTERNAL_ERROR                               _HRESULT_TYPEDEF_(0x80090304)
#define SEC_E_SECPKG_NOT_FOUND                             _HRESULT_TYPEDEF_(0x80090305)
#define SEC_E_NOT_OWNER                                    _HRESULT_TYPEDEF_(0x80090306)
#define SEC_E_CANNOT_INSTALL                               _HRESULT_TYPEDEF_(0x80090307)
#define SEC_E_INVALID_TOKEN                                _HRESULT_TYPEDEF_(0x80090308)
#define SEC_E_CANNOT_PACK                                  _HRESULT_TYPEDEF_(0x80090309)
#define SEC_E_QOP_NOT_SUPPORTED                            _HRESULT_TYPEDEF_(0x8009030A)
#define SEC_E_NO_IMPERSONATION                             _HRESULT_TYPEDEF_(0x8009030B)
#define SEC_E_LOGON_DENIED                                 _HRESULT_TYPEDEF_(0x8009030C)
#define SEC_E_UNKNOWN_CREDENTIALS                          _HRESULT_TYPEDEF_(0x8009030D)
#define SEC_E_NO_CREDENTIALS                               _HRESULT_TYPEDEF_(0x8009030E)
#define SEC_E_MESSAGE_ALTERED                              _HRESULT_TYPEDEF_(0x8009030F)
#define SEC_E_OUT_OF_SEQUENCE                              _HRESULT_TYPEDEF_(0x80090310)
#define SEC_E_NO_AUTHENTICATING_AUTHORITY                  _HRESULT_TYPEDEF_(0x80090311)
#define SEC_E_BAD_PKGID                                    _HRESULT_TYPEDEF_(0x80090316)
#define SEC_E_CONTEXT_EXPIRED                              _HRESULT_TYPEDEF_(0x80090317)
#define SEC_E_INCOMPLETE_MESSAGE                           _HRESULT_TYPEDEF_(0x80090318)
#define SEC_E_INCOMPLETE_CREDENTIALS                       _HRESULT_TYPEDEF_(0x80090320)
#define SEC_E_BUFFER_TOO_SMALL                             _HRESULT_TYPEDEF_(0x80090321)
#define SEC_E_WRONG_PRINCIPAL                              _HRESULT_TYPEDEF_(0x80090322)
#define SEC_E_TIME_SKEW                                    _HRESULT_TYPEDEF_(0x80090324)
#define SEC_E_UNTRUSTED_ROOT                               _HRESULT_TYPEDEF_(0x80090325)
#define SEC_E_ILLEGAL_MESSAGE                              _HRESULT_TYPEDEF_(0x80090326)
#define SEC_E_CERT_UNKNOWN                                 _HRESULT_TYPEDEF_(0x80090327)
#define SEC_E_CERT_EXPIRED                                 _HRESULT_TYPEDEF_(0x80090328)
#define SEC_E_ENCRYPT_FAILURE                              _HRESULT_TYPEDEF_(0x80090329)
#define SEC_E_DECRYPT_FAILURE                              _HRESULT_TYPEDEF_(0x80090330)
#define SEC_E_ALGORITHM_MISMATCH                           _HRESULT_TYPEDEF_(0x80090331)
#define SEC_E_SECURITY_QOS_FAILED                          _HRESULT_TYPEDEF_(0x80090332)
#define SEC_E_UNFINISHED_CONTEXT_DELETED                   _HRESULT_TYPEDEF_(0x80090333)
#define SEC_E_NO_TGT_REPLY                                 _HRESULT_TYPEDEF_(0x80090334)
#define SEC_E_NO_IP_ADDRESSES                              _HRESULT_TYPEDEF_(0x80090335)
#define SEC_E_WRONG_CREDENTIAL_HANDLE                      _HRESULT_TYPEDEF_(0x80090336)
#define SEC_E_CRYPTO_SYSTEM_INVALID                        _HRESULT_TYPEDEF_(0x80090337)
#define SEC_E_MAX_REFERRALS_EXCEEDED                       _HRESULT_TYPEDEF_(0x80090338)
#define SEC_E_MUST_BE_KDC                                  _HRESULT_TYPEDEF_(0x80090339)
#define SEC_E_STRONG_CRYPTO_NOT_SUPPORTED                  _HRESULT_TYPEDEF_(0x8009033A)
#define SEC_E_TOO_MANY_PRINCIPALS                          _HRESULT_TYPEDEF_(0x8009033B)
#define SEC_E_NO_PA_DATA                                   _HRESULT_TYPEDEF_(0x8009033C)
#define SEC_E_PKINIT_NAME_MISMATCH                         _HRESULT_TYPEDEF_(0x8009033D)
#define SEC_E_SMARTCARD_LOGON_REQUIRED                     _HRESULT_TYPEDEF_(0x8009033E)
#define SEC_E_SHUTDOWN_IN_PROGRESS                         _HRESULT_TYPEDEF_(0x8009033F)
#define SEC_E_KDC_INVALID_REQUEST                          _HRESULT_TYPEDEF_(0x80090340)
#define SEC_E_KDC_UNABLE_TO_REFER                          _HRESULT_TYPEDEF_(0x80090341)
#define SEC_E_KDC_UNKNOWN_ETYPE                            _HRESULT_TYPEDEF_(0x80090342)
#define SEC_E_UNSUPPORTED_PREAUTH                          _HRESULT_TYPEDEF_(0x80090343)
#define SEC_E_DELEGATION_REQUIRED                          _HRESULT_TYPEDEF_(0x80090345)
#define SEC_E_BAD_BINDINGS                                 _HRESULT_TYPEDEF_(0x80090346)
#define SEC_E_MULTIPLE_ACCOUNTS                            _HRESULT_TYPEDEF_(0x80090347)
#define SEC_E_NO_KERB_KEY                                  _HRESULT_TYPEDEF_(0x80090348)
#define SEC_E_CERT_WRONG_USAGE                             _HRESULT_TYPEDEF_(0x80090349)
#define SEC_E_DOWNGRADE_DETECTED                           _HRESULT_TYPEDEF_(0x80090350)
#define SEC_E_SMARTCARD_CERT_REVOKED                       _HRESULT_TYPEDEF_(0x80090351)
#define SEC_E_ISSUING_CA_UNTRUSTED                         _HRESULT_TYPEDEF_(0x80090352)
#define SEC_E_REVOCATION_OFFLINE_C                         _HRESULT_TYPEDEF_(0x80090353)
#define SEC_E_PKINIT_CLIENT_FAILURE                        _HRESULT_TYPEDEF_(0x80090354)
#define SEC_E_SMARTCARD_CERT_EXPIRED                       _HRESULT_TYPEDEF_(0x80090355)
#define SEC_E_NO_S4U_PROT_SUPPORT                          _HRESULT_TYPEDEF_(0x80090356)
#define SEC_E_CROSSREALM_DELEGATION_FAILURE                _HRESULT_TYPEDEF_(0x80090357)
#define SEC_E_REVOCATION_OFFLINE_KDC                       _HRESULT_TYPEDEF_(0x80090358)
#define SEC_E_ISSUING_CA_UNTRUSTED_KDC                     _HRESULT_TYPEDEF_(0x80090359)
#define SEC_E_KDC_CERT_EXPIRED                             _HRESULT_TYPEDEF_(0x8009035A)
#define SEC_E_KDC_CERT_REVOKED                             _HRESULT_TYPEDEF_(0x8009035B)
#define SEC_E_INVALID_PARAMETER                            _HRESULT_TYPEDEF_(0x8009035D)
#define SEC_E_DELEGATION_POLICY                            _HRESULT_TYPEDEF_(0x8009035E)
#define SEC_E_POLICY_NLTM_ONLY                             _HRESULT_TYPEDEF_(0x8009035F)
#define SEC_E_NO_CONTEXT                                   _HRESULT_TYPEDEF_(0x80090361)
#define SEC_E_PKU2U_CERT_FAILURE                           _HRESULT_TYPEDEF_(0x80090362)
#define SEC_E_MUTUAL_AUTH_FAILED                           _HRESULT_TYPEDEF_(0x80090363)
#define SEC_E_ONLY_HTTPS_ALLOWED                           _HRESULT_TYPEDEF_(0x80090365)
#define SEC_E_APPLICATION_PROTOCOL_MISMATCH                _HRESULT_TYPEDEF_(0x80090367)
#define SEC_E_INVALID_UPN_NAME                             _HRESULT_TYPEDEF_(0x80090369)

#define SEC_I_CONTINUE_NEEDED                              _HRESULT_TYPEDEF_(0x00090312)
#define SEC_I_COMPLETE_NEEDED                              _HRESULT_TYPEDEF_(0x00090313)
#define SEC_I_COMPLETE_AND_CONTINUE                        _HRESULT_TYPEDEF_(0x00090314)
#define SEC_I_CONTEXT_EXPIRED                              _HRESULT_TYPEDEF_(0x00090317)
#define SEC_I_RENEGOTIATE                                  _HRESULT_TYPEDEF_(0x00090321)
#define SEC_I_SIGNATURE_NEEDED                             _HRESULT_TYPEDEF_(0x0009035C)
#define SEC_I_NO_RENEGOTIATION                             _HRESULT_TYPEDEF_(0x00090360)
#define SEC_I_MESSAGE_FRAGMENT                             _HRESULT_TYPEDEF_(0x00090364)
#define SEC_I_CONTINUE_NEEDED_MESSAGE_OK                   _HRESULT_TYPEDEF_(0x00090366)
#define SEC_I_ASYNC_CALL_PENDING                           _HRESULT_TYPEDEF_(0x00090368)

#define CRYPT_E_MSG_ERROR                                  _HRESULT_TYPEDEF_(0x80091001)
#define CRYPT_E_UNKNOWN_ALGO                               _HRESULT_TYPEDEF_(0x80091002)
#define CRYPT_E_OID_FORMAT                                 _HRESULT_TYPEDEF_(0x80091003)
#define CRYPT_E_INVALID_MSG_TYPE                           _HRESULT_TYPEDEF_(0x80091004)
#define CRYPT_E_UNEXPECTED_ENCODING                        _HRESULT_TYPEDEF_(0x80091005)
#define CRYPT_E_AUTH_ATTR_MISSING                          _HRESULT_TYPEDEF_(0x80091006)
#define CRYPT_E_HASH_VALUE                                 _HRESULT_TYPEDEF_(0x80091007)
#define CRYPT_E_INVALID_INDEX                              _HRESULT_TYPEDEF_(0x80091008)
#define CRYPT_E_ALREADY_DECRYPTED                          _HRESULT_TYPEDEF_(0x80091009)
#define CRYPT_E_NOT_DECRYPTED                              _HRESULT_TYPEDEF_(0x8009100A)
#define CRYPT_E_RECIPIENT_NOT_FOUND                        _HRESULT_TYPEDEF_(0x8009100B)
#define CRYPT_E_CONTROL_TYPE                               _HRESULT_TYPEDEF_(0x8009100C)
#define CRYPT_E_ISSUER_SERIALNUMBER                        _HRESULT_TYPEDEF_(0x8009100D)
#define CRYPT_E_SIGNER_NOT_FOUND                           _HRESULT_TYPEDEF_(0x8009100E)
#define CRYPT_E_ATTRIBUTES_MISSING                         _HRESULT_TYPEDEF_(0x8009100F)
#define CRYPT_E_STREAM_MSG_NOT_READY                       _HRESULT_TYPEDEF_(0x80091010)
#define CRYPT_E_STREAM_INSUFFICIENT_DATA                   _HRESULT_TYPEDEF_(0x80091011)
#define CRYPT_I_NEW_PROTECTION_REQUIRED                    _HRESULT_TYPEDEF_(0x80091012)

#define CRYPT_E_BAD_LEN                                    _HRESULT_TYPEDEF_(0x80092001)
#define CRYPT_E_BAD_ENCODE                                 _HRESULT_TYPEDEF_(0x80092002)
#define CRYPT_E_FILE_ERROR                                 _HRESULT_TYPEDEF_(0x80092003)
#define CRYPT_E_NOT_FOUND                                  _HRESULT_TYPEDEF_(0x80092004)
#define CRYPT_E_EXISTS                                     _HRESULT_TYPEDEF_(0x80092005)
#define CRYPT_E_NO_PROVIDER                                _HRESULT_TYPEDEF_(0x80092006)
#define CRYPT_E_SELF_SIGNED                                _HRESULT_TYPEDEF_(0x80092007)
#define CRYPT_E_DELETED_PREV                               _HRESULT_TYPEDEF_(0x80092008)
#define CRYPT_E_NO_MATCH                                   _HRESULT_TYPEDEF_(0x80092009)
#define CRYPT_E_UNEXPECTED_MSG_TYPE                        _HRESULT_TYPEDEF_(0x8009200A)
#define CRYPT_E_NO_KEY_PROPERTY                            _HRESULT_TYPEDEF_(0x8009200B)
#define CRYPT_E_NO_DECRYPT_CERT                            _HRESULT_TYPEDEF_(0x8009200C)
#define CRYPT_E_BAD_MSG                                    _HRESULT_TYPEDEF_(0x8009200D)
#define CRYPT_E_NO_SIGNER                                  _HRESULT_TYPEDEF_(0x8009200E)
#define CRYPT_E_PENDING_CLOSE                              _HRESULT_TYPEDEF_(0x8009200F)
#define CRYPT_E_REVOKED                                    _HRESULT_TYPEDEF_(0x80092010)
#define CRYPT_E_NO_REVOCATION_DLL                          _HRESULT_TYPEDEF_(0x80092011)
#define CRYPT_E_NO_REVOCATION_CHECK                        _HRESULT_TYPEDEF_(0x80092012)
#define CRYPT_E_REVOCATION_OFFLINE                         _HRESULT_TYPEDEF_(0x80092013)
#define CRYPT_E_NOT_IN_REVOCATION_DATABASE                 _HRESULT_TYPEDEF_(0x80092014)
#define CRYPT_E_INVALID_NUMERIC_STRING                     _HRESULT_TYPEDEF_(0x80092020)
#define CRYPT_E_INVALID_PRINTABLE_STRING                   _HRESULT_TYPEDEF_(0x80092021)
#define CRYPT_E_INVALID_IA5_STRING                         _HRESULT_TYPEDEF_(0x80092022)
#define CRYPT_E_INVALID_X500_STRING                        _HRESULT_TYPEDEF_(0x80092023)
#define CRYPT_E_NOT_CHAR_STRING                            _HRESULT_TYPEDEF_(0x80092024)
#define CRYPT_E_FILERESIZED                                _HRESULT_TYPEDEF_(0x80092025)
#define CRYPT_E_SECURITY_SETTINGS                          _HRESULT_TYPEDEF_(0x80092026)
#define CRYPT_E_NO_VERIFY_USAGE_DLL                        _HRESULT_TYPEDEF_(0x80092027)
#define CRYPT_E_NO_VERIFY_USAGE_CHECK                      _HRESULT_TYPEDEF_(0x80092028)
#define CRYPT_E_VERIFY_USAGE_OFFLINE                       _HRESULT_TYPEDEF_(0x80092029)
#define CRYPT_E_NOT_IN_CTL                                 _HRESULT_TYPEDEF_(0x8009202A)
#define CRYPT_E_NO_TRUSTED_SIGNER                          _HRESULT_TYPEDEF_(0x8009202B)
#define CRYPT_E_MISSING_PUBKEY_PARA                        _HRESULT_TYPEDEF_(0x8009202C)
#define CRYPT_E_OSS_ERROR                                  _HRESULT_TYPEDEF_(0x80093000)
#define OSS_MORE_BUF                                       _HRESULT_TYPEDEF_(0x80093001)
#define OSS_NEGATIVE_UINTEGER                              _HRESULT_TYPEDEF_(0x80093002)
#define OSS_PDU_RANGE                                      _HRESULT_TYPEDEF_(0x80093003)
#define OSS_MORE_INPUT                                     _HRESULT_TYPEDEF_(0x80093004)
#define OSS_DATA_ERROR                                     _HRESULT_TYPEDEF_(0x80093005)
#define OSS_BAD_ARG                                        _HRESULT_TYPEDEF_(0x80093006)
#define OSS_BAD_VERSION                                    _HRESULT_TYPEDEF_(0x80093007)
#define OSS_OUT_MEMORY                                     _HRESULT_TYPEDEF_(0x80093008)
#define OSS_PDU_MISMATCH                                   _HRESULT_TYPEDEF_(0x80093009)
#define OSS_LIMITED                                        _HRESULT_TYPEDEF_(0x8009300A)
#define OSS_BAD_PTR                                        _HRESULT_TYPEDEF_(0x8009300B)
#define OSS_BAD_TIME                                       _HRESULT_TYPEDEF_(0x8009300C)
#define OSS_INDEFINITE_NOT_SUPPORTED                       _HRESULT_TYPEDEF_(0x8009300D)
#define OSS_MEM_ERROR                                      _HRESULT_TYPEDEF_(0x8009300E)
#define OSS_BAD_TABLE                                      _HRESULT_TYPEDEF_(0x8009300F)
#define OSS_TOO_LONG                                       _HRESULT_TYPEDEF_(0x80093010)
#define OSS_CONSTRAINT_VIOLATED                            _HRESULT_TYPEDEF_(0x80093011)
#define OSS_FATAL_ERROR                                    _HRESULT_TYPEDEF_(0x80093012)
#define OSS_ACCESS_SERIALIZATION_ERROR                     _HRESULT_TYPEDEF_(0x80093013)
#define OSS_NULL_TBL                                       _HRESULT_TYPEDEF_(0x80093014)
#define OSS_NULL_FCN                                       _HRESULT_TYPEDEF_(0x80093015)
#define OSS_BAD_ENCRULES                                   _HRESULT_TYPEDEF_(0x80093016)
#define OSS_UNAVAIL_ENCRULES                               _HRESULT_TYPEDEF_(0x80093017)
#define OSS_CANT_OPEN_TRACE_WINDOW                         _HRESULT_TYPEDEF_(0x80093018)
#define OSS_UNIMPLEMENTED                                  _HRESULT_TYPEDEF_(0x80093019)
#define OSS_OID_DLL_NOT_LINKED                             _HRESULT_TYPEDEF_(0x8009301A)
#define OSS_CANT_OPEN_TRACE_FILE                           _HRESULT_TYPEDEF_(0x8009301B)
#define OSS_TRACE_FILE_ALREADY_OPEN                        _HRESULT_TYPEDEF_(0x8009301C)
#define OSS_TABLE_MISMATCH                                 _HRESULT_TYPEDEF_(0x8009301D)
#define OSS_TYPE_NOT_SUPPORTED                             _HRESULT_TYPEDEF_(0x8009301E)
#define OSS_REAL_DLL_NOT_LINKED                            _HRESULT_TYPEDEF_(0x8009301F)
#define OSS_REAL_CODE_NOT_LINKED                           _HRESULT_TYPEDEF_(0x80093020)
#define OSS_OUT_OF_RANGE                                   _HRESULT_TYPEDEF_(0x80093021)
#define OSS_COPIER_DLL_NOT_LINKED                          _HRESULT_TYPEDEF_(0x80093022)
#define OSS_CONSTRAINT_DLL_NOT_LINKED                      _HRESULT_TYPEDEF_(0x80093023)
#define OSS_COMPARATOR_DLL_NOT_LINKED                      _HRESULT_TYPEDEF_(0x80093024)
#define OSS_COMPARATOR_CODE_NOT_LINKED                     _HRESULT_TYPEDEF_(0x80093025)
#define OSS_MEM_MGR_DLL_NOT_LINKED                         _HRESULT_TYPEDEF_(0x80093026)
#define OSS_PDV_DLL_NOT_LINKED                             _HRESULT_TYPEDEF_(0x80093027)
#define OSS_PDV_CODE_NOT_LINKED                            _HRESULT_TYPEDEF_(0x80093028)
#define OSS_API_DLL_NOT_LINKED                             _HRESULT_TYPEDEF_(0x80093029)
#define OSS_BERDER_DLL_NOT_LINKED                          _HRESULT_TYPEDEF_(0x8009302A)
#define OSS_PER_DLL_NOT_LINKED                             _HRESULT_TYPEDEF_(0x8009302B)
#define OSS_OPEN_TYPE_ERROR                                _HRESULT_TYPEDEF_(0x8009302C)
#define OSS_MUTEX_NOT_CREATED                              _HRESULT_TYPEDEF_(0x8009302D)
#define OSS_CANT_CLOSE_TRACE_FILE                          _HRESULT_TYPEDEF_(0x8009302E)
#define CRYPT_E_ASN1_ERROR                                 _HRESULT_TYPEDEF_(0x80093100)
#define CRYPT_E_ASN1_INTERNAL                              _HRESULT_TYPEDEF_(0x80093101)
#define CRYPT_E_ASN1_EOD                                   _HRESULT_TYPEDEF_(0x80093102)
#define CRYPT_E_ASN1_CORRUPT                               _HRESULT_TYPEDEF_(0x80093103)
#define CRYPT_E_ASN1_LARGE                                 _HRESULT_TYPEDEF_(0x80093104)
#define CRYPT_E_ASN1_CONSTRAINT                            _HRESULT_TYPEDEF_(0x80093105)
#define CRYPT_E_ASN1_MEMORY                                _HRESULT_TYPEDEF_(0x80093106)
#define CRYPT_E_ASN1_OVERFLOW                              _HRESULT_TYPEDEF_(0x80093107)
#define CRYPT_E_ASN1_BADPDU                                _HRESULT_TYPEDEF_(0x80093108)
#define CRYPT_E_ASN1_BADARGS                               _HRESULT_TYPEDEF_(0x80093109)
#define CRYPT_E_ASN1_BADREAL                               _HRESULT_TYPEDEF_(0x8009310A)
#define CRYPT_E_ASN1_BADTAG                                _HRESULT_TYPEDEF_(0x8009310B)
#define CRYPT_E_ASN1_CHOICE                                _HRESULT_TYPEDEF_(0x8009310C)
#define CRYPT_E_ASN1_RULE                                  _HRESULT_TYPEDEF_(0x8009310D)
#define CRYPT_E_ASN1_UTF8                                  _HRESULT_TYPEDEF_(0x8009310E)
#define CRYPT_E_ASN1_PDU_TYPE                              _HRESULT_TYPEDEF_(0x80093133)
#define CRYPT_E_ASN1_NYI                                   _HRESULT_TYPEDEF_(0x80093134)
#define CRYPT_E_ASN1_EXTENDED                              _HRESULT_TYPEDEF_(0x80093201)
#define CRYPT_E_ASN1_NOEOD                                 _HRESULT_TYPEDEF_(0x80093202)

#define TRUST_E_SYSTEM_ERROR                               _HRESULT_TYPEDEF_(0x80096001)
#define TRUST_E_NO_SIGNER_CERT                             _HRESULT_TYPEDEF_(0x80096002)
#define TRUST_E_COUNTER_SIGNER                             _HRESULT_TYPEDEF_(0x80096003)
#define TRUST_E_CERT_SIGNATURE                             _HRESULT_TYPEDEF_(0x80096004)
#define TRUST_E_TIME_STAMP                                 _HRESULT_TYPEDEF_(0x80096005)
#define TRUST_E_BAD_DIGEST                                 _HRESULT_TYPEDEF_(0x80096010)
#define TRUST_E_BASIC_CONSTRAINTS                          _HRESULT_TYPEDEF_(0x80096019)
#define TRUST_E_FINANCIAL_CRITERIA                         _HRESULT_TYPEDEF_(0x8009601E)

#define MSSIPOTF_E_OUTOFMEMRANGE                           _HRESULT_TYPEDEF_(0x80097001)
#define MSSIPOTF_E_CANTGETOBJECT                           _HRESULT_TYPEDEF_(0x80097002)
#define MSSIPOTF_E_NOHEADTABLE                             _HRESULT_TYPEDEF_(0x80097003)
#define MSSIPOTF_E_BAD_MAGICNUMBER                         _HRESULT_TYPEDEF_(0x80097004)
#define MSSIPOTF_E_BAD_OFFSET_TABLE                        _HRESULT_TYPEDEF_(0x80097005)
#define MSSIPOTF_E_TABLE_TAGORDER                          _HRESULT_TYPEDEF_(0x80097006)
#define MSSIPOTF_E_TABLE_LONGWORD                          _HRESULT_TYPEDEF_(0x80097007)
#define MSSIPOTF_E_BAD_FIRST_TABLE_PLACEMENT               _HRESULT_TYPEDEF_(0x80097008)
#define MSSIPOTF_E_TABLES_OVERLAP                          _HRESULT_TYPEDEF_(0x80097009)
#define MSSIPOTF_E_TABLE_PADBYTES                          _HRESULT_TYPEDEF_(0x8009700A)
#define MSSIPOTF_E_FILETOOSMALL                            _HRESULT_TYPEDEF_(0x8009700B)
#define MSSIPOTF_E_TABLE_CHECKSUM                          _HRESULT_TYPEDEF_(0x8009700C)
#define MSSIPOTF_E_FILE_CHECKSUM                           _HRESULT_TYPEDEF_(0x8009700D)
#define MSSIPOTF_E_FAILED_POLICY                           _HRESULT_TYPEDEF_(0x80097010)
#define MSSIPOTF_E_FAILED_HINTS_CHECK                      _HRESULT_TYPEDEF_(0x80097011)
#define MSSIPOTF_E_NOT_OPENTYPE                            _HRESULT_TYPEDEF_(0x80097012)
#define MSSIPOTF_E_FILE                                    _HRESULT_TYPEDEF_(0x80097013)
#define MSSIPOTF_E_CRYPT                                   _HRESULT_TYPEDEF_(0x80097014)
#define MSSIPOTF_E_BADVERSION                              _HRESULT_TYPEDEF_(0x80097015)
#define MSSIPOTF_E_DSIG_STRUCTURE                          _HRESULT_TYPEDEF_(0x80097016)
#define MSSIPOTF_E_PCONST_CHECK                            _HRESULT_TYPEDEF_(0x80097017)
#define MSSIPOTF_E_STRUCTURE                               _HRESULT_TYPEDEF_(0x80097018)
#define ERROR_CRED_REQUIRES_CONFIRMATION                   _HRESULT_TYPEDEF_(0x80097019)

#define TRUST_E_PROVIDER_UNKNOWN                           _HRESULT_TYPEDEF_(0x800B0001)
#define TRUST_E_ACTION_UNKNOWN                             _HRESULT_TYPEDEF_(0x800B0002)
#define TRUST_E_SUBJECT_FORM_UNKNOWN                       _HRESULT_TYPEDEF_(0x800B0003)
#define TRUST_E_SUBJECT_NOT_TRUSTED                        _HRESULT_TYPEDEF_(0x800B0004)
#define TRUST_E_NOSIGNATURE                                _HRESULT_TYPEDEF_(0x800B0100)
#define CERT_E_EXPIRED                                     _HRESULT_TYPEDEF_(0x800B0101)
#define CERT_E_VALIDITYPERIODNESTING                       _HRESULT_TYPEDEF_(0x800B0102)
#define CERT_E_ROLE                                        _HRESULT_TYPEDEF_(0x800B0103)
#define CERT_E_PATHLENCONST                                _HRESULT_TYPEDEF_(0x800B0104)
#define CERT_E_CRITICAL                                    _HRESULT_TYPEDEF_(0x800B0105)
#define CERT_E_PURPOSE                                     _HRESULT_TYPEDEF_(0x800B0106)
#define CERT_E_ISSUERCHAINING                              _HRESULT_TYPEDEF_(0x800B0107)
#define CERT_E_MALFORMED                                   _HRESULT_TYPEDEF_(0x800B0108)
#define CERT_E_UNTRUSTEDROOT                               _HRESULT_TYPEDEF_(0x800B0109)
#define CERT_E_CHAINING                                    _HRESULT_TYPEDEF_(0x800B010A)
#define TRUST_E_FAIL                                       _HRESULT_TYPEDEF_(0x800B010B)
#define CERT_E_REVOKED                                     _HRESULT_TYPEDEF_(0x800B010C)
#define CERT_E_UNTRUSTEDTESTROOT                           _HRESULT_TYPEDEF_(0x800B010D)
#define CERT_E_REVOCATION_FAILURE                          _HRESULT_TYPEDEF_(0x800B010E)
#define CERT_E_CN_NO_MATCH                                 _HRESULT_TYPEDEF_(0x800B010F)
#define CERT_E_WRONG_USAGE                                 _HRESULT_TYPEDEF_(0x800B0110)
#define TRUST_E_EXPLICIT_DISTRUST                          _HRESULT_TYPEDEF_(0x800B0111)
#define CERT_E_UNTRUSTEDCA                                 _HRESULT_TYPEDEF_(0x800B0112)
#define CERT_E_INVALID_POLICY                              _HRESULT_TYPEDEF_(0x800B0113)
#define CERT_E_INVALID_NAME                                _HRESULT_TYPEDEF_(0x800B0114)

#define SPAPI_E_EXPECTED_SECTION_NAME                      _HRESULT_TYPEDEF_(0x800F0000)
#define SPAPI_E_BAD_SECTION_NAME_LINE                      _HRESULT_TYPEDEF_(0x800F0001)
#define SPAPI_E_SECTION_NAME_TOO_LONG                      _HRESULT_TYPEDEF_(0x800F0002)
#define SPAPI_E_GENERAL_SYNTAX                             _HRESULT_TYPEDEF_(0x800F0003)
#define SPAPI_E_WRONG_INF_STYLE                            _HRESULT_TYPEDEF_(0x800F0100)
#define SPAPI_E_SECTION_NOT_FOUND                          _HRESULT_TYPEDEF_(0x800F0101)
#define SPAPI_E_LINE_NOT_FOUND                             _HRESULT_TYPEDEF_(0x800F0102)
#define SPAPI_E_NO_BACKUP                                  _HRESULT_TYPEDEF_(0x800F0103)
#define SPAPI_E_NO_ASSOCIATED_CLASS                        _HRESULT_TYPEDEF_(0x800F0200)
#define SPAPI_E_CLASS_MISMATCH                             _HRESULT_TYPEDEF_(0x800F0201)
#define SPAPI_E_DUPLICATE_FOUND                            _HRESULT_TYPEDEF_(0x800F0202)
#define SPAPI_E_NO_DRIVER_SELECTED                         _HRESULT_TYPEDEF_(0x800F0203)
#define SPAPI_E_KEY_DOES_NOT_EXIST                         _HRESULT_TYPEDEF_(0x800F0204)
#define SPAPI_E_INVALID_DEVINST_NAME                       _HRESULT_TYPEDEF_(0x800F0205)
#define SPAPI_E_INVALID_CLASS                              _HRESULT_TYPEDEF_(0x800F0206)
#define SPAPI_E_DEVINST_ALREADY_EXISTS                     _HRESULT_TYPEDEF_(0x800F0207)
#define SPAPI_E_DEVINFO_NOT_REGISTERED                     _HRESULT_TYPEDEF_(0x800F0208)
#define SPAPI_E_INVALID_REG_PROPERTY                       _HRESULT_TYPEDEF_(0x800F0209)
#define SPAPI_E_NO_INF                                     _HRESULT_TYPEDEF_(0x800F020A)
#define SPAPI_E_NO_SUCH_DEVINST                            _HRESULT_TYPEDEF_(0x800F020B)
#define SPAPI_E_CANT_LOAD_CLASS_ICON                       _HRESULT_TYPEDEF_(0x800F020C)
#define SPAPI_E_INVALID_CLASS_INSTALLER                    _HRESULT_TYPEDEF_(0x800F020D)
#define SPAPI_E_DI_DO_DEFAULT                              _HRESULT_TYPEDEF_(0x800F020E)
#define SPAPI_E_DI_NOFILECOPY                              _HRESULT_TYPEDEF_(0x800F020F)
#define SPAPI_E_INVALID_HWPROFILE                          _HRESULT_TYPEDEF_(0x800F0210)
#define SPAPI_E_NO_DEVICE_SELECTED                         _HRESULT_TYPEDEF_(0x800F0211)
#define SPAPI_E_DEVINFO_LIST_LOCKED                        _HRESULT_TYPEDEF_(0x800F0212)
#define SPAPI_E_DEVINFO_DATA_LOCKED                        _HRESULT_TYPEDEF_(0x800F0213)
#define SPAPI_E_DI_BAD_PATH                                _HRESULT_TYPEDEF_(0x800F0214)
#define SPAPI_E_NO_CLASSINSTALL_PARAMS                     _HRESULT_TYPEDEF_(0x800F0215)
#define SPAPI_E_FILEQUEUE_LOCKED                           _HRESULT_TYPEDEF_(0x800F0216)
#define SPAPI_E_BAD_SERVICE_INSTALLSECT                    _HRESULT_TYPEDEF_(0x800F0217)
#define SPAPI_E_NO_CLASS_DRIVER_LIST                       _HRESULT_TYPEDEF_(0x800F0218)
#define SPAPI_E_NO_ASSOCIATED_SERVICE                      _HRESULT_TYPEDEF_(0x800F0219)
#define SPAPI_E_NO_DEFAULT_DEVICE_INTERFACE                _HRESULT_TYPEDEF_(0x800F021A)
#define SPAPI_E_DEVICE_INTERFACE_ACTIVE                    _HRESULT_TYPEDEF_(0x800F021B)
#define SPAPI_E_DEVICE_INTERFACE_REMOVED                   _HRESULT_TYPEDEF_(0x800F021C)
#define SPAPI_E_BAD_INTERFACE_INSTALLSECT                  _HRESULT_TYPEDEF_(0x800F021D)
#define SPAPI_E_NO_SUCH_INTERFACE_CLASS                    _HRESULT_TYPEDEF_(0x800F021E)
#define SPAPI_E_INVALID_REFERENCE_STRING                   _HRESULT_TYPEDEF_(0x800F021F)
#define SPAPI_E_INVALID_MACHINENAME                        _HRESULT_TYPEDEF_(0x800F0220)
#define SPAPI_E_REMOTE_COMM_FAILURE                        _HRESULT_TYPEDEF_(0x800F0221)
#define SPAPI_E_MACHINE_UNAVAILABLE                        _HRESULT_TYPEDEF_(0x800F0222)
#define SPAPI_E_NO_CONFIGMGR_SERVICES                      _HRESULT_TYPEDEF_(0x800F0223)
#define SPAPI_E_INVALID_PROPPAGE_PROVIDER                  _HRESULT_TYPEDEF_(0x800F0224)
#define SPAPI_E_NO_SUCH_DEVICE_INTERFACE                   _HRESULT_TYPEDEF_(0x800F0225)
#define SPAPI_E_DI_POSTPROCESSING_REQUIRED                 _HRESULT_TYPEDEF_(0x800F0226)
#define SPAPI_E_INVALID_COINSTALLER                        _HRESULT_TYPEDEF_(0x800F0227)
#define SPAPI_E_NO_COMPAT_DRIVERS                          _HRESULT_TYPEDEF_(0x800F0228)
#define SPAPI_E_NO_DEVICE_ICON                             _HRESULT_TYPEDEF_(0x800F0229)
#define SPAPI_E_INVALID_INF_LOGCONFIG                      _HRESULT_TYPEDEF_(0x800F022A)
#define SPAPI_E_DI_DONT_INSTALL                            _HRESULT_TYPEDEF_(0x800F022B)
#define SPAPI_E_INVALID_FILTER_DRIVER                      _HRESULT_TYPEDEF_(0x800F022C)
#define SPAPI_E_NON_WINDOWS_NT_DRIVER                      _HRESULT_TYPEDEF_(0x800F022D)
#define SPAPI_E_NON_WINDOWS_DRIVER                         _HRESULT_TYPEDEF_(0x800F022E)
#define SPAPI_E_NO_CATALOG_FOR_OEM_INF                     _HRESULT_TYPEDEF_(0x800F022F)
#define SPAPI_E_DEVINSTALL_QUEUE_NONNATIVE                 _HRESULT_TYPEDEF_(0x800F0230)
#define SPAPI_E_NOT_DISABLEABLE                            _HRESULT_TYPEDEF_(0x800F0231)
#define SPAPI_E_CANT_REMOVE_DEVINST                        _HRESULT_TYPEDEF_(0x800F0232)
#define SPAPI_E_INVALID_TARGET                             _HRESULT_TYPEDEF_(0x800F0233)
#define SPAPI_E_DRIVER_NONNATIVE                           _HRESULT_TYPEDEF_(0x800F0234)
#define SPAPI_E_IN_WOW64                                   _HRESULT_TYPEDEF_(0x800F0235)
#define SPAPI_E_SET_SYSTEM_RESTORE_POINT                   _HRESULT_TYPEDEF_(0x800F0236)
#define SPAPI_E_INCORRECTLY_COPIED_INF                     _HRESULT_TYPEDEF_(0x800F0237)
#define SPAPI_E_SCE_DISABLED                               _HRESULT_TYPEDEF_(0x800F0238)
#define SPAPI_E_ERROR_NOT_INSTALLED                        _HRESULT_TYPEDEF_(0x800F1000)

/* Smart card management error codes */
#define SCARD_S_SUCCESS                                    NO_ERROR
#define SCARD_F_INTERNAL_ERROR                             _HRESULT_TYPEDEF_(0x80100001)
#define SCARD_E_CANCELLED                                  _HRESULT_TYPEDEF_(0x80100002)
#define SCARD_E_INVALID_HANDLE                             _HRESULT_TYPEDEF_(0x80100003)
#define SCARD_E_INVALID_PARAMETER                          _HRESULT_TYPEDEF_(0x80100004)
#define SCARD_E_INVALID_TARGET                             _HRESULT_TYPEDEF_(0x80100005)
#define SCARD_E_NO_MEMORY                                  _HRESULT_TYPEDEF_(0x80100006)
#define SCARD_F_WAITED_TOO_LONG                            _HRESULT_TYPEDEF_(0x80100007)
#define SCARD_E_INSUFFICIENT_BUFFER                        _HRESULT_TYPEDEF_(0x80100008)
#define SCARD_E_UNKNOWN_READER                             _HRESULT_TYPEDEF_(0x80100009)
#define SCARD_E_TIMEOUT                                    _HRESULT_TYPEDEF_(0x8010000A)
#define SCARD_E_SHARING_VIOLATION                          _HRESULT_TYPEDEF_(0x8010000B)
#define SCARD_E_NO_SMARTCARD                               _HRESULT_TYPEDEF_(0x8010000C)
#define SCARD_E_UNKNOWN_CARD                               _HRESULT_TYPEDEF_(0x8010000D)
#define SCARD_E_CANT_DISPOSE                               _HRESULT_TYPEDEF_(0x8010000E)
#define SCARD_E_PROTO_MISMATCH                             _HRESULT_TYPEDEF_(0x8010000F)
#define SCARD_E_NOT_READY                                  _HRESULT_TYPEDEF_(0x80100010)
#define SCARD_E_INVALID_VALUE                              _HRESULT_TYPEDEF_(0x80100011)
#define SCARD_E_SYSTEM_CANCELLED                           _HRESULT_TYPEDEF_(0x80100012)
#define SCARD_F_COMM_ERROR                                 _HRESULT_TYPEDEF_(0x80100013)
#define SCARD_F_UNKNOWN_ERROR                              _HRESULT_TYPEDEF_(0x80100014)
#define SCARD_E_INVALID_ATR                                _HRESULT_TYPEDEF_(0x80100015)
#define SCARD_E_NOT_TRANSACTED                             _HRESULT_TYPEDEF_(0x80100016)
#define SCARD_E_READER_UNAVAILABLE                         _HRESULT_TYPEDEF_(0x80100017)
#define SCARD_P_SHUTDOWN                                   _HRESULT_TYPEDEF_(0x80100018)
#define SCARD_E_PCI_TOO_SMALL                              _HRESULT_TYPEDEF_(0x80100019)
#define SCARD_E_READER_UNSUPPORTED                         _HRESULT_TYPEDEF_(0x8010001A)
#define SCARD_E_DUPLICATE_READER                           _HRESULT_TYPEDEF_(0x8010001B)
#define SCARD_E_CARD_UNSUPPORTED                           _HRESULT_TYPEDEF_(0x8010001C)
#define SCARD_E_NO_SERVICE                                 _HRESULT_TYPEDEF_(0x8010001D)
#define SCARD_E_SERVICE_STOPPED                            _HRESULT_TYPEDEF_(0x8010001E)
#define SCARD_E_UNEXPECTED                                 _HRESULT_TYPEDEF_(0x8010001F)
#define SCARD_E_ICC_INSTALLATION                           _HRESULT_TYPEDEF_(0x80100020)
#define SCARD_E_ICC_CREATEORDER                            _HRESULT_TYPEDEF_(0x80100021)
#define SCARD_E_UNSUPPORTED_FEATURE                        _HRESULT_TYPEDEF_(0x80100022)
#define SCARD_E_DIR_NOT_FOUND                              _HRESULT_TYPEDEF_(0x80100023)
#define SCARD_E_FILE_NOT_FOUND                             _HRESULT_TYPEDEF_(0x80100024)
#define SCARD_E_NO_DIR                                     _HRESULT_TYPEDEF_(0x80100025)
#define SCARD_E_NO_FILE                                    _HRESULT_TYPEDEF_(0x80100026)
#define SCARD_E_NO_ACCESS                                  _HRESULT_TYPEDEF_(0x80100027)
#define SCARD_E_WRITE_TOO_MANY                             _HRESULT_TYPEDEF_(0x80100028)
#define SCARD_E_BAD_SEEK                                   _HRESULT_TYPEDEF_(0x80100029)
#define SCARD_E_INVALID_CHV                                _HRESULT_TYPEDEF_(0x8010002A)
#define SCARD_E_UNKNOWN_RES_MNG                            _HRESULT_TYPEDEF_(0x8010002B)
#define SCARD_E_NO_SUCH_CERTIFICATE                        _HRESULT_TYPEDEF_(0x8010002C)
#define SCARD_E_CERTIFICATE_UNAVAILABLE                    _HRESULT_TYPEDEF_(0x8010002D)
#define SCARD_E_NO_READERS_AVAILABLE                       _HRESULT_TYPEDEF_(0x8010002E)
#define SCARD_E_COMM_DATA_LOST                             _HRESULT_TYPEDEF_(0x8010002F)
#define SCARD_E_NO_KEY_CONTAINER                           _HRESULT_TYPEDEF_(0x80100030)
#define SCARD_E_SERVER_TOO_BUSY                            _HRESULT_TYPEDEF_(0x80100031)
#define SCARD_W_UNSUPPORTED_CARD                           _HRESULT_TYPEDEF_(0x80100065)
#define SCARD_W_UNRESPONSIVE_CARD                          _HRESULT_TYPEDEF_(0x80100066)
#define SCARD_W_UNPOWERED_CARD                             _HRESULT_TYPEDEF_(0x80100067)
#define SCARD_W_RESET_CARD                                 _HRESULT_TYPEDEF_(0x80100068)
#define SCARD_W_REMOVED_CARD                               _HRESULT_TYPEDEF_(0x80100069)
#define SCARD_W_SECURITY_VIOLATION                         _HRESULT_TYPEDEF_(0x8010006A)
#define SCARD_W_WRONG_CHV                                  _HRESULT_TYPEDEF_(0x8010006B)
#define SCARD_W_CHV_BLOCKED                                _HRESULT_TYPEDEF_(0x8010006C)
#define SCARD_W_EOF                                        _HRESULT_TYPEDEF_(0x8010006D)
#define SCARD_W_CANCELLED_BY_USER                          _HRESULT_TYPEDEF_(0x8010006E)
#define SCARD_W_CARD_NOT_AUTHENTICATED                     _HRESULT_TYPEDEF_(0x8010006F)
#define SCARD_W_CACHE_ITEM_NOT_FOUND                       _HRESULT_TYPEDEF_(0x80100070)
#define SCARD_W_CACHE_ITEM_STALE                           _HRESULT_TYPEDEF_(0x80100071)
#define SCARD_W_CACHE_ITEM_TOO_BIG                         _HRESULT_TYPEDEF_(0x80100072)

#define WININET_E_OUT_OF_HANDLES                           _HRESULT_TYPEDEF_(0x80072ee1)
#define WININET_E_TIMEOUT                                  _HRESULT_TYPEDEF_(0x80072ee2)
#define WININET_E_EXTENDED_ERROR                           _HRESULT_TYPEDEF_(0x80072ee3)
#define WININET_E_INTERNAL_ERROR                           _HRESULT_TYPEDEF_(0x80072ee4)
#define WININET_E_INVALID_URL                              _HRESULT_TYPEDEF_(0x80072ee5)
#define WININET_E_UNRECOGNIZED_SCHEME                      _HRESULT_TYPEDEF_(0x80072ee6)
#define WININET_E_NAME_NOT_RESOLVED                        _HRESULT_TYPEDEF_(0x80072ee7)
#define WININET_E_PROTOCOL_NOT_FOUND                       _HRESULT_TYPEDEF_(0x80072ee8)
#define WININET_E_INVALID_OPTION                           _HRESULT_TYPEDEF_(0x80072ee9)
#define WININET_E_BAD_OPTION_LENGTH                        _HRESULT_TYPEDEF_(0x80072eea)
#define WININET_E_OPTION_NOT_SETTABLE                      _HRESULT_TYPEDEF_(0x80072eeb)
#define WININET_E_SHUTDOWN                                 _HRESULT_TYPEDEF_(0x80072eec)
#define WININET_E_INCORRECT_USER_NAME                      _HRESULT_TYPEDEF_(0x80072eed)
#define WININET_E_INCORRECT_PASSWORD                       _HRESULT_TYPEDEF_(0x80072eee)
#define WININET_E_LOGIN_FAILURE                            _HRESULT_TYPEDEF_(0x80072eef)
#define WININET_E_INVALID_OPERATION                        _HRESULT_TYPEDEF_(0x80072ef0)
#define WININET_E_OPERATION_CANCELLED                      _HRESULT_TYPEDEF_(0x80072ef1)
#define WININET_E_INCORRECT_HANDLE_TYPE                    _HRESULT_TYPEDEF_(0x80072ef2)
#define WININET_E_INCORRECT_HANDLE_STATE                   _HRESULT_TYPEDEF_(0x80072ef3)
#define WININET_E_NOT_PROXY_REQUEST                        _HRESULT_TYPEDEF_(0x80072ef4)
#define WININET_E_REGISTRY_VALUE_NOT_FOUND                 _HRESULT_TYPEDEF_(0x80072ef5)
#define WININET_E_BAD_REGISTRY_PARAMETER                   _HRESULT_TYPEDEF_(0x80072ef6)
#define WININET_E_NO_DIRECT_ACCESS                         _HRESULT_TYPEDEF_(0x80072ef7)
#define WININET_E_NO_CONTEXT                               _HRESULT_TYPEDEF_(0x80072ef8)
#define WININET_E_NO_CALLBACK                              _HRESULT_TYPEDEF_(0x80072ef9)
#define WININET_E_REQUEST_PENDING                          _HRESULT_TYPEDEF_(0x80072efa)
#define WININET_E_INCORRECT_FORMAT                         _HRESULT_TYPEDEF_(0x80072efb)
#define WININET_E_ITEM_NOT_FOUND                           _HRESULT_TYPEDEF_(0x80072efc)
#define WININET_E_CANNOT_CONNECT                           _HRESULT_TYPEDEF_(0x80072efd)
#define WININET_E_CONNECTION_ABORTED                       _HRESULT_TYPEDEF_(0x80072efe)
#define WININET_E_CONNECTION_RESET                         _HRESULT_TYPEDEF_(0x80072eff)
#define WININET_E_FORCE_RETRY                              _HRESULT_TYPEDEF_(0x80072f00)
#define WININET_E_INVALID_PROXY_REQUEST                    _HRESULT_TYPEDEF_(0x80072f01)
#define WININET_E_NEED_UI                                  _HRESULT_TYPEDEF_(0x80072f02)
#define WININET_E_HANDLE_EXISTS                            _HRESULT_TYPEDEF_(0x80072f04)
#define WININET_E_SEC_CERT_DATE_INVALID                    _HRESULT_TYPEDEF_(0x80072f05)
#define WININET_E_SEC_CERT_CN_INVALID                      _HRESULT_TYPEDEF_(0x80072f06)
#define WININET_E_HTTP_TO_HTTPS_ON_REDIR                   _HRESULT_TYPEDEF_(0x80072f07)
#define WININET_E_HTTPS_TO_HTTP_ON_REDIR                   _HRESULT_TYPEDEF_(0x80072f08)
#define WININET_E_MIXED_SECURITY                           _HRESULT_TYPEDEF_(0x80072f09)
#define WININET_E_CHG_POST_IS_NON_SECURE                   _HRESULT_TYPEDEF_(0x80072f0a)
#define WININET_E_POST_IS_NON_SECURE                       _HRESULT_TYPEDEF_(0x80072f0b)
#define WININET_E_CLIENT_AUTH_CERT_NEEDED                  _HRESULT_TYPEDEF_(0x80072f0c)
#define WININET_E_INVALID_CA                               _HRESULT_TYPEDEF_(0x80072f0d)
#define WININET_E_CLIENT_AUTH_NOT_SETUP                    _HRESULT_TYPEDEF_(0x80072f0e)
#define WININET_E_ASYNC_THREAD_FAILED                      _HRESULT_TYPEDEF_(0x80072f0f)
#define WININET_E_REDIRECT_SCHEME_CHANGE                   _HRESULT_TYPEDEF_(0x80072f10)
#define WININET_E_DIALOG_PENDING                           _HRESULT_TYPEDEF_(0x80072f11)
#define WININET_E_RETRY_DIALOG                             _HRESULT_TYPEDEF_(0x80072f12)
#define WININET_E_NO_NEW_CONTAINERS                        _HRESULT_TYPEDEF_(0x80072f13)
#define WININET_E_HTTPS_HTTP_SUBMIT_REDIR                  _HRESULT_TYPEDEF_(0x80072f14)
#define WININET_E_SEC_CERT_ERRORS                          _HRESULT_TYPEDEF_(0x80072f17)
#define WININET_E_SEC_CERT_REV_FAILED                      _HRESULT_TYPEDEF_(0x80072f19)
#define WININET_E_HEADER_NOT_FOUND                         _HRESULT_TYPEDEF_(0x80072f76)
#define WININET_E_DOWNLEVEL_SERVER                         _HRESULT_TYPEDEF_(0x80072f77)
#define WININET_E_INVALID_SERVER_RESPONSE                  _HRESULT_TYPEDEF_(0x80072f78)
#define WININET_E_INVALID_HEADER                           _HRESULT_TYPEDEF_(0x80072f79)
#define WININET_E_INVALID_QUERY_REQUEST                    _HRESULT_TYPEDEF_(0x80072f7a)
#define WININET_E_HEADER_ALREADY_EXISTS                    _HRESULT_TYPEDEF_(0x80072f7b)
#define WININET_E_REDIRECT_FAILED                          _HRESULT_TYPEDEF_(0x80072f7c)
#define WININET_E_SECURITY_CHANNEL_ERROR                   _HRESULT_TYPEDEF_(0x80072f7d)
#define WININET_E_UNABLE_TO_CACHE_FILE                     _HRESULT_TYPEDEF_(0x80072f7e)
#define WININET_E_TCPIP_NOT_INSTALLED                      _HRESULT_TYPEDEF_(0x80072f7f)
#define WININET_E_NOT_REDIRECTED                           _HRESULT_TYPEDEF_(0x80072f80)
#define WININET_E_COOKIE_NEEDS_CONFIRMATION                _HRESULT_TYPEDEF_(0x80072f81)
#define WININET_E_COOKIE_DECLINED                          _HRESULT_TYPEDEF_(0x80072f82)
#define WININET_E_DISCONNECTED                             _HRESULT_TYPEDEF_(0x80072f83)
#define WININET_E_SERVER_UNREACHABLE                       _HRESULT_TYPEDEF_(0x80072f84)
#define WININET_E_PROXY_SERVER_UNREACHABLE                 _HRESULT_TYPEDEF_(0x80072f85)
#define WININET_E_BAD_AUTO_PROXY_SCRIPT                    _HRESULT_TYPEDEF_(0x80072f86)
#define WININET_E_UNABLE_TO_DOWNLOAD_SCRIPT                _HRESULT_TYPEDEF_(0x80072f87)
#define WININET_E_REDIRECT_NEEDS_CONFIRMATION              _HRESULT_TYPEDEF_(0x80072f88)
#define WININET_E_SEC_INVALID_CERT                         _HRESULT_TYPEDEF_(0x80072f89)
#define WININET_E_SEC_CERT_REVOKED                         _HRESULT_TYPEDEF_(0x80072f8a)
#define WININET_E_FAILED_DUETOSECURITYCHECK                _HRESULT_TYPEDEF_(0x80072f8b)
#define WININET_E_NOT_INITIALIZED                          _HRESULT_TYPEDEF_(0x80072f8c)
#define WININET_E_LOGIN_FAILURE_DISPLAY_ENTITY_BODY        _HRESULT_TYPEDEF_(0x80072f8e)
#define WININET_E_DECODING_FAILED                          _HRESULT_TYPEDEF_(0x80072f8f)

#define ERROR_HUNG_DISPLAY_DRIVER_THREAD                   _HRESULT_TYPEDEF_(0x80260001)
#define DWM_E_COMPOSITIONDISABLED                          _HRESULT_TYPEDEF_(0x80263001)
#define DWM_E_REMOTING_NOT_SUPPORTED                       _HRESULT_TYPEDEF_(0x80263002)
#define DWM_E_NO_REDIRECTION_SURFACE_AVAILABLE             _HRESULT_TYPEDEF_(0x80263003)
#define DWM_E_NOT_QUEUING_PRESENTS                         _HRESULT_TYPEDEF_(0x80263004)
#define DWM_E_ADAPTER_NOT_FOUND                            _HRESULT_TYPEDEF_(0x80263005)
#define DWM_S_GDI_REDIRECTION_SURFACE                      _HRESULT_TYPEDEF_(0x00263005)

#define TBS_E_INTERNAL_ERROR                               _HRESULT_TYPEDEF_(0x80284001)
#define TBS_E_BAD_PARAMETER                                _HRESULT_TYPEDEF_(0x80284002)
#define TBS_E_INVALID_OUTPUT_POINTER                       _HRESULT_TYPEDEF_(0x80284003)
#define TBS_E_INVALID_CONTEXT                              _HRESULT_TYPEDEF_(0x80284004)
#define TBS_E_INSUFFICIENT_BUFFER                          _HRESULT_TYPEDEF_(0x80284005)
#define TBS_E_IOERROR                                      _HRESULT_TYPEDEF_(0x80284006)
#define TBS_E_INVALID_CONTEXT_PARAM                        _HRESULT_TYPEDEF_(0x80284007)
#define TBS_E_SERVICE_NOT_RUNNING                          _HRESULT_TYPEDEF_(0x80284008)
#define TBS_E_TOO_MANY_TBS_CONTEXTS                        _HRESULT_TYPEDEF_(0x80284009)
#define TBS_E_TOO_MANY_RESOURCES                           _HRESULT_TYPEDEF_(0x8028400a)
#define TBS_E_SERVICE_START_PENDING                        _HRESULT_TYPEDEF_(0x8028400b)
#define TBS_E_PPI_NOT_SUPPORTED                            _HRESULT_TYPEDEF_(0x8028400c)
#define TBS_E_COMMAND_CANCELED                             _HRESULT_TYPEDEF_(0x8028400d)
#define TBS_E_BUFFER_TOO_LARGE                             _HRESULT_TYPEDEF_(0x8028400e)
#define TBS_E_TPM_NOT_FOUND                                _HRESULT_TYPEDEF_(0x8028400f)
#define TBS_E_SERVICE_DISABLED                             _HRESULT_TYPEDEF_(0x80284010)
#define TBS_E_NO_EVENT_LOG                                 _HRESULT_TYPEDEF_(0x80284011)
#define TBS_E_ACCESS_DENIED                                _HRESULT_TYPEDEF_(0x80284012)
#define TBS_E_PROVISIONING_NOT_ALLOWED                     _HRESULT_TYPEDEF_(0x80284013)
#define TBS_E_PPI_FUNCTION_UNSUPPORTED                     _HRESULT_TYPEDEF_(0x80284014)
#define TBS_E_OWNERAUTH_NOT_FOUND                          _HRESULT_TYPEDEF_(0x80284015)
#define TBS_E_PROVISIONING_INCOMPLETE                      _HRESULT_TYPEDEF_(0x80284016)

#define UI_E_CREATE_FAILED                                 _HRESULT_TYPEDEF_(0x802a0001)
#define UI_E_SHUTDOWN_CALLED                               _HRESULT_TYPEDEF_(0x802a0002)
#define UI_E_ILLEGAL_REENTRANCY                            _HRESULT_TYPEDEF_(0x802a0003)
#define UI_E_OBJECT_SEALED                                 _HRESULT_TYPEDEF_(0x802a0004)
#define UI_E_VALUE_NOT_SET                                 _HRESULT_TYPEDEF_(0x802a0005)
#define UI_E_VALUE_NOT_DETERMINED                          _HRESULT_TYPEDEF_(0x802a0006)
#define UI_E_INVALID_OUTPUT                                _HRESULT_TYPEDEF_(0x802a0007)
#define UI_E_BOOLEAN_EXPECTED                              _HRESULT_TYPEDEF_(0x802a0008)
#define UI_E_DIFFERENT_OWNER                               _HRESULT_TYPEDEF_(0x802a0009)
#define UI_E_AMBIGUOUS_MATCH                               _HRESULT_TYPEDEF_(0x802a000a)
#define UI_E_FP_OVERFLOW                                   _HRESULT_TYPEDEF_(0x802a000b)
#define UI_E_WRONG_THREAD                                  _HRESULT_TYPEDEF_(0x802a000c)
#define UI_E_STORYBOARD_ACTIVE                             _HRESULT_TYPEDEF_(0x802a0101)
#define UI_E_STORYBOARD_NOT_PLAYING                        _HRESULT_TYPEDEF_(0x802a0102)
#define UI_E_START_KEYFRAME_AFTER_END                      _HRESULT_TYPEDEF_(0x802a0103)
#define UI_E_END_KEYFRAME_NOT_DETERMINED                   _HRESULT_TYPEDEF_(0x802a0104)
#define UI_E_LOOPS_OVERLAP                                 _HRESULT_TYPEDEF_(0x802a0105)
#define UI_E_TRANSITION_ALREADY_USED                       _HRESULT_TYPEDEF_(0x802a0106)
#define UI_E_TRANSITION_NOT_IN_STORYBOARD                  _HRESULT_TYPEDEF_(0x802a0107)
#define UI_E_TRANSITION_ECLIPSED                           _HRESULT_TYPEDEF_(0x802a0108)
#define UI_E_TIME_BEFORE_LAST_UPDATE                       _HRESULT_TYPEDEF_(0x802a0109)
#define UI_E_TIMER_CLIENT_ALREADY_CONNECTED                _HRESULT_TYPEDEF_(0x802a010a)
#define UI_E_INVALID_DIMENSION                             _HRESULT_TYPEDEF_(0x802a010b)
#define UI_E_PRIMITIVE_OUT_OF_BOUNDS                       _HRESULT_TYPEDEF_(0x802a010c)
#define UI_E_WINDOW_CLOSED                                 _HRESULT_TYPEDEF_(0x802a0201)

#define ERROR_VOLMGR_INCOMPLETE_REGENERATION               _NDIS_ERROR_TYPEDEF_(0x80380001)
#define ERROR_VOLMGR_INCOMPLETE_DISK_MIGRATION             _NDIS_ERROR_TYPEDEF_(0x80380002)
#define ERROR_VOLMGR_DATABASE_FULL                         _NDIS_ERROR_TYPEDEF_(0xC0380001)
#define ERROR_VOLMGR_DISK_CONFIGURATION_CORRUPTED          _NDIS_ERROR_TYPEDEF_(0xC0380002)
#define ERROR_VOLMGR_DISK_CONFIGURATION_NOT_IN_SYNC        _NDIS_ERROR_TYPEDEF_(0xC0380003)
#define ERROR_VOLMGR_PACK_CONFIG_UPDATE_FAILED             _NDIS_ERROR_TYPEDEF_(0xC0380004)
#define ERROR_VOLMGR_DISK_CONTAINS_NON_SIMPLE_VOLUME       _NDIS_ERROR_TYPEDEF_(0xC0380005)
#define ERROR_VOLMGR_DISK_DUPLICATE                        _NDIS_ERROR_TYPEDEF_(0xC0380006)
#define ERROR_VOLMGR_DISK_DYNAMIC                          _NDIS_ERROR_TYPEDEF_(0xC0380007)
#define ERROR_VOLMGR_DISK_ID_INVALID                       _NDIS_ERROR_TYPEDEF_(0xC0380008)
#define ERROR_VOLMGR_DISK_INVALID                          _NDIS_ERROR_TYPEDEF_(0xC0380009)
#define ERROR_VOLMGR_DISK_LAST_VOTER                       _NDIS_ERROR_TYPEDEF_(0xC038000A)
#define ERROR_VOLMGR_DISK_LAYOUT_INVALID                   _NDIS_ERROR_TYPEDEF_(0xC038000B)
#define ERROR_VOLMGR_DISK_LAYOUT_NON_BASIC_BETWEEN_BASIC_PARTITIONS _NDIS_ERROR_TYPEDEF_(0xC038000C)
#define ERROR_VOLMGR_DISK_LAYOUT_NOT_CYLINDER_ALIGNED      _NDIS_ERROR_TYPEDEF_(0xC038000D)
#define ERROR_VOLMGR_DISK_LAYOUT_PARTITIONS_TOO_SMALL      _NDIS_ERROR_TYPEDEF_(0xC038000E)
#define ERROR_VOLMGR_DISK_LAYOUT_PRIMARY_BETWEEN_LOGICAL_PARTITIONS _NDIS_ERROR_TYPEDEF_(0xC038000F)
#define ERROR_VOLMGR_DISK_LAYOUT_TOO_MANY_PARTITIONS       _NDIS_ERROR_TYPEDEF_(0xC0380010)
#define ERROR_VOLMGR_DISK_MISSING                          _NDIS_ERROR_TYPEDEF_(0xC0380011)
#define ERROR_VOLMGR_DISK_NOT_EMPTY                        _NDIS_ERROR_TYPEDEF_(0xC0380012)
#define ERROR_VOLMGR_DISK_NOT_ENOUGH_SPACE                 _NDIS_ERROR_TYPEDEF_(0xC0380013)
#define ERROR_VOLMGR_DISK_REVECTORING_FAILED               _NDIS_ERROR_TYPEDEF_(0xC0380014)
#define ERROR_VOLMGR_DISK_SECTOR_SIZE_INVALID              _NDIS_ERROR_TYPEDEF_(0xC0380015)
#define ERROR_VOLMGR_DISK_SET_NOT_CONTAINED                _NDIS_ERROR_TYPEDEF_(0xC0380016)
#define ERROR_VOLMGR_DISK_USED_BY_MULTIPLE_MEMBERS         _NDIS_ERROR_TYPEDEF_(0xC0380017)
#define ERROR_VOLMGR_DISK_USED_BY_MULTIPLE_PLEXES          _NDIS_ERROR_TYPEDEF_(0xC0380018)
#define ERROR_VOLMGR_DYNAMIC_DISK_NOT_SUPPORTED            _NDIS_ERROR_TYPEDEF_(0xC0380019)
#define ERROR_VOLMGR_EXTENT_ALREADY_USED                   _NDIS_ERROR_TYPEDEF_(0xC038001A)
#define ERROR_VOLMGR_EXTENT_NOT_CONTIGUOUS                 _NDIS_ERROR_TYPEDEF_(0xC038001B)
#define ERROR_VOLMGR_EXTENT_NOT_IN_PUBLIC_REGION           _NDIS_ERROR_TYPEDEF_(0xC038001C)
#define ERROR_VOLMGR_EXTENT_NOT_SECTOR_ALIGNED             _NDIS_ERROR_TYPEDEF_(0xC038001D)
#define ERROR_VOLMGR_EXTENT_OVERLAPS_EBR_PARTITION         _NDIS_ERROR_TYPEDEF_(0xC038001E)
#define ERROR_VOLMGR_EXTENT_VOLUME_LENGTHS_DO_NOT_MATCH    _NDIS_ERROR_TYPEDEF_(0xC038001F)
#define ERROR_VOLMGR_FAULT_TOLERANT_NOT_SUPPORTED          _NDIS_ERROR_TYPEDEF_(0xC0380020)
#define ERROR_VOLMGR_INTERLEAVE_LENGTH_INVALID             _NDIS_ERROR_TYPEDEF_(0xC0380021)
#define ERROR_VOLMGR_MAXIMUM_REGISTERED_USERS              _NDIS_ERROR_TYPEDEF_(0xC0380022)
#define ERROR_VOLMGR_MEMBER_IN_SYNC                        _NDIS_ERROR_TYPEDEF_(0xC0380023)
#define ERROR_VOLMGR_MEMBER_INDEX_DUPLICATE                _NDIS_ERROR_TYPEDEF_(0xC0380024)
#define ERROR_VOLMGR_MEMBER_INDEX_INVALID                  _NDIS_ERROR_TYPEDEF_(0xC0380025)
#define ERROR_VOLMGR_MEMBER_MISSING                        _NDIS_ERROR_TYPEDEF_(0xC0380026)
#define ERROR_VOLMGR_MEMBER_NOT_DETACHED                   _NDIS_ERROR_TYPEDEF_(0xC0380027)
#define ERROR_VOLMGR_MEMBER_REGENERATING                   _NDIS_ERROR_TYPEDEF_(0xC0380028)
#define ERROR_VOLMGR_ALL_DISKS_FAILED                      _NDIS_ERROR_TYPEDEF_(0xC0380029)
#define ERROR_VOLMGR_NO_REGISTERED_USERS                   _NDIS_ERROR_TYPEDEF_(0xC038002A)
#define ERROR_VOLMGR_NO_SUCH_USER                          _NDIS_ERROR_TYPEDEF_(0xC038002B)
#define ERROR_VOLMGR_NOTIFICATION_RESET                    _NDIS_ERROR_TYPEDEF_(0xC038002C)
#define ERROR_VOLMGR_NUMBER_OF_MEMBERS_INVALID             _NDIS_ERROR_TYPEDEF_(0xC038002D)
#define ERROR_VOLMGR_NUMBER_OF_PLEXES_INVALID              _NDIS_ERROR_TYPEDEF_(0xC038002E)
#define ERROR_VOLMGR_PACK_DUPLICATE                        _NDIS_ERROR_TYPEDEF_(0xC038002F)
#define ERROR_VOLMGR_PACK_ID_INVALID                       _NDIS_ERROR_TYPEDEF_(0xC0380030)
#define ERROR_VOLMGR_PACK_INVALID                          _NDIS_ERROR_TYPEDEF_(0xC0380031)
#define ERROR_VOLMGR_PACK_NAME_INVALID                     _NDIS_ERROR_TYPEDEF_(0xC0380032)
#define ERROR_VOLMGR_PACK_OFFLINE                          _NDIS_ERROR_TYPEDEF_(0xC0380033)
#define ERROR_VOLMGR_PACK_HAS_QUORUM                       _NDIS_ERROR_TYPEDEF_(0xC0380034)
#define ERROR_VOLMGR_PACK_WITHOUT_QUORUM                   _NDIS_ERROR_TYPEDEF_(0xC0380035)
#define ERROR_VOLMGR_PARTITION_STYLE_INVALID               _NDIS_ERROR_TYPEDEF_(0xC0380036)
#define ERROR_VOLMGR_PARTITION_UPDATE_FAILED               _NDIS_ERROR_TYPEDEF_(0xC0380037)
#define ERROR_VOLMGR_PLEX_IN_SYNC                          _NDIS_ERROR_TYPEDEF_(0xC0380038)
#define ERROR_VOLMGR_PLEX_INDEX_DUPLICATE                  _NDIS_ERROR_TYPEDEF_(0xC0380039)
#define ERROR_VOLMGR_PLEX_INDEX_INVALID                    _NDIS_ERROR_TYPEDEF_(0xC038003A)
#define ERROR_VOLMGR_PLEX_LAST_ACTIVE                      _NDIS_ERROR_TYPEDEF_(0xC038003B)
#define ERROR_VOLMGR_PLEX_MISSING                          _NDIS_ERROR_TYPEDEF_(0xC038003C)
#define ERROR_VOLMGR_PLEX_REGENERATING                     _NDIS_ERROR_TYPEDEF_(0xC038003D)
#define ERROR_VOLMGR_PLEX_TYPE_INVALID                     _NDIS_ERROR_TYPEDEF_(0xC038003E)
#define ERROR_VOLMGR_PLEX_NOT_RAID5                        _NDIS_ERROR_TYPEDEF_(0xC038003F)
#define ERROR_VOLMGR_PLEX_NOT_SIMPLE                       _NDIS_ERROR_TYPEDEF_(0xC0380040)
#define ERROR_VOLMGR_STRUCTURE_SIZE_INVALID                _NDIS_ERROR_TYPEDEF_(0xC0380041)
#define ERROR_VOLMGR_TOO_MANY_NOTIFICATION_REQUESTS        _NDIS_ERROR_TYPEDEF_(0xC0380042)
#define ERROR_VOLMGR_TRANSACTION_IN_PROGRESS               _NDIS_ERROR_TYPEDEF_(0xC0380043)
#define ERROR_VOLMGR_UNEXPECTED_DISK_LAYOUT_CHANGE         _NDIS_ERROR_TYPEDEF_(0xC0380044)
#define ERROR_VOLMGR_VOLUME_CONTAINS_MISSING_DISK          _NDIS_ERROR_TYPEDEF_(0xC0380045)
#define ERROR_VOLMGR_VOLUME_ID_INVALID                     _NDIS_ERROR_TYPEDEF_(0xC0380046)
#define ERROR_VOLMGR_VOLUME_LENGTH_INVALID                 _NDIS_ERROR_TYPEDEF_(0xC0380047)
#define ERROR_VOLMGR_VOLUME_LENGTH_NOT_SECTOR_SIZE_MULTIPLE _NDIS_ERROR_TYPEDEF_(0xC0380048)
#define ERROR_VOLMGR_VOLUME_NOT_MIRRORED                   _NDIS_ERROR_TYPEDEF_(0xC0380049)
#define ERROR_VOLMGR_VOLUME_NOT_RETAINED                   _NDIS_ERROR_TYPEDEF_(0xC038004A)
#define ERROR_VOLMGR_VOLUME_OFFLINE                        _NDIS_ERROR_TYPEDEF_(0xC038004B)
#define ERROR_VOLMGR_VOLUME_RETAINED                       _NDIS_ERROR_TYPEDEF_(0xC038004C)
#define ERROR_VOLMGR_NUMBER_OF_EXTENTS_INVALID             _NDIS_ERROR_TYPEDEF_(0xC038004D)
#define ERROR_VOLMGR_DIFFERENT_SECTOR_SIZE                 _NDIS_ERROR_TYPEDEF_(0xC038004E)
#define ERROR_VOLMGR_BAD_BOOT_DISK                         _NDIS_ERROR_TYPEDEF_(0xC038004F)
#define ERROR_VOLMGR_PACK_CONFIG_OFFLINE                   _NDIS_ERROR_TYPEDEF_(0xC0380050)
#define ERROR_VOLMGR_PACK_CONFIG_ONLINE                    _NDIS_ERROR_TYPEDEF_(0xC0380051)
#define ERROR_VOLMGR_NOT_PRIMARY_PACK                      _NDIS_ERROR_TYPEDEF_(0xC0380052)
#define ERROR_VOLMGR_PACK_LOG_UPDATE_FAILED                _NDIS_ERROR_TYPEDEF_(0xC0380053)
#define ERROR_VOLMGR_NUMBER_OF_DISKS_IN_PLEX_INVALID       _NDIS_ERROR_TYPEDEF_(0xC0380054)
#define ERROR_VOLMGR_NUMBER_OF_DISKS_IN_MEMBER_INVALID     _NDIS_ERROR_TYPEDEF_(0xC0380055)
#define ERROR_VOLMGR_VOLUME_MIRRORED                       _NDIS_ERROR_TYPEDEF_(0xC0380056)
#define ERROR_VOLMGR_PLEX_NOT_SIMPLE_SPANNED               _NDIS_ERROR_TYPEDEF_(0xC0380057)
#define ERROR_VOLMGR_NO_VALID_LOG_COPIES                   _NDIS_ERROR_TYPEDEF_(0xC0380058)
#define ERROR_VOLMGR_PRIMARY_PACK_PRESENT                  _NDIS_ERROR_TYPEDEF_(0xC0380059)
#define ERROR_VOLMGR_NUMBER_OF_DISKS_INVALID               _NDIS_ERROR_TYPEDEF_(0xC038005A)
#define ERROR_VOLMGR_MIRROR_NOT_SUPPORTED                  _NDIS_ERROR_TYPEDEF_(0xC038005B)
#define ERROR_VOLMGR_RAID5_NOT_SUPPORTED                   _NDIS_ERROR_TYPEDEF_(0xC038005C)

#define ERROR_SPACES_POOL_WAS_DELETED                      _HRESULT_TYPEDEF_(0x00e70001)
#define ERROR_SPACES_FAULT_DOMAIN_TYPE_INVALID             _HRESULT_TYPEDEF_(0x80e70001)
#define ERROR_SPACES_INTERNAL_ERROR                        _HRESULT_TYPEDEF_(0x80e70002)
#define ERROR_SPACES_RESILIENCY_TYPE_INVALID               _HRESULT_TYPEDEF_(0x80e70003)
#define ERROR_SPACES_DRIVE_SECTOR_SIZE_INVALID             _HRESULT_TYPEDEF_(0x80e70004)
#define ERROR_SPACES_DRIVE_REDUNDANCY_INVALID              _HRESULT_TYPEDEF_(0x80e70006)
#define ERROR_SPACES_NUMBER_OF_DATA_COPIES_INVALID         _HRESULT_TYPEDEF_(0x80e70007)
#define ERROR_SPACES_PARITY_LAYOUT_INVALID                 _HRESULT_TYPEDEF_(0x80e70008)
#define ERROR_SPACES_INTERLEAVE_LENGTH_INVALID             _HRESULT_TYPEDEF_(0x80e70009)
#define ERROR_SPACES_NUMBER_OF_COLUMNS_INVALID             _HRESULT_TYPEDEF_(0x80e7000a)
#define ERROR_SPACES_NOT_ENOUGH_DRIVES                     _HRESULT_TYPEDEF_(0x80e7000b)
#define ERROR_SPACES_EXTENDED_ERROR                        _HRESULT_TYPEDEF_(0x80e7000c)
#define ERROR_SPACES_PROVISIONING_TYPE_INVALID             _HRESULT_TYPEDEF_(0x80e7000d)
#define ERROR_SPACES_ALLOCATION_SIZE_INVALID               _HRESULT_TYPEDEF_(0x80e7000e)
#define ERROR_SPACES_ENCLOSURE_AWARE_INVALID               _HRESULT_TYPEDEF_(0x80e7000f)
#define ERROR_SPACES_WRITE_CACHE_SIZE_INVALID              _HRESULT_TYPEDEF_(0x80e70010)
#define ERROR_SPACES_NUMBER_OF_GROUPS_INVALID              _HRESULT_TYPEDEF_(0x80e70011)
#define ERROR_SPACES_DRIVE_OPERATIONAL_STATE_INVALID       _HRESULT_TYPEDEF_(0x80e70012)

#define D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS          _HRESULT_TYPEDEF_(0x887c0001)
#define D3D11_ERROR_FILE_NOT_FOUND                         _HRESULT_TYPEDEF_(0x887c0002)
#define D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS           _HRESULT_TYPEDEF_(0x887c0003)
#define D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD  _HRESULT_TYPEDEF_(0x887c0004)

#define WINCODEC_ERR_WRONGSTATE                            _HRESULT_TYPEDEF_(0x88982f04)
#define WINCODEC_ERR_VALUEOUTOFRANGE                       _HRESULT_TYPEDEF_(0x88982f05)
#define WINCODEC_ERR_UNKNOWNIMAGEFORMAT                    _HRESULT_TYPEDEF_(0x88982f07)
#define WINCODEC_ERR_UNSUPPORTEDVERSION                    _HRESULT_TYPEDEF_(0x88982f0b)
#define WINCODEC_ERR_NOTINITIALIZED                        _HRESULT_TYPEDEF_(0x88982f0c)
#define WINCODEC_ERR_ALREADYLOCKED                         _HRESULT_TYPEDEF_(0x88982f0d)
#define WINCODEC_ERR_PROPERTYNOTFOUND                      _HRESULT_TYPEDEF_(0x88982f40)
#define WINCODEC_ERR_PROPERTYNOTSUPPORTED                  _HRESULT_TYPEDEF_(0x88982f41)
#define WINCODEC_ERR_PROPERTYSIZE                          _HRESULT_TYPEDEF_(0x88982f42)
#define WINCODEC_ERR_CODECPRESENT                          _HRESULT_TYPEDEF_(0x88982f43)
#define WINCODEC_ERR_CODECNOTHUMBNAIL                      _HRESULT_TYPEDEF_(0x88982f44)
#define WINCODEC_ERR_PALETTEUNAVAILABLE                    _HRESULT_TYPEDEF_(0x88982f45)
#define WINCODEC_ERR_CODECTOOMANYSCANLINES                 _HRESULT_TYPEDEF_(0x88982f46)
#define WINCODEC_ERR_INTERNALERROR                         _HRESULT_TYPEDEF_(0x88982f48)
#define WINCODEC_ERR_SOURCERECTDOESNOTMATCHDIMENSIONS      _HRESULT_TYPEDEF_(0x88982f49)
#define WINCODEC_ERR_COMPONENTNOTFOUND                     _HRESULT_TYPEDEF_(0x88982f50)
#define WINCODEC_ERR_IMAGESIZEOUTOFRANGE                   _HRESULT_TYPEDEF_(0x88982f51)
#define WINCODEC_ERR_TOOMUCHMETADATA                       _HRESULT_TYPEDEF_(0x88982f52)
#define WINCODEC_ERR_BADIMAGE                              _HRESULT_TYPEDEF_(0x88982f60)
#define WINCODEC_ERR_BADHEADER                             _HRESULT_TYPEDEF_(0x88982f61)
#define WINCODEC_ERR_FRAMEMISSING                          _HRESULT_TYPEDEF_(0x88982f62)
#define WINCODEC_ERR_BADMETADATAHEADER                     _HRESULT_TYPEDEF_(0x88982f63)
#define WINCODEC_ERR_BADSTREAMDATA                         _HRESULT_TYPEDEF_(0x88982f70)
#define WINCODEC_ERR_STREAMWRITE                           _HRESULT_TYPEDEF_(0x88982f71)
#define WINCODEC_ERR_STREAMREAD                            _HRESULT_TYPEDEF_(0x88982f72)
#define WINCODEC_ERR_STREAMNOTAVAILABLE                    _HRESULT_TYPEDEF_(0x88982f73)
#define WINCODEC_ERR_UNSUPPORTEDPIXELFORMAT                _HRESULT_TYPEDEF_(0x88982f80)
#define WINCODEC_ERR_UNSUPPORTEDOPERATION                  _HRESULT_TYPEDEF_(0x88982f81)
#define WINCODEC_ERR_INVALIDREGISTRATION                   _HRESULT_TYPEDEF_(0x88982f8a)
#define WINCODEC_ERR_COMPONENTINITIALIZEFAILURE            _HRESULT_TYPEDEF_(0x88982f8b)
#define WINCODEC_ERR_INSUFFICIENTBUFFER                    _HRESULT_TYPEDEF_(0x88982f8c)
#define WINCODEC_ERR_DUPLICATEMETADATAPRESENT              _HRESULT_TYPEDEF_(0x88982f8d)
#define WINCODEC_ERR_PROPERTYUNEXPECTEDTYPE                _HRESULT_TYPEDEF_(0x88982f8e)
#define WINCODEC_ERR_UNEXPECTEDSIZE                        _HRESULT_TYPEDEF_(0x88982f8f)
#define WINCODEC_ERR_INVALIDQUERYREQUEST                   _HRESULT_TYPEDEF_(0x88982f90)
#define WINCODEC_ERR_UNEXPECTEDMETADATATYPE                _HRESULT_TYPEDEF_(0x88982f91)
#define WINCODEC_ERR_REQUESTONLYVALIDATMETADATAROOT        _HRESULT_TYPEDEF_(0x88982f92)
#define WINCODEC_ERR_INVALIDQUERYCHARACTER                 _HRESULT_TYPEDEF_(0x88982f93)
#define WINCODEC_ERR_WIN32ERROR                            _HRESULT_TYPEDEF_(0x88982f94)
#define WINCODEC_ERR_INVALIDPROGRESSIVELEVEL               _HRESULT_TYPEDEF_(0x88982f95)

#define MILERR_OBJECTBUSY                                  _HRESULT_TYPEDEF_(0x88980001)
#define MILERR_INSUFFICIENTBUFFER                          _HRESULT_TYPEDEF_(0x88980002)
#define MILERR_WIN32ERROR                                  _HRESULT_TYPEDEF_(0x88980003)
#define MILERR_SCANNER_FAILED                              _HRESULT_TYPEDEF_(0x88980004)
#define MILERR_SCREENACCESSDENIED                          _HRESULT_TYPEDEF_(0x88980005)
#define MILERR_DISPLAYSTATEINVALID                         _HRESULT_TYPEDEF_(0x88980006)
#define MILERR_NONINVERTIBLEMATRIX                         _HRESULT_TYPEDEF_(0x88980007)
#define MILERR_ZEROVECTOR                                  _HRESULT_TYPEDEF_(0x88980008)
#define MILERR_TERMINATED                                  _HRESULT_TYPEDEF_(0x88980009)
#define MILERR_BADNUMBER                                   _HRESULT_TYPEDEF_(0x8898000a)
#define MILERR_INTERNALERROR                               _HRESULT_TYPEDEF_(0x88980080)
#define MILERR_DISPLAYFORMATNOTSUPPORTED                   _HRESULT_TYPEDEF_(0x88980084)
#define MILERR_INVALIDCALL                                 _HRESULT_TYPEDEF_(0x88980085)
#define MILERR_ALREADYLOCKED                               _HRESULT_TYPEDEF_(0x88980086)
#define MILERR_NOTLOCKED                                   _HRESULT_TYPEDEF_(0x88980087)
#define MILERR_DEVICECANNOTRENDERTEXT                      _HRESULT_TYPEDEF_(0x88980088)
#define MILERR_GLYPHBITMAPMISSED                           _HRESULT_TYPEDEF_(0x88980089)
#define MILERR_MALFORMEDGLYPHCACHE                         _HRESULT_TYPEDEF_(0x8898008a)
#define MILERR_GENERIC_IGNORE                              _HRESULT_TYPEDEF_(0x8898008b)
#define MILERR_MALFORMED_GUIDELINE_DATA                    _HRESULT_TYPEDEF_(0x8898008c)
#define MILERR_NO_HARDWARE_DEVICE                          _HRESULT_TYPEDEF_(0x8898008d)
#define MILERR_NEED_RECREATE_AND_PRESENT                   _HRESULT_TYPEDEF_(0x8898008e)
#define MILERR_ALREADY_INITIALIZED                         _HRESULT_TYPEDEF_(0x8898008f)
#define MILERR_MISMATCHED_SIZE                             _HRESULT_TYPEDEF_(0x88980090)
#define MILERR_NO_REDIRECTION_SURFACE_AVAILABLE            _HRESULT_TYPEDEF_(0x88980091)
#define MILERR_REMOTING_NOT_SUPPORTED                      _HRESULT_TYPEDEF_(0x88980092)
#define MILERR_QUEUED_PRESENT_NOT_SUPPORTED                _HRESULT_TYPEDEF_(0x88980093)
#define MILERR_NOT_QUEUING_PRESENTS                        _HRESULT_TYPEDEF_(0x88980094)
#define MILERR_NO_REDIRECTION_SURFACE_RETRY_LATER          _HRESULT_TYPEDEF_(0x88980095)
#define MILERR_TOOMANYSHADERELEMNTS                        _HRESULT_TYPEDEF_(0x88980096)
#define MILERR_MROW_READLOCK_FAILED                        _HRESULT_TYPEDEF_(0x88980097)
#define MILERR_MROW_UPDATE_FAILED                          _HRESULT_TYPEDEF_(0x88980098)
#define MILERR_SHADER_COMPILE_FAILED                       _HRESULT_TYPEDEF_(0x88980099)
#define MILERR_MAX_TEXTURE_SIZE_EXCEEDED                   _HRESULT_TYPEDEF_(0x8898009a)
#define MILERR_QPC_TIME_WENT_BACKWARD                      _HRESULT_TYPEDEF_(0x8898009b)
#define MILERR_DXGI_ENUMERATION_OUT_OF_SYNC                _HRESULT_TYPEDEF_(0x8898009d)
#define MILERR_ADAPTER_NOT_FOUND                           _HRESULT_TYPEDEF_(0x8898009e)
#define MILERR_COLORSPACE_NOT_SUPPORTED                    _HRESULT_TYPEDEF_(0x8898009f)
#define MILERR_PREFILTER_NOT_SUPPORTED                     _HRESULT_TYPEDEF_(0x889800a0)
#define MILERR_DISPLAYID_ACCESS_DENIED                     _HRESULT_TYPEDEF_(0x889800a1)

#define DWRITE_E_FILEFORMAT                                _HRESULT_TYPEDEF_(0x88985000)
#define DWRITE_E_UNEXPECTED                                _HRESULT_TYPEDEF_(0x88985001)
#define DWRITE_E_NOFONT                                    _HRESULT_TYPEDEF_(0x88985002)
#define DWRITE_E_FILENOTFOUND                              _HRESULT_TYPEDEF_(0x88985003)
#define DWRITE_E_FILEACCESS                                _HRESULT_TYPEDEF_(0x88985004)
#define DWRITE_E_FONTCOLLECTIONOBSOLETE                    _HRESULT_TYPEDEF_(0x88985005)
#define DWRITE_E_ALREADYREGISTERED                         _HRESULT_TYPEDEF_(0x88985006)
#define DWRITE_E_CACHEFORMAT                               _HRESULT_TYPEDEF_(0x88985007)
#define DWRITE_E_CACHEVERSION                              _HRESULT_TYPEDEF_(0x88985008)
#define DWRITE_E_UNSUPPORTEDOPERATION                      _HRESULT_TYPEDEF_(0x88985009)
#define DWRITE_E_TEXTRENDERERINCOMPATIBLE                  _HRESULT_TYPEDEF_(0x8898500A)
#define DWRITE_E_FLOWDIRECTIONCONFLICTS                    _HRESULT_TYPEDEF_(0x8898500B)
#define DWRITE_E_NOCOLOR                                   _HRESULT_TYPEDEF_(0x8898500C)

#define D2DERR_WRONG_STATE                                 _HRESULT_TYPEDEF_(0x88990001)
#define D2DERR_NOT_INITIALIZED                             _HRESULT_TYPEDEF_(0x88990002)
#define D2DERR_UNSUPPORTED_OPERATION                       _HRESULT_TYPEDEF_(0x88990003)
#define D2DERR_SCANNER_FAILED                              _HRESULT_TYPEDEF_(0x88990004)
#define D2DERR_SCREEN_ACCESS_DENIED                        _HRESULT_TYPEDEF_(0x88990005)
#define D2DERR_DISPLAY_STATE_INVALID                       _HRESULT_TYPEDEF_(0x88990006)
#define D2DERR_ZERO_VECTOR                                 _HRESULT_TYPEDEF_(0x88990007)
#define D2DERR_INTERNAL_ERROR                              _HRESULT_TYPEDEF_(0x88990008)
#define D2DERR_DISPLAY_FORMAT_NOT_SUPPORTED                _HRESULT_TYPEDEF_(0x88990009)
#define D2DERR_INVALID_CALL                                _HRESULT_TYPEDEF_(0x8899000a)
#define D2DERR_NO_HARDWARE_DEVICE                          _HRESULT_TYPEDEF_(0x8899000b)
#define D2DERR_RECREATE_TARGET                             _HRESULT_TYPEDEF_(0x8899000c)
#define D2DERR_TOO_MANY_SHADER_ELEMENTS                    _HRESULT_TYPEDEF_(0x8899000d)
#define D2DERR_SHADER_COMPILE_FAILED                       _HRESULT_TYPEDEF_(0x8899000e)
#define D2DERR_MAX_TEXTURE_SIZE_EXCEEDED                   _HRESULT_TYPEDEF_(0x8899000f)
#define D2DERR_UNSUPPORTED_VERSION                         _HRESULT_TYPEDEF_(0x88990010)
#define D2DERR_BAD_NUMBER                                  _HRESULT_TYPEDEF_(0x88990011)
#define D2DERR_WRONG_FACTORY                               _HRESULT_TYPEDEF_(0x88990012)
#define D2DERR_LAYER_ALREADY_IN_USE                        _HRESULT_TYPEDEF_(0x88990013)
#define D2DERR_POP_CALL_DID_NOT_MATCH_PUSH                 _HRESULT_TYPEDEF_(0x88990014)
#define D2DERR_WRONG_RESOURCE_DOMAIN                       _HRESULT_TYPEDEF_(0x88990015)
#define D2DERR_PUSH_POP_UNBALANCED                         _HRESULT_TYPEDEF_(0x88990016)
#define D2DERR_RENDER_TARGET_HAS_LAYER_OR_CLIPRECT         _HRESULT_TYPEDEF_(0x88990017)
#define D2DERR_INCOMPATIBLE_BRUSH_TYPES                    _HRESULT_TYPEDEF_(0x88990018)
#define D2DERR_WIN32_ERROR                                 _HRESULT_TYPEDEF_(0x88990019)
#define D2DERR_TARGET_NOT_GDI_COMPATIBLE                   _HRESULT_TYPEDEF_(0x8899001a)
#define D2DERR_TEXT_EFFECT_IS_WRONG_TYPE                   _HRESULT_TYPEDEF_(0x8899001b)
#define D2DERR_TEXT_RENDERER_NOT_RELEASED                  _HRESULT_TYPEDEF_(0x8899001c)
#define D2DERR_EXCEEDS_MAX_BITMAP_SIZE                     _HRESULT_TYPEDEF_(0x8899001d)
#define D2DERR_INVALID_GRAPH_CONFIGURATION                 _HRESULT_TYPEDEF_(0x8899001e)
#define D2DERR_INVALID_INTERNAL_GRAPH_CONFIGURATION        _HRESULT_TYPEDEF_(0x8899001f)
#define D2DERR_CYCLIC_GRAPH                                _HRESULT_TYPEDEF_(0x88990020)
#define D2DERR_BITMAP_CANNOT_DRAW                          _HRESULT_TYPEDEF_(0x88990021)
#define D2DERR_OUTSTANDING_BITMAP_REFERENCES               _HRESULT_TYPEDEF_(0x88990022)
#define D2DERR_ORIGINAL_TARGET_NOT_BOUND                   _HRESULT_TYPEDEF_(0x88990023)
#define D2DERR_INVALID_TARGET                              _HRESULT_TYPEDEF_(0x88990024)
#define D2DERR_BITMAP_BOUND_AS_TARGET                      _HRESULT_TYPEDEF_(0x88990025)
#define D2DERR_INSUFFICIENT_DEVICE_CAPABILITIES            _HRESULT_TYPEDEF_(0x88990026)
#define D2DERR_INTERMEDIATE_TOO_LARGE                      _HRESULT_TYPEDEF_(0x88990027)
#define D2DERR_EFFECT_IS_NOT_REGISTERED                    _HRESULT_TYPEDEF_(0x88990028)
#define D2DERR_INVALID_PROPERTY                            _HRESULT_TYPEDEF_(0x88990029)
#define D2DERR_NO_SUBPROPERTIES                            _HRESULT_TYPEDEF_(0x8899002a)
#define D2DERR_PRINT_JOB_CLOSED                            _HRESULT_TYPEDEF_(0x8899002b)
#define D2DERR_PRINT_FORMAT_NOT_SUPPORTED                  _HRESULT_TYPEDEF_(0x8899002c)
#define D2DERR_TOO_MANY_TRANSFORM_INPUTS                   _HRESULT_TYPEDEF_(0x8899002d)

#define DXGI_STATUS_OCCLUDED                               _HRESULT_TYPEDEF_(0x087a0001)
#define DXGI_STATUS_CLIPPED                                _HRESULT_TYPEDEF_(0x087a0002)
#define DXGI_STATUS_NO_REDIRECTION                         _HRESULT_TYPEDEF_(0x087a0004)
#define DXGI_STATUS_NO_DESKTOP_ACCESS                      _HRESULT_TYPEDEF_(0x087a0005)
#define DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE           _HRESULT_TYPEDEF_(0x087a0006)
#define DXGI_STATUS_MODE_CHANGED                           _HRESULT_TYPEDEF_(0x087a0007)
#define DXGI_STATUS_MODE_CHANGE_IN_PROGRESS                _HRESULT_TYPEDEF_(0x087a0008)
#define DXGI_STATUS_UNOCCLUDED                             _HRESULT_TYPEDEF_(0x087a0009)
#define DXGI_STATUS_DDA_WAS_STILL_DRAWING                  _HRESULT_TYPEDEF_(0x087a000a)
#define DXGI_STATUS_PRESENT_REQUIRED                       _HRESULT_TYPEDEF_(0x087a002f)

#define DXGI_ERROR_INVALID_CALL                            _HRESULT_TYPEDEF_(0x887a0001)
#define DXGI_ERROR_NOT_FOUND                               _HRESULT_TYPEDEF_(0x887a0002)
#define DXGI_ERROR_MORE_DATA                               _HRESULT_TYPEDEF_(0x887a0003)
#define DXGI_ERROR_UNSUPPORTED                             _HRESULT_TYPEDEF_(0x887a0004)
#define DXGI_ERROR_DEVICE_REMOVED                          _HRESULT_TYPEDEF_(0x887a0005)
#define DXGI_ERROR_DEVICE_HUNG                             _HRESULT_TYPEDEF_(0x887a0006)
#define DXGI_ERROR_DEVICE_RESET                            _HRESULT_TYPEDEF_(0x887a0007)
#define DXGI_ERROR_WAS_STILL_DRAWING                       _HRESULT_TYPEDEF_(0x887a000a)
#define DXGI_ERROR_FRAME_STATISTICS_DISJOINT               _HRESULT_TYPEDEF_(0x887a000b)
#define DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE            _HRESULT_TYPEDEF_(0x887a000c)
#define DXGI_ERROR_DRIVER_INTERNAL_ERROR                   _HRESULT_TYPEDEF_(0x887a0020)
#define DXGI_ERROR_NONEXCLUSIVE                            _HRESULT_TYPEDEF_(0x887a0021)
#define DXGI_ERROR_NOT_CURRENTLY_AVAILABLE                 _HRESULT_TYPEDEF_(0x887a0022)
#define DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED              _HRESULT_TYPEDEF_(0x887a0023)
#define DXGI_ERROR_REMOTE_OUTOFMEMORY                      _HRESULT_TYPEDEF_(0x887a0024)
#define DXGI_ERROR_ACCESS_LOST                             _HRESULT_TYPEDEF_(0x887a0026)
#define DXGI_ERROR_WAIT_TIMEOUT                            _HRESULT_TYPEDEF_(0x887a0027)
#define DXGI_ERROR_SESSION_DISCONNECTED                    _HRESULT_TYPEDEF_(0x887a0028)
#define DXGI_ERROR_RESTRICT_TO_OUTPUT_STALE                _HRESULT_TYPEDEF_(0x887a0029)
#define DXGI_ERROR_CANNOT_PROTECT_CONTENT                  _HRESULT_TYPEDEF_(0x887a002a)
#define DXGI_ERROR_ACCESS_DENIED                           _HRESULT_TYPEDEF_(0x887a002b)
#define DXGI_ERROR_NAME_ALREADY_EXISTS                     _HRESULT_TYPEDEF_(0x887a002c)
#define DXGI_ERROR_SDK_COMPONENT_MISSING                   _HRESULT_TYPEDEF_(0x887a002d)
#define DXGI_ERROR_NOT_CURRENT                             _HRESULT_TYPEDEF_(0x887a002e)
#define DXGI_ERROR_HW_PROTECTION_OUTOFMEMORY               _HRESULT_TYPEDEF_(0x887a0030)
#define DXGI_ERROR_MODE_CHANGE_IN_PROGRESS                 _HRESULT_TYPEDEF_(0x887a0025)

#define DCOMPOSITION_ERROR_WINDOW_ALREADY_COMPOSED         _HRESULT_TYPEDEF_(0x88980800)
#define DCOMPOSITION_ERROR_SURFACE_BEING_RENDERED          _HRESULT_TYPEDEF_(0x88980801)
#define DCOMPOSITION_ERROR_SURFACE_NOT_BEING_RENDERED      _HRESULT_TYPEDEF_(0x88980802)

#define ERROR_AUDITING_DISABLED                            _HRESULT_TYPEDEF_(0xC0090001)
#define ERROR_ALL_SIDS_FILTERED                            _HRESULT_TYPEDEF_(0xC0090002)

#endif  /* __WINE_WINERROR_H */
