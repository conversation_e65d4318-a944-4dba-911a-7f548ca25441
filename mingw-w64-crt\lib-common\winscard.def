;
; Definition file of WinSCard.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "WinSCard.dll"
EXPORTS
ClassInstall32
SCardAccessNewReaderEvent
SCardReleaseAllEvents
SCardReleaseNewReaderEvent
SCardAccessStartedEvent
SCardAddReaderToGroupA
SCardAddReaderToGroupW
SCardAudit
SCardBeginTransaction
SCardCancel
SCardConnectA
SCardConnectW
SCardControl
SCardDisconnect
SCardEndTransaction
SCardEstablishContext
SCardForgetCardTypeA
SCardForgetCardTypeW
SCardForgetReaderA
SCardForgetReaderGroupA
SCardForgetReaderGroupW
SCardForgetReaderW
SCardFreeMemory
SCardGetAttrib
SCardGetCardTypeProviderNameA
SCardGetCardTypeProviderNameW
SCardGetDeviceTypeIdA
SCardGetDeviceTypeIdW
SCardGetProviderIdA
SCardGetP<PERSON>ardGetReaderDeviceInstanceIdA
SCardGetReaderDeviceInstanceIdW
SCardGetReaderIconA
SCardGetReaderIconW
SCardGetStatusChangeA
SCardGetStatusChangeW
SCardGetTransmitCount
SCardIntroduceCardTypeA
SCardIntroduceCardTypeW
SCardIntroduceReaderA
SCardIntroduceReaderGroupA
SCardIntroduceReaderGroupW
SCardIntroduceReaderW
SCardIsValidContext
SCardListCardsA
SCardListCardsW
SCardListInterfacesA
SCardListInterfacesW
SCardListReaderGroupsA
SCardListReaderGroupsW
SCardListReadersA
SCardListReadersW
SCardListReadersWithDeviceInstanceIdA
SCardListReadersWithDeviceInstanceIdW
SCardLocateCardsA
SCardLocateCardsByATRA
SCardLocateCardsByATRW
SCardLocateCardsW
SCardReadCacheA
SCardReadCacheW
SCardReconnect
SCardReleaseContext
SCardReleaseStartedEvent
SCardRemoveReaderFromGroupA
SCardRemoveReaderFromGroupW
SCardSetAttrib
SCardSetCardTypeProviderNameA
SCardSetCardTypeProviderNameW
SCardState
SCardStatusA
SCardStatusW
SCardTransmit
SCardWriteCacheA
SCardWriteCacheW
g_rgSCardRawPci DATA
g_rgSCardT0Pci DATA
g_rgSCardT1Pci DATA
