<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: src/m_token.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>src/m_token.c File Reference</h1><code>#include &lt;stdio.h&gt;</code><br/>
<code>#include &lt;stdlib.h&gt;</code><br/>
<code>#include &lt;malloc.h&gt;</code><br/>
<code>#include &lt;string.h&gt;</code><br/>
<code>#include &lt;inttypes.h&gt;</code><br/>
<code>#include &lt;stdint.h&gt;</code><br/>
<code>#include &quot;<a class="el" href="m__token_8h_source.html">m_token.h</a>&quot;</code><br/>
<code>#include &quot;<a class="el" href="m__ms_8h_source.html">m_ms.h</a>&quot;</code><br/>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a7defdcab1465fcfa706d66f1572a08d5">MY_LL</a>&nbsp;&nbsp;&nbsp;&quot;ll&quot;</td></tr>
<tr><td colspan="2"><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle_generate_gc</a> (void)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a3d90ad7945dc89f63c39837ee512fd85">gen_tok</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> kind, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> subkind, size_t addend)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#abf3eb472b66b477d0165a31437d35c09">libmangle_dump_tok</a> (FILE *fp, <a class="el" href="unionu_m_token.html">uMToken</a> *p)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a50ef074a3d1cf22f842abd4df7081743">chain_tok</a> (<a class="el" href="unionu_m_token.html">uMToken</a> *l, <a class="el" href="unionu_m_token.html">uMToken</a> *add)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a5e98df3f83afcc6e9e1f079091c0e567">gen_value</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, uint64_t val, int is_signed, int size)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#ac1a6fe5d506c4fd78650742da8d9e669">gen_name</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, const char *name)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#aced2d2323162ca5846cdb13d631169d6">gen_dim</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, uint64_t val, const char *non_tt_param, int fSigned, int fNegate)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a8c630a1c57e3d4f5009448af0d43fbb8">gen_unary</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <a class="el" href="unionu_m_token.html">uMToken</a> *un)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#a0deb555c3210a60f0b5189ae462ed620">gen_binary</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <a class="el" href="unionu_m_token.html">uMToken</a> *l, <a class="el" href="unionu_m_token.html">uMToken</a> *r)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#ac0f7cf41cc7c3e9c57dd94ed318dd5a4">libmangle_sprint_decl</a> (<a class="el" href="unionu_m_token.html">uMToken</a> *r)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8c.html#afb0d47109f166db3186774ddfcb994be">libmangle_print_decl</a> (FILE *fp, <a class="el" href="unionu_m_token.html">uMToken</a> *r)</td></tr>
</table>
<hr/><h2>Define Documentation</h2>
<a class="anchor" id="a7defdcab1465fcfa706d66f1572a08d5"></a><!-- doxytag: member="m_token.c::MY_LL" ref="a7defdcab1465fcfa706d66f1572a08d5" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MY_LL&nbsp;&nbsp;&nbsp;&quot;ll&quot;</td>
        </tr>
      </table>
</div>
<div class="memdoc">

</div>
</div>
<hr/><h2>Function Documentation</h2>
<a class="anchor" id="a50ef074a3d1cf22f842abd4df7081743"></a><!-- doxytag: member="m_token.c::chain_tok" ref="a50ef074a3d1cf22f842abd4df7081743" args="(uMToken *l, uMToken *add)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* chain_tok </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>l</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>add</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Chains uMTokens together. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>l</em>&nbsp;</td><td>uMtoken chain to link up with. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>add</em>&nbsp;</td><td>uMtoken to add to chain. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd><em>l</em> unchanged </dd></dl>

</div>
</div>
<a class="anchor" id="a0deb555c3210a60f0b5189ae462ed620"></a><!-- doxytag: member="m_token.c::gen_binary" ref="a0deb555c3210a60f0b5189ae462ed620" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uMToken *l, uMToken *r)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_binary </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>l</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>r</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Generates a binary node token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subKind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>l</em>&nbsp;</td><td>Left node element. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>r</em>&nbsp;</td><td>Right node element. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to binary token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__binary.html">sMToken_binary</a> </dd></dl>

</div>
</div>
<a class="anchor" id="aced2d2323162ca5846cdb13d631169d6"></a><!-- doxytag: member="m_token.c::gen_dim" ref="aced2d2323162ca5846cdb13d631169d6" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uint64_t val, const char *non_tt_param, int fSigned, int fNegate)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_dim </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>non_tt_param</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>fSigned</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>fNegate</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "dim" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>val</em>&nbsp;</td><td>Token numerical value. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>non_tt_param</em>&nbsp;</td><td>pointer to decoded C++ template name. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fSigned</em>&nbsp;</td><td>Signedness of the numerical value. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fNegate</em>&nbsp;</td><td>1 for "val" is negative digit. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to dim token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__dim.html">sMToken_dim</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ac1a6fe5d506c4fd78650742da8d9e669"></a><!-- doxytag: member="m_token.c::gen_name" ref="ac1a6fe5d506c4fd78650742da8d9e669" args="(libmangle_gc_context_t *gc, enum eMSToken skind, const char *name)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_name </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>name</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "name" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>name</em>&nbsp;</td><td>Pointer to name string. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to name token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__name.html">sMToken_name</a> </dd></dl>

</div>
</div>
<a class="anchor" id="a3d90ad7945dc89f63c39837ee512fd85"></a><!-- doxytag: member="m_token.c::gen_tok" ref="a3d90ad7945dc89f63c39837ee512fd85" args="(libmangle_gc_context_t *gc, enum eMToken kind, enum eMSToken subkind, size_t addend)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_tok </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>subkind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>addend</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

</div>
</div>
<a class="anchor" id="a8c630a1c57e3d4f5009448af0d43fbb8"></a><!-- doxytag: member="m_token.c::gen_unary" ref="a8c630a1c57e3d4f5009448af0d43fbb8" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uMToken *un)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_unary </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>un</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "unary" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>un</em>&nbsp;</td><td>Pointer to leaf element. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to a unary token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd>sMToken_unary </dd></dl>

</div>
</div>
<a class="anchor" id="a5e98df3f83afcc6e9e1f079091c0e567"></a><!-- doxytag: member="m_token.c::gen_value" ref="a5e98df3f83afcc6e9e1f079091c0e567" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uint64_t val, int is_signed, int size)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_value </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>is_signed</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>size</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "value" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>val</em>&nbsp;</td><td>Sets the value on token, </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>is_signed</em>&nbsp;</td><td>Signed bit of <em>val</em>. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>size</em>&nbsp;</td><td>Width of <em>val</em>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to value token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__value.html">sMToken_value</a> </dd></dl>

</div>
</div>
<a class="anchor" id="abf3eb472b66b477d0165a31437d35c09"></a><!-- doxytag: member="m_token.c::libmangle_dump_tok" ref="abf3eb472b66b477d0165a31437d35c09" args="(FILE *fp, uMToken *p)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void libmangle_dump_tok </td>
          <td>(</td>
          <td class="paramtype">FILE *&nbsp;</td>
          <td class="paramname"> <em>fp</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>p</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Dumps <a class="el" href="unionu_m_token.html">uMToken</a> to a file descriptor for debugging. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fp</em>&nbsp;</td><td>File descriptor to print the token to. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>p</em>&nbsp;</td><td><a class="el" href="unionu_m_token.html">uMToken</a> chain to print. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="a54257a43469abe9c5f9556a1913bbf2f"></a><!-- doxytag: member="m_token.c::libmangle_generate_gc" ref="a54257a43469abe9c5f9556a1913bbf2f" args="(void)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a>* libmangle_generate_gc </td>
          <td>(</td>
          <td class="paramtype">void&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a garbage collection context token. </p>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to context. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="afb0d47109f166db3186774ddfcb994be"></a><!-- doxytag: member="m_token.c::libmangle_print_decl" ref="afb0d47109f166db3186774ddfcb994be" args="(FILE *fp, uMToken *r)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void libmangle_print_decl </td>
          <td>(</td>
          <td class="paramtype">FILE *&nbsp;</td>
          <td class="paramname"> <em>fp</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>p</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Prints C++ name to file descriptor. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fp</em>&nbsp;</td><td>Output file descriptor. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>p</em>&nbsp;</td><td>Token containing information about the C++ name. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle_decode_ms_name()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ac6f10b5d722b67adc42b2efaf4683dc1"></a><!-- doxytag: member="m_token.c::libmangle_release_gc" ref="ac6f10b5d722b67adc42b2efaf4683dc1" args="(libmangle_gc_context_t *gc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void libmangle_release_gc </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em></td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Releases memory tracked by context. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Garbage collection context to work on. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle_generate_gc()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ac0f7cf41cc7c3e9c57dd94ed318dd5a4"></a><!-- doxytag: member="m_token.c::libmangle_sprint_decl" ref="ac0f7cf41cc7c3e9c57dd94ed318dd5a4" args="(uMToken *r)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* libmangle_sprint_decl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>r</em></td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Get pointer to decoded C++ name string. Use free() to release returned string. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>r</em>&nbsp;</td><td>C++ name token. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>pointer to decoded C++ name string. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle_decode_ms_name()</a> </dd></dl>

</div>
</div>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
