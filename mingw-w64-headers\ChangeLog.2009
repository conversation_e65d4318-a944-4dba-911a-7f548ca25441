2009-12-23  <PERSON>  <<EMAIL>>

	* crt/intrin.h (_mm_shuffle_ps): Adjust arguments.

2009-12-19  <PERSON>  <<EMAIL>>

	PR/2916490
	* include/certbcli.h (IN, OUT, OPTIONAL): Protect definition
	by _NO_W32_PSEUDO_MODIFIERS guard.
	* include/lmcons.h: Likewise.
	* include/ntsecapi.h: Likewise.
	* include/rpcdce.h: Likewise.
	* include/wincrypt.h: Likewise.
	* include/windef.h: Likewise.

2009-12-02  Kai Tietz  <<EMAIL>>

	* crt/ivec.h: New dummy file.
	* crt/dvec.h: Use intrin.h and guard definitions for enabled SSE.
	* crt/fvec.h: Likewise.
	* Makefile.am: Add crt/ivec.h to crt headers.
	* Makefile.in: Regenerated.

2009-11-30  Kai <PERSON>  <<EMAIL>>

	* Makefile.in: Regenerated.

2009-11-30  <PERSON>  <<EMAIL>>

	* Makefile.am (syshead_HEADERS): Drop sys/ioctl.h from here, too,
	as the file was moved to the experimental branch.
