LIBRARY api-ms-win-core-com-l1-1-0

EXPORTS

CLSIDFromProgID
CLSIDFromString
CoAddRefServerProcess
CoCreateFreeThreadedMarshaler
CoCreateGuid
CoCreateInstance
CoCreateInstanceEx
CoCreateInstanceFromApp
CoDecrementMTAUsage
CoDisconnectObject
CoFreeUnusedLibraries
CoFreeUnusedLibrariesEx
CoGetApartmentType
CoGetClassObject
CoGetContextToken
CoGetCurrentLogicalThreadId
CoGetInterfaceAndReleaseStream
CoGetMalloc
CoGetMarshalSizeMax
CoGetObjectContext
CoGetStandardMarshal
CoIncrementMTAUsage
CoInitializeEx
CoInitializeSecurity
CoLockObjectExternal
CoMarshalInterface
CoMarshalInterThreadInterfaceInStream
CoRegisterClassObject
CoRegisterPSClsid
CoReleaseMarshalData
CoReleaseServerProcess
CoResumeClassObjects
CoRevokeClassObject
CoSetProxyBlanket
CoSuspendClassObjects
CoSwitchCallContext
CoTaskMemAlloc
CoTaskMemFree
CoTaskMemRealloc
CoUninitialize
CoUnmarshalInterface
CoWaitForMultipleHandles
CoWaitForMultipleObjects
CreateStreamOnHGlobal
FreePropVariantArray
GetHGlobalFromStream
IIDFromString
ProgIDFromCLSID
PropVariantClear
PropVariantCopy
StringFromCLSID
StringFromGUID2
StringFromIID
