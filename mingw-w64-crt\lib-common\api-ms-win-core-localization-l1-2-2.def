LIBRARY api-ms-win-core-localization-l1-2-2

EXPORTS

EnumSystemGeoID
EnumSystemLocalesA
EnumSystemLocalesEx
EnumSystemLocalesW
FindNLSStringEx
FormatMessageA
FormatMessageW
GetACP
GetCalendarInfoEx
GetCPInfo
GetCPInfoExW
GetGeoInfoW
GetLocaleInfoA
GetLocaleInfoEx
GetLocaleInfoW
GetNLSVersionEx
GetOEMCP
GetSystemDefaultLangID
GetSystemDefaultLCID
GetSystemDefaultLocaleName
GetThreadLocale
GetUserDefaultLangID
GetUserDefaultLCID
GetUserDefaultLocaleName
GetUserGeoID
IdnToAscii
IdnToUnicode
IsDBCSLeadByte
IsDBCSLeadByteEx
IsNLSDefinedString
IsValidCodePage
IsValidLocale
IsValidLocaleName
IsValidNLSVersion
LCIDToLocaleName
LCMapStringA
LCMapStringEx
LCMapStringW
LocaleNameToLCID
ResolveLocaleName
VerLanguageNameA
VerLanguageNameW
