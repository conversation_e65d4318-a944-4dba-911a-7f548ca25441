#line 1 "tools/widl/parser.yy.c"

#line 3 "tools/widl/parser.yy.c"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define yy_create_buffer parser__create_buffer
#define yy_delete_buffer parser__delete_buffer
#define yy_scan_buffer parser__scan_buffer
#define yy_scan_string parser__scan_string
#define yy_scan_bytes parser__scan_bytes
#define yy_init_buffer parser__init_buffer
#define yy_flush_buffer parser__flush_buffer
#define yy_load_buffer_state parser__load_buffer_state
#define yy_switch_to_buffer parser__switch_to_buffer
#define yypush_buffer_state parser_push_buffer_state
#define yypop_buffer_state parser_pop_buffer_state
#define yyensure_buffer_stack parser_ensure_buffer_stack
#define yy_flex_debug parser__flex_debug
#define yyin parser_in
#define yyleng parser_leng
#define yylex parser_lex
#define yylineno parser_lineno
#define yyout parser_out
#define yyrestart parser_restart
#define yytext parser_text
#define yywrap parser_wrap
#define yyalloc parser_alloc
#define yyrealloc parser_realloc
#define yyfree parser_free

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yy_create_buffer
#define parser__create_buffer_ALREADY_DEFINED
#else
#define yy_create_buffer parser__create_buffer
#endif

#ifdef yy_delete_buffer
#define parser__delete_buffer_ALREADY_DEFINED
#else
#define yy_delete_buffer parser__delete_buffer
#endif

#ifdef yy_scan_buffer
#define parser__scan_buffer_ALREADY_DEFINED
#else
#define yy_scan_buffer parser__scan_buffer
#endif

#ifdef yy_scan_string
#define parser__scan_string_ALREADY_DEFINED
#else
#define yy_scan_string parser__scan_string
#endif

#ifdef yy_scan_bytes
#define parser__scan_bytes_ALREADY_DEFINED
#else
#define yy_scan_bytes parser__scan_bytes
#endif

#ifdef yy_init_buffer
#define parser__init_buffer_ALREADY_DEFINED
#else
#define yy_init_buffer parser__init_buffer
#endif

#ifdef yy_flush_buffer
#define parser__flush_buffer_ALREADY_DEFINED
#else
#define yy_flush_buffer parser__flush_buffer
#endif

#ifdef yy_load_buffer_state
#define parser__load_buffer_state_ALREADY_DEFINED
#else
#define yy_load_buffer_state parser__load_buffer_state
#endif

#ifdef yy_switch_to_buffer
#define parser__switch_to_buffer_ALREADY_DEFINED
#else
#define yy_switch_to_buffer parser__switch_to_buffer
#endif

#ifdef yypush_buffer_state
#define parser_push_buffer_state_ALREADY_DEFINED
#else
#define yypush_buffer_state parser_push_buffer_state
#endif

#ifdef yypop_buffer_state
#define parser_pop_buffer_state_ALREADY_DEFINED
#else
#define yypop_buffer_state parser_pop_buffer_state
#endif

#ifdef yyensure_buffer_stack
#define parser_ensure_buffer_stack_ALREADY_DEFINED
#else
#define yyensure_buffer_stack parser_ensure_buffer_stack
#endif

#ifdef yylex
#define parser_lex_ALREADY_DEFINED
#else
#define yylex parser_lex
#endif

#ifdef yyrestart
#define parser_restart_ALREADY_DEFINED
#else
#define yyrestart parser_restart
#endif

#ifdef yylex_init
#define parser_lex_init_ALREADY_DEFINED
#else
#define yylex_init parser_lex_init
#endif

#ifdef yylex_init_extra
#define parser_lex_init_extra_ALREADY_DEFINED
#else
#define yylex_init_extra parser_lex_init_extra
#endif

#ifdef yylex_destroy
#define parser_lex_destroy_ALREADY_DEFINED
#else
#define yylex_destroy parser_lex_destroy
#endif

#ifdef yyget_debug
#define parser_get_debug_ALREADY_DEFINED
#else
#define yyget_debug parser_get_debug
#endif

#ifdef yyset_debug
#define parser_set_debug_ALREADY_DEFINED
#else
#define yyset_debug parser_set_debug
#endif

#ifdef yyget_extra
#define parser_get_extra_ALREADY_DEFINED
#else
#define yyget_extra parser_get_extra
#endif

#ifdef yyset_extra
#define parser_set_extra_ALREADY_DEFINED
#else
#define yyset_extra parser_set_extra
#endif

#ifdef yyget_in
#define parser_get_in_ALREADY_DEFINED
#else
#define yyget_in parser_get_in
#endif

#ifdef yyset_in
#define parser_set_in_ALREADY_DEFINED
#else
#define yyset_in parser_set_in
#endif

#ifdef yyget_out
#define parser_get_out_ALREADY_DEFINED
#else
#define yyget_out parser_get_out
#endif

#ifdef yyset_out
#define parser_set_out_ALREADY_DEFINED
#else
#define yyset_out parser_set_out
#endif

#ifdef yyget_leng
#define parser_get_leng_ALREADY_DEFINED
#else
#define yyget_leng parser_get_leng
#endif

#ifdef yyget_text
#define parser_get_text_ALREADY_DEFINED
#else
#define yyget_text parser_get_text
#endif

#ifdef yyget_lineno
#define parser_get_lineno_ALREADY_DEFINED
#else
#define yyget_lineno parser_get_lineno
#endif

#ifdef yyset_lineno
#define parser_set_lineno_ALREADY_DEFINED
#else
#define yyset_lineno parser_set_lineno
#endif

#ifdef yywrap
#define parser_wrap_ALREADY_DEFINED
#else
#define yywrap parser_wrap
#endif

#ifdef yyget_lval
#define parser_get_lval_ALREADY_DEFINED
#else
#define yyget_lval parser_get_lval
#endif

#ifdef yyset_lval
#define parser_set_lval_ALREADY_DEFINED
#else
#define yyset_lval parser_set_lval
#endif

#ifdef yyget_lloc
#define parser_get_lloc_ALREADY_DEFINED
#else
#define yyget_lloc parser_get_lloc
#endif

#ifdef yyset_lloc
#define parser_set_lloc_ALREADY_DEFINED
#else
#define yyset_lloc parser_set_lloc
#endif

#ifdef yyalloc
#define parser_alloc_ALREADY_DEFINED
#else
#define yyalloc parser_alloc
#endif

#ifdef yyrealloc
#define parser_realloc_ALREADY_DEFINED
#else
#define yyrealloc parser_realloc
#endif

#ifdef yyfree
#define parser_free_ALREADY_DEFINED
#else
#define yyfree parser_free
#endif

#ifdef yytext
#define parser_text_ALREADY_DEFINED
#else
#define yytext parser_text
#endif

#ifdef yyleng
#define parser_leng_ALREADY_DEFINED
#else
#define yyleng parser_leng
#endif

#ifdef yyin
#define parser_in_ALREADY_DEFINED
#else
#define yyin parser_in
#endif

#ifdef yyout
#define parser_out_ALREADY_DEFINED
#else
#define yyout parser_out
#endif

#ifdef yy_flex_debug
#define parser__flex_debug_ALREADY_DEFINED
#else
#define yy_flex_debug parser__flex_debug
#endif

#ifdef yylineno
#define parser_lineno_ALREADY_DEFINED
#else
#define yylineno parser_lineno
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */

#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C++ systems might need __STDC_LIMIT_MACROS defined before including
 * <stdint.h>, if you want the limit (max/min) macros for int types.
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern int yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = NULL; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static int yy_n_chars;		/* number of characters read into yy_ch_buf */
int yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = NULL;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart ( FILE *input_file  );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size  );
void yy_delete_buffer ( YY_BUFFER_STATE b  );
void yy_flush_buffer ( YY_BUFFER_STATE b  );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer  );
void yypop_buffer_state ( void );

static void yyensure_buffer_stack ( void );
static void yy_load_buffer_state ( void );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file  );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size  );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len  );

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define parser_wrap() (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP
typedef flex_uint8_t YY_CHAR;

FILE *yyin = NULL, *yyout = NULL;

typedef int yy_state_type;

extern int yylineno;
int yylineno = 1;

extern char *yytext;
#ifdef yytext_ptr
#undef yytext_ptr
#endif
#define yytext_ptr yytext

static yy_state_type yy_get_previous_state ( void );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  );
static int yy_get_next_buffer ( void );
static void yynoreturn yy_fatal_error ( const char* msg  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;
#define YY_NUM_RULES 217
#define YY_END_OF_BUFFER 218
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[1456] =
    {   0,
        0,    0,    0,    0,    0,    0,    0,    0,    4,    4,
      218,  216,  205,  204,  216,  216,  216,  216,  216,  216,
      200,  200,  216,  216,  216,  198,  198,  198,  198,  198,
      198,  142,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  216,  205,  141,  198,  216,  200,  200,  198,  198,
        8,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      205,  141,  217,    5,    7,  217,    4,    4,    4,  210,
        0,  202,    0,  214,    0,  203,    0,  208,    0,    0,

      200,  200,  200,    0,  206,  212,  209,  211,  207,  198,
      198,    0,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  213,    0,  141,
        0,  141,    0,  198,    0,  200,    0,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,   66,

      198,  198,  198,   72,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,    0,  141,  141,    5,    5,    7,    0,    6,    0,
        4,    4,    4,  215,  139,  200,  200,  199,  198,    0,
      201,    0,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      175,  198,  198,  198,  198,  198,  198,  198,  198,  198,

      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,    0,    0,  198,    0,  200,    0,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,   82,  198,  198,  198,  198,  198,
       92,  198,  198,   96,  198,  198,  198,  198,  106,  198,
      198,  198,  110,  198,  198,  198,  198,  198,  198,  198,

      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,    6,    4,    4,    0,  199,  199,
      198,  144,  198,  145,  198,  198,  198,  198,  155,  156,
      198,  157,  198,  198,  198,  198,  198,  198,  198,  198,
      166,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      178,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      196,  198,    0,    0,  198,    0,  200,    0,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
       22,  198,  155,  198,  198,   27,  198,  198,  198,  198,

      198,  198,  198,  198,  198,  198,   46,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,   58,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,   75,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,   87,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  131,  198,  198,  198,  198,  198,    4,    4,    0,
      139,  199,  199,  143,  198,  198,  198,  198,  198,  146,
      198,  159,  198,  198,  198,  198,  198,  198,  198,  198,

      169,  198,  171,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  186,  198,  198,  189,  198,
      198,  198,  198,  198,  194,  198,  198,    0,    0,  198,
        0,  200,    0,  198,  198,  198,   12,  198,  198,  198,
      198,  198,   18,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,   50,  198,  198,  198,  198,   56,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,   78,  198,   80,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,

      198,  198,  198,  198,  198,  105,  198,  108,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,    4,    3,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  165,  198,  168,  198,
      172,  174,  198,  198,  198,  180,  198,  147,  198,  198,
      198,  198,  187,  188,  190,  198,  191,  192,  198,  198,
      198,    0,    0,  198,    0,  200,    0,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,   36,   37,  198,

      198,  198,  198,  198,  198,   48,  198,  198,  198,  198,
      198,  198,  198,   59,  198,  198,  198,   65,  198,   68,
       69,  198,  198,  198,  198,  198,  198,  198,  198,  198,
       84,  198,  198,  198,   89,   91,  198,  198,  198,  198,
      198,  198,  100,  198,  198,  198,  107,  198,  198,  198,
      198,  114,  115,  198,  118,  198,  120,  198,  122,  192,
      198,  198,  198,  128,  198,  198,  198,  133,  198,  198,
      198,    4,    3,    3,  198,  150,  152,  198,  198,  154,
      158,  198,  161,  162,  198,  198,  198,  198,  198,  198,
      177,  179,  198,  198,  198,  198,  198,  148,  193,  198,

      197,    0,    0,  198,    0,  200,    0,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
       25,  198,  198,  198,  198,  198,   35,  162,  198,  198,
       44,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  198,  198,  198,   73,  198,  198,  198,
      198,   81,   83,  198,  198,  198,  198,  198,  198,  198,
      198,  198,  198,  101,  102,  198,  198,  198,  198,  198,
      198,  117,  198,  198,  198,  198,  198,  198,  198,  198,
      132,  134,  198,  198,    4,    3,  198,  198,  198,  198,
      198,  163,  198,  198,  170,  198,  198,  198,  198,  183,

      184,  198,  195,    0,  137,  198,    0,  200,    0,  198,
      198,  198,  198,   14,  198,  198,  198,  198,  198,   21,
      198,  198,   26,  198,  198,  198,   33,  198,  198,  198,
      198,  198,  198,  198,   49,   51,  198,  198,  198,  198,
      198,  198,   61,  198,  198,  198,  198,  198,  198,   77,
      198,  198,  198,  198,  198,  198,   94,   95,   97,  198,
      198,  198,  198,  109,  198,  198,  198,  198,  119,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,    4,
        3,  198,  151,  149,  198,  160,  198,  198,  173,  176,
      181,  198,  198,    0,  198,    0,    0,  200,  198,  198,

       13,  198,   16,   17,  198,  198,   23,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,   45,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,   76,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  104,  198,  198,  198,  198,  198,  123,  198,  125,
      198,  127,  198,  198,  135,  198,    4,    3,    0,  140,
      198,  198,  198,  182,  198,    0,  198,    0,    0,  198,
      198,   15,   19,  198,   24,  198,   29,  198,  198,  198,
      198,  198,  198,  198,  198,  198,  198,  198,  198,  198,
      198,  198,   62,   67,  198,  198,   74,  198,  198,  198,

      198,  198,  198,  198,  198,  103,  198,  198,  113,  198,
      198,  198,  198,  198,  198,  198,    1,    3,  153,  198,
      198,  198,    0,  198,    0,    0,   10,  198,   20,   28,
      198,  198,   38,  198,  198,  198,  198,   43,  198,   52,
       53,  198,  198,  198,   60,  198,  198,  198,  198,  198,
      198,  198,  198,   90,  198,  198,  198,  198,  112,  116,
      198,  124,  126,  198,  198,  198,    3,  198,  198,  185,
        0,    0,    0,    0,   11,  198,  198,  198,   40,  198,
      198,  198,  198,   55,  198,  198,  198,  198,  198,  198,
       85,   86,  198,  198,  198,  198,  111,  198,  129,  198,

      136,    3,  164,  198,    0,    0,    0,  198,  198,  198,
       41,  198,  198,  198,  198,  198,   64,   70,  198,  198,
       88,   93,  198,  198,  198,  198,    3,  167,    0,    0,
        0,   30,  198,  198,  198,  198,  198,   57,  198,  198,
      198,   98,  198,  198,  198,    3,    0,    0,    0,  198,
       34,   39,  198,   47,   54,  198,   71,  198,   99,  198,
      198,    2,    0,    0,    0,  198,  198,  198,  198,  198,
      198,  130,    2,    0,    0,    0,  198,  198,  198,   63,
      198,  198,    0,    0,    0,  198,  198,  198,  198,  198,
      138,    0,    0,  198,  198,  198,   79,  198,    0,    0,

      198,  198,  198,  198,    0,    0,  198,  198,  198,  121,
        0,    0,  198,  198,  198,    0,    0,  198,  198,  198,
        0,    0,  198,   32,  198,    0,    0,  198,   42,    0,
        0,   31,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    9,    0,    9,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    2,    2,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    4,    5,    6,    1,    1,    7,    8,    9,
        1,    1,   10,    1,   11,   12,    1,   13,   14,   15,
       16,   17,   18,   19,   18,   18,   18,    1,    1,   20,
       21,   22,    1,    1,   23,   24,   24,   24,   25,   26,
       27,   27,   27,   27,   27,   28,   27,   29,   27,   27,
       27,   30,   31,   32,   33,   27,   27,   34,   35,   27,
       36,   37,   38,    1,   39,    1,   40,   41,   42,   43,

       44,   45,   46,   47,   48,   49,   50,   51,   52,   53,
       54,   55,   56,   57,   58,   59,   60,   61,   62,   63,
       64,   65,    1,   66,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[67] =
    {   0,
        1,    1,    2,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    3,    3,    3,    3,    3,    3,    3,    1,
        1,    1,    3,    3,    3,    3,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    1,    1,    1,    4,    3,
        3,    3,    3,    3,    3,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    1
    } ;

static const flex_int16_t yy_base[1499] =
    {   0,
        0,   65,  113,   66,   60,   67, 2473, 2472,   88,   90,
     2476, 2479, 2479, 2479, 2454,   64, 2467,   62, 2451, 2460,
      164,   53,   68, 2450,   73,    0, 2447, 2464, 2435, 2444,
     2436, 2479,   64, 2410,   36,  146,   48,   51, 2413,   57,
       58,   59,   61, 2423,   84,   89,  143, 2398, 2408, 2406,
     2417, 2392,  157,  132,  140,  223,  256,  151,  307, 2434,
     2479,  165,  165,  248,  309,  198,  287,  186,  313,  292,
      314,  177,  321,  252,  218,  331,  188,  257,  356,  162,
      141, 2454, 2479,  399,    0,  207,    0, 2407, 2406, 2479,
      248, 2479, 2450, 2479,  195, 2479, 2449, 2479, 2439,  406,

      220,   65,   84,    0, 2479, 2479, 2479, 2479, 2479,    0,
     2422,  281, 2421, 2422, 2414,  329, 2403, 2405, 2404, 2384,
     2394, 2387, 2381, 2381, 2394, 2397,  234, 2381,  344, 2377,
     2374, 2373, 2375, 2372, 2376, 2376, 2373, 2372,  278, 2385,
     2372, 2365, 2380, 2370, 2363, 2366,  150, 2366, 2364,  248,
     2377,  105, 2368, 2360,  185, 2366, 2366, 2479,  386,  144,
     2364,  312, 2354, 2367,  426,  459,    0,  507, 2381, 2349,
      297, 2356, 2353,  351, 2341, 2345, 2350,  321, 2348, 2342,
      277, 2356,  355, 2341,  360, 2340, 2346,  373, 2356,  387,
     2351,  406, 2334,  439, 2336, 2348, 2338, 2339, 2346, 2344,

     2334, 2343,   68,  456, 2337, 2331,  215,  251,  371,  373,
     2343, 2322,  438, 2332, 2329, 2335, 2319, 2318, 2332,  379,
     2327, 2320, 2316, 2331, 2318,  498,  443, 2310,  502, 2321,
     2311, 2327, 2323,  405, 2321, 2316, 2324, 2305, 2304, 2321,
     2302,  452, 2356, 2355, 2354,  553,    0,  451, 2353,  406,
        0, 2311, 2300, 2479,  560, 2479, 2479,  530, 2321,  457,
     2479, 2348, 2322, 2324, 2323, 2294, 2288, 2302, 2302, 2292,
     2298, 2297, 2298, 2282, 2287, 2279, 2297, 2284, 2294, 2289,
     2277, 2290, 2278, 2275, 2284, 2287, 2283, 2281, 2270, 2275,
     2278, 2264, 2274, 2272, 2258, 2273, 2274, 2260, 2266, 2253,

     2253, 2254, 2257, 2265, 2257, 2248, 2264, 2245, 2245, 2259,
     2248, 2253, 2257, 2259, 2255, 2257, 2245,  592,  625,    0,
      673, 2247, 2237, 2242,  451, 2238, 2234, 2236, 2236, 2234,
     2244, 2239, 2245, 2240, 2232, 2240, 2237,  287,  454, 2221,
      406, 2239,  474, 2225, 2218, 2225, 2234, 2220, 2218, 2215,
     2218, 2219, 2218, 2217, 2221, 2224, 2221, 2221, 2208, 2219,
     2209, 2206, 2220, 2212,  455, 2201, 2184, 2197, 2190, 2162,
     2165, 2146, 2162, 2144,    0, 2142, 2146,  493, 2151, 2154,
        0, 2157, 2148,    0, 2138, 2135, 2136,  534,    0, 2137,
     2141, 2130,    0, 2115, 2111, 2111, 2108, 2122, 2123, 2109,

      487,  438, 2106, 2120, 2110, 2118,  462,  470, 2118, 2112,
     2115, 2096, 2085, 2095, 2136, 2086, 2079,  609,  527,  511,
     2110,    0, 2111,    0, 2074, 2073, 2077, 2086,    0,    0,
     2078,    0, 2088, 2064, 2066, 2081, 2047, 2060, 2057, 2053,
        0, 2046, 2045, 2042, 2049, 2042, 2041, 2044, 2039, 2055,
        0, 2036, 2038, 2030, 2034, 2029, 2014, 2023, 2022, 2010,
     2024, 2013, 2015, 2017, 2024, 2021, 2020, 2014, 2003, 2009,
        0, 1984, 1989, 1993, 1999,  706,  739,    0,  787, 1976,
     1992, 1991, 1981, 1991, 1973, 1972, 1989, 1987, 1985, 1983,
        0, 1979, 1969,  543, 1956,    0, 1967, 1951,  539, 1950,

     1960, 1942, 1960,  513, 1960, 1960,    0, 1947, 1954, 1942,
     1927, 1931, 1929, 1927, 1915, 1915, 1928,    0, 1920,  556,
     1926, 1914, 1911, 1919, 1923, 1917, 1916, 1904,    0, 1899,
     1904, 1905, 1895, 1897, 1900, 1882, 1895, 1880, 1879, 1872,
     1889, 1891, 1872,  534, 1880, 1882, 1866, 1876,  548, 1879,
     1845, 1860, 1863, 1852, 1861,  547, 1847, 1863, 1851,  591,
     1859, 1857, 1851,  560, 1856, 1857, 1834, 1846, 1830, 1837,
     1829,    0, 1821, 1816, 1824, 1814, 1831, 1830, 1809,  664,
      801, 2479, 2479,    0, 1837,  636, 1824, 1812, 1824,    0,
     1801,    0, 1798, 1800, 1792, 1802, 1788, 1796, 1800, 1785,

        0, 1793,    0, 1777, 1791, 1789, 1776, 1789, 1787, 1771,
     1774, 1767, 1751, 1752, 1756,    0, 1764, 1761,    0, 1763,
     1753, 1744, 1755, 1757,    0, 1747, 1760, 1759, 1741, 1737,
      833,  866,    0,  914, 1751, 1731,    0, 1722, 1735, 1734,
     1721, 1723, 1732, 1723, 1728, 1726, 1725, 1726, 1725, 1702,
     1701, 1695,  606, 1692, 1699, 1691, 1690, 1700, 1687, 1693,
     1693, 1692, 1687,    0,  424, 1676, 1691, 1693,    0, 1688,
     1682, 1671, 1663, 1651, 1656, 1654, 1663, 1648, 1657, 1662,
     1650, 1663, 1654, 1642,    0, 1659,    0, 1648, 1653, 1648,
     1624, 1633, 1617, 1611, 1615, 1614, 1624, 1618, 1616, 1629,

     1624, 1624, 1622, 1601, 1618,    0, 1617,    0, 1592, 1586,
      111,  160,  226,  311,  346,  359,  394,  413,  458,  490,
      494,  522,  547,  560,  568,  569,  585,  571,  601,  600,
      602,  605,  615,  659,  632,  648,  647,  631,  613,  620,
      616,  621,  640,  634,  635,  636,    0,  642,    0,  662,
      651,    0,  663,  640,  647,    0,  666,    0,  648,  664,
      665,  666,    0,    0,    0,  660,    0,    0,  667,  682,
      668,  673,  693,  677,  947,  980,    0, 1028,  676,  696,
      694,  679,  680,  696,  697,  682,  703,  693,  705,  705,
      702,  719,  707,  728,  710,  728,  720,    0,    0,  714,

      730,  711,  732,  720,  739,    0,  732,  743,  743,  740,
      741,  733,  752,  754,  741,  744,  739,    0,  738,    0,
        0,  758,  759,  764,  751,  782,  778,  772,  780,  774,
        0,  771,  794,  791,  797,    0,  783,  773,  799,  800,
      790,  785,    0,  784,  785,  794,    0,  803,  811,  801,
      819,    0,  823,  805,    0,  807,    0,  826,    0,  827,
      819,  820,  809,    0,  830,  812,  820,    0,  833,  841,
      848,  851,    0,  893,  873,  878,    0,  847,  843,    0,
        0,  842,    0,    0,  858,  859,  845,  846,  864,  871,
        0,    0,  872,  867,  859,  860,  877,    0,    0,  877,

        0,  864,  920,  883, 1061, 1094,    0, 1142,  884,  866,
      890,  891,  888,  888,  900,  883,  891,  901,  889,  888,
        0,  899,  910,  910,  913,  894,    0,  939,  928,  927,
        0,  925,  934,  916,  933,  925,  917,  920,  923,  932,
      925,  941,  938,  957,  943,  948,    0,  945,  962,  968,
      964,    0,    0,  956,  956,  963,  972,  966,  975,  975,
      984,  989,  990,    0,  973,  988,  969,  981,  991,  977,
      984,    0,  995,  997, 1000,  996,  991, 1004,  999, 1006,
        0,    0, 1010, 1003, 1014, 1004, 1028, 1047, 1014, 1026,
     1023,    0, 1024, 1042,    0, 1042, 1044, 1045, 1046,    0,

        0, 1040,    0, 1052, 1091, 1048, 1175, 1210, 1084, 1085,
     1056, 1058, 1041,    0, 1046, 1055, 1056, 1068, 1078,    0,
     1064, 1071,    0, 1066, 1075, 1081, 1068, 1082, 1077, 1092,
     1091, 1092, 1097, 1091,    0,    0, 1089, 1100, 1107, 1107,
     1097, 1105,    0, 1097, 1099, 1109, 1123, 1110, 1106,    0,
     1116, 1130, 1131, 1114, 1122, 1134,    0,    0,    0, 1127,
     1133, 1133, 1135,    0, 1120, 1137, 1137, 1141,    0, 1142,
     1139, 1138, 1157, 1165, 1146, 1148, 1167, 1165, 1151, 1156,
     1172, 1228,    0,    0, 1170,    0, 1168, 1155,    0,    0,
        0, 1164, 1191, 1186, 1181, 1260,    0, 1223, 1184, 1195,

        0, 1186,    0,    0, 1197, 1190,    0, 1183, 1184, 1201,
     1206, 1203, 1195, 1198, 1199, 1211, 1198, 1200,    0, 1203,
     1194, 1197, 1210, 1199, 1205, 1197, 1216, 1204, 1223, 1218,
     1224,    0, 1221, 1227, 1228, 1224, 1240, 1222, 1236, 1243,
     1243,    0, 1250, 1242, 1248, 1249, 1240,    0, 1239,    0,
     1255,    0, 1249, 1239,    0, 1251, 1290, 1251, 1305, 2479,
     1249, 1269, 1250,    0, 1253, 1260, 1273, 1302,    0, 1278,
     1272,    0,    0, 1280,    0, 1271,    0, 1277, 1274, 1289,
     1282, 1274, 1294, 1283, 1294, 1284, 1295, 1286, 1301, 1288,
     1307, 1291, 1309,    0, 1305, 1314,    0, 1316, 1305, 1306,

     1317, 1313, 1312, 1308, 1317,    0, 1323, 1305,    0, 1321,
     1307, 1323, 1310, 1329, 1311, 1331,    0, 1315,    0, 1331,
     1316, 1317, 1336, 1375, 1365,    0,    0, 1341,    0,    0,
     1343, 1329,    0, 1348, 1349, 1343, 1337,    0, 1354,    0,
        0, 1344, 1340, 1359,    0, 1346, 1350, 1349, 1350, 1363,
     1367, 1368, 1362,    0, 1360, 1361, 1376, 1359,    0,    0,
     1374,    0,    0, 1368, 1376, 1370, 1378, 1379, 1385,    0,
     1423, 1424, 1474,    0,    0, 1376, 1380, 1378,    0, 1386,
     1383, 1392, 1390,    0, 1375, 1382, 1385, 1394, 1395, 1395,
        0,    0, 1396, 1388, 1385, 1383,    0, 1381,    0, 1388,

        0, 1401,    0, 1388, 1408, 1507, 1438, 1406, 1397, 1408,
        0, 1395, 1395, 1404, 1412, 1398,    0,    0, 1407, 1412,
        0,    0, 1416, 1410, 1403, 1406, 1416,    0, 1408, 1455,
        0, 1428, 1415, 1417, 1411, 1427, 1428,    0, 1429, 1430,
     1435,    0, 1417, 1438, 1424, 1416, 1427, 1540,    0, 1443,
        0,    0, 1437,    0,    0, 1419,    0, 1422,    0, 1437,
     1428,    0, 1446, 1573,    0, 1441, 1458, 1450, 1445, 1457,
     1466,    0,    0, 1454, 1606,    0, 1450, 1452, 1468,    0,
     1457, 1459, 1467, 1639, 1516, 1484, 1481, 1470, 1478, 1493,
     2479, 1526,    0, 1481, 1499, 1501,    0, 1490, 1672,    0,

     1494, 1492, 1502, 1501, 1705,    0, 1506, 1512, 1514,    0,
     1738,    0, 1511, 1502, 1524, 1771, 1558, 1522, 1527, 1530,
     1562,    0, 1509,    0, 1525, 1804,    0, 1532,    0, 1837,
        0,    0, 1870,    0, 1903,    0, 1936,    0, 1969,    0,
     2002,    0, 2035,    0, 2068,    0, 2101,    0, 2134,    0,
     2167, 2479, 1589, 2479, 2479, 2212, 2216, 2220, 2224, 2228,
     2230, 2234, 2238, 2242, 1574, 2246, 1575, 1576, 1590, 1592,
     2250, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 2254,
     1606, 1607, 1608, 1609, 1623, 1624, 1625, 1630, 1631, 1632,
     1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640

    } ;

static const flex_int16_t yy_def[1499] =
    {   0,
     1455,    1,    1,    3, 1456, 1456, 1457, 1457, 1458, 1458,
     1455, 1455, 1455, 1455, 1455, 1459, 1455, 1460, 1455, 1455,
     1455,   21, 1455, 1455, 1455, 1461, 1461, 1461, 1461, 1461,
     1461, 1455, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1455, 1455, 1455, 1461, 1459, 1455,   57, 1461,   59,
     1455,   59,   59,   59,   59,   59,   59, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1455, 1455, 1455, 1455, 1462, 1463, 1464, 1464, 1464, 1455,
     1459, 1455, 1459, 1455, 1460, 1455, 1460, 1455, 1455, 1455,

       21, 1455, 1455, 1465, 1455, 1455, 1455, 1455, 1455, 1461,
     1461, 1466, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1455, 1455, 1455,
     1455, 1455, 1455, 1461,   56, 1455, 1467, 1461,  168,  168,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
      168,  168, 1461, 1461,  168, 1461, 1461, 1461, 1461, 1461,
     1461, 1461,  168, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1455, 1455, 1455, 1455, 1455, 1462, 1463, 1455, 1463,
     1464, 1464, 1464, 1455, 1455, 1455, 1455, 1465, 1461, 1466,
     1455, 1466, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1455, 1455, 1461,   56, 1455, 1468,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461,  321, 1461, 1461, 1461, 1461,
      321,  321, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1455, 1464, 1464, 1455, 1455, 1455,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1455, 1455, 1461,   56, 1455, 1469, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461,  479, 1461, 1461, 1461, 1461, 1461,

     1461,  479, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1464, 1464, 1455,
     1455, 1455, 1455, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1455, 1455, 1461,
       56, 1455, 1470, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1464, 1471, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1455, 1455, 1461,   56, 1455, 1472, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1464, 1471, 1471, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1455, 1455, 1461,   56, 1455, 1473, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1464, 1471, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1455, 1455, 1461,   56, 1455, 1455, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1464,
     1471, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1455, 1461,   56, 1474, 1008, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1464, 1471, 1455, 1455,
     1461, 1461, 1461, 1461, 1461, 1455, 1461,   56, 1475, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1464, 1471, 1461, 1461,
     1461, 1461, 1455, 1461,   56, 1476, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1471, 1461, 1461, 1461,
     1455, 1455,   56, 1477, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,

     1461, 1471, 1461, 1461, 1455,   56, 1455, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1471, 1461, 1455, 1096,
     1478, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1461, 1461, 1461, 1471, 1455,   56, 1479, 1461,
     1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461, 1461,
     1461, 1480, 1455,   56, 1481, 1461, 1461, 1461, 1461, 1461,
     1461, 1461, 1480, 1455,   56, 1482, 1461, 1461, 1461, 1461,
     1461, 1461, 1455,   56, 1455, 1461, 1461, 1461, 1461, 1461,
     1455, 1096, 1483, 1461, 1461, 1461, 1461, 1461,   56, 1484,

     1461, 1461, 1461, 1461,   56, 1485, 1461, 1461, 1461, 1461,
       56, 1486, 1461, 1461, 1461,   56, 1455, 1461, 1461, 1461,
     1096, 1487, 1461, 1461, 1461,   56, 1488, 1461, 1461,   56,
     1489, 1461,   56, 1490,   56, 1491,   56, 1492,   56, 1493,
       56, 1494,   56, 1495,   56, 1496,   56, 1497,   56, 1498,
       56, 1455, 1096, 1455,    0, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455

    } ;

static const flex_int16_t yy_nxt[2546] =
    {   0,
       12,   13,   14,   15,   16,   12,   17,   18,   12,   12,
       19,   20,   21,   22,   22,   22,   22,   22,   22,   23,
       24,   25,   26,   26,   26,   27,   26,   28,   29,   26,
       30,   31,   26,   26,   26,   32,   12,   12,   33,   34,
       35,   36,   37,   38,   39,   26,   40,   41,   26,   26,
       42,   43,   44,   26,   45,   26,   46,   47,   48,   49,
       50,   51,   26,   26,   26,   52,   53,   81,   92,   96,
       54,   82,   84,   84,   84,   84,   84,   84,   84,   84,
       84,   84,   84,   84,   84,   84, 1455,  105,  106,  122,
       83,  129,   83,  108,  109,  130,  136,  256,   97,  123,

       93,  131,  116,  132,  142,  117,  140,  133,  118,  138,
      139,  257,  141,  134,  143, 1455,   55,   56,  119,  364,
      137,  120,  365,  145,  256,   57,   58,   58,   58,   58,
       58,   58,  147,  162,  257,   59,   59,   59,   60,   88,
      146,   88,  242,   26,  306,  162,  243,  307,  148,   89,
       61,   89,   62,   63,   64,   65,   66,   67,  159,   68,
       69,  308,  160,   70,   71,   72,   73,   74,  850,   75,
       76,   77,   78,   79,   80,  100,  101,  101,  101,  101,
      101,  101,  101,  142, 1455,  124,  163,  164,  125,  149,
      150,  102,  126,  143,  151,  299,  103,  104,  163,  127,

      128,  152,   96,  157,  153,  300,  170,  851,  161,  241,
      171,  249,  177, 1455,  102,  172,  144,  173,  178,  174,
      212,  179,  175,  103,  176,  197,  104,   92,  180,  198,
      213,   97,  311,  199,  231,  165,  165,  165,  165,  165,
      165,  165,  312,  250,  232,  165,  165,  165,  165,  137,
      190,  154,   92, 1455,  133,  292,  370,  225,  191,   93,
      192,  226,  165,  165,  165,  165,  165,  165,  166,  166,
      166,  166,  166,  166,  166,  275,  852,  148,  167,  167,
      167,  167, 1455,  102,   93,  261,  276,  181,  103,  104,
      182,  220,  371,  303,  126,  167,  167,  167,  167,  167,

      167,  183,  128,  293,  233,  221,  102,  184,  222,  234,
      223,  224,  304,  162,  235,  103,  236,  262,  104,  168,
      168,  168,  168,  168,  168,  168,  193,  335,  290,  168,
      168,  168,  168,  205,  272,  206,  291,  194,  497,  207,
      195,  498,  323,  196,  324,  208,  168,  168,  168,  168,
      168,  168,  185,  209,  853,  200,  186,  210,  201,  187,
      202,  214,  188,  215,  203,  204,  163,  143,  189,  237,
      117,  216,  211,  118,  270,  217,  266,  149,  227,  332,
      218,  219,  151,  119,  228,  278,  120,  159,  279,  229,
      327,  160,  230,  854,  280,  238,  275,  337,  269,  239,

      245,  341,  855,  240,  342,  328,  338,  339,  248,  156,
      280,  246,  246,  246,  246,  246,  246,  246,  255,  255,
      255,  255,  255,  255,  255,  345,  347,  372,  348,  349,
      374,  294,  282,  856,  373,  386,  297,  161,  318,  318,
      318,  318,  318,  318,  318,  350,  283,  352,  318,  318,
      318,  318,  407,  242,  857,  249,  436,  243, 1455,  501,
      353,  261,  312,  808,  285,  318,  318,  318,  318,  318,
      318,  319,  319,  319,  319,  319,  319,  319,  355,  377,
      809,  320,  320,  320,  320,  564,  102,  250,  303,  483,
      378,  103,  286,  262,  366,  398,  379,  466,  320,  320,

      320,  320,  320,  320,  484,  526,  290,  399,  447,  102,
      367,  434,  499,  503,  291,  469,  858,  569,  103,  321,
      321,  321,  321,  321,  321,  321,  570,  571,  504,  321,
      321,  321,  321,  538,  539,  859,  540,  392,  583,  562,
      860,  401,  393,  299,  307,  563,  321,  321,  321,  321,
      321,  321,  394,  395,  245,  396,  397,  419,  402,  582,
      597,  583,  420,  658,  861,  246,  246,  246,  246,  246,
      246,  246,  255,  255,  255,  255,  255,  255,  255,  548,
      419,  648,  652,  649,  418,  697,  582,  698,  549,  420,
      711,  612,  550,  703,  614,  653,  551,  672,  862,  863,

      673,  719,  704,  418,  476,  476,  476,  476,  476,  476,
      476,  864,  720,  674,  476,  476,  476,  476,  580,  580,
      865,  581,  581,  581,  581,  581,  581,  581,  866,  715,
      867,  476,  476,  476,  476,  476,  476,  477,  477,  477,
      477,  477,  477,  477,  618,  796,  868,  478,  478,  478,
      478,  736,  102,  869,  737,  870,  871,  103,  872,  797,
      874,  875,  876,  877,  478,  478,  478,  478,  478,  478,
      878,  879,  880,  881,  882,  102,  581,  581,  581,  581,
      581,  581,  581,  883,  103,  479,  479,  479,  479,  479,
      479,  479,  884,  885,  886,  479,  479,  479,  479,  887,

      888,  889,  890,  891,  892,  893,  894,  895,  896,  897,
      898,  899,  479,  479,  479,  479,  479,  479,  631,  631,
      631,  631,  631,  631,  631,  900,  901,  902,  631,  631,
      631,  631,  903,  904,  909,  910,  911,  912,  913,  914,
      915,  916,  917,  918,  919,  631,  631,  631,  631,  631,
      631,  632,  632,  632,  632,  632,  632,  632,  920,  921,
      922,  633,  633,  633,  633,  923,  102,  924,  925,  926,
      927,  103,  928,  929,  930,  931,  932,  933,  633,  633,
      633,  633,  633,  633,  934,  935,  936,  937,  938,  102,
      939,  940,  888,  941,  942,  943,  944,  945,  103,  634,

      634,  634,  634,  634,  634,  634,  946,  947,  948,  634,
      634,  634,  634,  581,  581,  581,  581,  581,  581,  581,
      949,  950,  951,  952,  953,  418,  634,  634,  634,  634,
      634,  634,  954,  955,  956,  957,  958,  959,  960,  961,
      962,  963,  964,  965,  418,  775,  775,  775,  775,  775,
      775,  775,  966,  967,  968,  775,  775,  775,  775,  969,
      970,  971,  972,  973,  974,  975,  976,  977,  978,  979,
      980,  981,  775,  775,  775,  775,  775,  775,  776,  776,
      776,  776,  776,  776,  776,  982,  983,  984,  777,  777,
      777,  777,  985,  102,  874,  987,  988,  989,  103,  990,

      991,  992,  993,  994,  995,  777,  777,  777,  777,  777,
      777,  996,  997,  998,  999, 1000,  102, 1001, 1002, 1003,
     1004, 1005, 1006, 1011, 1012,  103,  778,  778,  778,  778,
      778,  778,  778, 1013, 1014, 1015,  778,  778,  778,  778,
     1016, 1017, 1018, 1019, 1020,  986, 1021, 1022, 1023, 1024,
     1025, 1026, 1027,  778,  778,  778,  778,  778,  778,  905,
      905,  905,  905,  905,  905,  905, 1031, 1032, 1033,  905,
      905,  905,  905, 1034, 1035, 1036, 1037, 1038, 1039, 1028,
     1029, 1040, 1041, 1042, 1043, 1044,  905,  905,  905,  905,
      905,  905,  906,  906,  906,  906,  906,  906,  906, 1030,

     1045, 1046,  907,  907,  907,  907, 1047,  102, 1048, 1049,
     1050, 1051,  103, 1052, 1053, 1054, 1055, 1056, 1057,  907,
      907,  907,  907,  907,  907, 1058, 1059, 1060, 1061, 1062,
      102, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070,  103,
      908,  908,  908,  908,  908,  908,  908, 1071, 1073, 1074,
      908,  908,  908,  908, 1075, 1076, 1077, 1078, 1072, 1079,
     1080, 1081, 1082, 1083, 1084, 1085, 1086,  908,  908,  908,
      908,  908,  908, 1007, 1007, 1007, 1007, 1007, 1007, 1007,
     1087, 1088, 1089, 1007, 1007, 1007, 1007, 1090, 1091, 1092,
     1093, 1094, 1005, 1095, 1097, 1097, 1099, 1100, 1101, 1102,

     1007, 1007, 1007, 1007, 1007, 1007, 1008, 1008, 1008, 1008,
     1008, 1008, 1008, 1103, 1104, 1105, 1009, 1009, 1009, 1009,
     1106,  102, 1107, 1108, 1109, 1110,  103, 1111, 1112, 1113,
     1114, 1115, 1117, 1009, 1009, 1009, 1009, 1009, 1009, 1118,
     1119, 1120, 1121, 1122,  102, 1123, 1124, 1125, 1126, 1127,
     1116, 1128, 1129,  103, 1010, 1010, 1010, 1010, 1010, 1010,
     1010, 1130, 1131, 1132, 1010, 1010, 1010, 1010, 1133, 1134,
     1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144,
     1145, 1010, 1010, 1010, 1010, 1010, 1010, 1096, 1096, 1096,
     1096, 1096, 1096, 1096, 1146, 1147, 1148, 1096, 1096, 1096,

     1096, 1149, 1150, 1151, 1152, 1153, 1154, 1155, 1156, 1157,
     1158, 1161, 1162, 1163, 1096, 1096, 1096, 1096, 1096, 1096,
     1097, 1164, 1098, 1098, 1098, 1098, 1098, 1098, 1098, 1159,
     1165, 1166, 1167, 1455, 1170, 1171, 1160,  102, 1172, 1173,
     1174, 1175,  103, 1176, 1177, 1178, 1179, 1180, 1181, 1182,
     1183, 1184, 1185, 1186, 1187, 1188, 1189, 1190, 1191, 1192,
      102, 1193, 1194, 1195, 1196, 1197, 1198, 1199, 1200,  103,
     1168, 1201,   91,   91,   91,   91,   91,   91,   91, 1202,
     1203, 1204,   91,   91,   91,   91, 1205, 1206, 1207, 1208,
     1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217,   91,

       91,   91,   91,   91,   91, 1218, 1159, 1219, 1220, 1221,
     1222, 1223, 1224, 1160, 1225, 1225, 1225, 1225, 1225, 1225,
     1225, 1227, 1228, 1229, 1225, 1225, 1225, 1225, 1230, 1231,
     1232, 1233, 1234, 1235, 1236, 1237, 1238, 1239, 1240, 1241,
     1242, 1225, 1225, 1225, 1225, 1225, 1225, 1243, 1244, 1245,
     1246, 1247, 1248, 1249, 1250, 1251, 1252, 1253, 1254, 1255,
     1256, 1257, 1258, 1259, 1260, 1261, 1262, 1263, 1264, 1265,
     1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273, 1273, 1273,
     1273, 1273, 1273, 1273, 1275, 1276, 1277, 1273, 1273, 1273,
     1273, 1278, 1279, 1280, 1281, 1282, 1283, 1284, 1285, 1286,

     1287, 1288, 1289, 1290, 1273, 1273, 1273, 1273, 1273, 1273,
     1291, 1292, 1293, 1294, 1295, 1296, 1297, 1298, 1299, 1300,
     1301, 1302, 1303, 1304, 1272, 1272, 1308, 1309, 1310, 1311,
     1312, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1320, 1321,
     1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1331, 1332,
     1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340, 1341, 1342,
     1343, 1344, 1345, 1346, 1347, 1348, 1350, 1351, 1352, 1353,
     1354, 1355, 1356, 1357, 1358, 1359, 1360, 1361, 1362, 1363,
     1368, 1369, 1370, 1371, 1372, 1305, 1306, 1306, 1306, 1306,
     1306, 1306, 1306, 1374, 1377, 1366, 1306, 1306, 1306, 1306,

     1367, 1378, 1379, 1380, 1381, 1382, 1383, 1386, 1387, 1388,
     1389, 1390, 1391, 1306, 1306, 1306, 1306, 1306, 1306, 1330,
     1330, 1330, 1330, 1330, 1330, 1330, 1393, 1394, 1395, 1330,
     1330, 1330, 1330, 1396, 1397, 1398, 1399, 1401, 1402, 1403,
     1404, 1407, 1408, 1409, 1410, 1413, 1330, 1330, 1330, 1330,
     1330, 1330, 1364, 1364, 1364, 1364, 1364, 1364, 1364, 1414,
     1415, 1418, 1364, 1364, 1364, 1364, 1419, 1420, 1422, 1423,
     1424, 1425, 1426, 1428, 1429, 1432,  258,  320,  478, 1364,
     1364, 1364, 1364, 1364, 1364, 1375, 1375, 1375, 1375, 1375,
     1375, 1375,  633, 1454,  777, 1375, 1375, 1375, 1375,   91,

      907, 1009, 1169, 1226, 1274, 1307, 1349, 1365, 1376, 1385,
     1400, 1406, 1375, 1375, 1375, 1375, 1375, 1375, 1384, 1384,
     1384, 1384, 1384, 1384, 1384, 1412, 1417, 1427, 1384, 1384,
     1384, 1384, 1431, 1434, 1436, 1438, 1440, 1442, 1444, 1446,
     1448, 1450, 1452,  849,  848, 1384, 1384, 1384, 1384, 1384,
     1384, 1392, 1392, 1392, 1392, 1392, 1392, 1392,  847,  846,
      845, 1392, 1392, 1392, 1392,  844,  843,  842,  841,  840,
      839,  838,  837,  836,  835,  834,  833,  832, 1392, 1392,
     1392, 1392, 1392, 1392, 1405, 1405, 1405, 1405, 1405, 1405,
     1405,  831,  830,  829, 1405, 1405, 1405, 1405,  828,  827,

      826,  825,  824,  823,  822,  821,  820,  819,  818,  817,
      816, 1405, 1405, 1405, 1405, 1405, 1405, 1411, 1411, 1411,
     1411, 1411, 1411, 1411,  815,  814,  813, 1411, 1411, 1411,
     1411,  812,  811,  810,  807,  806,  805,  804,  803,  802,
      801,  800,  799,  798, 1411, 1411, 1411, 1411, 1411, 1411,
     1416, 1416, 1416, 1416, 1416, 1416, 1416,  795,  794,  793,
     1416, 1416, 1416, 1416,  792,  791,  790,  789,  788,  787,
      786,  785,  784,  783,  782,  781,  780, 1416, 1416, 1416,
     1416, 1416, 1416, 1421, 1421, 1421, 1421, 1421, 1421, 1421,
      779,  774,  773, 1421, 1421, 1421, 1421,  772,  771,  770,

      769,  768,  767,  766,  765,  764,  763,  762,  761,  760,
     1421, 1421, 1421, 1421, 1421, 1421, 1430, 1430, 1430, 1430,
     1430, 1430, 1430,  759,  758,  757, 1430, 1430, 1430, 1430,
      756,  755,  754,  753,  752,  751,  750,  749,  748,  747,
      746,  745,  744, 1430, 1430, 1430, 1430, 1430, 1430, 1433,
     1433, 1433, 1433, 1433, 1433, 1433,  743,  742,  741, 1433,
     1433, 1433, 1433,  740,  739,  738,  735,  734,  733,  732,
      731,  730,  729,  728,  727,  726, 1433, 1433, 1433, 1433,
     1433, 1433, 1435, 1435, 1435, 1435, 1435, 1435, 1435,  725,
      724,  723, 1435, 1435, 1435, 1435,  722,  721,  718,  717,

      716,  714,  713,  712,  710,  709,  708,  707,  706, 1435,
     1435, 1435, 1435, 1435, 1435, 1437, 1437, 1437, 1437, 1437,
     1437, 1437,  705,  702,  701, 1437, 1437, 1437, 1437,  700,
      699,  696,  695,  694,  693,  692,  691,  690,  689,  688,
      687,  686, 1437, 1437, 1437, 1437, 1437, 1437, 1439, 1439,
     1439, 1439, 1439, 1439, 1439,  685,  684,  683, 1439, 1439,
     1439, 1439,  682,  681,  680,  679,  678,  677,  676,  675,
      671,  670,  669,  668,  667, 1439, 1439, 1439, 1439, 1439,
     1439, 1441, 1441, 1441, 1441, 1441, 1441, 1441,  666,  665,
      664, 1441, 1441, 1441, 1441,  663,  662,  661,  660,  659,

      657,  656,  655,  654,  651,  650,  590,  647, 1441, 1441,
     1441, 1441, 1441, 1441, 1443, 1443, 1443, 1443, 1443, 1443,
     1443,  646,  645,  644, 1443, 1443, 1443, 1443,  643,  642,
      641,  640,  639,  638,  637,  636,  635,  630,  629,  628,
      627, 1443, 1443, 1443, 1443, 1443, 1443, 1445, 1445, 1445,
     1445, 1445, 1445, 1445,  626,  625,  624, 1445, 1445, 1445,
     1445,  623,  622,  621,  620,  619,  618,  617,  616,  615,
      614,  613,  612,  611, 1445, 1445, 1445, 1445, 1445, 1445,
     1447, 1447, 1447, 1447, 1447, 1447, 1447,  610,  609,  608,
     1447, 1447, 1447, 1447,  607,  606,  605,  604,  603,  602,

      601,  600,  599,  598,  597,  596,  595, 1447, 1447, 1447,
     1447, 1447, 1447, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
      594,  593,  592, 1449, 1449, 1449, 1449,  591,  590,  589,
      588,  587,  586,  585,  584,  579,  578,  415,  577,  576,
     1449, 1449, 1449, 1449, 1449, 1449, 1451, 1451, 1451, 1451,
     1451, 1451, 1451,  575,  574,  573, 1451, 1451, 1451, 1451,
      572,  568,  567,  566,  565,  561,  560,  559,  558,  557,
      556,  555,  554, 1451, 1451, 1451, 1451, 1451, 1451, 1453,
     1453, 1453, 1453, 1453, 1453, 1453,  553,  552,  547, 1453,
     1453, 1453, 1453,  546,  545,  544,  543,  542,  541,  537,

      536,  535,  534,  533,  532,  531, 1453, 1453, 1453, 1453,
     1453, 1453,   83,   83,   83,   83,   85,   85,   85,   85,
       87,   87,   87,   87,   91,   91,   91,   91,   95,   95,
       95,   95,  110,  110,  247,  530,  247,  247,  248,  529,
      248,  248,  251,  528,  251,  251,  260,  260,  260,  260,
      873,  527,  873,  873, 1373,  525, 1373, 1373,  524,  523,
      522,  521,  520,  519,  518,  517,  516,  515,  514,  513,
      512,  511,  510,  509,  508,  507,  506,  505,  502,  500,
      496,  495,  494,  493,  492,  491,  490,  489,  488,  487,
      486,  485,  482,  481,  480,  475,  474,  473,  472,  471,

      470,  469,  468,  467,  466,  465,  464,  463,  462,  461,
      460,  459,  458,  457,  456,  455,  454,  453,  452,  451,
      450,  449,  448,  447,  446,  445,  444,  443,  442,  441,
      440,  439,  438,  437,  436,  435,  434,  433,  432,  431,
      430,  429,  428,  427,  307,  426,  425,  424,  423,  422,
     1455,  421,  417,  416,  415,  245,  244,  244,  414,  413,
      412,  411,  410,  409,  408,  406,  405,  404,  403,  400,
      391,  390,  389,  388,  387,  385,  384,  383,  382,  381,
      380,  376,  375,  369,  368,  363,  362,  361,  360,  359,
      358,  357,  356,  354,  351,  346,  344,  343,  340,  336,

      334,  333,  331,  330,  329,  326,  325,  322,  259,  317,
      316,  315,  314,  313,  310,  309,  305,  302,  301,  298,
      297,  296,  295,  294,  293,  292,  289,  288,  287,  286,
      285,  284,  283,  282,  281,  277,  274,  273,  272,  271,
      270,  269,  268,  145,  267,  125,  265,  264,  263,  259,
      254, 1455, 1455,  253,  252,  244,  169,  158,  157,  156,
      155,  154,  144,  135,  121,  115,  114,  113,  112,  111,
      107,   99,   98,   94,   90, 1455,   86,   86,   11, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,

     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455
    } ;

static const flex_int16_t yy_chk[2546] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    2,    4,   16,   18,
        2,    4,    5,    5,    5,    5,    5,    5,    5,    6,
        6,    6,    6,    6,    6,    6,   22,   23,   23,   35,
        9,   37,   10,   25,   25,   37,   40,  102,   18,   35,

       16,   37,   33,   38,   43,   33,   42,   38,   33,   41,
       41,  103,   42,   38,   43,   22,    2,    3,   33,  203,
       40,   33,  203,   45,  102,    3,    3,    3,    3,    3,
        3,    3,   46,   54,  103,    3,    3,    3,    3,    9,
       45,   10,   81,    3,  152,  160,   81,  152,   46,    9,
        3,   10,    3,    3,    3,    3,    3,    3,   53,    3,
        3,  152,   53,    3,    3,    3,    3,    3,  711,    3,
        3,    3,    3,    3,    3,   21,   21,   21,   21,   21,
       21,   21,   21,   55,   58,   36,   54,   55,   36,   47,
       47,   21,   36,   55,   47,  147,   21,   21,  160,   36,

       36,   47,   95,   80,   47,  147,   62,  712,   53,   80,
       62,   86,   63,   58,   21,   62,   72,   62,   63,   62,
       72,   63,   62,   21,   62,   68,   21,   56,   63,   68,
       72,   95,  155,   68,   77,   56,   56,   56,   56,   56,
       56,   56,  155,   86,   77,   56,   56,   56,   56,   68,
       66,   77,   91,  101,   66,  207,  207,   75,   66,   56,
       66,   75,   56,   56,   56,   56,   56,   56,   57,   57,
       57,   57,   57,   57,   57,  127,  713,   75,   57,   57,
       57,   57,  101,   57,   91,  112,  127,   64,   57,   57,
       64,   74,  208,  150,   64,   57,   57,   57,   57,   57,

       57,   64,   64,  208,   78,   74,   57,   64,   74,   78,
       74,   74,  150,  162,   78,   57,   78,  112,   57,   59,
       59,   59,   59,   59,   59,   59,   67,  181,  139,   59,
       59,   59,   59,   70,  181,   70,  139,   67,  338,   70,
       67,  338,  171,   67,  171,   70,   59,   59,   59,   59,
       59,   59,   65,   71,  714,   69,   65,   71,   69,   65,
       69,   73,   65,   73,   69,   69,  162,   71,   65,   79,
      116,   73,   71,  116,  178,   73,  116,   76,   76,  178,
       73,   73,   76,  116,   76,  129,  116,  159,  129,   76,
      174,  159,   76,  715,  129,   79,  183,  183,  174,   79,

       84,  185,  716,   79,  185,  174,  183,  183,  250,   79,
      185,   84,   84,   84,   84,   84,   84,   84,  100,  100,
      100,  100,  100,  100,  100,  188,  190,  209,  190,  190,
      210,  210,  188,  717,  209,  220,  220,  159,  165,  165,
      165,  165,  165,  165,  165,  190,  190,  192,  165,  165,
      165,  165,  234,  242,  718,  248,  341,  242,  250,  341,
      192,  260,  234,  665,  192,  165,  165,  165,  165,  165,
      165,  166,  166,  166,  166,  166,  166,  166,  194,  213,
      665,  166,  166,  166,  166,  402,  166,  248,  227,  325,
      213,  166,  194,  260,  204,  227,  213,  402,  166,  166,

      166,  166,  166,  166,  325,  365,  204,  227,  365,  166,
      204,  339,  339,  343,  204,  407,  719,  407,  166,  168,
      168,  168,  168,  168,  168,  168,  408,  408,  343,  168,
      168,  168,  168,  378,  378,  720,  378,  226,  420,  401,
      721,  229,  226,  226,  229,  401,  168,  168,  168,  168,
      168,  168,  226,  226,  246,  226,  226,  258,  229,  419,
      504,  420,  258,  504,  722,  246,  246,  246,  246,  246,
      246,  246,  255,  255,  255,  255,  255,  255,  255,  388,
      258,  494,  499,  494,  255,  544,  419,  544,  388,  258,
      556,  549,  388,  549,  556,  499,  388,  520,  723,  724,

      520,  564,  549,  255,  318,  318,  318,  318,  318,  318,
      318,  725,  564,  520,  318,  318,  318,  318,  418,  418,
      726,  418,  418,  418,  418,  418,  418,  418,  727,  560,
      728,  318,  318,  318,  318,  318,  318,  319,  319,  319,
      319,  319,  319,  319,  560,  653,  729,  319,  319,  319,
      319,  586,  319,  730,  586,  731,  732,  319,  733,  653,
      734,  735,  736,  737,  319,  319,  319,  319,  319,  319,
      738,  739,  740,  741,  742,  319,  580,  580,  580,  580,
      580,  580,  580,  743,  319,  321,  321,  321,  321,  321,
      321,  321,  744,  745,  746,  321,  321,  321,  321,  748,

      750,  751,  753,  754,  755,  757,  759,  760,  761,  762,
      766,  769,  321,  321,  321,  321,  321,  321,  476,  476,
      476,  476,  476,  476,  476,  770,  771,  772,  476,  476,
      476,  476,  773,  774,  779,  780,  781,  782,  783,  784,
      785,  786,  787,  788,  789,  476,  476,  476,  476,  476,
      476,  477,  477,  477,  477,  477,  477,  477,  790,  791,
      792,  477,  477,  477,  477,  793,  477,  794,  795,  796,
      797,  477,  800,  801,  802,  803,  804,  805,  477,  477,
      477,  477,  477,  477,  807,  808,  809,  810,  811,  477,
      812,  813,  814,  815,  816,  817,  819,  822,  477,  479,

      479,  479,  479,  479,  479,  479,  823,  824,  825,  479,
      479,  479,  479,  581,  581,  581,  581,  581,  581,  581,
      826,  827,  828,  829,  830,  581,  479,  479,  479,  479,
      479,  479,  832,  833,  834,  835,  837,  838,  839,  840,
      841,  842,  844,  845,  581,  631,  631,  631,  631,  631,
      631,  631,  846,  848,  849,  631,  631,  631,  631,  850,
      851,  853,  854,  856,  858,  860,  861,  862,  863,  865,
      866,  867,  631,  631,  631,  631,  631,  631,  632,  632,
      632,  632,  632,  632,  632,  869,  870,  871,  632,  632,
      632,  632,  872,  632,  874,  875,  876,  878,  632,  879,

      882,  885,  886,  887,  888,  632,  632,  632,  632,  632,
      632,  889,  890,  893,  894,  895,  632,  896,  897,  900,
      902,  903,  904,  909,  910,  632,  634,  634,  634,  634,
      634,  634,  634,  911,  912,  913,  634,  634,  634,  634,
      914,  915,  916,  917,  918,  874,  919,  920,  922,  923,
      924,  925,  926,  634,  634,  634,  634,  634,  634,  775,
      775,  775,  775,  775,  775,  775,  929,  930,  932,  775,
      775,  775,  775,  933,  934,  935,  936,  937,  938,  928,
      928,  939,  940,  941,  942,  943,  775,  775,  775,  775,
      775,  775,  776,  776,  776,  776,  776,  776,  776,  928,

      944,  945,  776,  776,  776,  776,  946,  776,  948,  949,
      950,  951,  776,  954,  955,  956,  957,  958,  959,  776,
      776,  776,  776,  776,  776,  960,  961,  962,  963,  965,
      776,  966,  967,  968,  969,  970,  971,  973,  974,  776,
      778,  778,  778,  778,  778,  778,  778,  975,  976,  977,
      778,  778,  778,  778,  978,  979,  980,  983,  975,  984,
      985,  986,  987,  988,  989,  990,  991,  778,  778,  778,
      778,  778,  778,  905,  905,  905,  905,  905,  905,  905,
      993,  994,  996,  905,  905,  905,  905,  997,  998,  999,
     1002, 1004, 1005, 1006, 1009, 1010, 1011, 1012, 1013, 1015,

      905,  905,  905,  905,  905,  905,  906,  906,  906,  906,
      906,  906,  906, 1016, 1017, 1018,  906,  906,  906,  906,
     1019,  906, 1021, 1022, 1024, 1025,  906, 1026, 1027, 1028,
     1029, 1030, 1031,  906,  906,  906,  906,  906,  906, 1032,
     1033, 1034, 1037, 1038,  906, 1039, 1040, 1041, 1042, 1044,
     1030, 1045, 1046,  906,  908,  908,  908,  908,  908,  908,
      908, 1047, 1048, 1049,  908,  908,  908,  908, 1051, 1052,
     1053, 1054, 1055, 1056, 1060, 1061, 1062, 1063, 1065, 1066,
     1067,  908,  908,  908,  908,  908,  908, 1007, 1007, 1007,
     1007, 1007, 1007, 1007, 1068, 1070, 1071, 1007, 1007, 1007,

     1007, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080,
     1081, 1085, 1087, 1088, 1007, 1007, 1007, 1007, 1007, 1007,
     1008, 1092, 1008, 1008, 1008, 1008, 1008, 1008, 1008, 1082,
     1093, 1094, 1095, 1098, 1099, 1100, 1082, 1008, 1102, 1105,
     1106, 1108, 1008, 1109, 1110, 1111, 1112, 1113, 1114, 1115,
     1116, 1117, 1118, 1120, 1121, 1122, 1123, 1124, 1125, 1126,
     1008, 1127, 1128, 1129, 1130, 1131, 1133, 1134, 1135, 1008,
     1096, 1136, 1096, 1096, 1096, 1096, 1096, 1096, 1096, 1137,
     1138, 1139, 1096, 1096, 1096, 1096, 1140, 1141, 1143, 1144,
     1145, 1146, 1147, 1149, 1151, 1153, 1154, 1156, 1157, 1096,

     1096, 1096, 1096, 1096, 1096, 1158, 1159, 1161, 1162, 1163,
     1165, 1166, 1167, 1159, 1168, 1168, 1168, 1168, 1168, 1168,
     1168, 1170, 1171, 1174, 1168, 1168, 1168, 1168, 1176, 1178,
     1179, 1180, 1181, 1182, 1183, 1184, 1185, 1186, 1187, 1188,
     1189, 1168, 1168, 1168, 1168, 1168, 1168, 1190, 1191, 1192,
     1193, 1193, 1195, 1196, 1198, 1199, 1200, 1201, 1202, 1203,
     1204, 1205, 1207, 1208, 1210, 1211, 1212, 1213, 1214, 1215,
     1216, 1218, 1220, 1221, 1222, 1223, 1224, 1225, 1225, 1225,
     1225, 1225, 1225, 1225, 1228, 1231, 1232, 1225, 1225, 1225,
     1225, 1234, 1235, 1236, 1237, 1239, 1242, 1243, 1244, 1246,

     1247, 1248, 1249, 1250, 1225, 1225, 1225, 1225, 1225, 1225,
     1251, 1252, 1253, 1255, 1256, 1257, 1258, 1261, 1264, 1265,
     1266, 1267, 1268, 1269, 1271, 1272, 1276, 1277, 1278, 1280,
     1281, 1282, 1283, 1285, 1286, 1287, 1288, 1289, 1290, 1293,
     1294, 1295, 1296, 1298, 1300, 1302, 1304, 1305, 1307, 1308,
     1309, 1310, 1312, 1313, 1314, 1315, 1316, 1319, 1320, 1323,
     1324, 1325, 1326, 1327, 1329, 1330, 1332, 1333, 1334, 1335,
     1336, 1337, 1339, 1340, 1341, 1343, 1344, 1345, 1346, 1347,
     1353, 1356, 1358, 1360, 1361, 1272, 1273, 1273, 1273, 1273,
     1273, 1273, 1273, 1363, 1366, 1350, 1273, 1273, 1273, 1273,

     1350, 1367, 1368, 1369, 1370, 1371, 1374, 1377, 1378, 1379,
     1381, 1382, 1383, 1273, 1273, 1273, 1273, 1273, 1273, 1306,
     1306, 1306, 1306, 1306, 1306, 1306, 1385, 1386, 1387, 1306,
     1306, 1306, 1306, 1388, 1389, 1390, 1392, 1394, 1395, 1396,
     1398, 1401, 1402, 1403, 1404, 1407, 1306, 1306, 1306, 1306,
     1306, 1306, 1348, 1348, 1348, 1348, 1348, 1348, 1348, 1408,
     1409, 1413, 1348, 1348, 1348, 1348, 1414, 1415, 1417, 1418,
     1419, 1420, 1421, 1423, 1425, 1428, 1465, 1467, 1468, 1348,
     1348, 1348, 1348, 1348, 1348, 1364, 1364, 1364, 1364, 1364,
     1364, 1364, 1469, 1453, 1470, 1364, 1364, 1364, 1364, 1453,

     1472, 1473, 1474, 1475, 1476, 1477, 1478, 1479, 1481, 1482,
     1483, 1484, 1364, 1364, 1364, 1364, 1364, 1364, 1375, 1375,
     1375, 1375, 1375, 1375, 1375, 1485, 1486, 1487, 1375, 1375,
     1375, 1375, 1488, 1489, 1490, 1491, 1492, 1493, 1494, 1495,
     1496, 1497, 1498,  710,  709, 1375, 1375, 1375, 1375, 1375,
     1375, 1384, 1384, 1384, 1384, 1384, 1384, 1384,  707,  705,
      704, 1384, 1384, 1384, 1384,  703,  702,  701,  700,  699,
      698,  697,  696,  695,  694,  693,  692,  691, 1384, 1384,
     1384, 1384, 1384, 1384, 1399, 1399, 1399, 1399, 1399, 1399,
     1399,  690,  689,  688, 1399, 1399, 1399, 1399,  686,  684,

      683,  682,  681,  680,  679,  678,  677,  676,  675,  674,
      673, 1399, 1399, 1399, 1399, 1399, 1399, 1405, 1405, 1405,
     1405, 1405, 1405, 1405,  672,  671,  670, 1405, 1405, 1405,
     1405,  668,  667,  666,  663,  662,  661,  660,  659,  658,
      657,  656,  655,  654, 1405, 1405, 1405, 1405, 1405, 1405,
     1411, 1411, 1411, 1411, 1411, 1411, 1411,  652,  651,  650,
     1411, 1411, 1411, 1411,  649,  648,  647,  646,  645,  644,
      643,  642,  641,  640,  639,  638,  636, 1411, 1411, 1411,
     1411, 1411, 1411, 1416, 1416, 1416, 1416, 1416, 1416, 1416,
      635,  630,  629, 1416, 1416, 1416, 1416,  628,  627,  626,

      624,  623,  622,  621,  620,  618,  617,  615,  614,  613,
     1416, 1416, 1416, 1416, 1416, 1416, 1426, 1426, 1426, 1426,
     1426, 1426, 1426,  612,  611,  610, 1426, 1426, 1426, 1426,
      609,  608,  607,  606,  605,  604,  602,  600,  599,  598,
      597,  596,  595, 1426, 1426, 1426, 1426, 1426, 1426, 1430,
     1430, 1430, 1430, 1430, 1430, 1430,  594,  593,  591, 1430,
     1430, 1430, 1430,  589,  588,  587,  585,  579,  578,  577,
      576,  575,  574,  573,  571,  570, 1430, 1430, 1430, 1430,
     1430, 1430, 1433, 1433, 1433, 1433, 1433, 1433, 1433,  569,
      568,  567, 1433, 1433, 1433, 1433,  566,  565,  563,  562,

      561,  559,  558,  557,  555,  554,  553,  552,  551, 1433,
     1433, 1433, 1433, 1433, 1433, 1435, 1435, 1435, 1435, 1435,
     1435, 1435,  550,  548,  547, 1435, 1435, 1435, 1435,  546,
      545,  543,  542,  541,  540,  539,  538,  537,  536,  535,
      534,  533, 1435, 1435, 1435, 1435, 1435, 1435, 1437, 1437,
     1437, 1437, 1437, 1437, 1437,  532,  531,  530, 1437, 1437,
     1437, 1437,  528,  527,  526,  525,  524,  523,  522,  521,
      519,  517,  516,  515,  514, 1437, 1437, 1437, 1437, 1437,
     1437, 1439, 1439, 1439, 1439, 1439, 1439, 1439,  513,  512,
      511, 1439, 1439, 1439, 1439,  510,  509,  508,  506,  505,

      503,  502,  501,  500,  498,  497,  495,  493, 1439, 1439,
     1439, 1439, 1439, 1439, 1441, 1441, 1441, 1441, 1441, 1441,
     1441,  492,  490,  489, 1441, 1441, 1441, 1441,  488,  487,
      486,  485,  484,  483,  482,  481,  480,  475,  474,  473,
      472, 1441, 1441, 1441, 1441, 1441, 1441, 1443, 1443, 1443,
     1443, 1443, 1443, 1443,  470,  469,  468, 1443, 1443, 1443,
     1443,  467,  466,  465,  464,  463,  462,  461,  460,  459,
      458,  457,  456,  455, 1443, 1443, 1443, 1443, 1443, 1443,
     1445, 1445, 1445, 1445, 1445, 1445, 1445,  454,  453,  452,
     1445, 1445, 1445, 1445,  450,  449,  448,  447,  446,  445,

      444,  443,  442,  440,  439,  438,  437, 1445, 1445, 1445,
     1445, 1445, 1445, 1447, 1447, 1447, 1447, 1447, 1447, 1447,
      436,  435,  434, 1447, 1447, 1447, 1447,  433,  431,  428,
      427,  426,  425,  423,  421,  417,  416,  415,  414,  413,
     1447, 1447, 1447, 1447, 1447, 1447, 1449, 1449, 1449, 1449,
     1449, 1449, 1449,  412,  411,  410, 1449, 1449, 1449, 1449,
      409,  406,  405,  404,  403,  400,  399,  398,  397,  396,
      395,  394,  392, 1449, 1449, 1449, 1449, 1449, 1449, 1451,
     1451, 1451, 1451, 1451, 1451, 1451,  391,  390,  387, 1451,
     1451, 1451, 1451,  386,  385,  383,  382,  380,  379,  377,

      376,  374,  373,  372,  371,  370, 1451, 1451, 1451, 1451,
     1451, 1451, 1456, 1456, 1456, 1456, 1457, 1457, 1457, 1457,
     1458, 1458, 1458, 1458, 1459, 1459, 1459, 1459, 1460, 1460,
     1460, 1460, 1461, 1461, 1462,  369, 1462, 1462, 1463,  368,
     1463, 1463, 1464,  367, 1464, 1464, 1466, 1466, 1466, 1466,
     1471,  366, 1471, 1471, 1480,  364, 1480, 1480,  363,  362,
      361,  360,  359,  358,  357,  356,  355,  354,  353,  352,
      351,  350,  349,  348,  347,  346,  345,  344,  342,  340,
      337,  336,  335,  334,  333,  332,  331,  330,  329,  328,
      327,  326,  324,  323,  322,  317,  316,  315,  314,  313,

      312,  311,  310,  309,  308,  307,  306,  305,  304,  303,
      302,  301,  300,  299,  298,  297,  296,  295,  294,  293,
      292,  291,  290,  289,  288,  287,  286,  285,  284,  283,
      282,  281,  280,  279,  278,  277,  276,  275,  274,  273,
      272,  271,  270,  269,  268,  267,  266,  265,  264,  263,
      262,  259,  253,  252,  249,  245,  244,  243,  241,  240,
      239,  238,  237,  236,  235,  233,  232,  231,  230,  228,
      225,  224,  223,  222,  221,  219,  218,  217,  216,  215,
      214,  212,  211,  206,  205,  202,  201,  200,  199,  198,
      197,  196,  195,  193,  191,  189,  187,  186,  184,  182,

      180,  179,  177,  176,  175,  173,  172,  170,  169,  164,
      163,  161,  157,  156,  154,  153,  151,  149,  148,  146,
      145,  144,  143,  142,  141,  140,  138,  137,  136,  135,
      134,  133,  132,  131,  130,  128,  126,  125,  124,  123,
      122,  121,  120,  119,  118,  117,  115,  114,  113,  111,
       99,   97,   93,   89,   88,   82,   60,   52,   51,   50,
       49,   48,   44,   39,   34,   31,   30,   29,   28,   27,
       24,   20,   19,   17,   15,   11,    8,    7, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,

     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455, 1455,
     1455, 1455, 1455, 1455, 1455
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
#line 1 "tools/widl/parser.l"
/* -*-C-*-
 * IDL Compiler
 *
 * Copyright 2002 Ove Kaaven
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */
#define YY_NO_INPUT 1




#line 38 "tools/widl/parser.l"

#include "config.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <assert.h>
#include <errno.h>
#include <limits.h>
#define YY_NO_UNISTD_H

#include "widl.h"
#include "utils.h"
#include "parser.h"
#include "wpp_private.h"

#define YYerror PARSER_error
#define YYSTYPE PARSER_STYPE
#define YYLTYPE PARSER_LTYPE
#define YYUNDEF PARSER_UNDEF
#define yyerror parser_error

#include "parser.tab.h"

static void reset_location( struct location *where, const char *input_name );
static void update_location( struct location *where, const char *yytext );
static void end_of_line( struct location *where );

#define YY_USER_INIT    reset_location( yylloc, input_name )
#define YY_USER_ACTION  update_location( yylloc, yytext );

static void switch_to_acf(void);

static warning_list_t *disabled_warnings = NULL;

struct import_state
{
    YY_BUFFER_STATE buffer;
    char *input_name;
    struct location where;
    struct list entry;
};
static struct list import_stack = LIST_INIT( import_stack );
int parse_only = 0;

struct import
{
    const char *name;
    struct list entry;
};
static struct list imports = LIST_INIT( imports );
static struct location previous_location;

/* converts an integer in string form to an unsigned long and prints an error
 * on overflow */
static unsigned int xstrtoul(const char *nptr, char **endptr, int base)
{
    unsigned long val;

    errno = 0;
    val = strtoul(nptr, endptr, base);
    if ((val == ULONG_MAX && errno == ERANGE) || ((unsigned int)val != val))
        error_loc("integer constant %s is too large\n", nptr);
    return val;
}

static int token_uuid( const char *str, YYSTYPE *yylval )
{
    struct uuid *uuid;
    char tmp[3] = {0};

    if (*str == '\"') str++;

    uuid = xmalloc( sizeof(*uuid) );
    uuid->Data1 = strtoul( str , NULL, 16 );
    uuid->Data2 = strtoul( str + 9, NULL, 16 );
    uuid->Data3 = strtoul( str + 14, NULL, 16 );
    memcpy( tmp, str + 19, 2 );
    uuid->Data4[0] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 21, 2 );
    uuid->Data4[1] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 24, 2 );
    uuid->Data4[2] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 26, 2 );
    uuid->Data4[3] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 28, 2 );
    uuid->Data4[4] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 30, 2 );
    uuid->Data4[5] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 32, 2 );
    uuid->Data4[6] = strtoul( tmp, NULL, 16 );
    memcpy( tmp, str + 34, 2 );
    uuid->Data4[7] = strtoul( tmp, NULL, 16 );

    yylval->uuid = uuid;
    return aUUID;
}

static int token_str( int token, const char *str, YYSTYPE *yylval )
{
    char *tmp = xstrdup( str );

    if (token == aWSTRING || token == aSTRING || token == aSQSTRING)
    {
        char *src, *dst;
        src = dst = ++tmp; /* skip first quote */
        while (*src)
        {
            if (*src == '\\') src++;
            *dst++ = *src++;
        }
        dst[-1] = 0; /* strip last quote */
    }

    yylval->str = tmp;
    return token;
}

static int token_num( int token, const char *yytext, YYSTYPE *yylval )
{
    yylval->num = xstrtoul( yytext, NULL, 0 );
    return token;
}

static int token_ident( const char *str, YYSTYPE *yylval )
{
    return token_str( is_type( str ) ? aKNOWNTYPE : aIDENTIFIER, str, yylval );
}

static int token_winrt( int token, const char *str, YYSTYPE *yylval )
{
    if (winrt_mode) return token;
    return token_ident( str, yylval );
}

static void winrt_enable( int ns_prefix )
{
    if (!list_empty( &import_stack ) && !winrt_mode) error_loc( "WinRT IDL file imported in non-winrt mode.\n" );

    use_abi_namespace = ns_prefix;
    winrt_mode = TRUE;
}

#line 1948 "tools/widl/parser.yy.c"
/*
 **************************************************************************
 * The flexer starts here
 **************************************************************************
 */
#line 1954 "tools/widl/parser.yy.c"

#define INITIAL 0
#define ATTR 1
#define PP_LINE 2
#define PP_FILE 3
#define PP_PRAGMA 4

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals ( void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( void );

int yyget_debug ( void );

void yyset_debug ( int debug_flag  );

YY_EXTRA_TYPE yyget_extra ( void );

void yyset_extra ( YY_EXTRA_TYPE user_defined  );

FILE *yyget_in ( void );

void yyset_in  ( FILE * _in_str  );

FILE *yyget_out ( void );

void yyset_out  ( FILE * _out_str  );

			int yyget_leng ( void );

char *yyget_text ( void );

int yyget_lineno ( void );

void yyset_lineno ( int _line_number  );

YYSTYPE * yyget_lval ( void );

void yyset_lval ( YYSTYPE * yylval_param  );

       YYLTYPE *yyget_lloc ( void );
    
        void yyset_lloc ( YYLTYPE * yylloc_param  );
    
/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( void );
#else
extern int yywrap ( void );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( void );
#else
static int input ( void );
#endif

#endif

        static int yy_start_stack_ptr = 0;
        static int yy_start_stack_depth = 0;
        static int *yy_start_stack = NULL;
    
    static void yy_push_state ( int _new_state );
    
    static void yy_pop_state ( void );
    
/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param );

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param )
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	if ( yyleng > 0 ) \
		YY_CURRENT_BUFFER_LVALUE->yy_at_bol = \
				(yytext[yyleng - 1] == '\n'); \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
        YYSTYPE * yylval;
    
        YYLTYPE * yylloc;
    
    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
#line 189 "tools/widl/parser.l"

#line 2204 "tools/widl/parser.yy.c"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);
		yy_current_state += YY_AT_BOL();
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 1456 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_current_state != 1455 );
		yy_cp = (yy_last_accepting_cpos);
		yy_current_state = (yy_last_accepting_state);

yy_find_action:
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 9;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 191 "tools/widl/parser.l"
{
                                                    yy_pop_state();
                                                    yylloc->first_line -= 1;
                                                    return tCPPQUOTE;
                                                }
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 196 "tools/widl/parser.l"
{
                                                    yy_pop_state();
                                                    yylloc->first_line -= 1;
                                                    winrt_enable( TRUE );
                                                }
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 201 "tools/widl/parser.l"
{
                                                    yy_pop_state();
                                                    yylloc->first_line -= 1;
                                                    winrt_enable( FALSE );
                                                }
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 206 "tools/widl/parser.l"
{
                                                    yy_pop_state();
                                                    yylloc->first_line -= 1;
                                                    return token_str( aPRAGMA, yytext, yylval );
                                                }
	YY_BREAK

case 5:
YY_RULE_SETUP
#line 212 "tools/widl/parser.l"
{
                                                    yylloc->first_line = strtoul( yytext, NULL, 10 ) - 1;
                                                    yylloc->last_line = yylloc->first_line;
                                                    yy_pop_state();
                                                    yy_push_state(PP_FILE);
                                                }
	YY_BREAK
case 6:
/* rule 6 can match eol */
YY_RULE_SETUP
#line 218 "tools/widl/parser.l"
{
                                                    input_name = xstrdup( yytext + 1 );
                                                    *strchr( input_name, '"' ) = 0;
                                                    yylloc->input_name = input_name;
                                                }
	YY_BREAK
case 7:
/* rule 7 can match eol */
YY_RULE_SETUP
#line 223 "tools/widl/parser.l"
{ yy_pop_state(); }
	YY_BREAK

case 8:
YY_RULE_SETUP
#line 226 "tools/widl/parser.l"
{ yy_pop_state(); return ']'; }
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 228 "tools/widl/parser.l"
{ return token_uuid( yytext, yylval ); }
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 229 "tools/widl/parser.l"
{ return token_winrt( tACTIVATABLE, yytext, yylval ); }
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 230 "tools/widl/parser.l"
{ return tAGGREGATABLE; }
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 231 "tools/widl/parser.l"
{ return token_winrt( tAGILE, yytext, yylval ); }
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 232 "tools/widl/parser.l"
{ return tALLNODES; }
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 233 "tools/widl/parser.l"
{ return tALLOCATE; }
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 234 "tools/widl/parser.l"
{ return tANNOTATION; }
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 235 "tools/widl/parser.l"
{ return tAPARTMENT; }
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 236 "tools/widl/parser.l"
{ return tAPPOBJECT; }
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 237 "tools/widl/parser.l"
{ return tASYNC; }
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 238 "tools/widl/parser.l"
{ return tASYNCUUID; }
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 239 "tools/widl/parser.l"
{ return tAUTOHANDLE; }
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 240 "tools/widl/parser.l"
{ return tBINDABLE; }
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 241 "tools/widl/parser.l"
{ return tBOTH; }
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 242 "tools/widl/parser.l"
{ return tBROADCAST; }
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 243 "tools/widl/parser.l"
{ return tBYTECOUNT; }
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 244 "tools/widl/parser.l"
{ return tCALLAS; }
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 245 "tools/widl/parser.l"
{ return tCALLBACK; }
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 246 "tools/widl/parser.l"
{ return tCODE; }
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 247 "tools/widl/parser.l"
{ return tCOMMSTATUS; }
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 248 "tools/widl/parser.l"
{ return token_winrt( tCOMPOSABLE, yytext, yylval ); }
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 249 "tools/widl/parser.l"
{ return tCONTEXTHANDLE; }
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 250 "tools/widl/parser.l"
{ return tCONTEXTHANDLENOSERIALIZE; }
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 251 "tools/widl/parser.l"
{ return tCONTEXTHANDLENOSERIALIZE; }
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 252 "tools/widl/parser.l"
{ return token_winrt( tCONTRACT, yytext, yylval ); }
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 253 "tools/widl/parser.l"
{ return token_winrt( tCONTRACTVERSION, yytext, yylval ); }
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 254 "tools/widl/parser.l"
{ return tCONTROL; }
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 255 "tools/widl/parser.l"
{ return tCUSTOM; }
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 256 "tools/widl/parser.l"
{ return tDECODE; }
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 257 "tools/widl/parser.l"
{ return tDEFAULTBIND; }
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 258 "tools/widl/parser.l"
{ return tDEFAULTCOLLELEM; }
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 259 "tools/widl/parser.l"
{ return tDEFAULTVALUE; }
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 260 "tools/widl/parser.l"
{ return tDEFAULTVTABLE; }
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 261 "tools/widl/parser.l"
{ return tDISABLECONSISTENCYCHECK; }
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 262 "tools/widl/parser.l"
{ return tDISPLAYBIND; }
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 263 "tools/widl/parser.l"
{ return tDLLNAME; }
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 264 "tools/widl/parser.l"
{ return tDONTFREE; }
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 265 "tools/widl/parser.l"
{ return tDUAL; }
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 266 "tools/widl/parser.l"
{ return tENABLEALLOCATE; }
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 267 "tools/widl/parser.l"
{ return tENCODE; }
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 268 "tools/widl/parser.l"
{ return tENDPOINT; }
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 269 "tools/widl/parser.l"
{ return tENTRY; }
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 270 "tools/widl/parser.l"
{ return token_winrt( tEVENTADD, yytext, yylval ); }
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 271 "tools/widl/parser.l"
{ return token_winrt( tEVENTREMOVE, yytext, yylval ); }
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 272 "tools/widl/parser.l"
{ return token_winrt( tEXCLUSIVETO, yytext, yylval ); }
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 273 "tools/widl/parser.l"
{ return tEXPLICITHANDLE; }
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 274 "tools/widl/parser.l"
{ return tFAULTSTATUS; }
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 275 "tools/widl/parser.l"
{ return token_winrt( tFLAGS, yytext, yylval ); }
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 276 "tools/widl/parser.l"
{ return tFORCEALLOCATE; }
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 277 "tools/widl/parser.l"
{ return tFREE; }
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 278 "tools/widl/parser.l"
{ return tHANDLE; }
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 279 "tools/widl/parser.l"
{ return tHELPCONTEXT; }
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 280 "tools/widl/parser.l"
{ return tHELPFILE; }
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 281 "tools/widl/parser.l"
{ return tHELPSTRING; }
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 282 "tools/widl/parser.l"
{ return tHELPSTRINGCONTEXT; }
	YY_BREAK
case 64:
YY_RULE_SETUP
#line 283 "tools/widl/parser.l"
{ return tHELPSTRINGDLL; }
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 284 "tools/widl/parser.l"
{ return tHIDDEN; }
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 285 "tools/widl/parser.l"
{ return tID; }
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 286 "tools/widl/parser.l"
{ return tIDEMPOTENT; }
	YY_BREAK
case 68:
YY_RULE_SETUP
#line 287 "tools/widl/parser.l"
{ return tIGNORE; }
	YY_BREAK
case 69:
YY_RULE_SETUP
#line 288 "tools/widl/parser.l"
{ return tIIDIS; }
	YY_BREAK
case 70:
YY_RULE_SETUP
#line 289 "tools/widl/parser.l"
{ return tIMMEDIATEBIND; }
	YY_BREAK
case 71:
YY_RULE_SETUP
#line 290 "tools/widl/parser.l"
{ return tIMPLICITHANDLE; }
	YY_BREAK
case 72:
YY_RULE_SETUP
#line 291 "tools/widl/parser.l"
{ return tIN; }
	YY_BREAK
case 73:
YY_RULE_SETUP
#line 292 "tools/widl/parser.l"
{ return tIN_LINE; }
	YY_BREAK
case 74:
YY_RULE_SETUP
#line 293 "tools/widl/parser.l"
{ return tINPUTSYNC; }
	YY_BREAK
case 75:
YY_RULE_SETUP
#line 294 "tools/widl/parser.l"
{ return tLCID; }
	YY_BREAK
case 76:
YY_RULE_SETUP
#line 295 "tools/widl/parser.l"
{ return tLENGTHIS; }
	YY_BREAK
case 77:
YY_RULE_SETUP
#line 296 "tools/widl/parser.l"
{ return tLICENSED; }
	YY_BREAK
case 78:
YY_RULE_SETUP
#line 297 "tools/widl/parser.l"
{ return tLOCAL; }
	YY_BREAK
case 79:
YY_RULE_SETUP
#line 298 "tools/widl/parser.l"
{ return token_winrt( tMARSHALINGBEHAVIOR, yytext, yylval ); }
	YY_BREAK
case 80:
YY_RULE_SETUP
#line 299 "tools/widl/parser.l"
{ return tMAYBE; }
	YY_BREAK
case 81:
YY_RULE_SETUP
#line 300 "tools/widl/parser.l"
{ return tMESSAGE; }
	YY_BREAK
case 82:
YY_RULE_SETUP
#line 301 "tools/widl/parser.l"
{ return tMTA; }
	YY_BREAK
case 83:
YY_RULE_SETUP
#line 302 "tools/widl/parser.l"
{ return tNEUTRAL; }
	YY_BREAK
case 84:
YY_RULE_SETUP
#line 303 "tools/widl/parser.l"
{ return tNOCODE; }
	YY_BREAK
case 85:
YY_RULE_SETUP
#line 304 "tools/widl/parser.l"
{ return tNONBROWSABLE; }
	YY_BREAK
case 86:
YY_RULE_SETUP
#line 305 "tools/widl/parser.l"
{ return tNONCREATABLE; }
	YY_BREAK
case 87:
YY_RULE_SETUP
#line 306 "tools/widl/parser.l"
{ return token_winrt( tNONE, yytext, yylval ); }
	YY_BREAK
case 88:
YY_RULE_SETUP
#line 307 "tools/widl/parser.l"
{ return tNONEXTENSIBLE; }
	YY_BREAK
case 89:
YY_RULE_SETUP
#line 308 "tools/widl/parser.l"
{ return tNOTIFY; }
	YY_BREAK
case 90:
YY_RULE_SETUP
#line 309 "tools/widl/parser.l"
{ return tNOTIFYFLAG; }
	YY_BREAK
case 91:
YY_RULE_SETUP
#line 310 "tools/widl/parser.l"
{ return tOBJECT; }
	YY_BREAK
case 92:
YY_RULE_SETUP
#line 311 "tools/widl/parser.l"
{ return tODL; }
	YY_BREAK
case 93:
YY_RULE_SETUP
#line 312 "tools/widl/parser.l"
{ return tOLEAUTOMATION; }
	YY_BREAK
case 94:
YY_RULE_SETUP
#line 313 "tools/widl/parser.l"
{ return tOPTIMIZE; }
	YY_BREAK
case 95:
YY_RULE_SETUP
#line 314 "tools/widl/parser.l"
{ return tOPTIONAL; }
	YY_BREAK
case 96:
YY_RULE_SETUP
#line 315 "tools/widl/parser.l"
{ return tOUT; }
	YY_BREAK
case 97:
YY_RULE_SETUP
#line 316 "tools/widl/parser.l"
{ return tOVERLOAD; }
	YY_BREAK
case 98:
YY_RULE_SETUP
#line 317 "tools/widl/parser.l"
{ return tPARTIALIGNORE; }
	YY_BREAK
case 99:
YY_RULE_SETUP
#line 318 "tools/widl/parser.l"
{ return tPOINTERDEFAULT; }
	YY_BREAK
case 100:
YY_RULE_SETUP
#line 319 "tools/widl/parser.l"
{ return tPROGID; }
	YY_BREAK
case 101:
YY_RULE_SETUP
#line 320 "tools/widl/parser.l"
{ return tPROPGET; }
	YY_BREAK
case 102:
YY_RULE_SETUP
#line 321 "tools/widl/parser.l"
{ return tPROPPUT; }
	YY_BREAK
case 103:
YY_RULE_SETUP
#line 322 "tools/widl/parser.l"
{ return tPROPPUTREF; }
	YY_BREAK
case 104:
YY_RULE_SETUP
#line 323 "tools/widl/parser.l"
{ return tPROTECTED; }
	YY_BREAK
case 105:
YY_RULE_SETUP
#line 324 "tools/widl/parser.l"
{ return tPROXY; }
	YY_BREAK
case 106:
YY_RULE_SETUP
#line 325 "tools/widl/parser.l"
{ return tPTR; }
	YY_BREAK
case 107:
YY_RULE_SETUP
#line 326 "tools/widl/parser.l"
{ return tPUBLIC; }
	YY_BREAK
case 108:
YY_RULE_SETUP
#line 327 "tools/widl/parser.l"
{ return tRANGE; }
	YY_BREAK
case 109:
YY_RULE_SETUP
#line 328 "tools/widl/parser.l"
{ return tREADONLY; }
	YY_BREAK
case 110:
YY_RULE_SETUP
#line 329 "tools/widl/parser.l"
{ return tREF; }
	YY_BREAK
case 111:
YY_RULE_SETUP
#line 330 "tools/widl/parser.l"
{ return tREPRESENTAS; }
	YY_BREAK
case 112:
YY_RULE_SETUP
#line 331 "tools/widl/parser.l"
{ return tREQUESTEDIT; }
	YY_BREAK
case 113:
YY_RULE_SETUP
#line 332 "tools/widl/parser.l"
{ return tRESTRICTED; }
	YY_BREAK
case 114:
YY_RULE_SETUP
#line 333 "tools/widl/parser.l"
{ return tRETVAL; }
	YY_BREAK
case 115:
YY_RULE_SETUP
#line 334 "tools/widl/parser.l"
{ return tSINGLE; }
	YY_BREAK
case 116:
YY_RULE_SETUP
#line 335 "tools/widl/parser.l"
{ return tSINGLENODE; }
	YY_BREAK
case 117:
YY_RULE_SETUP
#line 336 "tools/widl/parser.l"
{ return tSIZEIS; }
	YY_BREAK
case 118:
YY_RULE_SETUP
#line 337 "tools/widl/parser.l"
{ return tSOURCE; }
	YY_BREAK
case 119:
YY_RULE_SETUP
#line 338 "tools/widl/parser.l"
{ return token_winrt( tSTANDARD, yytext, yylval ); }
	YY_BREAK
case 120:
YY_RULE_SETUP
#line 339 "tools/widl/parser.l"
{ return token_winrt( tSTATIC, yytext, yylval ); }
	YY_BREAK
case 121:
YY_RULE_SETUP
#line 340 "tools/widl/parser.l"
{ return tSTRICTCONTEXTHANDLE; }
	YY_BREAK
case 122:
YY_RULE_SETUP
#line 341 "tools/widl/parser.l"
{ return tSTRING; }
	YY_BREAK
case 123:
YY_RULE_SETUP
#line 342 "tools/widl/parser.l"
{ return tSWITCHIS; }
	YY_BREAK
case 124:
YY_RULE_SETUP
#line 343 "tools/widl/parser.l"
{ return tSWITCHTYPE; }
	YY_BREAK
case 125:
YY_RULE_SETUP
#line 344 "tools/widl/parser.l"
{ return tTHREADING; }
	YY_BREAK
case 126:
YY_RULE_SETUP
#line 345 "tools/widl/parser.l"
{ return tTRANSMITAS; }
	YY_BREAK
case 127:
YY_RULE_SETUP
#line 346 "tools/widl/parser.l"
{ return tUIDEFAULT; }
	YY_BREAK
case 128:
YY_RULE_SETUP
#line 347 "tools/widl/parser.l"
{ return tUNIQUE; }
	YY_BREAK
case 129:
YY_RULE_SETUP
#line 348 "tools/widl/parser.l"
{ return tUSERMARSHAL; }
	YY_BREAK
case 130:
YY_RULE_SETUP
#line 349 "tools/widl/parser.l"
{ return tUSESGETLASTERROR; }
	YY_BREAK
case 131:
YY_RULE_SETUP
#line 350 "tools/widl/parser.l"
{ return tUUID; }
	YY_BREAK
case 132:
YY_RULE_SETUP
#line 351 "tools/widl/parser.l"
{ return tV1ENUM; }
	YY_BREAK
case 133:
YY_RULE_SETUP
#line 352 "tools/widl/parser.l"
{ return tVARARG; }
	YY_BREAK
case 134:
YY_RULE_SETUP
#line 353 "tools/widl/parser.l"
{ return tVERSION; }
	YY_BREAK
case 135:
YY_RULE_SETUP
#line 354 "tools/widl/parser.l"
{ return tVIPROGID; }
	YY_BREAK
case 136:
YY_RULE_SETUP
#line 355 "tools/widl/parser.l"
{ return tWIREMARSHAL; }
	YY_BREAK


case 137:
YY_RULE_SETUP
#line 359 "tools/widl/parser.l"
{ yy_push_state( PP_PRAGMA ); }
	YY_BREAK
case 138:
YY_RULE_SETUP
#line 360 "tools/widl/parser.l"
{ return tPRAGMA_WARNING; }
	YY_BREAK
case 139:
YY_RULE_SETUP
#line 362 "tools/widl/parser.l"
{
                                                    yylval->dbl = strtod( yytext, NULL );
                                                    return aDOUBLE;
                                                }
	YY_BREAK

case 140:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 368 "tools/widl/parser.l"
return tSAFEARRAY;
	YY_BREAK

case 141:
YY_RULE_SETUP
#line 371 "tools/widl/parser.l"
{ yy_push_state(PP_LINE); }
	YY_BREAK
case 142:
YY_RULE_SETUP
#line 372 "tools/widl/parser.l"
{ yy_push_state(ATTR); return '['; }
	YY_BREAK
case 143:
YY_RULE_SETUP
#line 374 "tools/widl/parser.l"
{ return tFALSE; }
	YY_BREAK
case 144:
YY_RULE_SETUP
#line 375 "tools/widl/parser.l"
{ return tNULL; }
	YY_BREAK
case 145:
YY_RULE_SETUP
#line 376 "tools/widl/parser.l"
{ return tTRUE; }
	YY_BREAK
case 146:
YY_RULE_SETUP
#line 377 "tools/widl/parser.l"
{ return token_str( tCDECL, "__cdecl", yylval ); }
	YY_BREAK
case 147:
YY_RULE_SETUP
#line 378 "tools/widl/parser.l"
{ return token_str( tPASCAL, "__pascal", yylval ); }
	YY_BREAK
case 148:
YY_RULE_SETUP
#line 379 "tools/widl/parser.l"
{ return token_str( tSTDCALL, "__stdcall", yylval ); }
	YY_BREAK
case 149:
YY_RULE_SETUP
#line 380 "tools/widl/parser.l"
{ return token_str( tFASTCALL, "__fastcall", yylval ); }
	YY_BREAK
case 150:
YY_RULE_SETUP
#line 381 "tools/widl/parser.l"
{ return tINT32; }
	YY_BREAK
case 151:
YY_RULE_SETUP
#line 382 "tools/widl/parser.l"
{ return tINT3264; }
	YY_BREAK
case 152:
YY_RULE_SETUP
#line 383 "tools/widl/parser.l"
{ return tINT64; }
	YY_BREAK
case 153:
YY_RULE_SETUP
#line 384 "tools/widl/parser.l"
{ return token_winrt( tAPICONTRACT, yytext, yylval ); }
	YY_BREAK
case 154:
YY_RULE_SETUP
#line 385 "tools/widl/parser.l"
{ return tBOOLEAN; }
	YY_BREAK
case 155:
YY_RULE_SETUP
#line 386 "tools/widl/parser.l"
{ return tBYTE; }
	YY_BREAK
case 156:
YY_RULE_SETUP
#line 387 "tools/widl/parser.l"
{ return tCASE; }
	YY_BREAK
case 157:
YY_RULE_SETUP
#line 388 "tools/widl/parser.l"
{ return tCHAR; }
	YY_BREAK
case 158:
YY_RULE_SETUP
#line 389 "tools/widl/parser.l"
{ return tCOCLASS; }
	YY_BREAK
case 159:
YY_RULE_SETUP
#line 390 "tools/widl/parser.l"
{ return tCONST; }
	YY_BREAK
case 160:
YY_RULE_SETUP
#line 391 "tools/widl/parser.l"
{ return tCPPQUOTE; }
	YY_BREAK
case 161:
YY_RULE_SETUP
#line 392 "tools/widl/parser.l"
{ return token_winrt( tDECLARE, yytext, yylval ); }
	YY_BREAK
case 162:
YY_RULE_SETUP
#line 393 "tools/widl/parser.l"
{ return tDEFAULT; }
	YY_BREAK
case 163:
YY_RULE_SETUP
#line 394 "tools/widl/parser.l"
{ return token_winrt( tDELEGATE, yytext, yylval ); }
	YY_BREAK
case 164:
YY_RULE_SETUP
#line 395 "tools/widl/parser.l"
{ return tDISPINTERFACE; }
	YY_BREAK
case 165:
YY_RULE_SETUP
#line 396 "tools/widl/parser.l"
{ return tDOUBLE; }
	YY_BREAK
case 166:
YY_RULE_SETUP
#line 397 "tools/widl/parser.l"
{ return tENUM; }
	YY_BREAK
case 167:
YY_RULE_SETUP
#line 398 "tools/widl/parser.l"
{ return tERRORSTATUST; }
	YY_BREAK
case 168:
YY_RULE_SETUP
#line 399 "tools/widl/parser.l"
{ return tEXTERN; }
	YY_BREAK
case 169:
YY_RULE_SETUP
#line 400 "tools/widl/parser.l"
{ return tFLOAT; }
	YY_BREAK
case 170:
YY_RULE_SETUP
#line 401 "tools/widl/parser.l"
{ return tHANDLET; }
	YY_BREAK
case 171:
YY_RULE_SETUP
#line 402 "tools/widl/parser.l"
{ return tHYPER; }
	YY_BREAK
case 172:
YY_RULE_SETUP
#line 403 "tools/widl/parser.l"
{ return tIMPORT; }
	YY_BREAK
case 173:
YY_RULE_SETUP
#line 404 "tools/widl/parser.l"
{ return tIMPORTLIB; }
	YY_BREAK
case 174:
YY_RULE_SETUP
#line 405 "tools/widl/parser.l"
{ return tINLINE; }
	YY_BREAK
case 175:
YY_RULE_SETUP
#line 406 "tools/widl/parser.l"
{ return tINT; }
	YY_BREAK
case 176:
YY_RULE_SETUP
#line 407 "tools/widl/parser.l"
{ return tINTERFACE; }
	YY_BREAK
case 177:
YY_RULE_SETUP
#line 408 "tools/widl/parser.l"
{ return tLIBRARY; }
	YY_BREAK
case 178:
YY_RULE_SETUP
#line 409 "tools/widl/parser.l"
{ return tLONG; }
	YY_BREAK
case 179:
YY_RULE_SETUP
#line 410 "tools/widl/parser.l"
{ return tMETHODS; }
	YY_BREAK
case 180:
YY_RULE_SETUP
#line 411 "tools/widl/parser.l"
{ return tMODULE; }
	YY_BREAK
case 181:
YY_RULE_SETUP
#line 412 "tools/widl/parser.l"
{ return token_winrt( tNAMESPACE, yytext, yylval ); }
	YY_BREAK
case 182:
YY_RULE_SETUP
#line 413 "tools/widl/parser.l"
{ return tPROPERTIES; }
	YY_BREAK
case 183:
YY_RULE_SETUP
#line 414 "tools/widl/parser.l"
{ return tREGISTER; }
	YY_BREAK
case 184:
YY_RULE_SETUP
#line 415 "tools/widl/parser.l"
{ return token_winrt( tREQUIRES, yytext, yylval ); }
	YY_BREAK
case 185:
YY_RULE_SETUP
#line 416 "tools/widl/parser.l"
{ return token_winrt( tRUNTIMECLASS, yytext, yylval ); }
	YY_BREAK
case 186:
YY_RULE_SETUP
#line 417 "tools/widl/parser.l"
{ return tSHORT; }
	YY_BREAK
case 187:
YY_RULE_SETUP
#line 418 "tools/widl/parser.l"
{ return tSIGNED; }
	YY_BREAK
case 188:
YY_RULE_SETUP
#line 419 "tools/widl/parser.l"
{ return tSIZEOF; }
	YY_BREAK
case 189:
YY_RULE_SETUP
#line 420 "tools/widl/parser.l"
{ return tSMALL; }
	YY_BREAK
case 190:
YY_RULE_SETUP
#line 421 "tools/widl/parser.l"
{ return tSTATIC; }
	YY_BREAK
case 191:
YY_RULE_SETUP
#line 422 "tools/widl/parser.l"
{ return tSTRUCT; }
	YY_BREAK
case 192:
YY_RULE_SETUP
#line 423 "tools/widl/parser.l"
{ return tSWITCH; }
	YY_BREAK
case 193:
YY_RULE_SETUP
#line 424 "tools/widl/parser.l"
{ return tTYPEDEF; }
	YY_BREAK
case 194:
YY_RULE_SETUP
#line 425 "tools/widl/parser.l"
{ return tUNION; }
	YY_BREAK
case 195:
YY_RULE_SETUP
#line 426 "tools/widl/parser.l"
{ return tUNSIGNED; }
	YY_BREAK
case 196:
YY_RULE_SETUP
#line 427 "tools/widl/parser.l"
{ return tVOID; }
	YY_BREAK
case 197:
YY_RULE_SETUP
#line 428 "tools/widl/parser.l"
{ return tWCHAR; }
	YY_BREAK
case 198:
YY_RULE_SETUP
#line 430 "tools/widl/parser.l"
{ return token_ident( yytext, yylval ); }
	YY_BREAK
case 199:
YY_RULE_SETUP
#line 432 "tools/widl/parser.l"
{ return token_num( aHEXNUM, yytext, yylval ); }
	YY_BREAK
case 200:
YY_RULE_SETUP
#line 433 "tools/widl/parser.l"
{ return token_num( aNUM, yytext, yylval ); }
	YY_BREAK
case 201:
/* rule 201 can match eol */
YY_RULE_SETUP
#line 435 "tools/widl/parser.l"
{ return token_str( aWSTRING, yytext + 1, yylval ); }
	YY_BREAK
case 202:
/* rule 202 can match eol */
YY_RULE_SETUP
#line 436 "tools/widl/parser.l"
{ return token_str( aSTRING, yytext, yylval ); }
	YY_BREAK
case 203:
/* rule 203 can match eol */
YY_RULE_SETUP
#line 437 "tools/widl/parser.l"
{ return token_str( aSQSTRING, yytext, yylval ); }
	YY_BREAK
case 204:
/* rule 204 can match eol */
YY_RULE_SETUP
#line 439 "tools/widl/parser.l"
{ end_of_line( yylloc ); }
	YY_BREAK
case 205:
YY_RULE_SETUP
#line 440 "tools/widl/parser.l"
{}
	YY_BREAK
case 206:
YY_RULE_SETUP
#line 441 "tools/widl/parser.l"
{ return SHL; }
	YY_BREAK
case 207:
YY_RULE_SETUP
#line 442 "tools/widl/parser.l"
{ return SHR; }
	YY_BREAK
case 208:
YY_RULE_SETUP
#line 443 "tools/widl/parser.l"
{ return MEMBERPTR; }
	YY_BREAK
case 209:
YY_RULE_SETUP
#line 444 "tools/widl/parser.l"
{ return EQUALITY; }
	YY_BREAK
case 210:
YY_RULE_SETUP
#line 445 "tools/widl/parser.l"
{ return INEQUALITY; }
	YY_BREAK
case 211:
YY_RULE_SETUP
#line 446 "tools/widl/parser.l"
{ return GREATEREQUAL; }
	YY_BREAK
case 212:
YY_RULE_SETUP
#line 447 "tools/widl/parser.l"
{ return LESSEQUAL; }
	YY_BREAK
case 213:
YY_RULE_SETUP
#line 448 "tools/widl/parser.l"
{ return LOGICALOR; }
	YY_BREAK
case 214:
YY_RULE_SETUP
#line 449 "tools/widl/parser.l"
{ return LOGICALAND; }
	YY_BREAK
case 215:
YY_RULE_SETUP
#line 450 "tools/widl/parser.l"
{ return ELLIPSIS; }
	YY_BREAK
case 216:
YY_RULE_SETUP
#line 451 "tools/widl/parser.l"
{ return yytext[0]; }
	YY_BREAK

case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(ATTR):
case YY_STATE_EOF(PP_LINE):
case YY_STATE_EOF(PP_FILE):
case YY_STATE_EOF(PP_PRAGMA):
#line 454 "tools/widl/parser.l"
{
                            if (!list_empty( &import_stack ))
                                return aEOF;
                            if (acf_name)
                            {
                                switch_to_acf();
                                return aACF;
                            }
                            yyterminate();
			}
	YY_BREAK
case 217:
YY_RULE_SETUP
#line 464 "tools/widl/parser.l"
ECHO;
	YY_BREAK
#line 3407 "tools/widl/parser.yy.c"

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_last_accepting_cpos);
				yy_current_state = (yy_last_accepting_state);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
	yy_current_state = (yy_start);
	yy_current_state += YY_AT_BOL();

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 1456 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	int yy_is_jam;
    	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 1456 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 1455);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = (c == '\n');

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void yypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr )
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n  );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

    static void yy_push_state (int  _new_state )
{
    	if ( (yy_start_stack_ptr) >= (yy_start_stack_depth) )
		{
		yy_size_t new_size;

		(yy_start_stack_depth) += YY_START_STACK_INCR;
		new_size = (yy_size_t) (yy_start_stack_depth) * sizeof( int );

		if ( ! (yy_start_stack) )
			(yy_start_stack) = (int *) yyalloc( new_size  );

		else
			(yy_start_stack) = (int *) yyrealloc(
					(void *) (yy_start_stack), new_size  );

		if ( ! (yy_start_stack) )
			YY_FATAL_ERROR( "out of memory expanding start-condition stack" );
		}

	(yy_start_stack)[(yy_start_stack_ptr)++] = YY_START;

	BEGIN(_new_state);
}

    static void yy_pop_state  (void)
{
    	if ( --(yy_start_stack_ptr) < 0 )
		YY_FATAL_ERROR( "start-condition stack underflow" );

	BEGIN((yy_start_stack)[(yy_start_stack_ptr)]);
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg )
{
			fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int yyget_lineno  (void)
{
    
    return yylineno;
}

/** Get the input stream.
 * 
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
int yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param _line_number line number
 * 
 */
void yyset_lineno (int  _line_number )
{
    
    yylineno = _line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str )
{
        yyin = _in_str ;
}

void yyset_out (FILE *  _out_str )
{
        yyout = _out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  _bdebug )
{
        yy_flex_debug = _bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = NULL;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = NULL;
    (yy_init) = 0;
    (yy_start) = 0;

    (yy_start_stack_ptr) = 0;
    (yy_start_stack_depth) = 0;
    (yy_start_stack) =  NULL;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state();
	}

	/* Destroy the stack itself. */
	yyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    /* Destroy the start condition stack. */
        yyfree( (yy_start_stack)  );
        (yy_start_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 464 "tools/widl/parser.l"


static void print_imports(void)
{
    struct import_state *state, *next;

    if (list_empty( &import_stack )) return;

    fprintf( stderr, "In file included from " );
    LIST_FOR_EACH_ENTRY_SAFE_REV( state, next, &import_stack, struct import_state, entry )
    {
        if (&next->entry == &import_stack) break;
        fprintf( stderr, "%s:%d,\n", state->input_name, state->where.first_line );
        fprintf( stderr, "                 from ");
    }
    fprintf( stderr, "%s:%d:\n", state->input_name, state->where.first_line );
}

void pop_import( struct location *where )
{
    struct list *entry = list_head( &import_stack );
    struct import_state *state;
    assert( entry );

    state = LIST_ENTRY( entry, struct import_state, entry );
    list_remove( &state->entry );
    parse_only = !list_empty( &import_stack );

    if (yyin) fclose( yyin );
    yy_delete_buffer( YY_CURRENT_BUFFER );
    yy_switch_to_buffer( state->buffer );

    input_name = state->input_name;
    *where = state->where;
    free( state );
}

void push_import( const char *import_name, struct location *where )
{
    struct import_state *state;
    struct import *import;
    FILE *file;

    state = xmalloc( sizeof(struct import_state ));
    list_add_head( &import_stack, &state->entry );
    parse_only = !list_empty( &import_stack );

    state->buffer = YY_CURRENT_BUFFER;
    state->input_name = input_name;
    state->where = *where;
    input_name = NULL;

    /* reset buffer for <<EOF>>, in case import fails or already imported */
    yy_scan_string( "" );

    LIST_FOR_EACH_ENTRY( import, &imports, struct import, entry )
        if (!strcmp( import->name, import_name )) return;  /* already imported */

    import = xmalloc( sizeof(struct import) );
    import->name = xstrdup( import_name );
    list_add_tail( &imports, &import->entry );

    input_name = find_input_file( import_name, state->input_name );
    file = open_input_file( input_name );
    reset_location( where, input_name );

    yy_switch_to_buffer( yy_create_buffer( file, YY_BUF_SIZE ) );
}

static void switch_to_acf(void)
{
    FILE *file;

    if (yyin) fclose( yyin );
    yy_delete_buffer( YY_CURRENT_BUFFER );

    input_name = xstrdup( acf_name );
    file = open_input_file( input_name );
    acf_name = NULL;

    yy_switch_to_buffer( yy_create_buffer( file, YY_BUF_SIZE ) );
}

static void reset_location( struct location *where, const char *input_name )
{
    where->first_line = 1;
    where->last_line = 1;
    where->first_column = 1;
    where->last_column = 1;
    where->input_name = xstrdup( input_name );
}

static void update_location( struct location *where, const char *yytext )
{
    int len = strlen( yytext );
    previous_location = *where;
    where->first_column = where->last_column;
    where->last_column += len;
}

static void end_of_line( struct location *where )
{
    where->first_line++;
    where->last_line++;
    where->first_column = 1;
    where->last_column = 1;
}

void init_location( struct location *where, const struct location *begin, const struct location *end )
{
    if (!begin) begin = &previous_location;
    *where = *begin;

    if (end)
    {
        where->last_line   = end->last_line;
        where->last_column = end->last_column;
    }
    else
    {
        where->first_line   = begin->last_line;
        where->first_column = begin->last_column;
    }
}

static void diagnostic( const struct location *where, const char *type, const char *message )
{
    char buffer[1024], *line = NULL;
    FILE *file;
    int i;

    if (!where) where = &previous_location;

    print_imports();

    fprintf( stderr, "%s:%d:%d: %s: %s\n", where->input_name, where->first_line, where->first_column, type, message );

    if (!where->input_name || !(file = fopen( where->input_name, "r" ))) return;
    for (i = 0; i < where->first_line; i++) if (!(line = fgets( buffer, sizeof(buffer), file ))) break;
    fclose( file );
    if (!line) return;
    fprintf( stderr, "%s", line );

    line = buffer;
    for (i = 0; i < where->first_column - 1; i++) line += sprintf( line, " " );
    line += sprintf( line, "^" );
    for (i = where->first_column + 1; i < where->last_column; i++) line += sprintf( line, "~" );
    fprintf( stderr, "%s\n", buffer );
}

void parser_error( const struct location *where, const char *message )
{
    diagnostic( where, "error", message );
}

void parser_warning( const struct location *where, const char *message )
{
    diagnostic( where, "warning", message );
}

static void warning_disable(int warning)
{
    warning_t *warning_entry;
    LIST_FOR_EACH_ENTRY(warning_entry, disabled_warnings, warning_t, entry)
        if(warning_entry->num == warning)
            return;
    warning_entry = xmalloc( sizeof(*warning_entry) );
    warning_entry->num = warning;
    list_add_tail(disabled_warnings, &warning_entry->entry);
}

static void warning_enable(int warning)
{
    warning_t *warning_entry;
    LIST_FOR_EACH_ENTRY(warning_entry, disabled_warnings, warning_t, entry)
        if(warning_entry->num == warning)
        {
            list_remove(&warning_entry->entry);
            free(warning_entry);
            break;
        }
}

int do_warning(const char *toggle, warning_list_t *wnum)
{
    warning_t *warning, *next;
    int ret = 1;
    if(!disabled_warnings)
    {
        disabled_warnings = xmalloc( sizeof(*disabled_warnings) );
        list_init( disabled_warnings );
    }

    if(!strcmp(toggle, "disable"))
        LIST_FOR_EACH_ENTRY(warning, wnum, warning_t, entry)
            warning_disable(warning->num);
    else if(!strcmp(toggle, "enable") || !strcmp(toggle, "default"))
        LIST_FOR_EACH_ENTRY(warning, wnum, warning_t, entry)
            warning_enable(warning->num);
    else
        ret = 0;

    LIST_FOR_EACH_ENTRY_SAFE(warning, next, wnum, warning_t, entry)
        free(warning);
    return ret;
}

int is_warning_enabled(int warning)
{
    warning_t *warning_entry;
    if(!disabled_warnings)
        return 1;
    LIST_FOR_EACH_ENTRY(warning_entry, disabled_warnings, warning_t, entry)
        if(warning_entry->num == warning)
            return 0;
    return 1;
}

