LIBRARY api-ms-win-core-synch-l1-1-0

EXPORTS

AcquireSRWLockExclusive
AcquireSRWLockShared
CancelWaitableTimer
CreateEventA
CreateEventExA
CreateEventExW
CreateEventW
CreateMutexA
CreateMutexExA
CreateMutexExW
CreateMutexW
CreateSemaphoreExW
CreateWaitableTimerExW
DeleteCriticalSection
EnterCriticalSection
InitializeCriticalSection
InitializeCriticalSectionAndSpinCount
InitializeCriticalSectionEx
InitializeSRWLock
LeaveCriticalSection
OpenEventA
OpenEventW
OpenMutexW
OpenProcess
OpenSemaphoreW
OpenWaitableTimerW
ReleaseMutex
ReleaseSemaphore
ReleaseSRWLockExclusive
ReleaseSRWLockShared
ResetEvent
SetCriticalSectionSpinCount
SetEvent
SetWaitableTimer
SetWaitableTimerEx
SleepEx
TryAcquireSRWLockExclusive
TryAcquireSRWLockShared
TryEnterCriticalSection
WaitForMultipleObjectsEx
WaitForSingleObject
WaitForSingleObjectEx
