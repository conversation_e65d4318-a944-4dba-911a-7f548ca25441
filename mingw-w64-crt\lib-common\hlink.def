; 
; Exports of file hlink.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY hlink.dll
EXPORTS
HlinkCreateFromMoniker
HlinkCreateFromString
HlinkCreateFromData
HlinkCreateBrowseContext
HlinkClone
HlinkNavigateToStringReference
HlinkOnNavigate
HlinkNavigate
HlinkUpdateStackItem
HlinkOnRenameDocument
DllCanUnloadNow
HlinkResolveMonikerForData
HlinkResolveStringForData
OleSaveToStreamEx
DllGetClassObject
HlinkParseDisplayName
DllRegisterServer
HlinkQueryCreateFromData
HlinkSetSpecialReference
HlinkGetSpecialReference
HlinkCreateShortcut
HlinkResolveShortcut
HlinkIsShortcut
HlinkResolveShortcutToString
HlinkCreateShortcutFromString
HlinkGetValueFromParams
HlinkCreateShortcutFromMoniker
HlinkResolveShortcutToMoni<PERSON>
HlinkTranslateURL
HlinkCreateExtensionServices
HlinkPreprocessMoniker
DllUnregisterServer
