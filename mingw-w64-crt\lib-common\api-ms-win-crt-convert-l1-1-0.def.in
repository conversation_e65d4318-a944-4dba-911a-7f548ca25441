LIBRARY api-ms-win-crt-convert-l1-1-0

EXPORTS

#include "func.def.in"

__toascii
toascii == __toascii
_atodbl
_atodbl_l
_atof_l
_atoflt
_atoflt_l
_atoi64
_atoi64_l
_atoi_l
_atol_l
_atoldbl
_atoldbl_l
_atoll_l
_ecvt
ecvt == _ecvt
_ecvt_s
_fcvt
fcvt == _fcvt
_fcvt_s
_gcvt
gcvt == _gcvt
_gcvt_s
_i64toa
_i64toa_s
_i64tow
_i64tow_s
_itoa
itoa == _itoa
_itoa_s
_itow
_itow_s
_ltoa
ltoa == _ltoa
_ltoa_s
_ltow
_ltow_s
_strtod_l
_strtof_l
_strtoi64
_strtoi64_l
_strtoimax_l
_strtol_l
_strtold_l
_strtoll_l
_strtoui64
_strtoui64_l
_strtoul_l
_strtoull_l
_strtoumax_l
_ui64toa
_ui64toa_s
_ui64tow
_ui64tow_s
_ultoa
_ultoa_s
_ultow
_ultow_s
_wcstod_l
_wcstof_l
_wcstoi64
_wcstoi64_l
_wcstoimax_l
_wcstol_l
_wcstold_l
_wcstoll_l
_wcstombs_l
_wcstombs_s_l
_wcstoui64
_wcstoui64_l
_wcstoul_l
_wcstoull_l
_wcstoumax_l
_wctomb_l
_wctomb_s_l
_wtof
_wtof_l
_wtoi
_wtoi64
_wtoi64_l
_wtoi_l
_wtol
_wtol_l
_wtoll
_wtoll_l
atof
atoi
atol
atoll
btowc
c16rtomb
c32rtomb
mbrtoc16
mbrtoc32
mbrtowc
mbsrtowcs
mbsrtowcs_s
mbstowcs
mbstowcs_s
mbtowc
strtod
strtof
strtoimax
strtol
; Can't use long double functions from the CRT on x86
F_ARM_ANY(strtold)
strtoll
strtoul
strtoull
strtoumax
wcrtomb
wcrtomb_s
wcsrtombs
wcsrtombs_s
wcstod
wcstof
wcstoimax
wcstol
; Can't use long double functions from the CRT on x86
F_ARM_ANY(wcstold)
wcstoll
wcstombs
wcstombs_s
wcstoul
wcstoull
wcstoumax
wctob
wctomb
wctomb_s
wctrans
