; 
; Exports of file GLU32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY GLU32.dll
EXPORTS
gluBeginCurve
gluBeginPolygon
gluBeginSurface
gluBeginTrim
gluBuild1DMipmaps
gluBuild2DMipmaps
gluCylinder
gluDeleteNurbsRenderer
gluDeleteQuadric
gluDeleteTess
gluDisk
gluEndCurve
gluEndPolygon
gluEndSurface
gluEndTrim
gluErrorString
gluErrorUnicodeStringEXT
gluGetNurbsProperty
gluGetString
gluGetTessProperty
gluLoadSamplingMatrices
gluLookAt
gluNewNurbsRenderer
gluNewQuadric
gluNewTess
gluNextContour
gluNurbsCallback
gluNurbsCurve
gluNurbsProperty
gluNurbsSurface
gluOrtho2D
gluPartialDisk
gluPerspective
gluPickMatrix
gluProject
gluPwlCurve
gluQuadricCallback
gluQuadricDrawStyle
gluQuadricNormals
gluQuadricOrientation
gluQuadricTexture
gluScaleImage
gluSphere
gluTessBeginContour
gluTessBeginPolygon
gluTessCallback
gluTessEndContour
gluTessEndPolygon
gluTessNormal
gluTessProperty
gluTessVertex
gluUnProject
