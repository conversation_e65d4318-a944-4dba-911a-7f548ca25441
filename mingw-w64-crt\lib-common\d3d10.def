LIBRARY "d3d10.dll"
EXPORTS
D3D10CompileEffectFromMemory
D3D10CompileShader
D3D10CreateBlob
D3D10CreateDevice
D3D10CreateDeviceAndSwapChain
D3D10CreateEffectFromMemory
D3D10CreateEffectPoolFromMemory
D3D10CreateStateBlock
D3D10DisassembleEffect
D3D10DisassembleShader
D3D10GetGeometryShaderProfile
D3D10GetInputAndOutputSignatureBlob
D3D10GetInputSignatureBlob
D3D10GetOutputSignatureBlob
D3D10GetPixelShaderProfile
D3D10GetShaderDebugInfo
D3D10GetVersion
D3D10GetVertexShaderProfile
D3D10PreprocessShader
D3D10ReflectShader
D3D10RegisterLayers
D3D10StateBlockMaskDifference
D3D10StateBlockMaskDisableAll
D3D10StateBlockMaskDisableCapture
D3D10StateBlockMaskEnableAll
D3D10StateBlockMaskEnableCapture
D3D10StateBlockMaskGetSetting
D3D10StateBlockMaskIntersect
D3D10StateBlockMaskUnion
