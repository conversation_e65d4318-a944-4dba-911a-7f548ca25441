LIBRARY "NETAPI32.dll"
EXPORTS
CredpValidateTargetName
DavAddConnection
DavDeleteConnection
DavFlushFile
DavGetExtendedError
DavGetHTTPFromUNCPath
DavGetUNCFromHTTPPath
DsAddressToSiteNamesA
DsAddressToSiteNamesExA
DsAddressToSiteNamesExW
DsAddressToSiteNamesW
DsDeregisterDnsHostRecordsA
DsDeregisterDnsHostRecordsW
DsEnumerateDomainTrustsA
DsEnumerateDomainTrustsW
DsGetDcCloseW
DsGetDcNameA
DsGetDcNameW
DsGetDcNameWithAccountA
DsGetDcNameWithAccountW
DsGetDcNextA
DsGetDcNextW
DsGetDcOpenA
DsGetDcOpenW
DsGetDcSiteCoverageA
DsGetDcSiteCoverageW
DsGetForestTrustInformationW
DsGetSiteNameA
DsGetSiteNameW
DsMergeForestTrustInformationW
DsRoleAbortDownlevelServerUpgrade
DsRoleCancel
DsRoleDcAsDc
DsRoleDcAsReplica
DsRoleDemoteDc
DsRoleDnsNameToFlatName
DsRoleFreeMemory
DsRoleGetDatabaseFacts
DsRoleGetDcOperationProgress
DsRoleGetDcOperationResults
DsRoleGetPrimaryDomainInformation
DsRoleIfmHandleFree
DsRoleServerSaveStateForUpgrade
DsRoleUpgradeDownlevelServer
DsValidateSubnetNameA
DsValidateSubnetNameW
I_BrowserDebugCall
I_BrowserDebugTrace
I_BrowserQueryEmulatedDomains
I_BrowserQueryOtherDomains
I_BrowserQueryStatistics
I_BrowserResetNetlogonState
I_BrowserResetStatistics
I_BrowserServerEnum
I_BrowserSetNetlogonState
I_DsUpdateReadOnlyServerDnsRecords
I_NetAccountDeltas
I_NetAccountSync
I_NetChainSetClientAttributes
I_NetChainSetClientAttributes2
I_NetDatabaseDeltas
I_NetDatabaseRedo
I_NetDatabaseSync
I_NetDatabaseSync2
I_NetDfsCreateExitPoint
I_NetDfsCreateLocalPartition
I_NetDfsDeleteExitPoint
I_NetDfsDeleteLocalPartition
I_NetDfsFixLocalVolume
I_NetDfsGetFtServers
I_NetDfsGetVersion
I_NetDfsIsThisADomainName
I_NetDfsManagerReportSiteInfo
I_NetDfsModifyPrefix
I_NetDfsSetLocalVolumeState
I_NetDfsSetServerInfo
I_NetGetDCList
I_NetGetForestTrustInformation
I_NetListCanonicalize
I_NetListTraverse
I_NetLogonControl
I_NetLogonControl2
I_NetLogonGetDomainInfo
I_NetLogonSamLogoff
I_NetLogonSamLogon
I_NetLogonSamLogonEx
I_NetLogonSamLogonWithFlags
I_NetLogonSendToSam
I_NetLogonUasLogoff
I_NetLogonUasLogon
I_NetNameCanonicalize
I_NetNameCompare
I_NetNameValidate
I_NetPathCanonicalize
I_NetPathCompare
I_NetPathType
I_NetServerAuthenticate
I_NetServerAuthenticate2
I_NetServerAuthenticate3
I_NetServerGetTrustInfo
I_NetServerPasswordGet
I_NetServerPasswordSet
I_NetServerPasswordSet2
I_NetServerReqChallenge
I_NetServerSetServiceBits
I_NetServerSetServiceBitsEx
I_NetServerTrustPasswordsGet
I_NetlogonComputeClientDigest
I_NetlogonComputeServerDigest
I_NetlogonGetTrustRid
NetAccessAdd
NetAccessDel
NetAccessEnum
NetAccessGetInfo
NetAccessGetUserPerms
NetAccessSetInfo
NetAddAlternateComputerName
NetAddServiceAccount
NetAlertRaise
NetAlertRaiseEx
NetApiBufferAllocate
NetApiBufferFree
NetApiBufferReallocate
NetApiBufferSize
NetAuditClear
NetAuditRead
NetAuditWrite
NetBrowserStatisticsGet
NetConfigGet
NetConfigGetAll
NetConfigSet
NetConnectionEnum
NetCreateProvisioningPackage
NetDfsAdd
NetDfsAddFtRoot
NetDfsAddRootTarget
NetDfsAddStdRoot
NetDfsAddStdRootForced
NetDfsEnum
NetDfsGetClientInfo
NetDfsGetDcAddress
NetDfsGetFtContainerSecurity
NetDfsGetInfo
NetDfsGetSecurity
NetDfsGetStdContainerSecurity
NetDfsGetSupportedNamespaceVersion
NetDfsManagerGetConfigInfo
NetDfsManagerInitialize
NetDfsManagerSendSiteInfo
NetDfsMove
NetDfsRemove
NetDfsRemoveFtRoot
NetDfsRemoveFtRootForced
NetDfsRemoveRootTarget
NetDfsRemoveStdRoot
NetDfsRename
NetDfsSetClientInfo
NetDfsSetFtContainerSecurity
NetDfsSetInfo
NetDfsSetSecurity
NetDfsSetStdContainerSecurity
NetEnumerateComputerNames
NetEnumerateServiceAccounts
NetEnumerateTrustedDomains
NetErrorLogClear
NetErrorLogRead
NetErrorLogWrite
NetFileClose
NetFileEnum
NetFileGetInfo
NetGetAnyDCName
NetGetDCName
NetGetDisplayInformationIndex
NetGetJoinInformation
NetGetJoinableOUs
NetGroupAdd
NetGroupAddUser
NetGroupDel
NetGroupDelUser
NetGroupEnum
NetGroupGetInfo
NetGroupGetUsers
NetGroupSetInfo
NetGroupSetUsers
NetIsServiceAccount
NetJoinDomain
NetLocalGroupAdd
NetLocalGroupAddMember
NetLocalGroupAddMembers
NetLocalGroupDel
NetLocalGroupDelMember
NetLocalGroupDelMembers
NetLocalGroupEnum
NetLocalGroupGetInfo
NetLocalGroupGetMembers
NetLocalGroupSetInfo
NetLocalGroupSetMembers
NetLogonGetTimeServiceParentDomain
NetLogonSetServiceBits
NetMessageBufferSend
NetMessageNameAdd
NetMessageNameDel
NetMessageNameEnum
NetMessageNameGetInfo
NetProvisionComputerAccount
NetQueryDisplayInformation
NetQueryServiceAccount
NetRegisterDomainNameChangeNotification
NetRemoteComputerSupports
NetRemoteTOD
NetRemoveAlternateComputerName
NetRemoveServiceAccount
NetRenameMachineInDomain
NetReplExportDirAdd
NetReplExportDirDel
NetReplExportDirEnum
NetReplExportDirGetInfo
NetReplExportDirLock
NetReplExportDirSetInfo
NetReplExportDirUnlock
NetReplGetInfo
NetReplImportDirAdd
NetReplImportDirDel
NetReplImportDirEnum
NetReplImportDirGetInfo
NetReplImportDirLock
NetReplImportDirUnlock
NetReplSetInfo
NetRequestOfflineDomainJoin
NetRequestProvisioningPackageInstall
NetScheduleJobAdd
NetScheduleJobDel
NetScheduleJobEnum
NetScheduleJobGetInfo
NetServerAliasAdd
NetServerAliasDel
NetServerAliasEnum
NetServerComputerNameAdd
NetServerComputerNameDel
NetServerDiskEnum
NetServerEnum
NetServerEnumEx
NetServerGetInfo
NetServerSetInfo
NetServerTransportAdd
NetServerTransportAddEx
NetServerTransportDel
NetServerTransportEnum
NetServiceControl
NetServiceEnum
NetServiceGetInfo
NetServiceInstall
NetSessionDel
NetSessionEnum
NetSessionGetInfo
NetSetPrimaryComputerName
NetShareAdd
NetShareCheck
NetShareDel
NetShareDelEx
NetShareDelSticky
NetShareEnum
NetShareEnumSticky
NetShareGetInfo
NetShareSetInfo
NetStatisticsGet
NetUnjoinDomain
NetUnregisterDomainNameChangeNotification
NetUseAdd
NetUseDel
NetUseEnum
NetUseGetInfo
NetUserAdd
NetUserChangePassword
NetUserDel
NetUserEnum
NetUserGetGroups
NetUserGetInfo
NetUserGetLocalGroups
NetUserModalsGet
NetUserModalsSet
NetUserSetGroups
NetUserSetInfo
NetValidateName
NetValidatePasswordPolicy
NetValidatePasswordPolicyFree
NetWkstaGetInfo
NetWkstaSetInfo
NetWkstaTransportAdd
NetWkstaTransportDel
NetWkstaTransportEnum
NetWkstaUserEnum
NetWkstaUserGetInfo
NetWkstaUserSetInfo
NetapipBufferAllocate
Netbios
NetpAccessCheck
NetpAccessCheckAndAudit
NetpAccessCheckAndAuditEx
NetpAddTlnFtinfoEntry
NetpAllocConfigName
NetpAllocFtinfoEntry
NetpAllocStrFromWStr
NetpAllocWStrFromStr
NetpAllocWStrFromWStr
NetpApiStatusToNtStatus
NetpAssertFailed
NetpCleanFtinfoContext
NetpCloseConfigData
NetpCopyFtinfoContext
NetpCopyStringToBuffer
NetpCreateSecurityObject
NetpDbgPrint
NetpDeleteSecurityObject
NetpEventlogClose
NetpEventlogOpen
NetpEventlogWriteEx
NetpGetComputerName
NetpGetConfigBool
NetpGetConfigDword
NetpGetConfigTStrArray
NetpGetConfigValue
NetpGetDomainName
NetpGetFileSecurity
NetpGetPrivilege
NetpHexDump
NetpInitFtinfoContext
NetpInitOemString
NetpIsRemote
NetpIsUncComputerNameValid
NetpLocalTimeZoneOffset
NetpLogonPutUnicodeString
NetpMergeFtinfo
NetpNetBiosAddName
NetpNetBiosCall
NetpNetBiosDelName
NetpNetBiosGetAdapterNumbers
NetpNetBiosHangup
NetpNetBiosReceive
NetpNetBiosReset
NetpNetBiosSend
NetpNetBiosStatusToApiStatus
NetpNtStatusToApiStatus
NetpOpenConfigData
NetpPackString
NetpParmsQueryUserProperty
NetpParmsQueryUserPropertyWithLength
NetpParmsSetUserProperty
NetpParmsSetUserPropertyWithLength
NetpParmsUserPropertyFree
NetpReleasePrivilege
NetpSetFileSecurity
NetpSmbCheck
NetpStoreIntialDcRecord
NetpStringToNetBiosName
NetpTStrArrayEntryCount
NetpUpgradePreNT5JoinInfo
NetpwNameCanonicalize
NetpwNameCompare
NetpwNameValidate
NetpwPathCanonicalize
NetpwPathCompare
NetpwPathType
NlBindingAddServerToCache
NlBindingRemoveServerFromCache
NlBindingSetAuthInfo
RxNetAccessAdd
RxNetAccessDel
RxNetAccessEnum
RxNetAccessGetInfo
RxNetAccessGetUserPerms
RxNetAccessSetInfo
RxNetServerEnum
RxNetUserPasswordSet
RxRemoteApi
