LIBRARY "dxva2.dll"
EXPORTS
CapabilitiesRequestAndCapabilitiesReply
DXVA2CreateDirect3DDeviceManager9
DXVA2CreateVideoService
DXVAHD_CreateDevice
DegaussMonitor
DestroyPhysicalMonitor
DestroyPhysicalMonitors
GetCapabilitiesStringLength
GetMonitorBrightness
GetMonitorCapabilities
GetMonitorColorTemperature
GetMonitorContrast
GetMonitorDisplayAreaPosition
GetMonitorDisplayAreaSize
GetMonitorRedGreenOrBlueDrive
GetMonitorRedGreenOrBlueGain
GetMonitorTechnologyType
GetNumberOfPhysicalMonitorsFromHMONITOR
GetNumberOfPhysicalMonitorsFromIDirect3DDevice9
GetPhysicalMonitorsFromHMONITOR
GetPhysicalMonitorsFromIDirect3DDevice9
GetTimingReport
GetVCPFeatureAndVCPFeatureReply
OPMGetVideoOutputsFromHMONITOR
OPMGetVideoOutputsFromIDirect3DDevice9Object
RestoreMonitorFactoryColorDefaults
RestoreMonitorFactoryDefaults
SaveCurrentMonitorSettings
SaveCurrentSettings
SetMonitorBrightness
SetMonitorColorTemperature
SetMonitorContrast
SetMonitorDisplayAreaPosition
SetMonitorDisplayAreaSize
SetMonitorRedGreenOrBlueDrive
SetMonitorRedGreenOrBlueGain
SetVCPFeature
UABGetCertificate
