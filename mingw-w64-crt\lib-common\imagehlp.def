;
; Definition file of imagehlp.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "imagehlp.dll"
EXPORTS
RemoveRelocations
BindImage
BindImageEx
CheckSumMappedFile
EnumerateLoadedModules
EnumerateLoadedModules64
EnumerateLoadedModulesEx
EnumerateLoadedModulesExW
EnumerateLoadedModulesW64
FindDebugInfoFile
FindDebugInfoFileEx
FindExecutableImage
FindExecutableImageEx
FindFileInPath
FindFileInSearchPath
GetImageConfigInformation
GetImageUnusedHeaderBytes
GetSymLoadError
GetTimestampForLoadedLibrary
ImageAddCertificate
ImageDirectoryEntryToData
ImageDirectoryEntryToDataEx
ImageEnumerateCertificates
ImageGetCertificateData
ImageGetCertificateHeader
ImageGetDigestStream
ImageLoad
ImageNtHeader
ImageRemoveCertificate
ImageRvaToSection
ImageRvaToVa
ImageUnload
ImagehlpApiVersion
ImagehlpApiVersionEx
MakeSureDirectoryPathExists
MapAndLoad
MapDebugInformation
MapFileAndCheckSumA
MapFileAndCheckSumW
ReBaseImage
ReBaseImage64
RemoveInvalidModuleList
RemovePrivateCvSymbolic
RemovePrivateCvSymbolicEx
ReportSymbolLoadSummary
SearchTreeForFile
SetCheckUserInterruptShared
SetImageConfigInformation
SetSymLoadError
SplitSymbols
StackWalk
StackWalk64
StackWalkEx
SymAddrIncludeInlineTrace
SymCleanup
SymCompareInlineTrace
SymEnumSym
SymEnumSymbols
SymEnumSymbolsEx
SymEnumSymbolsExW
SymEnumSymbolsForAddr
SymEnumTypes
SymEnumTypesByName
SymEnumTypesByNameW
SymEnumTypesW
SymEnumerateModules
SymEnumerateModules64
SymEnumerateSymbols
SymEnumerateSymbols64
SymEnumerateSymbolsW
SymEnumerateSymbolsW64
SymFindFileInPath
SymFindFileInPathW
SymFromAddr
SymFromInlineContext
SymFromInlineContextW
SymFromName
SymFunctionTableAccess
SymFunctionTableAccess64
SymFunctionTableAccess64AccessRoutines
SymGetLineFromAddr
SymGetLineFromAddr64
SymGetLineFromInlineContext
SymGetLineFromInlineContextW
SymGetLineFromName
SymGetLineFromName64
SymGetLineNext
SymGetLineNext64
SymGetLinePrev
SymGetLinePrev64
SymGetModuleBase
SymGetModuleBase64
SymGetModuleInfo
SymGetModuleInfo64
SymGetModuleInfoW
SymGetModuleInfoW64
SymGetOptions
SymGetSearchPath
SymGetSourceFileFromTokenW
SymGetSourceFileTokenW
SymGetSourceVarFromTokenW
SymGetSymFromAddr
SymGetSymFromAddr64
SymGetSymFromName
SymGetSymFromName64
SymGetSymNext
SymGetSymNext64
SymGetSymPrev
SymGetSymPrev64
SymGetSymbolFile
SymGetSymbolFileW
SymGetTypeFromName
SymGetTypeFromNameW
SymGetTypeInfo
SymGetTypeInfoEx
SymInitialize
SymLoadModule
SymLoadModule64
SymMatchFileName
SymMatchFileNameW
SymMatchString
SymMatchStringA
SymMatchStringW
SymQueryInlineTrace
SymRegisterCallback
SymRegisterCallback64
SymRegisterFunctionEntryCallback
SymRegisterFunctionEntryCallback64
SymSetContext
SymSetOptions
SymSetScopeFromAddr
SymSetScopeFromIndex
SymSetScopeFromInlineContext
SymSetSearchPath
SymSrvGetFileIndexString
SymSrvGetFileIndexStringW
SymSrvGetFileIndexes
SymSrvGetFileIndexesW
SymUnDName
SymUnDName64
SymUnloadModule
SymUnloadModule64
TouchFileTimes
UnDecorateSymbolName
UnMapAndLoad
UnmapDebugInformation
UpdateDebugInfoFile
UpdateDebugInfoFileEx
