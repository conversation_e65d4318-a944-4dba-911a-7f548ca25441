/* The standard CSS for doxygen */

body, table, div, p, dl {
	font-family: Lucida Grande, Verdana, Geneva, Arial, sans-serif;
	font-size: 12px;
}

/* @group Heading Levels */

h1 {
	text-align: center;
	font-size: 150%;
}

h2 {
	font-size: 120%;
}

h3 {
	font-size: 100%;
}

dt {
	font-weight: bold;
}

div.multicol {
	-moz-column-gap: 1em;
	-webkit-column-gap: 1em;
	-moz-column-count: 3;
	-webkit-column-count: 3;
}

p.startli, p.startdd {
	margin-top: 2px;
}

p.endli {
	margin-bottom: 0px;
}

p.enddd {
	margin-bottom: 4px;
}

/* @end */

caption {
	font-weight: bold;
}

span.legend {
        font-size: 70%;
        text-align: center;
}

div.qindex, div.navtab{
	background-color: #e8eef2;
	border: 1px solid #84b0c7;
	text-align: center;
	margin: 2px;
	padding: 2px;
}

div.qindex, div.navpath {
	width: 100%;
	line-height: 140%;
}

div.navtab {
	margin-right: 15px;
}

/* @group Link Styling */

a {
	color: #153788;
	font-weight: normal;
	text-decoration: none;
}

.contents a:visited {
	color: #1b77c5;
}

a:hover {
	text-decoration: underline;
}

a.qindex {
	font-weight: bold;
}

a.qindexHL {
	font-weight: bold;
	background-color: #6666cc;
	color: #ffffff;
	border: 1px double #9295C2;
}

.contents a.qindexHL:visited {
        color: #ffffff;
}

a.el {
	font-weight: bold;
}

a.elRef {
}

a.code {
}

a.codeRef {
}

/* @end */

dl.el {
	margin-left: -1cm;
}

.fragment {
	font-family: monospace, fixed;
	font-size: 105%;
}

pre.fragment {
	border: 1px solid #CCCCCC;
	background-color: #f5f5f5;
	padding: 4px 6px;
	margin: 4px 8px 4px 2px;
}

div.ah {
	background-color: black;
	font-weight: bold;
	color: #ffffff;
	margin-bottom: 3px;
	margin-top: 3px
}

div.groupHeader {
	margin-left: 16px;
	margin-top: 12px;
	margin-bottom: 6px;
	font-weight: bold;
}

div.groupText {
	margin-left: 16px;
	font-style: italic;
}

body {
	background: white;
	color: black;
	margin-right: 20px;
	margin-left: 20px;
}

td.indexkey {
	background-color: #e8eef2;
	font-weight: bold;
	border: 1px solid #CCCCCC;
	margin: 2px 0px 2px 0;
	padding: 2px 10px;
}

td.indexvalue {
	background-color: #e8eef2;
	border: 1px solid #CCCCCC;
	padding: 2px 10px;
	margin: 2px 0px;
}

tr.memlist {
	background-color: #f0f0f0;
}

p.formulaDsp {
	text-align: center;
}

img.formulaDsp {
	
}

img.formulaInl {
	vertical-align: middle;
}

div.center {
	text-align: center;
        margin-top: 0px;
        margin-bottom: 0px;
        padding: 0px;
}

div.center img {
	border: 0px;
}

img.footer {
	border: 0px;
	vertical-align: middle;
}

/* @group Code Colorization */

span.keyword {
	color: #008000
}

span.keywordtype {
	color: #604020
}

span.keywordflow {
	color: #e08000
}

span.comment {
	color: #800000
}

span.preprocessor {
	color: #806020
}

span.stringliteral {
	color: #002080
}

span.charliteral {
	color: #008080
}

span.vhdldigit { 
	color: #ff00ff 
}

span.vhdlchar { 
	color: #000000 
}

span.vhdlkeyword { 
	color: #700070 
}

span.vhdllogic { 
	color: #ff0000 
}

/* @end */

.search {
	color: #003399;
	font-weight: bold;
}

form.search {
	margin-bottom: 0px;
	margin-top: 0px;
}

input.search {
	font-size: 75%;
	color: #000080;
	font-weight: normal;
	background-color: #e8eef2;
}

td.tiny {
	font-size: 75%;
}

.dirtab {
	padding: 4px;
	border-collapse: collapse;
	border: 1px solid #84b0c7;
}

th.dirtab {
	background: #e8eef2;
	font-weight: bold;
}

hr {
	height: 0;
	border: none;
	border-top: 1px solid #666;
}

/* @group Member Descriptions */

.mdescLeft, .mdescRight,
.memItemLeft, .memItemRight,
.memTemplItemLeft, .memTemplItemRight, .memTemplParams {
	background-color: #FAFAFA;
	border: none;
	margin: 4px;
	padding: 1px 0 0 8px;
}

.mdescLeft, .mdescRight {
	padding: 0px 8px 4px 8px;
	color: #555;
}

.memItemLeft, .memItemRight, .memTemplParams {
	border-top: 1px solid #ccc;
}

.memItemLeft, .memTemplItemLeft {
        white-space: nowrap;
}

.memTemplParams {
	color: #606060;
        white-space: nowrap;
}

/* @end */

/* @group Member Details */

/* Styles for detailed member documentation */

.memtemplate {
	font-size: 80%;
	color: #606060;
	font-weight: normal;
	margin-left: 3px;
}

.memnav {
	background-color: #e8eef2;
	border: 1px solid #84b0c7;
	text-align: center;
	margin: 2px;
	margin-right: 15px;
	padding: 2px;
}

.memitem {
	padding: 0;
	margin-bottom: 10px;
}

.memname {
	white-space: nowrap;
	font-weight: bold;
}

.memproto, .memdoc {
	border: 1px solid #84b0c7;	
}

.memproto {
	padding: 0;
	background-color: #d5e1e8;
	font-weight: bold;
	-webkit-border-top-left-radius: 8px;
	-webkit-border-top-right-radius: 8px;
        -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
	-moz-border-radius-topleft: 8px;
	-moz-border-radius-topright: 8px;
        -moz-box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 5px;

}

.memdoc {
	padding: 2px 5px;
	background-color: #eef3f5;
	border-top-width: 0;
	-webkit-border-bottom-left-radius: 8px;
	-webkit-border-bottom-right-radius: 8px;
        -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
	-moz-border-radius-bottomleft: 8px;
	-moz-border-radius-bottomright: 8px;
        -moz-box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 5px;
}

.paramkey {
	text-align: right;
}

.paramtype {
	white-space: nowrap;
}

.paramname {
	color: #602020;
	white-space: nowrap;
}
.paramname em {
	font-style: normal;
}

/* @end */

/* @group Directory (tree) */

/* for the tree view */

.ftvtree {
	font-family: sans-serif;
	margin: 0.5em;
}

/* these are for tree view when used as main index */

.directory {
	font-size: 9pt;
	font-weight: bold;
}

.directory h3 {
	margin: 0px;
	margin-top: 1em;
	font-size: 11pt;
}

/*
The following two styles can be used to replace the root node title
with an image of your choice.  Simply uncomment the next two styles,
specify the name of your image and be sure to set 'height' to the
proper pixel height of your image.
*/

/*
.directory h3.swap {
	height: 61px;
	background-repeat: no-repeat;
	background-image: url("yourimage.gif");
}
.directory h3.swap span {
	display: none;
}
*/

.directory > h3 {
	margin-top: 0;
}

.directory p {
	margin: 0px;
	white-space: nowrap;
}

.directory div {
	display: none;
	margin: 0px;
}

.directory img {
	vertical-align: -30%;
}

/* these are for tree view when not used as main index */

.directory-alt {
	font-size: 100%;
	font-weight: bold;
}

.directory-alt h3 {
	margin: 0px;
	margin-top: 1em;
	font-size: 11pt;
}

.directory-alt > h3 {
	margin-top: 0;
}

.directory-alt p {
	margin: 0px;
	white-space: nowrap;
}

.directory-alt div {
	display: none;
	margin: 0px;
}

.directory-alt img {
	vertical-align: -30%;
}

/* @end */

address {
	font-style: normal;
	color: #333;
}
