;
; Definition file of VSSAPI.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "VSSAPI.DLL"
EXPORTS
IsVolumeSnapshotted@12
VssFreeSnapshotProperties@4
ShouldBlockRevert@8
; public: __thiscall CVssJetWriter::CVssJetWriter(void)
??0CVssJetWriter@@QAE@XZ ; has WINAPI (@0)
; public: __thiscall CVssWriter::CVssWriter(void)
??0CVssWriter@@QAE@XZ ; has WINAPI (@0)
; public: virtual __thiscall CVssJetWriter::~CVssJetWriter(void)
??1CVssJetWriter@@UAE@XZ ; has WINAPI (@0)
; public: virtual __thiscall CVssWriter::~CVssWriter(void)
??1CVssWriter@@UAE@XZ ; has WIN<PERSON>I (@0)
; protected: bool __stdcall CVssJetWriter::AreComponents<PERSON>ele<PERSON>(void)const 
?AreComponentsSelected@CVssJetWriter@@IBG_NXZ ; has WINAPI (@4)
; protected: bool __stdcall CVssWriter::AreComponentsSelected(void)const 
?AreComponentsSelected@CVssWriter@@IBG_NXZ ; has WINAPI (@4)
; long __stdcall CreateVssBackupComponents(class IVssBackupComponents **)
?CreateVssBackupComponents@@YGJPAPAVIVssBackupComponents@@@Z ; has WINAPI (@4)
; long __stdcall CreateVssExamineWriterMetadata(unsigned short *,class IVssExamineWriterMetadata **)
?CreateVssExamineWriterMetadata@@YGJPAGPAPAVIVssExamineWriterMetadata@@@Z ; has WINAPI (@8)
; long __stdcall CreateVssSnapshotSetDescription(struct _GUID,long,class IVssSnapshotSetDescription **)
?CreateVssSnapshotSetDescription@@YGJU_GUID@@JPAPAVIVssSnapshotSetDescription@@@Z ; has WINAPI (@24)
; protected: enum _VSS_BACKUP_TYPE __stdcall CVssJetWriter::GetBackupType(void)const 
?GetBackupType@CVssJetWriter@@IBG?AW4_VSS_BACKUP_TYPE@@XZ ; has WINAPI (@4)
; protected: enum _VSS_BACKUP_TYPE __stdcall CVssWriter::GetBackupType(void)const 
?GetBackupType@CVssWriter@@IBG?AW4_VSS_BACKUP_TYPE@@XZ ; has WINAPI (@4)
; protected: long __stdcall CVssJetWriter::GetContext(void)const 
?GetContext@CVssJetWriter@@IBGJXZ ; has WINAPI (@4)
; protected: long __stdcall CVssWriter::GetContext(void)const 
?GetContext@CVssWriter@@IBGJXZ ; has WINAPI (@4)
; protected: enum _VSS_APPLICATION_LEVEL __stdcall CVssJetWriter::GetCurrentLevel(void)const 
?GetCurrentLevel@CVssJetWriter@@IBG?AW4_VSS_APPLICATION_LEVEL@@XZ ; has WINAPI (@4)
; protected: enum _VSS_APPLICATION_LEVEL __stdcall CVssWriter::GetCurrentLevel(void)const 
?GetCurrentLevel@CVssWriter@@IBG?AW4_VSS_APPLICATION_LEVEL@@XZ ; has WINAPI (@4)
; protected: struct _GUID __stdcall CVssJetWriter::GetCurrentSnapshotSetId(void)const 
?GetCurrentSnapshotSetId@CVssJetWriter@@IBG?AU_GUID@@XZ ; has WINAPI (@8)
; protected: struct _GUID __stdcall CVssWriter::GetCurrentSnapshotSetId(void)const 
?GetCurrentSnapshotSetId@CVssWriter@@IBG?AU_GUID@@XZ ; has WINAPI (@8)
; protected: unsigned short const **__stdcall CVssJetWriter::GetCurrentVolumeArray(void)const 
?GetCurrentVolumeArray@CVssJetWriter@@IBGPAPBGXZ ; has WINAPI (@4)
; protected: unsigned short const **__stdcall CVssWriter::GetCurrentVolumeArray(void)const 
?GetCurrentVolumeArray@CVssWriter@@IBGPAPBGXZ ; has WINAPI (@4)
; protected: unsigned int __stdcall CVssJetWriter::GetCurrentVolumeCount(void)const 
?GetCurrentVolumeCount@CVssJetWriter@@IBGIXZ ; has WINAPI (@4)
; protected: unsigned int __stdcall CVssWriter::GetCurrentVolumeCount(void)const 
?GetCurrentVolumeCount@CVssWriter@@IBGIXZ ; has WINAPI (@4)
; protected: enum _VSS_RESTORE_TYPE __stdcall CVssJetWriter::GetRestoreType(void)const 
?GetRestoreType@CVssJetWriter@@IBG?AW4_VSS_RESTORE_TYPE@@XZ ; has WINAPI (@4)
; protected: enum _VSS_RESTORE_TYPE __stdcall CVssWriter::GetRestoreType(void)const 
?GetRestoreType@CVssWriter@@IBG?AW4_VSS_RESTORE_TYPE@@XZ ; has WINAPI (@4)
; protected: long __stdcall CVssJetWriter::GetSnapshotDeviceName(unsigned short const *,unsigned short const **)const 
?GetSnapshotDeviceName@CVssJetWriter@@IBGJPBGPAPBG@Z ; has WINAPI (@12)
; protected: long __stdcall CVssWriter::GetSnapshotDeviceName(unsigned short const *,unsigned short const **)const 
?GetSnapshotDeviceName@CVssWriter@@IBGJPBGPAPBG@Z ; has WINAPI (@12)
; public: long __stdcall CVssJetWriter::Initialize(struct _GUID,unsigned short const *,bool,bool,unsigned short const *,unsigned short const *,unsigned long)
?Initialize@CVssJetWriter@@QAGJU_GUID@@PBG_N211K@Z ; has WINAPI (@44)
; public: long __stdcall CVssWriter::Initialize(struct _GUID,unsigned short const *,enum VSS_USAGE_TYPE,enum VSS_SOURCE_TYPE,enum _VSS_APPLICATION_LEVEL,unsigned long,enum VSS_ALTERNATE_WRITER_STATE,bool,unsigned short const *)
?Initialize@CVssWriter@@QAGJU_GUID@@PBGW4VSS_USAGE_TYPE@@W4VSS_SOURCE_TYPE@@W4_VSS_APPLICATION_LEVEL@@KW4VSS_ALTERNATE_WRITER_STATE@@_N1@Z ; has WINAPI (@52)
; public: long __stdcall CVssWriter::InstallAlternateWriter(struct _GUID,struct _GUID)
?InstallAlternateWriter@CVssWriter@@QAGJU_GUID@@0@Z ; has WINAPI (@36)
; protected: bool __stdcall CVssJetWriter::IsBootableSystemStateBackedUp(void)const 
?IsBootableSystemStateBackedUp@CVssJetWriter@@IBG_NXZ ; has WINAPI (@4)
; protected: bool __stdcall CVssWriter::IsBootableSystemStateBackedUp(void)const 
?IsBootableSystemStateBackedUp@CVssWriter@@IBG_NXZ ; has WINAPI (@4)
; protected: bool __stdcall CVssJetWriter::IsPartialFileSupportEnabled(void)const 
?IsPartialFileSupportEnabled@CVssJetWriter@@IBG_NXZ ; has WINAPI (@4)
; protected: bool __stdcall CVssWriter::IsPartialFileSupportEnabled(void)const 
?IsPartialFileSupportEnabled@CVssWriter@@IBG_NXZ ; has WINAPI (@4)
; protected: bool __stdcall CVssJetWriter::IsPathAffected(unsigned short const *)const 
?IsPathAffected@CVssJetWriter@@IBG_NPBG@Z ; has WINAPI (@8)
; protected: bool __stdcall CVssWriter::IsPathAffected(unsigned short const *)const 
?IsPathAffected@CVssWriter@@IBG_NPBG@Z ; has WINAPI (@8)
; long __stdcall LoadVssSnapshotSetDescription(unsigned short const *,class IVssSnapshotSetDescription **,struct _GUID)
?LoadVssSnapshotSetDescription@@YGJPBGPAPAVIVssSnapshotSetDescription@@U_GUID@@@Z ; has WINAPI (@24)
; public: virtual void __stdcall CVssJetWriter::OnAbortBegin(void)
?OnAbortBegin@CVssJetWriter@@UAGXXZ ; has WINAPI (@4)
; public: virtual void __stdcall CVssJetWriter::OnAbortEnd(void)
?OnAbortEnd@CVssJetWriter@@UAGXXZ ; has WINAPI (@4)
; public: virtual bool __stdcall CVssWriter::OnBackOffIOOnVolume(unsigned short *,struct _GUID,struct _GUID)
?OnBackOffIOOnVolume@CVssWriter@@UAG_NPAGU_GUID@@1@Z ; has WINAPI (@40)
; public: virtual bool __stdcall CVssWriter::OnBackupComplete(class IVssWriterComponents *)
?OnBackupComplete@CVssWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnBackupCompleteBegin(class IVssWriterComponents *)
?OnBackupCompleteBegin@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnBackupCompleteEnd(class IVssWriterComponents *,bool)
?OnBackupCompleteEnd@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@_N@Z ; has WINAPI (@12)
; public: virtual bool __stdcall CVssWriter::OnBackupShutdown(struct _GUID)
?OnBackupShutdown@CVssWriter@@UAG_NU_GUID@@@Z ; has WINAPI (@20)
; public: virtual bool __stdcall CVssWriter::OnContinueIOOnVolume(unsigned short *,struct _GUID,struct _GUID)
?OnContinueIOOnVolume@CVssWriter@@UAG_NPAGU_GUID@@1@Z ; has WINAPI (@40)
; public: virtual bool __stdcall CVssJetWriter::OnFreezeBegin(void)
?OnFreezeBegin@CVssJetWriter@@UAG_NXZ ; has WINAPI (@4)
; public: virtual bool __stdcall CVssJetWriter::OnFreezeEnd(bool)
?OnFreezeEnd@CVssJetWriter@@UAG_N_N@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnIdentify(class IVssCreateWriterMetadata *)
?OnIdentify@CVssJetWriter@@UAG_NPAVIVssCreateWriterMetadata@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssWriter::OnIdentify(class IVssCreateWriterMetadata *)
?OnIdentify@CVssWriter@@UAG_NPAVIVssCreateWriterMetadata@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssWriter::OnPostRestore(class IVssWriterComponents *)
?OnPostRestore@CVssWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnPostRestoreBegin(class IVssWriterComponents *)
?OnPostRestoreBegin@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnPostRestoreEnd(class IVssWriterComponents *,bool)
?OnPostRestoreEnd@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@_N@Z ; has WINAPI (@12)
; public: virtual bool __stdcall CVssJetWriter::OnPostSnapshot(class IVssWriterComponents *)
?OnPostSnapshot@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssWriter::OnPostSnapshot(class IVssWriterComponents *)
?OnPostSnapshot@CVssWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssWriter::OnPreRestore(class IVssWriterComponents *)
?OnPreRestore@CVssWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnPreRestoreBegin(class IVssWriterComponents *)
?OnPreRestoreBegin@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnPreRestoreEnd(class IVssWriterComponents *,bool)
?OnPreRestoreEnd@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@_N@Z ; has WINAPI (@12)
; public: virtual bool __stdcall CVssWriter::OnPrepareBackup(class IVssWriterComponents *)
?OnPrepareBackup@CVssWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnPrepareBackupBegin(class IVssWriterComponents *)
?OnPrepareBackupBegin@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnPrepareBackupEnd(class IVssWriterComponents *,bool)
?OnPrepareBackupEnd@CVssJetWriter@@UAG_NPAVIVssWriterComponents@@_N@Z ; has WINAPI (@12)
; public: virtual bool __stdcall CVssJetWriter::OnPrepareSnapshotBegin(void)
?OnPrepareSnapshotBegin@CVssJetWriter@@UAG_NXZ ; has WINAPI (@4)
; public: virtual bool __stdcall CVssJetWriter::OnPrepareSnapshotEnd(bool)
?OnPrepareSnapshotEnd@CVssJetWriter@@UAG_N_N@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssJetWriter::OnThawBegin(void)
?OnThawBegin@CVssJetWriter@@UAG_NXZ ; has WINAPI (@4)
; public: virtual bool __stdcall CVssJetWriter::OnThawEnd(bool)
?OnThawEnd@CVssJetWriter@@UAG_N_N@Z ; has WINAPI (@8)
; public: virtual bool __stdcall CVssWriter::OnVSSApplicationStartup(void)
?OnVSSApplicationStartup@CVssWriter@@UAG_NXZ ; has WINAPI (@4)
; public: virtual bool __stdcall CVssWriter::OnVSSShutdown(void)
?OnVSSShutdown@CVssWriter@@UAG_NXZ ; has WINAPI (@4)
; protected: long __stdcall CVssJetWriter::SetWriterFailure(long)
?SetWriterFailure@CVssJetWriter@@IAGJJ@Z ; has WINAPI (@8)
; protected: long __stdcall CVssWriter::SetWriterFailure(long)
?SetWriterFailure@CVssWriter@@IAGJJ@Z ; has WINAPI (@8)
; public: long __stdcall CVssWriter::Subscribe(unsigned long)
?Subscribe@CVssWriter@@QAGJK@Z ; has WINAPI (@8)
; public: void __stdcall CVssJetWriter::Uninitialize(void)
?Uninitialize@CVssJetWriter@@QAGXXZ ; has WINAPI (@4)
; public: long __stdcall CVssWriter::Unsubscribe(void)
?Unsubscribe@CVssWriter@@QAGJXZ ; has WINAPI (@4)
CreateVssBackupComponentsInternal@4
CreateVssExamineWriterMetadataInternal@8
CreateVssExpressWriterInternal@4
CreateWriter@8
CreateWriterEx@8
;DllCanUnloadNow@0
;DllGetClassObject@12
GetProviderMgmtInterface@36
GetProviderMgmtInterfaceInternal@36
IsVolumeSnapshottedInternal@12
ShouldBlockRevertInternal@8
VssFreeSnapshotPropertiesInternal@4
