;
; Definition file of GDI32.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "GDI32.dll"
EXPORTS
AbortDoc
AbortPath
DwmCreatedBitmapRemotingOutput
AddFontMemResourceEx
AddFontResourceA
AddFontResourceExA
AddFontResourceExW
AddFontResourceTracking
AddFontResourceW
AngleArc
AnimatePalette
AnyLinkedFonts
Arc
ArcTo
BRUSHOBJ_hGetColorTransform
BRUSHOBJ_pvAllocRbrush
BRUSHOBJ_pvGetRbrush
BRUSHOBJ_ulGetBrushColor
BeginGdiRendering
BeginPath
BitBlt
CLIPOBJ_bEnum
CLIPOBJ_cEnumStart
CLIPOBJ_ppoGetPath
CancelDC
CheckColorsInGamut
ChoosePixelFormat
Chord
ClearBitmapAttributes
ClearBrushAttributes
CloseEnhMetaFile
CloseFigure
CloseMetaFile
ColorCorrectPalette
ColorMatchToTarget
CombineRgn
CombineTransform
ConfigureOPMProtectedOutput
CopyEnhMetaFileA
CopyEnhMetaFileW
CopyMetaFileA
CopyMetaFileW
CreateBitmap
CreateBitmapFromDxSurface
CreateBitmapFromDxSurface2
CreateBitmapIndirect
CreateBrushIndirect
CreateColorSpaceA
CreateColorSpaceW
CreateCompatibleBitmap
CreateCompatibleDC
CreateDCA
CreateDCW
CreateDIBPatternBrush
CreateDIBPatternBrushPt
CreateDIBSection
CreateDIBitmap
CreateDPIScaledDIBSection
CreateDiscardableBitmap
CreateEllipticRgn
CreateEllipticRgnIndirect
CreateEnhMetaFileA
CreateEnhMetaFileW
CreateFontA
CreateFontIndirectA
CreateFontIndirectExA
CreateFontIndirectExW
CreateFontIndirectW
CreateFontW
CreateHalftonePalette
CreateHatchBrush
CreateICA
CreateICW
CreateMetaFileA
CreateMetaFileW
CreateOPMProtectedOutput
CreateOPMProtectedOutputs
CreatePalette
CreatePatternBrush
CreatePen
CreatePenIndirect
CreatePolyPolygonRgn
CreatePolygonRgn
CreateRectRgn
CreateRectRgnIndirect
CreateRoundRectRgn
CreateScalableFontResourceA
CreateScalableFontResourceW
CreateScaledCompatibleBitmap
CreateSessionMappedDIBSection
CreateSolidBrush
D3DKMTAbandonSwapChain
D3DKMTAcquireKeyedMutex
D3DKMTAcquireKeyedMutex2
D3DKMTAcquireSwapChain
D3DKMTAddSurfaceToSwapChain
D3DKMTAdjustFullscreenGamma
D3DKMTCacheHybridQueryValue
D3DKMTChangeVideoMemoryReservation
D3DKMTCheckExclusiveOwnership
D3DKMTCheckMonitorPowerState
D3DKMTCheckMultiPlaneOverlaySupport
D3DKMTCheckMultiPlaneOverlaySupport2
D3DKMTCheckMultiPlaneOverlaySupport3
D3DKMTCheckOcclusion
D3DKMTCheckSharedResourceAccess
D3DKMTCheckVidPnExclusiveOwnership
D3DKMTCloseAdapter
D3DKMTConfigureSharedResource
D3DKMTCreateAllocation
D3DKMTCreateAllocation2
D3DKMTCreateBundleObject
D3DKMTCreateContext
D3DKMTCreateContextVirtual
D3DKMTCreateDCFromMemory
D3DKMTCreateDevice
D3DKMTCreateHwContext
D3DKMTCreateHwQueue
D3DKMTCreateKeyedMutex
D3DKMTCreateKeyedMutex2
D3DKMTCreateOutputDupl
D3DKMTCreateOverlay
D3DKMTCreatePagingQueue
D3DKMTCreateProtectedSession
D3DKMTCreateSwapChain
D3DKMTCreateSynchronizationObject
D3DKMTCreateSynchronizationObject2
D3DKMTCreateTrackedWorkload
D3DKMTDDisplayEnum
D3DKMTDestroyAllocation
D3DKMTDestroyAllocation2
D3DKMTDestroyContext
D3DKMTDestroyDCFromMemory
D3DKMTDestroyDevice
D3DKMTDestroyHwContext
D3DKMTDestroyHwQueue
D3DKMTDestroyKeyedMutex
D3DKMTDestroyOutputDupl
D3DKMTDestroyOverlay
D3DKMTDestroyPagingQueue
D3DKMTDestroyProtectedSession
D3DKMTDestroySynchronizationObject
D3DKMTDestroyTrackedWorkload
D3DKMTDispMgrCreate
D3DKMTDispMgrOperation
D3DKMTDispMgrSourceOperation
D3DKMTDispMgrTargetOperation
D3DKMTDisplayPortOperation
D3DKMTDuplicateHandle
D3DKMTEnumAdapters
D3DKMTEnumAdapters2
D3DKMTEnumAdapters3
D3DKMTEscape
D3DKMTEvict
D3DKMTExtractBundleObject
D3DKMTFlipOverlay
D3DKMTFlushHeapTransitions
D3DKMTFreeGpuVirtualAddress
D3DKMTGetAllocationPriority
D3DKMTGetAvailableTrackedWorkloadIndex
D3DKMTGetCachedHybridQueryValue
D3DKMTGetContextInProcessSchedulingPriority
D3DKMTGetContextSchedulingPriority
D3DKMTGetDWMVerticalBlankEvent
D3DKMTGetDeviceState
D3DKMTGetDisplayModeList
D3DKMTGetMemoryBudgetTarget
D3DKMTGetMultiPlaneOverlayCaps
D3DKMTGetMultisampleMethodList
D3DKMTGetOverlayState
D3DKMTGetPostCompositionCaps
D3DKMTGetPresentHistory
D3DKMTGetPresentQueueEvent
D3DKMTGetProcessDeviceRemovalSupport
D3DKMTGetProcessList
D3DKMTGetProcessSchedulingPriorityBand
D3DKMTGetProcessSchedulingPriorityClass
D3DKMTGetResourcePresentPrivateDriverData
D3DKMTGetRuntimeData
D3DKMTGetScanLine
D3DKMTGetSetSwapChainMetadata
D3DKMTGetSharedPrimaryHandle
D3DKMTGetSharedResourceAdapterLuid
D3DKMTGetTrackedWorkloadStatistics
D3DKMTGetYieldPercentage
D3DKMTInvalidateActiveVidPn
D3DKMTInvalidateCache
D3DKMTLock
D3DKMTLock2
D3DKMTMakeResident
D3DKMTMapGpuVirtualAddress
D3DKMTMarkDeviceAsError
D3DKMTNetDispGetNextChunkInfo
D3DKMTNetDispQueryMiracastDisplayDeviceStatus
D3DKMTNetDispQueryMiracastDisplayDeviceSupport
D3DKMTNetDispStartMiracastDisplayDevice
D3DKMTNetDispStartMiracastDisplayDevice2
D3DKMTNetDispStartMiracastDisplayDeviceEx
D3DKMTNetDispStopMiracastDisplayDevice
D3DKMTOfferAllocations
D3DKMTOpenAdapterFromDeviceName
D3DKMTOpenAdapterFromGdiDisplayName
D3DKMTOpenAdapterFromHdc
D3DKMTOpenAdapterFromLuid
D3DKMTOpenBundleObjectNtHandleFromName
D3DKMTOpenKeyedMutex
D3DKMTOpenKeyedMutex2
D3DKMTOpenKeyedMutexFromNtHandle
D3DKMTOpenNtHandleFromName
D3DKMTOpenProtectedSessionFromNtHandle
D3DKMTOpenResource
D3DKMTOpenResource2
D3DKMTOpenResourceFromNtHandle
D3DKMTOpenSwapChain
D3DKMTOpenSyncObjectFromNtHandle
D3DKMTOpenSyncObjectFromNtHandle2
D3DKMTOpenSyncObjectNtHandleFromName
D3DKMTOpenSynchronizationObject
D3DKMTOutputDuplGetFrameInfo
D3DKMTOutputDuplGetMetaData
D3DKMTOutputDuplGetPointerShapeData
D3DKMTOutputDuplPresent
D3DKMTOutputDuplPresentToHwQueue
D3DKMTOutputDuplReleaseFrame
D3DKMTPinDirectFlipResources
D3DKMTPinResources
D3DKMTPollDisplayChildren
D3DKMTPresent
D3DKMTPresentMultiPlaneOverlay
D3DKMTPresentMultiPlaneOverlay2
D3DKMTPresentMultiPlaneOverlay3
D3DKMTPresentRedirected
D3DKMTQueryAdapterInfo
D3DKMTQueryAllocationResidency
D3DKMTQueryClockCalibration
D3DKMTQueryFSEBlock
D3DKMTQueryProcessOfferInfo
D3DKMTQueryProtectedSessionInfoFromNtHandle
D3DKMTQueryProtectedSessionStatus
D3DKMTQueryRemoteVidPnSourceFromGdiDisplayName
D3DKMTQueryResourceInfo
D3DKMTQueryResourceInfoFromNtHandle
D3DKMTQueryStatistics
D3DKMTQueryVidPnExclusiveOwnership
D3DKMTQueryVideoMemoryInfo
D3DKMTReclaimAllocations
D3DKMTReclaimAllocations2
D3DKMTRegisterTrimNotification
D3DKMTRegisterVailProcess
D3DKMTReleaseKeyedMutex
D3DKMTReleaseKeyedMutex2
D3DKMTReleaseProcessVidPnSourceOwners
D3DKMTReleaseSwapChain
D3DKMTRemoveSurfaceFromSwapChain
D3DKMTRender
D3DKMTReserveGpuVirtualAddress
D3DKMTResetTrackedWorkloadStatistics
D3DKMTSetAllocationPriority
D3DKMTSetContextInProcessSchedulingPriority
D3DKMTSetContextSchedulingPriority
D3DKMTSetDisplayMode
D3DKMTSetDisplayPrivateDriverFormat
D3DKMTSetDodIndirectSwapchain
D3DKMTSetFSEBlock
D3DKMTSetGammaRamp
D3DKMTSetHwProtectionTeardownRecovery
D3DKMTSetMemoryBudgetTarget
D3DKMTSetMonitorColorSpaceTransform
D3DKMTSetProcessDeviceRemovalSupport
D3DKMTSetProcessSchedulingPriorityBand
D3DKMTSetProcessSchedulingPriorityClass
D3DKMTSetQueuedLimit
D3DKMTSetStablePowerState
D3DKMTSetStereoEnabled
D3DKMTSetSyncRefreshCountWaitTarget
D3DKMTSetVidPnSourceHwProtection
D3DKMTSetVidPnSourceOwner
D3DKMTSetVidPnSourceOwner1
D3DKMTSetVidPnSourceOwner2
D3DKMTSetYieldPercentage
D3DKMTShareObjects
D3DKMTSharedPrimaryLockNotification
D3DKMTSharedPrimaryUnLockNotification
D3DKMTSignalSynchronizationObject
D3DKMTSignalSynchronizationObject2
D3DKMTSignalSynchronizationObjectFromCpu
D3DKMTSignalSynchronizationObjectFromGpu
D3DKMTSignalSynchronizationObjectFromGpu2
D3DKMTSubmitCommand
D3DKMTSubmitCommandToHwQueue
D3DKMTSubmitPresentBltToHwQueue
D3DKMTSubmitPresentToHwQueue
D3DKMTSubmitSignalSyncObjectsToHwQueue
D3DKMTSubmitWaitForSyncObjectsToHwQueue
D3DKMTTrimProcessCommitment
D3DKMTUnOrderedPresentSwapChain
D3DKMTUnlock
D3DKMTUnlock2
D3DKMTUnpinDirectFlipResources
D3DKMTUnpinResources
D3DKMTUnregisterTrimNotification
D3DKMTUpdateAllocationProperty
D3DKMTUpdateGpuVirtualAddress
D3DKMTUpdateOverlay
D3DKMTUpdateTrackedWorkload
D3DKMTVailConnect
D3DKMTVailDisconnect
D3DKMTVailPromoteCompositionSurface
D3DKMTWaitForIdle
D3DKMTWaitForSynchronizationObject
D3DKMTWaitForSynchronizationObject2
D3DKMTWaitForSynchronizationObjectFromCpu
D3DKMTWaitForSynchronizationObjectFromGpu
D3DKMTWaitForVerticalBlankEvent
D3DKMTWaitForVerticalBlankEvent2
DDCCIGetCapabilitiesString
DDCCIGetCapabilitiesStringLength
DDCCIGetTimingReport
DDCCIGetVCPFeature
DDCCISaveCurrentSettings
DDCCISetVCPFeature
DPtoLP
DdCreateFullscreenSprite
DdDestroyFullscreenSprite
DdEntry0
DdEntry1
DdEntry10
DdEntry11
DdEntry12
DdEntry13
DdEntry14
DdEntry15
DdEntry16
DdEntry17
DdEntry18
DdEntry19
DdEntry2
DdEntry20
DdEntry21
DdEntry22
DdEntry23
DdEntry24
DdEntry25
DdEntry26
DdEntry27
DdEntry28
DdEntry29
DdEntry3
DdEntry30
DdEntry31
DdEntry32
DdEntry33
DdEntry34
DdEntry35
DdEntry36
DdEntry37
DdEntry38
DdEntry39
DdEntry4
DdEntry40
DdEntry41
DdEntry42
DdEntry43
DdEntry44
DdEntry45
DdEntry46
DdEntry47
DdEntry48
DdEntry49
DdEntry5
DdEntry50
DdEntry51
DdEntry52
DdEntry53
DdEntry54
DdEntry55
DdEntry56
DdEntry6
DdEntry7
DdEntry8
DdEntry9
DdNotifyFullscreenSpriteUpdate
DdQueryVisRgnUniqueness
DeleteColorSpace
DeleteDC
DeleteEnhMetaFile
DeleteMetaFile
DeleteObject
DescribePixelFormat
DestroyOPMProtectedOutput
DestroyPhysicalMonitorInternal
DeviceCapabilitiesExA
DeviceCapabilitiesExW
DrawEscape
DxTrimNotificationListHead DATA
Ellipse
EnableEUDC
EndDoc
EndFormPage
EndGdiRendering
EndPage
EndPath
EngAcquireSemaphore
EngAlphaBlend
EngAssociateSurface
EngBitBlt
EngCheckAbort
EngComputeGlyphSet
EngCopyBits
EngCreateBitmap
EngCreateClip
EngCreateDeviceBitmap
EngCreateDeviceSurface
EngCreatePalette
EngCreateSemaphore
EngDeleteClip
EngDeletePalette
EngDeletePath
EngDeleteSemaphore
EngDeleteSurface
EngEraseSurface
EngFillPath
EngFindResource
EngFreeModule
EngGetCurrentCodePage
EngGetDriverName
EngGetPrinterDataFileName
EngGradientFill
EngLineTo
EngLoadModule
EngLockSurface
EngMarkBandingSurface
EngMultiByteToUnicodeN
EngMultiByteToWideChar
EngPaint
EngPlgBlt
EngQueryEMFInfo
EngQueryLocalTime
EngReleaseSemaphore
EngStretchBlt
EngStretchBltROP
EngStrokeAndFillPath
EngStrokePath
EngTextOut
EngTransparentBlt
EngUnicodeToMultiByteN
EngUnlockSurface
EngWideCharToMultiByte
EnumEnhMetaFile
EnumFontFamiliesA
EnumFontFamiliesExA
EnumFontFamiliesExW
EnumFontFamiliesW
EnumFontsA
EnumFontsW
EnumICMProfilesA
EnumICMProfilesW
EnumMetaFile
EnumObjects
EqualRgn
Escape
EudcLoadLinkW
EudcUnloadLinkW
ExcludeClipRect
ExtCreatePen
ExtCreateRegion
ExtEscape
ExtFloodFill
ExtSelectClipRgn
ExtTextOutA
ExtTextOutW
FONTOBJ_cGetAllGlyphHandles
FONTOBJ_cGetGlyphs
FONTOBJ_pQueryGlyphAttrs
FONTOBJ_pfdg
FONTOBJ_pifi
FONTOBJ_pvTrueTypeFontFile
FONTOBJ_pxoGetXform
FONTOBJ_vGetInfo
FillPath
FillRgn
FixBrushOrgEx
FlattenPath
FloodFill
FontIsLinked
FrameRgn
Gdi32DllInitialize
GdiAddFontResourceW
GdiAddGlsBounds
GdiAddGlsRecord
GdiAddInitialFonts
GdiAlphaBlend
GdiArtificialDecrementDriver
GdiBatchLimit DATA
GdiCleanCacheDC
GdiClearStockObjectCache
GdiComment
GdiConsoleTextOut
GdiConvertAndCheckDC
GdiConvertBitmap
GdiConvertBitmapV5
GdiConvertBrush
GdiConvertDC
GdiConvertEnhMetaFile
GdiConvertFont
GdiConvertMetaFilePict
GdiConvertPalette
GdiConvertRegion
GdiConvertToDevmodeW
GdiCreateLocalEnhMetaFile
GdiCreateLocalMetaFilePict
GdiCurrentProcessSplWow64
GdiDeleteLocalDC
GdiDeleteSpoolFileHandle
GdiDescribePixelFormat
GdiDllInitialize
GdiDrawStream
GdiEndDocEMF
GdiEndPageEMF
GdiEntry1
GdiEntry10
GdiEntry11
GdiEntry12
GdiEntry13
GdiEntry14
GdiEntry15
GdiEntry16
GdiEntry2
GdiEntry3
GdiEntry4
GdiEntry5
GdiEntry6
GdiEntry7
GdiEntry8
GdiEntry9
GdiFixUpHandle
GdiFlush
GdiFullscreenControl
GdiGetBatchLimit
GdiGetBitmapBitsSize
GdiGetCharDimensions
GdiGetCodePage
GdiGetDC
GdiGetDevmodeForPage
GdiGetEntry
GdiGetLocalBrush
GdiGetLocalDC
GdiGetLocalFont
GdiGetPageCount
GdiGetPageHandle
GdiGetSpoolFileHandle
GdiGetSpoolMessage
GdiGetVariationStoreDelta
GdiGradientFill
GdiInitSpool
GdiInitializeLanguagePack
GdiIsMetaFileDC
GdiIsMetaPrintDC
GdiIsPlayMetafileDC
GdiIsScreenDC
GdiIsTrackingEnabled
GdiIsUMPDSandboxingEnabled
GdiLoadType1Fonts
GdiPlayDCScript
GdiPlayEMF
GdiPlayJournal
GdiPlayPageEMF
GdiPlayPrivatePageEMF
GdiPlayScript
GdiPrinterThunk
GdiProcessSetup
GdiQueryFonts
GdiQueryTable
GdiRealizationInfo
GdiReleaseDC
GdiReleaseLocalDC
GdiResetDCEMF
GdiSetAttrs
GdiSetBatchLimit
GdiSetLastError
GdiSetPixelFormat
GdiSetServerAttr
GdiStartDocEMF
GdiStartPageEMF
GdiSupportsFontChangeEvent
GdiSwapBuffers
GdiTrackHCreate
GdiTrackHDelete
GdiTransparentBlt
GdiValidateHandle
GetArcDirection
GetAspectRatioFilterEx
GetBitmapAttributes
GetBitmapBits
GetBitmapDimensionEx
GetBitmapDpiScaleValue
GetBkColor
GetBkMode
GetBoundsRect
GetBrushAttributes
GetBrushOrgEx
GetCOPPCompatibleOPMInformation
GetCertificate
GetCertificateByHandle
GetCertificateSize
GetCertificateSizeByHandle
GetCharABCWidthsA
GetCharABCWidthsFloatA
GetCharABCWidthsFloatI
GetCharABCWidthsFloatW
GetCharABCWidthsI
GetCharABCWidthsW
GetCharWidth32A
GetCharWidth32W
GetCharWidthA
GetCharWidthFloatA
GetCharWidthFloatW
GetCharWidthI
GetCharWidthInfo
GetCharWidthW
GetCharacterPlacementA
GetCharacterPlacementW
GetClipBox
GetClipRgn
GetColorAdjustment
GetColorSpace
GetCurrentDpiInfo
GetCurrentObject
GetCurrentPositionEx
GetDCBrushColor
GetDCDpiScaleValue
GetDCOrgEx
GetDCPenColor
GetDIBColorTable
GetDIBits
GetDeviceCaps
GetDeviceGammaRamp
GetETM
GetEUDCTimeStamp
GetEUDCTimeStampExW
GetEnhMetaFileA
GetEnhMetaFileBits
GetEnhMetaFileDescriptionA
GetEnhMetaFileDescriptionW
GetEnhMetaFileHeader
GetEnhMetaFilePaletteEntries
GetEnhMetaFilePixelFormat
GetEnhMetaFileW
GetFontAssocStatus
GetFontData
GetFontFileData
GetFontFileInfo
GetFontLanguageInfo
GetFontRealizationInfo
GetFontResourceInfoW
GetFontUnicodeRanges
GetGlyphIndicesA
GetGlyphIndicesW
GetGlyphOutline
GetGlyphOutlineA
GetGlyphOutlineW
GetGlyphOutlineWow
GetGraphicsMode
GetHFONT
GetICMProfileA
GetICMProfileW
GetKerningPairs
GetKerningPairsA
GetKerningPairsW
GetLayout
GetLogColorSpaceA
GetLogColorSpaceW
GetMapMode
GetMetaFileA
GetMetaFileBitsEx
GetMetaFileW
GetMetaRgn
GetMiterLimit
GetNearestColor
GetNearestPaletteIndex
GetNumberOfPhysicalMonitors
GetOPMInformation
GetOPMRandomNumber
GetObjectA
GetObjectType
GetObjectW
GetOutlineTextMetricsA
GetOutlineTextMetricsW
GetPaletteEntries
GetPath
GetPhysicalMonitorDescription
GetPhysicalMonitors
GetPixel
GetPixelFormat
GetPolyFillMode
GetProcessSessionFonts
GetROP2
GetRandomRgn
GetRasterizerCaps
GetRegionData
GetRelAbs
GetRgnBox
GetStockObject
GetStretchBltMode
GetStringBitmapA
GetStringBitmapW
GetSuggestedOPMProtectedOutputArraySize
GetSystemPaletteEntries
GetSystemPaletteUse
GetTextAlign
GetTextCharacterExtra
GetTextCharset
GetTextCharsetInfo
GetTextColor
GetTextExtentExPointA
GetTextExtentExPointI
GetTextExtentExPointW
GetTextExtentExPointWPri
GetTextExtentPoint32A
GetTextExtentPoint32W
GetTextExtentPointA
GetTextExtentPointI
GetTextExtentPointW
GetTextFaceA
GetTextFaceAliasW
GetTextFaceW
GetTextMetricsA
GetTextMetricsW
GetTransform
GetViewportExtEx
GetViewportOrgEx
GetWinMetaFileBits
GetWindowExtEx
GetWindowOrgEx
GetWorldTransform
HT_Get8BPPFormatPalette
HT_Get8BPPMaskPalette
InternalDeleteDC
InternalDeleteObject
IntersectClipRect
InvertRgn
IsValidEnhMetaRecord
IsValidEnhMetaRecordOffExt
LPtoDP
LineDDA
LineTo
LpkDrawTextEx
LpkEditControl DATA
LpkExtTextOut
LpkGetCharacterPlacement
LpkGetEditControl
LpkGetTextExtentExPoint
LpkInitialize
LpkPSMTextOut
LpkPresent
LpkTabbedTextOut
LpkUseGDIWidthCache
LpkpEditControlSize DATA
LpkpInitializeEditControl
MaskBlt
MirrorRgn
ModerncoreCreateICW
ModerncoreDeleteDC
ModerncoreGdiInit
ModifyWorldTransform
MoveToEx
NamedEscape
OffsetClipRgn
OffsetRgn
OffsetViewportOrgEx
OffsetWindowOrgEx
PATHOBJ_bEnum
PATHOBJ_bEnumClipLines
PATHOBJ_vEnumStart
PATHOBJ_vEnumStartClipLines
PATHOBJ_vGetBounds
PaintRgn
PatBlt
PathToRegion
Pie
PlayEnhMetaFile
PlayEnhMetaFileRecord
PlayMetaFile
PlayMetaFileRecord
PlgBlt
PolyBezier
PolyBezierTo
PolyDraw
PolyPatBlt
PolyPolygon
PolyPolyline
PolyTextOutA
PolyTextOutW
Polygon
Polyline
PolylineTo
PtInRegion
PtVisible
QueryFontAssocStatus
RealizePalette
RectInRegion
RectVisible
Rectangle
RemoveFontMemResourceEx
RemoveFontResourceA
RemoveFontResourceExA
RemoveFontResourceExW
RemoveFontResourceTracking
RemoveFontResourceW
ResetDCA
ResetDCW
ResizePalette
RestoreDC
RoundRect
STROBJ_bEnum
STROBJ_bEnumPositionsOnly
STROBJ_bGetAdvanceWidths
STROBJ_dwGetCodePage
STROBJ_vEnumStart
SaveDC
ScaleRgn
ScaleValues
ScaleViewportExtEx
ScaleWindowExtEx
ScriptApplyDigitSubstitution
ScriptApplyLogicalWidth
ScriptBreak
ScriptCPtoX
ScriptCacheGetHeight
ScriptFreeCache
ScriptGetCMap
ScriptGetFontAlternateGlyphs
ScriptGetFontFeatureTags
ScriptGetFontLanguageTags
ScriptGetFontProperties
ScriptGetFontScriptTags
ScriptGetGlyphABCWidth
ScriptGetLogicalWidths
ScriptGetProperties
ScriptIsComplex
ScriptItemize
ScriptItemizeOpenType
ScriptJustify
ScriptLayout
ScriptPlace
ScriptPlaceOpenType
ScriptPositionSingleGlyph
ScriptRecordDigitSubstitution
ScriptShape
ScriptShapeOpenType
ScriptStringAnalyse
ScriptStringCPtoX
ScriptStringFree
ScriptStringGetLogicalWidths
ScriptStringGetOrder
ScriptStringOut
ScriptStringValidate
ScriptStringXtoCP
ScriptString_pLogAttr
ScriptString_pSize
ScriptString_pcOutChars
ScriptSubstituteSingleGlyph
ScriptTextOut
ScriptXtoCP
SelectBrushLocal
SelectClipPath
SelectClipRgn
SelectFontLocal
SelectObject
SelectPalette
SetAbortProc
SetArcDirection
SetBitmapAttributes
SetBitmapBits
SetBitmapDimensionEx
SetBkColor
SetBkMode
SetBoundsRect
SetBrushAttributes
SetBrushOrgEx
SetColorAdjustment
SetColorSpace
SetDCBrushColor
SetDCPenColor
SetDIBColorTable
SetDIBits
SetDIBitsToDevice
SetDeviceGammaRamp
SetEnhMetaFileBits
SetFontEnumeration
SetGraphicsMode
SetICMMode
SetICMProfileA
SetICMProfileW
SetLayout
SetLayoutWidth
SetMagicColors
SetMapMode
SetMapperFlags
SetMetaFileBitsEx
SetMetaRgn
SetMiterLimit
SetOPMSigningKeyAndSequenceNumbers
SetPaletteEntries
SetPixel
SetPixelFormat
SetPixelV
SetPolyFillMode
SetROP2
SetRectRgn
SetRelAbs
SetStretchBltMode
SetSystemPaletteUse
SetTextAlign
SetTextCharacterExtra
SetTextColor
SetTextJustification
SetViewportExtEx
SetViewportOrgEx
SetVirtualResolution
SetWinMetaFileBits
SetWindowExtEx
SetWindowOrgEx
SetWorldTransform
StartDocA
StartDocW
StartFormPage
StartPage
StretchBlt
StretchDIBits
StrokeAndFillPath
StrokePath
SwapBuffers
TextOutA
TextOutW
TranslateCharsetInfo
UnloadNetworkFonts
UnrealizeObject
UpdateColors
UpdateICMRegKeyA
UpdateICMRegKeyW
UspAllocCache
UspAllocTemp
UspFreeMem
WidenPath
XFORMOBJ_bApplyXform
XFORMOBJ_iGetXform
XLATEOBJ_cGetPalette
XLATEOBJ_hGetColorTransform
XLATEOBJ_iXlate
XLATEOBJ_piVector
bCreateDCW
bDeleteLDC
bInitSystemAndFontsDirectoriesW
bMakePathNameW
cGetTTFFromFOT
fpClosePrinter DATA
ftsWordBreak
gMaxGdiHandleCount DATA
gW32PID DATA
g_systemCallFilterId DATA
gdiPlaySpoolStream
ghICM DATA
hGetPEBHandle
pGdiDevCaps DATA
pGdiSharedHandleTable DATA
pGdiSharedMemory DATA
pldcGet
semDxTrimNotification DATA
vSetPldc
CreateDCExW
