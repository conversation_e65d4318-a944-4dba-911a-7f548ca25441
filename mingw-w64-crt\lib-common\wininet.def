;
; Definition file of WININET.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "WININET.dll"
EXPORTS
DispatchAPICall
AppCacheCheckManifest
AppCacheCloseHandle
AppCacheCreateAndCommitFile
AppCacheDeleteGroup
AppCacheDeleteIEGroup
AppCacheDuplicateHandle
AppCacheFinalize
AppCacheFreeDownloadList
AppCacheFreeGroupList
AppCacheFreeIESpace
AppCacheFreeSpace
AppCacheGetDownloadList
AppCacheGetFallbackUrl
AppCacheGetGroupList
AppCacheGetIEGroupList
AppCacheGetInfo
AppCacheGetManifestUrl
AppCacheLookup
CommitUrlCacheEntryA
CommitUrlCacheEntryBinaryBlob
CommitUrlCacheEntryW
CreateMD5SSOHash
CreateUrlCacheContainerA
CreateUrlCacheContainerW
CreateUrlCacheEntryA
CreateUrlCacheEntryExW
CreateUrlCacheE<PERSON>ryW
CreateUrlCacheGroup
DeleteIE3Cache
DeleteUrlCacheContainerA
DeleteUrlCacheContainerW
DeleteUrlCacheEntry
DeleteUrlCacheEntryA
DeleteUrlCacheEntryW
DeleteUrlCacheGroup
DeleteWpadCacheForNetworks
DetectAutoProxyUrl
FindCloseUrlCache
FindFirstUrlCacheContainerA
FindFirstUrlCacheContainerW
FindFirstUrlCacheEntryA
FindFirstUrlCacheEntryExA
FindFirstUrlCacheEntryExW
FindFirstUrlCacheEntryW
FindFirstUrlCacheGroup
FindNextUrlCacheContainerA
FindNextUrlCacheContainerW
FindNextUrlCacheEntryA
FindNextUrlCacheEntryExA
FindNextUrlCacheEntryExW
FindNextUrlCacheEntryW
FindNextUrlCacheGroup
ForceNexusLookup
ForceNexusLookupExW
FreeUrlCacheSpaceA
FreeUrlCacheSpaceW
FtpCommandA
FtpCommandW
FtpCreateDirectoryA
FtpCreateDirectoryW
FtpDeleteFileA
FtpDeleteFileW
FtpFindFirstFileA
FtpFindFirstFileW
FtpGetCurrentDirectoryA
FtpGetCurrentDirectoryW
FtpGetFileA
FtpGetFileEx
FtpGetFileSize
FtpGetFileW
FtpOpenFileA
FtpOpenFileW
FtpPutFileA
FtpPutFileEx
FtpPutFileW
FtpRemoveDirectoryA
FtpRemoveDirectoryW
FtpRenameFileA
FtpRenameFileW
FtpSetCurrentDirectoryA
FtpSetCurrentDirectoryW
GetProxyDllInfo
GetUrlCacheConfigInfoA
GetUrlCacheConfigInfoW
GetUrlCacheEntryBinaryBlob
GetUrlCacheEntryInfoA
GetUrlCacheEntryInfoExA
GetUrlCacheEntryInfoExW
GetUrlCacheEntryInfoW
GetUrlCacheGroupAttributeA
GetUrlCacheGroupAttributeW
GetUrlCacheHeaderData
GopherCreateLocatorA
GopherCreateLocatorW
GopherFindFirstFileA
GopherFindFirstFileW
GopherGetAttributeA
GopherGetAttributeW
GopherGetLocatorTypeA
GopherGetLocatorTypeW
GopherOpenFileA
GopherOpenFileW
HttpAddRequestHeadersA
HttpAddRequestHeadersW
HttpCheckDavCompliance
HttpCloseDependencyHandle
HttpDuplicateDependencyHandle
HttpEndRequestA
HttpEndRequestW
HttpGetServerCredentials
HttpGetTunnelSocket
HttpIndicatePageLoadComplete
HttpIsHostHstsEnabled
HttpOpenDependencyHandle
HttpOpenRequestA
HttpOpenRequestW
HttpPushClose
HttpPushEnable
HttpPushWait
HttpQueryInfoA
HttpQueryInfoW
HttpSendRequestA
HttpSendRequestExA
HttpSendRequestExW
HttpSendRequestW
HttpWebSocketClose
HttpWebSocketCompleteUpgrade
HttpWebSocketQueryCloseStatus
HttpWebSocketReceive
HttpWebSocketSend
HttpWebSocketShutdown
IncrementUrlCacheHeaderData
InternetAlgIdToStringA
InternetAlgIdToStringW
InternetAttemptConnect
InternetAutodial
InternetAutodialCallback
InternetAutodialHangup
InternetCanonicalizeUrlA
InternetCanonicalizeUrlW
InternetCheckConnectionA
InternetCheckConnectionW
InternetClearAllPerSiteCookieDecisions
InternetCloseHandle
InternetCombineUrlA
InternetCombineUrlW
InternetConfirmZoneCrossing
InternetConfirmZoneCrossingA
InternetConfirmZoneCrossingW
InternetConnectA
InternetConnectW
InternetConvertUrlFromWireToWideChar
InternetCrackUrlA
InternetCrackUrlW
InternetCreateUrlA
InternetCreateUrlW
InternetDial
InternetDialA
InternetDialW
InternetEnumPerSiteCookieDecisionA
InternetEnumPerSiteCookieDecisionW
InternetErrorDlg
InternetFindNextFileA
InternetFindNextFileW
InternetFortezzaCommand
InternetFreeCookies
InternetFreeProxyInfoList
InternetGetCertByURL
InternetGetCertByURLA
InternetGetConnectedState
InternetGetConnectedStateEx
InternetGetConnectedStateExA
InternetGetConnectedStateExW
InternetGetCookieA
InternetGetCookieEx2
InternetGetCookieExA
InternetGetCookieExW
InternetGetCookieW
InternetGetLastResponseInfoA
InternetGetLastResponseInfoW
InternetGetPerSiteCookieDecisionA
InternetGetPerSiteCookieDecisionW
InternetGetProxyForUrl
InternetGetSecurityInfoByURL
InternetGetSecurityInfoByURLA
InternetGetSecurityInfoByURLW
InternetGoOnline
InternetGoOnlineA
InternetGoOnlineW
InternetHangUp
InternetInitializeAutoProxyDll
InternetLockRequestFile
InternetOpenA
InternetOpenUrlA
InternetOpenUrlW
InternetOpenW
InternetQueryDataAvailable
InternetQueryFortezzaStatus
InternetQueryOptionA
InternetQueryOptionW
InternetReadFile
InternetReadFileExA
InternetReadFileExW
InternetSecurityProtocolToStringA
InternetSecurityProtocolToStringW
InternetSetCookieA
InternetSetCookieEx2
InternetSetCookieExA
InternetSetCookieExW
InternetSetCookieW
InternetSetDialState
InternetSetDialStateA
InternetSetDialStateW
InternetSetFilePointer
InternetSetOptionA
InternetSetOptionExA
InternetSetOptionExW
InternetSetOptionW
InternetSetPerSiteCookieDecisionA
InternetSetPerSiteCookieDecisionW
InternetSetStatusCallback
InternetSetStatusCallbackA
InternetSetStatusCallbackW
InternetShowSecurityInfoByURL
InternetShowSecurityInfoByURLA
InternetShowSecurityInfoByURLW
InternetTimeFromSystemTime
InternetTimeFromSystemTimeA
InternetTimeFromSystemTimeW
InternetTimeToSystemTime
InternetTimeToSystemTimeA
InternetTimeToSystemTimeW
InternetUnlockRequestFile
InternetWriteFile
InternetWriteFileExA
InternetWriteFileExW
IsHostInProxyBypassList
IsUrlCacheEntryExpiredA
IsUrlCacheEntryExpiredW
LoadUrlCacheContent
ParseX509EncodedCertificateForListBoxEntry
PrivacyGetZonePreferenceW
PrivacySetZonePreferenceW
ReadUrlCacheEntryStream
ReadUrlCacheEntryStreamEx
RegisterUrlCacheNotification
ResumeSuspendedDownload
RetrieveUrlCacheEntryFileA
RetrieveUrlCacheEntryFileW
RetrieveUrlCacheEntryStreamA
RetrieveUrlCacheEntryStreamW
RunOnceUrlCache
SetUrlCacheConfigInfoA
SetUrlCacheConfigInfoW
SetUrlCacheEntryGroup
SetUrlCacheEntryGroupA
SetUrlCacheEntryGroupW
SetUrlCacheEntryInfoA
SetUrlCacheEntryInfoW
SetUrlCacheGroupAttributeA
SetUrlCacheGroupAttributeW
SetUrlCacheHeaderData
ShowCertificate
ShowClientAuthCerts
ShowSecurityInfo
ShowX509EncodedCertificate
UnlockUrlCacheEntryFile
UnlockUrlCacheEntryFileA
UnlockUrlCacheEntryFileW
UnlockUrlCacheEntryStream
UpdateUrlCacheContentPath
UrlCacheCheckEntriesExist
UrlCacheCloseEntryHandle
UrlCacheContainerSetEntryMaximumAge
UrlCacheCreateContainer
UrlCacheFindFirstEntry
UrlCacheFindNextEntry
UrlCacheFreeEntryInfo
UrlCacheFreeGlobalSpace
UrlCacheGetContentPaths
UrlCacheGetEntryInfo
UrlCacheGetGlobalCacheSize
UrlCacheGetGlobalLimit
UrlCacheReadEntryStream
UrlCacheReloadSettings
UrlCacheRetrieveEntryFile
UrlCacheRetrieveEntryStream
UrlCacheServer
UrlCacheSetGlobalLimit
UrlCacheUpdateEntryExtraData
UrlZonesDetach
_GetFileExtensionFromUrl
