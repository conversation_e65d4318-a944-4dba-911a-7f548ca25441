;
; Definition file of WIM<PERSON>PI.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "WIMGAPI.DLL"
EXPORTS
;DllCanUnloadNow
;DllMain
WIMAddImagePath
WIMAddImagePaths
WIMAddWimbootEntry
WIMApplyImage
WIMCaptureImage
WIMCloseHandle
WIMCommitImageHandle
WIMCopyFile
WIMCreateFile
WIMCreateImageFile
WIMCreateWofCompressedFile
WIMDeleteImage
WIMDeleteImageMounts
WIMEnumImageFiles
WIMExportImage
WIMExtractImageDirectory
WIMExtractImagePath
WIMFindFirstImageFile
WIMFindNextImageFile
WIMGetAttributes
WIMGetImageCount
WIMGetImageInformation
WIMGetMessageCallbackCount
WIMGetMountedImageHandle
WIMGetMountedImageInfo
WIMGetMountedImageInfoFromHandle
WIMGetMountedImages
WIM<PERSON>etWIMB<PERSON><PERSON><PERSON>ries
WIMGetWIMBootWIMPath
WIMInitFileIOCallbacks
WIMInitializeWofDriver
WIMIsCurrentSystemWimboot
WIMIsReferenceWim
WIMLoadImage
WIMMountImage
WIMMountImageHandle
WIMProcessCustomImage
WIMReadFileEx
WIMReadImageFile
WIMRedirectFolderBeforeApply
WIMRegisterLogFile
WIMRegisterMessageCallback
WIMRemountImage
WIMSetBootImage
WIMSetFileIOCallbackTemporaryPath
WIMSetImageInformation
WIMSetImageUserSpecifiedCreationTime
WIMSetReferenceFile
WIMSetTemporaryPath
WIMSetWimGuid
WIMSingleInstanceFile
WIMSplitFile
WIMUnmountImage
WIMUnmountImageHandle
WIMUnregisterLogFile
WIMUnregisterMessageCallback
WIMUpdateWIMBootEntry
WIMWriteFileWithIntegrity
