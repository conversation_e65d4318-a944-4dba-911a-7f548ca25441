LIBRARY "d3d12.dll"
EXPORTS
GetBehaviorValue
D3D12CreateDevice
D3D12GetDebugInterface
SetAppCompatStringPointer
D3D12CoreCreateLayeredDevice
D3D12CoreGetLayeredDeviceSize
D3D12CoreRegisterLayers
D3D12CreateRootSignatureDeserializer
D3D12CreateVersionedRootSignatureDeserializer
D3D12DeviceRemovedExtendedData DATA
D3D12EnableExperimentalFeatures
D3D12PIXEventsReplaceBlock
D3D12PIXGetThreadInfo
D3D12PIXNotifyWakeFromFenceSignal
D3D12PIXReportCounter
D3D12SerializeRootSignature
D3D12SerializeVersionedRootSignature
