/*
 * Copyright 2020 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "d3dcommon.idl";

typedef enum D3D12_SHADER_VERSION_TYPE
{
    D3D12_SHVER_PIXEL_SHADER = 0x0,
    D3D12_SHVER_VERTEX_SHADER = 0x1,
    D3D12_SHVER_GEOMETRY_SHADER = 0x2,
    D3D12_SHVER_HULL_SHADER = 0x3,
    D3D12_SHVER_DOMAIN_SHADER = 0x4,
    D3D12_SHVER_COMPUTE_SHADER = 0x5,
    D3D12_SHVER_RESERVED0 = 0xfff0,
} D3D12_SHADER_VERSION_TYPE;

typedef struct _D3D12_SHADER_DESC
{
    UINT Version;
    const char *Creator;
    UINT Flags;
    UINT ConstantBuffers;
    UINT BoundResources;
    UINT InputParameters;
    UINT OutputParameters;
    UINT InstructionCount;
    UINT TempRegisterCount;
    UINT TempArrayCount;
    UINT DefCount;
    UINT DclCount;
    UINT TextureNormalInstructions;
    UINT TextureLoadInstructions;
    UINT TextureCompInstructions;
    UINT TextureBiasInstructions;
    UINT TextureGradientInstructions;
    UINT FloatInstructionCount;
    UINT IntInstructionCount;
    UINT UintInstructionCount;
    UINT StaticFlowControlCount;
    UINT DynamicFlowControlCount;
    UINT MacroInstructionCount;
    UINT ArrayInstructionCount;
    UINT CutInstructionCount;
    UINT EmitInstructionCount;
    D3D_PRIMITIVE_TOPOLOGY GSOutputTopology;
    UINT GSMaxOutputVertexCount;
    D3D_PRIMITIVE InputPrimitive;
    UINT PatchConstantParameters;
    UINT cGSInstanceCount;
    UINT cControlPoints;
    D3D_TESSELLATOR_OUTPUT_PRIMITIVE HSOutputPrimitive;
    D3D_TESSELLATOR_PARTITIONING HSPartitioning;
    D3D_TESSELLATOR_DOMAIN TessellatorDomain;
    UINT cBarrierInstructions;
    UINT cInterlockedInstructions;
    UINT cTextureStoreInstructions;
} D3D12_SHADER_DESC;

typedef struct _D3D12_SHADER_VARIABLE_DESC
{
    const char *Name;
    UINT StartOffset;
    UINT Size;
    UINT uFlags;
    void *DefaultValue;
    UINT StartTexture;
    UINT TextureSize;
    UINT StartSampler;
    UINT SamplerSize;
} D3D12_SHADER_VARIABLE_DESC;

typedef struct _D3D12_SHADER_TYPE_DESC
{
    D3D_SHADER_VARIABLE_CLASS Class;
    D3D_SHADER_VARIABLE_TYPE Type;
    UINT Rows;
    UINT Columns;
    UINT Elements;
    UINT Members;
    UINT Offset;
    const char *Name;
} D3D12_SHADER_TYPE_DESC;

typedef struct _D3D12_SHADER_BUFFER_DESC
{
    const char *Name;
    D3D_CBUFFER_TYPE Type;
    UINT Variables;
    UINT Size;
    UINT uFlags;
} D3D12_SHADER_BUFFER_DESC;

typedef struct _D3D12_SHADER_INPUT_BIND_DESC
{
    const char *Name;
    D3D_SHADER_INPUT_TYPE Type;
    UINT BindPoint;
    UINT BindCount;
    UINT uFlags;
    D3D_RESOURCE_RETURN_TYPE ReturnType;
    D3D_SRV_DIMENSION Dimension;
    UINT NumSamples;
    UINT Space;
    UINT uID;
} D3D12_SHADER_INPUT_BIND_DESC;

typedef struct _D3D12_SIGNATURE_PARAMETER_DESC
{
    const char *SemanticName;
    UINT SemanticIndex;
    UINT Register;
    D3D_NAME SystemValueType;
    D3D_REGISTER_COMPONENT_TYPE ComponentType;
    BYTE Mask;
    BYTE ReadWriteMask;
    UINT Stream;
    D3D_MIN_PRECISION MinPrecision;
} D3D12_SIGNATURE_PARAMETER_DESC;

typedef struct _D3D12_PARAMETER_DESC
{
    const char *Name;
    const char *SemanticName;
    D3D_SHADER_VARIABLE_TYPE Type;
    D3D_SHADER_VARIABLE_CLASS Class;
    UINT Rows;
    UINT Columns;
    D3D_INTERPOLATION_MODE InterpolationMode;
    D3D_PARAMETER_FLAGS Flags;
    UINT FirstInRegister;
    UINT FirstInComponent;
    UINT FirstOutRegister;
    UINT FirstOutComponent;
} D3D12_PARAMETER_DESC;

typedef struct _D3D12_FUNCTION_DESC
{
    UINT Version;
    const char *Creator;
    UINT Flags;
    UINT ConstantBuffers;
    UINT BoundResources;
    UINT InstructionCount;
    UINT TempRegisterCount;
    UINT TempArrayCount;
    UINT DefCount;
    UINT DclCount;
    UINT TextureNormalInstructions;
    UINT TextureLoadInstructions;
    UINT TextureCompInstructions;
    UINT TextureBiasInstructions;
    UINT TextureGradientInstructions;
    UINT FloatInstructionCount;
    UINT IntInstructionCount;
    UINT UintInstructionCount;
    UINT StaticFlowControlCount;
    UINT DynamicFlowControlCount;
    UINT MacroInstructionCount;
    UINT ArrayInstructionCount;
    UINT MovInstructionCount;
    UINT MovcInstructionCount;
    UINT ConversionInstructionCount;
    UINT BitwiseInstructionCount;
    D3D_FEATURE_LEVEL MinFeatureLevel;
    UINT64 RequiredFeatureFlags;
    const char *Name;
    INT FunctionParameterCount;
    BOOL HasReturn;
    BOOL Has10Level9VertexShader;
    BOOL Has10Level9PixelShader;
} D3D12_FUNCTION_DESC;

typedef struct _D3D12_LIBRARY_DESC
{
    const char *Creator;
    UINT Flags;
    UINT FunctionCount;
} D3D12_LIBRARY_DESC;

interface ID3D12ShaderReflectionConstantBuffer;

[
    uuid(e913c351-783d-48ca-a1d1-4f306284ad56),
    object,
    local,
]
interface ID3D12ShaderReflectionType
{
    HRESULT GetDesc(D3D12_SHADER_TYPE_DESC *desc);
    ID3D12ShaderReflectionType *GetMemberTypeByIndex(UINT index);
    ID3D12ShaderReflectionType *GetMemberTypeByName(const char *name);
    const char *GetMemberTypeName(UINT index);
    HRESULT IsEqual(ID3D12ShaderReflectionType *type);
    ID3D12ShaderReflectionType *GetSubType();
    ID3D12ShaderReflectionType *GetBaseClass();
    UINT GetNumInterfaces();
    ID3D12ShaderReflectionType *GetInterfaceByIndex(UINT index);
    HRESULT IsOfType(ID3D12ShaderReflectionType *type);
    HRESULT ImplementsInterface(ID3D12ShaderReflectionType *base);
}

[
    uuid(8337a8a6-a216-444a-b2f4-314733a73aea),
    object,
    local,
]
interface ID3D12ShaderReflectionVariable
{
    HRESULT GetDesc(D3D12_SHADER_VARIABLE_DESC *desc);
    ID3D12ShaderReflectionType *GetType();
    ID3D12ShaderReflectionConstantBuffer *GetBuffer();
    UINT GetInterfaceSlot(UINT index);
}

[
    uuid(c59598b4-48b3-4869-b9b1-b1618b14a8b7),
    object,
    local,
]
interface ID3D12ShaderReflectionConstantBuffer
{
    HRESULT GetDesc(D3D12_SHADER_BUFFER_DESC *desc);
    ID3D12ShaderReflectionVariable *GetVariableByIndex(UINT index);
    ID3D12ShaderReflectionVariable *GetVariableByName(const char *name);
}

[
    uuid(5a58797d-a72c-478d-8ba2-efc6b0efe88e),
    object,
    local,
]
interface ID3D12ShaderReflection : IUnknown
{
    HRESULT GetDesc(D3D12_SHADER_DESC *desc);
    ID3D12ShaderReflectionConstantBuffer *GetConstantBufferByIndex(UINT index);
    ID3D12ShaderReflectionConstantBuffer *GetConstantBufferByName(const char *name);
    HRESULT GetResourceBindingDesc(UINT index, D3D12_SHADER_INPUT_BIND_DESC *desc);
    HRESULT GetInputParameterDesc(UINT index, D3D12_SIGNATURE_PARAMETER_DESC *desc);
    HRESULT GetOutputParameterDesc(UINT index, D3D12_SIGNATURE_PARAMETER_DESC *desc);
    HRESULT GetPatchConstantParameterDesc(UINT index, D3D12_SIGNATURE_PARAMETER_DESC *desc);
    ID3D12ShaderReflectionVariable *GetVariableByName(const char *name);
    HRESULT GetResourceBindingDescByName(const char *name, D3D12_SHADER_INPUT_BIND_DESC *desc);
    UINT GetMovInstructionCount();
    UINT GetMovcInstructionCount();
    UINT GetConversionInstructionCount();
    UINT GetBitwiseInstructionCount();
    D3D_PRIMITIVE GetGSInputPrimitive();
    BOOL IsSampleFrequencyShader();
    UINT GetNumInterfaceSlots();
    HRESULT GetMinFeatureLevel(D3D_FEATURE_LEVEL *level);
    UINT GetThreadGroupSize(UINT *sizex, UINT *sizey, UINT *sizez);
    UINT64 GetRequiresFlags();
}

[
    uuid(ec25f42d-7006-4f2b-b33e-02cc3375733f),
    object,
    local,
]
interface ID3D12FunctionParameterReflection
{
    HRESULT GetDesc(D3D12_PARAMETER_DESC *desc);
}

[
    uuid(1108795c-2772-4ba9-b2a8-d464dc7e2799),
    object,
    local,
]
interface ID3D12FunctionReflection
{
    HRESULT GetDesc(D3D12_FUNCTION_DESC *desc);
    ID3D12ShaderReflectionConstantBuffer *GetConstantBufferByIndex(UINT index);
    ID3D12ShaderReflectionConstantBuffer *GetConstantBufferByName(const char *name);
    HRESULT GetResourceBindingDesc(UINT index, D3D12_SHADER_INPUT_BIND_DESC *desc);
    ID3D12ShaderReflectionVariable *GetVariableByName(const char *name);
    HRESULT GetResourceBindingDescByName(const char *name, D3D12_SHADER_INPUT_BIND_DESC *desc);
    ID3D12FunctionParameterReflection *GetFunctionParameter(INT index);
}

[
    uuid(8e349d19-54db-4a56-9dc9-119d87bdb804),
    object,
    local,
]
interface ID3D12LibraryReflection : IUnknown
{
    HRESULT GetDesc(D3D12_LIBRARY_DESC *desc);
    ID3D12FunctionReflection *GetFunctionByIndex(INT index);
}
