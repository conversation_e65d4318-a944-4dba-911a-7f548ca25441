<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: sMToken_base Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>sMToken_base Struct Reference</h1><!-- doxytag: class="sMToken_base" -->
<p><code>#include &lt;<a class="el" href="m__token_8h_source.html">m_token.h</a>&gt;</code></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__base.html#a97bc54568330f5df3c61a99bd0721078">kind</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__base.html#a06c35134d5aee4c88c50576e1f2a62ce">subkind</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">union <a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__base.html#ae1284da4479fcba21ad51e471b89bd24">chain</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__base.html#a8ded4c9376b5162e1951127611fcaf93">flags</a></td></tr>
</table>
<hr/><a name="_details"></a><h2>Detailed Description</h2>
<p>Token base descriptor header. Descibes the type of token being processed. </p>
<hr/><h2>Field Documentation</h2>
<a class="anchor" id="ae1284da4479fcba21ad51e471b89bd24"></a><!-- doxytag: member="sMToken_base::chain" ref="ae1284da4479fcba21ad51e471b89bd24" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union <a class="el" href="unionu_m_token.html">uMToken</a>* <a class="el" href="structs_m_token__base.html#ae1284da4479fcba21ad51e471b89bd24">sMToken_base::chain</a><code> [write]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Pointer to next token. NULL terminated. </p>

</div>
</div>
<a class="anchor" id="a8ded4c9376b5162e1951127611fcaf93"></a><!-- doxytag: member="sMToken_base::flags" ref="a8ded4c9376b5162e1951127611fcaf93" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="structs_m_token__base.html#a8ded4c9376b5162e1951127611fcaf93">sMToken_base::flags</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Token flags. </p>

</div>
</div>
<a class="anchor" id="a97bc54568330f5df3c61a99bd0721078"></a><!-- doxytag: member="sMToken_base::kind" ref="a97bc54568330f5df3c61a99bd0721078" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> <a class="el" href="structs_m_token__base.html#a97bc54568330f5df3c61a99bd0721078">sMToken_base::kind</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Token kind. </p>

</div>
</div>
<a class="anchor" id="a06c35134d5aee4c88c50576e1f2a62ce"></a><!-- doxytag: member="sMToken_base::subkind" ref="a06c35134d5aee4c88c50576e1f2a62ce" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> <a class="el" href="structs_m_token__base.html#a06c35134d5aee4c88c50576e1f2a62ce">sMToken_base::subkind</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Token Subkind. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="m__token_8h_source.html">m_token.h</a></li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
