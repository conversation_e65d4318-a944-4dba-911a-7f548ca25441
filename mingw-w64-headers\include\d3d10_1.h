/*** Autogenerated by WIDL 8.5 from include/d3d10_1.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d10_1_h__
#define __d3d10_1_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __ID3D10BlendState1_FWD_DEFINED__
#define __ID3D10BlendState1_FWD_DEFINED__
typedef interface ID3D10BlendState1 ID3D10BlendState1;
#ifdef __cplusplus
interface ID3D10BlendState1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10ShaderResourceView1_FWD_DEFINED__
#define __ID3D10ShaderResourceView1_FWD_DEFINED__
typedef interface ID3D10ShaderResourceView1 ID3D10ShaderResourceView1;
#ifdef __cplusplus
interface ID3D10ShaderResourceView1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10Device1_FWD_DEFINED__
#define __ID3D10Device1_FWD_DEFINED__
typedef interface ID3D10Device1 ID3D10Device1;
#ifdef __cplusplus
interface ID3D10Device1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <d3d10.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef _D3D10_1_CONSTANTS
#define _D3D10_1_CONSTANTS
#define D3D10_1_DEFAULT_SAMPLE_MASK (0xffffffff)

#define D3D10_1_GS_INPUT_REGISTER_COUNT (32)

#define D3D10_1_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT (32)

#define D3D10_1_IA_VERTEX_INPUT_STRUCTURE_ELEMENTS_COMPONENTS (128)

#define D3D10_1_IA_VERTEX_INPUT_STRUCTURE_ELEMENT_COUNT (32)

#define D3D10_1_PS_OUTPUT_MASK_REGISTER_COMPONENTS (1)

#define D3D10_1_PS_OUTPUT_MASK_REGISTER_COMPONENT_BIT_COUNT (32)

#define D3D10_1_PS_OUTPUT_MASK_REGISTER_COUNT (1)

#define D3D10_1_SHADER_MAJOR_VERSION (4)

#define D3D10_1_SHADER_MINOR_VERSION (1)

#define D3D10_1_SO_BUFFER_MAX_STRIDE_IN_BYTES (2048)

#define D3D10_1_SO_BUFFER_MAX_WRITE_WINDOW_IN_BYTES (256)

#define D3D10_1_SO_BUFFER_SLOT_COUNT (4)

#define D3D10_1_SO_MULTIPLE_BUFFER_ELEMENTS_PER_BUFFER (1)

#define D3D10_1_SO_SINGLE_BUFFER_COMPONENT_LIMIT (64)

#define D3D10_1_STANDARD_VERTEX_ELEMENT_COUNT (32)

#define D3D10_1_SUBPIXEL_FRACTIONAL_BIT_COUNT (8)

#define D3D10_1_VS_INPUT_REGISTER_COUNT (32)

#define D3D10_1_VS_OUTPUT_REGISTER_COUNT (32)

#endif
#define D3D10_1_FLOAT16_FUSED_TOLERANCE_IN_ULP      (0.6)
#define D3D10_1_FLOAT32_TO_INTEGER_TOLERANCE_IN_ULP (0.6f)
#include <d3d10_1shader.h>
typedef enum D3D10_FEATURE_LEVEL1 {
    D3D10_FEATURE_LEVEL_10_0 = 0xa000,
    D3D10_FEATURE_LEVEL_10_1 = 0xa100,
    D3D10_FEATURE_LEVEL_9_1 = 0x9100,
    D3D10_FEATURE_LEVEL_9_2 = 0x9200,
    D3D10_FEATURE_LEVEL_9_3 = 0x9300
} D3D10_FEATURE_LEVEL1;
typedef struct D3D10_RENDER_TARGET_BLEND_DESC1 {
    WINBOOL BlendEnable;
    D3D10_BLEND SrcBlend;
    D3D10_BLEND DestBlend;
    D3D10_BLEND_OP BlendOp;
    D3D10_BLEND SrcBlendAlpha;
    D3D10_BLEND DestBlendAlpha;
    D3D10_BLEND_OP BlendOpAlpha;
    UINT8 RenderTargetWriteMask;
} D3D10_RENDER_TARGET_BLEND_DESC1;
typedef struct D3D10_BLEND_DESC1 {
    WINBOOL AlphaToCoverageEnable;
    WINBOOL IndependentBlendEnable;
    D3D10_RENDER_TARGET_BLEND_DESC1 RenderTarget[8];
} D3D10_BLEND_DESC1;
/*****************************************************************************
 * ID3D10BlendState1 interface
 */
#ifndef __ID3D10BlendState1_INTERFACE_DEFINED__
#define __ID3D10BlendState1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10BlendState1, 0xedad8d99, 0x8a35, 0x4d6d, 0x85,0x66, 0x2e,0xa2,0x76,0xcd,0xe1,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("edad8d99-8a35-4d6d-8566-2ea276cde161")
ID3D10BlendState1 : public ID3D10BlendState
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D10_BLEND_DESC1 *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10BlendState1, 0xedad8d99, 0x8a35, 0x4d6d, 0x85,0x66, 0x2e,0xa2,0x76,0xcd,0xe1,0x61)
#endif
#else
typedef struct ID3D10BlendState1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D10BlendState1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D10BlendState1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D10BlendState1 *This);

    /*** ID3D10DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D10BlendState1 *This,
        ID3D10Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D10BlendState1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D10BlendState1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D10BlendState1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D10BlendState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D10BlendState1 *This,
        D3D10_BLEND_DESC *pDesc);

    /*** ID3D10BlendState1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D10BlendState1 *This,
        D3D10_BLEND_DESC1 *pDesc);

    END_INTERFACE
} ID3D10BlendState1Vtbl;

interface ID3D10BlendState1 {
    CONST_VTBL ID3D10BlendState1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D10BlendState1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D10BlendState1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D10BlendState1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D10DeviceChild methods ***/
#define ID3D10BlendState1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D10BlendState1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D10BlendState1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D10BlendState1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D10BlendState methods ***/
#define ID3D10BlendState1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D10BlendState1 methods ***/
#define ID3D10BlendState1_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D10BlendState1_QueryInterface(ID3D10BlendState1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D10BlendState1_AddRef(ID3D10BlendState1* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D10BlendState1_Release(ID3D10BlendState1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D10DeviceChild methods ***/
static __WIDL_INLINE void ID3D10BlendState1_GetDevice(ID3D10BlendState1* This,ID3D10Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D10BlendState1_GetPrivateData(ID3D10BlendState1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D10BlendState1_SetPrivateData(ID3D10BlendState1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D10BlendState1_SetPrivateDataInterface(ID3D10BlendState1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D10BlendState methods ***/
static __WIDL_INLINE void ID3D10BlendState1_GetDesc(ID3D10BlendState1* This,D3D10_BLEND_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D10BlendState1 methods ***/
static __WIDL_INLINE void ID3D10BlendState1_GetDesc1(ID3D10BlendState1* This,D3D10_BLEND_DESC1 *pDesc) {
    This->lpVtbl->GetDesc1(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D10BlendState1_INTERFACE_DEFINED__ */

typedef struct D3D10_TEXCUBE_ARRAY_SRV1 {
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;
    UINT NumCubes;
} D3D10_TEXCUBE_ARRAY_SRV1;
typedef D3D_SRV_DIMENSION D3D10_SRV_DIMENSION1;
typedef struct D3D10_SHADER_RESOURCE_VIEW_DESC1 {
    DXGI_FORMAT Format;
    D3D10_SRV_DIMENSION1 ViewDimension;
    __C89_NAMELESS union {
        D3D10_BUFFER_SRV Buffer;
        D3D10_TEX1D_SRV Texture1D;
        D3D10_TEX1D_ARRAY_SRV Texture1DArray;
        D3D10_TEX2D_SRV Texture2D;
        D3D10_TEX2D_ARRAY_SRV Texture2DArray;
        D3D10_TEX2DMS_SRV Texture2DMS;
        D3D10_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D10_TEX3D_SRV Texture3D;
        D3D10_TEXCUBE_SRV TextureCube;
        D3D10_TEXCUBE_ARRAY_SRV1 TextureCubeArray;
    } __C89_NAMELESSUNIONNAME;
} D3D10_SHADER_RESOURCE_VIEW_DESC1;
/*****************************************************************************
 * ID3D10ShaderResourceView1 interface
 */
#ifndef __ID3D10ShaderResourceView1_INTERFACE_DEFINED__
#define __ID3D10ShaderResourceView1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10ShaderResourceView1, 0x9b7e4c87, 0x342c, 0x4106, 0xa1,0x9f, 0x4f,0x27,0x04,0xf6,0x89,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b7e4c87-342c-4106-a19f-4f2704f689f0")
ID3D10ShaderResourceView1 : public ID3D10ShaderResourceView
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D10_SHADER_RESOURCE_VIEW_DESC1 *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10ShaderResourceView1, 0x9b7e4c87, 0x342c, 0x4106, 0xa1,0x9f, 0x4f,0x27,0x04,0xf6,0x89,0xf0)
#endif
#else
typedef struct ID3D10ShaderResourceView1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D10ShaderResourceView1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D10ShaderResourceView1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D10ShaderResourceView1 *This);

    /*** ID3D10DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D10ShaderResourceView1 *This,
        ID3D10Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D10ShaderResourceView1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D10ShaderResourceView1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D10ShaderResourceView1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D10View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D10ShaderResourceView1 *This,
        ID3D10Resource **ppResource);

    /*** ID3D10ShaderResourceView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D10ShaderResourceView1 *This,
        D3D10_SHADER_RESOURCE_VIEW_DESC *pDesc);

    /*** ID3D10ShaderResourceView1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D10ShaderResourceView1 *This,
        D3D10_SHADER_RESOURCE_VIEW_DESC1 *pDesc);

    END_INTERFACE
} ID3D10ShaderResourceView1Vtbl;

interface ID3D10ShaderResourceView1 {
    CONST_VTBL ID3D10ShaderResourceView1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D10ShaderResourceView1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D10ShaderResourceView1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D10ShaderResourceView1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D10DeviceChild methods ***/
#define ID3D10ShaderResourceView1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D10ShaderResourceView1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D10ShaderResourceView1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D10ShaderResourceView1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D10View methods ***/
#define ID3D10ShaderResourceView1_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D10ShaderResourceView methods ***/
#define ID3D10ShaderResourceView1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D10ShaderResourceView1 methods ***/
#define ID3D10ShaderResourceView1_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D10ShaderResourceView1_QueryInterface(ID3D10ShaderResourceView1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D10ShaderResourceView1_AddRef(ID3D10ShaderResourceView1* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D10ShaderResourceView1_Release(ID3D10ShaderResourceView1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D10DeviceChild methods ***/
static __WIDL_INLINE void ID3D10ShaderResourceView1_GetDevice(ID3D10ShaderResourceView1* This,ID3D10Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D10ShaderResourceView1_GetPrivateData(ID3D10ShaderResourceView1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D10ShaderResourceView1_SetPrivateData(ID3D10ShaderResourceView1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D10ShaderResourceView1_SetPrivateDataInterface(ID3D10ShaderResourceView1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D10View methods ***/
static __WIDL_INLINE void ID3D10ShaderResourceView1_GetResource(ID3D10ShaderResourceView1* This,ID3D10Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D10ShaderResourceView methods ***/
static __WIDL_INLINE void ID3D10ShaderResourceView1_GetDesc(ID3D10ShaderResourceView1* This,D3D10_SHADER_RESOURCE_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D10ShaderResourceView1 methods ***/
static __WIDL_INLINE void ID3D10ShaderResourceView1_GetDesc1(ID3D10ShaderResourceView1* This,D3D10_SHADER_RESOURCE_VIEW_DESC1 *pDesc) {
    This->lpVtbl->GetDesc1(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D10ShaderResourceView1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10Device1 interface
 */
#ifndef __ID3D10Device1_INTERFACE_DEFINED__
#define __ID3D10Device1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10Device1, 0x9b7e4c8f, 0x342c, 0x4106, 0xa1,0x9f, 0x4f,0x27,0x04,0xf6,0x89,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b7e4c8f-342c-4106-a19f-4f2704f689f0")
ID3D10Device1 : public ID3D10Device
{
    virtual HRESULT STDMETHODCALLTYPE CreateShaderResourceView1(
        ID3D10Resource *pResource,
        const D3D10_SHADER_RESOURCE_VIEW_DESC1 *pDesc,
        ID3D10ShaderResourceView1 **ppSRView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBlendState1(
        const D3D10_BLEND_DESC1 *pBlendStateDesc,
        ID3D10BlendState1 **ppBlendState) = 0;

    virtual D3D10_FEATURE_LEVEL1 STDMETHODCALLTYPE GetFeatureLevel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10Device1, 0x9b7e4c8f, 0x342c, 0x4106, 0xa1,0x9f, 0x4f,0x27,0x04,0xf6,0x89,0xf0)
#endif
#else
typedef struct ID3D10Device1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D10Device1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D10Device1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D10Device1 *This);

    /*** ID3D10Device methods ***/
    void (STDMETHODCALLTYPE *VSSetConstantBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSSetShaderResources)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D10ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSSetShader)(
        ID3D10Device1 *This,
        ID3D10PixelShader *pPixelShader);

    void (STDMETHODCALLTYPE *PSSetSamplers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D10SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *VSSetShader)(
        ID3D10Device1 *This,
        ID3D10VertexShader *pVertexShader);

    void (STDMETHODCALLTYPE *DrawIndexed)(
        ID3D10Device1 *This,
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation);

    void (STDMETHODCALLTYPE *Draw)(
        ID3D10Device1 *This,
        UINT VertexCount,
        UINT StartVertexLocation);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *IASetInputLayout)(
        ID3D10Device1 *This,
        ID3D10InputLayout *pInputLayout);

    void (STDMETHODCALLTYPE *IASetVertexBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *IASetIndexBuffer)(
        ID3D10Device1 *This,
        ID3D10Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset);

    void (STDMETHODCALLTYPE *DrawIndexedInstanced)(
        ID3D10Device1 *This,
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *DrawInstanced)(
        ID3D10Device1 *This,
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSSetShader)(
        ID3D10Device1 *This,
        ID3D10GeometryShader *pShader);

    void (STDMETHODCALLTYPE *IASetPrimitiveTopology)(
        ID3D10Device1 *This,
        D3D10_PRIMITIVE_TOPOLOGY Topology);

    void (STDMETHODCALLTYPE *VSSetShaderResources)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D10ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSSetSamplers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D10SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D10Device1 *This,
        ID3D10Predicate *pPredicate,
        WINBOOL PredicateValue);

    void (STDMETHODCALLTYPE *GSSetShaderResources)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D10ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSSetSamplers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D10SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *OMSetRenderTargets)(
        ID3D10Device1 *This,
        UINT NumViews,
        ID3D10RenderTargetView *const *ppRenderTargetViews,
        ID3D10DepthStencilView *pDepthStencilView);

    void (STDMETHODCALLTYPE *OMSetBlendState)(
        ID3D10Device1 *This,
        ID3D10BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask);

    void (STDMETHODCALLTYPE *OMSetDepthStencilState)(
        ID3D10Device1 *This,
        ID3D10DepthStencilState *pDepthStencilState,
        UINT StencilRef);

    void (STDMETHODCALLTYPE *SOSetTargets)(
        ID3D10Device1 *This,
        UINT NumBuffers,
        ID3D10Buffer *const *ppSOTargets,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *DrawAuto)(
        ID3D10Device1 *This);

    void (STDMETHODCALLTYPE *RSSetState)(
        ID3D10Device1 *This,
        ID3D10RasterizerState *pRasterizerState);

    void (STDMETHODCALLTYPE *RSSetViewports)(
        ID3D10Device1 *This,
        UINT NumViewports,
        const D3D10_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSSetScissorRects)(
        ID3D10Device1 *This,
        UINT NumRects,
        const D3D10_RECT *pRects);

    void (STDMETHODCALLTYPE *CopySubresourceRegion)(
        ID3D10Device1 *This,
        ID3D10Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D10Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D10_BOX *pSrcBox);

    void (STDMETHODCALLTYPE *CopyResource)(
        ID3D10Device1 *This,
        ID3D10Resource *pDstResource,
        ID3D10Resource *pSrcResource);

    void (STDMETHODCALLTYPE *UpdateSubresource)(
        ID3D10Device1 *This,
        ID3D10Resource *pDstResource,
        UINT DstSubresource,
        const D3D10_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch);

    void (STDMETHODCALLTYPE *ClearRenderTargetView)(
        ID3D10Device1 *This,
        ID3D10RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]);

    void (STDMETHODCALLTYPE *ClearDepthStencilView)(
        ID3D10Device1 *This,
        ID3D10DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil);

    void (STDMETHODCALLTYPE *GenerateMips)(
        ID3D10Device1 *This,
        ID3D10ShaderResourceView *pShaderResourceView);

    void (STDMETHODCALLTYPE *ResolveSubresource)(
        ID3D10Device1 *This,
        ID3D10Resource *pDstResource,
        UINT DstSubresource,
        ID3D10Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSGetShaderResources)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D10ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSGetShader)(
        ID3D10Device1 *This,
        ID3D10PixelShader **ppPixelShader);

    void (STDMETHODCALLTYPE *PSGetSamplers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D10SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *VSGetShader)(
        ID3D10Device1 *This,
        ID3D10VertexShader **ppVertexShader);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *IAGetInputLayout)(
        ID3D10Device1 *This,
        ID3D10InputLayout **ppInputLayout);

    void (STDMETHODCALLTYPE *IAGetVertexBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *IAGetIndexBuffer)(
        ID3D10Device1 *This,
        ID3D10Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D10Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSGetShader)(
        ID3D10Device1 *This,
        ID3D10GeometryShader **ppGeometryShader);

    void (STDMETHODCALLTYPE *IAGetPrimitiveTopology)(
        ID3D10Device1 *This,
        D3D10_PRIMITIVE_TOPOLOGY *pTopology);

    void (STDMETHODCALLTYPE *VSGetShaderResources)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D10ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSGetSamplers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D10SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *GetPredication)(
        ID3D10Device1 *This,
        ID3D10Predicate **ppPredicate,
        WINBOOL *pPredicateValue);

    void (STDMETHODCALLTYPE *GSGetShaderResources)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D10ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSGetSamplers)(
        ID3D10Device1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D10SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *OMGetRenderTargets)(
        ID3D10Device1 *This,
        UINT NumViews,
        ID3D10RenderTargetView **ppRenderTargetViews,
        ID3D10DepthStencilView **ppDepthStencilView);

    void (STDMETHODCALLTYPE *OMGetBlendState)(
        ID3D10Device1 *This,
        ID3D10BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask);

    void (STDMETHODCALLTYPE *OMGetDepthStencilState)(
        ID3D10Device1 *This,
        ID3D10DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef);

    void (STDMETHODCALLTYPE *SOGetTargets)(
        ID3D10Device1 *This,
        UINT NumBuffers,
        ID3D10Buffer **ppSOTargets,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *RSGetState)(
        ID3D10Device1 *This,
        ID3D10RasterizerState **ppRasterizerState);

    void (STDMETHODCALLTYPE *RSGetViewports)(
        ID3D10Device1 *This,
        UINT *NumViewports,
        D3D10_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSGetScissorRects)(
        ID3D10Device1 *This,
        UINT *NumRects,
        D3D10_RECT *pRects);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D10Device1 *This);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D10Device1 *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D10Device1 *This);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D10Device1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D10Device1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D10Device1 *This,
        REFGUID guid,
        const IUnknown *pData);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D10Device1 *This);

    void (STDMETHODCALLTYPE *Flush)(
        ID3D10Device1 *This);

    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D10Device1 *This,
        const D3D10_BUFFER_DESC *pDesc,
        const D3D10_SUBRESOURCE_DATA *pInitialData,
        ID3D10Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D10Device1 *This,
        const D3D10_TEXTURE1D_DESC *pDesc,
        const D3D10_SUBRESOURCE_DATA *pInitialData,
        ID3D10Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D10Device1 *This,
        const D3D10_TEXTURE2D_DESC *pDesc,
        const D3D10_SUBRESOURCE_DATA *pInitialData,
        ID3D10Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D10Device1 *This,
        const D3D10_TEXTURE3D_DESC *pDesc,
        const D3D10_SUBRESOURCE_DATA *pInitialData,
        ID3D10Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D10Device1 *This,
        ID3D10Resource *pResource,
        const D3D10_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D10ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D10Device1 *This,
        ID3D10Resource *pResource,
        const D3D10_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D10RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D10Device1 *This,
        ID3D10Resource *pResource,
        const D3D10_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D10DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D10Device1 *This,
        const D3D10_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D10InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D10Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D10VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D10Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D10GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D10Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D10_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        UINT OutputStreamStride,
        ID3D10GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D10Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D10PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D10Device1 *This,
        const D3D10_BLEND_DESC *pBlendStateDesc,
        ID3D10BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D10Device1 *This,
        const D3D10_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D10DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D10Device1 *This,
        const D3D10_RASTERIZER_DESC *pRasterizerDesc,
        ID3D10RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D10Device1 *This,
        const D3D10_SAMPLER_DESC *pSamplerDesc,
        ID3D10SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D10Device1 *This,
        const D3D10_QUERY_DESC *pQueryDesc,
        ID3D10Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D10Device1 *This,
        const D3D10_QUERY_DESC *pPredicateDesc,
        ID3D10Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D10Device1 *This,
        const D3D10_COUNTER_DESC *pCounterDesc,
        ID3D10Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D10Device1 *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D10Device1 *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D10Device1 *This,
        D3D10_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D10Device1 *This,
        const D3D10_COUNTER_DESC *pDesc,
        D3D10_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        char *name,
        UINT *pNameLength,
        char *units,
        UINT *pUnitsLength,
        char *description,
        UINT *pDescriptionLength);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D10Device1 *This);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D10Device1 *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    void (STDMETHODCALLTYPE *SetTextFilterSize)(
        ID3D10Device1 *This,
        UINT Width,
        UINT Height);

    void (STDMETHODCALLTYPE *GetTextFilterSize)(
        ID3D10Device1 *This,
        UINT *pWidth,
        UINT *pHeight);

    /*** ID3D10Device1 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView1)(
        ID3D10Device1 *This,
        ID3D10Resource *pResource,
        const D3D10_SHADER_RESOURCE_VIEW_DESC1 *pDesc,
        ID3D10ShaderResourceView1 **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState1)(
        ID3D10Device1 *This,
        const D3D10_BLEND_DESC1 *pBlendStateDesc,
        ID3D10BlendState1 **ppBlendState);

    D3D10_FEATURE_LEVEL1 (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D10Device1 *This);

    END_INTERFACE
} ID3D10Device1Vtbl;

interface ID3D10Device1 {
    CONST_VTBL ID3D10Device1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D10Device1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D10Device1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D10Device1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D10Device methods ***/
#define ID3D10Device1_VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D10Device1_PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D10Device1_PSSetShader(This,pPixelShader) (This)->lpVtbl->PSSetShader(This,pPixelShader)
#define ID3D10Device1_PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D10Device1_VSSetShader(This,pVertexShader) (This)->lpVtbl->VSSetShader(This,pVertexShader)
#define ID3D10Device1_DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation) (This)->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation)
#define ID3D10Device1_Draw(This,VertexCount,StartVertexLocation) (This)->lpVtbl->Draw(This,VertexCount,StartVertexLocation)
#define ID3D10Device1_PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D10Device1_IASetInputLayout(This,pInputLayout) (This)->lpVtbl->IASetInputLayout(This,pInputLayout)
#define ID3D10Device1_IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D10Device1_IASetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D10Device1_DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation)
#define ID3D10Device1_DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation)
#define ID3D10Device1_GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D10Device1_GSSetShader(This,pShader) (This)->lpVtbl->GSSetShader(This,pShader)
#define ID3D10Device1_IASetPrimitiveTopology(This,Topology) (This)->lpVtbl->IASetPrimitiveTopology(This,Topology)
#define ID3D10Device1_VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D10Device1_VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D10Device1_SetPredication(This,pPredicate,PredicateValue) (This)->lpVtbl->SetPredication(This,pPredicate,PredicateValue)
#define ID3D10Device1_GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D10Device1_GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D10Device1_OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView) (This)->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView)
#define ID3D10Device1_OMSetBlendState(This,pBlendState,BlendFactor,SampleMask) (This)->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask)
#define ID3D10Device1_OMSetDepthStencilState(This,pDepthStencilState,StencilRef) (This)->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef)
#define ID3D10Device1_SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D10Device1_DrawAuto(This) (This)->lpVtbl->DrawAuto(This)
#define ID3D10Device1_RSSetState(This,pRasterizerState) (This)->lpVtbl->RSSetState(This,pRasterizerState)
#define ID3D10Device1_RSSetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSSetViewports(This,NumViewports,pViewports)
#define ID3D10Device1_RSSetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSSetScissorRects(This,NumRects,pRects)
#define ID3D10Device1_CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox) (This)->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox)
#define ID3D10Device1_CopyResource(This,pDstResource,pSrcResource) (This)->lpVtbl->CopyResource(This,pDstResource,pSrcResource)
#define ID3D10Device1_UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch) (This)->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch)
#define ID3D10Device1_ClearRenderTargetView(This,pRenderTargetView,ColorRGBA) (This)->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA)
#define ID3D10Device1_ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil) (This)->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil)
#define ID3D10Device1_GenerateMips(This,pShaderResourceView) (This)->lpVtbl->GenerateMips(This,pShaderResourceView)
#define ID3D10Device1_ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format) (This)->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format)
#define ID3D10Device1_VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D10Device1_PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D10Device1_PSGetShader(This,ppPixelShader) (This)->lpVtbl->PSGetShader(This,ppPixelShader)
#define ID3D10Device1_PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D10Device1_VSGetShader(This,ppVertexShader) (This)->lpVtbl->VSGetShader(This,ppVertexShader)
#define ID3D10Device1_PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D10Device1_IAGetInputLayout(This,ppInputLayout) (This)->lpVtbl->IAGetInputLayout(This,ppInputLayout)
#define ID3D10Device1_IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D10Device1_IAGetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D10Device1_GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D10Device1_GSGetShader(This,ppGeometryShader) (This)->lpVtbl->GSGetShader(This,ppGeometryShader)
#define ID3D10Device1_IAGetPrimitiveTopology(This,pTopology) (This)->lpVtbl->IAGetPrimitiveTopology(This,pTopology)
#define ID3D10Device1_VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D10Device1_VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D10Device1_GetPredication(This,ppPredicate,pPredicateValue) (This)->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue)
#define ID3D10Device1_GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D10Device1_GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D10Device1_OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView) (This)->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView)
#define ID3D10Device1_OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask) (This)->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask)
#define ID3D10Device1_OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef) (This)->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef)
#define ID3D10Device1_SOGetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D10Device1_RSGetState(This,ppRasterizerState) (This)->lpVtbl->RSGetState(This,ppRasterizerState)
#define ID3D10Device1_RSGetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSGetViewports(This,NumViewports,pViewports)
#define ID3D10Device1_RSGetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSGetScissorRects(This,NumRects,pRects)
#define ID3D10Device1_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D10Device1_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D10Device1_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
#define ID3D10Device1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D10Device1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D10Device1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D10Device1_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D10Device1_Flush(This) (This)->lpVtbl->Flush(This)
#define ID3D10Device1_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D10Device1_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D10Device1_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D10Device1_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D10Device1_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D10Device1_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D10Device1_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D10Device1_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D10Device1_CreateVertexShader(This,pShaderBytecode,BytecodeLength,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,ppVertexShader)
#define ID3D10Device1_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,ppGeometryShader)
#define ID3D10Device1_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,OutputStreamStride,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,OutputStreamStride,ppGeometryShader)
#define ID3D10Device1_CreatePixelShader(This,pShaderBytecode,BytecodeLength,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,ppPixelShader)
#define ID3D10Device1_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D10Device1_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D10Device1_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D10Device1_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D10Device1_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D10Device1_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D10Device1_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D10Device1_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D10Device1_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D10Device1_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D10Device1_CheckCounter(This,pDesc,pType,pActiveCounters,name,pNameLength,units,pUnitsLength,description,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,name,pNameLength,units,pUnitsLength,description,pDescriptionLength)
#define ID3D10Device1_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D10Device1_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D10Device1_SetTextFilterSize(This,Width,Height) (This)->lpVtbl->SetTextFilterSize(This,Width,Height)
#define ID3D10Device1_GetTextFilterSize(This,pWidth,pHeight) (This)->lpVtbl->GetTextFilterSize(This,pWidth,pHeight)
/*** ID3D10Device1 methods ***/
#define ID3D10Device1_CreateShaderResourceView1(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView1(This,pResource,pDesc,ppSRView)
#define ID3D10Device1_CreateBlendState1(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState)
#define ID3D10Device1_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D10Device1_QueryInterface(ID3D10Device1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D10Device1_AddRef(ID3D10Device1* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D10Device1_Release(ID3D10Device1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D10Device methods ***/
static __WIDL_INLINE void ID3D10Device1_VSSetConstantBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer *const *ppConstantBuffers) {
    This->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D10Device1_PSSetShaderResources(ID3D10Device1* This,UINT StartSlot,UINT NumViews,ID3D10ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D10Device1_PSSetShader(ID3D10Device1* This,ID3D10PixelShader *pPixelShader) {
    This->lpVtbl->PSSetShader(This,pPixelShader);
}
static __WIDL_INLINE void ID3D10Device1_PSSetSamplers(ID3D10Device1* This,UINT StartSlot,UINT NumSamplers,ID3D10SamplerState *const *ppSamplers) {
    This->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D10Device1_VSSetShader(ID3D10Device1* This,ID3D10VertexShader *pVertexShader) {
    This->lpVtbl->VSSetShader(This,pVertexShader);
}
static __WIDL_INLINE void ID3D10Device1_DrawIndexed(ID3D10Device1* This,UINT IndexCount,UINT StartIndexLocation,INT BaseVertexLocation) {
    This->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation);
}
static __WIDL_INLINE void ID3D10Device1_Draw(ID3D10Device1* This,UINT VertexCount,UINT StartVertexLocation) {
    This->lpVtbl->Draw(This,VertexCount,StartVertexLocation);
}
static __WIDL_INLINE void ID3D10Device1_PSSetConstantBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer *const *ppConstantBuffers) {
    This->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D10Device1_IASetInputLayout(ID3D10Device1* This,ID3D10InputLayout *pInputLayout) {
    This->lpVtbl->IASetInputLayout(This,pInputLayout);
}
static __WIDL_INLINE void ID3D10Device1_IASetVertexBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer *const *ppVertexBuffers,const UINT *pStrides,const UINT *pOffsets) {
    This->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static __WIDL_INLINE void ID3D10Device1_IASetIndexBuffer(ID3D10Device1* This,ID3D10Buffer *pIndexBuffer,DXGI_FORMAT Format,UINT Offset) {
    This->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static __WIDL_INLINE void ID3D10Device1_DrawIndexedInstanced(ID3D10Device1* This,UINT IndexCountPerInstance,UINT InstanceCount,UINT StartIndexLocation,INT BaseVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation);
}
static __WIDL_INLINE void ID3D10Device1_DrawInstanced(ID3D10Device1* This,UINT VertexCountPerInstance,UINT InstanceCount,UINT StartVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation);
}
static __WIDL_INLINE void ID3D10Device1_GSSetConstantBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer *const *ppConstantBuffers) {
    This->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D10Device1_GSSetShader(ID3D10Device1* This,ID3D10GeometryShader *pShader) {
    This->lpVtbl->GSSetShader(This,pShader);
}
static __WIDL_INLINE void ID3D10Device1_IASetPrimitiveTopology(ID3D10Device1* This,D3D10_PRIMITIVE_TOPOLOGY Topology) {
    This->lpVtbl->IASetPrimitiveTopology(This,Topology);
}
static __WIDL_INLINE void ID3D10Device1_VSSetShaderResources(ID3D10Device1* This,UINT StartSlot,UINT NumViews,ID3D10ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D10Device1_VSSetSamplers(ID3D10Device1* This,UINT StartSlot,UINT NumSamplers,ID3D10SamplerState *const *ppSamplers) {
    This->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D10Device1_SetPredication(ID3D10Device1* This,ID3D10Predicate *pPredicate,WINBOOL PredicateValue) {
    This->lpVtbl->SetPredication(This,pPredicate,PredicateValue);
}
static __WIDL_INLINE void ID3D10Device1_GSSetShaderResources(ID3D10Device1* This,UINT StartSlot,UINT NumViews,ID3D10ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D10Device1_GSSetSamplers(ID3D10Device1* This,UINT StartSlot,UINT NumSamplers,ID3D10SamplerState *const *ppSamplers) {
    This->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D10Device1_OMSetRenderTargets(ID3D10Device1* This,UINT NumViews,ID3D10RenderTargetView *const *ppRenderTargetViews,ID3D10DepthStencilView *pDepthStencilView) {
    This->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView);
}
static __WIDL_INLINE void ID3D10Device1_OMSetBlendState(ID3D10Device1* This,ID3D10BlendState *pBlendState,const FLOAT BlendFactor[4],UINT SampleMask) {
    This->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask);
}
static __WIDL_INLINE void ID3D10Device1_OMSetDepthStencilState(ID3D10Device1* This,ID3D10DepthStencilState *pDepthStencilState,UINT StencilRef) {
    This->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef);
}
static __WIDL_INLINE void ID3D10Device1_SOSetTargets(ID3D10Device1* This,UINT NumBuffers,ID3D10Buffer *const *ppSOTargets,const UINT *pOffsets) {
    This->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static __WIDL_INLINE void ID3D10Device1_DrawAuto(ID3D10Device1* This) {
    This->lpVtbl->DrawAuto(This);
}
static __WIDL_INLINE void ID3D10Device1_RSSetState(ID3D10Device1* This,ID3D10RasterizerState *pRasterizerState) {
    This->lpVtbl->RSSetState(This,pRasterizerState);
}
static __WIDL_INLINE void ID3D10Device1_RSSetViewports(ID3D10Device1* This,UINT NumViewports,const D3D10_VIEWPORT *pViewports) {
    This->lpVtbl->RSSetViewports(This,NumViewports,pViewports);
}
static __WIDL_INLINE void ID3D10Device1_RSSetScissorRects(ID3D10Device1* This,UINT NumRects,const D3D10_RECT *pRects) {
    This->lpVtbl->RSSetScissorRects(This,NumRects,pRects);
}
static __WIDL_INLINE void ID3D10Device1_CopySubresourceRegion(ID3D10Device1* This,ID3D10Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D10Resource *pSrcResource,UINT SrcSubresource,const D3D10_BOX *pSrcBox) {
    This->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox);
}
static __WIDL_INLINE void ID3D10Device1_CopyResource(ID3D10Device1* This,ID3D10Resource *pDstResource,ID3D10Resource *pSrcResource) {
    This->lpVtbl->CopyResource(This,pDstResource,pSrcResource);
}
static __WIDL_INLINE void ID3D10Device1_UpdateSubresource(ID3D10Device1* This,ID3D10Resource *pDstResource,UINT DstSubresource,const D3D10_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch) {
    This->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch);
}
static __WIDL_INLINE void ID3D10Device1_ClearRenderTargetView(ID3D10Device1* This,ID3D10RenderTargetView *pRenderTargetView,const FLOAT ColorRGBA[4]) {
    This->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA);
}
static __WIDL_INLINE void ID3D10Device1_ClearDepthStencilView(ID3D10Device1* This,ID3D10DepthStencilView *pDepthStencilView,UINT ClearFlags,FLOAT Depth,UINT8 Stencil) {
    This->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil);
}
static __WIDL_INLINE void ID3D10Device1_GenerateMips(ID3D10Device1* This,ID3D10ShaderResourceView *pShaderResourceView) {
    This->lpVtbl->GenerateMips(This,pShaderResourceView);
}
static __WIDL_INLINE void ID3D10Device1_ResolveSubresource(ID3D10Device1* This,ID3D10Resource *pDstResource,UINT DstSubresource,ID3D10Resource *pSrcResource,UINT SrcSubresource,DXGI_FORMAT Format) {
    This->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format);
}
static __WIDL_INLINE void ID3D10Device1_VSGetConstantBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer **ppConstantBuffers) {
    This->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D10Device1_PSGetShaderResources(ID3D10Device1* This,UINT StartSlot,UINT NumViews,ID3D10ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D10Device1_PSGetShader(ID3D10Device1* This,ID3D10PixelShader **ppPixelShader) {
    This->lpVtbl->PSGetShader(This,ppPixelShader);
}
static __WIDL_INLINE void ID3D10Device1_PSGetSamplers(ID3D10Device1* This,UINT StartSlot,UINT NumSamplers,ID3D10SamplerState **ppSamplers) {
    This->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D10Device1_VSGetShader(ID3D10Device1* This,ID3D10VertexShader **ppVertexShader) {
    This->lpVtbl->VSGetShader(This,ppVertexShader);
}
static __WIDL_INLINE void ID3D10Device1_PSGetConstantBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer **ppConstantBuffers) {
    This->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D10Device1_IAGetInputLayout(ID3D10Device1* This,ID3D10InputLayout **ppInputLayout) {
    This->lpVtbl->IAGetInputLayout(This,ppInputLayout);
}
static __WIDL_INLINE void ID3D10Device1_IAGetVertexBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer **ppVertexBuffers,UINT *pStrides,UINT *pOffsets) {
    This->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static __WIDL_INLINE void ID3D10Device1_IAGetIndexBuffer(ID3D10Device1* This,ID3D10Buffer **pIndexBuffer,DXGI_FORMAT *Format,UINT *Offset) {
    This->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static __WIDL_INLINE void ID3D10Device1_GSGetConstantBuffers(ID3D10Device1* This,UINT StartSlot,UINT NumBuffers,ID3D10Buffer **ppConstantBuffers) {
    This->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D10Device1_GSGetShader(ID3D10Device1* This,ID3D10GeometryShader **ppGeometryShader) {
    This->lpVtbl->GSGetShader(This,ppGeometryShader);
}
static __WIDL_INLINE void ID3D10Device1_IAGetPrimitiveTopology(ID3D10Device1* This,D3D10_PRIMITIVE_TOPOLOGY *pTopology) {
    This->lpVtbl->IAGetPrimitiveTopology(This,pTopology);
}
static __WIDL_INLINE void ID3D10Device1_VSGetShaderResources(ID3D10Device1* This,UINT StartSlot,UINT NumViews,ID3D10ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D10Device1_VSGetSamplers(ID3D10Device1* This,UINT StartSlot,UINT NumSamplers,ID3D10SamplerState **ppSamplers) {
    This->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D10Device1_GetPredication(ID3D10Device1* This,ID3D10Predicate **ppPredicate,WINBOOL *pPredicateValue) {
    This->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue);
}
static __WIDL_INLINE void ID3D10Device1_GSGetShaderResources(ID3D10Device1* This,UINT StartSlot,UINT NumViews,ID3D10ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D10Device1_GSGetSamplers(ID3D10Device1* This,UINT StartSlot,UINT NumSamplers,ID3D10SamplerState **ppSamplers) {
    This->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D10Device1_OMGetRenderTargets(ID3D10Device1* This,UINT NumViews,ID3D10RenderTargetView **ppRenderTargetViews,ID3D10DepthStencilView **ppDepthStencilView) {
    This->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView);
}
static __WIDL_INLINE void ID3D10Device1_OMGetBlendState(ID3D10Device1* This,ID3D10BlendState **ppBlendState,FLOAT BlendFactor[4],UINT *pSampleMask) {
    This->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask);
}
static __WIDL_INLINE void ID3D10Device1_OMGetDepthStencilState(ID3D10Device1* This,ID3D10DepthStencilState **ppDepthStencilState,UINT *pStencilRef) {
    This->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef);
}
static __WIDL_INLINE void ID3D10Device1_SOGetTargets(ID3D10Device1* This,UINT NumBuffers,ID3D10Buffer **ppSOTargets,UINT *pOffsets) {
    This->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static __WIDL_INLINE void ID3D10Device1_RSGetState(ID3D10Device1* This,ID3D10RasterizerState **ppRasterizerState) {
    This->lpVtbl->RSGetState(This,ppRasterizerState);
}
static __WIDL_INLINE void ID3D10Device1_RSGetViewports(ID3D10Device1* This,UINT *NumViewports,D3D10_VIEWPORT *pViewports) {
    This->lpVtbl->RSGetViewports(This,NumViewports,pViewports);
}
static __WIDL_INLINE void ID3D10Device1_RSGetScissorRects(ID3D10Device1* This,UINT *NumRects,D3D10_RECT *pRects) {
    This->lpVtbl->RSGetScissorRects(This,NumRects,pRects);
}
static __WIDL_INLINE HRESULT ID3D10Device1_GetDeviceRemovedReason(ID3D10Device1* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static __WIDL_INLINE HRESULT ID3D10Device1_SetExceptionMode(ID3D10Device1* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static __WIDL_INLINE UINT ID3D10Device1_GetExceptionMode(ID3D10Device1* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
static __WIDL_INLINE HRESULT ID3D10Device1_GetPrivateData(ID3D10Device1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D10Device1_SetPrivateData(ID3D10Device1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D10Device1_SetPrivateDataInterface(ID3D10Device1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static __WIDL_INLINE void ID3D10Device1_ClearState(ID3D10Device1* This) {
    This->lpVtbl->ClearState(This);
}
static __WIDL_INLINE void ID3D10Device1_Flush(ID3D10Device1* This) {
    This->lpVtbl->Flush(This);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateBuffer(ID3D10Device1* This,const D3D10_BUFFER_DESC *pDesc,const D3D10_SUBRESOURCE_DATA *pInitialData,ID3D10Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateTexture1D(ID3D10Device1* This,const D3D10_TEXTURE1D_DESC *pDesc,const D3D10_SUBRESOURCE_DATA *pInitialData,ID3D10Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateTexture2D(ID3D10Device1* This,const D3D10_TEXTURE2D_DESC *pDesc,const D3D10_SUBRESOURCE_DATA *pInitialData,ID3D10Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateTexture3D(ID3D10Device1* This,const D3D10_TEXTURE3D_DESC *pDesc,const D3D10_SUBRESOURCE_DATA *pInitialData,ID3D10Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateShaderResourceView(ID3D10Device1* This,ID3D10Resource *pResource,const D3D10_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D10ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateRenderTargetView(ID3D10Device1* This,ID3D10Resource *pResource,const D3D10_RENDER_TARGET_VIEW_DESC *pDesc,ID3D10RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateDepthStencilView(ID3D10Device1* This,ID3D10Resource *pResource,const D3D10_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D10DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateInputLayout(ID3D10Device1* This,const D3D10_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D10InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateVertexShader(ID3D10Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D10VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,ppVertexShader);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateGeometryShader(ID3D10Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D10GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,ppGeometryShader);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateGeometryShaderWithStreamOutput(ID3D10Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D10_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,UINT OutputStreamStride,ID3D10GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,OutputStreamStride,ppGeometryShader);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreatePixelShader(ID3D10Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D10PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,ppPixelShader);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateBlendState(ID3D10Device1* This,const D3D10_BLEND_DESC *pBlendStateDesc,ID3D10BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateDepthStencilState(ID3D10Device1* This,const D3D10_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D10DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateRasterizerState(ID3D10Device1* This,const D3D10_RASTERIZER_DESC *pRasterizerDesc,ID3D10RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateSamplerState(ID3D10Device1* This,const D3D10_SAMPLER_DESC *pSamplerDesc,ID3D10SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateQuery(ID3D10Device1* This,const D3D10_QUERY_DESC *pQueryDesc,ID3D10Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreatePredicate(ID3D10Device1* This,const D3D10_QUERY_DESC *pPredicateDesc,ID3D10Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateCounter(ID3D10Device1* This,const D3D10_COUNTER_DESC *pCounterDesc,ID3D10Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CheckFormatSupport(ID3D10Device1* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CheckMultisampleQualityLevels(ID3D10Device1* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static __WIDL_INLINE void ID3D10Device1_CheckCounterInfo(ID3D10Device1* This,D3D10_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CheckCounter(ID3D10Device1* This,const D3D10_COUNTER_DESC *pDesc,D3D10_COUNTER_TYPE *pType,UINT *pActiveCounters,char *name,UINT *pNameLength,char *units,UINT *pUnitsLength,char *description,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,name,pNameLength,units,pUnitsLength,description,pDescriptionLength);
}
static __WIDL_INLINE UINT ID3D10Device1_GetCreationFlags(ID3D10Device1* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static __WIDL_INLINE HRESULT ID3D10Device1_OpenSharedResource(ID3D10Device1* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static __WIDL_INLINE void ID3D10Device1_SetTextFilterSize(ID3D10Device1* This,UINT Width,UINT Height) {
    This->lpVtbl->SetTextFilterSize(This,Width,Height);
}
static __WIDL_INLINE void ID3D10Device1_GetTextFilterSize(ID3D10Device1* This,UINT *pWidth,UINT *pHeight) {
    This->lpVtbl->GetTextFilterSize(This,pWidth,pHeight);
}
/*** ID3D10Device1 methods ***/
static __WIDL_INLINE HRESULT ID3D10Device1_CreateShaderResourceView1(ID3D10Device1* This,ID3D10Resource *pResource,const D3D10_SHADER_RESOURCE_VIEW_DESC1 *pDesc,ID3D10ShaderResourceView1 **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView1(This,pResource,pDesc,ppSRView);
}
static __WIDL_INLINE HRESULT ID3D10Device1_CreateBlendState1(ID3D10Device1* This,const D3D10_BLEND_DESC1 *pBlendStateDesc,ID3D10BlendState1 **ppBlendState) {
    return This->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState);
}
static __WIDL_INLINE D3D10_FEATURE_LEVEL1 ID3D10Device1_GetFeatureLevel(ID3D10Device1* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
#endif
#endif

#endif


#endif  /* __ID3D10Device1_INTERFACE_DEFINED__ */

#define D3D10_1_SDK_VERSION (0x20)

HRESULT WINAPI D3D10CreateDevice1(IDXGIAdapter*,D3D10_DRIVER_TYPE,
    HMODULE,UINT,D3D10_FEATURE_LEVEL1,UINT,ID3D10Device1**);
HRESULT __stdcall  D3D10CreateDeviceAndSwapChain1(IDXGIAdapter *adapter,enum D3D10_DRIVER_TYPE driver_type,HMODULE swrast,UINT flags,D3D10_FEATURE_LEVEL1 feature_level,UINT sdk_version,DXGI_SWAP_CHAIN_DESC *swapchain_desc,IDXGISwapChain **swapchain,ID3D10Device1 **device);

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d10_1_h__ */
