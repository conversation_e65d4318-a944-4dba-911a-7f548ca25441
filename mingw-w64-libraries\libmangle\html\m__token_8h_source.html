<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: src/m_token.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<h1>src/m_token.h</h1><a href="m__token_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment">   Copyright (c) 2009, 2010 mingw-w64 project</span>
<a name="l00003"></a>00003 <span class="comment"></span>
<a name="l00004"></a>00004 <span class="comment">   Contributing authors: Kai Tietz, Jonathan Yong</span>
<a name="l00005"></a>00005 <span class="comment"></span>
<a name="l00006"></a>00006 <span class="comment">   Permission is hereby granted, free of charge, to any person obtaining a</span>
<a name="l00007"></a>00007 <span class="comment">   copy of this software and associated documentation files (the &quot;Software&quot;),</span>
<a name="l00008"></a>00008 <span class="comment">   to deal in the Software without restriction, including without limitation</span>
<a name="l00009"></a>00009 <span class="comment">   the rights to use, copy, modify, merge, publish, distribute, sublicense,</span>
<a name="l00010"></a>00010 <span class="comment">   and/or sell copies of the Software, and to permit persons to whom the</span>
<a name="l00011"></a>00011 <span class="comment">   Software is furnished to do so, subject to the following conditions:</span>
<a name="l00012"></a>00012 <span class="comment"></span>
<a name="l00013"></a>00013 <span class="comment">   The above copyright notice and this permission notice shall be included in</span>
<a name="l00014"></a>00014 <span class="comment">   all copies or substantial portions of the Software.</span>
<a name="l00015"></a>00015 <span class="comment"></span>
<a name="l00016"></a>00016 <span class="comment">   THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR</span>
<a name="l00017"></a>00017 <span class="comment">   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,</span>
<a name="l00018"></a>00018 <span class="comment">   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE</span>
<a name="l00019"></a>00019 <span class="comment">   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER</span>
<a name="l00020"></a>00020 <span class="comment">   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING</span>
<a name="l00021"></a>00021 <span class="comment">   FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER</span>
<a name="l00022"></a>00022 <span class="comment">   DEALINGS IN THE SOFTWARE.</span>
<a name="l00023"></a>00023 <span class="comment">*/</span>
<a name="l00024"></a>00024 <span class="preprocessor">#ifndef _M_TOKEN_HXX</span>
<a name="l00025"></a>00025 <span class="preprocessor"></span><span class="preprocessor">#define _M_TOKEN_HXX</span>
<a name="l00026"></a>00026 <span class="preprocessor"></span>
<a name="l00032"></a><a class="code" href="structs_gc_elem.html">00032</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_gc_elem.html">sGcElem</a> {
<a name="l00033"></a><a class="code" href="structs_gc_elem.html#a64b8d1bdb2e359ccd617a7c9d11ebac2">00033</a>   <span class="keyword">struct </span><a class="code" href="structs_gc_elem.html">sGcElem</a> *<a class="code" href="structs_gc_elem.html#a64b8d1bdb2e359ccd617a7c9d11ebac2">chain</a>;        
<a name="l00034"></a><a class="code" href="structs_gc_elem.html#a46d6f71dd8ab29948e8bfdb286bcc2fa">00034</a>   <span class="keywordtype">size_t</span> <a class="code" href="structs_gc_elem.html#a46d6f71dd8ab29948e8bfdb286bcc2fa">length</a>;                
<a name="l00035"></a><a class="code" href="structs_gc_elem.html#a9b0deb41a0551f5867e262c05cf9511b">00035</a>   <span class="keywordtype">char</span> <a class="code" href="structs_gc_elem.html#a9b0deb41a0551f5867e262c05cf9511b">dta</a>[1];                  
<a name="l00036"></a>00036 } <a class="code" href="structs_gc_elem.html">sGcElem</a>;
<a name="l00037"></a>00037 
<a name="l00044"></a>00044 <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> {
<a name="l00045"></a><a class="code" href="structlibmangle__gc__context__t.html#a8ab8fca04c3ee04275e085898817a4e8">00045</a>   <a class="code" href="structs_gc_elem.html">sGcElem</a> *<a class="code" href="structlibmangle__gc__context__t.html#a87a971b39a14440678c40974f56bbf08">head</a>;                
<a name="l00046"></a><a class="code" href="structlibmangle__gc__context__t.html#a194fd1438df6900a61b545e0e2dad866">00046</a>   <a class="code" href="structs_gc_elem.html">sGcElem</a> *<a class="code" href="structlibmangle__gc__context__t.html#a26e6a7692028d660e4a8224e14f0c3f6">tail</a>;                
<a name="l00047"></a>00047 } <a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a>;
<a name="l00048"></a>00048 
<a name="l00055"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">00055</a> <span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> {
<a name="l00056"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdaab5d1cb176ec018a7dd2e1debed34b98">00056</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdaab5d1cb176ec018a7dd2e1debed34b98">eMToken_none</a> = 0,                
<a name="l00057"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda8e367c0d5a2c127b0f1ae55573e92fcd">00057</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda8e367c0d5a2c127b0f1ae55573e92fcd">eMToken_value</a> = 1,               
<a name="l00058"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdae4b3f564d86b8e05792093eceba0612e">00058</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdae4b3f564d86b8e05792093eceba0612e">eMToken_name</a> = 2,                
<a name="l00059"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c">00059</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c">eMToken_dim</a> = 3,                 
<a name="l00060"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda22e1257761823f14db1c71ae94e7af8b">00060</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda22e1257761823f14db1c71ae94e7af8b">eMToken_unary</a> = 4,               
<a name="l00061"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda9e20893db7dcbd0e2173b4f559cbb363">00061</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda9e20893db7dcbd0e2173b4f559cbb363">eMToken_binary</a> = 5,              
<a name="l00062"></a><a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda66baeee525a866cdabe57f4e248b0f1a">00062</a>   <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda66baeee525a866cdabe57f4e248b0f1a">eMToken_MAX</a>                      
<a name="l00063"></a>00063 } <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a>;
<a name="l00064"></a>00064 
<a name="l00072"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">00072</a> <span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> {
<a name="l00073"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aeba2165312b4788cea28a9edd3011eb1">00073</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aeba2165312b4788cea28a9edd3011eb1">eMST_unmangled</a> = 0,               
<a name="l00074"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ace877760d7bed370bfdd6570130878bc">00074</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ace877760d7bed370bfdd6570130878bc">eMST_nttp</a> = 1,                    
<a name="l00075"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aab3ff1160a7b9f50b2734b0263482589">00075</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aab3ff1160a7b9f50b2734b0263482589">eMST_name</a> = 2,                    
<a name="l00076"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a92a6f2d7b92b576b9b64565f40cb8267">00076</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a92a6f2d7b92b576b9b64565f40cb8267">eMST_colon</a> = 3,                   
<a name="l00077"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc536c03cd872d606af6a4749c7a84e8">00077</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc536c03cd872d606af6a4749c7a84e8">eMST_rtti</a> = 4,                    
<a name="l00078"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a35df6016f35b2a5e8df1d01f477f9e17">00078</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a35df6016f35b2a5e8df1d01f477f9e17">eMST_cv</a> = 5,                      
<a name="l00079"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aacf994772d1d924f2213dfd8d224b1fe">00079</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aacf994772d1d924f2213dfd8d224b1fe">eMST_vftable</a> = 6,                 
<a name="l00080"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc20bcf98832c814c92709447c1db811">00080</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc20bcf98832c814c92709447c1db811">eMST_vbtable</a> = 7,                 
<a name="l00081"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a9d8acdbf8ea203bad5af3728e8ee5e43">00081</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a9d8acdbf8ea203bad5af3728e8ee5e43">eMST_vcall</a> = 8,                   
<a name="l00082"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a56c4f2ba212e690d3862736cefc9e9d1">00082</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a56c4f2ba212e690d3862736cefc9e9d1">eMST_opname</a> = 9,                  
<a name="l00083"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a02c0ba2ae316a629377149299fa60732">00083</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a02c0ba2ae316a629377149299fa60732">eMST_templargname</a> = 10,           
<a name="l00084"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a1c6d0872377629404a837b33093e4c20">00084</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a1c6d0872377629404a837b33093e4c20">eMST_type</a> = 11,                   
<a name="l00085"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d3878378f218ed88b37f69ccc8b212a">00085</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d3878378f218ed88b37f69ccc8b212a">eMST_dim</a>,                         
<a name="l00086"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af0a43b0c61ff4f5379159b46e21ca8c1">00086</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af0a43b0c61ff4f5379159b46e21ca8c1">eMST_val</a>,                         
<a name="l00087"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a0f681303ab234dbd4f348e4f4ca2d3fb">00087</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a0f681303ab234dbd4f348e4f4ca2d3fb">eMST_gcarray</a>, <span class="comment">/* __gc[,,,,] */</span>    
<a name="l00088"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a795ccccfc527f0ab6cfe6c372c47cbe6">00088</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a795ccccfc527f0ab6cfe6c372c47cbe6">eMST_slashed</a>,                     
<a name="l00089"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac94af9ef7756a7c82213991ed795600a">00089</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac94af9ef7756a7c82213991ed795600a">eMST_array</a>,                       
<a name="l00090"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a376aea6022acbe69f38de3c9ea784484">00090</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a376aea6022acbe69f38de3c9ea784484">eMST_element</a>,                     
<a name="l00091"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afcc464362c6d0c42ce707a51a7fbb9ae">00091</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afcc464362c6d0c42ce707a51a7fbb9ae">eMST_template_argument_list</a>,      
<a name="l00092"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ad08a86d5932f63c1a505083332ba1f2b">00092</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ad08a86d5932f63c1a505083332ba1f2b">eMST_ltgt</a>,                        
<a name="l00093"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac7455cb6f9f1dc1c495178075f5e617f">00093</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac7455cb6f9f1dc1c495178075f5e617f">eMST_frame</a>,                       
<a name="l00094"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae1a781af8dddef1a996e911152071d2f">00094</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae1a781af8dddef1a996e911152071d2f">eMST_throw</a>,                       
<a name="l00095"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a6726a799efbe0d2d722fc83e3a04ca05">00095</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a6726a799efbe0d2d722fc83e3a04ca05">eMST_rframe</a>,                      
<a name="l00096"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ab87ff64219a845b0674207f9352895ee">00096</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ab87ff64219a845b0674207f9352895ee">eMST_destructor</a>,                  
<a name="l00097"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a04c4d8922a43196bcbf80ef96ad3c5f8">00097</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a04c4d8922a43196bcbf80ef96ad3c5f8">eMST_oper</a>,                        
<a name="l00098"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a17a05df86d0d2e85f841baaccc2ca5c2">00098</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a17a05df86d0d2e85f841baaccc2ca5c2">eMST_colonarray</a>, <span class="comment">/* ::[] */</span>       
<a name="l00099"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae271da23693826e5cacab27fd56779dc">00099</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae271da23693826e5cacab27fd56779dc">eMST_lexical_frame</a>,               
<a name="l00100"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a4c46333751fc7ce9d5e6e89e82abeef4">00100</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a4c46333751fc7ce9d5e6e89e82abeef4">eMST_scope</a>,                       
<a name="l00101"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a3f4d325424d8fd3976f2dfb834cd145d">00101</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a3f4d325424d8fd3976f2dfb834cd145d">eMST_udt_returning</a>,               
<a name="l00102"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a90545698adebd8bbce09897217bd9695">00102</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a90545698adebd8bbce09897217bd9695">eMST_coloncolon</a>,                  
<a name="l00103"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a921c684ed8d84a713967491a27abc903">00103</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a921c684ed8d84a713967491a27abc903">eMST_assign</a>,                      
<a name="l00104"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afa6fba3d6a855d55277e3a8b74f4cb16">00104</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afa6fba3d6a855d55277e3a8b74f4cb16">eMST_templateparam</a>,               
<a name="l00105"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acf92fa1bb19698734ba3e697b96ce9e5">00105</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acf92fa1bb19698734ba3e697b96ce9e5">eMST_nonetypetemplateparam</a>,       
<a name="l00106"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af8e71df660ac1c8cf469ca3ddbbd0477">00106</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af8e71df660ac1c8cf469ca3ddbbd0477">eMST_exp</a>,                         
<a name="l00107"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae947463010b4b392684e86e14337d61b">00107</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae947463010b4b392684e86e14337d61b">eMST_combine</a>,                     
<a name="l00108"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acc1c13446a5ec298703df9bd94c6f20e">00108</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acc1c13446a5ec298703df9bd94c6f20e">eMST_ecsu</a>,                        
<a name="l00109"></a><a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d78963c03a17b55f921b31419f83d56">00109</a>   <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d78963c03a17b55f921b31419f83d56">eMST_based</a>                       
<a name="l00110"></a>00110 } <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>;
<a name="l00111"></a>00111 
<a name="l00116"></a><a class="code" href="structs_m_token__base.html">00116</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_m_token__base.html">sMToken_base</a> {
<a name="l00117"></a><a class="code" href="structs_m_token__base.html#a97bc54568330f5df3c61a99bd0721078">00117</a>   <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> <a class="code" href="structs_m_token__base.html#a97bc54568330f5df3c61a99bd0721078">kind</a>;            
<a name="l00118"></a><a class="code" href="structs_m_token__base.html#a06c35134d5aee4c88c50576e1f2a62ce">00118</a>   <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> <a class="code" href="structs_m_token__base.html#a06c35134d5aee4c88c50576e1f2a62ce">subkind</a>;        
<a name="l00119"></a><a class="code" href="structs_m_token__base.html#ae1284da4479fcba21ad51e471b89bd24">00119</a>   <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_m_token__base.html#ae1284da4479fcba21ad51e471b89bd24">chain</a>;         
<a name="l00120"></a><a class="code" href="structs_m_token__base.html#a8ded4c9376b5162e1951127611fcaf93">00120</a>   <span class="keywordtype">int</span> <a class="code" href="structs_m_token__base.html#a8ded4c9376b5162e1951127611fcaf93">flags</a>;                    
<a name="l00121"></a>00121 } <a class="code" href="structs_m_token__base.html">sMToken_base</a>;
<a name="l00122"></a>00122 
<a name="l00124"></a><a class="code" href="m__token_8h.html#a9c7f6053956c20047da91268be3e6a47">00124</a> <span class="preprocessor">#define MTOKEN_KIND(PT)         ((PT)-&gt;base.kind)</span>
<a name="l00125"></a>00125 <span class="preprocessor"></span>
<a name="l00127"></a><a class="code" href="m__token_8h.html#a5753385eac52aad6be25ae37f0ea5d6a">00127</a> <span class="preprocessor">#define MTOKEN_SUBKIND(PT)      ((PT)-&gt;base.subkind)</span>
<a name="l00128"></a>00128 <span class="preprocessor"></span>
<a name="l00130"></a><a class="code" href="m__token_8h.html#a22776018ac7fe7e7c0aa47cfe5f473a8">00130</a> <span class="preprocessor">#define MTOKEN_CHAIN(PT)        ((PT)-&gt;base.chain)</span>
<a name="l00131"></a>00131 <span class="preprocessor"></span>
<a name="l00133"></a><a class="code" href="m__token_8h.html#ac080f6582086796b1ede7f1f65ae9fcf">00133</a> <span class="preprocessor">#define MTOKEN_FLAGS(PT)        ((PT)-&gt;base.flags)</span>
<a name="l00134"></a>00134 <span class="preprocessor"></span>
<a name="l00135"></a><a class="code" href="m__token_8h.html#a1163ae872e9f50ddae2aeb936fc4d5e6">00135</a> <span class="preprocessor">#define MTOKEN_FLAGS_UDC    0x1 </span>
<a name="l00136"></a><a class="code" href="m__token_8h.html#adde521240f7b6401ffb3954772cfdb30">00136</a> <span class="preprocessor">#define MTOKEN_FLAGS_NOTE   0x2 </span>
<a name="l00137"></a><a class="code" href="m__token_8h.html#aa5b2060375f2aa1caa8995fb9e3fe8c2">00137</a> <span class="preprocessor">#define MTOKEN_FLAGS_PTRREF 0x4 </span>
<a name="l00138"></a><a class="code" href="m__token_8h.html#a707505a9dd27394e28326b9e24b8a0e4">00138</a> <span class="preprocessor">#define MTOKEN_FLAGS_ARRAY  0x8 </span>
<a name="l00145"></a><a class="code" href="structs_m_token__value.html">00145</a> <span class="preprocessor">typedef struct sMToken_value {</span>
<a name="l00146"></a><a class="code" href="structs_m_token__value.html#af2e6b79bc6f420cfe90256a8527c0e1e">00146</a> <span class="preprocessor"></span>  <a class="code" href="structs_m_token__base.html">sMToken_base</a> <a class="code" href="unionu_m_token.html#a4a4795bbd5a58f0f5d21ded3506c4a6c">base</a>;            
<a name="l00147"></a><a class="code" href="structs_m_token__value.html#a2f802692b54a6c65c1845939d0dbb202">00147</a>   uint64_t <a class="code" href="unionu_m_token.html#a97f52a4d0d3ed56e9cf79045402c5202">value</a>;               
<a name="l00148"></a><a class="code" href="structs_m_token__value.html#a6e11527994c9fcff6f3afecd3b8f3059">00148</a>   uint64_t size : 5;            
<a name="l00149"></a><a class="code" href="structs_m_token__value.html#a63ebab037c42f8740600cdec41b92407">00149</a>   uint64_t is_signed : 1;       
<a name="l00150"></a>00150 } <a class="code" href="structs_m_token__value.html">sMToken_value</a>;
<a name="l00151"></a>00151 
<a name="l00153"></a><a class="code" href="m__token_8h.html#a0d7b7e44c99e08fe263ea15190ceeee1">00153</a> <span class="preprocessor">#define MTOKEN_VALUE(PT)        ((PT)-&gt;value.value)</span>
<a name="l00154"></a>00154 <span class="preprocessor"></span>
<a name="l00156"></a><a class="code" href="m__token_8h.html#a92051c626009297e17ff622b77e809f7">00156</a> <span class="preprocessor">#define MTOKEN_VALUE_SIGNED(PT) ((PT)-&gt;value.is_signed)</span>
<a name="l00157"></a>00157 <span class="preprocessor"></span>
<a name="l00159"></a><a class="code" href="m__token_8h.html#a0912420c7697d7824cb9ce3761e999ac">00159</a> <span class="preprocessor">#define MTOKEN_VALUE_SIZE(PT)   ((PT)-&gt;value.size)</span>
<a name="l00160"></a>00160 <span class="preprocessor"></span>
<a name="l00165"></a><a class="code" href="structs_m_token__name.html">00165</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_m_token__name.html">sMToken_name</a> {
<a name="l00166"></a><a class="code" href="structs_m_token__name.html#af1aaf64a4294eb6493563806d78d6128">00166</a>   <a class="code" href="structs_m_token__base.html">sMToken_base</a> base;            
<a name="l00167"></a><a class="code" href="structs_m_token__name.html#a410cb714d5a874dd848e75360ddcd32a">00167</a>   <span class="keywordtype">char</span> <a class="code" href="structs_m_token__name.html#a410cb714d5a874dd848e75360ddcd32a">name</a>[1];                 
<a name="l00168"></a>00168 } <a class="code" href="structs_m_token__name.html">sMToken_name</a>;
<a name="l00169"></a>00169 
<a name="l00171"></a><a class="code" href="m__token_8h.html#ae3b0c2bd397aa5acede119bad863c8f8">00171</a> <span class="preprocessor">#define MTOKEN_NAME(PT)         ((PT)-&gt;name.name)</span>
<a name="l00172"></a>00172 <span class="preprocessor"></span>
<a name="l00177"></a><a class="code" href="structs_m_token__dim.html">00177</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_m_token__dim.html">sMToken_dim</a> {
<a name="l00178"></a><a class="code" href="structs_m_token__dim.html#a103ae0105ea54cb4e42960cad17e75b0">00178</a>   <a class="code" href="structs_m_token__base.html">sMToken_base</a> base;            
<a name="l00179"></a><a class="code" href="structs_m_token__dim.html#a5c16c40c478e326a93952cf620b2f99e">00179</a>   <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_m_token__dim.html#a5c16c40c478e326a93952cf620b2f99e">value</a>;         
<a name="l00180"></a><a class="code" href="structs_m_token__dim.html#ac1f77b90776014a1ea3f1920dd00637d">00180</a>   <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_m_token__dim.html#ac1f77b90776014a1ea3f1920dd00637d">non_tt_param</a>;  
<a name="l00181"></a><a class="code" href="structs_m_token__dim.html#a65b1c85b42d9b7870dde3b3bcfa067a3">00181</a>   <span class="keywordtype">int</span> <a class="code" href="structs_m_token__dim.html#a65b1c85b42d9b7870dde3b3bcfa067a3">beNegate</a>;                 
<a name="l00182"></a>00182 } <a class="code" href="structs_m_token__dim.html">sMToken_dim</a>;
<a name="l00183"></a>00183 
<a name="l00185"></a><a class="code" href="m__token_8h.html#a519b6bd0fb1c60d1077842bddeb731c0">00185</a> <span class="preprocessor">#define MTOKEN_DIM_VALUE(PT)    ((PT)-&gt;dim.value)</span>
<a name="l00186"></a>00186 <span class="preprocessor"></span>
<a name="l00188"></a><a class="code" href="m__token_8h.html#a5ded8e065363aa57a8c5ecb0e4b3a0f7">00188</a> <span class="preprocessor">#define MTOKEN_DIM_NTTP(PT)     ((PT)-&gt;dim.non_tt_param)</span>
<a name="l00189"></a>00189 <span class="preprocessor"></span>
<a name="l00191"></a><a class="code" href="m__token_8h.html#abe961c81235d1d263052dd61439dbcf3">00191</a> <span class="preprocessor">#define MTOKEN_DIM_NEGATE(PT)   ((PT)-&gt;dim.beNegate)</span>
<a name="l00192"></a>00192 <span class="preprocessor"></span>
<a name="l00197"></a><a class="code" href="structs_m_token___unary.html">00197</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_m_token___unary.html">sMToken_Unary</a>
<a name="l00198"></a>00198 {
<a name="l00199"></a><a class="code" href="structs_m_token___unary.html#a0df56324a69152319b1e3b86b767ae0c">00199</a>   <a class="code" href="structs_m_token__base.html">sMToken_base</a> base;            
<a name="l00200"></a><a class="code" href="structs_m_token___unary.html#a2ee427ec8f1a8a64df7df56ff438cf5d">00200</a>   <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_m_token___unary.html#a2ee427ec8f1a8a64df7df56ff438cf5d">unary</a>;         
<a name="l00201"></a>00201 } <a class="code" href="structs_m_token___unary.html">sMToken_Unary</a>;
<a name="l00202"></a>00202 
<a name="l00204"></a><a class="code" href="m__token_8h.html#a362970fb206c74f30355356570000221">00204</a> <span class="preprocessor">#define MTOKEN_UNARY(PT)        ((PT)-&gt;unary.unary)</span>
<a name="l00205"></a>00205 <span class="preprocessor"></span>
<a name="l00211"></a><a class="code" href="structs_m_token__binary.html">00211</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_m_token__binary.html">sMToken_binary</a> {
<a name="l00212"></a><a class="code" href="structs_m_token__binary.html#adce4a3fd6700e408f79cfadc39ad58f7">00212</a>   <a class="code" href="structs_m_token__base.html">sMToken_base</a> base;            
<a name="l00213"></a><a class="code" href="structs_m_token__binary.html#abee09455681a857a6ac3fc6bd1877f5c">00213</a>   <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_m_token__binary.html#abee09455681a857a6ac3fc6bd1877f5c">left</a>;          
<a name="l00214"></a><a class="code" href="structs_m_token__binary.html#a6e2b3b9e012d9bee596529f517d8752a">00214</a>   <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_m_token__binary.html#a6e2b3b9e012d9bee596529f517d8752a">right</a>;         
<a name="l00215"></a>00215 } <a class="code" href="structs_m_token__binary.html">sMToken_binary</a>;
<a name="l00216"></a>00216 
<a name="l00218"></a><a class="code" href="m__token_8h.html#ae7a7881952af6eea195152209a4166d8">00218</a> <span class="preprocessor">#define MTOKEN_BINARY_LEFT(PT)          ((PT)-&gt;binary.left)</span>
<a name="l00219"></a>00219 <span class="preprocessor"></span>
<a name="l00221"></a><a class="code" href="m__token_8h.html#a158648c39041985090c587f092b38316">00221</a> <span class="preprocessor">#define MTOKEN_BINARY_RIGHT(PT)         ((PT)-&gt;binary.right)</span>
<a name="l00222"></a>00222 <span class="preprocessor"></span>
<a name="l00229"></a><a class="code" href="unionu_m_token.html">00229</a> <span class="keyword">typedef</span> <span class="keyword">union </span><a class="code" href="unionu_m_token.html">uMToken</a> {
<a name="l00230"></a><a class="code" href="unionu_m_token.html#a4a4795bbd5a58f0f5d21ded3506c4a6c">00230</a>   <a class="code" href="structs_m_token__base.html">sMToken_base</a> base;        
<a name="l00231"></a><a class="code" href="unionu_m_token.html#a97f52a4d0d3ed56e9cf79045402c5202">00231</a>   <a class="code" href="structs_m_token__value.html">sMToken_value</a> <a class="code" href="unionu_m_token.html#a97f52a4d0d3ed56e9cf79045402c5202">value</a>;      
<a name="l00232"></a><a class="code" href="unionu_m_token.html#a168eea3eefe059407dbafc873823ce4d">00232</a>   <a class="code" href="structs_m_token__name.html">sMToken_name</a> <a class="code" href="unionu_m_token.html#a168eea3eefe059407dbafc873823ce4d">name</a>;        
<a name="l00233"></a><a class="code" href="unionu_m_token.html#aef4d7f4b830f7133b09b6670b98d1cb0">00233</a>   <a class="code" href="structs_m_token__dim.html">sMToken_dim</a> <a class="code" href="unionu_m_token.html#aef4d7f4b830f7133b09b6670b98d1cb0">dim</a>;          
<a name="l00234"></a><a class="code" href="unionu_m_token.html#ac30a468d7a8b3e6f8eca74bce8f9bf05">00234</a>   <a class="code" href="structs_m_token___unary.html">sMToken_Unary</a> <a class="code" href="unionu_m_token.html#ac30a468d7a8b3e6f8eca74bce8f9bf05">unary</a>;      
<a name="l00235"></a><a class="code" href="unionu_m_token.html#a7a0e65155e6a9f49c3cd18c682071bb6">00235</a>   <a class="code" href="structs_m_token__binary.html">sMToken_binary</a> <a class="code" href="unionu_m_token.html#a7a0e65155e6a9f49c3cd18c682071bb6">binary</a>;    
<a name="l00236"></a>00236 } <a class="code" href="unionu_m_token.html">uMToken</a>;
<a name="l00237"></a>00237 
<a name="l00247"></a>00247 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8h.html#abeb019f98a7616488287af32a6f9e51b">libmangle_gen_tok</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> kind, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> subkind, <span class="keywordtype">size_t</span> addend);
<a name="l00248"></a>00248 
<a name="l00254"></a>00254 <span class="keywordtype">void</span> <a class="code" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc);
<a name="l00255"></a>00255 
<a name="l00261"></a>00261 <a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *<a class="code" href="libmangle_8h.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle_generate_gc</a> (<span class="keywordtype">void</span>);
<a name="l00262"></a>00262 
<a name="l00269"></a>00269 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8c.html#a50ef074a3d1cf22f842abd4df7081743">chain_tok</a> (<a class="code" href="unionu_m_token.html">uMToken</a> *l, <a class="code" href="unionu_m_token.html">uMToken</a> *add);
<a name="l00270"></a>00270 
<a name="l00276"></a>00276 <span class="keywordtype">void</span> <a class="code" href="libmangle_8h.html#ab22601869037438e47eca7186a4cef65">libmangle_dump_tok</a> (FILE *fp, <a class="code" href="unionu_m_token.html">uMToken</a> *p);
<a name="l00277"></a>00277 
<a name="l00284"></a>00284 <span class="keywordtype">void</span> <a class="code" href="libmangle_8h.html#a2c4d83f71d35e434250eb2779e29ef29">libmangle_print_decl</a> (FILE *fp, <a class="code" href="unionu_m_token.html">uMToken</a> *p);
<a name="l00285"></a>00285 
<a name="l00293"></a>00293 <span class="keywordtype">char</span> *<a class="code" href="libmangle_8h.html#abf4af1b2e483a32beb147474853e696b">libmangle_sprint_decl</a> (<a class="code" href="unionu_m_token.html">uMToken</a> *r);
<a name="l00294"></a>00294 
<a name="l00305"></a>00305 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8c.html#a5e98df3f83afcc6e9e1f079091c0e567">gen_value</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, uint64_t val, <span class="keywordtype">int</span> is_signed, <span class="keywordtype">int</span> size);
<a name="l00306"></a>00306 
<a name="l00315"></a>00315 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8c.html#ac1a6fe5d506c4fd78650742da8d9e669">gen_name</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <span class="keyword">const</span> <span class="keywordtype">char</span> *name);
<a name="l00316"></a>00316 
<a name="l00328"></a>00328 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8c.html#aced2d2323162ca5846cdb13d631169d6">gen_dim</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, uint64_t val, <span class="keyword">const</span> <span class="keywordtype">char</span> *non_tt_param, <span class="keywordtype">int</span> fSigned, <span class="keywordtype">int</span> fNegate);
<a name="l00329"></a>00329 
<a name="l00338"></a>00338 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8c.html#a8c630a1c57e3d4f5009448af0d43fbb8">gen_unary</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <a class="code" href="unionu_m_token.html">uMToken</a> *un);
<a name="l00339"></a>00339 
<a name="l00349"></a>00349 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="m__token_8c.html#a0deb555c3210a60f0b5189ae462ed620">gen_binary</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">enum</span> <a class="code" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <a class="code" href="unionu_m_token.html">uMToken</a> *l, <a class="code" href="unionu_m_token.html">uMToken</a> *r);
<a name="l00350"></a>00350 
<a name="l00351"></a>00351 <span class="preprocessor">#endif</span>
</pre></div></div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
