;
; Definition file of CLUSAPI.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "CLUSAPI.dll"
EXPORTS
CCHlpAddNodeUpdateCluster
CCHlpConfigureNode
CCHlpCreateClusterNameCOIfNotExists
CCHlpGetClusterServiceSecret
CCHlpGetDNSHostLabel
CCHlpRestoreClusterVirtualObjectToInitialState
AddClusterNode
AddClusterResourceDependency
AddClusterResourceNode
AddResourceToClusterSharedVolumes
BackupClusterDatabase
CanResourceBeDependent
CancelClusterGroupOperation
ChangeClusterResourceGroup
CloseCluster
CloseClusterGroup
CloseClusterNetInterface
CloseClusterNetwork
CloseClusterNode
CloseClusterNotifyPort
CloseClusterResource
ClusterCloseEnum
ClusterCloseEnumEx
ClusterControl
ClusterEnum
ClusterEnumEx
ClusterFreeMemory
ClusterFreeMrrResponse
ClusterGetEnumCount
ClusterGetEnumC<PERSON>Ex
ClusterGroupCloseEnum
ClusterGroupCloseEnumEx
ClusterGroupControl
ClusterGroupEnum
ClusterGroupEnumEx
ClusterGroupGetEnumCount
ClusterGroupGetEnumCountEx
ClusterGroupOpenEnum
ClusterGroupOpenEnumEx
ClusterNetInterfaceControl
ClusterNetworkCloseEnum
ClusterNetworkControl
ClusterNetworkEnum
ClusterNetworkGetEnumCount
ClusterNetworkOpenEnum
ClusterNodeCloseEnum
ClusterNodeCloseEnumEx
ClusterNodeControl
ClusterNodeEnum
ClusterNodeEnumEx
ClusterNodeGetEnumCount
ClusterNodeGetEnumCountEx
ClusterNodeOpenEnum
ClusterNodeOpenEnumEx
ClusterOpenEnum
ClusterOpenEnumEx
ClusterRegBatchAddCommand
ClusterRegBatchCloseNotification
ClusterRegBatchReadCommand
ClusterRegCloseBatch
ClusterRegCloseBatchEx
ClusterRegCloseBatchNotifyPort
ClusterRegCloseKey
ClusterRegCloseReadBatch
ClusterRegCloseReadBatchReply
ClusterRegCreateBatch
ClusterRegCreateBatchNotifyPort
ClusterRegCreateKey
ClusterRegCreateKeyForceSync
ClusterRegCreateReadBatch
ClusterRegDeleteKey
ClusterRegDeleteKeyForceSync
ClusterRegDeleteValue
ClusterRegDeleteValueForceSync
ClusterRegEnumKey
ClusterRegEnumValue
ClusterRegGetBatchNotification
ClusterRegGetKeySecurity
ClusterRegOpenKey
ClusterRegQueryAllValues
ClusterRegQueryInfoKey
ClusterRegQueryValue
ClusterRegReadBatchAddCommand
ClusterRegReadBatchReplyNextCommand
ClusterRegSetKeySecurity
ClusterRegSetValue
ClusterRegSetValueForceSync
ClusterRegSyncDatabase
ClusterResourceCloseEnum
ClusterResourceCloseEnumEx
ClusterResourceControl
ClusterResourceEnum
ClusterResourceEnumEx
ClusterResourceGetEnumCount
ClusterResourceGetEnumCountEx
ClusterResourceOpenEnum
ClusterResourceOpenEnumEx
ClusterResourceTypeCloseEnum
ClusterResourceTypeControl
ClusterResourceTypeEnum
ClusterResourceTypeGetEnumCount
ClusterResourceTypeOpenEnum
ClusterSendReceiveMrr
ClusterSharedVolumeClearBackupState
ClusterSharedVolumeSetSnapshotState
ClusterStmFindDisk
CreateCluster
CreateClusterGroup
CreateClusterGroupEx
CreateClusterManagementPoint
CreateClusterNotifyPort
CreateClusterNotifyPortV2
CreateClusterResource
CreateClusterResourceType
CreateClusterResourceWithId
DeleteClusterGroup
DeleteClusterResource
DeleteClusterResourceType
DestroyCluster
DestroyClusterGroup
EvictClusterNode
EvictClusterNodeEx
FailClusterResource
GetClusterFromGroup
GetClusterFromNetInterface
GetClusterFromNetwork
GetClusterFromNode
GetClusterFromResource
GetClusterGroupKey
GetClusterGroupState
GetClusterInformation
GetClusterKey
GetClusterNetInterface
GetClusterNetInterfaceKey
GetClusterNetInterfaceState
GetClusterNetworkId
GetClusterNetworkKey
GetClusterNetworkState
GetClusterNodeId
GetClusterNodeKey
GetClusterNodeState
GetClusterNotify
GetClusterNotifyV2
GetClusterQuorumResource
GetClusterResourceDependencyExpression
GetClusterResourceKey
GetClusterResourceNetworkName
GetClusterResourceState
GetClusterResourceTypeKey
GetClusterSharedVolumeNameForFile
GetNodeClusterState
GetNotifyEventHandle
IsFileOnClusterSharedVolume
MoveClusterGroup
MoveClusterGroupEx
OfflineClusterGroup
OfflineClusterGroupEx
OfflineClusterResource
OfflineClusterResourceEx
OnlineClusterGroup
OnlineClusterGroupEx
OnlineClusterResource
OnlineClusterResourceEx
OpenCluster
OpenClusterEx
OpenClusterEx2
OpenClusterGroup
OpenClusterGroupEx
OpenClusterNetInterface
OpenClusterNetInterfaceEx
OpenClusterNetwork
OpenClusterNetworkEx
OpenClusterNode
OpenClusterNodeEx
OpenClusterResource
OpenClusterResourceEx
PauseClusterNode
PauseClusterNodeEx
RegisterClusterNotify
RegisterClusterNotifyV2
RemoveClusterResourceDependency
RemoveClusterResourceNode
RemoveResourceFromClusterSharedVolumes
RestartClusterResource
RestoreClusterDatabase
ResumeClusterNode
ResumeClusterNodeEx
SetClusterGroupName
SetClusterGroupNodeList
SetClusterName
SetClusterNetworkName
SetClusterNetworkPriorityOrder
SetClusterQuorumResource
SetClusterResourceDependencyExpression
SetClusterResourceName
SetClusterServiceAccountPassword
