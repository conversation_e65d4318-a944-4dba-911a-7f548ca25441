;
; Definition file of P2PGRAPH.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "P2PGRAPH.dll"
EXPORTS
PeerGraphForceStopPresencePrivate
pMemoryHelper DATA
PeerGraphAddRecord
PeerGraphClose
PeerGraphCloseDirectConnection
PeerGraphConnect
PeerGraphCreate
PeerGraphDelete
PeerGraphDeleteRecord
PeerGraphEndEnumeration
PeerGraphEnumConnections
PeerGraphEnumNodes
PeerGraphEnumRecords
PeerGraphExportDatabase
PeerGraphFreeData
PeerGraphGetEventData
PeerGraphGetItemCount
PeerGraphGetNextItem
PeerGraphGetNodeInfo
PeerGraphGetProperties
PeerGraphGetRecord
PeerGraphGetStatus
PeerGraphImportDatabase
PeerGraphListen
PeerGraphOpen
PeerGraphOpenDirectConnection
<PERSON>eer<PERSON>raph<PERSON>eerTimeToUniversalTime
PeerGraphRegisterEvent
PeerGraphSearchRecords
PeerGraphSendData
PeerGraphSetNodeAttributes
PeerGraphSetPresence
PeerGraphSetProperties
PeerGraphShutdown
PeerGraphStartup
PeerGraphSuspendTimers
PeerGraphUniversalTimeToPeerTime
PeerGraphUnregisterEvent
PeerGraphUpdateRecord
PeerGraphValidateDeferredRecords
