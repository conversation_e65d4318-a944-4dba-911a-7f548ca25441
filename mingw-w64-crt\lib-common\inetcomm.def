;
; Definition file of INETCOMM.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "INETCOMM.dll"
EXPORTS
ord_1 @1
RichMimeEdit_CreateInstance
CreateCommunityTransport
CreateIMAPTransport
CreateIMAPTransport2
CreateNNTPTransport
CreatePOP3Transport
CreateRASTransport
CreateRangeList
CreateSMTPTransport
EssContentHintDecodeEx
EssContentHintEncodeEx
EssKeyExchPreferenceDecodeEx
EssKeyExchPreferenceEncodeEx
EssMLHistoryDecodeEx
EssMLHistoryEncodeEx
EssReceiptDecodeEx
EssReceiptEncodeEx
EssReceiptRequestDecodeEx
EssReceiptRequestEncodeEx
EssSecurityLabelDecodeEx
EssSecurityLabelEncodeEx
EssSignCertificateDecodeEx
EssSignCertificateEncodeEx
GetDllMajorVersion
HrAthGetFileN<PERSON>etFileNameW
HrAttachDataFromBodyPart
HrAttachDataFromFile
HrCreateDisplayNameWithSizeForFile
HrDoAttachmentVerb
HrFreeAttachData
HrGetAttachIcon
HrGetAttachIconByFile
HrGetDisplayNameWithSizeForFile
HrGetLastOpenFileDirectory
HrGetLastOpenFileDirectoryW
HrSaveAttachToFile
HrSaveAttachmentAs
MimeEditCreateMimeDocument
MimeEditDocumentFromStream
MimeEditGetBackgroundImageUrl
MimeEditIsSafeToRun
MimeEditViewSource
MimeGetAddressFormatW
MimeOleAlgNameFromSMimeCap
MimeOleAlgStrengthFromSMimeCap
MimeOleClearDirtyTree
MimeOleConvertEnrichedToHTML
MimeOleCreateBody
MimeOleCreateByteStream
MimeOleCreateHashTable
MimeOleCreateHeaderTable
MimeOleCreateMessage
MimeOleCreateMessageParts
MimeOleCreatePropertySet
MimeOleCreateSecurity
MimeOleCreateVirtualStream
MimeOleDecodeHeader
MimeOleEncodeHeader
MimeOleFileTimeToInetDate
MimeOleFindCharset
MimeOleGenerateCID
MimeOleGenerateFileName
MimeOleGenerateMID
MimeOleGetAllocator
MimeOleGetBodyPropA
MimeOleGetBodyPropW
MimeOleGetCertsFromThumbprints
MimeOleGetCharsetInfo
MimeOleGetCodePageCharset
MimeOleGetCodePageInfo
MimeOleGetContentTypeExt
MimeOleGetDefaultCharset
MimeOleGetExtContentType
MimeOleGetFileExtension
MimeOleGetFileInfo
MimeOleGetFileInfoW
MimeOleGetInternat
MimeOleGetPropA
MimeOleGetPropW
MimeOleGetPropertySchema
MimeOleGetRelatedSection
MimeOleInetDateToFileTime
MimeOleObjectFromMoniker
MimeOleOpenFileStream
MimeOleParseMhtmlUrl
MimeOleParseRfc822Address
MimeOleParseRfc822AddressW
MimeOleSMimeCapAddCert
MimeOleSMimeCapAddSMimeCap
MimeOleSMimeCapGetEncAlg
MimeOleSMimeCapGetHashAlg
MimeOleSMimeCapInit
MimeOleSMimeCapRelease
MimeOleSMimeCapsFromDlg
MimeOleSMimeCapsFull
MimeOleSMimeCapsToDlg
MimeOleSetBodyPropA
MimeOleSetBodyPropW
MimeOleSetCompatMode
MimeOleSetDefaultCharset
MimeOleSetPropA
MimeOleSetPropW
MimeOleStripHeaders
MimeOleUnEscapeStringInPlace
MimeOleUnEscapeStringInPlaceW
ord_702 @702
ord_703 @703
ord_704 @704
ord_705 @705
ord_706 @706
ord_707 @707
ord_708 @708
