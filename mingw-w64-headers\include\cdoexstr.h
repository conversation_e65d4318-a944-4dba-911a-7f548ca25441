/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef __cdostr_h_
#define __cdostr_h_

#define cdoBusy L"Busy"
#define cdoFree L"Free"
#define cdoOOF L"OOF"
#define cdoTentative L"Tentative"

#define cdoAllDayEvent L"urn:schemas:calendar:alldayevent"
#define cdoAttendeeRole L"urn:schemas:calendar:attendeerole"
#define cdoAttendeeStatus L"urn:schemas:calendar:attendeestatus"
#define cdoBusyStatus L"urn:schemas:calendar:busystatus"
#define cdoCalendarLastModified L"urn:schemas:calendar:lastmodified"
#define cdoCalendarUID L"urn:schemas:calendar:uid"
#define cdoContact L"urn:schemas:calendar:contact"
#define cdoContactURL L"urn:schemas:calendar:contacturl"
#define cdoCreated L"urn:schemas:calendar:created"
#define cdoDelegatedFrom L"urn:schemas:calendar:delegated-from"
#define cdoDelegatedTo L"urn:schemas:calendar:delegated-to"
#define cdoDescriptionURL L"urn:schemas:calendar:descriptionurl"
#define cdoDTEnd L"urn:schemas:calendar:dtend"
#define cdoDTStamp L"urn:schemas:calendar:dtstamp"
#define cdoDTStart L"urn:schemas:calendar:dtstart"
#define cdoDuration L"urn:schemas:calendar:duration"
#define cdoExDate L"urn:schemas:calendar:exdate"
#define cdoExRule L"urn:schemas:calendar:exrule"
#define cdoFburl L"urn:schemas:calendar:fburl"
#define cdoGEOLatitude L"urn:schemas:calendar:geolatitude"
#define cdoGEOLongitude L"urn:schemas:calendar:geolongitude"
#define cdoInstanceType L"urn:schemas:calendar:instancetype"
#define cdoIsOrganizer L"urn:schemas:calendar:isorganizer"
#define cdoLastModifiedTime L"urn:schemas:calendar:lastmodifiedtime"
#define cdoLocation L"urn:schemas:calendar:location"
#define cdoLocationURL L"urn:schemas:calendar:locationurl"
#define cdoMeetingStatus L"urn:schemas:calendar:meetingstatus"
#define cdoMethod L"urn:schemas:calendar:method"
#define cdoProdId L"urn:schemas:calendar:prodid"
#define cdoRDate L"urn:schemas:calendar:rdate"
#define cdoRecurrenceId L"urn:schemas:calendar:recurrenceid"
#define cdoRecurrenceIdRange L"urn:schemas:calendar:recurrenceidrange"
#define cdoReminderOffset L"urn:schemas:calendar:reminderoffset"
#define cdoReplyTime L"urn:schemas:calendar:replytime"
#define cdoResources L"urn:schemas:calendar:resources"
#define cdoResponseRequested L"urn:schemas:calendar:responserequested"
#define cdoRRule L"urn:schemas:calendar:rrule"
#define cdoRSVP L"urn:schemas:calendar:rsvp"
#define cdoSentBy L"urn:schemas:calendar:sent-by"
#define cdoSequence L"urn:schemas:calendar:sequence"
#define cdoTimeZoneIDURN L"urn:schemas:calendar:timezoneid"
#define cdoTimeZoneURN L"urn:schemas:calendar:timezone"
#define cdoTransparency L"urn:schemas:calendar:transparent"
#define cdoVersion L"urn:schemas:calendar:version"

#define cdoBIG5 L"big5"
#define cdoEUC_JP L"euc-jp"
#define cdoEUC_KR L"euc-kr"
#define cdoGB2312 L"gb2312"
#define cdoISO_2022_JP L"iso-2022-jp"
#define cdoISO_2022_KR L"iso-2022-kr"
#define cdoISO_8859_1 L"iso-8859-1"
#define cdoISO_8859_2 L"iso-8859-2"
#define cdoISO_8859_3 L"iso-8859-3"
#define cdoISO_8859_4 L"iso-8859-4"
#define cdoISO_8859_5 L"iso-8859-5"
#define cdoISO_8859_6 L"iso-8859-6"
#define cdoISO_8859_7 L"iso-8859-7"
#define cdoISO_8859_8 L"iso-8859-8"
#define cdoISO_8859_9 L"iso-8859-9"
#define cdoKOI8_R L"koi8-r"
#define cdoShift_JIS L"shift-jis"
#define cdoUS_ASCII L"us-ascii"
#define cdoUTF_7 L"utf-7"
#define cdoUTF_8 L"utf-8"

#define cdoAutoPromoteBodyParts L"http://schemas.microsoft.com/cdo/configuration/autopromotebodyparts"
#define cdoFlushBuffersOnWrite L"http://schemas.microsoft.com/cdo/configuration/flushbuffersonwrite"
#define cdoHTTPCookies L"http://schemas.microsoft.com/cdo/configuration/httpcookies"
#define cdoLanguageCode L"http://schemas.microsoft.com/cdo/configuration/languagecode"
#define cdoNNTPAccountName L"http://schemas.microsoft.com/cdo/configuration/nntpaccountname"
#define cdoNNTPAuthenticate L"http://schemas.microsoft.com/cdo/configuration/nntpauthenticate"
#define cdoNNTPConnectionTimeout L"http://schemas.microsoft.com/cdo/configuration/nntpconnectiontimeout"
#define cdoNNTPServer L"http://schemas.microsoft.com/cdo/configuration/nntpserver"
#define cdoNNTPServerPickupDirectory L"http://schemas.microsoft.com/cdo/configuration/nntpserverpickupdirectory"
#define cdoNNTPServerPort L"http://schemas.microsoft.com/cdo/configuration/nntpserverport"
#define cdoNNTPUseSSL L"http://schemas.microsoft.com/cdo/configuration/nntpusessl"
#define cdoPostEmailAddress L"http://schemas.microsoft.com/cdo/configuration/postemailaddress"
#define cdoPostPassword L"http://schemas.microsoft.com/cdo/configuration/postpassword"
#define cdoPostUserName L"http://schemas.microsoft.com/cdo/configuration/postusername"
#define cdoPostUserReplyEmailAddress L"http://schemas.microsoft.com/cdo/configuration/postuserreplyemailaddress"
#define cdoPostUsingMethod L"http://schemas.microsoft.com/cdo/configuration/postusing"
#define cdoSaveSentItems L"http://schemas.microsoft.com/cdo/configuration/savesentitems"
#define cdoSendEmailAddress L"http://schemas.microsoft.com/cdo/configuration/sendemailaddress"
#define cdoSendPassword L"http://schemas.microsoft.com/cdo/configuration/sendpassword"
#define cdoSendUserName L"http://schemas.microsoft.com/cdo/configuration/sendusername"
#define cdoSendUserReplyEmailAddress L"http://schemas.microsoft.com/cdo/configuration/senduserreplyemailaddress"
#define cdoSendUsingMethod L"http://schemas.microsoft.com/cdo/configuration/sendusing"
#define cdoSMTPAccountName L"http://schemas.microsoft.com/cdo/configuration/smtpaccountname"
#define cdoSMTPAuthenticate L"http://schemas.microsoft.com/cdo/configuration/smtpauthenticate"
#define cdoSMTPConnectionTimeout L"http://schemas.microsoft.com/cdo/configuration/smtpconnectiontimeout"
#define cdoSMTPServer L"http://schemas.microsoft.com/cdo/configuration/smtpserver"
#define cdoSMTPServerPickupDirectory L"http://schemas.microsoft.com/cdo/configuration/smtpserverpickupdirectory"
#define cdoSMTPServerPort L"http://schemas.microsoft.com/cdo/configuration/smtpserverport"
#define cdoSMTPUseSSL L"http://schemas.microsoft.com/cdo/configuration/smtpusessl"
#define cdoURLGetLatestVersion L"http://schemas.microsoft.com/cdo/configuration/urlgetlatestversion"
#define cdoURLProxyBypass L"http://schemas.microsoft.com/cdo/configuration/urlproxybypass"
#define cdoURLProxyServer L"http://schemas.microsoft.com/cdo/configuration/urlproxyserver"
#define cdoURLSource L"http://schemas.microsoft.com/cdo/configuration/urlsource"
#define cdoUseMessageResponseText L"http://schemas.microsoft.com/cdo/configuration/usemessageresponsetext"

#define cdoAccount L"urn:schemas:contacts:account"
#define cdoBirthday L"urn:schemas:contacts:bday"
#define cdoCallbackPhone L"urn:schemas:contacts:callbackphone"
#define cdoChildrensNames L"urn:schemas:contacts:childrensnames"
#define cdoCommonName L"urn:schemas:contacts:cn"
#define cdoComputerNetworkName L"urn:schemas:contacts:computernetworkname"
#define cdoCustomerId L"urn:schemas:contacts:customerid"
#define cdoDepartment L"urn:schemas:contacts:department"
#define cdoDistinguishedName L"urn:schemas:contacts:dn"
#define cdoEmail1Address L"urn:schemas:contacts:email1"
#define cdoEmail2Address L"urn:schemas:contacts:email2"
#define cdoEmail3Address L"urn:schemas:contacts:email3"
#define cdoEmployeeNumber L"urn:schemas:contacts:employeenumber"
#define cdoFileAs L"urn:schemas:contacts:fileas"
#define cdoFileAsId L"urn:schemas:contacts:fileasid"
#define cdoFirstName L"urn:schemas:contacts:givenName"
#define cdoFtpSite L"urn:schemas:contacts:ftpsite"
#define cdoGender L"urn:schemas:contacts:gender"
#define cdoGovernmentId L"urn:schemas:contacts:governmentid"
#define cdoHobbies L"urn:schemas:contacts:hobbies"
#define cdoHomeCity L"urn:schemas:contacts:homeCity"
#define cdoHomeCountry L"urn:schemas:contacts:homeCountry"
#define cdoHomeFax L"urn:schemas:contacts:homefax"
#define cdoHomeLatitude L"urn:schemas:contacts:homelatitude"
#define cdoHomeLongitude L"urn:schemas:contacts:homelongitude"
#define cdoHomePhone L"urn:schemas:contacts:homePhone"
#define cdoHomePhone2 L"urn:schemas:contacts:homephone2"
#define cdoHomePostalAddress L"urn:schemas:contacts:homepostaladdress"
#define cdoHomePostalCode L"urn:schemas:contacts:homePostalCode"
#define cdoHomePostOfficeBox L"urn:schemas:contacts:homepostofficebox"
#define cdoHomeState L"urn:schemas:contacts:homeState"
#define cdoHomeStreet L"urn:schemas:contacts:homeStreet"
#define cdoHomeTimeZone L"urn:schemas:contacts:hometimezone"
#define cdoInitials L"urn:schemas:contacts:initials"
#define cdoInternationalISDNNumber L"urn:schemas:contacts:internationalisdnnumber"
#define cdoLanguage L"urn:schemas:contacts:language"
#define cdoLastName L"urn:schemas:contacts:sn"
#define cdoMailingAddressId L"urn:schemas:contacts:mailingaddressid"
#define cdoMailingCity L"urn:schemas:contacts:mailingcity"
#define cdoMailingCountry L"urn:schemas:contacts:mailingcountry"
#define cdoMailingPostalAddress L"urn:schemas:contacts:mailingpostaladdress"
#define cdoMailingPostalCode L"urn:schemas:contacts:mailingpostalcode"
#define cdoMailingPostOfficeBox L"urn:schemas:contacts:mailingpostofficebox"
#define cdoMailingState L"urn:schemas:contacts:mailingstate"
#define cdoMailingStreet L"urn:schemas:contacts:mailingstreet"
#define cdoManager L"urn:schemas:contacts:manager"
#define cdoMapURL L"urn:schemas:contacts:mapurl"
#define cdoMiddleName L"urn:schemas:contacts:middlename"
#define cdoNamePrefix L"urn:schemas:contacts:personaltitle"
#define cdoNameSuffix L"urn:schemas:contacts:namesuffix"
#define cdoNickname L"urn:schemas:contacts:nickname"
#define cdoOrganizationName L"urn:schemas:contacts:o"
#define cdoOriginalAuthor L"urn:schemas:contacts:authorig"
#define cdoOtherCity L"urn:schemas:contacts:othercity"
#define cdoOtherCountry L"urn:schemas:contacts:othercountry"
#define cdoOtherCountryCode L"urn:schemas:contacts:othercountrycode"
#define cdoOtherFax L"urn:schemas:contacts:otherfax"
#define cdoOtherMobile L"urn:schemas:contacts:othermobile"
#define cdoOtherPager L"urn:schemas:contacts:otherpager"
#define cdoOtherPostalAddress L"urn:schemas:contacts:otherpostaladdress"
#define cdoOtherPostalCode L"urn:schemas:contacts:otherpostalcode"
#define cdoOtherPostOfficeBox L"urn:schemas:contacts:otherpostofficebox"
#define cdoOtherState L"urn:schemas:contacts:otherstate"
#define cdoOtherStreet L"urn:schemas:contacts:otherstreet"
#define cdoOtherTimeZone L"urn:schemas:contacts:othertimezone"
#define cdoOtherWorkPhone L"urn:schemas:contacts:otherTelephone"
#define cdoPersonalURL L"urn:schemas:contacts:personalHomePage"
#define cdoProfession L"urn:schemas:contacts:profession"
#define cdoProxyAddresses L"urn:schemas:contacts:proxyaddresses"
#define cdoRoomNumber L"urn:schemas:contacts:roomnumber"
#define cdoSecretary L"urn:schemas:contacts:secretary"
#define cdoSecretaryCommonName L"urn:schemas:contacts:secretarycn"
#define cdoSecretaryURL L"urn:schemas:contacts:secretaryurl"
#define cdoSourceURL L"urn:schemas:contacts:sourceurl"
#define cdoSpouseCommonName L"urn:schemas:contacts:spousecn"
#define cdoSubmissionContLength L"urn:schemas:contacts:submissioncontlength"
#define cdoTelexNumber L"urn:schemas:contacts:telexnumber"
#define cdoTitle L"urn:schemas:contacts:title"
#define cdoUserCertificate L"urn:schemas:contacts:usercertificate"
#define cdoWeddingAnniversary L"urn:schemas:contacts:weddinganniversary"
#define cdoWorkAddress L"urn:schemas:contacts:workaddress"
#define cdoWorkCity L"urn:schemas:contacts:l"
#define cdoWorkCountry L"urn:schemas:contacts:co"
#define cdoWorkCountryAbbreviation L"urn:schemas:contacts:c"
#define cdoWorkFax L"urn:schemas:contacts:facsimiletelephonenumber"
#define cdoWorkMobilePhone L"urn:schemas:contacts:mobile"
#define cdoWorkPager L"urn:schemas:contacts:pager"
#define cdoWorkPhone L"urn:schemas:contacts:telephoneNumber"
#define cdoWorkPhone2 L"urn:schemas:contacts:telephonenumber2"
#define cdoWorkPostalCode L"urn:schemas:contacts:postalcode"
#define cdoWorkPostOfficeBox L"urn:schemas:contacts:postofficebox"
#define cdoWorkState L"urn:schemas:contacts:st"
#define cdoWorkStreet L"urn:schemas:contacts:street"

#define cdoGif L"image/gif"
#define cdoJpeg L"image/jpeg"
#define cdoMessageExternalBody L"message/external-body"
#define cdoMessagePartial L"message/partial"
#define cdoMessageRFC822 L"message/rfc822"
#define cdoMultipartAlternative L"multipart/alternative"
#define cdoMultipartDigest L"multipart/digest"
#define cdoMultipartMixed L"multipart/mixed"
#define cdoMultipartRelated L"multipart/related"
#define cdoTextHTML L"text/html"
#define cdoTextPlain L"text/plain"

#define cdoAbstract L"DAV:abstract"
#define cdoChildCount L"DAV:childcount"
#define cdoContentClass L"DAV:contentclass"
#define cdoCreationDate L"DAV:creationdate"
#define cdoDAVComment L"DAV:comment"
#define cdoDefaultDocument L"DAV:defaultdocument"
#define cdoDisplayName L"DAV:displayname"
#define cdoGetContentLength L"DAV:getcontentlength"
#define cdoGetContentType L"DAV:getcontenttype"
#define cdoGetEtag L"DAV:getetag"
#define cdoGetLastModified L"DAV:getlastmodified"
#define cdoHasChildren L"DAV:haschildren"
#define cdoHasSubs L"DAV:hassubs"
#define cdoHref L"DAV:href"
#define cdoId L"DAV:id"
#define cdoIsCollection L"DAV:iscollection"
#define cdoIsFolder L"DAV:isfolder"
#define cdoIsHidden L"DAV:ishidden"
#define cdoIsRoot L"DAV:isroot"
#define cdoIsStructuredDocument L"DAV:isstructureddocument"
#define cdoLastAccessed L"DAV:lastaccessed"
#define cdoLastModified L"DAV:lastmodified"
#define cdoLockDiscovery L"DAV:lockdiscovery"
#define cdoNoSubs L"DAV:nosubs"
#define cdoObjectCount L"DAV:objectcount"
#define cdoParentName L"DAV:parentname"
#define cdoResourceType L"DAV:resourcetype"
#define cdoSupportedLock L"DAV:supportedlock"
#define cdoUID L"DAV:uid"
#define cdoVisibleCount L"DAV:visiblecount"

#define cdo7bit L"7bit"
#define cdo8bit L"8bit"
#define cdoBase64 L"base64"
#define cdoBinary L"binary"
#define cdoMacBinHex40 L"mac-binhex40"
#define cdoQuotedPrintable L"quoted-printable"
#define cdoUuencode L"uuencode"

#define cdoAltRecipient L"http://schemas.microsoft.com/exchange/altrecipient"
#define cdoCompanies L"http://schemas.microsoft.com/exchange/companies"
#define cdoFolderSize L"http://schemas.microsoft.com/exchange/foldersize"
#define cdoHardLinkList L"http://schemas.microsoft.com/exchange/hardlinklist"
#define cdoSensitivity L"http://schemas.microsoft.com/exchange/sensitivity"

#define cdoAttachmentFilename L"urn:schemas:httpmail:attachmentfilename"
#define cdoBcc L"urn:schemas:httpmail:bcc"
#define cdoCalendarFolderURL L"urn:schemas:httpmail:calendar"
#define cdoCc L"urn:schemas:httpmail:cc"
#define cdoContactFolderURL L"urn:schemas:httpmail:contacts"
#define cdoContentDispositionType L"urn:schemas:httpmail:content-disposition-type"
#define cdoContentMediaType L"urn:schemas:httpmail:content-media-type"
#define cdoDate L"urn:schemas:httpmail:date"
#define cdoDateReceived L"urn:schemas:httpmail:datereceived"
#define cdoDeletedItems L"urn:schemas:httpmail:deleteditems"
#define cdoFrom L"urn:schemas:httpmail:from"
#define cdoHasAttachment L"urn:schemas:httpmail:hasattachment"
#define cdoHTMLDescription L"urn:schemas:httpmail:htmldescription"
#define cdoImportance L"urn:schemas:httpmail:importance"
#define cdoInbox L"urn:schemas:httpmail:inbox"
#define cdoJournal L"urn:schemas:httpmail:journal"
#define cdoMsgFolderRoot L"urn:schemas:httpmail:msgfolderroot"
#define cdoNormalizedSubject L"urn:schemas:httpmail:normalizedsubject"
#define cdoNotes L"urn:schemas:httpmail:notes"
#define cdoOutbox L"urn:schemas:httpmail:outbox"
#define cdoPriority L"urn:schemas:httpmail:priority"
#define cdoRead L"urn:schemas:httpmail:read"
#define cdoReplyTo L"urn:schemas:httpmail:reply-to"
#define cdoSender L"urn:schemas:httpmail:sender"
#define cdoSendMsg L"urn:schemas:httpmail:sendmsg"
#define cdoSentItems L"urn:schemas:httpmail:sentitems"
#define cdoSpecial L"urn:schemas:httpmail:special"
#define cdoSubject L"urn:schemas:httpmail:subject"
#define cdoSubmitted L"urn:schemas:httpmail:submitted"
#define cdoTasks L"urn:schemas:httpmail:tasks"
#define cdoTextDescription L"urn:schemas:httpmail:textdescription"
#define cdoThreadTopic L"urn:schemas:httpmail:thread-topic"
#define cdoTo L"urn:schemas:httpmail:to"
#define cdoUnreadCount L"urn:schemas:httpmail:unreadcount"

#define cdoAdoRecord L"_Record"
#define cdoAdoRecordset L"_Recordset"
#define cdoAdoStream L"_Stream"
#define cdoIAddressee L"IAddressee"
#define cdoIAppointment L"IAppointment"
#define cdoIBodyPart L"IBodyPart"
#define cdoICalendarMessage L"ICalendarMessage"
#define cdoICalendarPart L"ICalendarPart"
#define cdoICalendarParts L"ICalendarParts"
#define cdoIConfiguration L"IConfiguration"
#define cdoIDataSource L"IDataSource"
#define cdoIFolder L"IFolder"
#define cdoIItem L"IItem"
#define cdoIMailbox L"IMailbox"
#define cdoIMessage L"IMessage"
#define cdoIPerson L"IPerson"
#define cdoIRow L"IRow"
#define cdoIRowset L"IRowset"
#define cdoIStream L"IStream"

#define cdoApproved L"urn:schemas:mailheader:approved"
#define cdoComment L"urn:schemas:mailheader:comment"
#define cdoContentBase L"urn:schemas:mailheader:content-base"
#define cdoContentDescription L"urn:schemas:mailheader:content-description"
#define cdoContentDisposition L"urn:schemas:mailheader:content-disposition"
#define cdoContentId L"urn:schemas:mailheader:content-id"
#define cdoContentLanguage L"urn:schemas:mailheader:content-language"
#define cdoContentLocation L"urn:schemas:mailheader:content-location"
#define cdoContentTransferEncoding L"urn:schemas:mailheader:content-transfer-encoding"
#define cdoContentType L"urn:schemas:mailheader:content-type"
#define cdoControl L"urn:schemas:mailheader:control"
#define cdoDisposition L"urn:schemas:mailheader:disposition"
#define cdoDispositionNotificationTo L"urn:schemas:mailheader:disposition-notification-to"
#define cdoDistribution L"urn:schemas:mailheader:distribution"
#define cdoExpires L"urn:schemas:mailheader:expires"
#define cdoFollowupTo L"urn:schemas:mailheader:followup-to"
#define cdoInReplyTo L"urn:schemas:mailheader:in-reply-to"
#define cdoLines L"urn:schemas:mailheader:lines"
#define cdoMessageId L"urn:schemas:mailheader:message-id"
#define cdoMIMEVersion L"urn:schemas:mailheader:mime-version"
#define cdoNewsgroups L"urn:schemas:mailheader:newsgroups"
#define cdoOrganization L"urn:schemas:mailheader:organization"
#define cdoOriginalRecipient L"urn:schemas:mailheader:original-recipient"
#define cdoPath L"urn:schemas:mailheader:path"
#define cdoPostingVersion L"urn:schemas:mailheader:posting-version"
#define cdoReceived L"urn:schemas:mailheader:received"
#define cdoReferences L"urn:schemas:mailheader:references"
#define cdoRelayVersion L"urn:schemas:mailheader:relay-version"
#define cdoReturnPath L"urn:schemas:mailheader:return-path"
#define cdoReturnReceiptTo L"urn:schemas:mailheader:return-receipt-to"
#define cdoSummary L"urn:schemas:mailheader:summary"
#define cdoThreadIndex L"urn:schemas:mailheader:thread-index"
#define cdoXMailer L"urn:schemas:mailheader:x-mailer"
#define cdoXref L"urn:schemas:mailheader:xref"
#define cdoXUnsent L"urn:schemas:mailheader:x-unsent"

#define cdoMeetingStatusCancelled L"Cancelled"
#define cdoMeetingStatusConfirmed L"Confirmed"
#define cdoMeetingStatusTentative L"Tentative"

#define cdoAdd L"Add"
#define cdoCancel L"Cancel"
#define cdoCounter L"Counter"
#define cdoDeclineCounter L"DeclineCounter"
#define cdoPublish L"Publish"
#define cdoRefresh L"Refresh"
#define cdoReply L"Reply"
#define cdoRequest L"Request"

#define cdoNSCalendar L"urn:schemas:calendar:"
#define cdoNSConfiguration L"http://schemas.microsoft.com/cdo/configuration/"
#define cdoNSContacts L"urn:schemas:contacts:"
#define cdoNSDAV L"DAV:"
#define cdoNSHTTPMail L"urn:schemas:httpmail:"
#define cdoNSMailHeader L"urn:schemas:mailheader:"
#define cdoNSNNTPEnvelope L"http://schemas.microsoft.com/cdo/nntpenvelope/"
#define cdoNSSMTPEnvelope L"http://schemas.microsoft.com/cdo/smtpenvelope/"
#define cdoNSVCAL L"urn:schemas:vcal:"
#define cdoNewsgroupList L"http://schemas.microsoft.com/cdo/nntpenvelope/newsgrouplist"
#define cdoNNTPProcessing L"http://schemas.microsoft.com/cdo/nntpenvelope/nntpprocessing"
#define cdoKeywords L"urn:schemas-microsoft-com:office:office#Keywords"
#define cdoNone L"None"
#define cdoThisAndFuture L"ThisAndFuture"
#define cdoThisAndPrior L"ThisAndPrior"
#define cdoArrivalTime L"http://schemas.microsoft.com/cdo/smtpenvelope/arrivaltime"
#define cdoClientIPAddress L"http://schemas.microsoft.com/cdo/smtpenvelope/clientipaddress"
#define cdoMessageStatus L"http://schemas.microsoft.com/cdo/smtpenvelope/messagestatus"
#define cdoPickupFileName L"http://schemas.microsoft.com/cdo/smtpenvelope/pickupfilename"
#define cdoRecipientList L"http://schemas.microsoft.com/cdo/smtpenvelope/recipientlist"
#define cdoSenderEmailAddress L"http://schemas.microsoft.com/cdo/smtpenvelope/senderemailaddress"
#define cdoOpaque L"Opaque"
#define cdoTransparent L"Transparent"

#endif
