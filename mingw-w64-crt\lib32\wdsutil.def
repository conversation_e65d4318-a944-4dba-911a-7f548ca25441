;
; Definition file of WDSUTIL.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "WDSUTIL.dll"
EXPORTS
; public: __thiscall <class CStringUserSetting>::<class CStringUserSetting>(class <class CStringUserSetting> const &)
??0?$CShimUserSetting@VCStringUserSetting@@@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall <class CStringUserSetting>::<class CStringUserSetting>(void)
??0?$CShimUserSetting@VCStringUserSetting@@@@QAE@XZ
; public: __thiscall <class CUInt32UserSetting>::<class CUInt32UserSetting>(class <class CUInt32UserSetting> const &)
??0?$CShimUserSetting@VCUInt32UserSetting@@@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall <class CUInt32UserSetting>::<class CUInt32UserSetting>(void)
??0?$CShimUser<PERSON>etting@VCUInt32UserSetting@@@@QAE@XZ
; public: __thiscall <class CUInt64UserSetting>::<class CUInt64UserSetting>(class <class CUInt64UserSetting> const &)
??0?$CShimUserSetting@VCUInt64UserSetting@@@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall <class CUInt64UserSetting>::<class CUInt64UserSetting>(void)
??0?$CShimUserSetting@VCUInt64UserSetting@@@@QAE@XZ
; public: __thiscall CComputerNameSetting::CComputerNameSetting(class CComputerNameSetting const &)
??0CComputerNameSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CComputerNameSetting::CComputerNameSetting(void)
??0CComputerNameSetting@@QAE@XZ
; public: __thiscall CDUUIProgressSetting::CDUUIProgressSetting(class CDUUIProgressSetting const &)
??0CDUUIProgressSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CDUUIProgressSetting::CDUUIProgressSetting(void)
??0CDUUIProgressSetting@@QAE@XZ
; public: __thiscall CDUUIWelcomeSetting::CDUUIWelcomeSetting(class CDUUIWelcomeSetting const &)
??0CDUUIWelcomeSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CDUUIWelcomeSetting::CDUUIWelcomeSetting(void)
??0CDUUIWelcomeSetting@@QAE@XZ
; public: __thiscall CDiskPartFileSystemUserSetting::CDiskPartFileSystemUserSetting(class CDiskPartFileSystemUserSetting const &)
??0CDiskPartFileSystemUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CDiskPartFileSystemUserSetting::CDiskPartFileSystemUserSetting(void)
??0CDiskPartFileSystemUserSetting@@QAE@XZ
; public: __thiscall CDiskPartFormatUserSetting::CDiskPartFormatUserSetting(class CDiskPartFormatUserSetting const &)
??0CDiskPartFormatUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CDiskPartFormatUserSetting::CDiskPartFormatUserSetting(void)
??0CDiskPartFormatUserSetting@@QAE@XZ
; public: __thiscall CDiskPartUserSetting::CDiskPartUserSetting(class CDiskPartUserSetting const &)
??0CDiskPartUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CDiskPartUserSetting::CDiskPartUserSetting(void)
??0CDiskPartUserSetting@@QAE@XZ
; public: __thiscall CEulaSetting::CEulaSetting(class CEulaSetting const &)
??0CEulaSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CEulaSetting::CEulaSetting(void)
??0CEulaSetting@@QAE@XZ
; public: __thiscall CIBSUIImageSelectionSetting::CIBSUIImageSelectionSetting(class CIBSUIImageSelectionSetting const &)
??0CIBSUIImageSelectionSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CIBSUIImageSelectionSetting::CIBSUIImageSelectionSetting(void)
??0CIBSUIImageSelectionSetting@@QAE@XZ
; public: __thiscall CKeyboardSetting::CKeyboardSetting(class CKeyboardSetting const &)
??0CKeyboardSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CKeyboardSetting::CKeyboardSetting(void)
??0CKeyboardSetting@@QAE@XZ
; public: __thiscall COOBEUIFinishSetting::COOBEUIFinishSetting(class COOBEUIFinishSetting const &)
??0COOBEUIFinishSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall COOBEUIFinishSetting::COOBEUIFinishSetting(void)
??0COOBEUIFinishSetting@@QAE@XZ
; public: __thiscall COOBEUIWelcomeSetting::COOBEUIWelcomeSetting(class COOBEUIWelcomeSetting const &)
??0COOBEUIWelcomeSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall COOBEUIWelcomeSetting::COOBEUIWelcomeSetting(void)
??0COOBEUIWelcomeSetting@@QAE@XZ
; public: __thiscall CProductKeyUserSetting::CProductKeyUserSetting(class CProductKeyUserSetting const &)
??0CProductKeyUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CProductKeyUserSetting::CProductKeyUserSetting(void)
??0CProductKeyUserSetting@@QAE@XZ
; public: __thiscall CSetupUISummarySetting::CSetupUISummarySetting(class CSetupUISummarySetting const &)
??0CSetupUISummarySetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CSetupUISummarySetting::CSetupUISummarySetting(void)
??0CSetupUISummarySetting@@QAE@XZ
; public: __thiscall CSetupUIWelcomeSetting::CSetupUIWelcomeSetting(class CSetupUIWelcomeSetting const &)
??0CSetupUIWelcomeSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CSetupUIWelcomeSetting::CSetupUIWelcomeSetting(void)
??0CSetupUIWelcomeSetting@@QAE@XZ
; public: __thiscall CShimStringUserSetting::CShimStringUserSetting(class CShimStringUserSetting const &)
??0CShimStringUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CShimStringUserSetting::CShimStringUserSetting(void)
??0CShimStringUserSetting@@QAE@XZ
; public: __thiscall CShimUInt32UserSetting::CShimUInt32UserSetting(class CShimUInt32UserSetting const &)
??0CShimUInt32UserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CShimUInt32UserSetting::CShimUInt32UserSetting(void)
??0CShimUInt32UserSetting@@QAE@XZ
; public: __thiscall CShimUInt64UserSetting::CShimUInt64UserSetting(class CShimUInt64UserSetting const &)
??0CShimUInt64UserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CShimUInt64UserSetting::CShimUInt64UserSetting(void)
??0CShimUInt64UserSetting@@QAE@XZ
; protected: __thiscall CShowFlagUserSetting::CShowFlagUserSetting(void)
??0CShowFlagUserSetting@@IAE@XZ
; public: __thiscall CShowFlagUserSetting::CShowFlagUserSetting(class CShowFlagUserSetting const &)
??0CShowFlagUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CSimpleStringUserSetting::CSimpleStringUserSetting(class CSimpleStringUserSetting const &)
??0CSimpleStringUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CSimpleStringUserSetting::CSimpleStringUserSetting(void)
??0CSimpleStringUserSetting@@QAE@XZ
; public: __thiscall CSimpleUInt32UserSetting::CSimpleUInt32UserSetting(class CSimpleUInt32UserSetting const &)
??0CSimpleUInt32UserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CSimpleUInt32UserSetting::CSimpleUInt32UserSetting(void)
??0CSimpleUInt32UserSetting@@QAE@XZ
; public: __thiscall CSimpleUInt64UserSetting::CSimpleUInt64UserSetting(class CSimpleUInt64UserSetting const &)
??0CSimpleUInt64UserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CSimpleUInt64UserSetting::CSimpleUInt64UserSetting(void)
??0CSimpleUInt64UserSetting@@QAE@XZ
; protected: __thiscall CStringUserSetting::CStringUserSetting(void)
??0CStringUserSetting@@IAE@XZ
; public: __thiscall CStringUserSetting::CStringUserSetting(class CStringUserSetting const &)
??0CStringUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CTimezoneSetting::CTimezoneSetting(class CTimezoneSetting const &)
??0CTimezoneSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CTimezoneSetting::CTimezoneSetting(void)
??0CTimezoneSetting@@QAE@XZ
; protected: __thiscall CUInt32UserSetting::CUInt32UserSetting(void)
??0CUInt32UserSetting@@IAE@XZ
; public: __thiscall CUInt32UserSetting::CUInt32UserSetting(class CUInt32UserSetting const &)
??0CUInt32UserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; protected: __thiscall CUInt64UserSetting::CUInt64UserSetting(void)
??0CUInt64UserSetting@@IAE@XZ
; public: __thiscall CUInt64UserSetting::CUInt64UserSetting(class CUInt64UserSetting const &)
??0CUInt64UserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CUpgStoreUserSetting::CUpgStoreUserSetting(class CUpgStoreUserSetting const &)
??0CUpgStoreUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CUpgStoreUserSetting::CUpgStoreUserSetting(void)
??0CUpgStoreUserSetting@@QAE@XZ
; public: __thiscall CUpgradeUserSetting::CUpgradeUserSetting(class CUpgradeUserSetting const &)
??0CUpgradeUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CUpgradeUserSetting::CUpgradeUserSetting(void)
??0CUpgradeUserSetting@@QAE@XZ
; public: __thiscall CUserSetting::CUserSetting(class CUserSetting const &)
??0CUserSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CUserSetting::CUserSetting(void)
??0CUserSetting@@QAE@XZ
; public: __thiscall CWDSUIImageSelectionSetting::CWDSUIImageSelectionSetting(class CWDSUIImageSelectionSetting const &)
??0CWDSUIImageSelectionSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CWDSUIImageSelectionSetting::CWDSUIImageSelectionSetting(void)
??0CWDSUIImageSelectionSetting@@QAE@XZ
; public: __thiscall CWDSUIWelcomeSetting::CWDSUIWelcomeSetting(class CWDSUIWelcomeSetting const &)
??0CWDSUIWelcomeSetting@@QAE@ABV0@@Z ; has WINAPI (@4)
; public: __thiscall CWDSUIWelcomeSetting::CWDSUIWelcomeSetting(void)
??0CWDSUIWelcomeSetting@@QAE@XZ
; public: __thiscall <class CStringUserSetting>::~<class CStringUserSetting>(void)
??1?$CShimUserSetting@VCStringUserSetting@@@@QAE@XZ
; public: __thiscall <class CUInt32UserSetting>::~<class CUInt32UserSetting>(void)
??1?$CShimUserSetting@VCUInt32UserSetting@@@@QAE@XZ
; public: __thiscall <class CUInt64UserSetting>::~<class CUInt64UserSetting>(void)
??1?$CShimUserSetting@VCUInt64UserSetting@@@@QAE@XZ
; public: __thiscall CComputerNameSetting::~CComputerNameSetting(void)
??1CComputerNameSetting@@QAE@XZ
; public: __thiscall CDUUIProgressSetting::~CDUUIProgressSetting(void)
??1CDUUIProgressSetting@@QAE@XZ
; public: __thiscall CDUUIWelcomeSetting::~CDUUIWelcomeSetting(void)
??1CDUUIWelcomeSetting@@QAE@XZ
; public: __thiscall CDiskPartFileSystemUserSetting::~CDiskPartFileSystemUserSetting(void)
??1CDiskPartFileSystemUserSetting@@QAE@XZ
; public: __thiscall CDiskPartFormatUserSetting::~CDiskPartFormatUserSetting(void)
??1CDiskPartFormatUserSetting@@QAE@XZ
; public: __thiscall CDiskPartUserSetting::~CDiskPartUserSetting(void)
??1CDiskPartUserSetting@@QAE@XZ
; public: __thiscall CEulaSetting::~CEulaSetting(void)
??1CEulaSetting@@QAE@XZ
; public: __thiscall CIBSUIImageSelectionSetting::~CIBSUIImageSelectionSetting(void)
??1CIBSUIImageSelectionSetting@@QAE@XZ
; public: __thiscall CKeyboardSetting::~CKeyboardSetting(void)
??1CKeyboardSetting@@QAE@XZ
; public: __thiscall COOBEUIFinishSetting::~COOBEUIFinishSetting(void)
??1COOBEUIFinishSetting@@QAE@XZ
; public: __thiscall COOBEUIWelcomeSetting::~COOBEUIWelcomeSetting(void)
??1COOBEUIWelcomeSetting@@QAE@XZ
; public: __thiscall CProductKeyUserSetting::~CProductKeyUserSetting(void)
??1CProductKeyUserSetting@@QAE@XZ
; public: __thiscall CSetupUISummarySetting::~CSetupUISummarySetting(void)
??1CSetupUISummarySetting@@QAE@XZ
; public: __thiscall CSetupUIWelcomeSetting::~CSetupUIWelcomeSetting(void)
??1CSetupUIWelcomeSetting@@QAE@XZ
; public: __thiscall CShimStringUserSetting::~CShimStringUserSetting(void)
??1CShimStringUserSetting@@QAE@XZ
; public: __thiscall CShimUInt32UserSetting::~CShimUInt32UserSetting(void)
??1CShimUInt32UserSetting@@QAE@XZ
; public: __thiscall CShimUInt64UserSetting::~CShimUInt64UserSetting(void)
??1CShimUInt64UserSetting@@QAE@XZ
; protected: __thiscall CShowFlagUserSetting::~CShowFlagUserSetting(void)
??1CShowFlagUserSetting@@IAE@XZ
; public: __thiscall CSimpleStringUserSetting::~CSimpleStringUserSetting(void)
??1CSimpleStringUserSetting@@QAE@XZ
; public: __thiscall CSimpleUInt32UserSetting::~CSimpleUInt32UserSetting(void)
??1CSimpleUInt32UserSetting@@QAE@XZ
; public: __thiscall CSimpleUInt64UserSetting::~CSimpleUInt64UserSetting(void)
??1CSimpleUInt64UserSetting@@QAE@XZ
; protected: __thiscall CStringUserSetting::~CStringUserSetting(void)
??1CStringUserSetting@@IAE@XZ
; public: __thiscall CTimezoneSetting::~CTimezoneSetting(void)
??1CTimezoneSetting@@QAE@XZ
; protected: __thiscall CUInt32UserSetting::~CUInt32UserSetting(void)
??1CUInt32UserSetting@@IAE@XZ
; protected: __thiscall CUInt64UserSetting::~CUInt64UserSetting(void)
??1CUInt64UserSetting@@IAE@XZ
; public: __thiscall CUpgStoreUserSetting::~CUpgStoreUserSetting(void)
??1CUpgStoreUserSetting@@QAE@XZ
; public: __thiscall CUpgradeUserSetting::~CUpgradeUserSetting(void)
??1CUpgradeUserSetting@@QAE@XZ
; public: __thiscall CUserSetting::~CUserSetting(void)
??1CUserSetting@@QAE@XZ
; public: __thiscall CWDSUIImageSelectionSetting::~CWDSUIImageSelectionSetting(void)
??1CWDSUIImageSelectionSetting@@QAE@XZ
; public: __thiscall CWDSUIWelcomeSetting::~CWDSUIWelcomeSetting(void)
??1CWDSUIWelcomeSetting@@QAE@XZ
; public: class <class CStringUserSetting> &__thiscall <class CStringUserSetting>::operator =(class <class CStringUserSetting> const &)
??4?$CShimUserSetting@VCStringUserSetting@@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <class CUInt32UserSetting> &__thiscall <class CUInt32UserSetting>::operator =(class <class CUInt32UserSetting> const &)
??4?$CShimUserSetting@VCUInt32UserSetting@@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <class CUInt64UserSetting> &__thiscall <class CUInt64UserSetting>::operator =(class <class CUInt64UserSetting> const &)
??4?$CShimUserSetting@VCUInt64UserSetting@@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CComputerNameSetting &__thiscall CComputerNameSetting::operator =(class CComputerNameSetting const &)
??4CComputerNameSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CDUUIProgressSetting &__thiscall CDUUIProgressSetting::operator =(class CDUUIProgressSetting const &)
??4CDUUIProgressSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CDUUIWelcomeSetting &__thiscall CDUUIWelcomeSetting::operator =(class CDUUIWelcomeSetting const &)
??4CDUUIWelcomeSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CDiskPartFileSystemUserSetting &__thiscall CDiskPartFileSystemUserSetting::operator =(class CDiskPartFileSystemUserSetting const &)
??4CDiskPartFileSystemUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CDiskPartFormatUserSetting &__thiscall CDiskPartFormatUserSetting::operator =(class CDiskPartFormatUserSetting const &)
??4CDiskPartFormatUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CDiskPartUserSetting &__thiscall CDiskPartUserSetting::operator =(class CDiskPartUserSetting const &)
??4CDiskPartUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CEulaSetting &__thiscall CEulaSetting::operator =(class CEulaSetting const &)
??4CEulaSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CIBSUIImageSelectionSetting &__thiscall CIBSUIImageSelectionSetting::operator =(class CIBSUIImageSelectionSetting const &)
??4CIBSUIImageSelectionSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CKeyboardSetting &__thiscall CKeyboardSetting::operator =(class CKeyboardSetting const &)
??4CKeyboardSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class COOBEUIFinishSetting &__thiscall COOBEUIFinishSetting::operator =(class COOBEUIFinishSetting const &)
??4COOBEUIFinishSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class COOBEUIWelcomeSetting &__thiscall COOBEUIWelcomeSetting::operator =(class COOBEUIWelcomeSetting const &)
??4COOBEUIWelcomeSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CProductKeyUserSetting &__thiscall CProductKeyUserSetting::operator =(class CProductKeyUserSetting const &)
??4CProductKeyUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CSetupUISummarySetting &__thiscall CSetupUISummarySetting::operator =(class CSetupUISummarySetting const &)
??4CSetupUISummarySetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CSetupUIWelcomeSetting &__thiscall CSetupUIWelcomeSetting::operator =(class CSetupUIWelcomeSetting const &)
??4CSetupUIWelcomeSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CShimStringUserSetting &__thiscall CShimStringUserSetting::operator =(class CShimStringUserSetting const &)
??4CShimStringUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CShimUInt32UserSetting &__thiscall CShimUInt32UserSetting::operator =(class CShimUInt32UserSetting const &)
??4CShimUInt32UserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CShimUInt64UserSetting &__thiscall CShimUInt64UserSetting::operator =(class CShimUInt64UserSetting const &)
??4CShimUInt64UserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CShowFlagUserSetting &__thiscall CShowFlagUserSetting::operator =(class CShowFlagUserSetting const &)
??4CShowFlagUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CSimpleStringUserSetting &__thiscall CSimpleStringUserSetting::operator =(class CSimpleStringUserSetting const &)
??4CSimpleStringUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CSimpleUInt32UserSetting &__thiscall CSimpleUInt32UserSetting::operator =(class CSimpleUInt32UserSetting const &)
??4CSimpleUInt32UserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CSimpleUInt64UserSetting &__thiscall CSimpleUInt64UserSetting::operator =(class CSimpleUInt64UserSetting const &)
??4CSimpleUInt64UserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CStringUserSetting &__thiscall CStringUserSetting::operator =(class CStringUserSetting const &)
??4CStringUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CTimezoneSetting &__thiscall CTimezoneSetting::operator =(class CTimezoneSetting const &)
??4CTimezoneSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CUInt32UserSetting &__thiscall CUInt32UserSetting::operator =(class CUInt32UserSetting const &)
??4CUInt32UserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CUInt64UserSetting &__thiscall CUInt64UserSetting::operator =(class CUInt64UserSetting const &)
??4CUInt64UserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CUpgStoreUserSetting &__thiscall CUpgStoreUserSetting::operator =(class CUpgStoreUserSetting const &)
??4CUpgStoreUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CUpgradeUserSetting &__thiscall CUpgradeUserSetting::operator =(class CUpgradeUserSetting const &)
??4CUpgradeUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CUserSetting &__thiscall CUserSetting::operator =(class CUserSetting const &)
??4CUserSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CWDSUIImageSelectionSetting &__thiscall CWDSUIImageSelectionSetting::operator =(class CWDSUIImageSelectionSetting const &)
??4CWDSUIImageSelectionSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class CWDSUIWelcomeSetting &__thiscall CWDSUIWelcomeSetting::operator =(class CWDSUIWelcomeSetting const &)
??4CWDSUIWelcomeSetting@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; const <class CStringUserSetting>::$vftable
??_7?$CShimUserSetting@VCStringUserSetting@@@@6B@ DATA
; const <class CUInt32UserSetting>::$vftable
??_7?$CShimUserSetting@VCUInt32UserSetting@@@@6B@ DATA
; const <class CUInt64UserSetting>::$vftable
??_7?$CShimUserSetting@VCUInt64UserSetting@@@@6B@ DATA
; const CComputerNameSetting::$vftable
??_7CComputerNameSetting@@6B@ DATA
; const CDUUIProgressSetting::$vftable
??_7CDUUIProgressSetting@@6B@ DATA
; const CDUUIWelcomeSetting::$vftable
??_7CDUUIWelcomeSetting@@6B@ DATA
; const CDiskPartFileSystemUserSetting::$vftable
??_7CDiskPartFileSystemUserSetting@@6B@ DATA
; const CDiskPartFormatUserSetting::$vftable
??_7CDiskPartFormatUserSetting@@6B@ DATA
; const CDiskPartUserSetting::$vftable
??_7CDiskPartUserSetting@@6B@ DATA
; const CEulaSetting::$vftable
??_7CEulaSetting@@6B@ DATA
; const CIBSUIImageSelectionSetting::$vftable
??_7CIBSUIImageSelectionSetting@@6B@ DATA
; const CKeyboardSetting::$vftable
??_7CKeyboardSetting@@6B@ DATA
; const COOBEUIFinishSetting::$vftable
??_7COOBEUIFinishSetting@@6B@ DATA
; const COOBEUIWelcomeSetting::$vftable
??_7COOBEUIWelcomeSetting@@6B@ DATA
; const CProductKeyUserSetting::$vftable
??_7CProductKeyUserSetting@@6B@ DATA
; const CSetupUISummarySetting::$vftable
??_7CSetupUISummarySetting@@6B@ DATA
; const CSetupUIWelcomeSetting::$vftable
??_7CSetupUIWelcomeSetting@@6B@ DATA
; const CShimStringUserSetting::$vftable
??_7CShimStringUserSetting@@6B@ DATA
; const CShimUInt32UserSetting::$vftable
??_7CShimUInt32UserSetting@@6B@ DATA
; const CShimUInt64UserSetting::$vftable
??_7CShimUInt64UserSetting@@6B@ DATA
; const CShowFlagUserSetting::$vftable
??_7CShowFlagUserSetting@@6B@ DATA
; const CSimpleStringUserSetting::$vftable
??_7CSimpleStringUserSetting@@6B@ DATA
; const CSimpleUInt32UserSetting::$vftable
??_7CSimpleUInt32UserSetting@@6B@ DATA
; const CSimpleUInt64UserSetting::$vftable
??_7CSimpleUInt64UserSetting@@6B@ DATA
; const CStringUserSetting::$vftable
??_7CStringUserSetting@@6B@ DATA
; const CTimezoneSetting::$vftable
??_7CTimezoneSetting@@6B@ DATA
; const CUInt32UserSetting::$vftable
??_7CUInt32UserSetting@@6B@ DATA
; const CUInt64UserSetting::$vftable
??_7CUInt64UserSetting@@6B@ DATA
; const CUpgStoreUserSetting::$vftable
??_7CUpgStoreUserSetting@@6B@ DATA
; const CUpgradeUserSetting::$vftable
??_7CUpgradeUserSetting@@6B@ DATA
; const CUserSetting::$vftable
??_7CUserSetting@@6B@ DATA
; const CWDSUIImageSelectionSetting::$vftable
??_7CWDSUIImageSelectionSetting@@6B@ DATA
; const CWDSUIWelcomeSetting::$vftable
??_7CWDSUIWelcomeSetting@@6B@ DATA
; protected: void __thiscall CUserSetting::AcquireMutex(void)
?AcquireMutex@CUserSetting@@IAEXXZ
; protected: long __thiscall CUserSetting::DeserializeField(unsigned short const *,unsigned int,struct WDS_DATA *,int)
?DeserializeField@CUserSetting@@IAEJPBGIPAUWDS_DATA@@H@Z ; has WINAPI (@16)
; protected: long __thiscall CStringUserSetting::DeserializeString(unsigned short **,int)
?DeserializeString@CStringUserSetting@@IAEJPAPAGH@Z ; has WINAPI (@8)
; protected: long __thiscall CUInt32UserSetting::DeserializeUInt32(unsigned int *,int)
?DeserializeUInt32@CUInt32UserSetting@@IAEJPAIH@Z ; has WINAPI (@8)
; protected: long __thiscall CUInt64UserSetting::DeserializeUInt64(unsigned __int64 *,int)
?DeserializeUInt64@CUInt64UserSetting@@IAEJPA_KH@Z ; has WINAPI (@8)
DiskRegionSupportsCapabilityForType@28
DiskSupportsCapabilityForType@20
FreeReason@4
GetApplicableDiskReason@20
GetApplicableDiskRegionReason@28
; protected: struct _BLACKBOARD *__thiscall CUserSetting::GetBlackboard(void)
?GetBlackboard@CUserSetting@@IAEPAU_BLACKBOARD@@XZ
GetDiskKey@8
; protected: long __thiscall CUserSetting::GetKeyName(unsigned short *,int,int)
?GetKeyName@CUserSetting@@IAEJPAGHH@Z ; has WINAPI (@12)
; public: void *__thiscall CUserSetting::GetModuleId(void)
?GetModuleId@CUserSetting@@QAEPAXXZ
GetRegionKey@16
; public: static int __stdcall CUpgradeUserSetting::IsUpgrade(void)
?IsUpgrade@CUpgradeUserSetting@@SGHXZ
LogDiskReasons@8
LogDiskRegionReasons@16
; protected: long __thiscall CUserSetting::ReadError(long *,int)
?ReadError@CUserSetting@@IAEJPAJH@Z ; has WINAPI (@8)
; protected: long __thiscall CUserSetting::ReadShow(int *,int)
?ReadShow@CUserSetting@@IAEJPAHH@Z ; has WINAPI (@8)
; protected: void __thiscall CUserSetting::ReleaseMutex(void)
?ReleaseMutex@CUserSetting@@IAEXXZ
; protected: void __thiscall CUserSetting::SerializeField(unsigned short const *,struct WDS_DATA *)
?SerializeField@CUserSetting@@IAEXPBGPAUWDS_DATA@@@Z ; has WINAPI (@8)
; protected: void __thiscall CStringUserSetting::SerializeString(unsigned short const *)
?SerializeString@CStringUserSetting@@IAEXPBG@Z ; has WINAPI (@4)
; protected: void __thiscall CUInt32UserSetting::SerializeUInt32(unsigned int)
?SerializeUInt32@CUInt32UserSetting@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall CUInt64UserSetting::SerializeUInt64(unsigned __int64)
?SerializeUInt64@CUInt64UserSetting@@IAEX_K@Z ; has WINAPI (@8)
; public: void __thiscall CUserSetting::SetModuleId(void *)
?SetModuleId@CUserSetting@@QAEXPAX@Z ; has WINAPI (@4)
; protected: long __thiscall CStringUserSetting::Simple_get_String(unsigned short **)
?Simple_get_String@CStringUserSetting@@IAEJPAPAG@Z ; has WINAPI (@4)
; protected: long __thiscall CUInt32UserSetting::Simple_get_UInt32(unsigned int *)
?Simple_get_UInt32@CUInt32UserSetting@@IAEJPAI@Z ; has WINAPI (@4)
; protected: long __thiscall CUInt64UserSetting::Simple_get_UInt64(unsigned __int64 *)
?Simple_get_UInt64@CUInt64UserSetting@@IAEJPA_K@Z ; has WINAPI (@4)
; protected: long __thiscall CStringUserSetting::Simple_set_String(unsigned short const *)
?Simple_set_String@CStringUserSetting@@IAEJPBG@Z ; has WINAPI (@4)
; protected: long __thiscall CUInt32UserSetting::Simple_set_UInt32(unsigned int)
?Simple_set_UInt32@CUInt32UserSetting@@IAEJI@Z ; has WINAPI (@4)
; protected: long __thiscall CUInt64UserSetting::Simple_set_UInt64(unsigned __int64)
?Simple_set_UInt64@CUInt64UserSetting@@IAEJ_K@Z ; has WINAPI (@8)
; public: static int __stdcall CUpgradeUserSetting::UnattendChecked(void)
?UnattendChecked@CUpgradeUserSetting@@SGHXZ
; protected: static unsigned short const *const const CUserSetting::c_stErrorName
?c_stErrorName@CUserSetting@@1QBGB DATA
; protected: static unsigned short const *const const CUserSetting::c_stMutex
?c_stMutex@CUserSetting@@1QBGB DATA
; protected: static unsigned short const *const const CUserSetting::c_stShowName
?c_stShowName@CUserSetting@@1QBGB DATA
; protected: static unsigned short const *const const CUserSetting::c_stValueName
?c_stValueName@CUserSetting@@1QBGB DATA
; public: virtual long __thiscall <class CStringUserSetting>::get_Error(long *)
?get_Error@?$CShimUserSetting@VCStringUserSetting@@@@UAEJPAJ@Z ; has WINAPI (@4)
; public: virtual long __thiscall <class CUInt32UserSetting>::get_Error(long *)
?get_Error@?$CShimUserSetting@VCUInt32UserSetting@@@@UAEJPAJ@Z ; has WINAPI (@4)
; public: virtual long __thiscall <class CUInt64UserSetting>::get_Error(long *)
?get_Error@?$CShimUserSetting@VCUInt64UserSetting@@@@UAEJPAJ@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShowFlagUserSetting::get_Error(long *)
?get_Error@CShowFlagUserSetting@@UAEJPAJ@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleStringUserSetting::get_Error(long *)
?get_Error@CSimpleStringUserSetting@@UAEJPAJ@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt32UserSetting::get_Error(long *)
?get_Error@CSimpleUInt32UserSetting@@UAEJPAJ@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt64UserSetting::get_Error(long *)
?get_Error@CSimpleUInt64UserSetting@@UAEJPAJ@Z ; has WINAPI (@4)
; private: virtual unsigned short *__thiscall CComputerNameSetting::get_Name(void)
?get_Name@CComputerNameSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CDUUIProgressSetting::get_Name(void)
?get_Name@CDUUIProgressSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CDUUIWelcomeSetting::get_Name(void)
?get_Name@CDUUIWelcomeSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CDiskPartFileSystemUserSetting::get_Name(void)
?get_Name@CDiskPartFileSystemUserSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CDiskPartFormatUserSetting::get_Name(void)
?get_Name@CDiskPartFormatUserSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CDiskPartUserSetting::get_Name(void)
?get_Name@CDiskPartUserSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CEulaSetting::get_Name(void)
?get_Name@CEulaSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CIBSUIImageSelectionSetting::get_Name(void)
?get_Name@CIBSUIImageSelectionSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CKeyboardSetting::get_Name(void)
?get_Name@CKeyboardSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall COOBEUIFinishSetting::get_Name(void)
?get_Name@COOBEUIFinishSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall COOBEUIWelcomeSetting::get_Name(void)
?get_Name@COOBEUIWelcomeSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CProductKeyUserSetting::get_Name(void)
?get_Name@CProductKeyUserSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CSetupUISummarySetting::get_Name(void)
?get_Name@CSetupUISummarySetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CSetupUIWelcomeSetting::get_Name(void)
?get_Name@CSetupUIWelcomeSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CTimezoneSetting::get_Name(void)
?get_Name@CTimezoneSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CUpgStoreUserSetting::get_Name(void)
?get_Name@CUpgStoreUserSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CUpgradeUserSetting::get_Name(void)
?get_Name@CUpgradeUserSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CWDSUIImageSelectionSetting::get_Name(void)
?get_Name@CWDSUIImageSelectionSetting@@EAEPAGXZ
; private: virtual unsigned short *__thiscall CWDSUIWelcomeSetting::get_Name(void)
?get_Name@CWDSUIWelcomeSetting@@EAEPAGXZ
; public: virtual long __thiscall <class CStringUserSetting>::get_Show(int *)
?get_Show@?$CShimUserSetting@VCStringUserSetting@@@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall <class CUInt32UserSetting>::get_Show(int *)
?get_Show@?$CShimUserSetting@VCUInt32UserSetting@@@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall <class CUInt64UserSetting>::get_Show(int *)
?get_Show@?$CShimUserSetting@VCUInt64UserSetting@@@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CComputerNameSetting::get_Show(int *)
?get_Show@CComputerNameSetting@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CDiskPartUserSetting::get_Show(int *)
?get_Show@CDiskPartUserSetting@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShowFlagUserSetting::get_Show(int *)
?get_Show@CShowFlagUserSetting@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleStringUserSetting::get_Show(int *)
?get_Show@CSimpleStringUserSetting@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt32UserSetting::get_Show(int *)
?get_Show@CSimpleUInt32UserSetting@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt64UserSetting::get_Show(int *)
?get_Show@CSimpleUInt64UserSetting@@UAEJPAH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShimStringUserSetting::get_String(unsigned short **)
?get_String@CShimStringUserSetting@@UAEJPAPAG@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleStringUserSetting::get_String(unsigned short **)
?get_String@CSimpleStringUserSetting@@UAEJPAPAG@Z ; has WINAPI (@4)
; public: virtual long __thiscall CDiskPartUserSetting::get_UInt32(unsigned int *)
?get_UInt32@CDiskPartUserSetting@@UAEJPAI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShimUInt32UserSetting::get_UInt32(unsigned int *)
?get_UInt32@CShimUInt32UserSetting@@UAEJPAI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt32UserSetting::get_UInt32(unsigned int *)
?get_UInt32@CSimpleUInt32UserSetting@@UAEJPAI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShimUInt64UserSetting::get_UInt64(unsigned __int64 *)
?get_UInt64@CShimUInt64UserSetting@@UAEJPA_K@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt64UserSetting::get_UInt64(unsigned __int64 *)
?get_UInt64@CSimpleUInt64UserSetting@@UAEJPA_K@Z ; has WINAPI (@4)
; private: virtual int __thiscall CComputerNameSetting::get_ValidateID(void)
?get_ValidateID@CComputerNameSetting@@EAEHXZ
; private: virtual int __thiscall CDiskPartUserSetting::get_ValidateID(void)
?get_ValidateID@CDiskPartUserSetting@@EAEHXZ
; private: virtual int __thiscall CProductKeyUserSetting::get_ValidateID(void)
?get_ValidateID@CProductKeyUserSetting@@EAEHXZ
; public: long __thiscall CUserSetting::set_Error(long)
?set_Error@CUserSetting@@QAEJJ@Z ; has WINAPI (@4)
; public: long __thiscall CUserSetting::set_Show(int)
?set_Show@CUserSetting@@QAEJH@Z ; has WINAPI (@4)
; public: virtual long __thiscall CComputerNameSetting::set_String(unsigned short const *)
?set_String@CComputerNameSetting@@UAEJPBG@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShimStringUserSetting::set_String(unsigned short const *)
?set_String@CShimStringUserSetting@@UAEJPBG@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleStringUserSetting::set_String(unsigned short const *)
?set_String@CSimpleStringUserSetting@@UAEJPBG@Z ; has WINAPI (@4)
; public: virtual long __thiscall CDiskPartUserSetting::set_UInt32(unsigned int)
?set_UInt32@CDiskPartUserSetting@@UAEJI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShimUInt32UserSetting::set_UInt32(unsigned int)
?set_UInt32@CShimUInt32UserSetting@@UAEJI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CSimpleUInt32UserSetting::set_UInt32(unsigned int)
?set_UInt32@CSimpleUInt32UserSetting@@UAEJI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CUpgradeUserSetting::set_UInt32(unsigned int)
?set_UInt32@CUpgradeUserSetting@@UAEJI@Z ; has WINAPI (@4)
; public: virtual long __thiscall CShimUInt64UserSetting::set_UInt64(unsigned __int64)
?set_UInt64@CShimUInt64UserSetting@@UAEJ_K@Z ; has WINAPI (@8)
; public: virtual long __thiscall CSimpleUInt64UserSetting::set_UInt64(unsigned __int64)
?set_UInt64@CSimpleUInt64UserSetting@@UAEJ_K@Z ; has WINAPI (@8)
CallbackGetArgumentInt32@12
CallbackGetArgumentString@12
CallbackGetArgumentUInt64@12
IsCrossArchitectureInstall@8
PublishMessage
SignalSetupComplianceBlock@8
WdsCollectionAddString@12
WdsCollectionAddUInt32@12
WdsCollectionAddUInt64@16
WdsPickTempDriveBasedOnInstallDrive@20
WdsValidateInstallDrive@20
