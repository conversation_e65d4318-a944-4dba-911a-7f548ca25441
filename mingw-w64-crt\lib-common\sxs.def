; 
; Exports of file sxs.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY sxs.dll
EXPORTS
SxsFindClrClassInformation
SxsFindClrSurrogateInformation
SxsLookupClrGuid
SxsRunDllInstallAssembly
SxsRunDllInstallAssemblyW
SxspGenerateManifestPathOnAssemblyIdentity
SxspGeneratePolicyPathOnAssemblyIdentity
SxspRunDllDeleteDirectory
SxspRunDllDeleteDirectoryW
CreateAssemblyCache
CreateAssemblyNameObject
DllInstall
SxsBeginAssemblyInstall
SxsEndAssemblyInstall
SxsGenerateActivationContext
SxsInstallW
SxsOleAut32MapConfiguredClsidToReferenceClsid
SxsOleAut32MapIIDOrCLSIDToTypeLibrary
SxsOleAut32MapIIDToProxyStubCLSID
S<PERSON><PERSON><PERSON><PERSON>Aut32MapIIDToTLBPath
SxsOleAut32MapReferenceClsidToConfiguredClsid
SxsOleAut32RedirectTypeLibrary
SxsProbeAssemblyInstallation
SxsProtectionGatherEntriesW
SxsProtectionNotifyW
SxsProtectionPerformScanNow
SxsProtectionUserLogoffEvent
SxsProtectionUserLogonEvent
SxsQueryManifestInformation
SxsUninstallW
