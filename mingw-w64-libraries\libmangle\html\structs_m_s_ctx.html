<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: sMSCtx Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>sMSCtx Struct Reference</h1><!-- doxytag: class="sMSCtx" -->
<p><code>#include &lt;<a class="el" href="m__ms_8h_source.html">m_ms.h</a>&gt;</code></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a2f5063c35143e68593acf2f1d718cfeb">gc</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#ac0a073d6988c2278ef48f9d159383d84">name</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a6e5fc61ecefe939aea462a75a2ba1332">end</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a26e2b2ad1f83c22f21581f0fd474fc21">pos</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a78565b455c2442fb61bf1bcce3af88e4">err</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a6bc7b52416f139ba855c13b30621c59d">fExplicitTemplateParams</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#af7821943d90885f933ff5ab411b339a3">fGetTemplateArgumentList</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_cached.html">sCached</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a5fd6ba39ed9dde4dbdc7c8ac632955f2">pZNameList</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_cached.html">sCached</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#a106ed398e4438095320072ffea744e3b">pTemplateArgList</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_cached.html">sCached</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html#ac4b8422234e32c0045fa7f3f09dd0412">pArgList</a></td></tr>
</table>
<hr/><h2>Field Documentation</h2>
<a class="anchor" id="a6e5fc61ecefe939aea462a75a2ba1332"></a><!-- doxytag: member="sMSCtx::end" ref="a6e5fc61ecefe939aea462a75a2ba1332" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structs_m_s_ctx.html#a6e5fc61ecefe939aea462a75a2ba1332">sMSCtx::end</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Last character in the export name. </p>

</div>
</div>
<a class="anchor" id="a78565b455c2442fb61bf1bcce3af88e4"></a><!-- doxytag: member="sMSCtx::err" ref="a78565b455c2442fb61bf1bcce3af88e4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="structs_m_s_ctx.html#a78565b455c2442fb61bf1bcce3af88e4">sMSCtx::err</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Error codes. Zero indicates success. </p>

</div>
</div>
<a class="anchor" id="a6bc7b52416f139ba855c13b30621c59d"></a><!-- doxytag: member="sMSCtx::fExplicitTemplateParams" ref="a6bc7b52416f139ba855c13b30621c59d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="structs_m_s_ctx.html#a6bc7b52416f139ba855c13b30621c59d">sMSCtx::fExplicitTemplateParams</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Indicates that explicit template parameters are used. </p>

</div>
</div>
<a class="anchor" id="af7821943d90885f933ff5ab411b339a3"></a><!-- doxytag: member="sMSCtx::fGetTemplateArgumentList" ref="af7821943d90885f933ff5ab411b339a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="structs_m_s_ctx.html#af7821943d90885f933ff5ab411b339a3">sMSCtx::fGetTemplateArgumentList</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Indicates that the template argument list should be used. </p>

</div>
</div>
<a class="anchor" id="a2f5063c35143e68593acf2f1d718cfeb"></a><!-- doxytag: member="sMSCtx::gc" ref="a2f5063c35143e68593acf2f1d718cfeb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a>* <a class="el" href="structs_m_s_ctx.html#a2f5063c35143e68593acf2f1d718cfeb">sMSCtx::gc</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">

</div>
</div>
<a class="anchor" id="ac0a073d6988c2278ef48f9d159383d84"></a><!-- doxytag: member="sMSCtx::name" ref="ac0a073d6988c2278ef48f9d159383d84" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structs_m_s_ctx.html#ac0a073d6988c2278ef48f9d159383d84">sMSCtx::name</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>MSVC export name. </p>

</div>
</div>
<a class="anchor" id="ac4b8422234e32c0045fa7f3f09dd0412"></a><!-- doxytag: member="sMSCtx::pArgList" ref="ac4b8422234e32c0045fa7f3f09dd0412" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_cached.html">sCached</a>* <a class="el" href="structs_m_s_ctx.html#ac4b8422234e32c0045fa7f3f09dd0412">sMSCtx::pArgList</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>z-buffer if decoded arguments. </p>

</div>
</div>
<a class="anchor" id="a26e2b2ad1f83c22f21581f0fd474fc21"></a><!-- doxytag: member="sMSCtx::pos" ref="a26e2b2ad1f83c22f21581f0fd474fc21" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="structs_m_s_ctx.html#a26e2b2ad1f83c22f21581f0fd474fc21">sMSCtx::pos</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Export name processing position marker. </p>

</div>
</div>
<a class="anchor" id="a106ed398e4438095320072ffea744e3b"></a><!-- doxytag: member="sMSCtx::pTemplateArgList" ref="a106ed398e4438095320072ffea744e3b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_cached.html">sCached</a>* <a class="el" href="structs_m_s_ctx.html#a106ed398e4438095320072ffea744e3b">sMSCtx::pTemplateArgList</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>z-buffer of decoded template arguments. </p>

</div>
</div>
<a class="anchor" id="a5fd6ba39ed9dde4dbdc7c8ac632955f2"></a><!-- doxytag: member="sMSCtx::pZNameList" ref="a5fd6ba39ed9dde4dbdc7c8ac632955f2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_cached.html">sCached</a>* <a class="el" href="structs_m_s_ctx.html#a5fd6ba39ed9dde4dbdc7c8ac632955f2">sMSCtx::pZNameList</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>z-buffer of decoded names. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="m__ms_8h_source.html">m_ms.h</a></li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
