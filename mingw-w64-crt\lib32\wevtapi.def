;
; Definition file of wevtapi.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "wevtapi.dll"
EXPORTS
EvtIntSysprepCleanup@0
EvtSetObjectArrayProperty@20
EvtArchiveExportedLog@16
EvtCancel@4
EvtClearLog@16
EvtClose@4
EvtCreateBookmark@4
EvtCreateRenderContext@12
EvtExportLog@20
EvtFormatMessage@36
EvtGetChannelConfigProperty@24
EvtGetEventInfo@20
EvtGetEventMetadataProperty@24
EvtGetExtendedStatus@12
EvtGetLogInfo@20
EvtGetObjectArrayProperty@28
EvtGetObjectArraySize@8
EvtGetPublisherMetadataProperty@24
EvtGetQueryInfo@20
EvtIntAssertConfig@12
EvtIntCreateLocal<PERSON>og<PERSON>le@8
EvtInt<PERSON><PERSON><PERSON>lass<PERSON>LogDisplayName@28
EvtIntRenderResourceEventTemplate@0
EvtIntReportAuthzEventAndSourceAsync@44
EvtIntReportEventAndSourceAsync@44
EvtIntRetractConfig@12
EvtIntWriteXmlEventToLocalLogfile@12
EvtNext@24
EvtNextChannelPath@16
EvtNextEventMetadata@8
EvtNextPublisherId@16
EvtOpenChannelConfig@12
EvtOpenChannelEnum@8
EvtOpenEventMetadataEnum@8
EvtOpenLog@12
EvtOpenPublisherEnum@8
EvtOpenPublisherMetadata@20
EvtOpenSession@16
EvtQuery@16
EvtRender@28
EvtSaveChannelConfig@8
EvtSeek@24
EvtSetChannelConfigProperty@16
EvtSubscribe@32
EvtUpdateBookmark@8
