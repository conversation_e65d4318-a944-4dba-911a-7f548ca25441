LIBRARY api-ms-win-core-processthreads-l1-1-2

EXPORTS

CreateProcessA
CreateProcessAsUserW
CreateProcessW
CreateThread
ExitProcess
ExitThread
FlushInstructionCache
FlushProcessWriteBuffers
GetCurrentProcess
GetCurrentProcessId
GetCurrentProcessorNumber
GetCurrentProcessorNumberEx
GetCurrentThread
GetCurrentThreadId
GetCurrentThreadStackLimits
GetExitCodeProcess
GetExitCodeThread
GetPriorityClass
GetProcessId
GetProcessMitigationPolicy
GetProcessPriorityBoost
GetProcessTimes
GetStartupInfoW
GetSystemTimes
GetThreadContext
GetThreadId
GetThreadIdealProcessorEx
GetThreadPriority
GetThreadPriorityBoost
GetThreadTimes
IsProcessorFeaturePresent
OpenProcess
OpenProcessToken
OpenThread
OpenThreadToken
ProcessIdToSessionId
QueueUserAPC
ResumeThread
SetPriorityClass
SetProcessMitigationPolicy
SetProcessPriorityBoost
SetThreadContext
SetThreadIdealProcessorEx
SetThreadInformation
SetThreadPriority
SetThreadPriorityBoost
SetThreadStackGuarantee
SetThreadToken
SuspendThread
SwitchToThread
TerminateProcess
TerminateThread
TlsAlloc
TlsFree
TlsGetValue
TlsSetValue
