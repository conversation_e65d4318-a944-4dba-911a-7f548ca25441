;
; Definition file of logoncli.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "logoncli.dll"
EXPORTS
AuthzrExtAccessCheck
AuthzrExtFreeContext
AuthzrExtFreeResourceManager
AuthzrExtGetInformationFromContext
AuthzrExtInitializeCompoundContext
AuthzrExtInitializeContextFromSid
AuthzrExtInitializeRemoteResourceManager
AuthzrExtModifyClaims
DsAddressToSiteNamesA
DsAddressToSiteNamesExA
DsAddressToSiteNamesExW
DsAddressToSiteNamesW
DsDeregisterDnsHostRecordsA
DsDeregisterDnsHostRecordsW
DsEnumerateDomainTrustsA
DsEnumerateDomainTrustsW
DsGetDcCloseW
DsGetDcNameA
DsGetDcNameW
DsGet<PERSON>ithAccountA
DsGetDcNameWithAccountW
DsGetDcNextA
DsGetDcNextW
DsGetDcOpenA
DsGetDcOpenW
DsGetDcSiteCoverageA
DsGetDcSiteCoverageW
DsGetForestTrustInformationW
DsGetSiteNameA
DsGetSiteNameW
DsMergeForestTrustInformationW
DsValidateSubnetNameA
DsValidateSubnetNameW
I_DsUpdateReadOnlyServerDnsRecords
I_NetAccountDeltas
I_NetAccountSync
I_NetChainSetClientAttributes
I_NetChainSetClientAttributes2
I_NetDatabaseDeltas
I_NetDatabaseRedo
I_NetDatabaseSync
I_NetDatabaseSync2
I_NetGetDCList
I_NetGetForestTrustInformation
I_NetLogonControl
I_NetLogonControl2
I_NetLogonGetCapabilities
I_NetLogonGetDomainInfo
I_NetLogonSamLogoff
I_NetLogonSamLogon
I_NetLogonSamLogonEx
I_NetLogonSamLogonWithFlags
I_NetLogonSendToSam
I_NetLogonUasLogoff
I_NetLogonUasLogon
I_NetServerAuthenticate
I_NetServerAuthenticate2
I_NetServerAuthenticate3
I_NetServerGetTrustInfo
I_NetServerPasswordGet
I_NetServerPasswordSet
I_NetServerPasswordSet2
I_NetServerReqChallenge
I_NetServerTrustPasswordsGet
I_NetlogonComputeClientDigest
I_NetlogonComputeClientSignature
I_NetlogonComputeServerDigest
I_NetlogonComputeServerSignature
I_NetlogonGetTrustRid
I_RpcExtInitializeExtensionPoint
NetAddServiceAccount
NetEnumerateServiceAccounts
NetEnumerateTrustedDomains
NetGetAnyDCName
NetGetDCName
NetIsServiceAccount
NetLogonGetTimeServiceParentDomain
NetLogonSetServiceBits
NetQueryServiceAccount
NetRemoveServiceAccount
NlBindingAddServerToCache
NlBindingRemoveServerFromCache
NlBindingSetAuthInfo
NlSetDsIsCloningPDC
