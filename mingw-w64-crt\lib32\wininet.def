; Which header declares the functions not in wininet?
LIBRARY WININET.DLL
EXPORTS
DispatchAPICall@16
AppCacheCheckManifest@32
AppCache<PERSON>loseHandle@4
AppCacheCreateAndCommitFile@20
AppCacheDeleteGroup@4
AppCacheDeleteIEGroup@4
AppCache<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@8
AppCacheFinalize@16
AppCache<PERSON>reeDownloadList@4
AppCacheFreeGroupList@4
AppCacheFreeIESpace@8
AppCacheFreeSpace@8
AppCacheGetDownloadList@8
AppCacheGetFallbackUrl@12
AppCacheGetGroupList@4
AppCacheGetIEGroupList@4
AppCacheGetInfo@8
AppCacheGetManifestUrl@8
AppCacheLookup@12
CommitUrlCacheEntryA@44
CommitUrlCacheEntryBinaryBlob@32
CommitUrlCacheEntryW@44
CreateMD5SSOHash@16
CreateUrlCacheContainerA@32
CreateUrlCache<PERSON><PERSON>r<PERSON>@32
Create<PERSON>rl<PERSON>ache<PERSON>ntry<PERSON>@20
CreateUrlCacheE<PERSON>ryExW@24
CreateUrl<PERSON>ache<PERSON><PERSON>ryW@20
CreateUrlCacheGroup@8
DeleteIE3Cache@16
DeleteUrlCacheContainerA@8
DeleteUrlCacheContainerW@8
DeleteUrlCacheEntry@4
DeleteUrlCacheEntryA@4
DeleteUrlCacheEntryW@4
DeleteUrlCacheGroup@16
DeleteWpadCacheForNetworks@4
DetectAutoProxyUrl@12
DoConnectoidsExist@0
ExportCookieFileA@8
ExportCookieFileW@8
FindCloseUrlCache@4
FindFirstUrlCacheContainerA@16
FindFirstUrlCacheContainerW@16
FindFirstUrlCacheEntryA@12
FindFirstUrlCacheEntryExA@40
FindFirstUrlCacheEntryExW@40
FindFirstUrlCacheEntryW@12
FindFirstUrlCacheGroup@24
FindNextUrlCacheContainerA@12
FindNextUrlCacheContainerW@12
FindNextUrlCacheEntryA@12
FindNextUrlCacheEntryExA@24
FindNextUrlCacheEntryExW@24
FindNextUrlCacheEntryW@12
FindNextUrlCacheGroup@12
FindP3PPolicySymbol@4
ForceNexusLookup@0
ForceNexusLookupExW@20
FreeP3PObject@4
FreeUrlCacheSpaceA@12
FreeUrlCacheSpaceW@12
FtpCommandA@24
FtpCommandW@24
FtpCreateDirectoryA@8
FtpCreateDirectoryW@8
FtpDeleteFileA@8
FtpDeleteFileW@8
FtpFindFirstFileA@20
FtpFindFirstFileW@20
FtpGetCurrentDirectoryA@12
FtpGetCurrentDirectoryW@12
FtpGetFileA@28
FtpGetFileEx@28
FtpGetFileSize@8
FtpGetFileW@28
FtpOpenFileA@20
FtpOpenFileW@20
FtpPutFileA@20
FtpPutFileEx@20
FtpPutFileW@20
FtpRemoveDirectoryA@8
FtpRemoveDirectoryW@8
FtpRenameFileA@12
FtpRenameFileW@12
FtpSetCurrentDirectoryA@8
FtpSetCurrentDirectoryW@8
GetDiskInfoA@16
GetP3PPolicy@16
GetP3PRequestStatus@4
GetUrlCacheConfigInfoA@12
GetUrlCacheConfigInfoW@12
GetUrlCacheEntryBinaryBlob@28
GetUrlCacheEntryInfoA@12
GetUrlCacheEntryInfoExA@28
GetUrlCacheEntryInfoExW@28
GetUrlCacheEntryInfoW@12
GetUrlCacheGroupAttributeA@28
GetUrlCacheGroupAttributeW@28
GetUrlCacheHeaderData@8
GopherCreateLocatorA@28
GopherCreateLocatorW@28
GopherFindFirstFileA@24
GopherFindFirstFileW@24
GopherGetAttributeA@32
GopherGetAttributeW@32
GopherGetLocatorTypeA@8
GopherGetLocatorTypeW@8
GopherOpenFileA@20
GopherOpenFileW@20
HttpAddRequestHeadersA@16
HttpAddRequestHeadersW@16
HttpCheckDavCompliance@20
HttpCheckDavComplianceA@20
HttpCheckDavComplianceW@20
HttpCloseDependencyHandle@4
HttpDuplicateDependencyHandle@8
HttpEndRequestA@16
HttpEndRequestW@16
HttpGetServerCredentials@12
HttpGetTunnelSocket@16
HttpIndicatePageLoadComplete@4
HttpIsHostHstsEnabled@8
HttpOpenDependencyHandle@12
HttpOpenRequestA@32
HttpOpenRequestW@32
HttpPushClose@4
HttpPushEnable@12
HttpPushWait@12
HttpQueryInfoA@20
HttpQueryInfoW@20
HttpSendRequestA@20
HttpSendRequestExA@20
HttpSendRequestExW@20
HttpSendRequestW@20
HttpWebSocketClose@16
HttpWebSocketCompleteUpgrade@8
HttpWebSocketQueryCloseStatus@20
HttpWebSocketReceive@20
HttpWebSocketSend@16
HttpWebSocketShutdown@16
ImportCookieFileA@4
ImportCookieFileW@4
IncrementUrlCacheHeaderData@8
InternetAlgIdToStringA@16
InternetAlgIdToStringW@16
InternetAttemptConnect@4
InternetAutodial@8
InternetAutodialCallback@8
InternetAutodialHangup@4
InternetCanonicalizeUrlA@16
InternetCanonicalizeUrlW@16
InternetCheckConnectionA@12
InternetCheckConnectionW@12
InternetClearAllPerSiteCookieDecisions@0
InternetCloseHandle@4
InternetCombineUrlA@20
InternetCombineUrlW@20
InternetConfirmZoneCrossing@16
InternetConfirmZoneCrossingA@16
InternetConfirmZoneCrossingW@16
InternetConnectA@32
InternetConnectW@32
InternetConvertUrlFromWireToWideChar@32
InternetCrackUrlA@16
InternetCrackUrlW@16
InternetCreateUrlA@16
InternetCreateUrlW@16
;InternetDebugGetLocalTime@8
InternetDial@20
InternetDialA@20
InternetDialW@20
InternetEnumPerSiteCookieDecisionA@16
InternetEnumPerSiteCookieDecisionW@16
InternetErrorDlg@20
InternetFindNextFileA@8
InternetFindNextFileW@8
InternetFortezzaCommand@12
InternetFreeCookies@8
InternetFreeProxyInfoList@4
InternetGetCertByURL@12
InternetGetCertByURLA@12
InternetGetConnectedState@8
InternetGetConnectedStateEx@16
InternetGetConnectedStateExA@16
InternetGetConnectedStateExW@16
InternetGetCookieA@16
InternetGetCookieEx2@20
InternetGetCookieExA@24
InternetGetCookieExW@24
InternetGetCookieW@16
InternetGetLastResponseInfoA@12
InternetGetLastResponseInfoW@12
InternetGetPerSiteCookieDecisionA@8
InternetGetPerSiteCookieDecisionW@8
InternetGetProxyForUrl@12
InternetGetSecurityInfoByURL@12
InternetGetSecurityInfoByURLA@12
InternetGetSecurityInfoByURLW@12
InternetGoOnline@12
InternetGoOnlineA@12
InternetGoOnlineW@12
InternetHangUp@8
InternetInitializeAutoProxyDll@4
InternetLockRequestFile@8
InternetOpenA@20
InternetOpenUrlA@24
InternetOpenUrlW@24
InternetOpenW@20
InternetQueryDataAvailable@16
InternetQueryFortezzaStatus@8
InternetQueryOptionA@16
InternetQueryOptionW@16
InternetReadFile@16
InternetReadFileExA@16
InternetReadFileExW@16
InternetSecurityProtocolToStringA@16
InternetSecurityProtocolToStringW@16
InternetSetCookieA@12
InternetSetCookieEx2@20
InternetSetCookieExA@20
InternetSetCookieExW@20
InternetSetCookieW@12
InternetSetDialState@12
InternetSetDialStateA@12
InternetSetDialStateW@12
InternetSetFilePointer@20
InternetSetOptionA@16
InternetSetOptionExA@20
InternetSetOptionExW@20
InternetSetOptionW@16
InternetSetPerSiteCookieDecisionA@8
InternetSetPerSiteCookieDecisionW@8
InternetSetStatusCallback@8
InternetSetStatusCallbackA@8
InternetSetStatusCallbackW@8
InternetShowSecurityInfoByURL@8
InternetShowSecurityInfoByURLA@8
InternetShowSecurityInfoByURLW@8
InternetTimeFromSystemTime@16
InternetTimeFromSystemTimeA@16
InternetTimeFromSystemTimeW@16
InternetTimeToSystemTime@12
InternetTimeToSystemTimeA@12
InternetTimeToSystemTimeW@12
InternetUnlockRequestFile@4
InternetWriteFile@16
InternetWriteFileExA@16
InternetWriteFileExW@16
IsDomainLegalCookieDomainA@8
IsDomainLegalCookieDomainW@8
IsHostInProxyBypassList@12
IsProfilesEnabled@0
IsUrlCacheEntryExpiredA@12
IsUrlCacheEntryExpiredW@12
LoadUrlCacheContent@0
MapResourceToPolicy@16
ParseX509EncodedCertificateForListBoxEntry@16
PerformOperationOverUrlCacheA@40
PrivacyGetZonePreferenceW@20
PrivacySetZonePreferenceW@16
ReadUrlCacheEntryStream@20
ReadUrlCacheEntryStreamEx@20
RegisterUrlCacheNotification@24
ResumeSuspendedDownload@8
RetrieveUrlCacheEntryFileA@16
RetrieveUrlCacheEntryFileW@16
RetrieveUrlCacheEntryStreamA@20
RetrieveUrlCacheEntryStreamW@20
RunOnceUrlCache@16
SetUrlCacheConfigInfoA@8
SetUrlCacheConfigInfoW@8
SetUrlCacheEntryGroup@28
SetUrlCacheEntryGroupA@28
SetUrlCacheEntryGroupW@28
SetUrlCacheEntryInfoA@12
SetUrlCacheEntryInfoW@12
SetUrlCacheGroupAttributeA@24
SetUrlCacheGroupAttributeW@24
SetUrlCacheHeaderData@8
ShowCertificate@8
ShowClientAuthCerts@4
ShowSecurityInfo@8
ShowX509EncodedCertificate@12
UnlockUrlCacheEntryFile@8
UnlockUrlCacheEntryFileA@8
UnlockUrlCacheEntryFileW@8
UnlockUrlCacheEntryStream@8
UpdateUrlCacheContentPath@4
UrlCacheCheckEntriesExist@12
UrlCacheCloseEntryHandle@4
UrlCacheContainerSetEntryMaximumAge@8
UrlCacheCreateContainer@24
UrlCacheFindFirstEntry@28
UrlCacheFindNextEntry@8
UrlCacheFreeEntryInfo@4
UrlCacheFreeGlobalSpace@12
UrlCacheGetContentPaths@8
UrlCacheGetEntryInfo@12
UrlCacheGetGlobalCacheSize@12
UrlCacheGetGlobalLimit@8
UrlCacheReadEntryStream@24
UrlCacheReloadSettings@0
UrlCacheRetrieveEntryFile@16
UrlCacheRetrieveEntryStream@20
UrlCacheServer@0
UrlCacheSetGlobalLimit@12
UrlCacheUpdateEntryExtraData@16
UrlZonesDetach@0
_GetFileExtensionFromUrl@16
