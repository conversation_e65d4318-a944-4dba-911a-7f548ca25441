;
; Definition file of wer.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "wer.dll"
EXPORTS
WerSysprepCleanup@0
WerSysprepGeneralize@0
WerSysprepSpecialize@0
WerUnattendedSetup@0
WerpAddAppCompatData@12
WerpAddFile@24
WerpAddMemoryBlock@12
WerpAddRegisteredDataToReport@8
WerpAddSecondaryParameter@12
WerpAddTextToReport@12
WerpArchiveReport@20
WerpCancelResponseDownload@4
WerpCancelUpload@4
WerpCloseStore@4
WerpCreateMachineStore@0
WerpDeleteReport@8
WerpDestroyWerString@4
WerpDownloadResponse@28
WerpDownloadResponseTemplate@12
WerpEnumerateStoreNext@8
WerpEnumerateStoreStart@4
WerpExtractReportFiles@12
WerpGetBucketId@8
WerpGetDynamic<PERSON>meter@16
WerpGetEventType@8
WerpGetFileByIndex@24
WerpGetFilePathByIndex@12
WerpGetNumFiles@8
WerpGetNumSecParams@8
WerpGetNumSigParams@8
WerpGetReportFinalConsent@8
WerpGetReportFlags@8
WerpGetReportInformation@8
WerpGetReportTime@8
WerpGetReportType@8
WerpGetResponseId@12
WerpGetResponseUrl@8
WerpGetSecParamByIndex@16
WerpGetSigParamByIndex@16
WerpGetStoreLocation@12
WerpGetStoreType@8
WerpGetTextFromReport@12
WerpGetUIParamByIndex@12
WerpGetUploadTime@8
WerpGetWerStringData@4
WerpIsTransportAvailable@0
WerpLoadReport@16
WerpOpenMachineArchive@8
WerpOpenMachineQueue@8
WerpOpenUserArchive@8
WerpReportCancel@4
WerpRestartApplication@20
WerpSetDynamicParameter@16
WerpSetEventName@8
WerpSetReportFlags@8
WerpSetReportInformation@8
WerpSetReportTime@8
WerpSetReportUploadContextToken@8
WerpShowNXNotification@4
WerpShowSecondLevelConsent@12
WerpShowUpsellUI@8
WerpSubmitReportFromStore@28
WerpSvcReportFromMachineQueue@8
WerAddExcludedApplication@8
WerRemoveExcludedApplication@8
WerReportAddDump@28
WerReportAddFile@16
WerReportCloseHandle@4
WerReportCreate@16
WerReportSetParameter@16
WerReportSetUIOption@12
WerReportSubmit@16
WerpGetReportConsent@12
WerpIsDisabled@8
WerpOpenUserQueue@8
WerpPromtUser@16
WerpSetCallBack@12
