;
; Definition file of WinSCard.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WinSCard.dll"
EXPORTS
ClassInstall32@12
SCardAccessNewReaderEvent@0
SCardReleaseAllEvents@0
SCardReleaseNewReaderEvent@0
SCardAccessStartedEvent@0
SCardAddReaderToGroupA@12
SCardAddReaderToGroupW@12
SCardBeginTransaction@4
SCardCancel@4
SCardConnectA@24
SCardConnectW@24
SCardControl@28
SCardDisconnect@8
SCardEndTransaction@8
SCardEstablishContext@16
SCardForgetCardTypeA@8
SCardForgetCardTypeW@8
SCardForgetReaderA@8
SCardForgetReaderGroupA@8
SCardForgetReaderGroupW@8
SCardForgetReaderW@8
SCardFreeMemory@8
SCardGetAttrib@16
SCardGetCardTypeProviderNameA@20
SCardGetCardType<PERSON>rovider<PERSON><PERSON><PERSON>@20
SCardGetProviderIdA@12
SCardGetProviderIdW@12
SCardGetStatusChangeA@16
SCardGetStatusChangeW@16
SCardGetTransmitCount@8
SCardIntroduceCardTypeA@32
SCardIntroduceCardTypeW@32
SCardIntroduceReaderA@12
SCardIntroduceReaderGroupA@8
SCardIntroduceReaderGroupW@8
SCardIntroduceReaderW@12
SCardIsValidContext@4
SCardListCardsA@24
SCardListCardsW@24
SCardListInterfacesA@16
SCardListInterfacesW@16
SCardListReaderGroupsA@12
SCardListReaderGroupsW@12
SCardListReadersA@16
SCardListReadersW@16
SCardLocateCardsA@16
SCardLocateCardsByATRA@20
SCardLocateCardsByATRW@20
SCardLocateCardsW@16
SCardReadCacheA@24
SCardReadCacheW@24
SCardReconnect@20
SCardReleaseContext@4
SCardReleaseStartedEvent@0
SCardRemoveReaderFromGroupA@12
SCardRemoveReaderFromGroupW@12
SCardSetAttrib@16
SCardSetCardTypeProviderNameA@16
SCardSetCardTypeProviderNameW@16
SCardState@20
SCardStatusA@28
SCardStatusW@28
SCardTransmit@28
SCardWriteCacheA@24
SCardWriteCacheW@24
g_rgSCardRawPci DATA
g_rgSCardT0Pci DATA
g_rgSCardT1Pci DATA
