;
; Definition file of MPR.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "MPR.dll"
EXPORTS
DoBroadcastSystemMessage
DoCommandLinePrompt
DoPasswordDialog
DoProfileErrorDialog
ShowReconnectDialog
ShowReconnectDialogEnd
ShowReconnectDialogUI
WNetConnectionDialog2
WNetDisconnectDialog2
I_MprSaveConn
MultinetGetConnectionPerformanceA
MultinetGetConnectionPerformanceW
MultinetGetErrorTextA
MultinetGetErrorTextW
RestoreConnectionA0
WNetAddConnection2A
WNetAddConnection2W
WNetAddConnection3A
WNetAddConnection3W
WNetAddConnectionA
WNetAddConnectionW
WNetCancelConnection2A
WNetCancelConnection2W
WNetCancelConnectionA
WNetCancelConnectionW
WNetClearConnections
WNetCloseEnum
WNetConnectionDialog
WNetConnectionD<PERSON>og1A
WNetConnectionDialog1W
WNetDirectoryNotifyA
WNetDirectoryNotifyW
WNetDisconnectDialog
WNetDisconnectDialog1A
WNetDisconnectDialog1W
WNetEnumResourceA
WNetEnumResourceW
WNetFMXEditPerm
WNetFMXGetPermCaps
WNetFMXGetPermHelp
WNetFormatNetworkNameA
WNetFormatNetworkNameW
WNetGetConnection2A
WNetGetConnection2W
WNetGetConnection3A
WNetGetConnection3W
WNetGetConnectionA
WNetGetConnectionW
WNetGetDirectoryTypeA
WNetGetDirectoryTypeW
WNetGetHomeDirectoryW
WNetGetLastErrorA
WNetGetLastErrorW
WNetGetNetworkInformationA
WNetGetNetworkInformationW
WNetGetPropertyTextA
WNetGetPropertyTextW
WNetGetProviderNameA
WNetGetProviderNameW
WNetGetProviderTypeA
WNetGetProviderTypeW
WNetGetResourceInformationA
WNetGetResourceInformationW
WNetGetResourceParentA
WNetGetResourceParentW
WNetGetSearchDialog
WNetGetUniversalNameA
WNetGetUniversalNameW
WNetGetUserA
WNetGetUserW
WNetLogonNotify
WNetOpenEnumA
WNetOpenEnumW
WNetPasswordChangeNotify
WNetPropertyDialogA
WNetPropertyDialogW
WNetRestoreAllConnectionsW
WNetRestoreConnection2W
WNetRestoreConnectionW
WNetRestoreSingleConnectionW
WNetSetConnectionA
WNetSetConnectionW
WNetSetLastErrorA
WNetSetLastErrorW
WNetSupportGlobalEnum
WNetUseConnectionA
WNetUseConnectionW
