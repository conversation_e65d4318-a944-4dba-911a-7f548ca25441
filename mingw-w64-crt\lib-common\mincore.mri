CREATE libmincore.a
ADDLIB libapi-ms-win-core-com-l1-1-0.a
ADDLIB libapi-ms-win-core-com-l1-1-1.a
; FIXME libapi-ms-win-core-com-l1-1-2.a
ADDLIB libapi-ms-win-core-com-midlproxystub-l1-1-0.a
ADDLIB libapi-ms-win-core-comm-l1-1-0.a
ADDLIB libapi-ms-win-core-comm-l1-1-1.a
ADDLIB libapi-ms-win-core-comm-l1-1-2.a
ADDLIB libapi-ms-win-core-console-l1-1-0.a
ADDLIB libapi-ms-win-core-console-l1-2-0.a
; FIXME libapi-ms-win-core-console-l1-2-1.a
ADDLIB libapi-ms-win-core-console-l2-1-0.a
ADDLIB libapi-ms-win-core-console-l2-2-0.a
ADDLIB libapi-ms-win-core-console-l3-2-0.a
ADDLIB libapi-ms-win-core-datetime-l1-1-0.a
ADDLIB libapi-ms-win-core-datetime-l1-1-1.a
ADDLIB libapi-ms-win-core-datetime-l1-1-2.a
ADDLIB libapi-ms-win-core-debug-l1-1-0.a
ADDLIB libapi-ms-win-core-debug-l1-1-1.a
; FIXME libapi-ms-win-core-debug-l1-1-2.a
; FIXME libapi-ms-win-core-delayload-l1-1-0.a
ADDLIB libapi-ms-win-core-delayload-l1-1-1.a
ADDLIB libapi-ms-win-core-errorhandling-l1-1-0.a
ADDLIB libapi-ms-win-core-errorhandling-l1-1-1.a
; FIXME libapi-ms-win-core-errorhandling-l1-1-2.a
ADDLIB libapi-ms-win-core-errorhandling-l1-1-3.a
; FIXME libapi-ms-win-core-fibers-l1-1-0.a
ADDLIB libapi-ms-win-core-fibers-l1-1-1.a
ADDLIB libapi-ms-win-core-file-ansi-l2-1-0.a
ADDLIB libapi-ms-win-core-file-l1-1-0.a
; FIXME libapi-ms-win-core-file-l1-2-0.a
ADDLIB libapi-ms-win-core-file-l1-2-1.a
ADDLIB libapi-ms-win-core-file-l1-2-2.a
; FIXME libapi-ms-win-core-file-l1-2-3.a
ADDLIB libapi-ms-win-core-file-l2-1-0.a
ADDLIB libapi-ms-win-core-file-l2-1-1.a
ADDLIB libapi-ms-win-core-file-l2-1-2.a
; FIXME libapi-ms-win-core-file-l2-1-3.a
ADDLIB libapi-ms-win-core-firmware-l1-1-0.a
ADDLIB libapi-ms-win-core-handle-l1-1-0.a
; FIXME libapi-ms-win-core-heap-l1-1-0.a
ADDLIB libapi-ms-win-core-heap-l1-2-0.a
ADDLIB libapi-ms-win-core-interlocked-l1-1-0.a
ADDLIB libapi-ms-win-core-interlocked-l1-2-0.a
ADDLIB libapi-ms-win-core-io-l1-1-0.a
ADDLIB libapi-ms-win-core-io-l1-1-1.a
; FIXME libapi-ms-win-core-job-l1-1-0.a
ADDLIB libapi-ms-win-core-libraryloader-l1-2-0.a
ADDLIB libapi-ms-win-core-libraryloader-l1-2-1.a
; FIXME libapi-ms-win-core-libraryloader-l1-2-2.a
ADDLIB libapi-ms-win-core-libraryloader-l2-1-0.a
ADDLIB libapi-ms-win-core-localization-l1-2-0.a
ADDLIB libapi-ms-win-core-localization-l1-2-1.a
ADDLIB libapi-ms-win-core-localization-l1-2-2.a
; FIXME libapi-ms-win-core-localization-l1-2-3.a
ADDLIB libapi-ms-win-core-localization-l2-1-0.a
ADDLIB libapi-ms-win-core-memory-l1-1-0.a
ADDLIB libapi-ms-win-core-memory-l1-1-1.a
ADDLIB libapi-ms-win-core-memory-l1-1-2.a
ADDLIB libapi-ms-win-core-memory-l1-1-3.a
; FIXME libapi-ms-win-core-memory-l1-1-4.a
ADDLIB libapi-ms-win-core-memory-l1-1-5.a
ADDLIB libapi-ms-win-core-memory-l1-1-6.a
ADDLIB libapi-ms-win-core-memory-l1-1-7.a
ADDLIB libapi-ms-win-core-namedpipe-ansi-l1-1-0.a
ADDLIB libapi-ms-win-core-namedpipe-ansi-l1-1-1.a
ADDLIB libapi-ms-win-core-namedpipe-l1-1-0.a
ADDLIB libapi-ms-win-core-namedpipe-l1-2-1.a
ADDLIB libapi-ms-win-core-namedpipe-l1-2-2.a
ADDLIB libapi-ms-win-core-namespace-l1-1-0.a
ADDLIB libapi-ms-win-core-path-l1-1-0.a
ADDLIB libapi-ms-win-core-processenvironment-l1-1-0.a
ADDLIB libapi-ms-win-core-processenvironment-l1-2-0.a
; FIXME libapi-ms-win-core-processsnapshot-l1-1-0.a
ADDLIB libapi-ms-win-core-processthreads-l1-1-0.a
ADDLIB libapi-ms-win-core-processthreads-l1-1-1.a
ADDLIB libapi-ms-win-core-processthreads-l1-1-2.a
ADDLIB libapi-ms-win-core-processthreads-l1-1-3.a
; FIXME libapi-ms-win-core-processtopology-l1-1-0.a
ADDLIB libapi-ms-win-core-profile-l1-1-0.a
ADDLIB libapi-ms-win-core-psapi-l1-1-0.a
ADDLIB libapi-ms-win-core-psapi-ansi-l1-1-0.a
; FIXME libapi-ms-win-core-quirks-l1-1-0.a
; FIXME libapi-ms-win-core-quirks-l1-1-1.a
ADDLIB libapi-ms-win-core-realtime-l1-1-0.a
ADDLIB libapi-ms-win-core-realtime-l1-1-1.a
ADDLIB libapi-ms-win-core-realtime-l1-1-2.a
; FIXME libapi-ms-win-core-registry-l1-1-0.a
; FIXME libapi-ms-win-core-registry-l1-1-1.a
; FIXME libapi-ms-win-core-registry-l1-1-2.a
; FIXME libapi-ms-win-core-rtlsupport-l1-1-0.a
ADDLIB libapi-ms-win-core-rtlsupport-l1-2-0.a
; FIXME libapi-ms-win-core-shutdown-l1-1-0.a
; FIXME libapi-ms-win-core-shutdown-l1-1-1.a
ADDLIB libapi-ms-win-core-string-l1-1-0.a
; FIXME libapi-ms-win-core-string-l2-1-0.a
; FIXME libapi-ms-win-core-string-l2-1-1.a
ADDLIB libapi-ms-win-core-synch-l1-1-0.a
ADDLIB libapi-ms-win-core-synch-l1-2-0.a
ADDLIB libapi-ms-win-core-synch-l1-2-1.a
ADDLIB libapi-ms-win-core-sysinfo-l1-1-0.a
ADDLIB libapi-ms-win-core-sysinfo-l1-2-0.a
ADDLIB libapi-ms-win-core-sysinfo-l1-2-1.a
; FIXME libapi-ms-win-core-sysinfo-l1-2-2.a
ADDLIB libapi-ms-win-core-sysinfo-l1-2-3.a
; FIXME libapi-ms-win-core-sysinfo-l1-2-4.a
; FIXME libapi-ms-win-core-sysinfo-l1-2-5.a
; FIXME libapi-ms-win-core-systemtopology-l1-1-0.a
; FIXME libapi-ms-win-core-systemtopology-l1-1-1.a
ADDLIB libapi-ms-win-core-threadpool-l1-2-0.a
ADDLIB libapi-ms-win-core-timezone-l1-1-0.a
ADDLIB libapi-ms-win-core-timezone-l1-1-1.a
ADDLIB libapi-ms-win-core-util-l1-1-0.a
; FIXME libapi-ms-win-core-util-l1-1-1.a
ADDLIB libapi-ms-win-core-version-l1-1-0.a
ADDLIB libapi-ms-win-core-version-l1-1-1.a
; FIXME libapi-ms-win-core-winrt-error-l1-1-0.a
ADDLIB libapi-ms-win-core-winrt-error-l1-1-1.a
ADDLIB libapi-ms-win-core-winrt-l1-1-0.a
ADDLIB libapi-ms-win-core-winrt-string-l1-1-0.a
; FIXME libapi-ms-win-core-winrt-string-l1-1-1.a
ADDLIB libapi-ms-win-core-wow64-l1-1-0.a
ADDLIB libapi-ms-win-core-wow64-l1-1-1.a
; FIXME libapi-ms-win-core-wow64-l1-1-2.a
; FIXME libapi-ms-win-core-xstate-l1-1-0.a
; FIXME libapi-ms-win-core-xstate-l1-1-1.a
; FIXME libapi-ms-win-core-xstate-l1-1-2.a
; FIXME libapi-ms-win-core-xstate-l1-1-3.a
ADDLIB libapi-ms-win-core-xstate-l2-1-0.a
; FIXME libapi-ms-win-core-xstate-l2-1-1.a
ADDLIB libapi-ms-win-devices-config-l1-1-1.a
ADDLIB libapi-ms-win-devices-config-l1-1-2.a
; FIXME libapi-ms-win-devices-swdevice-l1-1-0.a
; FIXME libapi-ms-win-devices-swdevice-l1-1-1.a
ADDLIB libapi-ms-win-eventing-classicprovider-l1-1-0.a
ADDLIB libapi-ms-win-eventing-consumer-l1-1-0.a
; FIXME libapi-ms-win-eventing-consumer-l1-1-1.a
ADDLIB libapi-ms-win-eventing-controller-l1-1-0.a
ADDLIB libapi-ms-win-eventing-provider-l1-1-0.a
; FIXME libapi-ms-win-power-base-l1-1-0.a
; FIXME libapi-ms-win-power-setting-l1-1-0.a
; FIXME libapi-ms-win-power-setting-l1-1-1.a
; FIXME libapi-ms-win-security-appcontainer-l1-1-0.a
ADDLIB libapi-ms-win-security-base-l1-1-0.a
ADDLIB libapi-ms-win-security-base-l1-2-0.a
ADDLIB libapi-ms-win-security-base-l1-2-1.a
; FIXME libapi-ms-win-security-base-l1-2-2.a
; FIXME libapi-ms-win-security-credentials-l1-1-0.a
ADDLIB libapi-ms-win-security-lsalookup-l2-1-0.a
; FIXME libapi-ms-win-security-lsalookup-l2-1-1.a
ADDLIB libapi-ms-win-security-sddl-l1-1-0.a
; FIXME libapi-ms-win-service-core-l1-1-0.a
; FIXME libapi-ms-win-service-core-l1-1-1.a
; FIXME libapi-ms-win-service-core-l1-1-2.a
; FIXME libapi-ms-win-service-core-l1-1-3.a
; FIXME libapi-ms-win-service-core-l1-1-4.a
; FIXME libapi-ms-win-service-management-l1-1-0.a
; FIXME libapi-ms-win-service-management-l2-1-0.a
; FIXME libapi-ms-win-service-winsvc-l1-1-0.a
ADDLIB libauthz.a
ADDLIB libbcrypt.a
ADDLIB libcabinet.a
ADDLIB libcrypt32.a
ADDLIB libcryptbase.a
ADDLIB libcryptnet.a
; FIXME libdfscli.a
ADDLIB libdnsapi.a
; FIXME libdsparse.a
; FIXME libdsrole.a
ADDLIB libiphlpapi.a
; FIXME liblogoncli.a
ADDLIB libmpr.a
ADDLIB libmswsock.a
ADDLIB libncrypt.a
; FIXME libnetutils.a
ADDLIB liboleaut32.a
ADDLIB librpcrt4.a
; FIXME libsamcli.a
; FIXME libschedcli.a
; FIXME libsrvcli.a
ADDLIB libsspicli.a
ADDLIB libuserenv.a
ADDLIB libwebsocket.a
ADDLIB libwinhttp.a
; FIXME libwkscli.a
ADDLIB libwldap32.a
ADDLIB libws2_32.a
SAVE
END
