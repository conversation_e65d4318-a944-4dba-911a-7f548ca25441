;
; Definition file of CFGMGR32.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRAR<PERSON> "CFGMGR32.dll"
EXPORTS
CMP_GetBlockedDriverInfo
CMP_GetServerSideDeviceInstallFlags
CMP_Init_Detection
CMP_RegisterNotification
CMP_RegisterServiceNotification
CMP_Register_Notification
CMP_Report_LogOn
CMP_UnregisterNotification
CMP_WaitNoPendingInstallEvents
CMP_WaitServicesAvailable
CM_Add_Driver_PackageW
CM_Add_Driver_Package_ExW
CM_Add_Empty_Log_Conf
CM_Add_Empty_Log_Conf_Ex
CM_Add_IDA
CM_Add_IDW
CM_Add_ID_ExA
CM_Add_ID_ExW
CM_Add_Range
CM_Add_Res_Des
CM_Add_Res_Des_Ex
CM_Apply_PowerScheme
CM_Connect_MachineA
CM_Connect_MachineW
CM_Create_DevNodeA
CM_Create_DevNodeW
CM_Create_DevNode_ExA
CM_Create_DevNode_ExW
CM_Create_Range_List
CM_Delete_Class_Key
CM_Delete_Class_Key_Ex
CM_Delete_DevNode_Key
CM_Delete_DevNode_Key_Ex
CM_Delete_Device_Interface_KeyA
CM_Delete_Device_Interface_KeyW
CM_Delete_Device_Interface_Key_ExA
CM_Delete_Device_Interface_Key_ExW
CM_Delete_Driver_PackageW
CM_Delete_Driver_Package_ExW
CM_Delete_PowerScheme
CM_Delete_Range
CM_Detect_Resource_Conflict
CM_Detect_Resource_Conflict_Ex
CM_Disable_DevNode
CM_Disable_DevNode_Ex
CM_Disconnect_Machine
CM_Dup_Range_List
CM_Duplicate_PowerScheme
CM_Enable_DevNode
CM_Enable_DevNode_Ex
CM_Enumerate_Classes
CM_Enumerate_Classes_Ex
CM_Enumerate_EnumeratorsA
CM_Enumerate_EnumeratorsW
CM_Enumerate_Enumerators_ExA
CM_Enumerate_Enumerators_ExW
CM_Find_Range
CM_First_Range
CM_Free_Log_Conf
CM_Free_Log_Conf_Ex
CM_Free_Log_Conf_Handle
CM_Free_Range_List
CM_Free_Res_Des
CM_Free_Res_Des_Ex
CM_Free_Res_Des_Handle
CM_Free_Resource_Conflict_Handle
CM_Get_Child
CM_Get_Child_Ex
CM_Get_Class_Key_NameA
CM_Get_Class_Key_NameW
CM_Get_Class_Key_Name_ExA
CM_Get_Class_Key_Name_ExW
CM_Get_Class_NameA
CM_Get_Class_NameW
CM_Get_Class_Name_ExA
CM_Get_Class_Name_ExW
CM_Get_Class_PropertyW
CM_Get_Class_Property_ExW
CM_Get_Class_Property_Keys
CM_Get_Class_Property_Keys_Ex
CM_Get_Class_Registry_PropertyA
CM_Get_Class_Registry_PropertyW
CM_Get_Depth
CM_Get_Depth_Ex
CM_Get_DevNode_Custom_PropertyA
CM_Get_DevNode_Custom_PropertyW
CM_Get_DevNode_Custom_Property_ExA
CM_Get_DevNode_Custom_Property_ExW
CM_Get_DevNode_PropertyW
CM_Get_DevNode_Property_ExW
CM_Get_DevNode_Property_Keys
CM_Get_DevNode_Property_Keys_Ex
CM_Get_DevNode_Registry_PropertyA
CM_Get_DevNode_Registry_PropertyW
CM_Get_DevNode_Registry_Property_ExA
CM_Get_DevNode_Registry_Property_ExW
CM_Get_DevNode_Status
CM_Get_DevNode_Status_Ex
CM_Get_Device_IDA
CM_Get_Device_IDW
CM_Get_Device_ID_ExA
CM_Get_Device_ID_ExW
CM_Get_Device_ID_ListA
CM_Get_Device_ID_ListW
CM_Get_Device_ID_List_ExA
CM_Get_Device_ID_List_ExW
CM_Get_Device_ID_List_SizeA
CM_Get_Device_ID_List_SizeW
CM_Get_Device_ID_List_Size_ExA
CM_Get_Device_ID_List_Size_ExW
CM_Get_Device_ID_Size
CM_Get_Device_ID_Size_Ex
CM_Get_Device_Interface_AliasA
CM_Get_Device_Interface_AliasW
CM_Get_Device_Interface_Alias_ExA
CM_Get_Device_Interface_Alias_ExW
CM_Get_Device_Interface_ListA
CM_Get_Device_Interface_ListW
CM_Get_Device_Interface_List_ExA
CM_Get_Device_Interface_List_ExW
CM_Get_Device_Interface_List_SizeA
CM_Get_Device_Interface_List_SizeW
CM_Get_Device_Interface_List_Size_ExA
CM_Get_Device_Interface_List_Size_ExW
CM_Get_Device_Interface_PropertyW
CM_Get_Device_Interface_Property_ExW
CM_Get_Device_Interface_Property_KeysW
CM_Get_Device_Interface_Property_Keys_ExW
CM_Get_First_Log_Conf
CM_Get_First_Log_Conf_Ex
CM_Get_Global_State
CM_Get_Global_State_Ex
CM_Get_HW_Prof_FlagsA
CM_Get_HW_Prof_FlagsW
CM_Get_HW_Prof_Flags_ExA
CM_Get_HW_Prof_Flags_ExW
CM_Get_Hardware_Profile_InfoA
CM_Get_Hardware_Profile_InfoW
CM_Get_Hardware_Profile_Info_ExA
CM_Get_Hardware_Profile_Info_ExW
CM_Get_Log_Conf_Priority
CM_Get_Log_Conf_Priority_Ex
CM_Get_Next_Log_Conf
CM_Get_Next_Log_Conf_Ex
CM_Get_Next_Res_Des
CM_Get_Next_Res_Des_Ex
CM_Get_Parent
CM_Get_Parent_Ex
CM_Get_Res_Des_Data
CM_Get_Res_Des_Data_Ex
CM_Get_Res_Des_Data_Size
CM_Get_Res_Des_Data_Size_Ex
CM_Get_Resource_Conflict_Count
CM_Get_Resource_Conflict_DetailsA
CM_Get_Resource_Conflict_DetailsW
CM_Get_Sibling
CM_Get_Sibling_Ex
CM_Get_Version
CM_Get_Version_Ex
CM_Import_PowerScheme
CM_Install_DevNodeW
CM_Install_DevNode_ExW
CM_Intersect_Range_List
CM_Invert_Range_List
CM_Is_Dock_Station_Present
CM_Is_Dock_Station_Present_Ex
CM_Is_Version_Available
CM_Is_Version_Available_Ex
CM_Locate_DevNodeA
CM_Locate_DevNodeW
CM_Locate_DevNode_ExA
CM_Locate_DevNode_ExW
CM_MapCrToSpErr
CM_MapCrToWin32Err
CM_Merge_Range_List
CM_Modify_Res_Des
CM_Modify_Res_Des_Ex
CM_Move_DevNode
CM_Move_DevNode_Ex
CM_Next_Range
CM_Open_Class_KeyA
CM_Open_Class_KeyW
CM_Open_Class_Key_ExA
CM_Open_Class_Key_ExW
CM_Open_DevNode_Key
CM_Open_DevNode_Key_Ex
CM_Open_Device_Interface_KeyA
CM_Open_Device_Interface_KeyW
CM_Open_Device_Interface_Key_ExA
CM_Open_Device_Interface_Key_ExW
CM_Query_And_Remove_SubTreeA
CM_Query_And_Remove_SubTreeW
CM_Query_And_Remove_SubTree_ExA
CM_Query_And_Remove_SubTree_ExW
CM_Query_Arbitrator_Free_Data
CM_Query_Arbitrator_Free_Data_Ex
CM_Query_Arbitrator_Free_Size
CM_Query_Arbitrator_Free_Size_Ex
CM_Query_Remove_SubTree
CM_Query_Remove_SubTree_Ex
CM_Query_Resource_Conflict_List
CM_Reenumerate_DevNode
CM_Reenumerate_DevNode_Ex
CM_Register_Device_Driver
CM_Register_Device_Driver_Ex
CM_Register_Device_InterfaceA
CM_Register_Device_InterfaceW
CM_Register_Device_Interface_ExA
CM_Register_Device_Interface_ExW
CM_Register_Notification
CM_Remove_SubTree
CM_Remove_SubTree_Ex
CM_Request_Device_EjectA
CM_Request_Device_EjectW
CM_Request_Device_Eject_ExA
CM_Request_Device_Eject_ExW
CM_Request_Eject_PC
CM_Request_Eject_PC_Ex
CM_RestoreAll_DefaultPowerSchemes
CM_Restore_DefaultPowerScheme
CM_Run_Detection
CM_Run_Detection_Ex
CM_Set_ActiveScheme
CM_Set_Class_PropertyW
CM_Set_Class_Property_ExW
CM_Set_Class_Registry_PropertyA
CM_Set_Class_Registry_PropertyW
CM_Set_DevNode_Problem
CM_Set_DevNode_Problem_Ex
CM_Set_DevNode_PropertyW
CM_Set_DevNode_Property_ExW
CM_Set_DevNode_Registry_PropertyA
CM_Set_DevNode_Registry_PropertyW
CM_Set_DevNode_Registry_Property_ExA
CM_Set_DevNode_Registry_Property_ExW
CM_Set_Device_Interface_PropertyW
CM_Set_Device_Interface_Property_ExW
CM_Set_HW_Prof
CM_Set_HW_Prof_Ex
CM_Set_HW_Prof_FlagsA
CM_Set_HW_Prof_FlagsW
CM_Set_HW_Prof_Flags_ExA
CM_Set_HW_Prof_Flags_ExW
CM_Setup_DevNode
CM_Setup_DevNode_Ex
CM_Test_Range_Available
CM_Uninstall_DevNode
CM_Uninstall_DevNode_Ex
CM_Unregister_Device_InterfaceA
CM_Unregister_Device_InterfaceW
CM_Unregister_Device_Interface_ExA
CM_Unregister_Device_Interface_ExW
CM_Unregister_Notification
CM_Write_UserPowerKey
DevCloseObjectQuery
DevCreateObjectQuery
DevCreateObjectQueryEx
DevCreateObjectQueryFromId
DevCreateObjectQueryFromIdEx
DevCreateObjectQueryFromIds
DevCreateObjectQueryFromIdsEx
DevFindProperty
DevFreeObjectProperties
DevFreeObjects
DevGetObjectProperties
DevGetObjectPropertiesEx
DevGetObjects
DevGetObjectsEx
DevSetObjectProperties
SwDeviceClose
SwDeviceCreate
SwDeviceGetLifetime
SwDeviceInterfacePropertySet
SwDeviceInterfaceRegister
SwDeviceInterfaceSetState
SwDevicePropertySet
SwDeviceSetLifetime
SwMemFree
