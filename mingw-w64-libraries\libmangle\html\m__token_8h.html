<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: src/m_token.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>src/m_token.h File Reference</h1>
<p><a href="m__token_8h_source.html">Go to the source code of this file.</a></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_gc_elem.html">sGcElem</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__base.html">sMToken_base</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__value.html">sMToken_value</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__name.html">sMToken_name</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__dim.html">sMToken_dim</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token___unary.html">sMToken_Unary</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__binary.html">sMToken_binary</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">union &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html">uMToken</a></td></tr>
<tr><td colspan="2"><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a9c7f6053956c20047da91268be3e6a47">MTOKEN_KIND</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;base.kind)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a5753385eac52aad6be25ae37f0ea5d6a">MTOKEN_SUBKIND</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;base.subkind)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a22776018ac7fe7e7c0aa47cfe5f473a8">MTOKEN_CHAIN</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;base.chain)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ac080f6582086796b1ede7f1f65ae9fcf">MTOKEN_FLAGS</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;base.flags)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a1163ae872e9f50ddae2aeb936fc4d5e6">MTOKEN_FLAGS_UDC</a>&nbsp;&nbsp;&nbsp;0x1</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#adde521240f7b6401ffb3954772cfdb30">MTOKEN_FLAGS_NOTE</a>&nbsp;&nbsp;&nbsp;0x2</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#aa5b2060375f2aa1caa8995fb9e3fe8c2">MTOKEN_FLAGS_PTRREF</a>&nbsp;&nbsp;&nbsp;0x4</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a707505a9dd27394e28326b9e24b8a0e4">MTOKEN_FLAGS_ARRAY</a>&nbsp;&nbsp;&nbsp;0x8</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a0d7b7e44c99e08fe263ea15190ceeee1">MTOKEN_VALUE</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;value.value)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a92051c626009297e17ff622b77e809f7">MTOKEN_VALUE_SIGNED</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;value.is_signed)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a0912420c7697d7824cb9ce3761e999ac">MTOKEN_VALUE_SIZE</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;value.size)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ae3b0c2bd397aa5acede119bad863c8f8">MTOKEN_NAME</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;name.name)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a519b6bd0fb1c60d1077842bddeb731c0">MTOKEN_DIM_VALUE</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;dim.value)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a5ded8e065363aa57a8c5ecb0e4b3a0f7">MTOKEN_DIM_NTTP</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;dim.non_tt_param)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#abe961c81235d1d263052dd61439dbcf3">MTOKEN_DIM_NEGATE</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;dim.beNegate)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a362970fb206c74f30355356570000221">MTOKEN_UNARY</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;unary.unary)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ae7a7881952af6eea195152209a4166d8">MTOKEN_BINARY_LEFT</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;binary.left)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a158648c39041985090c587f092b38316">MTOKEN_BINARY_RIGHT</a>(PT)&nbsp;&nbsp;&nbsp;((PT)-&gt;binary.right)</td></tr>
<tr><td colspan="2"><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> { <br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdaab5d1cb176ec018a7dd2e1debed34b98">eMToken_none</a> =  0, 
<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda8e367c0d5a2c127b0f1ae55573e92fcd">eMToken_value</a> =  1, 
<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdae4b3f564d86b8e05792093eceba0612e">eMToken_name</a> =  2, 
<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c">eMToken_dim</a> =  3, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda22e1257761823f14db1c71ae94e7af8b">eMToken_unary</a> =  4, 
<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda9e20893db7dcbd0e2173b4f559cbb363">eMToken_binary</a> =  5, 
<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda66baeee525a866cdabe57f4e248b0f1a">eMToken_MAX</a>
<br/>
 }</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> { <br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aeba2165312b4788cea28a9edd3011eb1">eMST_unmangled</a> =  0, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ace877760d7bed370bfdd6570130878bc">eMST_nttp</a> =  1, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aab3ff1160a7b9f50b2734b0263482589">eMST_name</a> =  2, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a92a6f2d7b92b576b9b64565f40cb8267">eMST_colon</a> =  3, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc536c03cd872d606af6a4749c7a84e8">eMST_rtti</a> =  4, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a35df6016f35b2a5e8df1d01f477f9e17">eMST_cv</a> =  5, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aacf994772d1d924f2213dfd8d224b1fe">eMST_vftable</a> =  6, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc20bcf98832c814c92709447c1db811">eMST_vbtable</a> =  7, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a9d8acdbf8ea203bad5af3728e8ee5e43">eMST_vcall</a> =  8, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a56c4f2ba212e690d3862736cefc9e9d1">eMST_opname</a> =  9, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a02c0ba2ae316a629377149299fa60732">eMST_templargname</a> =  10, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a1c6d0872377629404a837b33093e4c20">eMST_type</a> =  11, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d3878378f218ed88b37f69ccc8b212a">eMST_dim</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af0a43b0c61ff4f5379159b46e21ca8c1">eMST_val</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a0f681303ab234dbd4f348e4f4ca2d3fb">eMST_gcarray</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a795ccccfc527f0ab6cfe6c372c47cbe6">eMST_slashed</a>, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac94af9ef7756a7c82213991ed795600a">eMST_array</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a376aea6022acbe69f38de3c9ea784484">eMST_element</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afcc464362c6d0c42ce707a51a7fbb9ae">eMST_template_argument_list</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ad08a86d5932f63c1a505083332ba1f2b">eMST_ltgt</a>, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac7455cb6f9f1dc1c495178075f5e617f">eMST_frame</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae1a781af8dddef1a996e911152071d2f">eMST_throw</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a6726a799efbe0d2d722fc83e3a04ca05">eMST_rframe</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ab87ff64219a845b0674207f9352895ee">eMST_destructor</a>, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a04c4d8922a43196bcbf80ef96ad3c5f8">eMST_oper</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a17a05df86d0d2e85f841baaccc2ca5c2">eMST_colonarray</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae271da23693826e5cacab27fd56779dc">eMST_lexical_frame</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a4c46333751fc7ce9d5e6e89e82abeef4">eMST_scope</a>, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a3f4d325424d8fd3976f2dfb834cd145d">eMST_udt_returning</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a90545698adebd8bbce09897217bd9695">eMST_coloncolon</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a921c684ed8d84a713967491a27abc903">eMST_assign</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afa6fba3d6a855d55277e3a8b74f4cb16">eMST_templateparam</a>, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acf92fa1bb19698734ba3e697b96ce9e5">eMST_nonetypetemplateparam</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af8e71df660ac1c8cf469ca3ddbbd0477">eMST_exp</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae947463010b4b392684e86e14337d61b">eMST_combine</a>, 
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acc1c13446a5ec298703df9bd94c6f20e">eMST_ecsu</a>, 
<br/>
&nbsp;&nbsp;<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d78963c03a17b55f921b31419f83d56">eMST_based</a>
<br/>
 }</td></tr>
<tr><td colspan="2"><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#abeb019f98a7616488287af32a6f9e51b">libmangle_gen_tok</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> kind, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> subkind, size_t addend)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle_generate_gc</a> (void)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a50ef074a3d1cf22f842abd4df7081743">chain_tok</a> (<a class="el" href="unionu_m_token.html">uMToken</a> *l, <a class="el" href="unionu_m_token.html">uMToken</a> *add)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#abf3eb472b66b477d0165a31437d35c09">libmangle_dump_tok</a> (FILE *fp, <a class="el" href="unionu_m_token.html">uMToken</a> *p)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a278a5859cf0ffb4e32fd2ad4cb2584de">libmangle_print_decl</a> (FILE *fp, <a class="el" href="unionu_m_token.html">uMToken</a> *p)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ac0f7cf41cc7c3e9c57dd94ed318dd5a4">libmangle_sprint_decl</a> (<a class="el" href="unionu_m_token.html">uMToken</a> *r)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a5e98df3f83afcc6e9e1f079091c0e567">gen_value</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, uint64_t val, int is_signed, int size)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#ac1a6fe5d506c4fd78650742da8d9e669">gen_name</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, const char *name)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#aced2d2323162ca5846cdb13d631169d6">gen_dim</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, uint64_t val, const char *non_tt_param, int fSigned, int fNegate)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a8c630a1c57e3d4f5009448af0d43fbb8">gen_unary</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <a class="el" href="unionu_m_token.html">uMToken</a> *un)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__token_8h.html#a0deb555c3210a60f0b5189ae462ed620">gen_binary</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> skind, <a class="el" href="unionu_m_token.html">uMToken</a> *l, <a class="el" href="unionu_m_token.html">uMToken</a> *r)</td></tr>
</table>
<hr/><h2>Define Documentation</h2>
<a class="anchor" id="ae7a7881952af6eea195152209a4166d8"></a><!-- doxytag: member="m_token.h::MTOKEN_BINARY_LEFT" ref="ae7a7881952af6eea195152209a4166d8" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_BINARY_LEFT</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;binary.left)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the left node on binary token, <em>PT</em> pointer to a binary <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="a158648c39041985090c587f092b38316"></a><!-- doxytag: member="m_token.h::MTOKEN_BINARY_RIGHT" ref="a158648c39041985090c587f092b38316" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_BINARY_RIGHT</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;binary.right)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the right node on binary token, <em>PT</em> pointer to a binary <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="a22776018ac7fe7e7c0aa47cfe5f473a8"></a><!-- doxytag: member="m_token.h::MTOKEN_CHAIN" ref="a22776018ac7fe7e7c0aa47cfe5f473a8" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_CHAIN</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;base.chain)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the pointer to the next token in the chain. </p>

</div>
</div>
<a class="anchor" id="abe961c81235d1d263052dd61439dbcf3"></a><!-- doxytag: member="m_token.h::MTOKEN_DIM_NEGATE" ref="abe961c81235d1d263052dd61439dbcf3" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_DIM_NEGATE</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;dim.beNegate)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Retrieve or set negative bit on value token, <em>PT</em> pointer to an generic <a class="el" href="unionu_m_token.html">uMToken</a> </p>

</div>
</div>
<a class="anchor" id="a5ded8e065363aa57a8c5ecb0e4b3a0f7"></a><!-- doxytag: member="m_token.h::MTOKEN_DIM_NTTP" ref="a5ded8e065363aa57a8c5ecb0e4b3a0f7" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_DIM_NTTP</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;dim.non_tt_param)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Retrieve or set the template of a token, <em>PT</em> pointer to a name <a class="el" href="unionu_m_token.html">uMToken</a> </p>

</div>
</div>
<a class="anchor" id="a519b6bd0fb1c60d1077842bddeb731c0"></a><!-- doxytag: member="m_token.h::MTOKEN_DIM_VALUE" ref="a519b6bd0fb1c60d1077842bddeb731c0" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_DIM_VALUE</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;dim.value)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Retrieve or set the value of a token, <em>PT</em> pointer to a value <a class="el" href="unionu_m_token.html">uMToken</a> </p>

</div>
</div>
<a class="anchor" id="ac080f6582086796b1ede7f1f65ae9fcf"></a><!-- doxytag: member="m_token.h::MTOKEN_FLAGS" ref="ac080f6582086796b1ede7f1f65ae9fcf" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_FLAGS</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;base.flags)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets flags in base descriptor. </p>

</div>
</div>
<a class="anchor" id="a707505a9dd27394e28326b9e24b8a0e4"></a><!-- doxytag: member="m_token.h::MTOKEN_FLAGS_ARRAY" ref="a707505a9dd27394e28326b9e24b8a0e4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_FLAGS_ARRAY&nbsp;&nbsp;&nbsp;0x8</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Decoded fragment has an array-like expression. </p>

</div>
</div>
<a class="anchor" id="adde521240f7b6401ffb3954772cfdb30"></a><!-- doxytag: member="m_token.h::MTOKEN_FLAGS_NOTE" ref="adde521240f7b6401ffb3954772cfdb30" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_FLAGS_NOTE&nbsp;&nbsp;&nbsp;0x2</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Contains "note" name token. </p>

</div>
</div>
<a class="anchor" id="aa5b2060375f2aa1caa8995fb9e3fe8c2"></a><!-- doxytag: member="m_token.h::MTOKEN_FLAGS_PTRREF" ref="aa5b2060375f2aa1caa8995fb9e3fe8c2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_FLAGS_PTRREF&nbsp;&nbsp;&nbsp;0x4</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Decoded fragment is a referrence. </p>

</div>
</div>
<a class="anchor" id="a1163ae872e9f50ddae2aeb936fc4d5e6"></a><!-- doxytag: member="m_token.h::MTOKEN_FLAGS_UDC" ref="a1163ae872e9f50ddae2aeb936fc4d5e6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_FLAGS_UDC&nbsp;&nbsp;&nbsp;0x1</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Indicates a following "name" token for named struct/union/class. </p>

</div>
</div>
<a class="anchor" id="a9c7f6053956c20047da91268be3e6a47"></a><!-- doxytag: member="m_token.h::MTOKEN_KIND" ref="a9c7f6053956c20047da91268be3e6a47" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_KIND</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;base.kind)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the token kind, <em>PT</em> pointer to a base <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="ae3b0c2bd397aa5acede119bad863c8f8"></a><!-- doxytag: member="m_token.h::MTOKEN_NAME" ref="ae3b0c2bd397aa5acede119bad863c8f8" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_NAME</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;name.name)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Retrieve or set the name string, <em>PT</em> pointer to a name <a class="el" href="unionu_m_token.html">uMToken</a> </p>

</div>
</div>
<a class="anchor" id="a5753385eac52aad6be25ae37f0ea5d6a"></a><!-- doxytag: member="m_token.h::MTOKEN_SUBKIND" ref="a5753385eac52aad6be25ae37f0ea5d6a" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_SUBKIND</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;base.subkind)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the token subkind, <em>PT</em> pointer to a base <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="a362970fb206c74f30355356570000221"></a><!-- doxytag: member="m_token.h::MTOKEN_UNARY" ref="a362970fb206c74f30355356570000221" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_UNARY</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;unary.unary)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the leaf element on a unary token, <em>PT</em> pointer to a unary <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="a0d7b7e44c99e08fe263ea15190ceeee1"></a><!-- doxytag: member="m_token.h::MTOKEN_VALUE" ref="a0d7b7e44c99e08fe263ea15190ceeee1" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_VALUE</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;value.value)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the token value. <em>PT</em> pointer to a value <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="a92051c626009297e17ff622b77e809f7"></a><!-- doxytag: member="m_token.h::MTOKEN_VALUE_SIGNED" ref="a92051c626009297e17ff622b77e809f7" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_VALUE_SIGNED</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;value.is_signed)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the signed bit on value token. <em>PT</em> pointer to a value <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<a class="anchor" id="a0912420c7697d7824cb9ce3761e999ac"></a><!-- doxytag: member="m_token.h::MTOKEN_VALUE_SIZE" ref="a0912420c7697d7824cb9ce3761e999ac" args="(PT)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MTOKEN_VALUE_SIZE</td>
          <td>(</td>
          <td class="paramtype">PT&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((PT)-&gt;value.size)</td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Sets the byte width of value in value token. <em>PT</em> pointer to a value <a class="el" href="unionu_m_token.html">uMToken</a>. </p>

</div>
</div>
<hr/><h2>Enumeration Type Documentation</h2>
<a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321"></a><!-- doxytag: member="m_token.h::eMSToken" ref="ad211982ef565f0550b5f86e4d15c6321" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Token "Subkind" enumeration list. Also used by internal function sprint_decl1() for printing. </p>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="m__token_8c.html#a3d90ad7945dc89f63c39837ee512fd85">gen_tok()</a> </dd>
<dd>
<a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a> </dd>
<dd>
<a class="el" href="structs_m_token__base.html">sMToken_base</a> </dd></dl>
<dl><dt><b>Enumerator: </b></dt><dd><table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321aeba2165312b4788cea28a9edd3011eb1"></a><!-- doxytag: member="eMST_unmangled" ref="ad211982ef565f0550b5f86e4d15c6321aeba2165312b4788cea28a9edd3011eb1" args="" -->eMST_unmangled</em>&nbsp;</td><td>
<p>Name is unmagled. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ace877760d7bed370bfdd6570130878bc"></a><!-- doxytag: member="eMST_nttp" ref="ad211982ef565f0550b5f86e4d15c6321ace877760d7bed370bfdd6570130878bc" args="" -->eMST_nttp</em>&nbsp;</td><td>
<p>Template name. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321aab3ff1160a7b9f50b2734b0263482589"></a><!-- doxytag: member="eMST_name" ref="ad211982ef565f0550b5f86e4d15c6321aab3ff1160a7b9f50b2734b0263482589" args="" -->eMST_name</em>&nbsp;</td><td>
<p>Decoded function name. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a92a6f2d7b92b576b9b64565f40cb8267"></a><!-- doxytag: member="eMST_colon" ref="ad211982ef565f0550b5f86e4d15c6321a92a6f2d7b92b576b9b64565f40cb8267" args="" -->eMST_colon</em>&nbsp;</td><td>
<p>Class member accessibility. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321adc536c03cd872d606af6a4749c7a84e8"></a><!-- doxytag: member="eMST_rtti" ref="ad211982ef565f0550b5f86e4d15c6321adc536c03cd872d606af6a4749c7a84e8" args="" -->eMST_rtti</em>&nbsp;</td><td>
<p>Runtime Type information name. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a35df6016f35b2a5e8df1d01f477f9e17"></a><!-- doxytag: member="eMST_cv" ref="ad211982ef565f0550b5f86e4d15c6321a35df6016f35b2a5e8df1d01f477f9e17" args="" -->eMST_cv</em>&nbsp;</td><td>
<p>Function call convention / data qualifiers / pointer. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321aacf994772d1d924f2213dfd8d224b1fe"></a><!-- doxytag: member="eMST_vftable" ref="ad211982ef565f0550b5f86e4d15c6321aacf994772d1d924f2213dfd8d224b1fe" args="" -->eMST_vftable</em>&nbsp;</td><td>
<p>Virtual Function Table. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321adc20bcf98832c814c92709447c1db811"></a><!-- doxytag: member="eMST_vbtable" ref="ad211982ef565f0550b5f86e4d15c6321adc20bcf98832c814c92709447c1db811" args="" -->eMST_vbtable</em>&nbsp;</td><td>
<p>Virtual Base Table. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a9d8acdbf8ea203bad5af3728e8ee5e43"></a><!-- doxytag: member="eMST_vcall" ref="ad211982ef565f0550b5f86e4d15c6321a9d8acdbf8ea203bad5af3728e8ee5e43" args="" -->eMST_vcall</em>&nbsp;</td><td>
<p>Virtual Function Call. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a56c4f2ba212e690d3862736cefc9e9d1"></a><!-- doxytag: member="eMST_opname" ref="ad211982ef565f0550b5f86e4d15c6321a56c4f2ba212e690d3862736cefc9e9d1" args="" -->eMST_opname</em>&nbsp;</td><td>
<p>Overloaded operator. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a02c0ba2ae316a629377149299fa60732"></a><!-- doxytag: member="eMST_templargname" ref="ad211982ef565f0550b5f86e4d15c6321a02c0ba2ae316a629377149299fa60732" args="" -->eMST_templargname</em>&nbsp;</td><td>
<p>Explicit template arg name. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a1c6d0872377629404a837b33093e4c20"></a><!-- doxytag: member="eMST_type" ref="ad211982ef565f0550b5f86e4d15c6321a1c6d0872377629404a837b33093e4c20" args="" -->eMST_type</em>&nbsp;</td><td>
<p>Function return type. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a7d3878378f218ed88b37f69ccc8b212a"></a><!-- doxytag: member="eMST_dim" ref="ad211982ef565f0550b5f86e4d15c6321a7d3878378f218ed88b37f69ccc8b212a" args="" -->eMST_dim</em>&nbsp;</td><td>
<p>Print array-like expression. </p>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c">eMToken_dim</a> </dd></dl>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321af0a43b0c61ff4f5379159b46e21ca8c1"></a><!-- doxytag: member="eMST_val" ref="ad211982ef565f0550b5f86e4d15c6321af0a43b0c61ff4f5379159b46e21ca8c1" args="" -->eMST_val</em>&nbsp;</td><td>
<p>Print value expression. </p>
<dl class="see"><dt><b>See also:</b></dt><dd>sMToken_val </dd></dl>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a0f681303ab234dbd4f348e4f4ca2d3fb"></a><!-- doxytag: member="eMST_gcarray" ref="ad211982ef565f0550b5f86e4d15c6321a0f681303ab234dbd4f348e4f4ca2d3fb" args="" -->eMST_gcarray</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a795ccccfc527f0ab6cfe6c372c47cbe6"></a><!-- doxytag: member="eMST_slashed" ref="ad211982ef565f0550b5f86e4d15c6321a795ccccfc527f0ab6cfe6c372c47cbe6" args="" -->eMST_slashed</em>&nbsp;</td><td>
<p>MSVC extenstion: "__gc" Managed C++ reference. MTOKEN_UNARY appended and prepended with "/". </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ac94af9ef7756a7c82213991ed795600a"></a><!-- doxytag: member="eMST_array" ref="ad211982ef565f0550b5f86e4d15c6321ac94af9ef7756a7c82213991ed795600a" args="" -->eMST_array</em>&nbsp;</td><td>
<p>MTOKEN_UNARY enclosed by square brackets. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a376aea6022acbe69f38de3c9ea784484"></a><!-- doxytag: member="eMST_element" ref="ad211982ef565f0550b5f86e4d15c6321a376aea6022acbe69f38de3c9ea784484" args="" -->eMST_element</em>&nbsp;</td><td>
<p>MTOKEN_UNARY in an argument list. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321afcc464362c6d0c42ce707a51a7fbb9ae"></a><!-- doxytag: member="eMST_template_argument_list" ref="ad211982ef565f0550b5f86e4d15c6321afcc464362c6d0c42ce707a51a7fbb9ae" args="" -->eMST_template_argument_list</em>&nbsp;</td><td>
<p>MTOKEN_UNARY in an argument list. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ad08a86d5932f63c1a505083332ba1f2b"></a><!-- doxytag: member="eMST_ltgt" ref="ad211982ef565f0550b5f86e4d15c6321ad08a86d5932f63c1a505083332ba1f2b" args="" -->eMST_ltgt</em>&nbsp;</td><td>
<p>MTOKEN_UNARY enclosed by angular brackets. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ac7455cb6f9f1dc1c495178075f5e617f"></a><!-- doxytag: member="eMST_frame" ref="ad211982ef565f0550b5f86e4d15c6321ac7455cb6f9f1dc1c495178075f5e617f" args="" -->eMST_frame</em>&nbsp;</td><td>
<p>MTOKEN_UNARY enclosed by curly brackets. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ae1a781af8dddef1a996e911152071d2f"></a><!-- doxytag: member="eMST_throw" ref="ad211982ef565f0550b5f86e4d15c6321ae1a781af8dddef1a996e911152071d2f" args="" -->eMST_throw</em>&nbsp;</td><td>
<p>MTOKEN_UNARY prepended by "throw ". </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a6726a799efbe0d2d722fc83e3a04ca05"></a><!-- doxytag: member="eMST_rframe" ref="ad211982ef565f0550b5f86e4d15c6321a6726a799efbe0d2d722fc83e3a04ca05" args="" -->eMST_rframe</em>&nbsp;</td><td>
<p>MTOKEN_UNARY enclosed by parentheses. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ab87ff64219a845b0674207f9352895ee"></a><!-- doxytag: member="eMST_destructor" ref="ad211982ef565f0550b5f86e4d15c6321ab87ff64219a845b0674207f9352895ee" args="" -->eMST_destructor</em>&nbsp;</td><td>
<p>MTOKEN_UNARY prepended with "~". </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a04c4d8922a43196bcbf80ef96ad3c5f8"></a><!-- doxytag: member="eMST_oper" ref="ad211982ef565f0550b5f86e4d15c6321a04c4d8922a43196bcbf80ef96ad3c5f8" args="" -->eMST_oper</em>&nbsp;</td><td>
<p>indicates that token an operand, prints from MTOKEN_UNARY. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a17a05df86d0d2e85f841baaccc2ca5c2"></a><!-- doxytag: member="eMST_colonarray" ref="ad211982ef565f0550b5f86e4d15c6321a17a05df86d0d2e85f841baaccc2ca5c2" args="" -->eMST_colonarray</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ae271da23693826e5cacab27fd56779dc"></a><!-- doxytag: member="eMST_lexical_frame" ref="ad211982ef565f0550b5f86e4d15c6321ae271da23693826e5cacab27fd56779dc" args="" -->eMST_lexical_frame</em>&nbsp;</td><td>
<p>Unused, to be removed. MTOKEN_UNARY enclosed by single quotes "'". </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a4c46333751fc7ce9d5e6e89e82abeef4"></a><!-- doxytag: member="eMST_scope" ref="ad211982ef565f0550b5f86e4d15c6321a4c46333751fc7ce9d5e6e89e82abeef4" args="" -->eMST_scope</em>&nbsp;</td><td>
<p>MTOKEN_UNARY, unenclosed. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a3f4d325424d8fd3976f2dfb834cd145d"></a><!-- doxytag: member="eMST_udt_returning" ref="ad211982ef565f0550b5f86e4d15c6321a3f4d325424d8fd3976f2dfb834cd145d" args="" -->eMST_udt_returning</em>&nbsp;</td><td>
<p>User defined types (RTTI). </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a90545698adebd8bbce09897217bd9695"></a><!-- doxytag: member="eMST_coloncolon" ref="ad211982ef565f0550b5f86e4d15c6321a90545698adebd8bbce09897217bd9695" args="" -->eMST_coloncolon</em>&nbsp;</td><td>
<p>"::" between MTOKEN_BINARY_LEFT and MTOKEN_BINARY_RIGHT. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a921c684ed8d84a713967491a27abc903"></a><!-- doxytag: member="eMST_assign" ref="ad211982ef565f0550b5f86e4d15c6321a921c684ed8d84a713967491a27abc903" args="" -->eMST_assign</em>&nbsp;</td><td>
<p>"=" between MTOKEN_BINARY_LEFT and MTOKEN_BINARY_RIGHT and appended with "}". </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321afa6fba3d6a855d55277e3a8b74f4cb16"></a><!-- doxytag: member="eMST_templateparam" ref="ad211982ef565f0550b5f86e4d15c6321afa6fba3d6a855d55277e3a8b74f4cb16" args="" -->eMST_templateparam</em>&nbsp;</td><td>
<p>Explicit template. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321acf92fa1bb19698734ba3e697b96ce9e5"></a><!-- doxytag: member="eMST_nonetypetemplateparam" ref="ad211982ef565f0550b5f86e4d15c6321acf92fa1bb19698734ba3e697b96ce9e5" args="" -->eMST_nonetypetemplateparam</em>&nbsp;</td><td>
<p>Non-explicit template. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321af8e71df660ac1c8cf469ca3ddbbd0477"></a><!-- doxytag: member="eMST_exp" ref="ad211982ef565f0550b5f86e4d15c6321af8e71df660ac1c8cf469ca3ddbbd0477" args="" -->eMST_exp</em>&nbsp;</td><td>
<p>dim 'e' (exponent) dim </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321ae947463010b4b392684e86e14337d61b"></a><!-- doxytag: member="eMST_combine" ref="ad211982ef565f0550b5f86e4d15c6321ae947463010b4b392684e86e14337d61b" args="" -->eMST_combine</em>&nbsp;</td><td>
<p>Unary grouping. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321acc1c13446a5ec298703df9bd94c6f20e"></a><!-- doxytag: member="eMST_ecsu" ref="ad211982ef565f0550b5f86e4d15c6321acc1c13446a5ec298703df9bd94c6f20e" args="" -->eMST_ecsu</em>&nbsp;</td><td>
<p>Is an Enum/Class/Struct/Union </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="ad211982ef565f0550b5f86e4d15c6321a7d78963c03a17b55f921b31419f83d56"></a><!-- doxytag: member="eMST_based" ref="ad211982ef565f0550b5f86e4d15c6321a7d78963c03a17b55f921b31419f83d56" args="" -->eMST_based</em>&nbsp;</td><td>
<p>MSVC extension: "__based" Based addressing </p>
</td></tr>
</table>
</dd>
</dl>

</div>
</div>
<a class="anchor" id="a50bee8455836804dd921dd275b0bcebd"></a><!-- doxytag: member="m_token.h::eMToken" ref="a50bee8455836804dd921dd275b0bcebd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Token "Kind" enumeration list. </p>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="m__token_8c.html#a3d90ad7945dc89f63c39837ee512fd85">gen_tok()</a> </dd>
<dd>
<a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a> </dd>
<dd>
<a class="el" href="structs_m_token__base.html">sMToken_base</a> </dd></dl>
<dl><dt><b>Enumerator: </b></dt><dd><table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebdaab5d1cb176ec018a7dd2e1debed34b98"></a><!-- doxytag: member="eMToken_none" ref="a50bee8455836804dd921dd275b0bcebdaab5d1cb176ec018a7dd2e1debed34b98" args="" -->eMToken_none</em>&nbsp;</td><td>
<p>Token type: None. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebda8e367c0d5a2c127b0f1ae55573e92fcd"></a><!-- doxytag: member="eMToken_value" ref="a50bee8455836804dd921dd275b0bcebda8e367c0d5a2c127b0f1ae55573e92fcd" args="" -->eMToken_value</em>&nbsp;</td><td>
<p>Token type: Value. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebdae4b3f564d86b8e05792093eceba0612e"></a><!-- doxytag: member="eMToken_name" ref="a50bee8455836804dd921dd275b0bcebdae4b3f564d86b8e05792093eceba0612e" args="" -->eMToken_name</em>&nbsp;</td><td>
<p>Token type: Name. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c"></a><!-- doxytag: member="eMToken_dim" ref="a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c" args="" -->eMToken_dim</em>&nbsp;</td><td>
<p>Token type: Dim. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebda22e1257761823f14db1c71ae94e7af8b"></a><!-- doxytag: member="eMToken_unary" ref="a50bee8455836804dd921dd275b0bcebda22e1257761823f14db1c71ae94e7af8b" args="" -->eMToken_unary</em>&nbsp;</td><td>
<p>Token type: Unary </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebda9e20893db7dcbd0e2173b4f559cbb363"></a><!-- doxytag: member="eMToken_binary" ref="a50bee8455836804dd921dd275b0bcebda9e20893db7dcbd0e2173b4f559cbb363" args="" -->eMToken_binary</em>&nbsp;</td><td>
<p>Token type: Binary </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="a50bee8455836804dd921dd275b0bcebda66baeee525a866cdabe57f4e248b0f1a"></a><!-- doxytag: member="eMToken_MAX" ref="a50bee8455836804dd921dd275b0bcebda66baeee525a866cdabe57f4e248b0f1a" args="" -->eMToken_MAX</em>&nbsp;</td><td>
<p>Unused sentinel. </p>
</td></tr>
</table>
</dd>
</dl>

</div>
</div>
<hr/><h2>Function Documentation</h2>
<a class="anchor" id="a50ef074a3d1cf22f842abd4df7081743"></a><!-- doxytag: member="m_token.h::chain_tok" ref="a50ef074a3d1cf22f842abd4df7081743" args="(uMToken *l, uMToken *add)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* chain_tok </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>l</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>add</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Chains uMTokens together. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>l</em>&nbsp;</td><td>uMtoken chain to link up with. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>add</em>&nbsp;</td><td>uMtoken to add to chain. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd><em>l</em> unchanged </dd></dl>

</div>
</div>
<a class="anchor" id="a0deb555c3210a60f0b5189ae462ed620"></a><!-- doxytag: member="m_token.h::gen_binary" ref="a0deb555c3210a60f0b5189ae462ed620" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uMToken *l, uMToken *r)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_binary </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>l</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>r</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Generates a binary node token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subKind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>l</em>&nbsp;</td><td>Left node element. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>r</em>&nbsp;</td><td>Right node element. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to binary token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__binary.html">sMToken_binary</a> </dd></dl>

</div>
</div>
<a class="anchor" id="aced2d2323162ca5846cdb13d631169d6"></a><!-- doxytag: member="m_token.h::gen_dim" ref="aced2d2323162ca5846cdb13d631169d6" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uint64_t val, const char *non_tt_param, int fSigned, int fNegate)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_dim </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>non_tt_param</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>fSigned</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>fNegate</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "dim" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>val</em>&nbsp;</td><td>Token numerical value. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>non_tt_param</em>&nbsp;</td><td>pointer to decoded C++ template name. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fSigned</em>&nbsp;</td><td>Signedness of the numerical value. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fNegate</em>&nbsp;</td><td>1 for "val" is negative digit. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to dim token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__dim.html">sMToken_dim</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ac1a6fe5d506c4fd78650742da8d9e669"></a><!-- doxytag: member="m_token.h::gen_name" ref="ac1a6fe5d506c4fd78650742da8d9e669" args="(libmangle_gc_context_t *gc, enum eMSToken skind, const char *name)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_name </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>name</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "name" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>name</em>&nbsp;</td><td>Pointer to name string. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to name token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__name.html">sMToken_name</a> </dd></dl>

</div>
</div>
<a class="anchor" id="a8c630a1c57e3d4f5009448af0d43fbb8"></a><!-- doxytag: member="m_token.h::gen_unary" ref="a8c630a1c57e3d4f5009448af0d43fbb8" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uMToken *un)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_unary </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>un</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "unary" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>un</em>&nbsp;</td><td>Pointer to leaf element. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to a unary token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd>sMToken_unary </dd></dl>

</div>
</div>
<a class="anchor" id="a5e98df3f83afcc6e9e1f079091c0e567"></a><!-- doxytag: member="m_token.h::gen_value" ref="a5e98df3f83afcc6e9e1f079091c0e567" args="(libmangle_gc_context_t *gc, enum eMSToken skind, uint64_t val, int is_signed, int size)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* gen_value </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>skind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>is_signed</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>size</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a "value" kind token. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>skind</em>&nbsp;</td><td>Token subkind. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>val</em>&nbsp;</td><td>Sets the value on token, </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>is_signed</em>&nbsp;</td><td>Signed bit of <em>val</em>. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>size</em>&nbsp;</td><td>Width of <em>val</em>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to value token. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__value.html">sMToken_value</a> </dd></dl>

</div>
</div>
<a class="anchor" id="abf3eb472b66b477d0165a31437d35c09"></a><!-- doxytag: member="m_token.h::libmangle_dump_tok" ref="abf3eb472b66b477d0165a31437d35c09" args="(FILE *fp, uMToken *p)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void libmangle_dump_tok </td>
          <td>(</td>
          <td class="paramtype">FILE *&nbsp;</td>
          <td class="paramname"> <em>fp</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>p</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Dumps <a class="el" href="unionu_m_token.html">uMToken</a> to a file descriptor for debugging. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fp</em>&nbsp;</td><td>File descriptor to print the token to. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>p</em>&nbsp;</td><td><a class="el" href="unionu_m_token.html">uMToken</a> chain to print. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="abeb019f98a7616488287af32a6f9e51b"></a><!-- doxytag: member="m_token.h::libmangle_gen_tok" ref="abeb019f98a7616488287af32a6f9e51b" args="(libmangle_gc_context_t *gc, enum eMToken kind, enum eMSToken subkind, size_t addend)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* libmangle_gen_tok </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">eMToken</a>&nbsp;</td>
          <td class="paramname"> <em>kind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">eMSToken</a>&nbsp;</td>
          <td class="paramname"> <em>subkind</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&nbsp;</td>
          <td class="paramname"> <em>addend</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>gen_tok constructs <a class="el" href="unionu_m_token.html">uMToken</a> instances Instances are destroyed with <a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a>. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Pointer to garbage collection context. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>kind</em>&nbsp;</td><td>Kind of token to construct </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>subkind</em>&nbsp;</td><td>Subkind of token to construct </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>addend</em>&nbsp;</td><td>Additional byte padding at the end. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="a54257a43469abe9c5f9556a1913bbf2f"></a><!-- doxytag: member="m_token.h::libmangle_generate_gc" ref="a54257a43469abe9c5f9556a1913bbf2f" args="(void)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a>* libmangle_generate_gc </td>
          <td>(</td>
          <td class="paramtype">void&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Constructs a garbage collection context token. </p>
<dl class="return"><dt><b>Returns:</b></dt><dd>Pointer to context. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="a278a5859cf0ffb4e32fd2ad4cb2584de"></a><!-- doxytag: member="m_token.h::libmangle_print_decl" ref="a278a5859cf0ffb4e32fd2ad4cb2584de" args="(FILE *fp, uMToken *p)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void libmangle_print_decl </td>
          <td>(</td>
          <td class="paramtype">FILE *&nbsp;</td>
          <td class="paramname"> <em>fp</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>p</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Prints C++ name to file descriptor. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>fp</em>&nbsp;</td><td>Output file descriptor. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>p</em>&nbsp;</td><td>Token containing information about the C++ name. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle_decode_ms_name()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ac6f10b5d722b67adc42b2efaf4683dc1"></a><!-- doxytag: member="m_token.h::libmangle_release_gc" ref="ac6f10b5d722b67adc42b2efaf4683dc1" args="(libmangle_gc_context_t *gc)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void libmangle_release_gc </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em></td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Releases memory tracked by context. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>Garbage collection context to work on. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle_generate_gc()</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ac0f7cf41cc7c3e9c57dd94ed318dd5a4"></a><!-- doxytag: member="m_token.h::libmangle_sprint_decl" ref="ac0f7cf41cc7c3e9c57dd94ed318dd5a4" args="(uMToken *r)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* libmangle_sprint_decl </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>r</em></td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Get pointer to decoded C++ name string. Use free() to release returned string. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>r</em>&nbsp;</td><td>C++ name token. </td></tr>
  </table>
  </dd>
</dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>pointer to decoded C++ name string. </dd></dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle_decode_ms_name()</a> </dd></dl>

</div>
</div>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
