;
; Definition file of NDIS.SYS
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "NDIS.SYS"
EXPORTS
EthFilterDprIndicateReceive
EthFilterDprIndicateReceiveComplete
NDIS_BUFFER_TO_SPAN_PAGES
NdisAcquireRWLockRead
NdisAcquireRWLockWrite
NdisAcquireReadWriteLock
NdisAcquireSpinLock
NdisActiveGroupCount
NdisAdjustBufferLength
NdisAdjustNetBufferCurrentMdl
NdisAdvanceNetBufferDataStart
NdisAdvanceNetBufferListDataStart
NdisAllocateBuffer
NdisAllocateBufferPool
NdisAllocateCloneNetBufferList
NdisAllocateCloneOidRequest
NdisAllocateFragmentNetBufferList
NdisAllocateGenericObject
NdisAllocateIoWorkItem
NdisAllocateMdl
NdisAllocateMemory
NdisAllocateMemoryWithTag
NdisAllocateMemoryWithT<PERSON>
NdisAllocateNetBuffer
NdisAllocateNetBufferAndNetBufferList
NdisAllocateNetBufferList
NdisAllocateNetBufferListContext
NdisAllocateNetBufferListPool
NdisAllocateNetBufferMdlAndData
NdisAllocateNetBufferPool
NdisAllocateOidRequest
NdisAllocatePacket
NdisAllocatePacketPool
NdisAllocatePacketPoolEx
NdisAllocateRWLock
NdisAllocateReassembledNetBufferList
NdisAllocateRefCount
NdisAllocateSharedMemory
NdisAllocateSpinLock
NdisAllocateTimerObject
NdisAnsiStringToUnicodeString
NdisBufferLength
NdisBufferVirtualAddress
NdisBuildScatterGatherList
NdisCancelDirectOidRequest
NdisCancelOidRequest
NdisCancelSendNetBufferLists
NdisCancelSendPackets
NdisCancelTimer
NdisCancelTimerObject
NdisClAddParty
NdisClCloseAddressFamily
NdisClCloseCall
NdisClDeregisterSap
NdisClDropParty
NdisClGetProtocolVcContextFromTapiCallId
NdisClIncomingCallComplete
NdisClMakeCall
NdisClModifyCallQoS
NdisClNotifyCloseAddressFamilyComplete
NdisClOpenAddressFamily
NdisClOpenAddressFamilyEx
NdisClRegisterSap
NdisCloseAdapter
NdisCloseAdapterEx
NdisCloseConfiguration
NdisCloseFile
NdisCloseNDKAdapter
NdisCmActivateVc
NdisCmAddPartyComplete
NdisCmCloseAddressFamilyComplete
NdisCmCloseCallComplete
NdisCmDeactivateVc
NdisCmDeregisterSapComplete
NdisCmDispatchCallConnected
NdisCmDispatchIncomingCall
NdisCmDispatchIncomingCallQoSChange
NdisCmDispatchIncomingCloseCall
NdisCmDispatchIncomingDropParty
NdisCmDropPartyComplete
NdisCmMakeCallComplete
NdisCmModifyCallQoSComplete
NdisCmNotifyCloseAddressFamily
NdisCmOpenAddressFamilyComplete
NdisCmRegisterAddressFamily
NdisCmRegisterAddressFamilyEx
NdisCmRegisterSapComplete
NdisCoAssignInstanceName
NdisCoCreateVc
NdisCoDeleteVc
NdisCoGetTapiCallId
NdisCoOidRequest
NdisCoOidRequestComplete
NdisCoRequest
NdisCoRequestComplete
NdisCoSendNetBufferLists
NdisCoSendPackets
NdisCompareAnsiString
NdisCompareUnicodeString
NdisCompleteBindAdapter
NdisCompleteBindAdapterEx
NdisCompleteDmaTransfer
NdisCompleteNetPnPEvent
NdisCompletePnPEvent
NdisCompleteUnbindAdapter
NdisCompleteUnbindAdapterEx
NdisConvertNdisStatusToNtStatus
NdisConvertNtStatusToNdisStatus
NdisCopyBuffer
NdisCopyFromNetBufferToNetBuffer
NdisCopyFromPacketToPacket
NdisCopyFromPacketToPacketSafe
NdisCopyReceiveNetBufferListInfo
NdisCopySendNetBufferListInfo
NdisCurrentGroupAndProcessor
NdisCurrentProcessorIndex
NdisDereferenceWithTag
NdisDeregisterDeviceEx
NdisDeregisterProtocol
NdisDeregisterProtocolDriver
NdisDeregisterTdiCallBack
NdisDirectOidRequest
NdisDllInitialize
NdisDprAcquireReadWriteLock
NdisDprAcquireSpinLock
NdisDprAllocatePacket
NdisDprAllocatePacketNonInterlocked
NdisDprFreePacket
NdisDprFreePacketNonInterlocked
NdisDprReleaseReadWriteLock
NdisDprReleaseSpinLock
NdisEnumerateFilterModules
NdisEqualString
NdisFCancelDirectOidRequest
NdisFCancelOidRequest
NdisFCancelSendNetBufferLists
NdisFDeregisterFilterDriver
NdisFDevicePnPEventNotify
NdisFDirectOidRequest
NdisFDirectOidRequestComplete
NdisFGetOptionalSwitchHandlers
NdisFIndicateReceiveNetBufferLists
NdisFIndicateStatus
NdisFNetPnPEvent
NdisFOidRequest
NdisFOidRequestComplete
NdisFPauseComplete
NdisFRegisterFilterDriver
NdisFRestartComplete
NdisFRestartFilter
NdisFRetryAttach
NdisFReturnNetBufferLists
NdisFSendNetBufferLists
NdisFSendNetBufferListsComplete
NdisFSetAttributes
NdisFSynchronousOidRequest
NdisFreeBuffer
NdisFreeBufferPool
NdisFreeCloneNetBufferList
NdisFreeCloneOidRequest
NdisFreeFragmentNetBufferList
NdisFreeGenericObject
NdisFreeIoWorkItem
NdisFreeMdl
NdisFreeMemory
NdisFreeMemoryWithTag
NdisFreeMemoryWithTagPriority
NdisFreeNetBuffer
NdisFreeNetBufferList
NdisFreeNetBufferListContext
NdisFreeNetBufferListPool
NdisFreeNetBufferPool
NdisFreeOidRequest
NdisFreePacket
NdisFreePacketPool
NdisFreeRWLock
NdisFreeReassembledNetBufferList
NdisFreeRefCount
NdisFreeScatterGatherList
NdisFreeSharedMemory
NdisFreeSpinLock
NdisFreeTimerObject
NdisGeneratePartialCancelId
NdisGetAndReferenceCompartmentJobObject
NdisGetBufferPhysicalArraySize
NdisGetCurrentProcessorCounts
NdisGetCurrentProcessorCpuUsage
NdisGetCurrentSystemTime
NdisGetDataBuffer
NdisGetDeviceReservedExtension
NdisGetDriverHandle
NdisGetFirstBufferFromPacket
NdisGetFirstBufferFromPacketSafe
NdisGetHypervisorInfo
NdisGetJobObjectCompartmentId
NdisGetNetBufferListProtocolId
NdisGetPacketCancelId
NdisGetPacketFromNetBufferList
NdisGetPoolFromNetBuffer
NdisGetPoolFromNetBufferList
NdisGetPoolFromPacket
NdisGetProcessObjectCompartmentId
NdisGetProcessorInformation
NdisGetProcessorInformationEx
NdisGetReceivedPacket
NdisGetRefCount
NdisGetRoutineAddress
NdisGetRssProcessorInformation
NdisGetSessionCompartmentId
NdisGetSessionToCompartmentMappingEpochAndZero
NdisGetSharedDataAlignment
NdisGetSystemUpTime
NdisGetSystemUpTimeEx
NdisGetThreadObjectCompartmentId
NdisGetThreadObjectCompartmentScope
NdisGetVersion
NdisGroupActiveProcessorCount
NdisGroupActiveProcessorMask
NdisGroupMaxProcessorCount
NdisIMAssociateMiniport
NdisIMCancelInitializeDeviceInstance
NdisIMCopySendCompletePerPacketInfo
NdisIMCopySendPerPacketInfo
NdisIMDeInitializeDeviceInstance
NdisIMDeregisterLayeredMiniport
NdisIMGetBindingContext
NdisIMGetCurrentPacketStack
NdisIMGetDeviceContext
NdisIMInitializeDeviceInstance
NdisIMInitializeDeviceInstanceEx
NdisIMNotifyPnPEvent
NdisIMQueueMiniportCallback
NdisIMRegisterLayeredMiniport
NdisIMRevertBack
NdisIMSwitchToMiniport
NdisIMVBusDeviceAdd
NdisIMVBusDeviceRemove
NdisIfAddIfStackEntry
NdisIfAllocateNetLuidIndex
NdisIfAllocateNetLuidIndexEx
NdisIfDeleteIfStackEntry
NdisIfDeregisterInterface
NdisIfDeregisterProvider
NdisIfFreeNetLuidIndex
NdisIfGetInterfaceIndexFromNetLuid
NdisIfGetNetLuidFromInterfaceIndex
NdisIfQueryBindingIfIndex
NdisIfRegisterInterface
NdisIfRegisterProvider
NdisImmediateReadPciSlotInformation
NdisImmediateReadPortUchar
NdisImmediateReadPortUlong
NdisImmediateReadPortUshort
NdisImmediateReadSharedMemory
NdisImmediateWritePciSlotInformation
NdisImmediateWritePortUchar
NdisImmediateWritePortUlong
NdisImmediateWritePortUshort
NdisImmediateWriteSharedMemory
NdisInitAnsiString
NdisInitUnicodeString
NdisInitializeEvent
NdisInitializeReadWriteLock
NdisInitializeString
NdisInitializeTimer
NdisInitializeWrapper
NdisInitiateOffload
NdisInterlockedAddLargeInterger
NdisInterlockedAddUlong
NdisInterlockedDecrement
NdisInterlockedIncrement
NdisInterlockedInsertHeadList
NdisInterlockedInsertTailList
NdisInterlockedPopEntryList
NdisInterlockedPushEntryList
NdisInterlockedRemoveHeadList
NdisInvalidateOffload
NdisIsStatusIndicationCloneable
NdisLWMDeregisterMiniportDriver
NdisLWMInitializeNetworkInterface
NdisLWMRegisterMiniportDriver
NdisLWMStartNetworkInterface
NdisLWMUninitializeNetworkInterface
NdisMAllocateMapRegisters
NdisMAllocateNetBufferSGList
NdisMAllocatePort
NdisMAllocateSharedMemory
NdisMAllocateSharedMemoryAsync
NdisMAllocateSharedMemoryAsyncEx
NdisMCancelTimer
NdisMCloseLog
NdisMCmActivateVc
NdisMCmCreateVc
NdisMCmDeactivateVc
NdisMCmDeleteVc
NdisMCmOidRequest
NdisMCmRegisterAddressFamily
NdisMCmRegisterAddressFamilyEx
NdisMCmRequest
NdisMCoActivateVcComplete
NdisMCoDeactivateVcComplete
NdisMCoIndicateReceiveNetBufferLists
NdisMCoIndicateReceivePacket
NdisMCoIndicateStatus
NdisMCoIndicateStatusEx
NdisMCoOidRequestComplete
NdisMCoReceiveComplete
NdisMCoRequestComplete
NdisMCoSendComplete
NdisMCoSendNetBufferListsComplete
NdisMCompleteBufferPhysicalMapping
NdisMConfigMSIXTableEntry
NdisMCreateLog
NdisMDeregisterAdapterShutdownHandler
NdisMDeregisterDevice
NdisMDeregisterDmaChannel
NdisMDeregisterInterrupt
NdisMDeregisterInterruptEx
NdisMDeregisterIoPortRange
NdisMDeregisterMiniportDriver
NdisMDeregisterScatterGatherDma
NdisMDeregisterWdiMiniportDriver
NdisMDirectOidRequestComplete
NdisMEnableVirtualization
NdisMFlushLog
NdisMFreeMapRegisters
NdisMFreeNetBufferSGList
NdisMFreePort
NdisMFreeSharedMemory
NdisMGetBusData
NdisMGetDeviceProperty
NdisMGetDmaAlignment
NdisMGetMiniportInitAttributes
NdisMGetOffloadHandlers
NdisMGetVirtualDeviceLocation
NdisMGetVirtualFunctionBusData
NdisMGetVirtualFunctionLocation
NdisMIdleNotificationComplete
NdisMIdleNotificationCompleteEx
NdisMIdleNotificationConfirm
NdisMIndicateReceiveNetBufferLists
NdisMIndicateStatus
NdisMIndicateStatusComplete
NdisMIndicateStatusEx
NdisMInitializeScatterGatherDma
NdisMInitializeTimer
NdisMInitiateOffloadComplete
NdisMInvalidateConfigBlock
NdisMInvalidateOffloadComplete
NdisMMapIoSpace
NdisMNetPnPEvent
NdisMOffloadEventIndicate
NdisMOidRequestComplete
NdisMPauseComplete
NdisMPciAssignResources
NdisMPromoteMiniport
NdisMQueryAdapterInstanceName
NdisMQueryAdapterResources
NdisMQueryInformationComplete
NdisMQueryOffloadStateComplete
NdisMQueryProbedBars
NdisMQueueDpc
NdisMQueueDpcEx
NdisMReadConfigBlock
NdisMReadDmaCounter
NdisMReenumerateFailedAdapter
NdisMRegisterAdapterShutdownHandler
NdisMRegisterDevice
NdisMRegisterDmaChannel
NdisMRegisterInterrupt
NdisMRegisterInterruptEx
NdisMRegisterIoPortRange
NdisMRegisterMiniport
NdisMRegisterMiniportDriver
NdisMRegisterScatterGatherDma
NdisMRegisterUnloadHandler
NdisMRegisterWdiMiniportDriver
NdisMRemoveMiniport
NdisMRequestDpc
NdisMResetComplete
NdisMResetMiniport
NdisMRestartComplete
NdisMSendComplete
NdisMSendNetBufferListsComplete
NdisMSendResourcesAvailable
NdisMSetAttributes
NdisMSetAttributesEx
NdisMSetBusData
NdisMSetInformationComplete
NdisMSetInterfaceCompartment
NdisMSetMiniportAttributes
NdisMSetMiniportSecondary
NdisMSetPeriodicTimer
NdisMSetTimer
NdisMSetVirtualFunctionBusData
NdisMSleep
NdisMStartBufferPhysicalMapping
NdisMSynchronizeWithInterrupt
NdisMSynchronizeWithInterruptEx
NdisMTerminateOffloadComplete
NdisMTransferDataComplete
NdisMTriggerPDDrainNotification
NdisMUnmapIoSpace
NdisMUpdateOffloadComplete
NdisMWanIndicateReceive
NdisMWanIndicateReceiveComplete
NdisMWanSendComplete
NdisMWriteConfigBlock
NdisMWriteLogData
NdisMapFile
NdisMatchPdoWithPacket
NdisMaxGroupCount
NdisNblTrackerDeregisterComponent
NdisNblTrackerQueryNblCurrentOwner
NdisNblTrackerRecordEvent
NdisNblTrackerRegisterComponent
NdisNblTrackerTransferOwnership
NdisOffloadTcpDisconnect
NdisOffloadTcpForward
NdisOffloadTcpReceive
NdisOffloadTcpReceiveReturn
NdisOffloadTcpSend
NdisOidRequest
NdisOpenAdapter
NdisOpenAdapterEx
NdisOpenConfiguration
NdisOpenConfigurationEx
NdisOpenConfigurationKeyByIndex
NdisOpenConfigurationKeyByName
NdisOpenFile
NdisOpenNDKAdapter
NdisOpenProtocolConfiguration
NdisOverrideBusNumber
NdisPDStartup
NdisPacketPoolUsage
NdisPacketSize
NdisProcessorIndexToNumber
NdisProcessorNumberToIndex
NdisQueryAdapterInstanceName
NdisQueryBindInstanceName
NdisQueryBuffer
NdisQueryBufferOffset
NdisQueryBufferSafe
NdisQueryDiagnosticSetting
NdisQueryMapRegisterCount
NdisQueryNetBufferPhysicalCount
NdisQueryOffloadState
NdisQueryPendingIOCount
NdisQueueIoWorkItem
NdisReEnumerateProtocolBindings
NdisReadConfiguration
NdisReadEisaSlotInformation
NdisReadEisaSlotInformationEx
NdisReadMcaPosInformation
NdisReadNetworkAddress
NdisReadPciSlotInformation
NdisReadPcmciaAttributeMemory
NdisReferenceWithTag
NdisRegisterDeviceEx
NdisRegisterProtocol
NdisRegisterProtocolDriver
NdisRegisterTdiCallBack
NdisReleaseNicActive
NdisReleaseRWLock
NdisReleaseReadWriteLock
NdisReleaseSpinLock
NdisRequest
NdisRequestEx
NdisReset
NdisResetEvent
NdisRetreatNetBufferDataStart
NdisRetreatNetBufferListDataStart
NdisReturnNetBufferLists
NdisReturnPackets
NdisScheduleWorkItem
NdisSend
NdisSendNetBufferLists
NdisSendPackets
NdisSetAoAcOptions
NdisSetCoalescableTimerObject
NdisSetEvent
NdisSetOptionalHandlers
NdisSetPacketCancelId
NdisSetPacketPoolProtocolId
NdisSetPacketStatus
NdisSetPeriodicTimer
NdisSetProtocolFilter
NdisSetSessionCompartmentId
NdisSetThreadObjectCompartmentId
NdisSetThreadObjectCompartmentScope
NdisSetTimer
NdisSetTimerEx
NdisSetTimerObject
NdisSetupDmaTransfer
NdisSynchronousOidRequest
NdisSystemActiveProcessorCount
NdisSystemProcessorCount
NdisTerminateOffload
NdisTerminateWrapper
NdisTestRWLockHeldByCurrentProcessorRead
NdisTestRWLockHeldByCurrentProcessorWrite
NdisTransferData
NdisTryAcquireNicActive
NdisTryAcquireRWLockRead
NdisTryAcquireRWLockWrite
NdisTryPromoteRWLockFromReadToWrite
NdisUnbindAdapter
NdisUnchainBufferAtBack
NdisUnchainBufferAtFront
NdisUnicodeStringToAnsiString
NdisUnmapFile
NdisUpcaseUnicodeString
NdisUpdateOffload
NdisUpdateSharedMemory
NdisWaitEvent
NdisWdfAsyncPowerReferenceCompleteNotification
NdisWdfChangeSingleInstance
NdisWdfCloseIrpHandler
NdisWdfCreateIrpHandler
NdisWdfDeregisterCx
NdisWdfDeviceControlIrpHandler
NdisWdfDeviceInternalControlIrpHandler
NdisWdfExecuteMethod
NdisWdfGenerateFdoNameIndex
NdisWdfGetAdapterContextFromAdapterHandle
NdisWdfGetGuidToOidMap
NdisWdfMiniportDataPathPause
NdisWdfMiniportDataPathStart
NdisWdfMiniportDereference
NdisWdfMiniportSetPower
NdisWdfMiniportStarted
NdisWdfMiniportTryReference
NdisWdfPnPAddDevice
NdisWdfPnpPowerEventHandler
NdisWdfQueryAllData
NdisWdfQuerySingleInstance
NdisWdfReadConfiguration
NdisWdfRegisterCx
NdisWdfRegisterMiniportDriver
NdisWriteConfiguration
NdisWriteErrorLogEntry
NdisWriteEventLogEntry
NdisWritePciSlotInformation
NdisWritePcmciaAttributeMemory
NetDmaAllocateChannel
NetDmaChainCopyPhysicalToVirtual
NetDmaChainCopyVirtualToVirtual
NetDmaDeregisterClient
NetDmaDeregisterProvider
NetDmaEnumerateDmaProviders
NetDmaFlushPendingDescriptors
NetDmaFreeChannel
NetDmaGetMaxPendingDescriptors
NetDmaGetVersion
NetDmaInterruptDpc
NetDmaIsDmaCopyComplete
NetDmaIsr
NetDmaNullTransfer
NetDmaPnPEventNotify
NetDmaPrefetchNextDescriptor
NetDmaProviderStart
NetDmaProviderStop
NetDmaRegisterClient
NetDmaRegisterProvider
NetDmaSetMaxPendingDescriptors
TrFilterDprIndicateReceive
TrFilterDprIndicateReceiveComplete
