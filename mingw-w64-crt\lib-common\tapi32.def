; 
; Exports of file TAPI32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY TAPI32.dll
EXPORTS
GetTapi16CallbackMsg
LAddrParamsInited
LOpenDialAsst
LocWizardDlgProc
MMCAddProvider
MMCConfigProvider
MMCGetAvailableProviders
MMCGetDeviceFlags
MMCGetLineInfo
MMCGetLineStatus
MMCGetPhoneInfo
MMCGetPhoneStatus
MMCGetProviderList
MMCGetServerConfig
MMCInitialize
MMCRemoveProvider
MMCSetLineInfo
MMCSetPhoneInfo
MMCSetServerConfig
MMCShutdown
NonAsyncEventThread
TAPIWndProc
TUISPIDLLCallback
internalConfig
internalCreateDefLocation
internalNewLocationW
internalPerformance
internalRemoveLocation
internalRenameLocationW
lineAccept
lineAddProvider
lineAddProviderA
lineAddProviderW
lineAddToConference
lineAgentSpecific
lineAnswer
lineBlindTransfer
lineBlindTransferA
lineBlindTransferW
lineClose
lineCompleteCall
lineCompleteTransfer
lineConfigDialog
lineConfigDialogA
lineConfigDialogEdit
lineConfigDialogEditA
lineConfigDialogEditW
lineConfigDialogW
lineConfigProvider
lineCreateAgentA
lineCreateAgentSessionA
lineCreateAgentSessionW
lineCreateAgentW
lineDeallocateCall
lineDevSpecific
lineDevSpecificFeature
lineDial
lineDialA
lineDialW
lineDrop
lineForward
lineForwardA
lineForwardW
lineGatherDigits
lineGatherDigitsA
lineGatherDigitsW
lineGenerateDigits
lineGenerateDigitsA
lineGenerateDigitsW
lineGenerateTone
lineGetAddressCaps
lineGetAddressCapsA
lineGetAddressCapsW
lineGetAddressID
lineGetAddressIDA
lineGetAddressIDW
lineGetAddressStatus
lineGetAddressStatusA
lineGetAddressStatusW
lineGetAgentActivityListA
lineGetAgentActivityListW
lineGetAgentCapsA
lineGetAgentCapsW
lineGetAgentGroupListA
lineGetAgentGroupListW
lineGetAgentInfo
lineGetAgentSessionInfo
lineGetAgentSessionList
lineGetAgentStatusA
lineGetAgentStatusW
lineGetAppPriority
lineGetAppPriorityA
lineGetAppPriorityW
lineGetCallInfo
lineGetCallInfoA
lineGetCallInfoW
lineGetCallStatus
lineGetConfRelatedCalls
lineGetCountry
lineGetCountryA
lineGetCountryW
lineGetDevCaps
lineGetDevCapsA
lineGetDevCapsW
lineGetDevConfig
lineGetDevConfigA
lineGetDevConfigW
lineGetGroupListA
lineGetGroupListW
lineGetID
lineGetIDA
lineGetIDW
lineGetIcon
lineGetIconA
lineGetIconW
lineGetLineDevStatus
lineGetLineDevStatusA
lineGetLineDevStatusW
lineGetMessage
lineGetNewCalls
lineGetNumRings
lineGetProviderList
lineGetProviderListA
lineGetProviderListW
lineGetProxyStatus
lineGetQueueInfo
lineGetQueueListA
lineGetQueueListW
lineGetRequest
lineGetRequestA
lineGetRequestW
lineGetStatusMessages
lineGetTranslateCaps
lineGetTranslateCapsA
lineGetTranslateCapsW
lineHandoff
lineHandoffA
lineHandoffW
lineHold
lineInitialize
lineInitializeExA
lineInitializeExW
lineMakeCall
lineMakeCallA
lineMakeCallW
lineMonitorDigits
lineMonitorMedia
lineMonitorTones
lineNegotiateAPIVersion
lineNegotiateExtVersion
lineOpen
lineOpenA
lineOpenW
linePark
lineParkA
lineParkW
linePickup
linePickupA
linePickupW
linePrepareAddToConference
linePrepareAddToConferenceA
linePrepareAddToConferenceW
lineProxyMessage
lineProxyResponse
lineRedirect
lineRedirectA
lineRedirectW
lineRegisterRequestRecipient
lineReleaseUserUserInfo
lineRemoveFromConference
lineRemoveProvider
lineSecureCall
lineSendUserUserInfo
lineSetAgentActivity
lineSetAgentGroup
lineSetAgentMeasurementPeriod
lineSetAgentSessionState
lineSetAgentState
lineSetAgentStateEx
lineSetAppPriority
lineSetAppPriorityA
lineSetAppPriorityW
lineSetAppSpecific
lineSetCallData
lineSetCallParams
lineSetCallPrivilege
lineSetCallQualityOfService
lineSetCallTreatment
lineSetCurrentLocation
lineSetDevConfig
lineSetDevConfigA
lineSetDevConfigW
lineSetLineDevStatus
lineSetMediaControl
lineSetMediaMode
lineSetNumRings
lineSetQueueMeasurementPeriod
lineSetStatusMessages
lineSetTerminal
lineSetTollList
lineSetTollListA
lineSetTollListW
lineSetupConference
lineSetupConferenceA
lineSetupConferenceW
lineSetupTransfer
lineSetupTransferA
lineSetupTransferW
lineShutdown
lineSwapHold
lineTranslateAddress
lineTranslateAddressA
lineTranslateAddressW
lineTranslateDialog
lineTranslateDialogA
lineTranslateDialogW
lineUncompleteCall
lineUnhold
lineUnpark
lineUnparkA
lineUnparkW
phoneClose
phoneConfigDialog
phoneConfigDialogA
phoneConfigDialogW
phoneDevSpecific
phoneGetButtonInfo
phoneGetButtonInfoA
phoneGetButtonInfoW
phoneGetData
phoneGetDevCaps
phoneGetDevCapsA
phoneGetDevCapsW
phoneGetDisplay
phoneGetGain
phoneGetHookSwitch
phoneGetID
phoneGetIDA
phoneGetIDW
phoneGetIcon
phoneGetIconA
phoneGetIconW
phoneGetLamp
phoneGetMessage
phoneGetRing
phoneGetStatus
phoneGetStatusA
phoneGetStatusMessages
phoneGetStatusW
phoneGetVolume
phoneInitialize
phoneInitializeExA
phoneInitializeExW
phoneNegotiateAPIVersion
phoneNegotiateExtVersion
phoneOpen
phoneSetButtonInfo
phoneSetButtonInfoA
phoneSetButtonInfoW
phoneSetData
phoneSetDisplay
phoneSetGain
phoneSetHookSwitch
phoneSetLamp
phoneSetRing
phoneSetStatusMessages
phoneSetVolume
phoneShutdown
tapiGetLocationInfo
tapiGetLocationInfoA
tapiGetLocationInfoW
tapiRequestDrop
tapiRequestMakeCall
tapiRequestMakeCallA
tapiRequestMakeCallW
tapiRequestMediaCall
tapiRequestMediaCallA
tapiRequestMediaCallW
