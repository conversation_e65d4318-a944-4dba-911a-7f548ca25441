;
; Definition file of computecore.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "computecore.dll"
EXPORTS
HcsEnumerateVmWorkerProcesses
HcsFindVmWorkerProcesses
HcsGetWorkerProcessJob
HcsStartVmWorkerProcess
HcsAddResourceToOperation
HcsCancelOperation
HcsCloseComputeSystem
HcsCloseOperation
HcsCloseProcess
HcsCrashComputeSystem
HcsCreateComputeSystem
HcsCreateComputeSystemInNamespace
HcsCreateEmptyGuestStateFile
HcsCreateEmptyRuntimeStateFile
HcsCreateOperation
HcsCreateOperationWithNotifications
HcsCreateProcess
HcsEnumerateComputeSystems
HcsEnumerateComputeSystemsInNamespace
HcsGetComputeSystemFromOperation
HcsGetComputeSystemProperties
HcsGetOperationContext
HcsGetOperationId
HcsGetOperationProperties
H<PERSON>Result
HcsGetOperationResultAndProcessInfo
HcsGetOperationType
HcsGetProcessFromOperation
HcsGetProcessInfo
HcsGetProcessProperties
HcsGetProcessorCompatibilityFromSavedState
HcsGetServiceProperties
HcsGrantVmAccess
HcsGrantVmGroupAccess
HcsModifyComputeSystem
HcsModifyProcess
HcsModifyServiceSettings
HcsOpenComputeSystem
HcsOpenComputeSystemInNamespace
HcsOpenProcess
HcsPauseComputeSystem
HcsResumeComputeSystem
HcsRevokeVmAccess
HcsRevokeVmGroupAccess
HcsSaveComputeSystem
HcsSetComputeSystemCallback
HcsSetOperationCallback
HcsSetOperationContext
HcsSetProcessCallback
HcsShutDownComputeSystem
HcsSignalProcess
HcsStartComputeSystem
HcsSubmitWerReport
HcsSystemControl
HcsTerminateComputeSystem
HcsTerminateProcess
HcsWaitForComputeSystemExit
HcsWaitForOperationResult
HcsWaitForOperationResultAndProcessInfo
HcsWaitForProcessExit
