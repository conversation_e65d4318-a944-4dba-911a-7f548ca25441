/*
 * Copyright 2016 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "dxgi.idl";
import "d3dcommon.idl";

cpp_quote("#ifndef _D3D12_CONSTANTS")
cpp_quote("#define _D3D12_CONSTANTS")

const UINT D3D12_CS_TGSM_REGISTER_COUNT = 8192;
const UINT D3D12_MAX_ROOT_COST = 64;
const UINT D3D12_VIEWPORT_BOUNDS_MAX = 32767;
const UINT D3D12_VIEWPORT_BOUNDS_MIN = -32768;

const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COUNT = 15;

const UINT D3D12_APPEND_ALIGNED_ELEMENT = 0xffffffff;
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_ALPHA (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_BLUE (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_GREEN (1.0f)")
cpp_quote("#define D3D12_DEFAULT_BLEND_FACTOR_RED (1.0f)")
const UINT D3D12_DEFAULT_DEPTH_BIAS = 0;
cpp_quote("#define D3D12_DEFAULT_DEPTH_BIAS_CLAMP (0.0f)")
cpp_quote("#define D3D12_DEFAULT_SLOPE_SCALED_DEPTH_BIAS (0.0f)")
const UINT D3D12_DEFAULT_STENCIL_READ_MASK = 0xff;
const UINT D3D12_DEFAULT_STENCIL_WRITE_MASK = 0xff;
const UINT D3D12_DESCRIPTOR_RANGE_OFFSET_APPEND = 0xffffffff;
cpp_quote("#define D3D12_FLOAT32_MAX (3.402823466e+38f)")
const UINT D3D12_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT = 32;
const UINT D3D12_UAV_SLOT_COUNT = 64;
const UINT D3D12_REQ_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D12_REQ_IMMEDIATE_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D12_REQ_MIP_LEVELS = 15;
const UINT D3D12_REQ_TEXTURE1D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURE1D_U_DIMENSION = 16384;
const UINT D3D12_REQ_TEXTURE2D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURE2D_U_OR_V_DIMENSION = 16384;
const UINT D3D12_REQ_TEXTURE3D_U_V_OR_W_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURECUBE_DIMENSION = 16384;
const UINT D3D12_RESOURCE_BARRIER_ALL_SUBRESOURCES = 0xffffffff;
const UINT D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT = 8;
const UINT D3D12_SO_BUFFER_MAX_STRIDE_IN_BYTES = 2048;
const UINT D3D12_SO_BUFFER_SLOT_COUNT = 4;
const UINT D3D12_SO_DDI_REGISTER_INDEX_DENOTING_GAP = 0xffffffff;
const UINT D3D12_SO_NO_RASTERIZED_STREAM = 0xffffffff;
const UINT D3D12_SO_OUTPUT_COMPONENT_COUNT = 128;
const UINT D3D12_SO_STREAM_COUNT = 4;
const UINT D3D12_CONSTANT_BUFFER_DATA_PLACEMENT_ALIGNMENT = 256;
const UINT D3D12_DEFAULT_MSAA_RESOURCE_PLACEMENT_ALIGNMENT = 4194304;
const UINT D3D12_DEFAULT_RESOURCE_PLACEMENT_ALIGNMENT = 65536;
const UINT D3D12_RAW_UAV_SRV_BYTE_ALIGNMENT = 16;
const UINT D3D12_SDK_VERSION = 4;
const UINT D3D12_SMALL_MSAA_RESOURCE_PLACEMENT_ALIGNMENT = 65536;
const UINT D3D12_SMALL_RESOURCE_PLACEMENT_ALIGNMENT = 4096;
const UINT D3D12_STANDARD_MAXIMUM_ELEMENT_ALIGNMENT_BYTE_MULTIPLE = 4;
const UINT D3D12_TEXTURE_DATA_PITCH_ALIGNMENT = 256;
const UINT D3D12_TEXTURE_DATA_PLACEMENT_ALIGNMENT = 512;
const UINT D3D12_UAV_COUNTER_PLACEMENT_ALIGNMENT = 4096;
const UINT D3D12_VS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE = 16;

cpp_quote("#endif")

const UINT D3D12_SHADER_COMPONENT_MAPPING_MASK = 0x7;
const UINT D3D12_SHADER_COMPONENT_MAPPING_SHIFT = 3;
const UINT D3D12_SHADER_COMPONENT_MAPPING_ALWAYS_SET_BIT_AVOIDING_ZEROMEM_MISTAKES
        = 1 << (D3D12_SHADER_COMPONENT_MAPPING_SHIFT * 4);

typedef enum D3D12_SHADER_MIN_PRECISION_SUPPORT
{
    D3D12_SHADER_MIN_PRECISION_SUPPORT_NONE = 0x0,
    D3D12_SHADER_MIN_PRECISION_SUPPORT_10_BIT = 0x1,
    D3D12_SHADER_MIN_PRECISION_SUPPORT_16_BIT = 0x2,
} D3D12_SHADER_MIN_PRECISION_SUPPORT;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_MIN_PRECISION_SUPPORT);")

typedef enum D3D12_TILED_RESOURCES_TIER
{
    D3D12_TILED_RESOURCES_TIER_NOT_SUPPORTED = 0,
    D3D12_TILED_RESOURCES_TIER_1 = 1,
    D3D12_TILED_RESOURCES_TIER_2 = 2,
    D3D12_TILED_RESOURCES_TIER_3 = 3,
} D3D12_TILED_RESOURCES_TIER;

typedef enum D3D12_RESOURCE_BINDING_TIER
{
    D3D12_RESOURCE_BINDING_TIER_1 = 1,
    D3D12_RESOURCE_BINDING_TIER_2 = 2,
    D3D12_RESOURCE_BINDING_TIER_3 = 3,
} D3D12_RESOURCE_BINDING_TIER;

typedef enum D3D12_CONSERVATIVE_RASTERIZATION_TIER
{
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_NOT_SUPPORTED = 0,
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_1 = 1,
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_2 = 2,
    D3D12_CONSERVATIVE_RASTERIZATION_TIER_3 = 3,
} D3D12_CONSERVATIVE_RASTERIZATION_TIER;

typedef enum D3D12_CROSS_NODE_SHARING_TIER
{
    D3D12_CROSS_NODE_SHARING_TIER_NOT_SUPPORTED = 0,
    D3D12_CROSS_NODE_SHARING_TIER_1_EMULATED = 1,
    D3D12_CROSS_NODE_SHARING_TIER_1 = 2,
    D3D12_CROSS_NODE_SHARING_TIER_2 = 3,
} D3D12_CROSS_NODE_SHARING_TIER;

typedef enum D3D12_RESOURCE_HEAP_TIER
{
    D3D12_RESOURCE_HEAP_TIER_1 = 1,
    D3D12_RESOURCE_HEAP_TIER_2 = 2,
} D3D12_RESOURCE_HEAP_TIER;

typedef enum D3D12_FORMAT_SUPPORT1
{
    D3D12_FORMAT_SUPPORT1_NONE = 0x00000000,
    D3D12_FORMAT_SUPPORT1_BUFFER = 0x00000001,
    D3D12_FORMAT_SUPPORT1_IA_VERTEX_BUFFER = 0x00000002,
    D3D12_FORMAT_SUPPORT1_IA_INDEX_BUFFER = 0x00000004,
    D3D12_FORMAT_SUPPORT1_SO_BUFFER = 0x00000008,
    D3D12_FORMAT_SUPPORT1_TEXTURE1D = 0x00000010,
    D3D12_FORMAT_SUPPORT1_TEXTURE2D = 0x00000020,
    D3D12_FORMAT_SUPPORT1_TEXTURE3D = 0x00000040,
    D3D12_FORMAT_SUPPORT1_TEXTURECUBE = 0x00000080,
    D3D12_FORMAT_SUPPORT1_SHADER_LOAD = 0x00000100,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE = 0x00000200,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_COMPARISON = 0x00000400,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_MONO_TEXT = 0x00000800,
    D3D12_FORMAT_SUPPORT1_MIP = 0x00001000,
    D3D12_FORMAT_SUPPORT1_RENDER_TARGET = 0x00004000,
    D3D12_FORMAT_SUPPORT1_BLENDABLE = 0x00008000,
    D3D12_FORMAT_SUPPORT1_DEPTH_STENCIL = 0x00010000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RESOLVE = 0x00040000,
    D3D12_FORMAT_SUPPORT1_DISPLAY = 0x00080000,
    D3D12_FORMAT_SUPPORT1_CAST_WITHIN_BIT_LAYOUT = 0x00100000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RENDERTARGET = 0x00200000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_LOAD = 0x00400000,
    D3D12_FORMAT_SUPPORT1_SHADER_GATHER = 0x00800000,
    D3D12_FORMAT_SUPPORT1_BACK_BUFFER_CAST = 0x01000000,
    D3D12_FORMAT_SUPPORT1_TYPED_UNORDERED_ACCESS_VIEW = 0x02000000,
    D3D12_FORMAT_SUPPORT1_SHADER_GATHER_COMPARISON = 0x04000000,
    D3D12_FORMAT_SUPPORT1_DECODER_OUTPUT = 0x08000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_OUTPUT = 0x10000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_INPUT = 0x20000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_ENCODER = 0x40000000,
} D3D12_FORMAT_SUPPORT1;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_FORMAT_SUPPORT1);")

typedef enum D3D12_FORMAT_SUPPORT2
{
    D3D12_FORMAT_SUPPORT2_NONE = 0x00000000,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_ADD = 0x00000001,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS = 0x00000002,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE = 0x00000004,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE = 0x00000008,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX = 0x00000010,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX = 0x00000020,
    D3D12_FORMAT_SUPPORT2_UAV_TYPED_LOAD = 0x00000040,
    D3D12_FORMAT_SUPPORT2_UAV_TYPED_STORE = 0x00000080,
    D3D12_FORMAT_SUPPORT2_OUTPUT_MERGER_LOGIC_OP = 0x00000100,
    D3D12_FORMAT_SUPPORT2_TILED = 0x00000200,
    D3D12_FORMAT_SUPPORT2_MULTIPLANE_OVERLAY = 0x00004000,
} D3D12_FORMAT_SUPPORT2;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_FORMAT_SUPPORT2);")

typedef enum D3D12_WRITEBUFFERIMMEDIATE_MODE
{
    D3D12_WRITEBUFFERIMMEDIATE_MODE_DEFAULT = 0x0,
    D3D12_WRITEBUFFERIMMEDIATE_MODE_MARKER_IN = 0x1,
    D3D12_WRITEBUFFERIMMEDIATE_MODE_MARKER_OUT = 0x2,
} D3D12_WRITEBUFFERIMMEDIATE_MODE;

typedef enum D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER
{
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_NOT_SUPPORTED = 0x0,
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_1 = 0x1,
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_2 = 0x2,
}  D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER;

typedef enum D3D12_SHADER_CACHE_SUPPORT_FLAGS
{
    D3D12_SHADER_CACHE_SUPPORT_NONE = 0x0,
    D3D12_SHADER_CACHE_SUPPORT_SINGLE_PSO = 0x1,
    D3D12_SHADER_CACHE_SUPPORT_LIBRARY = 0x2,
    D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_INPROC_CACHE = 0x4,
    D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_DISK_CACHE = 0x8,
}  D3D12_SHADER_CACHE_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_SHADER_CACHE_SUPPORT_FLAGS);")

typedef enum D3D12_COMMAND_LIST_SUPPORT_FLAGS
{
    D3D12_COMMAND_LIST_SUPPORT_FLAG_NONE = 0x0,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_DIRECT = 0x1,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_BUNDLE = 0x2,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_COMPUTE = 0x4,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_COPY = 0x8,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_DECODE = 0x10,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_PROCESS = 0x20,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_ENCODE = 0x40,
} D3D12_COMMAND_LIST_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_LIST_SUPPORT_FLAGS);")

typedef enum D3D12_VIEW_INSTANCING_TIER
{
    D3D12_VIEW_INSTANCING_TIER_NOT_SUPPORTED = 0x0,
    D3D12_VIEW_INSTANCING_TIER_1 = 0x1,
    D3D12_VIEW_INSTANCING_TIER_2 = 0x2,
    D3D12_VIEW_INSTANCING_TIER_3 = 0x3,
}  D3D12_VIEW_INSTANCING_TIER;

typedef enum D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER
{
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 = 0x0,
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1 = 0x1,
}  D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER;

typedef enum D3D12_HEAP_SERIALIZATION_TIER
{
    D3D12_HEAP_SERIALIZATION_TIER_0 = 0x0,
    D3D12_HEAP_SERIALIZATION_TIER_10 = 0xa,
}  D3D12_HEAP_SERIALIZATION_TIER;

typedef enum D3D12_RENDER_PASS_TIER
{
    D3D12_RENDER_PASS_TIER_0 = 0x0,
    D3D12_RENDER_PASS_TIER_1 = 0x1,
    D3D12_RENDER_PASS_TIER_2 = 0x2,
} D3D12_RENDER_PASS_TIER;

typedef enum D3D12_RAYTRACING_TIER
{
    D3D12_RAYTRACING_TIER_NOT_SUPPORTED = 0x0,
    D3D12_RAYTRACING_TIER_1_0 = 0xa,
} D3D12_RAYTRACING_TIER;

interface ID3D12Fence;
interface ID3D12RootSignature;
interface ID3D12Heap;
interface ID3D12DescriptorHeap;
interface ID3D12Resource;
interface ID3D12CommandAllocator;
interface ID3D12GraphicsCommandList;
interface ID3D12CommandQueue;
interface ID3D12PipelineState;
interface ID3D12Device;

typedef RECT D3D12_RECT;

typedef struct D3D12_BOX
{
    UINT left;
    UINT top;
    UINT front;
    UINT right;
    UINT bottom;
    UINT back;
} D3D12_BOX;

typedef struct D3D12_VIEWPORT
{
    FLOAT TopLeftX;
    FLOAT TopLeftY;
    FLOAT Width;
    FLOAT Height;
    FLOAT MinDepth;
    FLOAT MaxDepth;
} D3D12_VIEWPORT;

typedef struct D3D12_RANGE
{
    SIZE_T Begin;
    SIZE_T End;
} D3D12_RANGE;

typedef struct D3D12_RANGE_UINT64
{
    UINT64 Begin;
    UINT64 End;
} D3D12_RANGE_UINT64;

typedef struct D3D12_SUBRESOURCE_RANGE_UINT64
{
    UINT Subresource;
    D3D12_RANGE_UINT64 Range;
} D3D12_SUBRESOURCE_RANGE_UINT64;

typedef struct D3D12_RESOURCE_ALLOCATION_INFO
{
    UINT64 SizeInBytes;
    UINT64 Alignment;
} D3D12_RESOURCE_ALLOCATION_INFO;

typedef struct D3D12_DRAW_ARGUMENTS
{
    UINT VertexCountPerInstance;
    UINT InstanceCount;
    UINT StartVertexLocation;
    UINT StartInstanceLocation;
} D3D12_DRAW_ARGUMENTS;

typedef struct D3D12_DRAW_INDEXED_ARGUMENTS
{
    UINT IndexCountPerInstance;
    UINT InstanceCount;
    UINT StartIndexLocation;
    INT BaseVertexLocation;
    UINT StartInstanceLocation;
} D3D12_DRAW_INDEXED_ARGUMENTS;

typedef struct D3D12_DISPATCH_ARGUMENTS
{
    UINT ThreadGroupCountX;
    UINT ThreadGroupCountY;
    UINT ThreadGroupCountZ;
} D3D12_DISPATCH_ARGUMENTS;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS
{
    BOOL DoublePrecisionFloatShaderOps;
    BOOL OutputMergerLogicOp;
    D3D12_SHADER_MIN_PRECISION_SUPPORT MinPrecisionSupport;
    D3D12_TILED_RESOURCES_TIER TiledResourcesTier;
    D3D12_RESOURCE_BINDING_TIER ResourceBindingTier;
    BOOL PSSpecifiedStencilRefSupported;
    BOOL TypedUAVLoadAdditionalFormats;
    BOOL ROVsSupported;
    D3D12_CONSERVATIVE_RASTERIZATION_TIER ConservativeRasterizationTier;
    UINT MaxGPUVirtualAddressBitsPerResource;
    BOOL StandardSwizzle64KBSupported;
    D3D12_CROSS_NODE_SHARING_TIER CrossNodeSharingTier;
    BOOL CrossAdapterRowMajorTextureSupported;
    BOOL VPAndRTArrayIndexFromAnyShaderFeedingRasterizerSupportedWithoutGSEmulation;
    D3D12_RESOURCE_HEAP_TIER ResourceHeapTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS;

typedef struct D3D12_FEATURE_DATA_FORMAT_SUPPORT
{
    DXGI_FORMAT Format;
    D3D12_FORMAT_SUPPORT1 Support1;
    D3D12_FORMAT_SUPPORT2 Support2;
} D3D12_FEATURE_DATA_FORMAT_SUPPORT;

typedef enum D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS
{
    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_NONE = 0x00000000,
    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_TILED_RESOURCE = 0x00000001,
} D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS);")

typedef struct D3D12_FEATURE_DATA_MULTISAMPLE_QUALITY_LEVELS
{
    DXGI_FORMAT Format;
    UINT SampleCount;
    D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS Flags;
    UINT NumQualityLevels;
} D3D12_FEATURE_DATA_MULTISAMPLE_QUALITY_LEVELS;

typedef enum D3D12_HEAP_TYPE
{
    D3D12_HEAP_TYPE_DEFAULT = 1,
    D3D12_HEAP_TYPE_UPLOAD = 2,
    D3D12_HEAP_TYPE_READBACK = 3,
    D3D12_HEAP_TYPE_CUSTOM = 4,
} D3D12_HEAP_TYPE;

typedef enum D3D12_CPU_PAGE_PROPERTY
{
    D3D12_CPU_PAGE_PROPERTY_UNKNOWN = 0,
    D3D12_CPU_PAGE_PROPERTY_NOT_AVAILABLE = 1,
    D3D12_CPU_PAGE_PROPERTY_WRITE_COMBINE = 2,
    D3D12_CPU_PAGE_PROPERTY_WRITE_BACK = 3,
} D3D12_CPU_PAGE_PROPERTY;

typedef enum D3D12_MEMORY_POOL
{
    D3D12_MEMORY_POOL_UNKNOWN = 0,
    D3D12_MEMORY_POOL_L0 = 1,
    D3D12_MEMORY_POOL_L1 = 2,
} D3D12_MEMORY_POOL;

typedef struct D3D12_HEAP_PROPERTIES
{
    D3D12_HEAP_TYPE Type;
    D3D12_CPU_PAGE_PROPERTY CPUPageProperty;
    D3D12_MEMORY_POOL MemoryPoolPreference;
    UINT CreationNodeMask;
    UINT VisibleNodeMask;
} D3D12_HEAP_PROPERTIES;

typedef enum D3D12_HEAP_FLAGS
{
    D3D12_HEAP_FLAG_NONE = 0x00,
    D3D12_HEAP_FLAG_SHARED = 0x01,
    D3D12_HEAP_FLAG_DENY_BUFFERS = 0x04,
    D3D12_HEAP_FLAG_ALLOW_DISPLAY = 0x08,
    D3D12_HEAP_FLAG_SHARED_CROSS_ADAPTER = 0x20,
    D3D12_HEAP_FLAG_DENY_RT_DS_TEXTURES = 0x40,
    D3D12_HEAP_FLAG_DENY_NON_RT_DS_TEXTURES = 0x80,
    D3D12_HEAP_FLAG_ALLOW_ALL_BUFFERS_AND_TEXTURES = 0x00,
    D3D12_HEAP_FLAG_ALLOW_ONLY_BUFFERS = 0xc0,
    D3D12_HEAP_FLAG_ALLOW_ONLY_NON_RT_DS_TEXTURES = 0x44,
    D3D12_HEAP_FLAG_ALLOW_ONLY_RT_DS_TEXTURES = 0x84,
} D3D12_HEAP_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_HEAP_FLAGS);")

typedef struct D3D12_HEAP_DESC
{
    UINT64 SizeInBytes;
    D3D12_HEAP_PROPERTIES Properties;
    UINT64 Alignment;
    D3D12_HEAP_FLAGS Flags;
} D3D12_HEAP_DESC;

typedef struct D3D12_TILED_RESOURCE_COORDINATE
{
    UINT X;
    UINT Y;
    UINT Z;
    UINT Subresource;
} D3D12_TILED_RESOURCE_COORDINATE;

typedef struct D3D12_TILE_REGION_SIZE
{
    UINT NumTiles;
    BOOL UseBox;
    UINT Width;
    UINT16 Height;
    UINT16 Depth;
} D3D12_TILE_REGION_SIZE;

typedef struct D3D12_SUBRESOURCE_TILING
{
    UINT WidthInTiles;
    UINT16 HeightInTiles;
    UINT16 DepthInTiles;
    UINT StartTileIndexInOverallResource;
} D3D12_SUBRESOURCE_TILING;

typedef struct D3D12_TILE_SHAPE
{
    UINT WidthInTexels;
    UINT HeightInTexels;
    UINT DepthInTexels;
} D3D12_TILE_SHAPE;

typedef struct D3D12_SHADER_BYTECODE
{
    const void *pShaderBytecode;
    SIZE_T BytecodeLength;
} D3D12_SHADER_BYTECODE;

typedef struct D3D12_DEPTH_STENCIL_VALUE
{
    FLOAT Depth;
    UINT8 Stencil;
} D3D12_DEPTH_STENCIL_VALUE;

typedef struct D3D12_CLEAR_VALUE
{
    DXGI_FORMAT Format;
    union
    {
        FLOAT Color[4];
        D3D12_DEPTH_STENCIL_VALUE DepthStencil;
    };
} D3D12_CLEAR_VALUE;

typedef struct D3D12_PACKED_MIP_INFO
{
    UINT8 NumStandardMips;
    UINT8 NumPackedMips;
    UINT NumTilesForPackedMips;
    UINT StartTileIndexInOverallResource;
} D3D12_PACKED_MIP_INFO;

typedef enum D3D12_RESOURCE_STATES
{
    D3D12_RESOURCE_STATE_COMMON = 0,
    D3D12_RESOURCE_STATE_VERTEX_AND_CONSTANT_BUFFER = 0x1,
    D3D12_RESOURCE_STATE_INDEX_BUFFER = 0x2,
    D3D12_RESOURCE_STATE_RENDER_TARGET = 0x4,
    D3D12_RESOURCE_STATE_UNORDERED_ACCESS = 0x8,
    D3D12_RESOURCE_STATE_DEPTH_WRITE = 0x10,
    D3D12_RESOURCE_STATE_DEPTH_READ = 0x20,
    D3D12_RESOURCE_STATE_NON_PIXEL_SHADER_RESOURCE = 0x40,
    D3D12_RESOURCE_STATE_PIXEL_SHADER_RESOURCE = 0x80,
    D3D12_RESOURCE_STATE_STREAM_OUT = 0x100,
    D3D12_RESOURCE_STATE_INDIRECT_ARGUMENT = 0x200,
    D3D12_RESOURCE_STATE_COPY_DEST = 0x400,
    D3D12_RESOURCE_STATE_COPY_SOURCE = 0x800,
    D3D12_RESOURCE_STATE_RESOLVE_DEST = 0x1000,
    D3D12_RESOURCE_STATE_RESOLVE_SOURCE = 0x2000,
    D3D12_RESOURCE_STATE_GENERIC_READ = 0x1 | 0x2 | 0x40 | 0x80 | 0x200 | 0x800,
    D3D12_RESOURCE_STATE_PRESENT = 0x0,
    D3D12_RESOURCE_STATE_PREDICATION = 0x200,
    D3D12_RESOURCE_STATE_VIDEO_DECODE_READ = 0x10000,
    D3D12_RESOURCE_STATE_VIDEO_DECODE_WRITE = 0x20000,
    D3D12_RESOURCE_STATE_VIDEO_PROCESS_READ = 0x40000,
    D3D12_RESOURCE_STATE_VIDEO_PROCESS_WRITE = 0x80000,
    D3D12_RESOURCE_STATE_VIDEO_ENCODE_READ = 0x200000,
    D3D12_RESOURCE_STATE_VIDEO_ENCODE_WRITE = 0x800000,
} D3D12_RESOURCE_STATES;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESOURCE_STATES);")

typedef enum D3D12_RESOURCE_BARRIER_TYPE
{
    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION = 0,
    D3D12_RESOURCE_BARRIER_TYPE_ALIASING = 1,
    D3D12_RESOURCE_BARRIER_TYPE_UAV = 2,
} D3D12_RESOURCE_BARRIER_TYPE;

typedef enum D3D12_RESOURCE_BARRIER_FLAGS
{
    D3D12_RESOURCE_BARRIER_FLAG_NONE = 0x0,
    D3D12_RESOURCE_BARRIER_FLAG_BEGIN_ONLY = 0x1,
    D3D12_RESOURCE_BARRIER_FLAG_END_ONLY = 0x2,
} D3D12_RESOURCE_BARRIER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESOURCE_BARRIER_FLAGS);")

typedef struct D3D12_RESOURCE_TRANSITION_BARRIER
{
    ID3D12Resource *pResource;
    UINT Subresource;
    D3D12_RESOURCE_STATES StateBefore;
    D3D12_RESOURCE_STATES StateAfter;
} D3D12_RESOURCE_TRANSITION_BARRIER;

typedef struct D3D12_RESOURCE_ALIASING_BARRIER_ALIASING
{
    ID3D12Resource *pResourceBefore;
    ID3D12Resource *pResourceAfter;
} D3D12_RESOURCE_ALIASING_BARRIER;

typedef struct D3D12_RESOURCE_UAV_BARRIER
{
    ID3D12Resource *pResource;
} D3D12_RESOURCE_UAV_BARRIER;

typedef struct D3D12_RESOURCE_BARRIER
{
    D3D12_RESOURCE_BARRIER_TYPE Type;
    D3D12_RESOURCE_BARRIER_FLAGS Flags;
    union
    {
        D3D12_RESOURCE_TRANSITION_BARRIER Transition;
        D3D12_RESOURCE_ALIASING_BARRIER Aliasing;
        D3D12_RESOURCE_UAV_BARRIER UAV;
    };
} D3D12_RESOURCE_BARRIER;

typedef enum D3D12_RESOURCE_DIMENSION
{
    D3D12_RESOURCE_DIMENSION_UNKNOWN = 0,
    D3D12_RESOURCE_DIMENSION_BUFFER = 1,
    D3D12_RESOURCE_DIMENSION_TEXTURE1D = 2,
    D3D12_RESOURCE_DIMENSION_TEXTURE2D = 3,
    D3D12_RESOURCE_DIMENSION_TEXTURE3D = 4,
} D3D12_RESOURCE_DIMENSION;

typedef enum D3D12_TEXTURE_LAYOUT
{
    D3D12_TEXTURE_LAYOUT_UNKNOWN = 0,
    D3D12_TEXTURE_LAYOUT_ROW_MAJOR = 1,
    D3D12_TEXTURE_LAYOUT_64KB_UNDEFINED_SWIZZLE = 2,
    D3D12_TEXTURE_LAYOUT_64KB_STANDARD_SWIZZLE = 3,
} D3D12_TEXTURE_LAYOUT;

typedef enum D3D12_RESOURCE_FLAGS
{
    D3D12_RESOURCE_FLAG_NONE = 0x0,
    D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET = 0x1,
    D3D12_RESOURCE_FLAG_ALLOW_DEPTH_STENCIL = 0x2,
    D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS = 0x4,
    D3D12_RESOURCE_FLAG_DENY_SHADER_RESOURCE = 0x8,
    D3D12_RESOURCE_FLAG_ALLOW_CROSS_ADAPTER = 0x10,
    D3D12_RESOURCE_FLAG_ALLOW_SIMULTANEOUS_ACCESS = 0x20,
} D3D12_RESOURCE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_RESOURCE_FLAGS);")

typedef struct D3D12_RESOURCE_DESC
{
    D3D12_RESOURCE_DIMENSION Dimension;
    UINT64 Alignment;
    UINT64 Width;
    UINT Height;
    UINT16 DepthOrArraySize;
    UINT16 MipLevels;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D12_TEXTURE_LAYOUT Layout;
    D3D12_RESOURCE_FLAGS Flags;
} D3D12_RESOURCE_DESC;

typedef enum D3D12_RESOLVE_MODE
{
    D3D12_RESOLVE_MODE_DECOMPRESS = 0,
    D3D12_RESOLVE_MODE_MIN = 1,
    D3D12_RESOLVE_MODE_MAX = 2,
    D3D12_RESOLVE_MODE_AVERAGE = 3,
} D3D12_RESOLVE_MODE;

typedef struct D3D12_SAMPLE_POSITION
{
    INT8 X;
    INT8 Y;
} D3D12_SAMPLE_POSITION;

typedef enum D3D12_TEXTURE_COPY_TYPE
{
    D3D12_TEXTURE_COPY_TYPE_SUBRESOURCE_INDEX = 0,
    D3D12_TEXTURE_COPY_TYPE_PLACED_FOOTPRINT = 1,
} D3D12_TEXTURE_COPY_TYPE;

typedef struct D3D12_SUBRESOURCE_FOOTPRINT
{
    DXGI_FORMAT Format;
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT RowPitch;
} D3D12_SUBRESOURCE_FOOTPRINT;

typedef struct D3D12_PLACED_SUBRESOURCE_FOOTPRINT
{
    UINT64 Offset;
    D3D12_SUBRESOURCE_FOOTPRINT Footprint;
} D3D12_PLACED_SUBRESOURCE_FOOTPRINT;

typedef struct D3D12_TEXTURE_COPY_LOCATION
{
    ID3D12Resource *pResource;
    D3D12_TEXTURE_COPY_TYPE Type;
    union
    {
        D3D12_PLACED_SUBRESOURCE_FOOTPRINT PlacedFootprint;
        UINT SubresourceIndex;
    };
} D3D12_TEXTURE_COPY_LOCATION;

typedef enum D3D12_DESCRIPTOR_RANGE_TYPE
{
    D3D12_DESCRIPTOR_RANGE_TYPE_SRV = 0,
    D3D12_DESCRIPTOR_RANGE_TYPE_UAV = 1,
    D3D12_DESCRIPTOR_RANGE_TYPE_CBV = 2,
    D3D12_DESCRIPTOR_RANGE_TYPE_SAMPLER = 3,
} D3D12_DESCRIPTOR_RANGE_TYPE;

typedef struct D3D12_DESCRIPTOR_RANGE
{
    D3D12_DESCRIPTOR_RANGE_TYPE RangeType;
    UINT NumDescriptors;
    UINT BaseShaderRegister;
    UINT RegisterSpace;
    UINT OffsetInDescriptorsFromTableStart;
} D3D12_DESCRIPTOR_RANGE;

typedef enum D3D12_DESCRIPTOR_RANGE_FLAGS
{
    D3D12_DESCRIPTOR_RANGE_FLAG_NONE = 0x0,
    D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_VOLATILE = 0x1,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_VOLATILE = 0x2,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE = 0x4,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC = 0x8,
} D3D12_DESCRIPTOR_RANGE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DESCRIPTOR_RANGE_FLAGS);")

typedef struct D3D12_DESCRIPTOR_RANGE1
{
    D3D12_DESCRIPTOR_RANGE_TYPE RangeType;
    UINT NumDescriptors;
    UINT BaseShaderRegister;
    UINT RegisterSpace;
    D3D12_DESCRIPTOR_RANGE_FLAGS Flags;
    UINT OffsetInDescriptorsFromTableStart;
} D3D12_DESCRIPTOR_RANGE1;

typedef struct D3D12_ROOT_DESCRIPTOR_TABLE
{
    UINT NumDescriptorRanges;
    const D3D12_DESCRIPTOR_RANGE *pDescriptorRanges;
} D3D12_ROOT_DESCRIPTOR_TABLE;

typedef struct D3D12_ROOT_DESCRIPTOR_TABLE1
{
    UINT NumDescriptorRanges;
    const D3D12_DESCRIPTOR_RANGE1 *pDescriptorRanges;
} D3D12_ROOT_DESCRIPTOR_TABLE1;

typedef struct D3D12_ROOT_CONSTANTS
{
    UINT ShaderRegister;
    UINT RegisterSpace;
    UINT Num32BitValues;
} D3D12_ROOT_CONSTANTS;

typedef struct D3D12_ROOT_DESCRIPTOR
{
    UINT ShaderRegister;
    UINT RegisterSpace;
} D3D12_ROOT_DESCRIPTOR;

typedef enum D3D12_ROOT_DESCRIPTOR_FLAGS
{
    D3D12_ROOT_DESCRIPTOR_FLAG_NONE = 0x0,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_VOLATILE = 0x2,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE = 0x4,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC = 0x8,
} D3D12_ROOT_DESCRIPTOR_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_ROOT_DESCRIPTOR_FLAGS);")

typedef struct D3D12_ROOT_DESCRIPTOR1
{
    UINT ShaderRegister;
    UINT RegisterSpace;
    D3D12_ROOT_DESCRIPTOR_FLAGS Flags;
} D3D12_ROOT_DESCRIPTOR1;

typedef enum D3D12_ROOT_PARAMETER_TYPE
{
    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE = 0,
    D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS = 1,
    D3D12_ROOT_PARAMETER_TYPE_CBV = 2,
    D3D12_ROOT_PARAMETER_TYPE_SRV = 3,
    D3D12_ROOT_PARAMETER_TYPE_UAV = 4,
} D3D12_ROOT_PARAMETER_TYPE;

typedef enum D3D12_SHADER_VISIBILITY
{
    D3D12_SHADER_VISIBILITY_ALL = 0,
    D3D12_SHADER_VISIBILITY_VERTEX = 1,
    D3D12_SHADER_VISIBILITY_HULL = 2,
    D3D12_SHADER_VISIBILITY_DOMAIN = 3,
    D3D12_SHADER_VISIBILITY_GEOMETRY = 4,
    D3D12_SHADER_VISIBILITY_PIXEL = 5,
} D3D12_SHADER_VISIBILITY;

typedef struct D3D12_ROOT_PARAMETER
{
    D3D12_ROOT_PARAMETER_TYPE ParameterType;
    union
    {
        D3D12_ROOT_DESCRIPTOR_TABLE DescriptorTable;
        D3D12_ROOT_CONSTANTS Constants;
        D3D12_ROOT_DESCRIPTOR Descriptor;
    };
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_ROOT_PARAMETER;

typedef struct D3D12_ROOT_PARAMETER1
{
    D3D12_ROOT_PARAMETER_TYPE ParameterType;
    union
    {
        D3D12_ROOT_DESCRIPTOR_TABLE1 DescriptorTable;
        D3D12_ROOT_CONSTANTS Constants;
        D3D12_ROOT_DESCRIPTOR1 Descriptor;
    };
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_ROOT_PARAMETER1;

typedef enum D3D12_STATIC_BORDER_COLOR
{
    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK = 0,
    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK = 1,
    D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE = 2,
} D3D12_STATIC_BORDER_COLOR;

typedef enum D3D12_FILTER
{
    D3D12_FILTER_MIN_MAG_MIP_POINT = 0x00,
    D3D12_FILTER_MIN_MAG_POINT_MIP_LINEAR = 0x01,
    D3D12_FILTER_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x04,
    D3D12_FILTER_MIN_POINT_MAG_MIP_LINEAR = 0x05,
    D3D12_FILTER_MIN_LINEAR_MAG_MIP_POINT = 0x10,
    D3D12_FILTER_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x11,
    D3D12_FILTER_MIN_MAG_LINEAR_MIP_POINT = 0x14,
    D3D12_FILTER_MIN_MAG_MIP_LINEAR = 0x15,
    D3D12_FILTER_ANISOTROPIC = 0x55,
    D3D12_FILTER_COMPARISON_MIN_MAG_MIP_POINT = 0x80,
    D3D12_FILTER_COMPARISON_MIN_MAG_POINT_MIP_LINEAR = 0x81,
    D3D12_FILTER_COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x84,
    D3D12_FILTER_COMPARISON_MIN_POINT_MAG_MIP_LINEAR = 0x85,
    D3D12_FILTER_COMPARISON_MIN_LINEAR_MAG_MIP_POINT = 0x90,
    D3D12_FILTER_COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x91,
    D3D12_FILTER_COMPARISON_MIN_MAG_LINEAR_MIP_POINT = 0x94,
    D3D12_FILTER_COMPARISON_MIN_MAG_MIP_LINEAR = 0x95,
    D3D12_FILTER_COMPARISON_ANISOTROPIC = 0xd5,
    D3D12_FILTER_MINIMUM_MIN_MAG_MIP_POINT = 0x100,
    D3D12_FILTER_MINIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x101,
    D3D12_FILTER_MINIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x104,
    D3D12_FILTER_MINIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x105,
    D3D12_FILTER_MINIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x110,
    D3D12_FILTER_MINIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x111,
    D3D12_FILTER_MINIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x114,
    D3D12_FILTER_MINIMUM_MIN_MAG_MIP_LINEAR = 0x115,
    D3D12_FILTER_MINIMUM_ANISOTROPIC = 0x155,
    D3D12_FILTER_MAXIMUM_MIN_MAG_MIP_POINT = 0x180,
    D3D12_FILTER_MAXIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x181,
    D3D12_FILTER_MAXIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x184,
    D3D12_FILTER_MAXIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x185,
    D3D12_FILTER_MAXIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x190,
    D3D12_FILTER_MAXIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x191,
    D3D12_FILTER_MAXIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x194,
    D3D12_FILTER_MAXIMUM_MIN_MAG_MIP_LINEAR = 0x195,
    D3D12_FILTER_MAXIMUM_ANISOTROPIC = 0x1d5,
} D3D12_FILTER;

typedef enum D3D12_FILTER_TYPE
{
    D3D12_FILTER_TYPE_POINT = 0,
    D3D12_FILTER_TYPE_LINEAR = 1,
} D3D12_FILTER_TYPE;

const UINT D3D12_MIP_FILTER_SHIFT = 0;
const UINT D3D12_MAG_FILTER_SHIFT = 2;
const UINT D3D12_MIN_FILTER_SHIFT = 4;
const UINT D3D12_FILTER_TYPE_MASK = 0x3;

const UINT D3D12_ANISOTROPIC_FILTERING_BIT = 0x40;

typedef enum D3D12_FILTER_REDUCTION_TYPE
{
    D3D12_FILTER_REDUCTION_TYPE_STANDARD = 0,
    D3D12_FILTER_REDUCTION_TYPE_COMPARISON = 1,
    D3D12_FILTER_REDUCTION_TYPE_MINIMUM = 2,
    D3D12_FILTER_REDUCTION_TYPE_MAXIMUM = 3,
} D3D12_FILTER_REDUCTION_TYPE;

const UINT D3D12_FILTER_REDUCTION_TYPE_MASK = 0x3;
const UINT D3D12_FILTER_REDUCTION_TYPE_SHIFT = 7;

cpp_quote("#define D3D12_DECODE_MAG_FILTER(filter) \\")
cpp_quote("    ((D3D12_FILTER_TYPE)(((filter) >> D3D12_MAG_FILTER_SHIFT) & D3D12_FILTER_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_MIN_FILTER(filter) \\")
cpp_quote("    ((D3D12_FILTER_TYPE)(((filter) >> D3D12_MIN_FILTER_SHIFT) & D3D12_FILTER_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_MIP_FILTER(filter) \\")
cpp_quote("    ((D3D12_FILTER_TYPE)(((filter) >> D3D12_MIP_FILTER_SHIFT) & D3D12_FILTER_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_IS_ANISOTROPIC_FILTER(filter)  \\")
cpp_quote("    (((filter) & D3D12_ANISOTROPIC_FILTERING_BIT) \\")
cpp_quote("    && (D3D12_DECODE_MIN_FILTER(filter) == D3D12_FILTER_TYPE_LINEAR) \\")
cpp_quote("    && (D3D12_DECODE_MAG_FILTER(filter) == D3D12_FILTER_TYPE_LINEAR) \\")
cpp_quote("    && (D3D12_DECODE_MIP_FILTER(filter) == D3D12_FILTER_TYPE_LINEAR))")

cpp_quote("#define D3D12_DECODE_FILTER_REDUCTION(filter) \\")
cpp_quote("    ((D3D12_FILTER_REDUCTION_TYPE)(((filter) >> D3D12_FILTER_REDUCTION_TYPE_SHIFT) \\")
cpp_quote("    & D3D12_FILTER_REDUCTION_TYPE_MASK))")

cpp_quote("#define D3D12_DECODE_IS_COMPARISON_FILTER(filter) \\")
cpp_quote("    (D3D12_DECODE_FILTER_REDUCTION(filter) == D3D12_FILTER_REDUCTION_TYPE_COMPARISON)")

typedef enum D3D12_TEXTURE_ADDRESS_MODE
{
    D3D12_TEXTURE_ADDRESS_MODE_WRAP = 1,
    D3D12_TEXTURE_ADDRESS_MODE_MIRROR = 2,
    D3D12_TEXTURE_ADDRESS_MODE_CLAMP = 3,
    D3D12_TEXTURE_ADDRESS_MODE_BORDER = 4,
    D3D12_TEXTURE_ADDRESS_MODE_MIRROR_ONCE = 5,
} D3D12_TEXTURE_ADDRESS_MODE;

typedef enum D3D12_COMPARISON_FUNC
{
    D3D12_COMPARISON_FUNC_NEVER = 1,
    D3D12_COMPARISON_FUNC_LESS = 2,
    D3D12_COMPARISON_FUNC_EQUAL = 3,
    D3D12_COMPARISON_FUNC_LESS_EQUAL = 4,
    D3D12_COMPARISON_FUNC_GREATER = 5,
    D3D12_COMPARISON_FUNC_NOT_EQUAL = 6,
    D3D12_COMPARISON_FUNC_GREATER_EQUAL = 7,
    D3D12_COMPARISON_FUNC_ALWAYS = 8,
} D3D12_COMPARISON_FUNC;

typedef struct D3D12_STATIC_SAMPLER_DESC
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    D3D12_STATIC_BORDER_COLOR BorderColor;
    FLOAT MinLOD;
    FLOAT MaxLOD;
    UINT ShaderRegister;
    UINT RegisterSpace;
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_STATIC_SAMPLER_DESC;

typedef enum D3D12_ROOT_SIGNATURE_FLAGS
{
    D3D12_ROOT_SIGNATURE_FLAG_NONE = 0x00,
    D3D12_ROOT_SIGNATURE_FLAG_ALLOW_INPUT_ASSEMBLER_INPUT_LAYOUT = 0x01,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_VERTEX_SHADER_ROOT_ACCESS = 0x02,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_HULL_SHADER_ROOT_ACCESS = 0x04,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_DOMAIN_SHADER_ROOT_ACCESS = 0x08,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_GEOMETRY_SHADER_ROOT_ACCESS = 0x10,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_PIXEL_SHADER_ROOT_ACCESS = 0x20,
    D3D12_ROOT_SIGNATURE_FLAG_ALLOW_STREAM_OUTPUT = 0x40,
} D3D12_ROOT_SIGNATURE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_ROOT_SIGNATURE_FLAGS);")

typedef struct D3D12_ROOT_SIGNATURE_DESC
{
    UINT NumParameters;
    const D3D12_ROOT_PARAMETER *pParameters;
    UINT NumStaticSamplers;
    const D3D12_STATIC_SAMPLER_DESC *pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC;

typedef struct D3D12_ROOT_SIGNATURE_DESC1
{
    UINT NumParameters;
    const D3D12_ROOT_PARAMETER1 *pParameters;
    UINT NumStaticSamplers;
    const D3D12_STATIC_SAMPLER_DESC *pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC1;

typedef enum D3D_ROOT_SIGNATURE_VERSION
{
    D3D_ROOT_SIGNATURE_VERSION_1 = 0x1,
    D3D_ROOT_SIGNATURE_VERSION_1_0 = 0x1,
    D3D_ROOT_SIGNATURE_VERSION_1_1 = 0x2,
} D3D_ROOT_SIGNATURE_VERSION;

typedef struct D3D12_VERSIONED_ROOT_SIGNATURE_DESC
{
    D3D_ROOT_SIGNATURE_VERSION Version;
    union
    {
        D3D12_ROOT_SIGNATURE_DESC Desc_1_0;
        D3D12_ROOT_SIGNATURE_DESC1 Desc_1_1;
    };
} D3D12_VERSIONED_ROOT_SIGNATURE_DESC;

typedef enum D3D12_DESCRIPTOR_HEAP_TYPE
{
    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV,
    D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER,
    D3D12_DESCRIPTOR_HEAP_TYPE_RTV,
    D3D12_DESCRIPTOR_HEAP_TYPE_DSV,
    D3D12_DESCRIPTOR_HEAP_TYPE_NUM_TYPES,
} D3D12_DESCRIPTOR_HEAP_TYPE;

typedef enum D3D12_DESCRIPTOR_HEAP_FLAGS
{
    D3D12_DESCRIPTOR_HEAP_FLAG_NONE = 0x0,
    D3D12_DESCRIPTOR_HEAP_FLAG_SHADER_VISIBLE = 0x1,
} D3D12_DESCRIPTOR_HEAP_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DESCRIPTOR_HEAP_FLAGS);")

typedef struct D3D12_DESCRIPTOR_HEAP_DESC
{
    D3D12_DESCRIPTOR_HEAP_TYPE Type;
    UINT NumDescriptors;
    D3D12_DESCRIPTOR_HEAP_FLAGS Flags;
    UINT NodeMask;
} D3D12_DESCRIPTOR_HEAP_DESC;

typedef UINT64 D3D12_GPU_VIRTUAL_ADDRESS;

typedef struct D3D12_CONSTANT_BUFFER_VIEW_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes;
} D3D12_CONSTANT_BUFFER_VIEW_DESC;

typedef enum D3D12_SRV_DIMENSION
{
    D3D12_SRV_DIMENSION_UNKNOWN = 0,
    D3D12_SRV_DIMENSION_BUFFER = 1,
    D3D12_SRV_DIMENSION_TEXTURE1D = 2,
    D3D12_SRV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_SRV_DIMENSION_TEXTURE2D = 4,
    D3D12_SRV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_SRV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_SRV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_SRV_DIMENSION_TEXTURE3D = 8,
    D3D12_SRV_DIMENSION_TEXTURECUBE = 9,
    D3D12_SRV_DIMENSION_TEXTURECUBEARRAY = 10,
} D3D12_SRV_DIMENSION;

typedef enum D3D12_BUFFER_SRV_FLAGS
{
    D3D12_BUFFER_SRV_FLAG_NONE = 0x0,
    D3D12_BUFFER_SRV_FLAG_RAW = 0x1,
} D3D12_BUFFER_SRV_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_BUFFER_SRV_FLAGS);")

typedef enum D3D12_SHADER_COMPONENT_MAPPING
{
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_0 = 0,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_1 = 1,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_2 = 2,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_3 = 3,
    D3D12_SHADER_COMPONENT_MAPPING_FORCE_VALUE_0 = 4,
    D3D12_SHADER_COMPONENT_MAPPING_FORCE_VALUE_1 = 5,
} D3D12_SHADER_COMPONENT_MAPPING;

cpp_quote("#define D3D12_ENCODE_SHADER_4_COMPONENT_MAPPING(x, y, z, w) \\")
cpp_quote("        (((x) & D3D12_SHADER_COMPONENT_MAPPING_MASK) \\")
cpp_quote("        | (((y) & D3D12_SHADER_COMPONENT_MAPPING_MASK) << D3D12_SHADER_COMPONENT_MAPPING_SHIFT) \\")
cpp_quote("        | (((z) & D3D12_SHADER_COMPONENT_MAPPING_MASK) << (D3D12_SHADER_COMPONENT_MAPPING_SHIFT * 2)) \\")
cpp_quote("        | (((w) & D3D12_SHADER_COMPONENT_MAPPING_MASK) << (D3D12_SHADER_COMPONENT_MAPPING_SHIFT * 3)) \\")
cpp_quote("        | D3D12_SHADER_COMPONENT_MAPPING_ALWAYS_SET_BIT_AVOIDING_ZEROMEM_MISTAKES)")
cpp_quote("#define D3D12_DEFAULT_SHADER_4_COMPONENT_MAPPING D3D12_ENCODE_SHADER_4_COMPONENT_MAPPING(0, 1, 2, 3)")

cpp_quote("#define D3D12_DECODE_SHADER_4_COMPONENT_MAPPING(i, mapping) \\")
cpp_quote("        ((D3D12_SHADER_COMPONENT_MAPPING)(mapping >> (i * D3D12_SHADER_COMPONENT_MAPPING_SHIFT) \\")
cpp_quote("        & D3D12_SHADER_COMPONENT_MAPPING_MASK))")

typedef struct D3D12_BUFFER_SRV
{
    UINT64 FirstElement;
    UINT NumElements;
    UINT StructureByteStride;
    D3D12_BUFFER_SRV_FLAGS Flags;
} D3D12_BUFFER_SRV;

typedef struct D3D12_TEX1D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX1D_SRV;

typedef struct D3D12_TEX1D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX1D_ARRAY_SRV;

typedef struct D3D12_TEX2D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT PlaneSlice;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX2D_SRV;

typedef struct D3D12_TEX2D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX2D_ARRAY_SRV;

typedef struct D3D12_TEX2DMS_SRV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_SRV;

typedef struct D3D12_TEX2DMS_ARRAY_SRV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_SRV;

typedef struct D3D12_TEX3D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX3D_SRV;

typedef struct D3D12_TEXCUBE_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEXCUBE_SRV;

typedef struct D3D12_TEXCUBE_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;
    UINT NumCubes;
    FLOAT ResourceMinLODClamp;
} D3D12_TEXCUBE_ARRAY_SRV;

typedef struct D3D12_SHADER_RESOURCE_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_SRV_DIMENSION ViewDimension;
    UINT Shader4ComponentMapping;
    union
    {
        D3D12_BUFFER_SRV Buffer;
        D3D12_TEX1D_SRV Texture1D;
        D3D12_TEX1D_ARRAY_SRV Texture1DArray;
        D3D12_TEX2D_SRV Texture2D;
        D3D12_TEX2D_ARRAY_SRV Texture2DArray;
        D3D12_TEX2DMS_SRV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D12_TEX3D_SRV Texture3D;
        D3D12_TEXCUBE_SRV TextureCube;
        D3D12_TEXCUBE_ARRAY_SRV TextureCubeArray;
    };
} D3D12_SHADER_RESOURCE_VIEW_DESC;

typedef enum D3D12_UAV_DIMENSION
{
    D3D12_UAV_DIMENSION_UNKNOWN = 0,
    D3D12_UAV_DIMENSION_BUFFER = 1,
    D3D12_UAV_DIMENSION_TEXTURE1D = 2,
    D3D12_UAV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_UAV_DIMENSION_TEXTURE2D = 4,
    D3D12_UAV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_UAV_DIMENSION_TEXTURE3D = 8,
} D3D12_UAV_DIMENSION;

typedef enum D3D12_BUFFER_UAV_FLAGS
{
    D3D12_BUFFER_UAV_FLAG_NONE = 0x0,
    D3D12_BUFFER_UAV_FLAG_RAW = 0x1,
} D3D12_BUFFER_UAV_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_BUFFER_UAV_FLAGS);")

typedef struct D3D12_BUFFER_UAV
{
    UINT64 FirstElement;
    UINT NumElements;
    UINT StructureByteStride;
    UINT64 CounterOffsetInBytes;
    D3D12_BUFFER_UAV_FLAGS Flags;
} D3D12_BUFFER_UAV;

typedef struct D3D12_TEX1D_UAV
{
    UINT MipSlice;
} D3D12_TEX1D_UAV;

typedef struct D3D12_TEX1D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_UAV;

typedef struct D3D12_TEX2D_UAV
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D12_TEX2D_UAV;

typedef struct D3D12_TEX2D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D12_TEX2D_ARRAY_UAV;

typedef struct D3D12_TEX3D_UAV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D12_TEX3D_UAV;

typedef struct D3D12_UNORDERED_ACCESS_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_UAV_DIMENSION ViewDimension;
    union
    {
        D3D12_BUFFER_UAV Buffer;
        D3D12_TEX1D_UAV Texture1D;
        D3D12_TEX1D_ARRAY_UAV Texture1DArray;
        D3D12_TEX2D_UAV Texture2D;
        D3D12_TEX2D_ARRAY_UAV Texture2DArray;
        D3D12_TEX3D_UAV Texture3D;
    };
} D3D12_UNORDERED_ACCESS_VIEW_DESC;

typedef enum D3D12_RTV_DIMENSION
{
    D3D12_RTV_DIMENSION_UNKNOWN = 0,
    D3D12_RTV_DIMENSION_BUFFER = 1,
    D3D12_RTV_DIMENSION_TEXTURE1D = 2,
    D3D12_RTV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_RTV_DIMENSION_TEXTURE2D = 4,
    D3D12_RTV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_RTV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_RTV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_RTV_DIMENSION_TEXTURE3D = 8,
} D3D12_RTV_DIMENSION;

typedef struct D3D12_BUFFER_RTV
{
    UINT64 FirstElement;
    UINT NumElements;
} D3D12_BUFFER_RTV;

typedef struct D3D12_TEX1D_RTV
{
    UINT MipSlice;
} D3D12_TEX1D_RTV;

typedef struct D3D12_TEX1D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_RTV;

typedef struct D3D12_TEX2D_RTV
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D12_TEX2D_RTV;

typedef struct D3D12_TEX2D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D12_TEX2D_ARRAY_RTV;

typedef struct D3D12_TEX2DMS_RTV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_RTV;

typedef struct D3D12_TEX2DMS_ARRAY_RTV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_RTV;

typedef struct D3D12_TEX3D_RTV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D12_TEX3D_RTV;

typedef struct D3D12_RENDER_TARGET_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_RTV_DIMENSION ViewDimension;
    union
    {
        D3D12_BUFFER_RTV Buffer;
        D3D12_TEX1D_RTV Texture1D;
        D3D12_TEX1D_ARRAY_RTV Texture1DArray;
        D3D12_TEX2D_RTV Texture2D;
        D3D12_TEX2D_ARRAY_RTV Texture2DArray;
        D3D12_TEX2DMS_RTV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D12_TEX3D_RTV Texture3D;
    };
} D3D12_RENDER_TARGET_VIEW_DESC;

typedef enum D3D12_DSV_DIMENSION
{
    D3D12_DSV_DIMENSION_UNKNOWN = 0,
    D3D12_DSV_DIMENSION_TEXTURE1D = 1,
    D3D12_DSV_DIMENSION_TEXTURE1DARRAY = 2,
    D3D12_DSV_DIMENSION_TEXTURE2D = 3,
    D3D12_DSV_DIMENSION_TEXTURE2DARRAY = 4,
    D3D12_DSV_DIMENSION_TEXTURE2DMS = 5,
    D3D12_DSV_DIMENSION_TEXTURE2DMSARRAY = 6,
} D3D12_DSV_DIMENSION;

typedef enum D3D12_DSV_FLAGS
{
    D3D12_DSV_FLAG_NONE = 0x0,
    D3D12_DSV_FLAG_READ_ONLY_DEPTH = 0x1,
    D3D12_DSV_FLAG_READ_ONLY_STENCIL = 0x2,
} D3D12_DSV_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_DSV_FLAGS);")

typedef struct D3D12_TEX1D_DSV
{
    UINT MipSlice;
} D3D12_TEX1D_DSV;

typedef struct D3D12_TEX1D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_DSV;

typedef struct D3D12_TEX2D_DSV
{
    UINT MipSlice;
} D3D12_TEX2D_DSV;

typedef struct D3D12_TEX2D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2D_ARRAY_DSV;

typedef struct D3D12_TEX2DMS_DSV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_DSV;

typedef struct D3D12_TEX2DMS_ARRAY_DSV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_DSV;

typedef struct D3D12_DEPTH_STENCIL_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_DSV_DIMENSION ViewDimension;
    D3D12_DSV_FLAGS Flags;
    union
    {
        D3D12_TEX1D_DSV Texture1D;
        D3D12_TEX1D_ARRAY_DSV Texture1DArray;
        D3D12_TEX2D_DSV Texture2D;
        D3D12_TEX2D_ARRAY_DSV Texture2DArray;
        D3D12_TEX2DMS_DSV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_DSV Texture2DMSArray;
    };
} D3D12_DEPTH_STENCIL_VIEW_DESC;

typedef struct D3D12_SAMPLER_DESC
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    FLOAT BorderColor[4];
    FLOAT MinLOD;
    FLOAT MaxLOD;
} D3D12_SAMPLER_DESC;

typedef struct D3D12_CPU_DESCRIPTOR_HANDLE
{
    SIZE_T ptr;
} D3D12_CPU_DESCRIPTOR_HANDLE;

typedef struct D3D12_GPU_DESCRIPTOR_HANDLE
{
    UINT64 ptr;
} D3D12_GPU_DESCRIPTOR_HANDLE;

typedef enum D3D12_STENCIL_OP
{
    D3D12_STENCIL_OP_KEEP = 1,
    D3D12_STENCIL_OP_ZERO = 2,
    D3D12_STENCIL_OP_REPLACE = 3,
    D3D12_STENCIL_OP_INCR_SAT = 4,
    D3D12_STENCIL_OP_DECR_SAT = 5,
    D3D12_STENCIL_OP_INVERT = 6,
    D3D12_STENCIL_OP_INCR = 7,
    D3D12_STENCIL_OP_DECR = 8,
} D3D12_STENCIL_OP;

typedef struct D3D12_DEPTH_STENCILOP_DESC
{
    D3D12_STENCIL_OP StencilFailOp;
    D3D12_STENCIL_OP StencilDepthFailOp;
    D3D12_STENCIL_OP StencilPassOp;
    D3D12_COMPARISON_FUNC StencilFunc;
} D3D12_DEPTH_STENCILOP_DESC;

typedef enum D3D12_DEPTH_WRITE_MASK
{
    D3D12_DEPTH_WRITE_MASK_ZERO = 0,
    D3D12_DEPTH_WRITE_MASK_ALL = 1,
} D3D12_DEPTH_WRITE_MASK;

typedef struct D3D12_DEPTH_STENCIL_DESC
{
    BOOL DepthEnable;
    D3D12_DEPTH_WRITE_MASK DepthWriteMask;
    D3D12_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D12_DEPTH_STENCILOP_DESC FrontFace;
    D3D12_DEPTH_STENCILOP_DESC BackFace;
} D3D12_DEPTH_STENCIL_DESC;

typedef enum D3D12_BLEND
{
    D3D12_BLEND_ZERO = 1,
    D3D12_BLEND_ONE = 2,
    D3D12_BLEND_SRC_COLOR = 3,
    D3D12_BLEND_INV_SRC_COLOR = 4,
    D3D12_BLEND_SRC_ALPHA = 5,
    D3D12_BLEND_INV_SRC_ALPHA = 6,
    D3D12_BLEND_DEST_ALPHA = 7,
    D3D12_BLEND_INV_DEST_ALPHA = 8,
    D3D12_BLEND_DEST_COLOR = 9,
    D3D12_BLEND_INV_DEST_COLOR = 10,
    D3D12_BLEND_SRC_ALPHA_SAT = 11,
    D3D12_BLEND_BLEND_FACTOR = 14,
    D3D12_BLEND_INV_BLEND_FACTOR = 15,
    D3D12_BLEND_SRC1_COLOR = 16,
    D3D12_BLEND_INV_SRC1_COLOR = 17,
    D3D12_BLEND_SRC1_ALPHA = 18,
    D3D12_BLEND_INV_SRC1_ALPHA = 19,
} D3D12_BLEND;

typedef enum D3D12_BLEND_OP
{
    D3D12_BLEND_OP_ADD = 1,
    D3D12_BLEND_OP_SUBTRACT = 2,
    D3D12_BLEND_OP_REV_SUBTRACT = 3,
    D3D12_BLEND_OP_MIN = 4,
    D3D12_BLEND_OP_MAX = 5,
} D3D12_BLEND_OP;

typedef enum D3D12_LOGIC_OP
{
    D3D12_LOGIC_OP_CLEAR = 0,
    D3D12_LOGIC_OP_SET = 1,
    D3D12_LOGIC_OP_COPY = 2,
    D3D12_LOGIC_OP_COPY_INVERTED = 3,
    D3D12_LOGIC_OP_NOOP = 4,
    D3D12_LOGIC_OP_INVERT = 5,
    D3D12_LOGIC_OP_AND = 6,
    D3D12_LOGIC_OP_NAND = 7,
    D3D12_LOGIC_OP_OR = 8,
    D3D12_LOGIC_OP_NOR = 9,
    D3D12_LOGIC_OP_XOR = 10,
    D3D12_LOGIC_OP_EQUIV = 11,
    D3D12_LOGIC_OP_AND_REVERSE = 12,
    D3D12_LOGIC_OP_AND_INVERTED = 13,
    D3D12_LOGIC_OP_OR_REVERSE = 14,
    D3D12_LOGIC_OP_OR_INVERTED = 15,
} D3D12_LOGIC_OP;

typedef enum D3D12_COLOR_WRITE_ENABLE
{
    D3D12_COLOR_WRITE_ENABLE_RED = 0x1,
    D3D12_COLOR_WRITE_ENABLE_GREEN = 0x2,
    D3D12_COLOR_WRITE_ENABLE_BLUE = 0x4,
    D3D12_COLOR_WRITE_ENABLE_ALPHA = 0x8,
    D3D12_COLOR_WRITE_ENABLE_ALL = (D3D12_COLOR_WRITE_ENABLE_RED
            | D3D12_COLOR_WRITE_ENABLE_GREEN | D3D12_COLOR_WRITE_ENABLE_BLUE
            | D3D12_COLOR_WRITE_ENABLE_ALPHA),
} D3D12_COLOR_WRITE_ENABLE;

typedef struct D3D12_RENDER_TARGET_BLEND_DESC
{
    BOOL BlendEnable;
    BOOL LogicOpEnable;
    D3D12_BLEND SrcBlend;
    D3D12_BLEND DestBlend;
    D3D12_BLEND_OP BlendOp;
    D3D12_BLEND SrcBlendAlpha;
    D3D12_BLEND DestBlendAlpha;
    D3D12_BLEND_OP BlendOpAlpha;
    D3D12_LOGIC_OP LogicOp;
    UINT8 RenderTargetWriteMask;
} D3D12_RENDER_TARGET_BLEND_DESC;

typedef struct D3D12_BLEND_DESC
{
    BOOL AlphaToCoverageEnable;
    BOOL IndependentBlendEnable;
    D3D12_RENDER_TARGET_BLEND_DESC RenderTarget[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
} D3D12_BLEND_DESC;

typedef enum D3D12_FILL_MODE
{
    D3D12_FILL_MODE_WIREFRAME = 2,
    D3D12_FILL_MODE_SOLID = 3,
} D3D12_FILL_MODE;

typedef enum D3D12_CULL_MODE
{
    D3D12_CULL_MODE_NONE = 1,
    D3D12_CULL_MODE_FRONT = 2,
    D3D12_CULL_MODE_BACK = 3,
} D3D12_CULL_MODE;

typedef enum D3D12_CONSERVATIVE_RASTERIZATION_MODE
{
    D3D12_CONSERVATIVE_RASTERIZATION_MODE_OFF = 0,
    D3D12_CONSERVATIVE_RASTERIZATION_MODE_ON = 1,
} D3D12_CONSERVATIVE_RASTERIZATION_MODE;

typedef struct D3D12_RASTERIZER_DESC
{
    D3D12_FILL_MODE FillMode;
    D3D12_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;
    D3D12_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D12_RASTERIZER_DESC;

typedef struct D3D12_SO_DECLARATION_ENTRY
{
    UINT Stream;
    const char *SemanticName;
    UINT SemanticIndex;
    BYTE StartComponent;
    BYTE ComponentCount;
    BYTE OutputSlot;
} D3D12_SO_DECLARATION_ENTRY;

typedef struct D3D12_STREAM_OUTPUT_DESC
{
    const D3D12_SO_DECLARATION_ENTRY *pSODeclaration;
    UINT NumEntries;
    const UINT *pBufferStrides;
    UINT NumStrides;
    UINT RasterizedStream;
} D3D12_STREAM_OUTPUT_DESC;

typedef enum D3D12_INPUT_CLASSIFICATION
{
    D3D12_INPUT_CLASSIFICATION_PER_VERTEX_DATA = 0,
    D3D12_INPUT_CLASSIFICATION_PER_INSTANCE_DATA = 1,
} D3D12_INPUT_CLASSIFICATION;

typedef struct D3D12_INPUT_ELEMENT_DESC
{
    const char *SemanticName;
    UINT SemanticIndex;
    DXGI_FORMAT Format;
    UINT InputSlot;
    UINT AlignedByteOffset;
    D3D12_INPUT_CLASSIFICATION InputSlotClass;
    UINT InstanceDataStepRate;
} D3D12_INPUT_ELEMENT_DESC;

typedef struct D3D12_INPUT_LAYOUT_DESC
{
    const D3D12_INPUT_ELEMENT_DESC *pInputElementDescs;
    UINT NumElements;
} D3D12_INPUT_LAYOUT_DESC;

typedef enum D3D12_INDEX_BUFFER_STRIP_CUT_VALUE
{
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_DISABLED = 0,
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_0xFFFF = 1,
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_0xFFFFFFFF = 2,
} D3D12_INDEX_BUFFER_STRIP_CUT_VALUE;

typedef D3D_PRIMITIVE_TOPOLOGY D3D12_PRIMITIVE_TOPOLOGY;

typedef enum D3D12_PRIMITIVE_TOPOLOGY_TYPE
{
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_UNDEFINED = 0,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_POINT = 1,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_LINE = 2,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_TRIANGLE = 3,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_PATCH = 4,
} D3D12_PRIMITIVE_TOPOLOGY_TYPE;

typedef struct D3D12_CACHED_PIPELINE_STATE
{
    const void *pCachedBlob;
    SIZE_T CachedBlobSizeInBytes;
} D3D12_CACHED_PIPELINE_STATE;

typedef enum D3D12_PIPELINE_STATE_FLAGS
{
    D3D12_PIPELINE_STATE_FLAG_NONE = 0x0,
    D3D12_PIPELINE_STATE_FLAG_DEBUG = 0x1,
} D3D12_PIPELINE_STATE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_PIPELINE_STATE_FLAGS);")

typedef struct D3D12_GRAPHICS_PIPELINE_STATE_DESC
{
    ID3D12RootSignature *pRootSignature;
    D3D12_SHADER_BYTECODE VS;
    D3D12_SHADER_BYTECODE PS;
    D3D12_SHADER_BYTECODE DS;
    D3D12_SHADER_BYTECODE HS;
    D3D12_SHADER_BYTECODE GS;
    D3D12_STREAM_OUTPUT_DESC StreamOutput;
    D3D12_BLEND_DESC BlendState;
    UINT SampleMask;
    D3D12_RASTERIZER_DESC RasterizerState;
    D3D12_DEPTH_STENCIL_DESC DepthStencilState;
    D3D12_INPUT_LAYOUT_DESC InputLayout;
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE IBStripCutValue;
    D3D12_PRIMITIVE_TOPOLOGY_TYPE PrimitiveTopologyType;
    UINT NumRenderTargets;
    DXGI_FORMAT RTVFormats[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
    DXGI_FORMAT DSVFormat;
    DXGI_SAMPLE_DESC SampleDesc;
    UINT NodeMask;
    D3D12_CACHED_PIPELINE_STATE CachedPSO;
    D3D12_PIPELINE_STATE_FLAGS Flags;
} D3D12_GRAPHICS_PIPELINE_STATE_DESC;

typedef struct D3D12_COMPUTE_PIPELINE_STATE_DESC
{
    ID3D12RootSignature *pRootSignature;
    D3D12_SHADER_BYTECODE CS;
    UINT NodeMask;
    D3D12_CACHED_PIPELINE_STATE CachedPSO;
    D3D12_PIPELINE_STATE_FLAGS Flags;
} D3D12_COMPUTE_PIPELINE_STATE_DESC;

typedef enum D3D12_COMMAND_LIST_TYPE
{
    D3D12_COMMAND_LIST_TYPE_DIRECT = 0,
    D3D12_COMMAND_LIST_TYPE_BUNDLE = 1,
    D3D12_COMMAND_LIST_TYPE_COMPUTE = 2,
    D3D12_COMMAND_LIST_TYPE_COPY = 3,
} D3D12_COMMAND_LIST_TYPE;

typedef enum D3D12_COMMAND_QUEUE_PRIORITY
{
    D3D12_COMMAND_QUEUE_PRIORITY_NORMAL = 0,
    D3D12_COMMAND_QUEUE_PRIORITY_HIGH = 100,
    D3D12_COMMAND_QUEUE_PRIORITY_GLOBAL_REALTIME = 10000,
} D3D12_COMMAND_QUEUE_PRIORITY;

typedef enum D3D12_COMMAND_QUEUE_FLAGS
{
    D3D12_COMMAND_QUEUE_FLAG_NONE = 0x0,
    D3D12_COMMAND_QUEUE_FLAG_DISABLE_GPU_TIMEOUT = 0x1,
} D3D12_COMMAND_QUEUE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_COMMAND_QUEUE_FLAGS);")

typedef struct D3D12_COMMAND_QUEUE_DESC
{
    D3D12_COMMAND_LIST_TYPE Type;
    INT Priority;
    D3D12_COMMAND_QUEUE_FLAGS Flags;
    UINT NodeMask;
} D3D12_COMMAND_QUEUE_DESC;

typedef struct D3D12_FEATURE_DATA_ARCHITECTURE
{
    UINT NodeIndex;
    BOOL TileBasedRenderer;
    BOOL UMA;
    BOOL CacheCoherentUMA;
} D3D12_FEATURE_DATA_ARCHITECTURE;

typedef struct D3D12_FEATURE_DATA_FORMAT_INFO
{
    DXGI_FORMAT Format;
    UINT8 PlaneCount;
} D3D12_FEATURE_DATA_FORMAT_INFO;

typedef struct D3D12_FEATURE_DATA_FEATURE_LEVELS
{
    UINT NumFeatureLevels;
    const D3D_FEATURE_LEVEL *pFeatureLevelsRequested;
    D3D_FEATURE_LEVEL MaxSupportedFeatureLevel;
} D3D12_FEATURE_DATA_FEATURE_LEVELS;

typedef struct D3D12_FEATURE_DATA_ROOT_SIGNATURE
{
    D3D_ROOT_SIGNATURE_VERSION HighestVersion;
} D3D12_FEATURE_DATA_ROOT_SIGNATURE;

typedef struct D3D12_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT
{
    UINT MaxGPUVirtualAddressBitsPerResource;
    UINT MaxGPUVirtualAddressBitsPerProcess;
} D3D12_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT;

typedef enum D3D_SHADER_MODEL
{
    D3D_SHADER_MODEL_5_1 = 0x51,
    D3D_SHADER_MODEL_6_0 = 0x60,
} D3D_SHADER_MODEL;

typedef struct D3D12_FEATURE_DATA_SHADER_MODEL
{
    D3D_SHADER_MODEL HighestShaderModel;
} D3D12_FEATURE_DATA_SHADER_MODEL;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS1
{
    BOOL WaveOps;
    UINT WaveLaneCountMin;
    UINT WaveLaneCountMax;
    UINT TotalLaneCount;
    BOOL ExpandedComputeResourceStates;
    BOOL Int64ShaderOps;
}  D3D12_FEATURE_DATA_D3D12_OPTIONS1;

typedef struct D3D12_FEATURE_DATA_ARCHITECTURE1
{
    UINT NodeIndex;
    BOOL TileBasedRenderer;
    BOOL UMA;
    BOOL CacheCoherentUMA;
    BOOL IsolatedMMU;
}  D3D12_FEATURE_DATA_ARCHITECTURE1;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS2
{
    BOOL DepthBoundsTestSupported;
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER ProgrammableSamplePositionsTier;
}  D3D12_FEATURE_DATA_D3D12_OPTIONS2;

typedef struct D3D12_FEATURE_DATA_SHADER_CACHE
{
    D3D12_SHADER_CACHE_SUPPORT_FLAGS SupportFlags;
}  D3D12_FEATURE_DATA_SHADER_CACHE;

typedef struct D3D12_FEATURE_DATA_COMMAND_QUEUE_PRIORITY
{
    D3D12_COMMAND_LIST_TYPE CommandListType;
    UINT Priority;
    BOOL PriorityForTypeIsSupported;
}  D3D12_FEATURE_DATA_COMMAND_QUEUE_PRIORITY;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS3
{
    BOOL CopyQueueTimestampQueriesSupported;
    BOOL CastingFullyTypedFormatSupported;
    D3D12_COMMAND_LIST_SUPPORT_FLAGS WriteBufferImmediateSupportFlags;
    D3D12_VIEW_INSTANCING_TIER ViewInstancingTier;
    BOOL BarycentricsSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS3;

typedef struct D3D12_FEATURE_DATA_EXISTING_HEAPS
{
    BOOL Supported;
}  D3D12_FEATURE_DATA_EXISTING_HEAPS;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS4
{
    BOOL MSAA64KBAlignedTextureSupported;
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER SharedResourceCompatibilityTier;
    BOOL Native16BitShaderOpsSupported;
}  D3D12_FEATURE_DATA_D3D12_OPTIONS4;

typedef struct D3D12_FEATURE_DATA_SERIALIZATION
{
    UINT NodeIndex;
    D3D12_HEAP_SERIALIZATION_TIER HeapSerializationTier;
}  D3D12_FEATURE_DATA_SERIALIZATION;

typedef struct D3D12_FEATURE_DATA_CROSS_NODE
{
    D3D12_CROSS_NODE_SHARING_TIER SharingTier;
    BOOL AtomicShaderInstructions;
}  D3D12_FEATURE_DATA_CROSS_NODE;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS5
{
    BOOL SRVOnlyTiledResourceTier3;
    D3D12_RENDER_PASS_TIER RenderPassesTier;
    D3D12_RAYTRACING_TIER RaytracingTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS5;

typedef enum D3D12_FEATURE
{
    D3D12_FEATURE_D3D12_OPTIONS = 0,
    D3D12_FEATURE_ARCHITECTURE = 1,
    D3D12_FEATURE_FEATURE_LEVELS = 2,
    D3D12_FEATURE_FORMAT_SUPPORT = 3,
    D3D12_FEATURE_MULTISAMPLE_QUALITY_LEVELS = 4,
    D3D12_FEATURE_FORMAT_INFO = 5,
    D3D12_FEATURE_GPU_VIRTUAL_ADDRESS_SUPPORT = 6,
    D3D12_FEATURE_SHADER_MODEL = 7,
    D3D12_FEATURE_D3D12_OPTIONS1 = 8,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_SUPPORT = 10,
    D3D12_FEATURE_ROOT_SIGNATURE = 12,
    D3D12_FEATURE_ARCHITECTURE1 = 16,
    D3D12_FEATURE_D3D12_OPTIONS2 = 18,
    D3D12_FEATURE_SHADER_CACHE = 19,
    D3D12_FEATURE_COMMAND_QUEUE_PRIORITY = 20,
    D3D12_FEATURE_D3D12_OPTIONS3 = 21,
    D3D12_FEATURE_EXISTING_HEAPS = 22,
    D3D12_FEATURE_D3D12_OPTIONS4 = 23,
    D3D12_FEATURE_SERIALIZATION = 24,
    D3D12_FEATURE_CROSS_NODE = 25,
    D3D12_FEATURE_D3D12_OPTIONS5 = 27,
    D3D12_FEATURE_D3D12_OPTIONS6 = 30,
    D3D12_FEATURE_QUERY_META_COMMAND = 31,
    D3D12_FEATURE_D3D12_OPTIONS7 = 32,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPE_COUNT = 33,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPES = 34,
} D3D12_FEATURE;

typedef struct D3D12_MEMCPY_DEST
{
    void *pData;
    SIZE_T RowPitch;
    SIZE_T SlicePitch;
} D3D12_MEMCPY_DEST;

typedef struct D3D12_SUBRESOURCE_DATA
{
    const void *pData;
    LONG_PTR RowPitch;
    LONG_PTR SlicePitch;
} D3D12_SUBRESOURCE_DATA;

typedef enum D3D12_MULTIPLE_FENCE_WAIT_FLAGS
{
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_NONE = 0x0,
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_ANY = 0x1,
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_ALL = 0x0,
} D3D12_MULTIPLE_FENCE_WAIT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_MULTIPLE_FENCE_WAIT_FLAGS);")

typedef enum D3D12_RESIDENCY_PRIORITY
{
    D3D12_RESIDENCY_PRIORITY_MINIMUM = 0x28000000,
    D3D12_RESIDENCY_PRIORITY_LOW = 0x50000000,
    D3D12_RESIDENCY_PRIORITY_NORMAL = 0x78000000,
    D3D12_RESIDENCY_PRIORITY_HIGH = 0xa0010000,
    D3D12_RESIDENCY_PRIORITY_MAXIMUM = 0xc8000000,
} D3D12_RESIDENCY_PRIORITY;

typedef struct D3D12_WRITEBUFFERIMMEDIATE_PARAMETER
{
    D3D12_GPU_VIRTUAL_ADDRESS Dest;
    UINT32 Value;
} D3D12_WRITEBUFFERIMMEDIATE_PARAMETER;

[
    uuid(c4fec28f-7966-4e95-9f94-f431cb56c3b8),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Object : IUnknown
{
    HRESULT GetPrivateData(REFGUID guid, UINT *data_size, void *data);
    HRESULT SetPrivateData(REFGUID guid, UINT data_size, const void *data);
    HRESULT SetPrivateDataInterface(REFGUID guid, const IUnknown *data);
    HRESULT SetName(const WCHAR *name);
}

[
    uuid(905db94b-a00c-4140-9df5-2b64ca9ea357),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceChild : ID3D12Object
{
    HRESULT GetDevice(REFIID riid, void **device);
}

[
    uuid(63ee58fb-1268-4835-86da-f008ce62f0d6),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Pageable : ID3D12DeviceChild
{
}

[
    uuid(6b3b2502-6e51-45b3-90ee-9884265e8df3),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Heap : ID3D12Pageable
{
    D3D12_HEAP_DESC GetDesc();
}

[
    uuid(696442be-a72e-4059-bc79-5b5c98040fad),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Resource : ID3D12Pageable
{
    HRESULT Map(UINT sub_resource, const D3D12_RANGE *read_range, void **data);
    void Unmap(UINT sub_resource, const D3D12_RANGE *written_range);

    D3D12_RESOURCE_DESC GetDesc();

    D3D12_GPU_VIRTUAL_ADDRESS GetGPUVirtualAddress();

    HRESULT WriteToSubresource(UINT dst_sub_resource, const D3D12_BOX *dst_box,
            const void *src_data, UINT src_row_pitch, UINT src_slice_pitch);
    HRESULT ReadFromSubresource(void *dst_data, UINT dst_row_pitch, UINT dst_slice_pitch,
            UINT src_sub_resource, const D3D12_BOX *src_box);

    HRESULT GetHeapProperties(D3D12_HEAP_PROPERTIES *heap_properties, D3D12_HEAP_FLAGS *flags);
}

[
    uuid(7116d91c-e7e4-47ce-b8c6-ec8168f437e5),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandList : ID3D12DeviceChild
{
    D3D12_COMMAND_LIST_TYPE GetType();
}

typedef enum D3D12_TILE_COPY_FLAGS
{
    D3D12_TILE_COPY_FLAG_NONE = 0x0,
    D3D12_TILE_COPY_FLAG_NO_HAZARD = 0x1,
    D3D12_TILE_COPY_FLAG_LINEAR_BUFFER_TO_SWIZZLED_TILED_RESOURCE = 0x2,
    D3D12_TILE_COPY_FLAG_SWIZZLED_TILED_RESOURCE_TO_LINEAR_BUFFER = 0x4,
} D3D12_TILE_COPY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_TILE_COPY_FLAGS);")

typedef struct D3D12_INDEX_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes;
    DXGI_FORMAT Format;
} D3D12_INDEX_BUFFER_VIEW;

typedef struct D3D12_VERTEX_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes;
    UINT StrideInBytes;
} D3D12_VERTEX_BUFFER_VIEW;

typedef struct D3D12_STREAM_OUTPUT_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT64 SizeInBytes;
    D3D12_GPU_VIRTUAL_ADDRESS BufferFilledSizeLocation;
} D3D12_STREAM_OUTPUT_BUFFER_VIEW;

typedef enum D3D12_CLEAR_FLAGS
{
    D3D12_CLEAR_FLAG_DEPTH = 0x1,
    D3D12_CLEAR_FLAG_STENCIL = 0x2,
} D3D12_CLEAR_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_CLEAR_FLAGS);")

typedef struct D3D12_DISCARD_REGION
{
    UINT NumRects;
    const D3D12_RECT *pRects;
    UINT FirstSubresource;
    UINT NumSubresources;
} D3D12_DISCARD_REGION;

typedef enum D3D12_QUERY_TYPE
{
    D3D12_QUERY_TYPE_OCCLUSION = 0,
    D3D12_QUERY_TYPE_BINARY_OCCLUSION = 1,
    D3D12_QUERY_TYPE_TIMESTAMP = 2,
    D3D12_QUERY_TYPE_PIPELINE_STATISTICS = 3,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM0 = 4,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM1 = 5,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM2 = 6,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM3 = 7,
} D3D12_QUERY_TYPE;

typedef struct D3D12_QUERY_DATA_PIPELINE_STATISTICS
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
} D3D12_QUERY_DATA_PIPELINE_STATISTICS;

typedef struct D3D12_QUERY_DATA_SO_STATISTICS
{
    UINT64 NumPrimitivesWritten;
    UINT64 PrimitivesStorageNeeded;
} D3D12_QUERY_DATA_SO_STATISTICS;

typedef enum D3D12_PREDICATION_OP
{
    D3D12_PREDICATION_OP_EQUAL_ZERO = 0,
    D3D12_PREDICATION_OP_NOT_EQUAL_ZERO = 1,
} D3D12_PREDICATION_OP;

[
    uuid(8efb471d-616c-4f49-90f7-127bb763fa51),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DescriptorHeap : ID3D12Pageable
{
    D3D12_DESCRIPTOR_HEAP_DESC GetDesc();

    D3D12_CPU_DESCRIPTOR_HANDLE GetCPUDescriptorHandleForHeapStart();
    D3D12_GPU_DESCRIPTOR_HANDLE GetGPUDescriptorHandleForHeapStart();
}

[
    uuid(0d9658ae-ed45-469e-a61d-970ec583cab4),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12QueryHeap : ID3D12Pageable
{
}

[
    uuid(c36a797c-ec80-4f0a-8985-a7b2475082d1),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandSignature : ID3D12Pageable
{
}

[
    uuid(5b160d0f-ac1b-4185-8ba8-b3ae42a5a455),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList : ID3D12CommandList
{
    HRESULT Close();

    HRESULT Reset(ID3D12CommandAllocator *allocator, ID3D12PipelineState *initial_state);

    void ClearState(ID3D12PipelineState *pipeline_state);

    void DrawInstanced(UINT vertex_count_per_instance, UINT instance_count,
            UINT start_vertex_location, UINT start_instance_location);
    void DrawIndexedInstanced(UINT index_count_per_instance, UINT instance_count,
            UINT start_vertex_location, INT base_vertex_location, UINT start_instance_location);

    void Dispatch(UINT x, UINT u, UINT z);

    void CopyBufferRegion(ID3D12Resource *dst_buffer, UINT64 dst_offset,
            ID3D12Resource *src_buffer, UINT64 src_offset, UINT64 byte_count);
    void CopyTextureRegion(const D3D12_TEXTURE_COPY_LOCATION *dst,
            UINT dst_x, UINT dst_y, UINT dst_z,
            const D3D12_TEXTURE_COPY_LOCATION *src, const D3D12_BOX *src_box);
    void CopyResource(ID3D12Resource *dst_resource, ID3D12Resource *src_resource);

    void CopyTiles(ID3D12Resource *tiled_resource,
            const D3D12_TILED_RESOURCE_COORDINATE *tile_region_start_coordinate,
            const D3D12_TILE_REGION_SIZE *tile_region_size,
            ID3D12Resource *buffer,
            UINT64 buffer_offset,
            D3D12_TILE_COPY_FLAGS flags);

    void ResolveSubresource(ID3D12Resource *dst_resource, UINT dst_sub_resource,
            ID3D12Resource *src_resource, UINT src_sub_resource,
            DXGI_FORMAT format);

    void IASetPrimitiveTopology(D3D12_PRIMITIVE_TOPOLOGY primitive_topology);

    void RSSetViewports(UINT viewport_count, const D3D12_VIEWPORT *viewports);
    void RSSetScissorRects(UINT rect_count, const D3D12_RECT *rects);

    void OMSetBlendFactor(const FLOAT blend_factor[4]);
    void OMSetStencilRef(UINT stencil_ref);

    void SetPipelineState(ID3D12PipelineState *pipeline_state);

    void ResourceBarrier(UINT barrier_count, const D3D12_RESOURCE_BARRIER *barriers);

    void ExecuteBundle(ID3D12GraphicsCommandList *command_list);

    void SetDescriptorHeaps(UINT heap_count, ID3D12DescriptorHeap * const *heaps);

    void SetComputeRootSignature(ID3D12RootSignature *root_signature);
    void SetGraphicsRootSignature(ID3D12RootSignature *root_signature);

    void SetComputeRootDescriptorTable(UINT root_parameter_index, D3D12_GPU_DESCRIPTOR_HANDLE base_descriptor);
    void SetGraphicsRootDescriptorTable(UINT root_parameter_index, D3D12_GPU_DESCRIPTOR_HANDLE base_descriptor);

    void SetComputeRoot32BitConstant(UINT root_parameter_index, UINT data, UINT dst_offset);
    void SetGraphicsRoot32BitConstant(UINT root_parameter_index, UINT data, UINT dst_offset);

    void SetComputeRoot32BitConstants(UINT root_parameter_index, UINT constant_count, const void *data,
            UINT dst_offset);
    void SetGraphicsRoot32BitConstants(UINT root_parameter_index, UINT constant_count, const void *data,
            UINT dst_offset);

    void SetComputeRootConstantBufferView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);
    void SetGraphicsRootConstantBufferView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);

    void SetComputeRootShaderResourceView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);
    void SetGraphicsRootShaderResourceView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);

    void SetComputeRootUnorderedAccessView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);
    void SetGraphicsRootUnorderedAccessView(UINT root_parameter_index, D3D12_GPU_VIRTUAL_ADDRESS address);

    void IASetIndexBuffer(const D3D12_INDEX_BUFFER_VIEW *view);
    void IASetVertexBuffers(UINT start_slot, UINT view_count, const D3D12_VERTEX_BUFFER_VIEW *views);

    void SOSetTargets(UINT start_slot, UINT view_count, const D3D12_STREAM_OUTPUT_BUFFER_VIEW *views);

    void OMSetRenderTargets(UINT render_target_descriptor_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE *render_target_descriptors,
            BOOL single_descriptor_handle,
            const D3D12_CPU_DESCRIPTOR_HANDLE *depth_stencil_descriptor);

    void ClearDepthStencilView(D3D12_CPU_DESCRIPTOR_HANDLE dsv, D3D12_CLEAR_FLAGS flags,
            FLOAT depth, UINT8 stencil, UINT rect_count, const D3D12_RECT *rects);
    void ClearRenderTargetView(D3D12_CPU_DESCRIPTOR_HANDLE rtv, const FLOAT color[4],
            UINT rect_count, const D3D12_RECT *rects);
    void ClearUnorderedAccessViewUint(D3D12_GPU_DESCRIPTOR_HANDLE gpu_handle,
            D3D12_CPU_DESCRIPTOR_HANDLE cpu_handle, ID3D12Resource *resource, const UINT values[4],
            UINT rect_count, const D3D12_RECT *rects);
    void ClearUnorderedAccessViewFloat(D3D12_GPU_DESCRIPTOR_HANDLE gpu_handle,
            D3D12_CPU_DESCRIPTOR_HANDLE cpu_handle, ID3D12Resource *resource, const float values[4],
            UINT rect_count, const D3D12_RECT *rects);

    void DiscardResource(ID3D12Resource *resource, const D3D12_DISCARD_REGION *region);

    void BeginQuery(ID3D12QueryHeap *heap, D3D12_QUERY_TYPE type, UINT index);
    void EndQuery(ID3D12QueryHeap *heap, D3D12_QUERY_TYPE type, UINT index);
    void ResolveQueryData(ID3D12QueryHeap *heap, D3D12_QUERY_TYPE type,
            UINT start_index, UINT query_count,
            ID3D12Resource *dst_buffer, UINT64 aligned_dst_buffer_offset);

    void SetPredication(ID3D12Resource *buffer, UINT64 aligned_buffer_offset,
            D3D12_PREDICATION_OP operation);

    void SetMarker(UINT metadata, const void *data, UINT size);
    void BeginEvent(UINT metadata, const void *data, UINT size);
    void EndEvent();

    void ExecuteIndirect(ID3D12CommandSignature *command_signature,
            UINT max_command_count, ID3D12Resource *arg_buffer, UINT64 arg_buffer_offset,
            ID3D12Resource *count_buffer, UINT64 count_buffer_offset);
}

[
    uuid(553103fb-1fe7-4557-bb38-946d7d0e7ca7),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList1 : ID3D12GraphicsCommandList
{
    void AtomicCopyBufferUINT(ID3D12Resource *dst_buffer, UINT64 dst_offset,
            ID3D12Resource *src_buffer, UINT64 src_offset,
            UINT dependent_resource_count, ID3D12Resource * const *dependent_resources,
            const D3D12_SUBRESOURCE_RANGE_UINT64 *dependent_sub_resource_ranges);

    void AtomicCopyBufferUINT64(ID3D12Resource *dst_buffer, UINT64 dst_offset,
            ID3D12Resource *src_buffer, UINT64 src_offset,
            UINT dependent_resource_count, ID3D12Resource * const *dependent_resources,
            const D3D12_SUBRESOURCE_RANGE_UINT64 *dependent_sub_resource_ranges);

    void OMSetDepthBounds(FLOAT min, FLOAT max);

    void SetSamplePositions(UINT sample_count, UINT pixel_count,
            D3D12_SAMPLE_POSITION *sample_positions);

    void ResolveSubresourceRegion(ID3D12Resource *dst_resource,
            UINT dst_sub_resource_idx, UINT dst_x, UINT dst_y,
            ID3D12Resource *src_resource, UINT src_sub_resource_idx,
            D3D12_RECT *src_rect, DXGI_FORMAT format, D3D12_RESOLVE_MODE mode);

    void SetViewInstanceMask(UINT mask);
}

[
    uuid(38c3e585-ff17-412c-9150-4fc6f9d72a28),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12GraphicsCommandList2 : ID3D12GraphicsCommandList1
{
    void WriteBufferImmediate(UINT count,
            const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *parameters,
            const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);
}

typedef enum D3D12_TILE_RANGE_FLAGS
{
    D3D12_TILE_RANGE_FLAG_NONE = 0x0,
    D3D12_TILE_RANGE_FLAG_NULL = 0x1,
    D3D12_TILE_RANGE_FLAG_SKIP = 0x2,
    D3D12_TILE_RANGE_FLAG_REUSE_SINGLE_TILE = 0x4
} D3D12_TILE_RANGE_FLAGS;

typedef enum D3D12_TILE_MAPPING_FLAGS
{
    D3D12_TILE_MAPPING_FLAG_NONE = 0x0,
    D3D12_TILE_MAPPING_FLAG_NO_HAZARD = 0x1,
} D3D12_TILE_MAPPING_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_TILE_MAPPING_FLAGS);")

[
    uuid(0ec870a6-5d7e-4c22-8cfc-5baae07616ed),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandQueue : ID3D12Pageable
{
    void UpdateTileMappings(ID3D12Resource *resource, UINT region_count,
            const D3D12_TILED_RESOURCE_COORDINATE *region_start_coordinates,
            const D3D12_TILE_REGION_SIZE *region_sizes,
            ID3D12Heap *heap,
            UINT range_count,
            const D3D12_TILE_RANGE_FLAGS *range_flags,
            UINT *heap_range_offsets,
            UINT *range_tile_counts,
            D3D12_TILE_MAPPING_FLAGS flags);

    void CopyTileMappings(ID3D12Resource *dst_resource,
            const D3D12_TILED_RESOURCE_COORDINATE *dst_region_start_coordinate,
            ID3D12Resource *src_resource,
            const D3D12_TILED_RESOURCE_COORDINATE *src_region_start_coordinate,
            const D3D12_TILE_REGION_SIZE *region_size,
            D3D12_TILE_MAPPING_FLAGS flags);

    void ExecuteCommandLists(UINT command_list_count,
            ID3D12CommandList * const * command_lists);

    void SetMarker(UINT metadata, const void *data, UINT size);
    void BeginEvent(UINT metadata, const void *data, UINT size);
    void EndEvent();

    HRESULT Signal(ID3D12Fence *fence, UINT64 value);
    HRESULT Wait(ID3D12Fence *fence, UINT64 value);

    HRESULT GetTimestampFrequency(UINT64 *frequency);
    HRESULT GetClockCalibration(UINT64 *gpu_timestamp, UINT64 *cpu_timestamp);

    D3D12_COMMAND_QUEUE_DESC GetDesc();
}

typedef enum D3D12_FENCE_FLAGS
{
    D3D12_FENCE_FLAG_NONE = 0x0,
    D3D12_FENCE_FLAG_SHARED = 0x1,
    D3D12_FENCE_FLAG_SHARED_CROSS_ADAPTER = 0x2,
} D3D12_FENCE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_FENCE_FLAGS);")

typedef enum D3D12_QUERY_HEAP_TYPE
{
    D3D12_QUERY_HEAP_TYPE_OCCLUSION = 0,
    D3D12_QUERY_HEAP_TYPE_TIMESTAMP = 1,
    D3D12_QUERY_HEAP_TYPE_PIPELINE_STATISTICS = 2,
    D3D12_QUERY_HEAP_TYPE_SO_STATISTICS = 3,
} D3D12_QUERY_HEAP_TYPE;

typedef struct D3D12_QUERY_HEAP_DESC
{
    D3D12_QUERY_HEAP_TYPE Type;
    UINT Count;
    UINT NodeMask;
} D3D12_QUERY_HEAP_DESC;

typedef enum D3D12_INDIRECT_ARGUMENT_TYPE
{
    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW,
    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH,
    D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT,
    D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW,
} D3D12_INDIRECT_ARGUMENT_TYPE;

typedef struct D3D12_INDIRECT_ARGUMENT_DESC
{
    D3D12_INDIRECT_ARGUMENT_TYPE Type;
    union
    {
        struct
        {
            UINT Slot;
        } VertexBuffer;
        struct
        {
            UINT RootParameterIndex;
            UINT DestOffsetIn32BitValues;
            UINT Num32BitValuesToSet;
        } Constant;
        struct
        {
            UINT RootParameterIndex;
        } ConstantBufferView;
        struct
        {
            UINT RootParameterIndex;
        } ShaderResourceView;
        struct
        {
            UINT RootParameterIndex;
        } UnorderedAccessView;
    };
} D3D12_INDIRECT_ARGUMENT_DESC;

typedef struct D3D12_COMMAND_SIGNATURE_DESC
{
    UINT ByteStride;
    UINT NumArgumentDescs;
    const D3D12_INDIRECT_ARGUMENT_DESC *pArgumentDescs;
    UINT NodeMask;
} D3D12_COMMAND_SIGNATURE_DESC;

[
    uuid(c54a6b66-72df-4ee8-8be5-a946a1429214),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12RootSignature : ID3D12DeviceChild
{
}

[
    uuid(765a30f3-f624-4c6f-a828-ace948622445),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12PipelineState : ID3D12Pageable
{
    HRESULT GetCachedBlob(ID3DBlob **blob);
}

[
    uuid(0a753dcf-c4d8-4b91-adf6-be5a60d95a76),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Fence : ID3D12Pageable
{
    UINT64 GetCompletedValue();
    HRESULT SetEventOnCompletion(UINT64 value, HANDLE event);
    HRESULT Signal(UINT64 value);
}

[
    uuid(6102dee4-af59-4b09-b999-b44d73f09b24),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12CommandAllocator : ID3D12Pageable
{
    HRESULT Reset();
}

[
    uuid(189819f1-1db6-4b57-be54-1821339b85f7),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device : ID3D12Object
{
    UINT GetNodeCount();

    HRESULT CreateCommandQueue(const D3D12_COMMAND_QUEUE_DESC *desc,
            REFIID riid, void **command_queue);
    HRESULT CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE type,
            REFIID riid, void **command_allocator);
    HRESULT CreateGraphicsPipelineState(const D3D12_GRAPHICS_PIPELINE_STATE_DESC *desc,
            REFIID riid, void **pipeline_state);
    HRESULT CreateComputePipelineState(const D3D12_COMPUTE_PIPELINE_STATE_DESC *desc,
            REFIID riid, void **pipeline_state);
    HRESULT CreateCommandList(UINT node_mask,
            D3D12_COMMAND_LIST_TYPE type,
            ID3D12CommandAllocator *command_allocator,
            ID3D12PipelineState *initial_pipeline_state,
            REFIID riid, void **command_list);

    HRESULT CheckFeatureSupport(D3D12_FEATURE feature,
            void *feature_data, UINT feature_data_size);

    HRESULT CreateDescriptorHeap(const D3D12_DESCRIPTOR_HEAP_DESC *desc,
            REFIID riid, void **descriptor_heap);
    UINT GetDescriptorHandleIncrementSize(D3D12_DESCRIPTOR_HEAP_TYPE descriptor_heap_type);

    HRESULT CreateRootSignature(UINT node_mask,
            const void *bytecode, SIZE_T bytecode_length,
            REFIID riid, void **root_signature);

    void CreateConstantBufferView(const D3D12_CONSTANT_BUFFER_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateShaderResourceView(ID3D12Resource *resource,
            const D3D12_SHADER_RESOURCE_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateUnorderedAccessView(ID3D12Resource *resource, ID3D12Resource *counter_resource,
            const D3D12_UNORDERED_ACCESS_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateRenderTargetView(ID3D12Resource *resource,
            const D3D12_RENDER_TARGET_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateDepthStencilView(ID3D12Resource *resource,
            const D3D12_DEPTH_STENCIL_VIEW_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);
    void CreateSampler(const D3D12_SAMPLER_DESC *desc,
            D3D12_CPU_DESCRIPTOR_HANDLE descriptor);

    void CopyDescriptors(UINT dst_descriptor_range_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE *dst_descriptor_range_offsets,
            const UINT *dst_descriptor_range_sizes,
            UINT src_descriptor_range_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE *src_descriptor_range_offsets,
            const UINT *src_descriptor_range_sizes,
            D3D12_DESCRIPTOR_HEAP_TYPE descriptor_heap_type);
    void CopyDescriptorsSimple(UINT descriptor_count,
            const D3D12_CPU_DESCRIPTOR_HANDLE dst_descriptor_range_offset,
            const D3D12_CPU_DESCRIPTOR_HANDLE src_descriptor_range_offset,
            D3D12_DESCRIPTOR_HEAP_TYPE descriptor_heap_type);

    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo(UINT visible_mask,
            UINT reource_desc_count, const D3D12_RESOURCE_DESC *resource_descs);

    D3D12_HEAP_PROPERTIES GetCustomHeapProperties(UINT node_mask,
            D3D12_HEAP_TYPE heap_type);

    HRESULT CreateCommittedResource(const D3D12_HEAP_PROPERTIES *heap_properties, D3D12_HEAP_FLAGS heap_flags,
            const D3D12_RESOURCE_DESC *desc, D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);

    HRESULT CreateHeap(const D3D12_HEAP_DESC *desc, REFIID riid, void **heap);

    HRESULT CreatePlacedResource(ID3D12Heap *heap, UINT64 heap_offset,
            const D3D12_RESOURCE_DESC *desc, D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);
    HRESULT CreateReservedResource(const D3D12_RESOURCE_DESC *desc, D3D12_RESOURCE_STATES initial_state,
            const D3D12_CLEAR_VALUE *optimized_clear_value,
            REFIID riid, void **resource);

    HRESULT CreateSharedHandle(ID3D12DeviceChild *object,
            const SECURITY_ATTRIBUTES *attributes, DWORD access,
            const WCHAR *name, HANDLE *handle);
    HRESULT OpenSharedHandle(HANDLE handle,
            REFIID riid, void **object);
    HRESULT OpenSharedHandleByName(const WCHAR *name, DWORD access, HANDLE *handle);

    HRESULT MakeResident(UINT object_count, ID3D12Pageable * const *objects);
    HRESULT Evict(UINT object_count, ID3D12Pageable * const *objects);

    HRESULT CreateFence(UINT64 initial_value, D3D12_FENCE_FLAGS flags, REFIID riid, void **fence);

    HRESULT GetDeviceRemovedReason();

    void GetCopyableFootprints(const D3D12_RESOURCE_DESC *desc,
            UINT first_sub_resource,
            UINT sub_resource_count,
            UINT64 base_offset,
            D3D12_PLACED_SUBRESOURCE_FOOTPRINT *layouts,
            UINT *row_count,
            UINT64 *row_size,
            UINT64 *total_bytes);

    HRESULT CreateQueryHeap(const D3D12_QUERY_HEAP_DESC *desc,
            REFIID riid, void **heap);

    HRESULT SetStablePowerState(BOOL enable);

    HRESULT CreateCommandSignature(const D3D12_COMMAND_SIGNATURE_DESC *desc,
            ID3D12RootSignature *root_signature,
            REFIID riid, void **command_signature);

    void GetResourceTiling(ID3D12Resource *resource,
            UINT *total_tile_count,
            D3D12_PACKED_MIP_INFO *packed_mip_info,
            D3D12_TILE_SHAPE *standard_tile_shape,
            UINT *sub_resource_tiling_count,
            UINT first_sub_resource_tiling,
            D3D12_SUBRESOURCE_TILING *sub_resource_tilings);

    LUID GetAdapterLuid();
}

[
    uuid(77acce80-638e-4e65-8895-c1f23386863e),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Device1 : ID3D12Device
{
    HRESULT CreatePipelineLibrary(const void *blob, SIZE_T blob_size, REFIID iid, void **lib);

    HRESULT SetEventOnMultipleFenceCompletion(ID3D12Fence * const *fences,
            const UINT64 *values, UINT fence_count, D3D12_MULTIPLE_FENCE_WAIT_FLAGS flags, HANDLE event);

    HRESULT SetResidencyPriority(UINT object_count, ID3D12Pageable * const *objects,
            const D3D12_RESIDENCY_PRIORITY *priorities);
}

[
    uuid(34ab647b-3cc8-46ac-841b-c0965645c046),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12RootSignatureDeserializer : IUnknown
{
    const D3D12_ROOT_SIGNATURE_DESC *GetRootSignatureDesc();
}

[
    uuid(7f91ce67-090c-4bb7-b78e-ed8ff2e31da0),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VersionedRootSignatureDeserializer : IUnknown
{
    HRESULT GetRootSignatureDescAtVersion(D3D_ROOT_SIGNATURE_VERSION version,
            const D3D12_VERSIONED_ROOT_SIGNATURE_DESC **desc);

    const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *GetUnconvertedRootSignatureDesc();
};

[local] HRESULT __stdcall D3D12CreateRootSignatureDeserializer(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

typedef HRESULT (__stdcall *PFN_D3D12_CREATE_VERSIONED_ROOT_SIGNATURE_DESERIALIZER)(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

[local] HRESULT __stdcall D3D12CreateVersionedRootSignatureDeserializer(
        const void *data, SIZE_T data_size, REFIID iid, void **deserializer);

[local] HRESULT __stdcall D3D12SerializeRootSignature(
        const D3D12_ROOT_SIGNATURE_DESC *root_signature_desc,
        D3D_ROOT_SIGNATURE_VERSION version, ID3DBlob **blob, ID3DBlob **error_blob);

typedef HRESULT (__stdcall *PFN_D3D12_SERIALIZE_VERSIONED_ROOT_SIGNATURE)(
        const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *desc, ID3DBlob **blob, ID3DBlob **error_blob);

[local] HRESULT __stdcall D3D12SerializeVersionedRootSignature(
        const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *root_signature_desc,
        ID3DBlob **blob, ID3DBlob **error_blob);

typedef HRESULT (__stdcall *PFN_D3D12_CREATE_DEVICE)(IUnknown *adapter,
        D3D_FEATURE_LEVEL minimum_feature_level, REFIID iid, void **device);

[local] HRESULT __stdcall D3D12CreateDevice(IUnknown *adapter,
        D3D_FEATURE_LEVEL minimum_feature_level, REFIID iid, void **device);

typedef HRESULT (__stdcall *PFN_D3D12_GET_DEBUG_INTERFACE)(REFIID iid, void **debug);

[local] HRESULT __stdcall D3D12GetDebugInterface(REFIID iid, void **debug);

[local] HRESULT __stdcall D3D12EnableExperimentalFeatures(UINT feature_count,
        const IID *iids, void *configurations, UINT *configurations_sizes);

cpp_quote("DEFINE_GUID(CLSID_D3D12Debug,                     0xf2352aeb, 0xdd84, 0x49fe, 0xb9, 0x7b, 0xa9, 0xdc, 0xfd, 0xcc, 0x1b, 0x4f);")
cpp_quote("DEFINE_GUID(CLSID_D3D12Tools,                     0xe38216b1, 0x3c8c, 0x4833, 0xaa, 0x09, 0x0a, 0x06, 0xb6, 0x5d, 0x96, 0xc8);")
cpp_quote("DEFINE_GUID(CLSID_D3D12DeviceRemovedExtendedData, 0x4a75bbc4, 0x9ff4, 0x4ad8, 0x9f, 0x18, 0xab, 0xae, 0x84, 0xdc, 0x5f, 0xf2);")
cpp_quote("DEFINE_GUID(CLSID_D3D12SDKConfiguration,          0x7cda6aca, 0xa03e, 0x49c8, 0x94, 0x58, 0x03, 0x34, 0xd2, 0x0e, 0x07, 0xce);")

typedef enum D3D12_DRED_ENABLEMENT
{
    D3D12_DRED_ENABLEMENT_SYSTEM_CONTROLLED = 0,
    D3D12_DRED_ENABLEMENT_FORCED_OFF        = 1,
    D3D12_DRED_ENABLEMENT_FORCED_ON         = 2,
} D3D12_DRED_ENABLEMENT;

typedef HRESULT (__stdcall *PFN_D3D12_GET_INTERFACE)(REFCLSID clsid, REFIID iid, void **debug);

[local] HRESULT __stdcall D3D12GetInterface(REFCLSID clsid, REFIID iid, void **debug);

[
    uuid(7071e1f0-e84b-4b33-974f-12fa49de65c5),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12Tools : IUnknown
{
    void EnableShaderInstrumentation(BOOL enable);
    BOOL ShaderInstrumentationEnabled();
}

[
    uuid(82bc481c-6b9b-4030-aedb-7ee3d1df1e63),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12DeviceRemovedExtendedDataSettings : IUnknown
{
    void SetAutoBreadcrumbsEnablement(D3D12_DRED_ENABLEMENT enablement);
    void SetPageFaultEnablement(D3D12_DRED_ENABLEMENT enablement);
    void SetWatsonDumpEnablement(D3D12_DRED_ENABLEMENT enablement);
};

[
    uuid(e9eb5314-33aa-42b2-a718-d77f58b1f1c7),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12SDKConfiguration : IUnknown
{
    HRESULT SetSDKVersion(UINT version, const char *path);
}
