;
; Definition file of SETUPAPI.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "SETUPAPI.dll"
EXPORTS
CMP_GetBlockedDriverInfo
CMP_GetServerSideDeviceInstallFlags
CMP_Init_Detection
CMP_RegisterNotification
CMP_Report_LogOn
CMP_UnregisterNotification
CMP_WaitNoPendingInstallEvents
CMP_WaitServicesAvailable
CM_Add_Driver_PackageW
CM_Add_Empty_Log_Conf
CM_Add_Empty_Log_Conf_Ex
CM_Add_IDA
CM_Add_IDW
CM_Add_ID_ExA
CM_Add_ID_ExW
CM_Add_Range
CM_Add_Res_Des
CM_Add_Res_Des_Ex
CM_Apply_PowerScheme
CM_Connect_MachineA
CM_Connect_MachineW
CM_Create_DevNodeA
CM_Create_DevNodeW
CM_Create_DevNode_ExA
CM_Create_DevNode_ExW
CM_Create_Range_List
CM_Delete_Class_Key
CM_Delete_Class_Key_Ex
CM_Delete_DevNode_Key
CM_Delete_DevNode_Key_Ex
CM_Delete_Device_Interface_KeyA
CM_Delete_Device_Interface_KeyW
CM_Delete_Device_Interface_Key_ExA
CM_Delete_Device_Interface_Key_ExW
CM_Delete_Driver_PackageW
CM_Delete_PowerScheme
CM_Delete_Range
CM_Detect_Resource_Conflict
CM_Detect_Resource_Conflict_Ex
CM_Disable_DevNode
CM_Disable_DevNode_Ex
CM_Disconnect_Machine
CM_Dup_Range_List
CM_Duplicate_PowerScheme
CM_Enable_DevNode
CM_Enable_DevNode_Ex
CM_Enumerate_Classes
CM_Enumerate_Classes_Ex
CM_Enumerate_EnumeratorsA
CM_Enumerate_EnumeratorsW
CM_Enumerate_Enumerators_ExA
CM_Enumerate_Enumerators_ExW
CM_Find_Range
CM_First_Range
CM_Free_Log_Conf
CM_Free_Log_Conf_Ex
CM_Free_Log_Conf_Handle
CM_Free_Range_List
CM_Free_Res_Des
CM_Free_Res_Des_Ex
CM_Free_Res_Des_Handle
CM_Free_Resource_Conflict_Handle
CM_Get_Child
CM_Get_Child_Ex
CM_Get_Class_Key_NameA
CM_Get_Class_Key_NameW
CM_Get_Class_Key_Name_ExA
CM_Get_Class_Key_Name_ExW
CM_Get_Class_NameA
CM_Get_Class_NameW
CM_Get_Class_Name_ExA
CM_Get_Class_Name_ExW
CM_Get_Class_Registry_PropertyA
CM_Get_Class_Registry_PropertyW
CM_Get_Depth
CM_Get_Depth_Ex
CM_Get_DevNode_Custom_PropertyA
CM_Get_DevNode_Custom_PropertyW
CM_Get_DevNode_Custom_Property_ExA
CM_Get_DevNode_Custom_Property_ExW
CM_Get_DevNode_Registry_PropertyA
CM_Get_DevNode_Registry_PropertyW
CM_Get_DevNode_Registry_Property_ExA
CM_Get_DevNode_Registry_Property_ExW
CM_Get_DevNode_Status
CM_Get_DevNode_Status_Ex
CM_Get_Device_IDA
CM_Get_Device_IDW
CM_Get_Device_ID_ExA
CM_Get_Device_ID_ExW
CM_Get_Device_ID_ListA
CM_Get_Device_ID_ListW
CM_Get_Device_ID_List_ExA
CM_Get_Device_ID_List_ExW
CM_Get_Device_ID_List_SizeA
CM_Get_Device_ID_List_SizeW
CM_Get_Device_ID_List_Size_ExA
CM_Get_Device_ID_List_Size_ExW
CM_Get_Device_ID_Size
CM_Get_Device_ID_Size_Ex
CM_Get_Device_Interface_AliasA
CM_Get_Device_Interface_AliasW
CM_Get_Device_Interface_Alias_ExA
CM_Get_Device_Interface_Alias_ExW
CM_Get_Device_Interface_ListA
CM_Get_Device_Interface_ListW
CM_Get_Device_Interface_List_ExA
CM_Get_Device_Interface_List_ExW
CM_Get_Device_Interface_List_SizeA
CM_Get_Device_Interface_List_SizeW
CM_Get_Device_Interface_List_Size_ExA
CM_Get_Device_Interface_List_Size_ExW
CM_Get_First_Log_Conf
CM_Get_First_Log_Conf_Ex
CM_Get_Global_State
CM_Get_Global_State_Ex
CM_Get_HW_Prof_FlagsA
CM_Get_HW_Prof_FlagsW
CM_Get_HW_Prof_Flags_ExA
CM_Get_HW_Prof_Flags_ExW
CM_Get_Hardware_Profile_InfoA
CM_Get_Hardware_Profile_InfoW
CM_Get_Hardware_Profile_Info_ExA
CM_Get_Hardware_Profile_Info_ExW
CM_Get_Log_Conf_Priority
CM_Get_Log_Conf_Priority_Ex
CM_Get_Next_Log_Conf
CM_Get_Next_Log_Conf_Ex
CM_Get_Next_Res_Des
CM_Get_Next_Res_Des_Ex
CM_Get_Parent
CM_Get_Parent_Ex
CM_Get_Res_Des_Data
CM_Get_Res_Des_Data_Ex
CM_Get_Res_Des_Data_Size
CM_Get_Res_Des_Data_Size_Ex
CM_Get_Resource_Conflict_Count
CM_Get_Resource_Conflict_DetailsA
CM_Get_Resource_Conflict_DetailsW
CM_Get_Sibling
CM_Get_Sibling_Ex
CM_Get_Version
CM_Get_Version_Ex
CM_Import_PowerScheme
CM_Install_DevNodeW
CM_Install_DevNode_ExW
CM_Intersect_Range_List
CM_Invert_Range_List
CM_Is_Dock_Station_Present
CM_Is_Dock_Station_Present_Ex
CM_Is_Version_Available
CM_Is_Version_Available_Ex
CM_Locate_DevNodeA
CM_Locate_DevNodeW
CM_Locate_DevNode_ExA
CM_Locate_DevNode_ExW
CM_Merge_Range_List
CM_Modify_Res_Des
CM_Modify_Res_Des_Ex
CM_Move_DevNode
CM_Move_DevNode_Ex
CM_Next_Range
CM_Open_Class_KeyA
CM_Open_Class_KeyW
CM_Open_Class_Key_ExA
CM_Open_Class_Key_ExW
CM_Open_DevNode_Key
CM_Open_DevNode_Key_Ex
CM_Open_Device_Interface_KeyA
CM_Open_Device_Interface_KeyW
CM_Open_Device_Interface_Key_ExA
CM_Open_Device_Interface_Key_ExW
CM_Query_And_Remove_SubTreeA
CM_Query_And_Remove_SubTreeW
CM_Query_And_Remove_SubTree_ExA
CM_Query_And_Remove_SubTree_ExW
CM_Query_Arbitrator_Free_Data
CM_Query_Arbitrator_Free_Data_Ex
CM_Query_Arbitrator_Free_Size
CM_Query_Arbitrator_Free_Size_Ex
CM_Query_Remove_SubTree
CM_Query_Remove_SubTree_Ex
CM_Query_Resource_Conflict_List
CM_Reenumerate_DevNode
CM_Reenumerate_DevNode_Ex
CM_Register_Device_Driver
CM_Register_Device_Driver_Ex
CM_Register_Device_InterfaceA
CM_Register_Device_InterfaceW
CM_Register_Device_Interface_ExA
CM_Register_Device_Interface_ExW
CM_Remove_SubTree
CM_Remove_SubTree_Ex
CM_Request_Device_EjectA
CM_Request_Device_EjectW
CM_Request_Device_Eject_ExA
CM_Request_Device_Eject_ExW
CM_Request_Eject_PC
CM_Request_Eject_PC_Ex
CM_RestoreAll_DefaultPowerSchemes
CM_Restore_DefaultPowerScheme
CM_Run_Detection
CM_Run_Detection_Ex
CM_Set_ActiveScheme
CM_Set_Class_Registry_PropertyA
CM_Set_Class_Registry_PropertyW
CM_Set_DevNode_Problem
CM_Set_DevNode_Problem_Ex
CM_Set_DevNode_Registry_PropertyA
CM_Set_DevNode_Registry_PropertyW
CM_Set_DevNode_Registry_Property_ExA
CM_Set_DevNode_Registry_Property_ExW
CM_Set_HW_Prof
CM_Set_HW_Prof_Ex
CM_Set_HW_Prof_FlagsA
CM_Set_HW_Prof_FlagsW
CM_Set_HW_Prof_Flags_ExA
CM_Set_HW_Prof_Flags_ExW
CM_Setup_DevNode
CM_Setup_DevNode_Ex
CM_Test_Range_Available
CM_Uninstall_DevNode
CM_Uninstall_DevNode_Ex
CM_Unregister_Device_InterfaceA
CM_Unregister_Device_InterfaceW
CM_Unregister_Device_Interface_ExA
CM_Unregister_Device_Interface_ExW
CM_Write_UserPowerKey
DoesUserHavePrivilege
DriverStoreAddDriverPackageA
DriverStoreAddDriverPackageW
DriverStoreDeleteDriverPackageA
DriverStoreDeleteDriverPackageW
DriverStoreEnumDriverPackageA
DriverStoreEnumDriverPackageW
DriverStoreFindDriverPackageA
DriverStoreFindDriverPackageW
ExtensionPropSheetPageProc
InstallCatalog
InstallHinfSection
InstallHinfSectionA
InstallHinfSectionW
IsUserAdmin
MyFree
MyMalloc
MyRealloc
PnpEnumDrpFile
PnpIsFileAclIntact
PnpIsFileContentIntact
PnpIsFilePnpDriver
PnpRepairWindowsProtectedDriver
Remote_CMP_GetServerSideDeviceInstallFlags
Remote_CMP_WaitServicesAvailable
Remote_CM_Add_Empty_Log_Conf
Remote_CM_Add_ID
Remote_CM_Add_Res_Des
Remote_CM_Connect_Machine_Worker
Remote_CM_Create_DevNode
Remote_CM_Delete_Class_Key
Remote_CM_Delete_DevNode_Key
Remote_CM_Delete_Device_Interface_Key
Remote_CM_Disable_DevNode
Remote_CM_Disconnect_Machine_Worker
Remote_CM_Enable_DevNode
Remote_CM_Enumerate_Classes
Remote_CM_Enumerate_Enumerators
Remote_CM_Free_Log_Conf
Remote_CM_Free_Res_Des
Remote_CM_Get_Child
Remote_CM_Get_Class_Name
Remote_CM_Get_Class_Property
Remote_CM_Get_Class_Property_Keys
Remote_CM_Get_Class_Registry_Property
Remote_CM_Get_Depth
Remote_CM_Get_DevNode_Custom_Property
Remote_CM_Get_DevNode_Property
Remote_CM_Get_DevNode_Property_Keys
Remote_CM_Get_DevNode_Registry_Property
Remote_CM_Get_DevNode_Status
Remote_CM_Get_Device_ID_List
Remote_CM_Get_Device_ID_List_Size
Remote_CM_Get_Device_Interface_Alias
Remote_CM_Get_Device_Interface_List
Remote_CM_Get_Device_Interface_List_Size
Remote_CM_Get_Device_Interface_Property
Remote_CM_Get_Device_Interface_Property_Keys
Remote_CM_Get_First_Log_Conf
Remote_CM_Get_Global_State
Remote_CM_Get_HW_Prof_Flags
Remote_CM_Get_Hardware_Profile_Info
Remote_CM_Get_Log_Conf_Priority
Remote_CM_Get_Next_Log_Conf
Remote_CM_Get_Next_Res_Des
Remote_CM_Get_Parent
Remote_CM_Get_Res_Des_Data
Remote_CM_Get_Res_Des_Data_Size
Remote_CM_Get_Sibling
Remote_CM_Get_Version
Remote_CM_Install_DevNode
Remote_CM_Is_Dock_Station_Present
Remote_CM_Is_Version_Available
Remote_CM_Locate_DevNode_Worker
Remote_CM_Modify_Res_Des
Remote_CM_Open_Class_Key
Remote_CM_Open_DevNode_Key
Remote_CM_Open_Device_Interface_Key
Remote_CM_Query_And_Remove_SubTree
Remote_CM_Query_Arbitrator_Free_Data
Remote_CM_Query_Arbitrator_Free_Size
Remote_CM_Query_Resource_Conflict_List_Worker
Remote_CM_Reenumerate_DevNode
Remote_CM_Register_Device_Driver
Remote_CM_Register_Device_Interface
Remote_CM_Request_Device_Eject
Remote_CM_Request_Eject_PC
Remote_CM_Run_Detection
Remote_CM_Set_Class_Property
Remote_CM_Set_Class_Registry_Property
Remote_CM_Set_DevNode_Problem
Remote_CM_Set_DevNode_Property
Remote_CM_Set_DevNode_Registry_Property
Remote_CM_Set_Device_Interface_Property
Remote_CM_Set_HW_Prof
Remote_CM_Set_HW_Prof_Flags
Remote_CM_Setup_DevNode
Remote_CM_Uninstall_DevNode
Remote_CM_Unregister_Device_Interface
SetupAddInstallSectionToDiskSpaceListA
SetupAddInstallSectionToDiskSpaceListW
SetupAddSectionToDiskSpaceListA
SetupAddSectionToDiskSpaceListW
SetupAddToDiskSpaceListA
SetupAddToDiskSpaceListW
SetupAddToSourceListA
SetupAddToSourceListW
SetupAdjustDiskSpaceListA
SetupAdjustDiskSpaceListW
SetupBackupErrorA
SetupBackupErrorW
SetupCancelTemporarySourceList
SetupCloseFileQueue
SetupCloseInfFile
SetupCloseLog
SetupCommitFileQueue
SetupCommitFileQueueA
SetupCommitFileQueueW
SetupConfigureWmiFromInfSectionA
SetupConfigureWmiFromInfSectionW
SetupCopyErrorA
SetupCopyErrorW
SetupCopyOEMInfA
SetupCopyOEMInfW
SetupCreateDiskSpaceListA
SetupCreateDiskSpaceListW
SetupDecompressOrCopyFileA
SetupDecompressOrCopyFileW
SetupDefaultQueueCallback
SetupDefaultQueueCallbackA
SetupDefaultQueueCallbackW
SetupDeleteErrorA
SetupDeleteErrorW
SetupDestroyDiskSpaceList
SetupDiApplyPowerScheme
SetupDiAskForOEMDisk
SetupDiBuildClassInfoList
SetupDiBuildClassInfoListExA
SetupDiBuildClassInfoListExW
SetupDiBuildDriverInfoList
SetupDiCallClassInstaller
SetupDiCancelDriverInfoSearch
SetupDiChangeState
SetupDiClassGuidsFromNameA
SetupDiClassGuidsFromNameExA
SetupDiClassGuidsFromNameExW
SetupDiClassGuidsFromNameW
SetupDiClassNameFromGuidA
SetupDiClassNameFromGuidExA
SetupDiClassNameFromGuidExW
SetupDiClassNameFromGuidW
SetupDiCreateDevRegKeyA
SetupDiCreateDevRegKeyW
SetupDiCreateDeviceInfoA
SetupDiCreateDeviceInfoList
SetupDiCreateDeviceInfoListExA
SetupDiCreateDeviceInfoListExW
SetupDiCreateDeviceInfoW
SetupDiCreateDeviceInterfaceA
SetupDiCreateDeviceInterfaceRegKeyA
SetupDiCreateDeviceInterfaceRegKeyW
SetupDiCreateDeviceInterfaceW
SetupDiDeleteDevRegKey
SetupDiDeleteDeviceInfo
SetupDiDeleteDeviceInterfaceData
SetupDiDeleteDeviceInterfaceRegKey
SetupDiDestroyClassImageList
SetupDiDestroyDeviceInfoList
SetupDiDestroyDriverInfoList
SetupDiDrawMiniIcon
SetupDiEnumDeviceInfo
SetupDiEnumDeviceInterfaces
SetupDiEnumDriverInfoA
SetupDiEnumDriverInfoW
SetupDiGetActualModelsSectionA
SetupDiGetActualModelsSectionW
SetupDiGetActualSectionToInstallA
SetupDiGetActualSectionToInstallExA
SetupDiGetActualSectionToInstallExW
SetupDiGetActualSectionToInstallW
SetupDiGetClassBitmapIndex
SetupDiGetClassDescriptionA
SetupDiGetClassDescriptionExA
SetupDiGetClassDescriptionExW
SetupDiGetClassDescriptionW
SetupDiGetClassDevPropertySheetsA
SetupDiGetClassDevPropertySheetsW
SetupDiGetClassDevsA
SetupDiGetClassDevsExA
SetupDiGetClassDevsExW
SetupDiGetClassDevsW
SetupDiGetClassImageIndex
SetupDiGetClassImageList
SetupDiGetClassImageListExA
SetupDiGetClassImageListExW
SetupDiGetClassInstallParamsA
SetupDiGetClassInstallParamsW
SetupDiGetClassPropertyExW
SetupDiGetClassPropertyKeys
SetupDiGetClassPropertyKeysExW
SetupDiGetClassPropertyW
SetupDiGetClassRegistryPropertyA
SetupDiGetClassRegistryPropertyW
SetupDiGetCustomDevicePropertyA
SetupDiGetCustomDevicePropertyW
SetupDiGetDeviceInfoListClass
SetupDiGetDeviceInfoListDetailA
SetupDiGetDeviceInfoListDetailW
SetupDiGetDeviceInstallParamsA
SetupDiGetDeviceInstallParamsW
SetupDiGetDeviceInstanceIdA
SetupDiGetDeviceInstanceIdW
SetupDiGetDeviceInterfaceAlias
SetupDiGetDeviceInterfaceDetailA
SetupDiGetDeviceInterfaceDetailW
SetupDiGetDeviceInterfacePropertyKeys
SetupDiGetDeviceInterfacePropertyW
SetupDiGetDevicePropertyKeys
SetupDiGetDevicePropertyW
SetupDiGetDeviceRegistryPropertyA
SetupDiGetDeviceRegistryPropertyW
SetupDiGetDriverInfoDetailA
SetupDiGetDriverInfoDetailW
SetupDiGetDriverInstallParamsA
SetupDiGetDriverInstallParamsW
SetupDiGetHwProfileFriendlyNameA
SetupDiGetHwProfileFriendlyNameExA
SetupDiGetHwProfileFriendlyNameExW
SetupDiGetHwProfileFriendlyNameW
SetupDiGetHwProfileList
SetupDiGetHwProfileListExA
SetupDiGetHwProfileListExW
SetupDiGetINFClassA
SetupDiGetINFClassW
SetupDiGetSelectedDevice
SetupDiGetSelectedDriverA
SetupDiGetSelectedDriverW
SetupDiGetWizardPage
SetupDiInstallClassA
SetupDiInstallClassExA
SetupDiInstallClassExW
SetupDiInstallClassW
SetupDiInstallDevice
SetupDiInstallDeviceInterfaces
SetupDiInstallDriverFiles
SetupDiLoadClassIcon
SetupDiLoadDeviceIcon
SetupDiMoveDuplicateDevice
SetupDiOpenClassRegKey
SetupDiOpenClassRegKeyExA
SetupDiOpenClassRegKeyExW
SetupDiOpenDevRegKey
SetupDiOpenDeviceInfoA
SetupDiOpenDeviceInfoW
SetupDiOpenDeviceInterfaceA
SetupDiOpenDeviceInterfaceRegKey
SetupDiOpenDeviceInterfaceW
SetupDiRegisterCoDeviceInstallers
SetupDiRegisterDeviceInfo
SetupDiRemoveDevice
SetupDiRemoveDeviceInterface
SetupDiReportAdditionalSoftwareRequested
SetupDiReportDeviceInstallError
SetupDiReportDriverNotFoundError
SetupDiReportDriverPackageImportationError
SetupDiReportGenericDriverInstalled
SetupDiReportPnPDeviceProblem
SetupDiRestartDevices
SetupDiSelectBestCompatDrv
SetupDiSelectDevice
SetupDiSelectOEMDrv
SetupDiSetClassInstallParamsA
SetupDiSetClassInstallParamsW
SetupDiSetClassPropertyExW
SetupDiSetClassPropertyW
SetupDiSetClassRegistryPropertyA
SetupDiSetClassRegistryPropertyW
SetupDiSetDeviceInstallParamsA
SetupDiSetDeviceInstallParamsW
SetupDiSetDeviceInterfaceDefault
SetupDiSetDeviceInterfacePropertyW
SetupDiSetDevicePropertyW
SetupDiSetDeviceRegistryPropertyA
SetupDiSetDeviceRegistryPropertyW
SetupDiSetDriverInstallParamsA
SetupDiSetDriverInstallParamsW
SetupDiSetSelectedDevice
SetupDiSetSelectedDriverA
SetupDiSetSelectedDriverW
SetupDiUnremoveDevice
SetupDuplicateDiskSpaceListA
SetupDuplicateDiskSpaceListW
SetupEnumInfSectionsA
SetupEnumInfSectionsW
SetupEnumPublishedInfA
SetupEnumPublishedInfW
SetupFindFirstLineA
SetupFindFirstLineW
SetupFindNextLine
SetupFindNextMatchLineA
SetupFindNextMatchLineW
SetupFreeSourceListA
SetupFreeSourceListW
SetupGetBackupInformationA
SetupGetBackupInformationW
SetupGetBinaryField
SetupGetFieldCount
SetupGetFileCompressionInfoA
SetupGetFileCompressionInfoExA
SetupGetFileCompressionInfoExW
SetupGetFileCompressionInfoW
SetupGetFileQueueCount
SetupGetFileQueueFlags
SetupGetInfDriverStoreLocationA
SetupGetInfDriverStoreLocationW
SetupGetInfFileListA
SetupGetInfFileListW
SetupGetInfInformationA
SetupGetInfInformationW
SetupGetInfPublishedNameA
SetupGetInfPublishedNameW
SetupGetInfSections
SetupGetIntField
SetupGetLineByIndexA
SetupGetLineByIndexW
SetupGetLineCountA
SetupGetLineCountW
SetupGetLineTextA
SetupGetLineTextW
SetupGetMultiSzFieldA
SetupGetMultiSzFieldW
SetupGetNonInteractiveMode
SetupGetSourceFileLocationA
SetupGetSourceFileLocationW
SetupGetSourceFileSizeA
SetupGetSourceFileSizeW
SetupGetSourceInfoA
SetupGetSourceInfoW
SetupGetStringFieldA
SetupGetStringFieldW
SetupGetTargetPathA
SetupGetTargetPathW
SetupGetThreadLogToken
SetupInitDefaultQueueCallback
SetupInitDefaultQueueCallbackEx
SetupInitializeFileLogA
SetupInitializeFileLogW
SetupInstallFileA
SetupInstallFileExA
SetupInstallFileExW
SetupInstallFileW
SetupInstallFilesFromInfSectionA
SetupInstallFilesFromInfSectionW
SetupInstallFromInfSectionA
SetupInstallFromInfSectionW
SetupInstallLogCloseEventGroup
SetupInstallLogCreateEventGroup
SetupInstallServicesFromInfSectionA
SetupInstallServicesFromInfSectionExA
SetupInstallServicesFromInfSectionExW
SetupInstallServicesFromInfSectionW
SetupIterateCabinetA
SetupIterateCabinetW
SetupLogErrorA
SetupLogErrorW
SetupLogFileA
SetupLogFileW
SetupOpenAppendInfFileA
SetupOpenAppendInfFileW
SetupOpenFileQueue
SetupOpenInfFileA
SetupOpenInfFileW
SetupOpenLog
SetupOpenMasterInf
SetupPrepareQueueForRestoreA
SetupPrepareQueueForRestoreW
SetupPromptForDiskA
SetupPromptForDiskW
SetupPromptReboot
SetupQueryDrivesInDiskSpaceListA
SetupQueryDrivesInDiskSpaceListW
SetupQueryFileLogA
SetupQueryFileLogW
SetupQueryInfFileInformationA
SetupQueryInfFileInformationW
SetupQueryInfOriginalFileInformationA
SetupQueryInfOriginalFileInformationW
SetupQueryInfVersionInformationA
SetupQueryInfVersionInformationW
SetupQuerySourceListA
SetupQuerySourceListW
SetupQuerySpaceRequiredOnDriveA
SetupQuerySpaceRequiredOnDriveW
SetupQueueCopyA
SetupQueueCopyIndirectA
SetupQueueCopyIndirectW
SetupQueueCopySectionA
SetupQueueCopySectionW
SetupQueueCopyW
SetupQueueDefaultCopyA
SetupQueueDefaultCopyW
SetupQueueDeleteA
SetupQueueDeleteSectionA
SetupQueueDeleteSectionW
SetupQueueDeleteW
SetupQueueRenameA
SetupQueueRenameSectionA
SetupQueueRenameSectionW
SetupQueueRenameW
SetupRemoveFileLogEntryA
SetupRemoveFileLogEntryW
SetupRemoveFromDiskSpaceListA
SetupRemoveFromDiskSpaceListW
SetupRemoveFromSourceListA
SetupRemoveFromSourceListW
SetupRemoveInstallSectionFromDiskSpaceListA
SetupRemoveInstallSectionFromDiskSpaceListW
SetupRemoveSectionFromDiskSpaceListA
SetupRemoveSectionFromDiskSpaceListW
SetupRenameErrorA
SetupRenameErrorW
SetupScanFileQueue
SetupScanFileQueueA
SetupScanFileQueueW
SetupSetDirectoryIdA
SetupSetDirectoryIdExA
SetupSetDirectoryIdExW
SetupSetDirectoryIdW
SetupSetFileQueueAlternatePlatformA
SetupSetFileQueueAlternatePlatformW
SetupSetFileQueueFlags
SetupSetNonInteractiveMode
SetupSetPlatformPathOverrideA
SetupSetPlatformPathOverrideW
SetupSetSourceListA
SetupSetSourceListW
SetupSetThreadLogToken
SetupTermDefaultQueueCallback
SetupTerminateFileLog
SetupUninstallNewlyCopiedInfs
SetupUninstallOEMInfA
SetupUninstallOEMInfW
SetupVerifyInfFileA
SetupVerifyInfFileW
SetupWriteTextLog
SetupWriteTextLogError
SetupWriteTextLogInfLine
UnicodeToMultiByte
VerifyCatalogFile
pGetDriverPackageHash
pSetupAccessRunOnceNodeList
pSetupAddMiniIconToList
pSetupAddTagToGroupOrderListEntry
pSetupAppendPath
pSetupCaptureAndConvertAnsiArg
pSetupCenterWindowRelativeToParent
pSetupCloseTextLogSection
pSetupConcatenatePaths
pSetupCreateTextLogSectionA
pSetupCreateTextLogSectionW
pSetupDestroyRunOnceNodeList
pSetupDiBuildInfoDataFromStrongName
pSetupDiCrimsonLogDeviceInstall
pSetupDiEnumSelectedDrivers
pSetupDiGetDriverInfoExtensionId
pSetupDiGetStrongNameForDriverNode
pSetupDiInvalidateHelperModules
pSetupDoLastKnownGoodBackup
pSetupDoesUserHavePrivilege
pSetupDuplicateString
pSetupEnablePrivilege
pSetupFree
pSetupGetCurrentDriverSigningPolicy
pSetupGetDriverDate
pSetupGetDriverVersion
pSetupGetField
pSetupGetFileTitle
pSetupGetGlobalFlags
pSetupGetIndirectStringsFromDriverInfo
pSetupGetInfSections
pSetupGetQueueFlags
pSetupGetRealSystemTime
pSetupGuidFromString
pSetupHandleFailedVerification
pSetupInfGetDigitalSignatureInfo
pSetupInfIsInbox
pSetupInfSetDigitalSignatureInfo
pSetupInstallCatalog
pSetupIsBiDiLocalizedSystemEx
pSetupIsGuidNull
pSetupIsLocalSystem
pSetupIsUserAdmin
pSetupIsUserTrustedInstaller
pSetupLoadIndirectString
pSetupMakeSurePathExists
pSetupMalloc
pSetupModifyGlobalFlags
pSetupMultiByteToUnicode
pSetupOpenAndMapFileForRead
pSetupOutOfMemory
pSetupQueryMultiSzValueToArray
pSetupRealloc
pSetupRegistryDelnode
pSetupRetrieveServiceConfig
pSetupSetArrayToMultiSzValue
pSetupSetDriverPackageRestorePoint
pSetupSetGlobalFlags
pSetupSetQueueFlags
pSetupShouldDeviceBeExcluded
pSetupStringFromGuid
pSetupStringTableAddString
pSetupStringTableAddStringEx
pSetupStringTableDestroy
pSetupStringTableDuplicate
pSetupStringTableEnum
pSetupStringTableGetExtraData
pSetupStringTableInitialize
pSetupStringTableInitializeEx
pSetupStringTableLookUpString
pSetupStringTableLookUpStringEx
pSetupStringTableSetExtraData
pSetupStringTableStringFromId
pSetupStringTableStringFromIdEx
pSetupUnicodeToMultiByte
pSetupUninstallCatalog
pSetupUnmapAndCloseFile
pSetupValidateDriverPackage
pSetupVerifyCatalogFile
pSetupVerifyQueuedCatalogs
pSetupWriteLogEntry
pSetupWriteLogError
