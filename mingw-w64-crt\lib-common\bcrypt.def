LIBRARY "bcrypt.dll"
EXPORTS
BCryptAddContextFunction
BCryptAddContextFunctionProvider
BCryptCloseAlgorithmProvider
BCryptConfigureContext
BCryptConfigureContextFunction
BCryptCreateContext
BCryptCreateHash
BCryptCreateMultiHash
BCryptDecrypt
BCryptDeleteContext
BCryptDeriveKey
BCryptDeriveKeyCapi
BCryptDeriveKeyPBKDF2
BCryptDestroyHash
BCryptDestroyKey
BCryptDestroySecret
BCryptDuplicateHash
BCryptDuplicateKey
BCryptEncrypt
BCryptEnumAlgorithms
BCryptEnumContextFunctionProviders
BCryptEnumContextFunctions
BCryptEnumContexts
BCryptEnumProviders
BCryptEnumRegisteredProviders
BCryptExportKey
BCryptFinalizeKeyPair
BCryptFinishHash
BCryptFreeBuffer
BCryptGenRandom
BCryptGenerateKeyPair
BCryptGenerateSymmetricKey
BCryptGetFipsAlgorithmMode
BCryptGetProperty
BCryptHash
BCryptHashData
BCryptImportKey
BCryptImportKeyPair
BCryptKeyDerivation
BCryptOpenAlgorithmProvider
BCryptProcessMultiOperations
BCryptQueryContextConfiguration
BCryptQueryContextFunctionConfiguration
BCryptQueryContextFunctionProperty
BCryptQueryProviderRegistration
BCryptRegisterConfigChangeNotify
BCryptRegisterProvider
BCryptRemoveContextFunction
BCryptRemoveContextFunctionProvider
BCryptResolveProviders
BCryptSecretAgreement
BCryptSetAuditingInterface
BCryptSetContextFunctionProperty
BCryptSetProperty
BCryptSignHash
BCryptUnregisterConfigChangeNotify
BCryptUnregisterProvider
BCryptVerifySignature
GetAsymmetricEncryptionInterface
GetCipherInterface
GetHashInterface
GetRngInterface
GetSecretAgreementInterface
GetSignatureInterface
