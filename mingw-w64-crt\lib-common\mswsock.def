;
; Definition file of MSWSOCK.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "MSWSOCK.dll"
EXPORTS
AcceptEx
EnumProtocolsA
EnumProtocolsW
GetAcceptExSockaddrs
GetAddressByNameA
GetAddressByNameW
GetNameByTypeA
GetNameByTypeW
GetServiceA
GetServiceW
GetSocketErrorMessageW
GetTypeByNameA
GetTypeByNameW
MigrateWinsockConfiguration
MigrateWinsockConfigurationEx
NPLoadNameSpaces
NSPStartup
; MSDN says ProcessSocketNotifications is from ws2_32.dll, not mswsock.dll
; ProcessSocketNotifications
SetServiceA
SetServiceW
StartWsdpService
StopWsdpService
Tcpip4_WSHAddressToString
Tcpip4_WSHEnumProtocols
Tcpip4_WSHGetBroadcastSockaddr
Tcpip4_WSHGetProviderGuid
Tc<PERSON><PERSON><PERSON>_WSHGetSockaddrType
Tcpip4_WSHGetSocketInformation
Tcpip4_WSHGetWSAProtocolInfo
Tcpip4_WSHGetWildcardSockaddr
Tcpip4_WSHGetWinsockMapping
Tcpip4_WSHIoctl
Tcpip4_WSHJoinLeaf
Tcpip4_WSHNotify
Tcpip4_WSHOpenSocket
Tcpip4_WSHOpenSocket2
Tcpip4_WSHSetSocketInformation
Tcpip4_WSHStringToAddress
Tcpip6_WSHAddressToString
Tcpip6_WSHEnumProtocols
Tcpip6_WSHGetProviderGuid
Tcpip6_WSHGetSockaddrType
Tcpip6_WSHGetSocketInformation
Tcpip6_WSHGetWSAProtocolInfo
Tcpip6_WSHGetWildcardSockaddr
Tcpip6_WSHGetWinsockMapping
Tcpip6_WSHIoctl
Tcpip6_WSHJoinLeaf
Tcpip6_WSHNotify
Tcpip6_WSHOpenSocket
Tcpip6_WSHOpenSocket2
Tcpip6_WSHSetSocketInformation
Tcpip6_WSHStringToAddress
TransmitFile
WSARecvEx
WSPStartup
dn_expand
getnetbyname
inet_network
rcmd
rexec
rresvport
s_perror
sethostname
