;
; Definition file of MFCORE.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "MFCORE.dll"
EXPORTS
AppendPropVariant
ConvertPropVariant
CopyPropertyStore
CreateNamedPropertyStore
; DllCanUnloadNow
; DllGetActivationFactory
; DllGetClassObject
; DllRegisterServer
; DllUnregisterServer
ExtractPropVariant
MFCopyMFMetadata
MFCopyPropertyStore
MFCopyStreamMetadata
MFCreateAggregateSource
MFCreateAppSourceProxy
MFCreateAudioRenderer
MFCreateAudioRendererActivate
MFCreateDeviceSource
MFCreateDeviceSourceActivate
MFCreateEncryptedMediaExtensionsStoreActivate
MFCreateExtendedCameraIntrinsicModel
MFCreateExtendedCameraIntrinsics
MFCreateFileSchemePlugin
MFCreateMFMetadataOnPropertyStore
MFCreateMediaProcessor
MFCreateMediaSession
MFCreatePMPHost
<PERSON>PMPMediaSession
MFCreatePMPServer
MFCreatePresentationClock
MFCreatePresentationClockAsyncTimeSource
MFCreateSampleCopierMFT
MFCreateSampleGrabberSinkActivate
MFCreateSequencerSegmentOffset
MFCreateSequencerSource
MFCreateSequencerSourceRemoteStream
MFCreateSimpleTypeHandler
MFCreateSoundEventSchemePlugin
MFCreateStandardQualityManager
MFCreateTopoLoader
MFCreateTopology
MFCreateTopologyNode
MFCreateTransformWrapper
MFCreateWMAEncoderActivate
MFCreateWMVEncoderActivate
MFEnumDeviceSources
MFGetMultipleServiceProviders
MFGetService
MFGetTopoNodeCurrentType
MFReadSequencerSegmentOffset
MFRequireProtectedEnvironment
MFShutdownObject
MergePropertyStore
