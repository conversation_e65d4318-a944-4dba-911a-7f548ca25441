; 
; Exports of file UTILDLL.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY UTILDLL.dll
EXPORTS
AsyncDeviceEnumerate
CachedGetUserFromSid
CalculateDiffTime
CalculateElapsedTime
CompareElapsedTime
ConfigureModem
CtxGetAnyDCName
CurrentDateTimeString
DateTimeString
ElapsedTimeString
EnumerateMultiUserServers
FormDecoratedAsyncDeviceName
GetAssociatedPortName
GetSystemMessageA
GetSystemMessageW
GetUnknownString
GetUserFromSid
HaveAnonymousUsersChanged
InitializeAnonymousUserCompareList
InstallModem
IsPartOfDomain
NetBIOSDeviceEnumerate
NetworkDeviceEnumerate
ParseDecoratedAsyncDeviceName
QueryCurrentWinStation
RegGetNetworkDeviceName
RegGetNetworkServiceName
SetupAsyncCdConfig
StandardErrorMessage
StrAsyncConnectState
StrConnectState
StrProcessState
StrSdClass
StrSystemWaitReason
TestUserForAdmin
WinEnumerateDevices
