;
; Definition file of DNSAPI.dll
; Automatic generated by g<PERSON><PERSON>
; written by <PERSON> 2008
;
LIBRARY "DNSAPI.dll"
EXPORTS
AdaptiveTimeout_ClearInterfaceSpecificConfiguration
AdaptiveTimeout_ResetAdaptiveTimeout
AddRefQueryBlobEx
BreakRecordsIntoBlob
Coalesce_UpdateNetVersion
CombineRecordsInBlob
DeRefQueryBlobEx
DelaySortDAServerlist
DnsAcquireContextHandle_A
DnsAcquireContextHandle_W
DnsAllocateRecord
DnsApiAlloc
DnsApiAllocZero
DnsApiFree
DnsApiHeapReset
DnsApiRealloc
DnsApiSetDebugGlobals
DnsAsyncRegisterHostAddrs
DnsAsyncRegisterInit
DnsAsyncRegisterTerm
DnsCancelQuery
DnsCheckNrptRuleIntegrity
DnsCheckNrptRules
DnsConnectionDeletePolicyEntries
DnsConnectionDeletePolic<PERSON>rivate
DnsConnectionDeleteProxyInfo
DnsConnectionFreeNameList
DnsConnectionFreeProxyInfo
DnsConnectionFreeProxyInfoEx
DnsConnectionFreeProxyList
DnsConnectionGetHandleForHostUrlPrivate
DnsConnectionGetNameList
DnsConnectionGetProxyInfo
DnsConnectionGetProxyInfoForHostUrl
DnsConnectionGetProxyList
DnsConnectionSetPolicyEntries
DnsConnectionSetPolicyEntriesPrivate
DnsConnectionSetProxyInfo
DnsConnectionUpdateIfIndexTable
DnsCopyStringEx
DnsCreateReverseNameStringForIpAddress
DnsCreateStandardDnsNameCopy
DnsCreateStringCopy
DnsDeRegisterLocal
DnsDhcpRegisterAddrs
DnsDhcpRegisterHostAddrs
DnsDhcpRegisterInit
DnsDhcpRegisterTerm
DnsDhcpRemoveRegistrations
DnsDhcpSrvRegisterHostAddr
DnsDhcpSrvRegisterHostAddrEx
DnsDhcpSrvRegisterHostName
DnsDhcpSrvRegisterHostNameEx
DnsDhcpSrvRegisterInit
DnsDhcpSrvRegisterInitEx
DnsDhcpSrvRegisterInitialize
DnsDhcpSrvRegisterTerm
DnsDisableIdnEncoding
DnsDowncaseDnsNameLabel
DnsExtractRecordsFromMessage_UTF8
DnsExtractRecordsFromMessage_W
DnsFindAuthoritativeZone
DnsFlushResolverCache
DnsFlushResolverCacheEntry_A
DnsFlushResolverCacheEntry_UTF8
DnsFlushResolverCacheEntry_W
DnsFree
DnsFreeAdaptersInfo
DnsFreeConfigStructure
DnsFreeNrptRule
DnsFreeNrptRuleNamesList
DnsFreePolicyConfig
DnsFreeProxyName
DnsGetAdaptersInfo
DnsGetApplicationIdentifier
DnsGetBufferLengthForStringCopy
DnsGetCacheDataTable
DnsGetCacheDataTableEx
DnsGetDnsServerList
DnsGetDomainName
DnsGetInterfaceSettings
DnsGetLastFailedUpdateInfo
DnsGetNrptRuleNamesList
DnsGetPolicyTableInfo
DnsGetPolicyTableInfoPrivate
DnsGetPrimaryDomainName_A
DnsGetProxyInfoPrivate
DnsGetProxyInformation
DnsGetQueryRetryTimeouts
DnsGetSettings
DnsGlobals DATA
DnsIpv6AddressToString
DnsIpv6StringToAddress
DnsIsAMailboxType
DnsIsNSECType
DnsIsStatusRcode
DnsIsStringCountValidForTextType
DnsLogEvent
DnsMapRcodeToStatus
DnsModifyRecordsInSet_A
DnsModifyRecordsInSet_UTF8
DnsModifyRecordsInSet_W
DnsNameCompareEx_A
DnsNameCompareEx_UTF8
DnsNameCompareEx_W
DnsNameCompare_A
DnsNameCompare_UTF8
DnsNameCompare_W
DnsNameCopy
DnsNameCopyAllocate
DnsNetworkInfo_CreateFromFAZ
DnsNetworkInformation_CreateFromFAZ
DnsNotifyResolver
DnsNotifyResolverClusterIp
DnsNotifyResolverEx
DnsQueryConfig
DnsQueryConfigAllocEx
DnsQueryConfigDword
DnsQueryEx
DnsQueryExA
DnsQueryExUTF8
DnsQueryExW
DnsQuery_A
DnsQuery_UTF8
DnsQuery_W
DnsRecordBuild_UTF8
DnsRecordBuild_W
DnsRecordCompare
DnsRecordCopyEx
DnsRecordListFree
DnsRecordListUnmapV4MappedAAAAInPlace
DnsRecordSetCompare
DnsRecordSetCopyEx
DnsRecordSetDetach
DnsRecordStringForType
DnsRecordStringForWritableType
DnsRecordTypeForName
DnsRegisterLocal
DnsReleaseContextHandle
DnsRemoveNrptRule
DnsRemoveRegistrations
DnsReplaceRecordSetA
DnsReplaceRecordSetUTF8
DnsReplaceRecordSetW
DnsResetQueryRetryTimeouts
DnsResolverOp
DnsResolverQueryHvsi
DnsScreenLocalAddrsForRegistration
DnsServiceBrowse
DnsServiceBrowseCancel
DnsServiceConstructInstance
DnsServiceCopyInstance
DnsServiceDeRegister
DnsServiceFreeInstance
DnsServiceRegister
DnsServiceRegisterCancel
DnsServiceResolve
DnsServiceResolveCancel
DnsSetConfigDword
DnsSetConfigValue
DnsSetInterfaceSettings
DnsSetNrptRule
DnsSetNrptRules
DnsSetQueryRetryTimeouts
DnsSetSettings
DnsStartMulticastQuery
DnsStatusString
DnsStopMulticastQuery
DnsStringCopyAllocateEx
DnsTraceServerConfig
DnsUnicodeToUtf8
DnsUpdate
DnsUpdateMachinePresence
DnsUpdateTest_A
DnsUpdateTest_UTF8
DnsUpdateTest_W
DnsUtf8ToUnicode
DnsValidateNameOrIp_TempW
DnsValidateName_A
DnsValidateName_UTF8
DnsValidateName_W
DnsValidateServerArray_A
DnsValidateServerArray_W
DnsValidateServerStatus
DnsValidateServer_A
DnsValidateServer_W
DnsValidateUtf8Byte
DnsWriteQuestionToBuffer_UTF8
DnsWriteQuestionToBuffer_W
DnsWriteReverseNameStringForIpAddress
Dns_AddRecordsToMessage
Dns_AllocateMsgBuf
Dns_BuildPacket
Dns_CacheServiceCleanup
Dns_CacheServiceInit
Dns_CacheServiceStopIssued
Dns_CleanupWinsock
Dns_CloseConnection
Dns_CloseSocket
Dns_CreateMulticastSocket
Dns_CreateSocket
Dns_CreateSocketEx
Dns_ExtractRecordsFromMessage
Dns_FindAuthoritativeZoneLib
Dns_FreeMsgBuf
Dns_GetRandomXid
Dns_InitializeMsgBuf
Dns_InitializeMsgRemoteSockaddr
Dns_InitializeWinsock
Dns_OpenTcpConnectionAndSend
Dns_ParseMessage
Dns_ParsePacketRecord
Dns_PingAdapterServers
Dns_ReadPacketName
Dns_ReadPacketNameAllocate
Dns_ReadRecordStructureFromPacket
Dns_RecvTcp
Dns_ResetNetworkInfo
Dns_SendAndRecvUdp
Dns_SendEx
Dns_SetRecordDatalength
Dns_SetRecordsSection
Dns_SetRecordsTtl
Dns_SkipPacketName
Dns_SkipToRecord
Dns_UpdateLib
Dns_UpdateLibEx
Dns_WriteDottedNameToPacket
Dns_WriteQuestionToMessage
Dns_WriteRecordStructureToPacketEx
ExtraInfo_Init
Faz_AreServerListsInSameNameSpace
FlushDnsPolicyUnreachableStatus
GetCurrentTimeInSeconds
HostsFile_Close
HostsFile_Open
HostsFile_ReadLine
IpHelp_IsAddrOnLink
Local_GetRecordsForLocalName
Local_GetRecordsForLocalNameEx
NetInfo_Build
NetInfo_Clean
NetInfo_Copy
NetInfo_CopyNetworkIndex
NetInfo_CreatePerNetworkNetinfo
NetInfo_Free
NetInfo_GetAdapterByAddress
NetInfo_GetAdapterByInterfaceIndex
NetInfo_GetAdapterByName
NetInfo_IsAddrConfig
NetInfo_IsForUpdate
NetInfo_IsTcpipConfigChange
NetInfo_ResetServerPriorities
NetInfo_UpdateDnsInterfaceConfigChange
NetInfo_UpdateNetworkProperties
NetInfo_UpdateServerReachability
QueryDirectEx
Query_Cancel
Query_Main
Reg_FreeUpdateInfo
Reg_GetValueEx
Reg_ReadGlobalsEx
Reg_ReadUpdateInfo
Security_ContextListTimeout
Send_AndRecvUdpWithParam
Send_MessagePrivate
Send_MessagePrivateEx
Send_OpenTcpConnectionAndSend
Socket_CacheCleanup
Socket_CacheInit
Socket_CleanupWinsock
Socket_ClearMessageSockets
Socket_CloseEx
Socket_CloseMessageSockets
Socket_Create
Socket_CreateMulticast
Socket_InitWinsock
Socket_JoinMulticast
Socket_RecvFrom
Socket_SetMulticastInterface
Socket_SetMulticastLoopBack
Socket_SetTtl
Socket_TcpListen
Trace_Reset
Update_ReplaceAddressRecordsW
Util_IsIp6Running
Util_IsRunningOnXboxOne
WriteDnsNrptRulesToRegistry
