# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)"
LIBRARIES = $(lib_LIBRARIES)
ARFLAGS = cru
AM_V_AR = $(am__v_AR_@AM_V@)
am__v_AR_ = $(am__v_AR_@AM_DEFAULT_V@)
am__v_AR_0 = @echo "  AR      " $@;
am__v_AR_1 = 
libwindowsappcompat_a_AR = $(AR) $(ARFLAGS)
libwindowsappcompat_a_LIBADD =
am__dirstamp = $(am__leading_dot)dirstamp
am_libwindowsappcompat_a_OBJECTS =  \
	src/libwindowsappcompat_a-beginthread.$(OBJEXT) \
	src/libwindowsappcompat_a-GetModuleHandle.$(OBJEXT) \
	src/libwindowsappcompat_a-LoadLibraryW.$(OBJEXT) \
	src/libwindowsappcompat_a-CreateFileW.$(OBJEXT) \
	src/libwindowsappcompat_a-UnhandledExceptionFilter.$(OBJEXT) \
	src/libwindowsappcompat_a-VirtualProtect.$(OBJEXT) \
	src/libwindowsappcompat_a-getenv.$(OBJEXT) \
	src/libwindowsappcompat_a-GetFileSize.$(OBJEXT) \
	src/libwindowsappcompat_a-SHGetFolderPathW.$(OBJEXT) \
	src/libwindowsappcompat_a-QueueTimer.$(OBJEXT) \
	src/libwindowsappcompat_a-Crypto.$(OBJEXT) \
	src/libwindowsappcompat_a-GetStartupInfo.$(OBJEXT) \
	src/libwindowsappcompat_a-EnumProcessModules.$(OBJEXT) \
	src/libwindowsappcompat_a-RtlAddFunctionTable.$(OBJEXT) \
	src/libwindowsappcompat_a-RtlCaptureContext.$(OBJEXT) \
	src/libwindowsappcompat_a-RtlVirtualUnwind.$(OBJEXT) \
	src/libwindowsappcompat_a-RtlRestoreContext.$(OBJEXT) \
	src/libwindowsappcompat_a-GetUserName.$(OBJEXT) \
	src/libwindowsappcompat_a-getpid.$(OBJEXT) \
	src/libwindowsappcompat_a-GetFileInformationByHandle.$(OBJEXT) \
	src/libwindowsappcompat_a-SystemFunction036.$(OBJEXT)
libwindowsappcompat_a_OBJECTS = $(am_libwindowsappcompat_a_OBJECTS)
libwinstorecompat_a_AR = $(AR) $(ARFLAGS)
libwinstorecompat_a_LIBADD =
am_libwinstorecompat_a_OBJECTS = src/beginthread.$(OBJEXT) \
	src/GetModuleHandle.$(OBJEXT) src/CreateEventW.$(OBJEXT) \
	src/CreateMutexW.$(OBJEXT) src/CreateSemaphoreW.$(OBJEXT) \
	src/InitializeCriticalSection.$(OBJEXT) \
	src/GetFileAttributes.$(OBJEXT) \
	src/WaitForSingleObject.$(OBJEXT) src/LoadLibraryW.$(OBJEXT) \
	src/CreateFileW.$(OBJEXT) src/GetTickCount.$(OBJEXT) \
	src/SetUnhandledExceptionFilter.$(OBJEXT) \
	src/UnhandledExceptionFilter.$(OBJEXT) \
	src/TerminateProcess.$(OBJEXT) src/IsDBCSLeadByteEx.$(OBJEXT) \
	src/SetErrorMode.$(OBJEXT) src/GetACP.$(OBJEXT) \
	src/VirtualProtect.$(OBJEXT) src/getenv.$(OBJEXT) \
	src/LocalAlloc.$(OBJEXT) src/LocalFree.$(OBJEXT) \
	src/Sleep.$(OBJEXT) src/SleepEx.$(OBJEXT) \
	src/SetFilePointer.$(OBJEXT) src/GetFileSize.$(OBJEXT) \
	src/Tls.$(OBJEXT) src/SHGetFolderPathW.$(OBJEXT) \
	src/QueueTimer.$(OBJEXT) src/Crypto.$(OBJEXT) \
	src/GetStartupInfo.$(OBJEXT) src/GetConsoleOutputCP.$(OBJEXT) \
	src/EnumProcessModules.$(OBJEXT) \
	src/RtlAddFunctionTable.$(OBJEXT) \
	src/RtlCaptureContext.$(OBJEXT) src/RtlVirtualUnwind.$(OBJEXT) \
	src/RtlRestoreContext.$(OBJEXT) src/GetUserName.$(OBJEXT) \
	src/getpid.$(OBJEXT) src/GetFileInformationByHandle.$(OBJEXT) \
	src/SystemFunction036.$(OBJEXT)
libwinstorecompat_a_OBJECTS = $(am_libwinstorecompat_a_OBJECTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/build-aux/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = src/$(DEPDIR)/CreateEventW.Po \
	src/$(DEPDIR)/CreateFileW.Po src/$(DEPDIR)/CreateMutexW.Po \
	src/$(DEPDIR)/CreateSemaphoreW.Po src/$(DEPDIR)/Crypto.Po \
	src/$(DEPDIR)/EnumProcessModules.Po src/$(DEPDIR)/GetACP.Po \
	src/$(DEPDIR)/GetConsoleOutputCP.Po \
	src/$(DEPDIR)/GetFileAttributes.Po \
	src/$(DEPDIR)/GetFileInformationByHandle.Po \
	src/$(DEPDIR)/GetFileSize.Po src/$(DEPDIR)/GetModuleHandle.Po \
	src/$(DEPDIR)/GetStartupInfo.Po src/$(DEPDIR)/GetTickCount.Po \
	src/$(DEPDIR)/GetUserName.Po \
	src/$(DEPDIR)/InitializeCriticalSection.Po \
	src/$(DEPDIR)/IsDBCSLeadByteEx.Po \
	src/$(DEPDIR)/LoadLibraryW.Po src/$(DEPDIR)/LocalAlloc.Po \
	src/$(DEPDIR)/LocalFree.Po src/$(DEPDIR)/QueueTimer.Po \
	src/$(DEPDIR)/RtlAddFunctionTable.Po \
	src/$(DEPDIR)/RtlCaptureContext.Po \
	src/$(DEPDIR)/RtlRestoreContext.Po \
	src/$(DEPDIR)/RtlVirtualUnwind.Po \
	src/$(DEPDIR)/SHGetFolderPathW.Po \
	src/$(DEPDIR)/SetErrorMode.Po src/$(DEPDIR)/SetFilePointer.Po \
	src/$(DEPDIR)/SetUnhandledExceptionFilter.Po \
	src/$(DEPDIR)/Sleep.Po src/$(DEPDIR)/SleepEx.Po \
	src/$(DEPDIR)/SystemFunction036.Po \
	src/$(DEPDIR)/TerminateProcess.Po src/$(DEPDIR)/Tls.Po \
	src/$(DEPDIR)/UnhandledExceptionFilter.Po \
	src/$(DEPDIR)/VirtualProtect.Po \
	src/$(DEPDIR)/WaitForSingleObject.Po \
	src/$(DEPDIR)/beginthread.Po src/$(DEPDIR)/getenv.Po \
	src/$(DEPDIR)/getpid.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-getenv.Po \
	src/$(DEPDIR)/libwindowsappcompat_a-getpid.Po
am__mv = mv -f
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libwindowsappcompat_a_SOURCES) \
	$(libwinstorecompat_a_SOURCES)
DIST_SOURCES = $(libwindowsappcompat_a_SOURCES) \
	$(libwinstorecompat_a_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
AM_RECURSIVE_TARGETS = cscope
am__DIST_COMMON = $(srcdir)/Makefile.in \
	$(top_srcdir)/build-aux/compile \
	$(top_srcdir)/build-aux/depcomp \
	$(top_srcdir)/build-aux/install-sh \
	$(top_srcdir)/build-aux/missing COPYING ChangeLog INSTALL \
	build-aux/compile build-aux/depcomp build-aux/install-sh \
	build-aux/missing
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
DIST_TARGETS = dist-gzip
# Exists only to be overridden by the user if desired.
AM_DISTCHECK_DVI_TARGET = dvi
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
RANLIB = @RANLIB@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build_alias = @build_alias@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host_alias = @host_alias@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AUTOMAKE_OPTIONS = foreign subdir-objects
AM_CFLAGS = -Wall -Wstrict-aliasing=2 -pedantic
lib_LIBRARIES = libwinstorecompat.a \
  libwindowsappcompat.a

libwinstorecompat_a_SOURCES = \
  src/beginthread.c \
  src/GetModuleHandle.c \
  src/CreateEventW.c \
  src/CreateMutexW.c \
  src/CreateSemaphoreW.c \
  src/InitializeCriticalSection.c \
  src/GetFileAttributes.c \
  src/WaitForSingleObject.c \
  src/LoadLibraryW.c \
  src/CreateFileW.c \
  src/GetTickCount.c \
  src/SetUnhandledExceptionFilter.c \
  src/UnhandledExceptionFilter.c \
  src/TerminateProcess.c \
  src/IsDBCSLeadByteEx.c \
  src/SetErrorMode.c \
  src/GetACP.c \
  src/VirtualProtect.c \
  src/getenv.c \
  src/LocalAlloc.c \
  src/LocalFree.c \
  src/Sleep.c \
  src/SleepEx.c \
  src/SetFilePointer.c \
  src/GetFileSize.c \
  src/Tls.c \
  src/SHGetFolderPathW.c \
  src/QueueTimer.c \
  src/Crypto.c \
  src/GetStartupInfo.c \
  src/GetConsoleOutputCP.c \
  src/EnumProcessModules.c \
  src/RtlAddFunctionTable.c \
  src/RtlCaptureContext.c \
  src/RtlVirtualUnwind.c \
  src/RtlRestoreContext.c \
  src/GetUserName.c \
  src/getpid.c \
  src/GetFileInformationByHandle.c \
  src/SystemFunction036.c \
  $(NULL)

libwindowsappcompat_a_SOURCES = \
  src/beginthread.c \
  src/GetModuleHandle.c \
  src/LoadLibraryW.c \
  src/CreateFileW.c \
  src/UnhandledExceptionFilter.c \
  src/VirtualProtect.c \
  src/getenv.c \
  src/GetFileSize.c \
  src/SHGetFolderPathW.c \
  src/QueueTimer.c \
  src/Crypto.c \
  src/GetStartupInfo.c \
  src/EnumProcessModules.c \
  src/RtlAddFunctionTable.c \
  src/RtlCaptureContext.c \
  src/RtlVirtualUnwind.c \
  src/RtlRestoreContext.c \
  src/GetUserName.c \
  src/getpid.c \
  src/GetFileInformationByHandle.c \
  src/SystemFunction036.c \
  $(NULL)

libwindowsappcompat_a_CFLAGS = $(AM_CFLAGS) -D_WIN32_WINNT=_WIN32_WINNT_WIN10
all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --foreign \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):
install-libLIBRARIES: $(lib_LIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(INSTALL_DATA) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(INSTALL_DATA) $$list2 "$(DESTDIR)$(libdir)" || exit $$?; }
	@$(POST_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  if test -f $$p; then \
	    $(am__strip_dir) \
	    echo " ( cd '$(DESTDIR)$(libdir)' && $(RANLIB) $$f )"; \
	    ( cd "$(DESTDIR)$(libdir)" && $(RANLIB) $$f ) || exit $$?; \
	  else :; fi; \
	done

uninstall-libLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(libdir)'; $(am__uninstall_files_from_dir)

clean-libLIBRARIES:
	-test -z "$(lib_LIBRARIES)" || rm -f $(lib_LIBRARIES)
src/$(am__dirstamp):
	@$(MKDIR_P) src
	@: > src/$(am__dirstamp)
src/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/$(DEPDIR)
	@: > src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-beginthread.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-GetModuleHandle.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-LoadLibraryW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-CreateFileW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-UnhandledExceptionFilter.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-VirtualProtect.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-getenv.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-GetFileSize.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-SHGetFolderPathW.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-QueueTimer.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-Crypto.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-GetStartupInfo.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-EnumProcessModules.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-RtlAddFunctionTable.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-RtlCaptureContext.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-RtlVirtualUnwind.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-RtlRestoreContext.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-GetUserName.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-getpid.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-GetFileInformationByHandle.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/libwindowsappcompat_a-SystemFunction036.$(OBJEXT):  \
	src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

libwindowsappcompat.a: $(libwindowsappcompat_a_OBJECTS) $(libwindowsappcompat_a_DEPENDENCIES) $(EXTRA_libwindowsappcompat_a_DEPENDENCIES) 
	$(AM_V_at)-rm -f libwindowsappcompat.a
	$(AM_V_AR)$(libwindowsappcompat_a_AR) libwindowsappcompat.a $(libwindowsappcompat_a_OBJECTS) $(libwindowsappcompat_a_LIBADD)
	$(AM_V_at)$(RANLIB) libwindowsappcompat.a
src/beginthread.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetModuleHandle.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/CreateEventW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/CreateMutexW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/CreateSemaphoreW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/InitializeCriticalSection.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetFileAttributes.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/WaitForSingleObject.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/LoadLibraryW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/CreateFileW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetTickCount.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/SetUnhandledExceptionFilter.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/UnhandledExceptionFilter.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/TerminateProcess.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/IsDBCSLeadByteEx.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/SetErrorMode.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetACP.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/VirtualProtect.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/getenv.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/LocalAlloc.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/LocalFree.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/Sleep.$(OBJEXT): src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/SleepEx.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/SetFilePointer.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetFileSize.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/Tls.$(OBJEXT): src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/SHGetFolderPathW.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/QueueTimer.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/Crypto.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetStartupInfo.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetConsoleOutputCP.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/EnumProcessModules.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/RtlAddFunctionTable.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/RtlCaptureContext.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/RtlVirtualUnwind.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/RtlRestoreContext.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetUserName.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/getpid.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/GetFileInformationByHandle.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/SystemFunction036.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

libwinstorecompat.a: $(libwinstorecompat_a_OBJECTS) $(libwinstorecompat_a_DEPENDENCIES) $(EXTRA_libwinstorecompat_a_DEPENDENCIES) 
	$(AM_V_at)-rm -f libwinstorecompat.a
	$(AM_V_AR)$(libwinstorecompat_a_AR) libwinstorecompat.a $(libwinstorecompat_a_OBJECTS) $(libwinstorecompat_a_LIBADD)
	$(AM_V_at)$(RANLIB) libwinstorecompat.a

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f src/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/CreateEventW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/CreateFileW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/CreateMutexW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/CreateSemaphoreW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/Crypto.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/EnumProcessModules.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetACP.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetConsoleOutputCP.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetFileAttributes.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetFileInformationByHandle.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetFileSize.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetModuleHandle.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetStartupInfo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetTickCount.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/GetUserName.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/InitializeCriticalSection.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/IsDBCSLeadByteEx.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/LoadLibraryW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/LocalAlloc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/LocalFree.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/QueueTimer.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/RtlAddFunctionTable.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/RtlCaptureContext.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/RtlRestoreContext.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/RtlVirtualUnwind.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/SHGetFolderPathW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/SetErrorMode.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/SetFilePointer.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/SetUnhandledExceptionFilter.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/Sleep.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/SleepEx.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/SystemFunction036.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/TerminateProcess.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/Tls.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/UnhandledExceptionFilter.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/VirtualProtect.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/WaitForSingleObject.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/beginthread.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/getenv.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/getpid.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-getenv.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/libwindowsappcompat_a-getpid.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

src/libwindowsappcompat_a-beginthread.o: src/beginthread.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-beginthread.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Tpo -c -o src/libwindowsappcompat_a-beginthread.o `test -f 'src/beginthread.c' || echo '$(srcdir)/'`src/beginthread.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Tpo src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/beginthread.c' object='src/libwindowsappcompat_a-beginthread.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-beginthread.o `test -f 'src/beginthread.c' || echo '$(srcdir)/'`src/beginthread.c

src/libwindowsappcompat_a-beginthread.obj: src/beginthread.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-beginthread.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Tpo -c -o src/libwindowsappcompat_a-beginthread.obj `if test -f 'src/beginthread.c'; then $(CYGPATH_W) 'src/beginthread.c'; else $(CYGPATH_W) '$(srcdir)/src/beginthread.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Tpo src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/beginthread.c' object='src/libwindowsappcompat_a-beginthread.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-beginthread.obj `if test -f 'src/beginthread.c'; then $(CYGPATH_W) 'src/beginthread.c'; else $(CYGPATH_W) '$(srcdir)/src/beginthread.c'; fi`

src/libwindowsappcompat_a-GetModuleHandle.o: src/GetModuleHandle.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetModuleHandle.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Tpo -c -o src/libwindowsappcompat_a-GetModuleHandle.o `test -f 'src/GetModuleHandle.c' || echo '$(srcdir)/'`src/GetModuleHandle.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetModuleHandle.c' object='src/libwindowsappcompat_a-GetModuleHandle.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetModuleHandle.o `test -f 'src/GetModuleHandle.c' || echo '$(srcdir)/'`src/GetModuleHandle.c

src/libwindowsappcompat_a-GetModuleHandle.obj: src/GetModuleHandle.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetModuleHandle.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Tpo -c -o src/libwindowsappcompat_a-GetModuleHandle.obj `if test -f 'src/GetModuleHandle.c'; then $(CYGPATH_W) 'src/GetModuleHandle.c'; else $(CYGPATH_W) '$(srcdir)/src/GetModuleHandle.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetModuleHandle.c' object='src/libwindowsappcompat_a-GetModuleHandle.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetModuleHandle.obj `if test -f 'src/GetModuleHandle.c'; then $(CYGPATH_W) 'src/GetModuleHandle.c'; else $(CYGPATH_W) '$(srcdir)/src/GetModuleHandle.c'; fi`

src/libwindowsappcompat_a-LoadLibraryW.o: src/LoadLibraryW.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-LoadLibraryW.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Tpo -c -o src/libwindowsappcompat_a-LoadLibraryW.o `test -f 'src/LoadLibraryW.c' || echo '$(srcdir)/'`src/LoadLibraryW.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Tpo src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/LoadLibraryW.c' object='src/libwindowsappcompat_a-LoadLibraryW.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-LoadLibraryW.o `test -f 'src/LoadLibraryW.c' || echo '$(srcdir)/'`src/LoadLibraryW.c

src/libwindowsappcompat_a-LoadLibraryW.obj: src/LoadLibraryW.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-LoadLibraryW.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Tpo -c -o src/libwindowsappcompat_a-LoadLibraryW.obj `if test -f 'src/LoadLibraryW.c'; then $(CYGPATH_W) 'src/LoadLibraryW.c'; else $(CYGPATH_W) '$(srcdir)/src/LoadLibraryW.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Tpo src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/LoadLibraryW.c' object='src/libwindowsappcompat_a-LoadLibraryW.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-LoadLibraryW.obj `if test -f 'src/LoadLibraryW.c'; then $(CYGPATH_W) 'src/LoadLibraryW.c'; else $(CYGPATH_W) '$(srcdir)/src/LoadLibraryW.c'; fi`

src/libwindowsappcompat_a-CreateFileW.o: src/CreateFileW.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-CreateFileW.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Tpo -c -o src/libwindowsappcompat_a-CreateFileW.o `test -f 'src/CreateFileW.c' || echo '$(srcdir)/'`src/CreateFileW.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Tpo src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/CreateFileW.c' object='src/libwindowsappcompat_a-CreateFileW.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-CreateFileW.o `test -f 'src/CreateFileW.c' || echo '$(srcdir)/'`src/CreateFileW.c

src/libwindowsappcompat_a-CreateFileW.obj: src/CreateFileW.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-CreateFileW.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Tpo -c -o src/libwindowsappcompat_a-CreateFileW.obj `if test -f 'src/CreateFileW.c'; then $(CYGPATH_W) 'src/CreateFileW.c'; else $(CYGPATH_W) '$(srcdir)/src/CreateFileW.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Tpo src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/CreateFileW.c' object='src/libwindowsappcompat_a-CreateFileW.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-CreateFileW.obj `if test -f 'src/CreateFileW.c'; then $(CYGPATH_W) 'src/CreateFileW.c'; else $(CYGPATH_W) '$(srcdir)/src/CreateFileW.c'; fi`

src/libwindowsappcompat_a-UnhandledExceptionFilter.o: src/UnhandledExceptionFilter.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-UnhandledExceptionFilter.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Tpo -c -o src/libwindowsappcompat_a-UnhandledExceptionFilter.o `test -f 'src/UnhandledExceptionFilter.c' || echo '$(srcdir)/'`src/UnhandledExceptionFilter.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Tpo src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/UnhandledExceptionFilter.c' object='src/libwindowsappcompat_a-UnhandledExceptionFilter.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-UnhandledExceptionFilter.o `test -f 'src/UnhandledExceptionFilter.c' || echo '$(srcdir)/'`src/UnhandledExceptionFilter.c

src/libwindowsappcompat_a-UnhandledExceptionFilter.obj: src/UnhandledExceptionFilter.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-UnhandledExceptionFilter.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Tpo -c -o src/libwindowsappcompat_a-UnhandledExceptionFilter.obj `if test -f 'src/UnhandledExceptionFilter.c'; then $(CYGPATH_W) 'src/UnhandledExceptionFilter.c'; else $(CYGPATH_W) '$(srcdir)/src/UnhandledExceptionFilter.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Tpo src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/UnhandledExceptionFilter.c' object='src/libwindowsappcompat_a-UnhandledExceptionFilter.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-UnhandledExceptionFilter.obj `if test -f 'src/UnhandledExceptionFilter.c'; then $(CYGPATH_W) 'src/UnhandledExceptionFilter.c'; else $(CYGPATH_W) '$(srcdir)/src/UnhandledExceptionFilter.c'; fi`

src/libwindowsappcompat_a-VirtualProtect.o: src/VirtualProtect.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-VirtualProtect.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Tpo -c -o src/libwindowsappcompat_a-VirtualProtect.o `test -f 'src/VirtualProtect.c' || echo '$(srcdir)/'`src/VirtualProtect.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Tpo src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/VirtualProtect.c' object='src/libwindowsappcompat_a-VirtualProtect.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-VirtualProtect.o `test -f 'src/VirtualProtect.c' || echo '$(srcdir)/'`src/VirtualProtect.c

src/libwindowsappcompat_a-VirtualProtect.obj: src/VirtualProtect.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-VirtualProtect.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Tpo -c -o src/libwindowsappcompat_a-VirtualProtect.obj `if test -f 'src/VirtualProtect.c'; then $(CYGPATH_W) 'src/VirtualProtect.c'; else $(CYGPATH_W) '$(srcdir)/src/VirtualProtect.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Tpo src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/VirtualProtect.c' object='src/libwindowsappcompat_a-VirtualProtect.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-VirtualProtect.obj `if test -f 'src/VirtualProtect.c'; then $(CYGPATH_W) 'src/VirtualProtect.c'; else $(CYGPATH_W) '$(srcdir)/src/VirtualProtect.c'; fi`

src/libwindowsappcompat_a-getenv.o: src/getenv.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-getenv.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-getenv.Tpo -c -o src/libwindowsappcompat_a-getenv.o `test -f 'src/getenv.c' || echo '$(srcdir)/'`src/getenv.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-getenv.Tpo src/$(DEPDIR)/libwindowsappcompat_a-getenv.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/getenv.c' object='src/libwindowsappcompat_a-getenv.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-getenv.o `test -f 'src/getenv.c' || echo '$(srcdir)/'`src/getenv.c

src/libwindowsappcompat_a-getenv.obj: src/getenv.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-getenv.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-getenv.Tpo -c -o src/libwindowsappcompat_a-getenv.obj `if test -f 'src/getenv.c'; then $(CYGPATH_W) 'src/getenv.c'; else $(CYGPATH_W) '$(srcdir)/src/getenv.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-getenv.Tpo src/$(DEPDIR)/libwindowsappcompat_a-getenv.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/getenv.c' object='src/libwindowsappcompat_a-getenv.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-getenv.obj `if test -f 'src/getenv.c'; then $(CYGPATH_W) 'src/getenv.c'; else $(CYGPATH_W) '$(srcdir)/src/getenv.c'; fi`

src/libwindowsappcompat_a-GetFileSize.o: src/GetFileSize.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetFileSize.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Tpo -c -o src/libwindowsappcompat_a-GetFileSize.o `test -f 'src/GetFileSize.c' || echo '$(srcdir)/'`src/GetFileSize.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetFileSize.c' object='src/libwindowsappcompat_a-GetFileSize.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetFileSize.o `test -f 'src/GetFileSize.c' || echo '$(srcdir)/'`src/GetFileSize.c

src/libwindowsappcompat_a-GetFileSize.obj: src/GetFileSize.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetFileSize.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Tpo -c -o src/libwindowsappcompat_a-GetFileSize.obj `if test -f 'src/GetFileSize.c'; then $(CYGPATH_W) 'src/GetFileSize.c'; else $(CYGPATH_W) '$(srcdir)/src/GetFileSize.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetFileSize.c' object='src/libwindowsappcompat_a-GetFileSize.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetFileSize.obj `if test -f 'src/GetFileSize.c'; then $(CYGPATH_W) 'src/GetFileSize.c'; else $(CYGPATH_W) '$(srcdir)/src/GetFileSize.c'; fi`

src/libwindowsappcompat_a-SHGetFolderPathW.o: src/SHGetFolderPathW.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-SHGetFolderPathW.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Tpo -c -o src/libwindowsappcompat_a-SHGetFolderPathW.o `test -f 'src/SHGetFolderPathW.c' || echo '$(srcdir)/'`src/SHGetFolderPathW.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Tpo src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/SHGetFolderPathW.c' object='src/libwindowsappcompat_a-SHGetFolderPathW.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-SHGetFolderPathW.o `test -f 'src/SHGetFolderPathW.c' || echo '$(srcdir)/'`src/SHGetFolderPathW.c

src/libwindowsappcompat_a-SHGetFolderPathW.obj: src/SHGetFolderPathW.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-SHGetFolderPathW.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Tpo -c -o src/libwindowsappcompat_a-SHGetFolderPathW.obj `if test -f 'src/SHGetFolderPathW.c'; then $(CYGPATH_W) 'src/SHGetFolderPathW.c'; else $(CYGPATH_W) '$(srcdir)/src/SHGetFolderPathW.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Tpo src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/SHGetFolderPathW.c' object='src/libwindowsappcompat_a-SHGetFolderPathW.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-SHGetFolderPathW.obj `if test -f 'src/SHGetFolderPathW.c'; then $(CYGPATH_W) 'src/SHGetFolderPathW.c'; else $(CYGPATH_W) '$(srcdir)/src/SHGetFolderPathW.c'; fi`

src/libwindowsappcompat_a-QueueTimer.o: src/QueueTimer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-QueueTimer.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Tpo -c -o src/libwindowsappcompat_a-QueueTimer.o `test -f 'src/QueueTimer.c' || echo '$(srcdir)/'`src/QueueTimer.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Tpo src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/QueueTimer.c' object='src/libwindowsappcompat_a-QueueTimer.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-QueueTimer.o `test -f 'src/QueueTimer.c' || echo '$(srcdir)/'`src/QueueTimer.c

src/libwindowsappcompat_a-QueueTimer.obj: src/QueueTimer.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-QueueTimer.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Tpo -c -o src/libwindowsappcompat_a-QueueTimer.obj `if test -f 'src/QueueTimer.c'; then $(CYGPATH_W) 'src/QueueTimer.c'; else $(CYGPATH_W) '$(srcdir)/src/QueueTimer.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Tpo src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/QueueTimer.c' object='src/libwindowsappcompat_a-QueueTimer.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-QueueTimer.obj `if test -f 'src/QueueTimer.c'; then $(CYGPATH_W) 'src/QueueTimer.c'; else $(CYGPATH_W) '$(srcdir)/src/QueueTimer.c'; fi`

src/libwindowsappcompat_a-Crypto.o: src/Crypto.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-Crypto.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Tpo -c -o src/libwindowsappcompat_a-Crypto.o `test -f 'src/Crypto.c' || echo '$(srcdir)/'`src/Crypto.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Tpo src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/Crypto.c' object='src/libwindowsappcompat_a-Crypto.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-Crypto.o `test -f 'src/Crypto.c' || echo '$(srcdir)/'`src/Crypto.c

src/libwindowsappcompat_a-Crypto.obj: src/Crypto.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-Crypto.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Tpo -c -o src/libwindowsappcompat_a-Crypto.obj `if test -f 'src/Crypto.c'; then $(CYGPATH_W) 'src/Crypto.c'; else $(CYGPATH_W) '$(srcdir)/src/Crypto.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Tpo src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/Crypto.c' object='src/libwindowsappcompat_a-Crypto.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-Crypto.obj `if test -f 'src/Crypto.c'; then $(CYGPATH_W) 'src/Crypto.c'; else $(CYGPATH_W) '$(srcdir)/src/Crypto.c'; fi`

src/libwindowsappcompat_a-GetStartupInfo.o: src/GetStartupInfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetStartupInfo.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Tpo -c -o src/libwindowsappcompat_a-GetStartupInfo.o `test -f 'src/GetStartupInfo.c' || echo '$(srcdir)/'`src/GetStartupInfo.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetStartupInfo.c' object='src/libwindowsappcompat_a-GetStartupInfo.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetStartupInfo.o `test -f 'src/GetStartupInfo.c' || echo '$(srcdir)/'`src/GetStartupInfo.c

src/libwindowsappcompat_a-GetStartupInfo.obj: src/GetStartupInfo.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetStartupInfo.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Tpo -c -o src/libwindowsappcompat_a-GetStartupInfo.obj `if test -f 'src/GetStartupInfo.c'; then $(CYGPATH_W) 'src/GetStartupInfo.c'; else $(CYGPATH_W) '$(srcdir)/src/GetStartupInfo.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetStartupInfo.c' object='src/libwindowsappcompat_a-GetStartupInfo.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetStartupInfo.obj `if test -f 'src/GetStartupInfo.c'; then $(CYGPATH_W) 'src/GetStartupInfo.c'; else $(CYGPATH_W) '$(srcdir)/src/GetStartupInfo.c'; fi`

src/libwindowsappcompat_a-EnumProcessModules.o: src/EnumProcessModules.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-EnumProcessModules.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Tpo -c -o src/libwindowsappcompat_a-EnumProcessModules.o `test -f 'src/EnumProcessModules.c' || echo '$(srcdir)/'`src/EnumProcessModules.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Tpo src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/EnumProcessModules.c' object='src/libwindowsappcompat_a-EnumProcessModules.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-EnumProcessModules.o `test -f 'src/EnumProcessModules.c' || echo '$(srcdir)/'`src/EnumProcessModules.c

src/libwindowsappcompat_a-EnumProcessModules.obj: src/EnumProcessModules.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-EnumProcessModules.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Tpo -c -o src/libwindowsappcompat_a-EnumProcessModules.obj `if test -f 'src/EnumProcessModules.c'; then $(CYGPATH_W) 'src/EnumProcessModules.c'; else $(CYGPATH_W) '$(srcdir)/src/EnumProcessModules.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Tpo src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/EnumProcessModules.c' object='src/libwindowsappcompat_a-EnumProcessModules.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-EnumProcessModules.obj `if test -f 'src/EnumProcessModules.c'; then $(CYGPATH_W) 'src/EnumProcessModules.c'; else $(CYGPATH_W) '$(srcdir)/src/EnumProcessModules.c'; fi`

src/libwindowsappcompat_a-RtlAddFunctionTable.o: src/RtlAddFunctionTable.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlAddFunctionTable.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Tpo -c -o src/libwindowsappcompat_a-RtlAddFunctionTable.o `test -f 'src/RtlAddFunctionTable.c' || echo '$(srcdir)/'`src/RtlAddFunctionTable.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlAddFunctionTable.c' object='src/libwindowsappcompat_a-RtlAddFunctionTable.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlAddFunctionTable.o `test -f 'src/RtlAddFunctionTable.c' || echo '$(srcdir)/'`src/RtlAddFunctionTable.c

src/libwindowsappcompat_a-RtlAddFunctionTable.obj: src/RtlAddFunctionTable.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlAddFunctionTable.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Tpo -c -o src/libwindowsappcompat_a-RtlAddFunctionTable.obj `if test -f 'src/RtlAddFunctionTable.c'; then $(CYGPATH_W) 'src/RtlAddFunctionTable.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlAddFunctionTable.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlAddFunctionTable.c' object='src/libwindowsappcompat_a-RtlAddFunctionTable.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlAddFunctionTable.obj `if test -f 'src/RtlAddFunctionTable.c'; then $(CYGPATH_W) 'src/RtlAddFunctionTable.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlAddFunctionTable.c'; fi`

src/libwindowsappcompat_a-RtlCaptureContext.o: src/RtlCaptureContext.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlCaptureContext.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Tpo -c -o src/libwindowsappcompat_a-RtlCaptureContext.o `test -f 'src/RtlCaptureContext.c' || echo '$(srcdir)/'`src/RtlCaptureContext.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlCaptureContext.c' object='src/libwindowsappcompat_a-RtlCaptureContext.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlCaptureContext.o `test -f 'src/RtlCaptureContext.c' || echo '$(srcdir)/'`src/RtlCaptureContext.c

src/libwindowsappcompat_a-RtlCaptureContext.obj: src/RtlCaptureContext.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlCaptureContext.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Tpo -c -o src/libwindowsappcompat_a-RtlCaptureContext.obj `if test -f 'src/RtlCaptureContext.c'; then $(CYGPATH_W) 'src/RtlCaptureContext.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlCaptureContext.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlCaptureContext.c' object='src/libwindowsappcompat_a-RtlCaptureContext.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlCaptureContext.obj `if test -f 'src/RtlCaptureContext.c'; then $(CYGPATH_W) 'src/RtlCaptureContext.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlCaptureContext.c'; fi`

src/libwindowsappcompat_a-RtlVirtualUnwind.o: src/RtlVirtualUnwind.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlVirtualUnwind.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Tpo -c -o src/libwindowsappcompat_a-RtlVirtualUnwind.o `test -f 'src/RtlVirtualUnwind.c' || echo '$(srcdir)/'`src/RtlVirtualUnwind.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlVirtualUnwind.c' object='src/libwindowsappcompat_a-RtlVirtualUnwind.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlVirtualUnwind.o `test -f 'src/RtlVirtualUnwind.c' || echo '$(srcdir)/'`src/RtlVirtualUnwind.c

src/libwindowsappcompat_a-RtlVirtualUnwind.obj: src/RtlVirtualUnwind.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlVirtualUnwind.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Tpo -c -o src/libwindowsappcompat_a-RtlVirtualUnwind.obj `if test -f 'src/RtlVirtualUnwind.c'; then $(CYGPATH_W) 'src/RtlVirtualUnwind.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlVirtualUnwind.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlVirtualUnwind.c' object='src/libwindowsappcompat_a-RtlVirtualUnwind.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlVirtualUnwind.obj `if test -f 'src/RtlVirtualUnwind.c'; then $(CYGPATH_W) 'src/RtlVirtualUnwind.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlVirtualUnwind.c'; fi`

src/libwindowsappcompat_a-RtlRestoreContext.o: src/RtlRestoreContext.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlRestoreContext.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Tpo -c -o src/libwindowsappcompat_a-RtlRestoreContext.o `test -f 'src/RtlRestoreContext.c' || echo '$(srcdir)/'`src/RtlRestoreContext.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlRestoreContext.c' object='src/libwindowsappcompat_a-RtlRestoreContext.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlRestoreContext.o `test -f 'src/RtlRestoreContext.c' || echo '$(srcdir)/'`src/RtlRestoreContext.c

src/libwindowsappcompat_a-RtlRestoreContext.obj: src/RtlRestoreContext.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-RtlRestoreContext.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Tpo -c -o src/libwindowsappcompat_a-RtlRestoreContext.obj `if test -f 'src/RtlRestoreContext.c'; then $(CYGPATH_W) 'src/RtlRestoreContext.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlRestoreContext.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Tpo src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/RtlRestoreContext.c' object='src/libwindowsappcompat_a-RtlRestoreContext.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-RtlRestoreContext.obj `if test -f 'src/RtlRestoreContext.c'; then $(CYGPATH_W) 'src/RtlRestoreContext.c'; else $(CYGPATH_W) '$(srcdir)/src/RtlRestoreContext.c'; fi`

src/libwindowsappcompat_a-GetUserName.o: src/GetUserName.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetUserName.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Tpo -c -o src/libwindowsappcompat_a-GetUserName.o `test -f 'src/GetUserName.c' || echo '$(srcdir)/'`src/GetUserName.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetUserName.c' object='src/libwindowsappcompat_a-GetUserName.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetUserName.o `test -f 'src/GetUserName.c' || echo '$(srcdir)/'`src/GetUserName.c

src/libwindowsappcompat_a-GetUserName.obj: src/GetUserName.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetUserName.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Tpo -c -o src/libwindowsappcompat_a-GetUserName.obj `if test -f 'src/GetUserName.c'; then $(CYGPATH_W) 'src/GetUserName.c'; else $(CYGPATH_W) '$(srcdir)/src/GetUserName.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetUserName.c' object='src/libwindowsappcompat_a-GetUserName.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetUserName.obj `if test -f 'src/GetUserName.c'; then $(CYGPATH_W) 'src/GetUserName.c'; else $(CYGPATH_W) '$(srcdir)/src/GetUserName.c'; fi`

src/libwindowsappcompat_a-getpid.o: src/getpid.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-getpid.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-getpid.Tpo -c -o src/libwindowsappcompat_a-getpid.o `test -f 'src/getpid.c' || echo '$(srcdir)/'`src/getpid.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-getpid.Tpo src/$(DEPDIR)/libwindowsappcompat_a-getpid.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/getpid.c' object='src/libwindowsappcompat_a-getpid.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-getpid.o `test -f 'src/getpid.c' || echo '$(srcdir)/'`src/getpid.c

src/libwindowsappcompat_a-getpid.obj: src/getpid.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-getpid.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-getpid.Tpo -c -o src/libwindowsappcompat_a-getpid.obj `if test -f 'src/getpid.c'; then $(CYGPATH_W) 'src/getpid.c'; else $(CYGPATH_W) '$(srcdir)/src/getpid.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-getpid.Tpo src/$(DEPDIR)/libwindowsappcompat_a-getpid.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/getpid.c' object='src/libwindowsappcompat_a-getpid.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-getpid.obj `if test -f 'src/getpid.c'; then $(CYGPATH_W) 'src/getpid.c'; else $(CYGPATH_W) '$(srcdir)/src/getpid.c'; fi`

src/libwindowsappcompat_a-GetFileInformationByHandle.o: src/GetFileInformationByHandle.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetFileInformationByHandle.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Tpo -c -o src/libwindowsappcompat_a-GetFileInformationByHandle.o `test -f 'src/GetFileInformationByHandle.c' || echo '$(srcdir)/'`src/GetFileInformationByHandle.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetFileInformationByHandle.c' object='src/libwindowsappcompat_a-GetFileInformationByHandle.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetFileInformationByHandle.o `test -f 'src/GetFileInformationByHandle.c' || echo '$(srcdir)/'`src/GetFileInformationByHandle.c

src/libwindowsappcompat_a-GetFileInformationByHandle.obj: src/GetFileInformationByHandle.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-GetFileInformationByHandle.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Tpo -c -o src/libwindowsappcompat_a-GetFileInformationByHandle.obj `if test -f 'src/GetFileInformationByHandle.c'; then $(CYGPATH_W) 'src/GetFileInformationByHandle.c'; else $(CYGPATH_W) '$(srcdir)/src/GetFileInformationByHandle.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Tpo src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/GetFileInformationByHandle.c' object='src/libwindowsappcompat_a-GetFileInformationByHandle.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-GetFileInformationByHandle.obj `if test -f 'src/GetFileInformationByHandle.c'; then $(CYGPATH_W) 'src/GetFileInformationByHandle.c'; else $(CYGPATH_W) '$(srcdir)/src/GetFileInformationByHandle.c'; fi`

src/libwindowsappcompat_a-SystemFunction036.o: src/SystemFunction036.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-SystemFunction036.o -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Tpo -c -o src/libwindowsappcompat_a-SystemFunction036.o `test -f 'src/SystemFunction036.c' || echo '$(srcdir)/'`src/SystemFunction036.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Tpo src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/SystemFunction036.c' object='src/libwindowsappcompat_a-SystemFunction036.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-SystemFunction036.o `test -f 'src/SystemFunction036.c' || echo '$(srcdir)/'`src/SystemFunction036.c

src/libwindowsappcompat_a-SystemFunction036.obj: src/SystemFunction036.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -MT src/libwindowsappcompat_a-SystemFunction036.obj -MD -MP -MF src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Tpo -c -o src/libwindowsappcompat_a-SystemFunction036.obj `if test -f 'src/SystemFunction036.c'; then $(CYGPATH_W) 'src/SystemFunction036.c'; else $(CYGPATH_W) '$(srcdir)/src/SystemFunction036.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Tpo src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/SystemFunction036.c' object='src/libwindowsappcompat_a-SystemFunction036.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libwindowsappcompat_a_CFLAGS) $(CFLAGS) -c -o src/libwindowsappcompat_a-SystemFunction036.obj `if test -f 'src/SystemFunction036.c'; then $(CYGPATH_W) 'src/SystemFunction036.c'; else $(CYGPATH_W) '$(srcdir)/src/SystemFunction036.c'; fi`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-zstd: distdir
	tardir=$(distdir) && $(am__tar) | zstd -c $${ZSTD_CLEVEL-$${ZSTD_OPT--19}} >$(distdir).tar.zst
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	*.tar.zst*) \
	  zstd -dc $(distdir).tar.zst | $(am__untar) ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) $(AM_DISTCHECK_DVI_TARGET) \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
check: check-am
all-am: Makefile $(LIBRARIES)
installdirs:
	for dir in "$(DESTDIR)$(libdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f src/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLIBRARIES mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
		-rm -f src/$(DEPDIR)/CreateEventW.Po
	-rm -f src/$(DEPDIR)/CreateFileW.Po
	-rm -f src/$(DEPDIR)/CreateMutexW.Po
	-rm -f src/$(DEPDIR)/CreateSemaphoreW.Po
	-rm -f src/$(DEPDIR)/Crypto.Po
	-rm -f src/$(DEPDIR)/EnumProcessModules.Po
	-rm -f src/$(DEPDIR)/GetACP.Po
	-rm -f src/$(DEPDIR)/GetConsoleOutputCP.Po
	-rm -f src/$(DEPDIR)/GetFileAttributes.Po
	-rm -f src/$(DEPDIR)/GetFileInformationByHandle.Po
	-rm -f src/$(DEPDIR)/GetFileSize.Po
	-rm -f src/$(DEPDIR)/GetModuleHandle.Po
	-rm -f src/$(DEPDIR)/GetStartupInfo.Po
	-rm -f src/$(DEPDIR)/GetTickCount.Po
	-rm -f src/$(DEPDIR)/GetUserName.Po
	-rm -f src/$(DEPDIR)/InitializeCriticalSection.Po
	-rm -f src/$(DEPDIR)/IsDBCSLeadByteEx.Po
	-rm -f src/$(DEPDIR)/LoadLibraryW.Po
	-rm -f src/$(DEPDIR)/LocalAlloc.Po
	-rm -f src/$(DEPDIR)/LocalFree.Po
	-rm -f src/$(DEPDIR)/QueueTimer.Po
	-rm -f src/$(DEPDIR)/RtlAddFunctionTable.Po
	-rm -f src/$(DEPDIR)/RtlCaptureContext.Po
	-rm -f src/$(DEPDIR)/RtlRestoreContext.Po
	-rm -f src/$(DEPDIR)/RtlVirtualUnwind.Po
	-rm -f src/$(DEPDIR)/SHGetFolderPathW.Po
	-rm -f src/$(DEPDIR)/SetErrorMode.Po
	-rm -f src/$(DEPDIR)/SetFilePointer.Po
	-rm -f src/$(DEPDIR)/SetUnhandledExceptionFilter.Po
	-rm -f src/$(DEPDIR)/Sleep.Po
	-rm -f src/$(DEPDIR)/SleepEx.Po
	-rm -f src/$(DEPDIR)/SystemFunction036.Po
	-rm -f src/$(DEPDIR)/TerminateProcess.Po
	-rm -f src/$(DEPDIR)/Tls.Po
	-rm -f src/$(DEPDIR)/UnhandledExceptionFilter.Po
	-rm -f src/$(DEPDIR)/VirtualProtect.Po
	-rm -f src/$(DEPDIR)/WaitForSingleObject.Po
	-rm -f src/$(DEPDIR)/beginthread.Po
	-rm -f src/$(DEPDIR)/getenv.Po
	-rm -f src/$(DEPDIR)/getpid.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-getenv.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-getpid.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
		-rm -f src/$(DEPDIR)/CreateEventW.Po
	-rm -f src/$(DEPDIR)/CreateFileW.Po
	-rm -f src/$(DEPDIR)/CreateMutexW.Po
	-rm -f src/$(DEPDIR)/CreateSemaphoreW.Po
	-rm -f src/$(DEPDIR)/Crypto.Po
	-rm -f src/$(DEPDIR)/EnumProcessModules.Po
	-rm -f src/$(DEPDIR)/GetACP.Po
	-rm -f src/$(DEPDIR)/GetConsoleOutputCP.Po
	-rm -f src/$(DEPDIR)/GetFileAttributes.Po
	-rm -f src/$(DEPDIR)/GetFileInformationByHandle.Po
	-rm -f src/$(DEPDIR)/GetFileSize.Po
	-rm -f src/$(DEPDIR)/GetModuleHandle.Po
	-rm -f src/$(DEPDIR)/GetStartupInfo.Po
	-rm -f src/$(DEPDIR)/GetTickCount.Po
	-rm -f src/$(DEPDIR)/GetUserName.Po
	-rm -f src/$(DEPDIR)/InitializeCriticalSection.Po
	-rm -f src/$(DEPDIR)/IsDBCSLeadByteEx.Po
	-rm -f src/$(DEPDIR)/LoadLibraryW.Po
	-rm -f src/$(DEPDIR)/LocalAlloc.Po
	-rm -f src/$(DEPDIR)/LocalFree.Po
	-rm -f src/$(DEPDIR)/QueueTimer.Po
	-rm -f src/$(DEPDIR)/RtlAddFunctionTable.Po
	-rm -f src/$(DEPDIR)/RtlCaptureContext.Po
	-rm -f src/$(DEPDIR)/RtlRestoreContext.Po
	-rm -f src/$(DEPDIR)/RtlVirtualUnwind.Po
	-rm -f src/$(DEPDIR)/SHGetFolderPathW.Po
	-rm -f src/$(DEPDIR)/SetErrorMode.Po
	-rm -f src/$(DEPDIR)/SetFilePointer.Po
	-rm -f src/$(DEPDIR)/SetUnhandledExceptionFilter.Po
	-rm -f src/$(DEPDIR)/Sleep.Po
	-rm -f src/$(DEPDIR)/SleepEx.Po
	-rm -f src/$(DEPDIR)/SystemFunction036.Po
	-rm -f src/$(DEPDIR)/TerminateProcess.Po
	-rm -f src/$(DEPDIR)/Tls.Po
	-rm -f src/$(DEPDIR)/UnhandledExceptionFilter.Po
	-rm -f src/$(DEPDIR)/VirtualProtect.Po
	-rm -f src/$(DEPDIR)/WaitForSingleObject.Po
	-rm -f src/$(DEPDIR)/beginthread.Po
	-rm -f src/$(DEPDIR)/getenv.Po
	-rm -f src/$(DEPDIR)/getpid.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-CreateFileW.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-Crypto.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-EnumProcessModules.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetFileInformationByHandle.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetFileSize.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetModuleHandle.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetStartupInfo.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-GetUserName.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-LoadLibraryW.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-QueueTimer.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlAddFunctionTable.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlCaptureContext.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlRestoreContext.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-RtlVirtualUnwind.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-SHGetFolderPathW.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-SystemFunction036.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-UnhandledExceptionFilter.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-VirtualProtect.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-beginthread.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-getenv.Po
	-rm -f src/$(DEPDIR)/libwindowsappcompat_a-getpid.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libLIBRARIES

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles am--refresh check \
	check-am clean clean-cscope clean-generic clean-libLIBRARIES \
	cscope cscopelist-am ctags ctags-am dist dist-all dist-bzip2 \
	dist-gzip dist-lzip dist-shar dist-tarZ dist-xz dist-zip \
	dist-zstd distcheck distclean distclean-compile \
	distclean-generic distclean-tags distcleancheck distdir \
	distuninstallcheck dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libLIBRARIES install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-libLIBRARIES

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
