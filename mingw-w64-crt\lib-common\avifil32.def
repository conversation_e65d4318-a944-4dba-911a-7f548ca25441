; 
; Exports of file AVIFIL32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY AVIFIL32.dll
EXPORTS
AVIBuildFilter
AVIBuildFilterA
AVIBuildFilterW
AVIClearClipboard
AVIFileAddRef
AVIFileCreateStream
AVIFileCreateStreamA
AVIFileCreateStreamW
AVIFileEndRecord
AVIFileExit
AVIFileGetStream
AVIFileInfo
AVIFileInfoA
AVIFileInfoW
AVIFileInit
AVIFileOpen
AVIFileOpenA
AVIFileOpenW
AVIFileReadData
AVIFileRelease
AVIFileWriteData
AVIGetFromClipboard
AVIMakeCompressedStream
AVIMakeFileFromStreams
AVIMakeStreamFromClipboard
AVIPutFileOnClipboard
AVISave
AVISaveA
AVISaveOptions
AVISaveOptionsFree
AVISaveV
AVISaveVA
AVISaveVW
AVISaveW
AVIStreamAddRef
AVIStreamBeginStreaming
AVIStreamCreate
AVIStreamEndStreaming
AVIStreamFindSample
AVIStreamGetFrame
AVIStreamGetFrameClose
AVIStreamGetFrameOpen
AVIStreamInfo
AVIStreamInfoA
AVIStreamInfoW
AVIStreamLength
AVIStreamOpenFromFile
AVIStreamOpenFromFileA
AVIStreamOpenFromFileW
AVIStreamRead
AVIStreamReadData
AVIStreamReadFormat
AVIStreamRelease
AVIStreamSampleToTime
AVIStreamSetFormat
AVIStreamStart
AVIStreamTimeToSample
AVIStreamWrite
AVIStreamWriteData
CreateEditableStream
DllCanUnloadNow
DllGetClassObject
EditStreamClone
EditStreamCopy
EditStreamCut
EditStreamPaste
EditStreamSetInfo
EditStreamSetInfoA
EditStreamSetInfoW
EditStreamSetName
EditStreamSetNameA
EditStreamSetNameW
IID_IAVIEditStream
IID_IAVIFile
IID_IAVIStream
IID_IGetFrame
