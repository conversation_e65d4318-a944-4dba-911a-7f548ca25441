/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _DDAOERR_H_
#define _DDAOERR_H_

#define DBDAOERR(x) MAKE_SCODE(SEVERITY_ERROR,FACILITY_CONTROL,x)

#define E_DAO_InternalError DBDAOERR(3000)
#define E_DAO_InvalidParameter DBDAOERR(3001)
#define E_DAO_CantBegin DBDAOERR(3002)
#define E_DAO_TransTooDeep DBDAOERR(3003)
#define E_DAO_DatabaseNotFound DBDAOERR(3004)
#define E_DAO_DatabaseInvalidName DBDAOERR(3005)
#define E_DAO_DatabaseLocked DBDAOERR(3006)
#define E_DAO_DatabaseOpenError DBDAOERR(3007)
#define E_DAO_TableLocked DBDAOERR(3008)
#define E_DAO_TableInUse DBDAOERR(3009)
#define E_DAO_TableDuplicate DBDAOERR(3010)
#define E_DAO_ObjectNotFound DBDAOERR(3011)
#define E_DAO_ObjectDuplicate DBDAOERR(3012)
#define E_DAO_CannotRename DBDAOERR(3013)
#define E_DAO_TooManyOpenTables DBDAOERR(3014)
#define E_DAO_IndexNotFound DBDAOERR(3015)
#define E_DAO_ColumnDoesNotFit DBDAOERR(3016)
#define E_DAO_ColumnTooBig DBDAOERR(3017)
#define E_DAO_ColumnNotFound DBDAOERR(3018)
#define E_DAO_NoCurrentIndex DBDAOERR(3019)
#define E_DAO_RecordNoCopy DBDAOERR(3020)
#define E_DAO_NoCurrentRecord DBDAOERR(3021)
#define E_DAO_KeyDuplicate DBDAOERR(3022)
#define E_DAO_AlreadyPrepared DBDAOERR(3023)
#define E_DAO_FileNotFound DBDAOERR(3024)
#define E_DAO_TooManyOpenFiles DBDAOERR(3025)
#define E_DAO_DiskFull DBDAOERR(3026)
#define E_DAO_PermissionDenied DBDAOERR(3027)
#define E_DAO_CannotOpenSystemDb DBDAOERR(3028)
#define E_DAO_InvalidLogon DBDAOERR(3029)
#define E_DAO_InvalidAccountName DBDAOERR(3030)
#define E_DAO_InvalidPassword DBDAOERR(3031)
#define E_DAO_InvalidOperation DBDAOERR(3032)
#define E_DAO_AccessDenied DBDAOERR(3033)
#define E_DAO_NotInTransaction DBDAOERR(3034)
#define E_DAO_OutOfMemory DBDAOERR(3035)
#define E_DAO_CantAllocatePage DBDAOERR(3036)
#define E_DAO_NoMoreCursors DBDAOERR(3037)
#define E_DAO_OutOfBuffers DBDAOERR(3038)
#define E_DAO_TooManyIndexes DBDAOERR(3039)
#define E_DAO_ReadVerifyFailure DBDAOERR(3040)
#define E_DAO_FilesysVersion DBDAOERR(3041)
#define E_DAO_NoMoreFiles DBDAOERR(3042)
#define E_DAO_DiskError DBDAOERR(3043)
#define E_DAO_InvalidPath DBDAOERR(3044)
#define E_DAO_FileShareViolation DBDAOERR(3045)
#define E_DAO_FileLockViolation DBDAOERR(3046)
#define E_DAO_RecordTooBig DBDAOERR(3047)
#define E_DAO_TooManyOpenDatabases DBDAOERR(3048)
#define E_DAO_InvalidDatabase DBDAOERR(3049)
#define E_DAO_FileLockingUnavailable DBDAOERR(3050)
#define E_DAO_FileAccessDenied DBDAOERR(3051)
#define E_DAO_SharingBufferExceeded DBDAOERR(3052)
#define E_DAO_TaskLimitExceeded DBDAOERR(3053)
#define E_DAO_TooManyLongColumns DBDAOERR(3054)
#define E_DAO_InvalidFilename DBDAOERR(3055)
#define E_DAO_AbortSalvage DBDAOERR(3056)
#define E_DAO_LinkNotSupported DBDAOERR(3057)
#define E_DAO_NullKeyDisallowed DBDAOERR(3058)
#define E_DAO_OperationCanceled DBDAOERR(3059)
#define E_DAO_QueryParmTypeMismatch DBDAOERR(3060)
#define E_DAO_QueryMissingParmsM DBDAOERR(3061)
#define E_DAO_QueryDuplicateAliasM DBDAOERR(3062)
#define E_DAO_QueryDuplicateOutputM DBDAOERR(3063)
#define E_DAO_QueryIsBulkOp DBDAOERR(3064)
#define E_DAO_QueryIsNotBulkOp DBDAOERR(3065)
#define E_DAO_QueryNoOutputsM DBDAOERR(3066)
#define E_DAO_QueryNoInputTablesM DBDAOERR(3067)
#define E_DAO_QueryInvalidAlias DBDAOERR(3068)
#define E_DAO_QueryInvalidBulkInputM DBDAOERR(3069)
#define E_DAO_QueryUnboundRef DBDAOERR(3070)
#define E_DAO_QueryExprEvaluation DBDAOERR(3071)
#define E_DAO_EvalEBESErr DBDAOERR(3072)
#define E_DAO_QueryNotUpdatable DBDAOERR(3073)
#define E_DAO_TableRepeatInFromList DBDAOERR(3074)
#define E_DAO_QueryExprSyntax DBDAOERR(3075)
#define E_DAO_QbeExprSyntax DBDAOERR(3076)
#define E_DAO_FindExprSyntax DBDAOERR(3077)
#define E_DAO_InputTableNotFound DBDAOERR(3078)
#define E_DAO_QueryAmbigRefM DBDAOERR(3079)
#define E_DAO_JoinTableNotInput DBDAOERR(3080)
#define E_DAO_UnaliasedSelfJoin DBDAOERR(3081)
#define E_DAO_ColumnNotInJoinTable DBDAOERR(3082)
#define E_DAO_QueryIsMGB DBDAOERR(3083)
#define E_DAO_QueryInsIntoBulkMGB DBDAOERR(3084)
#define E_DAO_ExprUnknownFunctionM DBDAOERR(3085)
#define E_DAO_QueryCannotDelete DBDAOERR(3086)
#define E_DAO_QueryTooManyGroupExprs DBDAOERR(3087)
#define E_DAO_QueryTooManyOrderExprs DBDAOERR(3088)
#define E_DAO_QueryTooManyDistExprs DBDAOERR(3089)
#define E_DAO_Column2ndSysMaint DBDAOERR(3090)
#define E_DAO_HavingWOGrouping DBDAOERR(3091)
#define E_DAO_HavingOnTransform DBDAOERR(3092)
#define E_DAO_OrderVsDistinct DBDAOERR(3093)
#define E_DAO_OrderVsGroup DBDAOERR(3094)
#define E_DAO_AggregateInArgument DBDAOERR(3095)
#define E_DAO_AggregateInWhere DBDAOERR(3096)
#define E_DAO_AggregateInOrderBy DBDAOERR(3097)
#define E_DAO_AggregateInGroupBy DBDAOERR(3098)
#define E_DAO_AggregateInJoin DBDAOERR(3099)
#define E_DAO_NullInJoinKey DBDAOERR(3100)
#define E_DAO_ValueBreaksJoin DBDAOERR(3101)
#define E_DAO_QueryTreeCycle DBDAOERR(3102)
#define E_DAO_OutputAliasCycle DBDAOERR(3103)
#define E_DAO_QryDuplicatedFixedSetM DBDAOERR(3104)
#define E_DAO_NoSelectIntoColumnName DBDAOERR(3105)
#define E_DAO_NoUpdateColumnName DBDAOERR(3106)
#define E_DAO_QueryNoInsertPerm DBDAOERR(3107)
#define E_DAO_QueryNoReplacePerm DBDAOERR(3108)
#define E_DAO_QueryNoDeletePerm DBDAOERR(3109)
#define E_DAO_QueryNoReadDefPerm DBDAOERR(3110)
#define E_DAO_QueryNoTblCrtPerm DBDAOERR(3111)
#define E_DAO_QueryNoReadPerm DBDAOERR(3112)
#define E_DAO_QueryColNotUpd DBDAOERR(3113)
#define E_DAO_QueryLVInDistinct DBDAOERR(3114)
#define E_DAO_QueryLVInAggregate DBDAOERR(3115)
#define E_DAO_QueryLVInHaving DBDAOERR(3116)
#define E_DAO_QueryLVInOrderBy DBDAOERR(3117)
#define E_DAO_QueryLVInJoin DBDAOERR(3118)
#define E_DAO_QueryLVInGroupBy DBDAOERR(3119)
#define E_DAO_DotStarWithGrouping DBDAOERR(3120)
#define E_DAO_StarWithGrouping DBDAOERR(3121)
#define E_DAO_IllegalDetailRef DBDAOERR(3122)
#define E_DAO_StarNotAtLevel0 DBDAOERR(3123)
#define E_DAO_QueryInvalidMGBInput DBDAOERR(3124)
#define E_DAO_InvalidName DBDAOERR(3125)
#define E_DAO_QueryBadBracketing DBDAOERR(3126)
#define E_DAO_InsertIntoUnknownCol DBDAOERR(3127)
#define E_DAO_QueryNoDeleteTables DBDAOERR(3128)
#define E_DAO_SQLSyntax DBDAOERR(3129)
#define E_DAO_SQLDeleteSyntax DBDAOERR(3130)
#define E_DAO_SQLFromSyntax DBDAOERR(3131)
#define E_DAO_SQLGroupBySyntax DBDAOERR(3132)
#define E_DAO_SQLHavingSyntax DBDAOERR(3133)
#define E_DAO_SQLInsertSyntax DBDAOERR(3134)
#define E_DAO_SQLJoinSyntax DBDAOERR(3135)
#define E_DAO_SQLLevelSyntax DBDAOERR(3136)
#define E_DAO_SQLMissingSemicolon DBDAOERR(3137)
#define E_DAO_SQLOrderBySyntax DBDAOERR(3138)
#define E_DAO_SQLParameterSyntax DBDAOERR(3139)
#define E_DAO_SQLProcedureSyntax DBDAOERR(3140)
#define E_DAO_SQLSelectSyntax DBDAOERR(3141)
#define E_DAO_SQLTooManyTokens DBDAOERR(3142)
#define E_DAO_SQLTransformSyntax DBDAOERR(3143)
#define E_DAO_SQLUpdateSyntax DBDAOERR(3144)
#define E_DAO_SQLWhereSyntax DBDAOERR(3145)
#define E_DAO_RmtSQLCError DBDAOERR(3146)
#define E_DAO_RmtDataOverflow DBDAOERR(3147)
#define E_DAO_RmtConnectFailed DBDAOERR(3148)
#define E_DAO_RmtIncorrectSqlcDll DBDAOERR(3149)
#define E_DAO_RmtMissingSqlcDll DBDAOERR(3150)
#define E_DAO_RmtConnectFailedM DBDAOERR(3151)
#define E_DAO_RmtDrvrVer DBDAOERR(3152)
#define E_DAO_RmtSrvrVer DBDAOERR(3153)
#define E_DAO_RmtMissingOdbcDll DBDAOERR(3154)
#define E_DAO_RmtInsertFailedM DBDAOERR(3155)
#define E_DAO_RmtDeleteFailedM DBDAOERR(3156)
#define E_DAO_RmtUpdateFailedM DBDAOERR(3157)
#define E_DAO_RecordLocked DBDAOERR(3158)
#define E_DAO_InvalidBookmark DBDAOERR(3159)
#define E_DAO_TableNotOpen DBDAOERR(3160)
#define E_DAO_DecryptFail DBDAOERR(3161)
#define E_DAO_NullInvalid DBDAOERR(3162)
#define E_DAO_InvalidBufferSize DBDAOERR(3163)
#define E_DAO_ColumnNotUpdatable DBDAOERR(3164)
#define E_DAO_CantMakeINFFile DBDAOERR(3165)
#define E_DAO_MissingMemoFile DBDAOERR(3166)
#define E_DAO_RecordDeleted DBDAOERR(3167)
#define E_DAO_INFFileError DBDAOERR(3168)
#define E_DAO_ExprIllegalType DBDAOERR(3169)
#define E_DAO_InstalIsamNotFound DBDAOERR(3170)
#define E_DAO_NoConfigParameters DBDAOERR(3171)
#define E_DAO_CantAccessPdoxNetDir DBDAOERR(3172)
#define E_DAO_NoMSysAccounts DBDAOERR(3173)
#define E_DAO_NoMSysGroups DBDAOERR(3174)
#define E_DAO_DateOutOfRange DBDAOERR(3175)
#define E_DAO_ImexCantOpenFile DBDAOERR(3176)
#define E_DAO_ImexBadTableName DBDAOERR(3177)
#define E_DAO_ImexOutOfMemory DBDAOERR(3178)
#define E_DAO_ImexEndofFile DBDAOERR(3179)
#define E_DAO_ImexCantWriteToFile DBDAOERR(3180)
#define E_DAO_ImexBadRange DBDAOERR(3181)
#define E_DAO_ImexBogusFile DBDAOERR(3182)
#define E_DAO_TempDiskFull DBDAOERR(3183)
#define E_DAO_RmtLinkNotFound DBDAOERR(3184)
#define E_DAO_RmtTooManyColumns DBDAOERR(3185)
#define E_DAO_ReadConflictM DBDAOERR(3186)
#define E_DAO_CommitConflictM DBDAOERR(3187)
#define E_DAO_SessionWriteConflict DBDAOERR(3188)
#define E_DAO_JetSpecialTableLocked DBDAOERR(3189)
#define E_DAO_TooManyColumns DBDAOERR(3190)
#define E_DAO_ColumnDuplicate DBDAOERR(3191)
#define E_DAO_OutputTableNotFound DBDAOERR(3192)
#define E_DAO_JetNoUserName DBDAOERR(3193)
#define E_DAO_JetNoMachineName DBDAOERR(3194)
#define E_DAO_JetNoColumnName DBDAOERR(3195)
#define E_DAO_DatabaseInUse DBDAOERR(3196)
#define E_DAO_DataHasChanged DBDAOERR(3197)
#define E_DAO_TooManySessions DBDAOERR(3198)
#define E_DAO_ReferenceNotFound DBDAOERR(3199)
#define E_DAO_IntegrityViolMasterM DBDAOERR(3200)
#define E_DAO_IntegrityViolSlaveM DBDAOERR(3201)
#define E_DAO_ReadConflict DBDAOERR(3202)
#define E_DAO_AggregatingHigherLevel DBDAOERR(3203)
#define E_DAO_DatabaseDuplicate DBDAOERR(3204)
#define E_DAO_QueryTooManyXvtColumn DBDAOERR(3205)
#define E_DAO_SelfReference DBDAOERR(3206)
#define E_DAO_CantUseUnkeyedTable DBDAOERR(3207)
#define E_DAO_IllegalDeletedOption DBDAOERR(3208)
#define E_DAO_IllegalStatsOption DBDAOERR(3209)
#define E_DAO_ConnStrTooLong DBDAOERR(3210)
#define E_DAO_TableInUseQM DBDAOERR(3211)
#define E_DAO_JetSpecialTableInUse DBDAOERR(3212)
#define E_DAO_IllegalDateOption DBDAOERR(3213)
#define E_DAO_IllegalMarkOption DBDAOERR(3214)
#define E_DAO_BtrieveTooManyTasks DBDAOERR(3215)
#define E_DAO_QueryParmNotTableid DBDAOERR(3216)
#define E_DAO_QueryParmNotDatabase DBDAOERR(3217)
#define E_DAO_WriteConflict DBDAOERR(3218)
#define E_DAO_IllegalOperation DBDAOERR(3219)
#define E_DAO_WrongCollatingSequence DBDAOERR(3220)
#define E_DAO_BadConfigParameters DBDAOERR(3221)
#define E_DAO_QueryContainsDbParm DBDAOERR(3222)
#define E_DAO_QueryInvalidParmM DBDAOERR(3223)
#define E_DAO_BtrieveDDCorrupted DBDAOERR(3224)
#define E_DAO_BtrieveDeadlock DBDAOERR(3225)
#define E_DAO_BtrieveFailure DBDAOERR(3226)
#define E_DAO_IllegalCenturyOption DBDAOERR(3227)
#define E_DAO_IllegalCollatingSeq DBDAOERR(3228)
#define E_DAO_NonModifiableKey DBDAOERR(3229)
#define E_DAO_ObsoleteLockFile DBDAOERR(3230)
#define E_DAO_RmtColDataTruncated DBDAOERR(3231)
#define E_DAO_RmtCreateTableFailed DBDAOERR(3232)
#define E_DAO_RmtOdbcVer DBDAOERR(3233)
#define E_DAO_RmtQueryTimeout DBDAOERR(3234)
#define E_DAO_RmtTypeIncompat DBDAOERR(3235)
#define E_DAO_RmtUnexpectedNull DBDAOERR(3236)
#define E_DAO_RmtUnexpectedType DBDAOERR(3237)
#define E_DAO_RmtValueOutOfRange DBDAOERR(3238)
#define E_DAO_TooManyActiveUsers DBDAOERR(3239)
#define E_DAO_CantStartBtrieve DBDAOERR(3240)
#define E_DAO_OutOfBVResources DBDAOERR(3241)
#define E_DAO_QueryBadUpwardRefedM DBDAOERR(3242)
#define E_DAO_ImexNoMatchingColumns DBDAOERR(3243)
#define E_DAO_ImexPasswordProtected DBDAOERR(3244)
#define E_DAO_ImexUnparsableRecord DBDAOERR(3245)
#define E_DAO_InTransaction DBDAOERR(3246)
#define E_DAO_RmtLinkOutOfSync DBDAOERR(3247)
#define E_DAO_IllegalNetworkOption DBDAOERR(3248)
#define E_DAO_IllegalTimeoutOption DBDAOERR(3249)
#define E_DAO_CantBuildKey DBDAOERR(3250)
#define E_DAO_FeatureNotAvailable DBDAOERR(3251)
#define E_DAO_IllegalReentrancy DBDAOERR(3252)
#define E_DAO_UNUSED DBDAOERR(3253)
#define E_DAO_RmtDenyWriteIsInvalid DBDAOERR(3254)
#define E_DAO_ODBCParmsChanged DBDAOERR(3255)
#define E_DAO_INFIndexNotFound DBDAOERR(3256)
#define E_DAO_SQLOwnerAccessSyntax DBDAOERR(3257)
#define E_DAO_QueryAmbiguousJoins DBDAOERR(3258)
#define E_DAO_InvalidColumnType DBDAOERR(3259)
#define E_DAO_WriteConflictM DBDAOERR(3260)
#define E_DAO_TableLockedM DBDAOERR(3261)
#define E_DAO_TableInUseMUQM DBDAOERR(3262)
#define E_DAO_InvalidTableId DBDAOERR(3263)
#define E_DAO_VtoNoFields DBDAOERR(3264)
#define E_DAO_VtoNameNotFound DBDAOERR(3265)
#define E_DAO_VtoFieldInCollection DBDAOERR(3266)
#define E_DAO_VtoNotARecordset DBDAOERR(3267)
#define E_DAO_VtoNoSetObjInDb DBDAOERR(3268)
#define E_DAO_VtoIndexInCollection DBDAOERR(3269)
#define E_DAO_VtoPropNotFound DBDAOERR(3270)
#define E_DAO_VtoIllegalValue DBDAOERR(3271)
#define E_DAO_VtoNotArray DBDAOERR(3272)
#define E_DAO_VtoNoSuchMethod DBDAOERR(3273)
#define E_DAO_NotExternalFormat DBDAOERR(3274)
#define E_DAO_UnexpectedEngineReturn DBDAOERR(3275)
#define E_DAO_InvalidDatabaseId DBDAOERR(3276)
#define E_DAO_TooManyKeys DBDAOERR(3277)
#define E_DAO_NotInitialized DBDAOERR(3278)
#define E_DAO_AlreadyInitialized DBDAOERR(3279)
#define E_DAO_ColumnInUse DBDAOERR(3280)
#define E_DAO_IndexInUse DBDAOERR(3281)
#define E_DAO_TableNotEmpty DBDAOERR(3282)
#define E_DAO_IndexHasPrimary DBDAOERR(3283)
#define E_DAO_IndexDuplicate DBDAOERR(3284)
#define E_DAO_IndexInvalidDef DBDAOERR(3285)
#define E_DAO_WrongMemoFileType DBDAOERR(3286)
#define E_DAO_ColumnCannotIndex DBDAOERR(3287)
#define E_DAO_IndexHasNoPrimary DBDAOERR(3288)
#define E_DAO_DDLConstraintSyntax DBDAOERR(3289)
#define E_DAO_DDLCreateTableSyntax DBDAOERR(3290)
#define E_DAO_DDLCreateIndexSyntax DBDAOERR(3291)
#define E_DAO_DDLColumnDefSyntax DBDAOERR(3292)
#define E_DAO_DDLAlterTableSyntax DBDAOERR(3293)
#define E_DAO_DDLDropIndexSyntax DBDAOERR(3294)
#define E_DAO_DDLDropSyntax DBDAOERR(3295)
#define E_DAO_V11NotSupported DBDAOERR(3296)
#define E_DAO_ImexNothingToImport DBDAOERR(3297)
#define E_DAO_RmtTableAmbiguous DBDAOERR(3298)
#define E_DAO_JetODBCConformanceError DBDAOERR(3299)
#define E_DAO_IllegalRelationship DBDAOERR(3300)
#define E_DAO_DBVerFeatureNotAvailable DBDAOERR(3301)
#define E_DAO_RulesLoaded DBDAOERR(3302)
#define E_DAO_ColumnInRelationship DBDAOERR(3303)
#define E_DAO_InvalidPin DBDAOERR(3304)
#define E_DAO_RmtBogusConnStr DBDAOERR(3305)
#define E_DAO_SingleColumnExpected DBDAOERR(3306)
#define E_DAO_ColumnCountMismatch DBDAOERR(3307)
#define E_DAO_InvalidTopArgumentM DBDAOERR(3308)
#define E_DAO_PropertyTooLarge DBDAOERR(3309)
#define E_DAO_JPMInvalidForV1x DBDAOERR(3310)
#define E_DAO_PropertyExists DBDAOERR(3311)
#define E_DAO_TLVNativeUserTablesOnly DBDAOERR(3312)
#define E_DAO_TLVInvalidColumn DBDAOERR(3313)
#define E_DAO_TLVNoNullM DBDAOERR(3314)
#define E_DAO_TLVNoBlankM DBDAOERR(3315)
#define E_DAO_TLVRuleViolationM DBDAOERR(3316)
#define E_DAO_TLVRuleVioNoMessage DBDAOERR(3317)
#define E_DAO_QueryTopNotAllowedM DBDAOERR(3318)
#define E_DAO_SQLUnionSyntax DBDAOERR(3319)
#define E_DAO_TLVExprSyntaxM DBDAOERR(3320)
#define E_DAO_NoDbInConnStr DBDAOERR(3321)
#define E_DAO_QueryBadValueListM DBDAOERR(3322)
#define E_DAO_QueryIsNotRowReturning DBDAOERR(3323)
#define E_DAO_QueryIsDDL DBDAOERR(3324)
#define E_DAO_SPTReturnedNoRecords DBDAOERR(3325)
#define E_DAO_QueryIsSnapshot DBDAOERR(3326)
#define E_DAO_QueryExprOutput DBDAOERR(3327)
#define E_DAO_QueryTableRO DBDAOERR(3328)
#define E_DAO_QueryRowDeleted DBDAOERR(3329)
#define E_DAO_QueryRowLocked DBDAOERR(3330)
#define E_DAO_QueryFixupChanged DBDAOERR(3331)
#define E_DAO_QueryCantFillIn DBDAOERR(3332)
#define E_DAO_QueryWouldOrphan DBDAOERR(3333)
#define E_DAO_V10Format DBDAOERR(3334)
#define E_DAO_InvalidDelete DBDAOERR(3335)
#define E_DAO_IllegalIndexDDFOption DBDAOERR(3336)
#define E_DAO_IllegalDataCodePage DBDAOERR(3337)
#define E_DAO_XtrieveEnvironmentError DBDAOERR(3338)
#define E_DAO_IllegalIndexNumberOption DBDAOERR(3339)
#define E_DAO_QueryIsCorruptM DBDAOERR(3340)
#define E_DAO_IncorrectJoinKeyM DBDAOERR(3341)
#define E_DAO_QueryLVInSubqueryM DBDAOERR(3342)
#define E_DAO_InvalidDatabaseM DBDAOERR(3343)
#define E_DAO_TLVCouldNotBindRef DBDAOERR(3344)
#define E_DAO_CouldNotBindRef DBDAOERR(3345)
#define E_DAO_QueryWrongNumDestCol DBDAOERR(3346)
#define E_DAO_QueryPKeyNotOutput DBDAOERR(3347)
#define E_DAO_QueryJKeyNotOutput DBDAOERR(3348)
#define E_DAO_NumericFieldOverflow DBDAOERR(3349)
#define E_DAO_InvalidObject DBDAOERR(3350)
#define E_DAO_OrderVsUnion DBDAOERR(3351)
#define E_DAO_NoInsertColumnNameM DBDAOERR(3352)
#define E_DAO_MissingDDFFile DBDAOERR(3353)
#define E_DAO_SingleRecordExpected DBDAOERR(3354)
#define E_DAO_DefaultExprSyntax DBDAOERR(3355)
#define E_DAO_ExclusiveDBConflict DBDAOERR(3356)
#define E_DAO_QueryIsNotDDL DBDAOERR(3357)
#define E_DAO_SysDatabaseOpenError DBDAOERR(3358)
#define E_DAO_SQLInvalidSPT DBDAOERR(3359)
#define E_DAO_QueryTooComplex DBDAOERR(3360)
#define E_DAO_SetOpInvalidInSubquery DBDAOERR(3361)
#define E_DAO_RmtMultiRowUpdate DBDAOERR(3362)
#define E_DAO_QueryNoJoinedRecord DBDAOERR(3363)
#define E_DAO_QueryLVInSetOp DBDAOERR(3364)
#define E_DAO_VtoInvalidOnRemote DBDAOERR(3365)
#define E_DAO_VtoNoFieldsRel DBDAOERR(3366)
#define E_DAO_VtoObjectInCollection DBDAOERR(3367)
#define E_DAO_DDLDiffNumRelCols DBDAOERR(3368)
#define E_DAO_DDLIndexColNotFound DBDAOERR(3369)
#define E_DAO_DDLPermissionDenied DBDAOERR(3370)
#define E_DAO_DDLObjectNotFound DBDAOERR(3371)
#define E_DAO_DDLIndexNotFound DBDAOERR(3372)
#define E_DAO_DDLNoPkeyOnRefdTable DBDAOERR(3373)
#define E_DAO_DDLColumnsNotUnique DBDAOERR(3374)
#define E_DAO_DDLIndexDuplicate DBDAOERR(3375)
#define E_DAO_DDLTableNotFound DBDAOERR(3376)
#define E_DAO_DDLRelNotFound DBDAOERR(3377)
#define E_DAO_DDLRelDuplicate DBDAOERR(3378)
#define E_DAO_DDLIntegrityViolation DBDAOERR(3379)
#define E_DAO_DDLColumnDuplicate DBDAOERR(3380)
#define E_DAO_DDLColumnNotFound DBDAOERR(3381)
#define E_DAO_DDLColumnTooBig DBDAOERR(3382)
#define E_DAO_DDLColumnInRel DBDAOERR(3383)
#define E_DAO_VtoCantDeleteBuiltIn DBDAOERR(3384)
#define E_DAO_VtoUDPsDontSupportNull DBDAOERR(3385)
#define E_DAO_VtoMissingRequiredParm DBDAOERR(3386)
#define E_DAO_JetJetInitInvalidPath DBDAOERR(3387)
#define E_DAO_TLVExprUnknownFunctionM DBDAOERR(3388)
#define E_DAO_QueryNotSupported DBDAOERR(3389)
#define E_DAO_AccountDuplicate DBDAOERR(3390)
#define E_DAO_JetwrnPropCouldNotSave DBDAOERR(3391)
#define E_DAO_RelNoPrimaryIndexM DBDAOERR(3392)
#define E_DAO_QueryKeyTooBig DBDAOERR(3393)
#define E_DAO_PropMustBeDDL DBDAOERR(3394)
#define E_DAO_IllegalRIConstraint DBDAOERR(3395)
#define E_DAO_RIViolationMasterCM DBDAOERR(3396)
#define E_DAO_RIViolationSlaveCM DBDAOERR(3397)
#define E_DAO_RIKeyNullDisallowedCM DBDAOERR(3398)
#define E_DAO_RIKeyDuplicateCM DBDAOERR(3399)
#define E_DAO_RIUpdateTwiceCM DBDAOERR(3400)
#define E_DAO_RITLVNoNullCM DBDAOERR(3401)
#define E_DAO_RITLVNoBlankCM DBDAOERR(3402)
#define E_DAO_RITLVRuleViolationCM DBDAOERR(3403)
#define E_DAO_RITLVRuleVioCNoMessage DBDAOERR(3404)
#define E_DAO_TLVRuleEvalEBESErr DBDAOERR(3405)
#define E_DAO_TLVDefaultEvalEBESErr DBDAOERR(3406)
#define E_DAO_BadMSysConf DBDAOERR(3407)
#define E_DAO_TooManyFindSessions DBDAOERR(3408)
#define E_DAO_InvalidColumnM DBDAOERR(3409)
#define E_DAO_REPReadOnly DBDAOERR(3410)
#define E_DAO_RIInvalidBufferSizeCM DBDAOERR(3411)
#define E_DAO_RIWriteConflictCM DBDAOERR(3412)
#define E_DAO_JetSpecialRIWriteConflictCM DBDAOERR(3413)
#define E_DAO_RISessWriteConflictCM DBDAOERR(3414)
#define E_DAO_NoBlank DBDAOERR(3415)
#define E_DAO_FutureError DBDAOERR(3416)
#define E_DAO_QueryInvalidBulkInput DBDAOERR(3417)
#define E_DAO_NetCtrlMismatch DBDAOERR(3418)
#define E_DAO_4xTableWith3xLocking DBDAOERR(3419)
#define E_DAO_VtoObjectNotSet DBDAOERR(3420)
#define E_DAO_VtoDataConvError DBDAOERR(3421)
#define E_DAO_TableNotLocked DBDAOERR(3422)
#define E_DAO_RmtDriverNotSupported DBDAOERR(3423)
#define E_DAO_InvalidLanguageId DBDAOERR(3424)
#define E_DAO_VtoInvalidOpDuringCallback DBDAOERR(3425)
#define E_DAO_VtoActionCancelled DBDAOERR(3426)
#define E_DAO_VtoOleAutoFailed DBDAOERR(3427)
#define E_DAO_DatabaseCorrupted_Cmpct DBDAOERR(3428)
#define E_DAO_IncompatibleIIsam DBDAOERR(3429)
#define E_DAO_OLEInitializeFailure DBDAOERR(3430)
#define E_DAO_OLENotCompoundFile DBDAOERR(3431)
#define E_DAO_OLEFailure DBDAOERR(3432)
#define E_DAO_IllegalIisamIniOption DBDAOERR(3433)
#define E_DAO_TableFull DBDAOERR(3434)
#define E_DAO_TableCantClear DBDAOERR(3435)
#define E_DAO_CreateFailed DBDAOERR(3436)
#define E_DAO_DatabaseFull DBDAOERR(3437)
#define E_DAO_SpecAndColumnMismatch DBDAOERR(3438)
#define E_DAO_CantOpenWordMergeFiles DBDAOERR(3439)
#define E_DAO_FileHasNoColumns DBDAOERR(3440)
#define E_DAO_AmbiguousDelimiters DBDAOERR(3441)
#define E_DAO_FileSpecErrorM DBDAOERR(3442)
#define E_DAO_NoSpecForFixedFormatM DBDAOERR(3443)
#define E_DAO_WidthMissInFixedSpecM DBDAOERR(3444)
#define E_DAO_VtoWrongDllVersion DBDAOERR(3445)
#define E_DAO_VtoMissingVBA DBDAOERR(3446)
#define E_DAO_VtoVBAFailed DBDAOERR(3447)
#define E_DAO_VtoOLEFailed DBDAOERR(3448)
#define E_DAO_InvalidCountry DBDAOERR(3449)
#define E_DAO_QueryIncompleteRowM DBDAOERR(3450)
#define E_DAO_QueryParmTypeNotAllowed DBDAOERR(3451)
#define E_DAO_REPDBNotMaster DBDAOERR(3452)
#define E_DAO_REPCantRelate DBDAOERR(3453)
#define E_DAO_REPNotOwner DBDAOERR(3454)
#define E_DAO_CantMakeReplicable DBDAOERR(3455)
#define E_DAO_CantMakeObjectReplicable DBDAOERR(3456)
#define E_DAO_REPCantKeepLocal DBDAOERR(3457)
#define E_DAO_REPCantKeepDBLocal DBDAOERR(3458)
#define E_DAO_CantUnreplDatabase DBDAOERR(3459)
#define E_DAO_ReplConflict DBDAOERR(3460)
#define E_DAO_REPSetRepid DBDAOERR(3461)
#define E_DAO_TransportLoadFailure DBDAOERR(3462)
#define E_DAO_TransportLoadFailureM DBDAOERR(3463)
#define E_DAO_TypeMismatchM DBDAOERR(3464)
#define E_DAO_DiskIOM DBDAOERR(3465)
#define E_DAO_FileAccessDeniedM DBDAOERR(3466)
#define E_DAO_InvalidPathM DBDAOERR(3467)
#define E_DAO_TranspAccessDeniedM DBDAOERR(3468)
#define E_DAO_TransportDiskFullM DBDAOERR(3469)
#define E_DAO_TransportDiskIOM DBDAOERR(3470)
#define E_DAO_LogWriteFail DBDAOERR(3471)
#define E_DAO_LogDiskFullM DBDAOERR(3472)
#define E_DAO_LogDiskIOM DBDAOERR(3473)
#define E_DAO_LogFileAccessDeniedM DBDAOERR(3474)
#define E_DAO_LogFileShareViolationM DBDAOERR(3475)
#define E_DAO_TransportInvalidPathM DBDAOERR(3476)
#define E_DAO_TranspInvalidAddressM DBDAOERR(3477)
#define E_DAO_RepNotPartial DBDAOERR(3478)
#define E_DAO_RepPartial DBDAOERR(3479)
#define E_DAO_PARTInvalidRelNameM DBDAOERR(3480)
#define E_DAO_PARTInvalidTableNameM DBDAOERR(3481)
#define E_DAO_REPInvalidFilter DBDAOERR(3482)
#define E_DAO_TranspInvalidPasswordM DBDAOERR(3483)
#define E_DAO_TransDestInvalidPassword DBDAOERR(3484)
#define E_DAO_REPDBNotRep DBDAOERR(3485)
#define E_DAO_REPSecondGuid DBDAOERR(3486)
#define E_DAO_REPOnlyBuiltin DBDAOERR(3487)
#define E_DAO_REPNoSuchRepid DBDAOERR(3488)
#define E_DAO_REPObjectNotRep DBDAOERR(3489)
#define E_DAO_CantCreateReplica DBDAOERR(3490)
#define E_DAO_MustOpenDbExclusive DBDAOERR(3491)
#define E_DAO_CantDoSchemaChange DBDAOERR(3492)
#define E_DAO_UnableToSetParam DBDAOERR(3493)
#define E_DAO_UnableToGetParam DBDAOERR(3494)
#define E_DAO_REPNoSuchSchedule DBDAOERR(3495)
#define E_DAO_REPNoSuchExchange DBDAOERR(3496)
#define E_DAO_REPCantSetSchedule DBDAOERR(3497)
#define E_DAO_REPCantGetSchedule DBDAOERR(3498)
#define E_DAO_REPCantGetDBPath DBDAOERR(3499)
#define E_DAO_REPCantSetExchange DBDAOERR(3500)
#define E_DAO_REPNotUpdated DBDAOERR(3501)
#define E_DAO_REPNotManaged DBDAOERR(3502)
#define E_DAO_ValueNotSet DBDAOERR(3503)
#define E_DAO_REPInvalidTID DBDAOERR(3504)
#define E_DAO_REPFilterNotFound DBDAOERR(3505)
#define E_DAO_OpenLog DBDAOERR(3506)
#define E_DAO_WriteLog DBDAOERR(3507)
#define E_DAO_NoTransport DBDAOERR(3508)
#define E_DAO_TransportNotFound DBDAOERR(3509)
#define E_DAO_ReplicaAlreadyLocked DBDAOERR(3510)
#define E_DAO_DBAccess DBDAOERR(3511)
#define E_DAO_TransportReadFailure DBDAOERR(3512)
#define E_DAO_TransportWriteFailure DBDAOERR(3513)
#define E_DAO_NoExchange DBDAOERR(3514)
#define E_DAO_SysClock DBDAOERR(3515)
#define E_DAO_NoTransportAddress DBDAOERR(3516)
#define E_DAO_NoMessage DBDAOERR(3517)
#define E_DAO_TransporterNotFound DBDAOERR(3518)
#define E_DAO_TransportSendFailure DBDAOERR(3519)
#define E_DAO_ReplicaNotFound DBDAOERR(3520)
#define E_DAO_OutOfSynch DBDAOERR(3521)
#define E_DAO_ExchangeNotFound DBDAOERR(3522)
#define E_DAO_SchemaNotFound DBDAOERR(3523)
#define E_DAO_SchemaHistMismatch DBDAOERR(3524)
#define E_DAO_MessageDBAccess DBDAOERR(3525)
#define E_DAO_ObjectAlreadyExists DBDAOERR(3526)
#define E_DAO_ObjectDoesntExist DBDAOERR(3527)
#define E_DAO_NoNewData DBDAOERR(3528)
#define E_DAO_PrevDataNotRead DBDAOERR(3529)
#define E_DAO_ClientAlreadyExists DBDAOERR(3530)
#define E_DAO_WaitTimeout DBDAOERR(3531)
#define E_DAO_ServerInitialization DBDAOERR(3532)
#define E_DAO_ObjectAbandoned DBDAOERR(3533)
#define E_DAO_NoEvent DBDAOERR(3534)
#define E_DAO_ClientSentTerm DBDAOERR(3535)
#define E_DAO_InvalidMessage DBDAOERR(3536)
#define E_DAO_NoClient DBDAOERR(3537)
#define E_DAO_TooManyTasks DBDAOERR(3538)
#define E_DAO_SysDiskIO DBDAOERR(3539)
#define E_DAO_PageFile DBDAOERR(3540)
#define E_DAO_ProcessStillActive DBDAOERR(3541)
#define E_DAO_ProcessAborted DBDAOERR(3542)
#define E_DAO_TransporterNotSetup DBDAOERR(3543)
#define E_DAO_ServerAlreadyRunning DBDAOERR(3544)
#define E_DAO_DiffReplicaSet DBDAOERR(3545)
#define E_DAO_BadExchangeType DBDAOERR(3546)
#define E_DAO_NoReplica DBDAOERR(3547)
#define E_DAO_GuidMismatch DBDAOERR(3548)
#define E_DAO_FilenameTooLong DBDAOERR(3549)
#define E_DAO_NoGuidIndex DBDAOERR(3550)
#define E_DAO_UnableToDeleteParam DBDAOERR(3551)
#define E_DAO_ValueTooBig DBDAOERR(3552)
#define E_DAO_REPGuidCreateFailure DBDAOERR(3553)
#define E_DAO_REPDBMovedCopied DBDAOERR(3554)
#define E_DAO_REPNoValidNick DBDAOERR(3555)
#define E_DAO_TransportDestInvalidPath DBDAOERR(3556)
#define E_DAO_TransDestInvalidAddress DBDAOERR(3557)
#define E_DAO_TransportDestDiskIO DBDAOERR(3558)
#define E_DAO_TransportDestDiskFull DBDAOERR(3559)
#define E_DAO_REPSameReplicaID DBDAOERR(3560)
#define E_DAO_REPBothMasters DBDAOERR(3561)
#define E_DAO_TransDestAccessDenied DBDAOERR(3562)
#define E_DAO_TransportSrcAccess DBDAOERR(3563)
#define E_DAO_TransportSrcFileNotFound DBDAOERR(3564)
#define E_DAO_TransSrcSharingViolation DBDAOERR(3565)
#define E_DAO_NetworkIO DBDAOERR(3566)
#define E_DAO_TransportWrongMessage DBDAOERR(3567)
#define E_DAO_TransportDeleteFailure DBDAOERR(3568)
#define E_DAO_RepRemoved DBDAOERR(3569)
#define E_DAO_FiltersChanged DBDAOERR(3570)
#define E_DAO_LimitedUpdate DBDAOERR(3571)
#define E_DAO_TempDiskIO DBDAOERR(3572)
#define E_DAO_DirNotManaged DBDAOERR(3573)
#define E_DAO_RepidChanged DBDAOERR(3574)
#define E_DAO_DiskFullM DBDAOERR(3575)
#define E_DAO_ShareViolationM DBDAOERR(3576)
#define E_DAO_UpdateReplCol DBDAOERR(3577)
#define E_DAO_GetDbinfoM DBDAOERR(3578)
#define E_DAO_MakeRepTablesM DBDAOERR(3579)
#define E_DAO_AddReplicaInfoM DBDAOERR(3580)
#define E_DAO_OpenRepTablesM DBDAOERR(3581)
#define E_DAO_CreateReplicaObjectM DBDAOERR(3582)
#define E_DAO_MakeObjectReplM DBDAOERR(3583)
#define E_DAO_OutOfMemoryM DBDAOERR(3584)
#define E_DAO_RepTooManyColumnsM DBDAOERR(3585)
#define E_DAO_PARTFilterExprSyntaxM DBDAOERR(3586)
#define E_DAO_PARTUnknownTokenM DBDAOERR(3587)
#define E_DAO_PARTExprEvaluationM DBDAOERR(3588)
#define E_DAO_PARTExprUnknownFuncM DBDAOERR(3589)
#define E_DAO_LimitedUpdateM DBDAOERR(3590)
#define E_DAO_LogInvalidPathM DBDAOERR(3591)
#define E_DAO_REPPasswdNotAllowed DBDAOERR(3592)
#define E_DAO_BadSingleMasterAttrib DBDAOERR(3593)
#define E_DAO_BadMultiMasterAttrib DBDAOERR(3594)
#define E_DAO_REPCantRepair DBDAOERR(3595)
#define E_DAO_NoDataIncluded DBDAOERR(3596)
#define E_DAO_SenderNotFound DBDAOERR(3597)
#define E_DAO_CouldnotFindService DBDAOERR(3598)
#define E_DAO_UnableToStartService DBDAOERR(3599)
#define E_DAO_ExprAggIllegalOnGuid DBDAOERR(3600)
#define E_DAO_RefreshReplicaList DBDAOERR(3601)
#define E_DAO_MoreWorkNeeded DBDAOERR(3602)
#define E_DAO_SenderTooOld DBDAOERR(3603)
#define E_DAO_RepAccess DBDAOERR(3604)
#define E_DAO_REPDbNotReplicableM DBDAOERR(3605)
#define E_DAO_DaemonDied DBDAOERR(3606)
#define E_DAO_REPCantDelete DBDAOERR(3607)
#define E_DAO_IndexCantBuild DBDAOERR(3608)
#define E_DAO_RelNoPrimaryIndex DBDAOERR(3609)
#define E_DAO_QuerySameSrcDestTableM DBDAOERR(3610)
#define E_DAO_InvalidDDLObject DBDAOERR(3611)
#define E_DAO_QueryMGBWithSubquery DBDAOERR(3612)
#define E_DAO_SQLLinkNotSupported DBDAOERR(3613)
#define E_DAO_InvalidFindOnGUID DBDAOERR(3614)
#define E_DAO_QueryJoinExprInComp DBDAOERR(3615)
#define E_DAO_UpdateNotAvailable DBDAOERR(3616)
#define E_DAO_DeleteNotAvailable DBDAOERR(3617)
#define E_DAO_ExceptTableCreateFail DBDAOERR(3618)
#define E_DAO_ExceptTableWriteFail DBDAOERR(3619)
#define E_DAO_ExcelOLEConnectLost DBDAOERR(3620)
#define E_DAO_CantChangeDbPwdOnShared DBDAOERR(3621)
#define E_DAO_RmtMustCheckTimeStamp DBDAOERR(3622)
#define E_DAO_NotWithBoundFileM DBDAOERR(3623)
#define E_DAO_CommitConflict DBDAOERR(3624)
#define E_DAO_NoSuchSpecM DBDAOERR(3625)
#define E_DAO_TooManyIndexesM DBDAOERR(3626)
#define E_DAO_TransExeNotFound DBDAOERR(3627)
#define E_DAO_RemRepNotManaged DBDAOERR(3628)
#define E_DAO_FSDropboxShared DBDAOERR(3629)
#define E_DAO_FSDropboxSharedM DBDAOERR(3630)
#define E_DAO_RepInvalidTableInFilter DBDAOERR(3631)
#define E_DAO_InetNotEnabled DBDAOERR(3632)
#define E_DAO_VtoDllLoadFailed DBDAOERR(3633)
#define E_DAO_REPDBIsPartial DBDAOERR(3634)
#define E_DAO_CantCreatePartialSys DBDAOERR(3635)
#define E_DAO_CantPopulateWithErrors DBDAOERR(3636)
#define E_DAO_QueryEmbeddedVarTab DBDAOERR(3637)
#define E_DAO_SrcCntrlDB DBDAOERR(3638)
#define E_DAO_CantCreateSysReplica DBDAOERR(3639)
#define E_DAO_VtoFetchBuffTooSmall DBDAOERR(3640)
#define E_DAO_VtoEOFDuringFetch DBDAOERR(3641)
#define E_DAO_VtoSilentCancel DBDAOERR(3642)
#define E_DAO_VtoRecordDeleted DBDAOERR(3643)
#define E_DAO_3644 DBDAOERR(3644)
#define E_DAO_VtoBadBindInfo DBDAOERR(3645)
#define E_DAO_VtoRowLenTooSmall DBDAOERR(3646)
#define E_DAO_VtoColumnMissing DBDAOERR(3647)
#define E_DAO_BothPartials DBDAOERR(3648)
#define E_DAO_InvalidCodePage DBDAOERR(3649)
#define E_DAO_InetTooSlow DBDAOERR(3650)
#define E_DAO_InetInvalidAddress DBDAOERR(3651)
#define E_DAO_InetLoginFailure DBDAOERR(3652)
#define E_DAO_InetNotSetup DBDAOERR(3653)
#define E_DAO_InetInternalFailure DBDAOERR(3654)
#define E_DAO_InetServicesUnavailable DBDAOERR(3655)
#define E_DAO_PARTExprEvaluation DBDAOERR(3656)
#define E_DAO_PARTFilterEvalM DBDAOERR(3657)
#define E_DAO_PARTBinaryNotAllowedM DBDAOERR(3658)
#define E_DAO_PARTUnenforcedRelM DBDAOERR(3659)
#define E_DAO_ExchangeFailed DBDAOERR(3660)
#define E_DAO_ExchangeFailedM DBDAOERR(3661)
#define E_DAO_VtoRSNeedsBatchCursorLib DBDAOERR(3662)
#define E_DAO_VtoNeedDiffCursorLibrary DBDAOERR(3663)
#define E_DAO_VtoStillConnecting DBDAOERR(3664)
#define E_DAO_AccessDeniedRepl DBDAOERR(3665)
#define E_DAO_AccessDeniedReplM DBDAOERR(3666)
#define E_DAO_VtoOtherOperBlocking DBDAOERR(3667)
#define E_DAO_VtoNoActiveConnection DBDAOERR(3668)
#define E_DAO_VtoExecCancelled DBDAOERR(3669)
#define E_DAO_VtoCursorNotValid DBDAOERR(3670)
#define E_DAO_VtoCanNotFindTable DBDAOERR(3671)
#define E_DAO_VtoCanNotFindCursLib DBDAOERR(3672)
#endif
