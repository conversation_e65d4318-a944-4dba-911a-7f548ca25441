;
; Definition file of WINHTTP.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "WINHTTP.dll"
EXPORTS
WinHttpPacJsWorkerMain
DllCanUnloadNow
DllGetClassObject
Private1
SvchostPushServiceGlobals
WinHttpAddRequestHeaders
WinHttpAddRequestHeadersEx
WinHttpAutoProxySvcMain
WinHttpCheckPlatform
WinHttpCloseHandle
WinHttpConnect
WinHttpConnectionDeletePolicyEntries
WinHttpConnectionDeleteProxyInfo
WinHttpConnectionFreeNameList
WinHttpConnectionFreeProxyInfo
WinHttpConnectionFreeProxyList
WinHttpConnectionGetNameList
WinHttpConnectionGetProxyInfo
WinHttpConnectionGetProxyList
WinHttpConnectionSetPolicyEntries
WinHttpConnectionSetProxyInfo
WinHttpConnectionU<PERSON>dateIfIndexTable
WinHttpCrackUrl
WinHttpCreateProxyResolver
WinHttpCreateUrl
WinHttpDetectAutoProxyConfigUrl
WinHttpFreeProxyResult
WinHttpFreeProxyResultEx
WinHttpFreeProxySettings
WinHttpGetDefaultProxyConfiguration
WinHttpGetIEProxyConfigForCurrentUser
WinHttpGetProxyForUrl
WinHttpGetProxyForUrlEx
WinHttpGetProxyForUrlEx2
WinHttpGetProxyForUrlHvsi
WinHttpGetProxyResult
WinHttpGetProxyResultEx
WinHttpGetProxySettingsVersion
WinHttpGetTunnelSocket
WinHttpOpen
WinHttpOpenRequest
WinHttpPalAcquireNextInterface
WinHttpPalAcquireNextInterfaceAsync
WinHttpPalCancelRequest
WinHttpPalCreateCmSessionReference
WinHttpPalCreateRequestCtx
WinHttpPalDllInit
WinHttpPalDllUnload
WinHttpPalFreeProxyInfo
WinHttpPalFreeRequestCtx
WinHttpPalGetProxyCreds
WinHttpPalGetProxyForCurrentInterface
WinHttpPalIsImplemented
WinHttpPalOnSendRequestComplete
WinHttpProbeConnectivity
WinHttpQueryAuthSchemes
WinHttpQueryDataAvailable
WinHttpQueryHeaders
WinHttpQueryOption
WinHttpReadData
WinHttpReadProxySettings
WinHttpReadProxySettingsHvsi
WinHttpReceiveResponse
WinHttpResetAutoProxy
WinHttpSaveProxyCredentials
WinHttpSendRequest
WinHttpSetCredentials
WinHttpSetDefaultProxyConfiguration
WinHttpSetOption
WinHttpSetProxySettingsPerUser
WinHttpSetStatusCallback
WinHttpSetTimeouts
WinHttpTimeFromSystemTime
WinHttpTimeToSystemTime
WinHttpWebSocketClose
WinHttpWebSocketCompleteUpgrade
WinHttpWebSocketQueryCloseStatus
WinHttpWebSocketReceive
WinHttpWebSocketSend
WinHttpWebSocketShutdown
WinHttpWriteData
WinHttpWriteProxySettings
