;
; Definition file of D3DCOMPILER_47.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "D3DCOMPILER_47.dll"
EXPORTS
D3DAssemble
DebugSetMute
D3DCompile
D3DCompile2
D3DCompileFromFile
D3DCompressShaders
D3DCreateBlob
D3DCreateFunctionLinkingGraph
D3DCreateLinker
D3DDecompressShaders
D3DDisassemble
D3DDisassemble10Effect
D3DDisassemble11Trace
D3DDisassembleRegion
D3DGetBlobPart
D3DGetDebugInfo
D3DGetInputAndOutputSignatureBlob
D3DGetInputSignatureBlob
D3DGetOutputSignatureBlob
D3DGetTraceInstructionOffsets
D3DLoadModule
D3DPreprocess
D3DReadFileToBlob
D3DReflect
D3DReflectLibrary
D3DReturnFailure1
D3DSetBlobPart
D3DStripShader
D3DWriteB<PERSON>bToFile
