;
; Definition file of pdh.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "pdh.dll"
EXPORTS
PdhAddCounterA
PdhAddCounterW
PdhAddEnglishCounterA
PdhAddEnglishCounterW
PdhAddRelogCounter
PdhBindInputDataSourceA
PdhBindInputDataSourceW
PdhBrowseCountersA
PdhBrowseCountersHA
PdhBrowseCountersHW
PdhBrowseCountersW
PdhCalculateCounterFromRawValue
PdhCloseLog
PdhCloseQuery
PdhCollectQueryData
PdhCollectQueryDataEx
PdhCollectQueryDataWithTime
PdhComputeCounterStatistics
PdhConnectMachineA
PdhConnectMachineW
PdhCreateSQLTablesA
PdhCreateSQLTablesW
PdhEnumLogSetNamesA
PdhEnumLogSetNamesW
PdhEnumMachinesA
PdhEnumMachinesHA
PdhEnumMachinesH<PERSON>achinesW
PdhEnumObjectItemsA
PdhEnumObjectItemsHA
PdhEnumObjectItemsHW
PdhEnumObjectItemsW
PdhEnumObjectsA
PdhEnumObjectsHA
PdhEnumObjectsHW
PdhEnumObjectsW
PdhExpandCounterPathA
PdhExpandCounterPathW
PdhExpandWildCardPathA
PdhExpandWildCardPathHA
PdhExpandWildCardPathHW
PdhExpandWildCardPathW
PdhFormatFromRawValue
PdhGetCounterInfoA
PdhGetCounterInfoW
PdhGetCounterTimeBase
PdhGetDataSourceTimeRangeA
PdhGetDataSourceTimeRangeH
PdhGetDataSourceTimeRangeW
PdhGetDefaultPerfCounterA
PdhGetDefaultPerfCounterHA
PdhGetDefaultPerfCounterHW
PdhGetDefaultPerfCounterW
PdhGetDefaultPerfObjectA
PdhGetDefaultPerfObjectHA
PdhGetDefaultPerfObjectHW
PdhGetDefaultPerfObjectW
PdhGetDllVersion
PdhGetExplainText
PdhGetFormattedCounterArrayA
PdhGetFormattedCounterArrayW
PdhGetFormattedCounterValue
PdhGetLogFileSize
PdhGetLogFileTypeW
PdhGetLogSetGUID
PdhGetRawCounterArrayA
PdhGetRawCounterArrayW
PdhGetRawCounterValue
PdhIsRealTimeQuery
PdhLookupPerfIndexByNameA
PdhLookupPerfIndexByNameW
PdhLookupPerfNameByIndexA
PdhLookupPerfNameByIndexW
PdhMakeCounterPathA
PdhMakeCounterPathW
PdhOpenLogA
PdhOpenLogW
PdhOpenQuery
PdhOpenQueryA
PdhOpenQueryH
PdhOpenQueryW
PdhParseCounterPathA
PdhParseCounterPathW
PdhParseInstanceNameA
PdhParseInstanceNameW
PdhReadRawLogRecord
PdhRelogW
PdhRemoveCounter
PdhResetRelogCounterValues
PdhSelectDataSourceA
PdhSelectDataSourceW
PdhSetCounterScaleFactor
PdhSetCounterValue
PdhSetDefaultRealTimeDataSource
PdhSetLogSetRunID
PdhSetQueryTimeRange
PdhTranslate009CounterW
PdhTranslateLocaleCounterW
PdhUpdateLogA
PdhUpdateLogFileCatalog
PdhUpdateLogW
PdhValidatePathA
PdhValidatePathExA
PdhValidatePathExW
PdhValidatePathW
PdhVbAddCounter
PdhVbCreateCounterPathList
PdhVbGetCounterPathElements
PdhVbGetCounterPathFromList
PdhVbGetDoubleCounterValue
PdhVbGetLogFileSize
PdhVbGetOneCounterPath
PdhVbIsGoodStatus
PdhVbOpenLog
PdhVbOpenQuery
PdhVbUpdateLog
PdhVerifySQLDBA
PdhVerifySQLDBW
PdhWriteRelogSample
