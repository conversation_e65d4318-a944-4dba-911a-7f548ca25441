;
; Definition file of SHLWAPI.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "SHLWAPI.dll"
EXPORTS
ParseURLA
ParseURLW
SHAllocShared
SHLockShared
SHUnlockShared
SHFreeShared
SHCreateMemStream
GetAcceptLanguagesA
GetAcceptLanguagesW
SHCreateThread
IsCharSpaceW
StrCmpNCA
StrCmpNCW
StrCmpNICA
StrCmpNICW
StrCmpCA
StrCmpCW
StrCmpICA
StrCmpICW
IUnknown_QueryStatus
IUnknown_Exec
ConnectToConnectionPoint
IUnknown_AtomicRelease
IUnknown_GetWindow
IUnknown_SetSite
IUnknown_QueryService
IStream_Read
SHMessageBoxCheckA
SHMessageBoxCheckW
IUnknown_Set
SHStripMneumonicA
SHIsChildOrSelf
IStream_Write
IStream_Reset
IStream_Size
SHAnsiToUnicode
SHUnicodeT<PERSON><PERSON><PERSON>
SHUnicodeToAnsiCP
QISearch
SHStripMneumonicW
SHPinDllOfCLSID
IUnknown_GetSite
GUIDFromStringW
WhichPlatform
SHCreateWorkerWindowW
SHRegGetIntW
SHPackDispParamsV
SHAnsiToAnsi
SHUnicodeToUnicode
SHFormatDateTimeA
SHFormatDateTimeW
MLLoadLibraryA
MLLoadLibraryW
ShellMessageBoxW
MLFreeLibrary
SHSendMessageBroadcastA
SHSendMessageBroadcastW
IsOS
PathFileExistsAndAttributesW
UrlFixupW
SHRunIndirectRegClientCommand
SHLoadIndirectString
IStream_ReadPidl
IStream_WritePidl
SHGetViewStatePropertyBag
IsInternetESCEnabled
SHPropertyBag_ReadStrAlloc
IStream_Copy
DelayLoadFailureHook
SHPropertyBag_WriteBSTR
AssocCreate
AssocGetPerceivedType
AssocIsDangerous
AssocQueryKeyA
AssocQueryKeyW
AssocQueryStringA
AssocQueryStringByKeyA
AssocQueryStringByKeyW
AssocQueryStringW
ChrCmpIA
ChrCmpIW
ColorAdjustLuma
ColorHLSToRGB
IStream_ReadStr
IStream_WriteStr
ColorRGBToHLS
DllGetVersion
GetMenuPosFromID
HashData
SHCreateThreadWithHandle
IntlStrEqWorkerA
IntlStrEqWorkerW
IsCharSpaceA
PathAddBackslashA
PathAddBackslashW
SHRegGetValueFromHKCUHKLM
SHRegGetBoolValueFromHKCUHKLM
PathAddExtensionA
PathAddExtensionW
PathAppendA
PathAppendW
PathBuildRootA
PathBuildRootW
PathCanonicalizeA
PathCanonicalizeW
PathCombineA
PathCombineW
PathCommonPrefixA
PathCommonPrefixW
PathCompactPathA
PathCompactPathExA
PathCompactPathExW
PathCompactPathW
PathCreateFromUrlA
PathCreateFromUrlAlloc
PathCreateFromUrlW
PathFileExistsA
PathFileExistsW
PathFindExtensionA
PathFindExtensionW
PathFindFileNameA
PathFindFileNameW
PathFindNextComponentA
PathFindNextComponentW
PathFindOnPathA
PathFindOnPathW
PathFindSuffixArrayA
PathFindSuffixArrayW
PathGetArgsA
PathGetArgsW
PathGetCharTypeA
PathGetCharTypeW
PathGetDriveNumberA
PathGetDriveNumberW
PathIsContentTypeA
PathIsContentTypeW
PathIsDirectoryA
PathIsDirectoryEmptyA
PathIsDirectoryEmptyW
PathIsDirectoryW
PathIsFileSpecA
PathIsFileSpecW
PathIsLFNFileSpecA
PathIsLFNFileSpecW
PathIsNetworkPathA
PathIsNetworkPathW
PathIsPrefixA
PathIsPrefixW
PathIsRelativeA
PathIsRelativeW
PathIsRootA
PathIsRootW
PathIsSameRootA
PathIsSameRootW
PathIsSystemFolderA
PathIsSystemFolderW
PathIsUNCA
PathIsUNCServerA
PathIsUNCServerShareA
PathIsUNCServerShareW
PathIsUNCServerW
PathIsUNCW
PathIsURLA
PathIsURLW
PathMakePrettyA
PathMakePrettyW
PathMakeSystemFolderA
PathMakeSystemFolderW
PathMatchSpecA
PathMatchSpecExA
PathMatchSpecExW
PathMatchSpecW
PathParseIconLocationA
PathParseIconLocationW
PathQuoteSpacesA
PathQuoteSpacesW
PathRelativePathToA
PathRelativePathToW
PathRemoveArgsA
PathRemoveArgsW
PathRemoveBackslashA
PathRemoveBackslashW
PathRemoveBlanksA
PathRemoveBlanksW
PathRemoveExtensionA
PathRemoveExtensionW
PathRemoveFileSpecA
PathRemoveFileSpecW
PathRenameExtensionA
PathRenameExtensionW
PathSearchAndQualifyA
PathSearchAndQualifyW
PathSetDlgItemPathA
PathSetDlgItemPathW
PathSkipRootA
PathSkipRootW
PathStripPathA
PathStripPathW
PathStripToRootA
PathStripToRootW
PathUnExpandEnvStringsA
PathUnExpandEnvStringsW
PathUndecorateA
PathUndecorateW
PathUnmakeSystemFolderA
PathUnmakeSystemFolderW
PathUnquoteSpacesA
PathUnquoteSpacesW
SHAutoComplete
SHCopyKeyA
SHCopyKeyW
SHCreateShellPalette
SHCreateStreamOnFileA
SHCreateStreamOnFileEx
SHCreateStreamOnFileW
SHCreateStreamWrapper
SHCreateThreadRef
SHDeleteEmptyKeyA
SHDeleteEmptyKeyW
SHDeleteKeyA
SHDeleteKeyW
SHDeleteOrphanKeyA
SHDeleteOrphanKeyW
SHDeleteValueA
SHDeleteValueW
SHEnumKeyExA
SHEnumKeyExW
SHEnumValueA
SHEnumValueW
SHGetInverseCMAP
SHGetThreadRef
SHGetValueA
SHGetValueW
SHIsLowMemoryMachine
SHOpenRegStream2A
SHOpenRegStream2W
SHOpenRegStreamA
SHOpenRegStreamW
SHQueryInfoKeyA
SHQueryInfoKeyW
SHQueryValueExA
SHQueryValueExW
SHRegCloseUSKey
SHRegCreateUSKeyA
SHRegCreateUSKeyW
SHRegDeleteEmptyUSKeyA
SHRegDeleteEmptyUSKeyW
SHRegDeleteUSValueA
SHRegDeleteUSValueW
SHRegDuplicateHKey
SHRegEnumUSKeyA
SHRegEnumUSKeyW
SHRegEnumUSValueA
SHRegEnumUSValueW
SHRegGetBoolUSValueA
SHRegGetBoolUSValueW
SHRegGetPathA
SHRegGetPathW
SHRegGetUSValueA
SHRegGetUSValueW
SHRegGetValueA
SHRegGetValueW
SHRegOpenUSKeyA
SHRegOpenUSKeyW
SHRegQueryInfoUSKeyA
SHRegQueryInfoUSKeyW
SHRegQueryUSValueA
SHRegQueryUSValueW
SHRegSetPathA
SHRegSetPathW
SHRegSetUSValueA
SHRegSetUSValueW
SHRegWriteUSValueA
SHRegWriteUSValueW
SHRegisterValidateTemplate
SHReleaseThreadRef
SHSetThreadRef
SHSetValueA
SHSetValueW
SHSkipJunction
SHStrDupA
SHStrDupW
ShellMessageBoxA
StrCSpnA
StrCSpnIA
StrCSpnIW
StrCSpnW
StrCatBuffA
StrCatBuffW
StrCatChainW
StrCatW
StrChrA
StrChrIA
StrChrIW
StrChrNIW
StrChrNW
StrChrW
StrCmpIW
StrCmpLogicalW
StrCmpNA
StrCmpNIA
StrCmpNIW
StrCmpNW
StrCmpW
StrCpyNW
StrCpyW
StrDupA
StrDupW
StrFormatByteSize64A
StrFormatByteSizeA
StrFormatByteSizeEx
StrFormatByteSizeW
StrFormatKBSizeA
StrFormatKBSizeW
StrFromTimeIntervalA
StrFromTimeIntervalW
StrIsIntlEqualA
StrIsIntlEqualW
StrNCatA
StrNCatW
StrPBrkA
StrPBrkW
StrRChrA
StrRChrIA
StrRChrIW
StrRChrW
StrRStrIA
StrRStrIW
StrRetToBSTR
StrRetToBufA
StrRetToBufW
StrRetToStrA
StrRetToStrW
StrSpnA
StrSpnW
StrStrA
StrStrIA
StrStrIW
StrStrNIW
StrStrNW
StrStrW
StrToInt64ExA
StrToInt64ExW
StrToIntA
StrToIntExA
StrToIntExW
StrToIntW
StrTrimA
StrTrimW
UrlApplySchemeA
UrlApplySchemeW
UrlCanonicalizeA
UrlCanonicalizeW
UrlCombineA
UrlCombineW
UrlCompareA
UrlCompareW
UrlCreateFromPathA
UrlCreateFromPathW
UrlEscapeA
UrlEscapeW
UrlGetLocationA
UrlGetLocationW
UrlGetPartA
UrlGetPartW
UrlHashA
UrlHashW
UrlIsA
UrlIsNoHistoryA
UrlIsNoHistoryW
UrlIsOpaqueA
UrlIsOpaqueW
UrlIsW
UrlUnescapeA
UrlUnescapeW
wnsprintfA
wnsprintfW
wvnsprintfA
wvnsprintfW
