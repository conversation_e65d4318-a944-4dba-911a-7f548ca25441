; 
; Exports of file HID.DLL
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY HID.DLL
EXPORTS
HidD_FlushQueue
HidD_FreePreparsedData
HidD_GetAttributes
HidD_GetConfiguration
HidD_GetFeature
HidD_GetHidGuid
HidD_GetIndexedString
HidD_GetInputReport
HidD_GetManufacturerString
HidD_GetMsGenreDescriptor
HidD_GetNumInputBuffers
HidD_GetPhysicalDescriptor
HidD_GetPreparsedData
HidD_GetProductString
HidD_GetSerialNumberString
HidD_Hello
HidD_SetConfiguration
HidD_SetFeature
HidD_SetNumInputBuffers
HidD_SetOutputReport
HidP_GetButtonCaps
HidP_GetCaps
HidP_GetData
HidP_GetExtendedAttributes
HidP_GetLinkCollectionNodes
HidP_GetScaledUsageValue
HidP_GetSpecificButtonCaps
HidP_GetSpecificValueCaps
HidP_GetUsageValue
HidP_GetUsageValueArray
HidP_GetUsages
HidP_GetUsagesEx
HidP_GetValueCaps
HidP_InitializeReportForID
HidP_MaxDataListLength
HidP_MaxUsageListLength
HidP_SetData
HidP_SetScaledUsageValue
HidP_SetUsageValue
HidP_SetUsageValueArray
HidP_SetUsages
HidP_TranslateUsagesToI8042ScanCodes
HidP_UnsetUsages
HidP_UsageListDifference
