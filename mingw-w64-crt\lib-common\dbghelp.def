;
; Definition file of dbghelp.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "dbghelp.dll"
EXPORTS
SymGetOmapBlockBase
DbgHelpCreateUserDump
DbgHelpCreateUserDumpW
EnumDirTree
EnumDirTreeW
EnumerateLoadedModules
EnumerateLoadedModules64
EnumerateLoadedModulesEx
EnumerateLoadedModulesExW
EnumerateLoadedModulesW64
ExtensionApiVersion
FindDebugInfoFile
FindDebugInfoFileEx
FindDebugInfoFileExW
FindExecutableImage
FindExecutableImageEx
FindExecutableImageExW
FindFileInPath
FindFileInSearchPath
GetSymLoadError
GetTimestampForLoadedLibrary
ImageDirectoryEntryToData
ImageDirectoryEntryToDataEx
ImageNtHeader
ImageRvaToSection
ImageRvaToVa
ImagehlpApiVersion
ImagehlpApiVersionEx
MakeSureDirectoryPathExists
MapDebugInformation
MiniDumpReadDumpStream
MiniDumpWriteDump
RangeMapAddPeImageSections
RangeMapCreate
RangeMapFree
RangeMapRead
RangeMapRemove
RangeMapWrite
RemoveInvalidModuleList
ReportSymbolLoadSummary
SearchTreeForFile
SearchTreeForFileW
SetCheckUserInterruptShared
SetSymLoadError
StackWalk
StackWalk64
StackWalkEx
SymAddSourceStream
SymAddSourceStreamA
SymAddSourceStreamW
SymAddSymbol
SymAddSymbolW
SymAddrIncludeInlineTrace
SymCleanup
SymCompareInlineTrace
SymDeleteSymbol
SymDeleteSymbolW
SymEnumLines
SymEnumLinesW
SymEnumProcesses
SymEnumSourceFileTokens
SymEnumSourceFiles
SymEnumSourceFilesW
SymEnumSourceLines
SymEnumSourceLinesW
SymEnumSym
SymEnumSymbols
SymEnumSymbolsEx
SymEnumSymbolsExW
SymEnumSymbolsForAddr
SymEnumSymbolsForAddrW
SymEnumSymbolsW
SymEnumTypes
SymEnumTypesByName
SymEnumTypesByNameW
SymEnumTypesW
SymEnumerateModules
SymEnumerateModules64
SymEnumerateModulesW64
SymEnumerateSymbols
SymEnumerateSymbols64
SymEnumerateSymbolsW
SymEnumerateSymbolsW64
SymFindDebugInfoFile
SymFindDebugInfoFileW
SymFindExecutableImage
SymFindExecutableImageW
SymFindFileInPath
SymFindFileInPathW
SymFromAddr
SymFromAddrW
SymFromIndex
SymFromIndexW
SymFromInlineContext
SymFromInlineContextW
SymFromName
SymFromNameW
SymFromToken
SymFromTokenW
SymFunctionTableAccess
SymFunctionTableAccess64
SymFunctionTableAccess64AccessRoutines
SymGetFileLineOffsets64
SymGetHomeDirectory
SymGetHomeDirectoryW
SymGetLineFromAddr
SymGetLineFromAddr64
SymGetLineFromAddrW64
SymGetLineFromInlineContext
SymGetLineFromInlineContextW
SymGetLineFromName
SymGetLineFromName64
SymGetLineFromNameW64
SymGetLineNext
SymGetLineNext64
SymGetLineNextW64
SymGetLinePrev
SymGetLinePrev64
SymGetLinePrevW64
SymGetModuleBase
SymGetModuleBase64
SymGetModuleInfo
SymGetModuleInfo64
SymGetModuleInfoW
SymGetModuleInfoW64
SymGetOmaps
SymGetOptions
SymGetScope
SymGetScopeW
SymGetSearchPath
SymGetSearchPathW
SymGetSourceFile
SymGetSourceFileFromToken
SymGetSourceFileFromTokenW
SymGetSourceFileToken
SymGetSourceFileTokenW
SymGetSourceFileW
SymGetSourceVarFromToken
SymGetSourceVarFromTokenW
SymGetSymFromAddr
SymGetSymFromAddr64
SymGetSymFromName
SymGetSymFromName64
SymGetSymNext
SymGetSymNext64
SymGetSymPrev
SymGetSymPrev64
SymGetSymbolFile
SymGetSymbolFileW
SymGetTypeFromName
SymGetTypeFromNameW
SymGetTypeInfo
SymGetTypeInfoEx
SymGetUnwindInfo
SymInitialize
SymInitializeW
SymLoadModule
SymLoadModule64
SymLoadModuleEx
SymLoadModuleExW
SymMatchFileName
SymMatchFileNameW
SymMatchString
SymMatchStringA
SymMatchStringW
SymNext
SymNextW
SymPrev
SymPrevW
SymQueryInlineTrace
SymRefreshModuleList
SymRegisterCallback
SymRegisterCallback64
SymRegisterCallbackW64
SymRegisterFunctionEntryCallback
SymRegisterFunctionEntryCallback64
SymSearch
SymSearchW
SymSetContext
SymSetHomeDirectory
SymSetHomeDirectoryW
SymSetOptions
SymSetParentWindow
SymSetScopeFromAddr
SymSetScopeFromIndex
SymSetScopeFromInlineContext
SymSetSearchPath
SymSetSearchPathW
SymSrvDeltaName
SymSrvDeltaNameW
SymSrvGetFileIndexInfo
SymSrvGetFileIndexInfoW
SymSrvGetFileIndexString
SymSrvGetFileIndexStringW
SymSrvGetFileIndexes
SymSrvGetFileIndexesW
SymSrvGetSupplement
SymSrvGetSupplementW
SymSrvIsStore
SymSrvIsStoreW
SymSrvStoreFile
SymSrvStoreFileW
SymSrvStoreSupplement
SymSrvStoreSupplementW
SymUnDName
SymUnDName64
SymUnloadModule
SymUnloadModule64
UnDecorateSymbolName
UnDecorateSymbolNameW
UnmapDebugInformation
WinDbgExtensionDllInit
block
chksym
dbghelp
dh
fptr
homedir
inlinedbg
itoldyouso
lmi
lminfo
omap
optdbgdump
optdbgdumpaddr
srcfiles
stack_force_ebp
stackdbg
sym
symsrv
vc7fpo
