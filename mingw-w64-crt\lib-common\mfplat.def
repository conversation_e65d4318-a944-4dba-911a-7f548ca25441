;
; Definition file of MFPlat.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "MFPlat.DLL"
EXPORTS
FormatTagFromWfx
MFCreateGuid
MFGetIoPortHandle
MFEnumLocalMFTRegistrations
MFGetPlatformFlags
MFGetPlatformVersion
MFGetRandomNumber
MFIsFeatureEnabled
MFIsQueueThread
MFPlatformBigEndian
MFPlatformLittleEndian
MFTraceError
MFllMulDiv
ValidateWaveFormat
CopyPropVariant
CreatePropVariant
CreatePropertyStore
DestroyPropVariant
GetAMSubtypeFromD3DFormat
GetD3DFormatFromMFSubtype
LFGetGlobalPool
MFAddPeriodicCallback
MFAllocateSerialWorkQueue
MFAllocateWorkQueue
MFAllocateWorkQueueEx
MFAppendCollection
MFAverageTimePerFrameToFrameRate
MFBeginCreateFile
MFBeginGetHostByName
<PERSON>R<PERSON>isterWorkQueueWithMMCSS
MFBeginRegisterWorkQueueWithMMCSSEx
MFBeginUnregisterWorkQueueWithMMCSS
MFBlockThread
MFCalculateBitmapImageSize
MFCalculateImageSize
MFCallStackTracingClearSnapshot
MFCallStackTracingLogSessionErrors
MFCallStackTracingRestoreSnapshot
MFCallStackTracingTakeSnapshot
MFCancelCreateFile
MFCancelWorkItem
MFCheckEnabledViaAppService
MFClearLocalMFTs
MFCombineSamples
MFCompareFullToPartialMediaType
MFCompareSockaddrAddresses
MFConvertColorInfoFromDXVA
MFConvertColorInfoToDXVA
MFConvertFromFP16Array
MFConvertToFP16Array
MFCopyImage
MFCreate2DMediaBuffer
MFCreate2DMediaBufferOn1DMediaBuffer
MFCreateAMMediaTypeFromMFMediaType
MFCreateAlignedMemoryBuffer
MFCreateAlignedSharedMemoryBuffer
MFCreateAsyncResult
MFCreateAttributes
MFCreateAudioMediaType
MFCreateByteStreamHandlerAppServiceActivate
MFCreateCollection
MFCreateContentDecryptorContext
MFCreateContentProtectionDevice
MFCreateD3D12SynchronizationObject
MFCreateDXGIDeviceManager
MFCreateDXGISurfaceBuffer
MFCreateDXSurfaceBuffer
MFCreateEMEStoreObject
MFCreateEventQueue
MFCreateFile
MFCreateFileFromHandle
MFCreateLegacyMediaBufferOnMFMediaBuffer
MFCreateMFByteStreamOnIStreamWithFlags
MFCreateMFByteStreamOnStream
MFCreateMFByteStreamOnStreamEx
MFCreateMFByteStreamWrapper
MFCreateMFVideoFormatFromMFMediaType
MFCreateMediaBufferFromMediaType
MFCreateMediaBufferWrapper
MFCreateMediaEvent
MFCreateMediaEventResult
MFCreateMediaExtensionActivate
MFCreateMediaExtensionActivateNoInit
MFCreateMediaExtensionAppServiceActivate
MFCreateMediaExtensionInprocActivate
MFCreateMediaType
MFCreateMediaTypeFromProperties
MFCreateMediaTypeFromRepresentation
MFCreateMemoryBuffer
MFCreateMemoryBufferFromRawBuffer
MFCreateMemoryStream
MFCreateMuxStreamAttributes
MFCreateMuxStreamMediaType
MFCreateMuxStreamSample
MFCreateOOPMFTProxy
MFCreateOOPMFTRemote
MFCreatePathFromURL
MFCreatePresentationDescriptor
MFCreatePropertiesFromMediaType
MFCreateReusableByteStream
MFCreateReusableByteStreamWithSharedLock
MFCreateSample
MFCreateSecureBufferAllocator
MFCreateSharedMemoryMediaBufferFromMediaType
MFCreateSocket
MFCreateSocketListener
MFCreateSourceResolver
MFCreateSourceResolverInternal
MFCreateStagingSurfaceWrapper
MFCreateStreamDescriptor
MFCreateStreamOnMFByteStream
MFCreateStreamOnMFByteStreamEx
MFCreateSystemTimeSource
MFCreateSystemUnderlyingClock
MFCreateTelemetrySession
MFCreateTempFile
MFCreateTrackedSample
MFCreateTransformActivate
MFCreateURLFromPath
MFCreateUdpSockets
MFCreateVideoDecryptorContext
MFCreateVideoMediaType
MFCreateVideoMediaTypeFromBitMapInfoHeader
MFCreateVideoMediaTypeFromBitMapInfoHeaderEx
MFCreateVideoMediaTypeFromSubtype
MFCreateVideoMediaTypeFromVideoInfoHeader
MFCreateVideoMediaTypeFromVideoInfoHeader2
MFCreateVideoSampleAllocatorEx
MFCreateWICBitmapBuffer
MFCreateWICDecoderProxy
MFCreateWaveFormatExFromMFMediaType
MFDeserializeAttributesFromStream
MFDeserializeEvent
MFDeserializeMediaTypeFromStream
MFDeserializePresentationDescriptor
MFEndCreateFile
MFEndGetHostByName
MFEndRegisterWorkQueueWithMMCSS
MFEndUnregisterWorkQueueWithMMCSS
MFFrameRateToAverageTimePerFrame
MFFreeAdaptersAddresses
MFGetAdaptersAddresses
MFGetAttributesAsBlob
MFGetAttributesAsBlobSize
MFGetCallStackTracingWeakReference
MFGetConfigurationDWORD
MFGetConfigurationPolicy
MFGetConfigurationStore
MFGetConfigurationString
MFGetContentProtectionSystemCLSID
MFGetMFTMerit
MFGetNumericNameFromSockaddr
MFGetPlaneSize
MFGetPlatform
MFGetPluginControl
MFGetPrivateWorkqueues
MFGetSockaddrFromNumericName
MFGetStrideForBitmapInfoHeader
MFGetSupportedMimeTypes
MFGetSupportedSchemes
MFGetSystemTime
MFGetTimerPeriodicity
MFGetUncompressedVideoFormat
MFGetWorkQueueMMCSSClass
MFGetWorkQueueMMCSSPriority
MFGetWorkQueueMMCSSTaskId
MFHasLocallyRegisteredByteStreamHandlers
MFHasLocallyRegisteredSchemeHandlers
MFHeapAlloc
MFHeapFree
MFInitAMMediaTypeFromMFMediaType
MFInitAttributesFromBlob
MFInitMediaTypeFromAMMediaType
MFInitMediaTypeFromMFVideoFormat
MFInitMediaTypeFromMPEG1VideoInfo
MFInitMediaTypeFromMPEG2VideoInfo
MFInitMediaTypeFromVideoInfoHeader
MFInitMediaTypeFromVideoInfoHeader2
MFInitMediaTypeFromWaveFormatEx
MFInitVideoFormat
MFInitVideoFormat_RGB
MFInvalidateMFTEnumCache
MFInvokeCallback
MFJoinIoPort
MFIsBottomUpFormat
MFIsContentProtectionDeviceSupported
MFIsLocallyRegisteredMimeType
MFIsLocallyRegisteredSchemeHandler
MFJoinWorkQueue
MFLockDXGIDeviceManager
MFLockPlatform
MFLockSharedWorkQueue
MFLockWorkQueue
MFMapDX9FormatToDXGIFormat
MFMapDXGIFormatToDX9Format
MFPutWaitingWorkItem
MFPutWorkItem
MFPutWorkItem2
MFPutWorkItemEx
MFPutWorkItemEx2
MFRecordError
MFRegisterLocalByteStreamHandler
MFRegisterLocalSchemeHandler
MFRegisterPlatformWithMMCSS
MFRemovePeriodicCallback
MFScheduleWorkItem
MFScheduleWorkItemEx
MFSerializeAttributesToStream
MFSerializeEvent
MFSerializeMediaTypeToStream
MFSerializePresentationDescriptor
MFSetMinimumMemoryAlignment
MFSetSockaddrAny
MFSetWindowForContentProtection
MFShutdown
MFSplitSample
MFStartup
MFStreamDescriptorProtectMediaType
MFTEnum
MFTEnum2
MFTEnumEx
MFTGetInfo
MFTRegister
MFTRegisterLocal
MFTRegisterLocalByCLSID
MFTUnregister
MFTUnregisterLocal
MFTUnregisterLocalByCLSID
MFTraceFuncEnter
MFUnblockThread
MFUnjoinWorkQueue
MFUnlockDXGIDeviceManager
MFUnlockPlatform
MFUnlockWorkQueue
MFUnregisterPlatformFromMMCSS
MFUnwrapMediaType
MFValidateMediaTypeSize
MFWrapMediaType
MFWrapSocket
PropVariantFromStream
PropVariantToStream
