;
; Definition file of IPHLPAPI.DLL
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "IPHLPAPI.DLL"
EXPORTS
AddIPAddress
AllocateAndGetArpEntTableFromStack
AllocateAndGetIfTableFromStack
AllocateAndGetInterfaceInfoFromStack
AllocateAndGetIpAddrTableFromStack
AllocateAndGetIpForwardTableFromStack
AllocateAndGetIpNetTableFromStack
AllocateAndGetTcpExTable2FromStack
AllocateAndGetTcpExTableFromStack
AllocateAndGetTcpTableFromStack
AllocateAndGetUdpExTable2FromStack
AllocateAndGetUdpExTableFromStack
AllocateAndGetUdpTableFromStack
CPNatfwtCreateProviderInstance
CPNatfwtDeregisterProviderInstance
CPNatfwtDestroyProviderInstance
CPNatfw<PERSON><PERSON>cateReceivedBuffers
CPNatfwtRegisterProviderInstance
CancelIfTimestampConfigChange
CancelIPChangeNotify
CancelSecurityHealthChangeNotify
CancelMibChangeNotify2
CaptureInterfaceHardwareCrossTimestamp
CloseCompartment
CloseGetIPPhysicalInterfaceForDestination
ConvertCompartmentGuidToId
ConvertCompartmentIdToGuid
ConvertGuidToStringA
ConvertGuidToStringW
ConvertInterfaceAliasToLuid
ConvertInterfaceGuidToLuid
ConvertInterfaceIndexToLuid
ConvertInterfaceLuidToAlias
ConvertInterfaceLuidToGuid
ConvertInterfaceLuidToIndex
ConvertInterfaceLuidToNameA
ConvertInterfaceLuidToNameW
ConvertInterfaceNameToLuidA
ConvertInterfaceNameToLuidW
ConvertInterfacePhysicalAddressToLuid
ConvertIpv4MaskToLength
ConvertLengthToIpv4Mask
ConvertRemoteInterfaceAliasToLuid
ConvertRemoteInterfaceGuidToLuid
ConvertRemoteInterfaceIndexToLuid
ConvertRemoteInterfaceLuidToAlias
ConvertRemoteInterfaceLuidToGuid
ConvertRemoteInterfaceLuidToIndex
ConvertStringToGuidA
ConvertStringToGuidW
ConvertStringToInterfacePhysicalAddress
CreateAnycastIpAddressEntry
CreateCompartment
CreateIpForwardEntry
CreateIpForwardEntry2
CreateIpNetEntry
CreateIpNetEntry2
CreatePersistentTcpPortReservation
CreatePersistentUdpPortReservation
CreateProxyArpEntry
CreateSortedAddressPairs
CreateUnicastIpAddressEntry
DeleteAnycastIpAddressEntry
DeleteCompartment
DeleteIPAddress
DeleteIpForwardEntry
DeleteIpForwardEntry2
DeleteIpNetEntry
DeleteIpNetEntry2
DeletePersistentTcpPortReservation
DeletePersistentUdpPortReservation
DeleteProxyArpEntry
DeleteUnicastIpAddressEntry
DisableMediaSense
EnableRouter
FlushIpNetTable
FlushIpNetTable2
FlushIpNetTableFromStack
FlushIpPathTable
FreeDnsSettings
FreeInterfaceDnsSettings
FreeMibTable
GetAdapterIndex
GetAdapterOrderMap
GetAdaptersAddresses
GetAdaptersInfo
GetAnycastIpAddressEntry
GetAnycastIpAddressTable
GetBestInterface
GetBestInterfaceEx
GetBestInterfaceFromStack
GetBestRoute
GetBestRoute2
GetBestRouteFromStack
GetCurrentThreadCompartmentId
GetCurrentThreadCompartmentScope
GetDefaultCompartmentId
GetDnsSettings
GetExtendedTcpTable
GetExtendedUdpTable
GetFriendlyIfIndex
GetIcmpStatistics
GetIcmpStatisticsEx
GetIcmpStatsFromStack
GetIcmpStatsFromStackEx
GetIfEntry
GetIfEntry2
GetIfEntry2Ex
GetIfEntryFromStack
GetIfStackTable
GetIfTable
GetIfTableFromStack
GetIgmpList
GetIfTable2
GetIfTable2Ex
GetInterfaceCompartmentId
GetInterfaceCurrentTimestampCapabilities
GetInterfaceDnsSettings
GetInterfaceHardwareTimestampCapabilities
GetInterfaceInfo
GetInvertedIfStackTable
GetIpAddrTable
GetIpAddrTableFromStack
GetIpErrorString
GetIpForwardEntry2
GetIpForwardTable
GetIpForwardTableFromStack
GetIpForwardTable2
GetIpInterfaceEntry
GetIpInterfaceTable
GetIpNetEntry2
GetIpNetTable
GetIpNetTable2
GetIpNetTableFromStack
GetIpNetworkConnectionBandwidthEstimates
GetIpPathEntry
GetIpPathTable
GetIpStatistics
GetIpStatisticsEx
GetIpStatsFromStack
GetIpStatsFromStackEx
GetJobCompartmentId
GetMulticastIpAddressEntry
GetMulticastIpAddressTable
GetNetworkConnectivityHint
GetNetworkConnectivityHintForInterface
GetNetworkInformation
GetNetworkParams
GetNumberOfInterfaces
GetOwnerModuleFromPidAndInfo
GetOwnerModuleFromTcp6Entry
GetOwnerModuleFromTcpEntry
GetOwnerModuleFromUdp6Entry
GetOwnerModuleFromUdpEntry
GetPerAdapterInfo
GetPerTcp6ConnectionEStats
GetPerTcp6ConnectionStats
GetPerTcpConnectionEStats
GetPerTcpConnectionStats
GetRTTAndHopCount
GetTcpExTable2FromStack
GetSessionCompartmentId
GetTcp6Table
GetTcp6Table2
GetTcpStatistics
GetTcpStatisticsEx
GetTcpStatisticsEx2
GetTcpStatsFromStack
GetTcpStatsFromStackEx
GetTcpTable
GetTcpTable2
GetTcpTableFromStack
GetUdpExTable2FromStack
GetTeredoPort
GetUdp6Table
GetUdpStatistics
GetUdpStatisticsEx
GetUdpStatisticsEx2
GetUdpStatsFromStack
GetUdpStatsFromStackEx
GetUdpTable
GetUdpTableFromStack
GetUniDirectionalAdapterInfo
GetUnicastIpAddressEntry
GetUnicastIpAddressTable
GetWPAOACSupportLevel
Icmp6CreateFile
Icmp6ParseReplies
Icmp6SendEcho2
IcmpCloseHandle
IcmpCreateFile
IcmpParseReplies
IcmpSendEcho
IcmpSendEcho2
IcmpSendEcho2Ex
InitializeCompartmentEntry
InitializeIpForwardEntry
InitializeIpInterfaceEntry
InitializeUnicastIpAddressEntry
InternalCleanupPersistentStore
InternalCreateAnycastIpAddressEntry
InternalCreateIpForwardEntry
InternalCreateIpForwardEntry2
InternalCreateIpNetEntry
InternalCreateIpNetEntry2
InternalCreateUnicastIpAddressEntry
InternalDeleteAnycastIpAddressEntry
InternalDeleteIpForwardEntry
InternalDeleteIpForwardEntry2
InternalDeleteIpNetEntry
InternalDeleteIpNetEntry2
InternalDeleteUnicastIpAddressEntry
InternalFindInterfaceByAddress
InternalGetAnycastIpAddressEntry
InternalGetAnycastIpAddressTable
InternalGetBoundTcp6EndpointTable
InternalGetBoundTcpEndpointTable
InternalGetForwardIpTable2
InternalGetIPPhysicalInterfaceForDestination
InternalGetIfEntry2
InternalGetIfTable
InternalGetIfTable2
InternalGetIpAddrTable
InternalGetIpForwardEntry2
InternalGetIpForwardTable
InternalGetIpInterfaceEntry
InternalGetIpInterfaceTable
InternalGetIpNetEntry2
InternalGetIpNetTable
InternalGetIpNetTable2
InternalGetMulticastIpAddressEntry
InternalGetMulticastIpAddressTable
InternalGetRtcSlotInformation
InternalGetTcp6Table2
InternalGetTcp6TableWithOwnerModule
InternalGetTcp6TableWithOwnerPid
InternalGetTcpTable
InternalGetTcpTable2
InternalGetTcpTableEx
InternalGetTcpTableWithOwnerModule
InternalGetTcpTableWithOwnerPid
InternalGetTunnelPhysicalAdapter
InternalGetUdp6TableWithOwnerModule
InternalGetUdp6TableWithOwnerPid
InternalGetUdpTable
InternalGetUdpTableEx
InternalGetUdpTableWithOwnerModule
InternalGetUdpTableWithOwnerPid
InternalGetUnicastIpAddressEntry
InternalGetUnicastIpAddressTable
InternalIcmpCreateFileEx
InternalSetIfEntry
InternalSetIpForwardEntry
InternalSetIpForwardEntry2
InternalSetIpInterfaceEntry
InternalSetIpNetEntry
InternalSetIpNetEntry2
InternalSetIpStats
InternalSetTcpEntry
InternalSetTeredoPort
InternalSetUnicastIpAddressEntry
IpReleaseAddress
IpRenewAddress
IsLocalAddress
LookupPersistentTcpPortReservation
LookupPersistentUdpPortReservation
NTPTimeToNTFileTime
NTTimeToNTPTime
NhGetGuidFromInterfaceName
NhGetInterfaceDescriptionFromGuid
NhGetInterfaceNameFromDeviceGuid
NhGetInterfaceNameFromGuid
NhpAllocateAndGetInterfaceInfoFromStack
NhpGetInterfaceIndexFromStack
NotifyAddrChange
NotifyCompartmentChange
NotifyIfTimestampConfigChange
NotifyIpInterfaceChange
NotifyNetworkConnectivityHintChange
NotifyRouteChange
NotifyRouteChange2
NotifyRouteChangeEx
NotifySecurityHealthChange
NotifyStableUnicastIpAddressTable
NotifyTeredoPortChange
NotifyUnicastIpAddressChange
OpenCompartment
ParseNetworkString
PfAddFiltersToInterface
PfAddGlobalFilterToInterface
PfBindInterfaceToIPAddress
PfBindInterfaceToIndex
PfCreateInterface
PfDeleteInterface
PfDeleteLog
PfGetInterfaceStatistics
PfMakeLog
PfRebindFilters
PfRemoveFilterHandles
PfRemoveFiltersFromInterface
PfRemoveGlobalFilterFromInterface
PfSetLogBuffer
PfTestPacket
PfUnBindInterface
ResolveIpNetEntry2
ResolveNeighbor
RestoreMediaSense
SendARP
SetAdapterIpAddress
SetBlockRoutes
SetCurrentThreadCompartmentId
SetCurrentThreadCompartmentScope
SetDnsSettings
SetIfEntry
SetIfEntryToStack
SetInterfaceDnsSettings
SetIpForwardEntry
SetIpForwardEntry2
SetIpForwardEntryToStack
SetIpMultihopRouteEntryToStack
SetIpInterfaceEntry
SetIpNetEntry
SetIpNetEntry2
SetIpNetEntryToStack
SetIpRouteEntryToStack
SetIpStatistics
SetIpStatsToStack
SetIpStatisticsEx
SetIpTTL
SetJobCompartmentId
SetProxyArpEntryToStack
SetRouteWithRef
SetNetworkInformation
SetPerTcp6ConnectionEStats
SetPerTcp6ConnectionStats
SetPerTcpConnectionEStats
SetPerTcpConnectionStats
SetSessionCompartmentId
SetTcpEntry
SetTcpEntryToStack
SetUnicastIpAddressEntry
UnenableRouter
do_echo_rep
do_echo_req
if_indextoname
if_nametoindex
register_icmp
