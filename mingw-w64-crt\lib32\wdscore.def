;
; Definition file of WDSCORE.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WDSCORE.dll"
EXPORTS
; public: __thiscall <unsigned char,unsigned char *>::<unsigned char,unsigned char *>(unsigned int)
??0?$CDynamicArray@EPAE@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <unsigned char,struct SKey *>::<unsigned char,struct SKey *>(unsigned int)
??0?$CDynamicArray@EPAUSKey@@@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <unsigned char,struct SValue *>::<unsigned char,struct SValue *>(unsigned int)
??0?$CDynamicArray@EPAUSValue@@@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <unsigned short,unsigned short *>::<unsigned short,unsigned short *>(unsigned int)
??0?$CD<PERSON>y@GPAG@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::<struct SEnumBinContext *,struct SEnumBinContext **>(unsigned int)
??0?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::<struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>(unsigned int)
??0?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <unsigned __int64,unsigned __int64 *>::<unsigned __int64,unsigned __int64 *>(unsigned int)
??0?$CDynamicArray@_KPA_K@@QAE@I@Z ; has WINAPI (@4)
; public: __thiscall <unsigned char,unsigned char *>::~<unsigned char,unsigned char *>(void)
??1?$CDynamicArray@EPAE@@QAE@XZ
; public: __thiscall <unsigned char,struct SKey *>::~<unsigned char,struct SKey *>(void)
??1?$CDynamicArray@EPAUSKey@@@@QAE@XZ
; public: __thiscall <unsigned char,struct SValue *>::~<unsigned char,struct SValue *>(void)
??1?$CDynamicArray@EPAUSValue@@@@QAE@XZ
; public: __thiscall <unsigned short,unsigned short *>::~<unsigned short,unsigned short *>(void)
??1?$CDynamicArray@GPAG@@QAE@XZ
; public: __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::~<struct SEnumBinContext *,struct SEnumBinContext **>(void)
??1?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAE@XZ
; public: __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::~<struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>(void)
??1?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAE@XZ
; public: __thiscall <unsigned __int64,unsigned __int64 *>::~<unsigned __int64,unsigned __int64 *>(void)
??1?$CDynamicArray@_KPA_K@@QAE@XZ
; public: class <unsigned char,unsigned char *> &__thiscall <unsigned char,unsigned char *>::operator =(class <unsigned char,unsigned char *> const &)
??4?$CDynamicArray@EPAE@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <unsigned char,struct SKey *> &__thiscall <unsigned char,struct SKey *>::operator =(class <unsigned char,struct SKey *> const &)
??4?$CDynamicArray@EPAUSKey@@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <unsigned char,struct SValue *> &__thiscall <unsigned char,struct SValue *>::operator =(class <unsigned char,struct SValue *> const &)
??4?$CDynamicArray@EPAUSValue@@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <unsigned short,unsigned short *> &__thiscall <unsigned short,unsigned short *>::operator =(class <unsigned short,unsigned short *> const &)
??4?$CDynamicArray@GPAG@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <struct SEnumBinContext *,struct SEnumBinContext **> &__thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::operator =(class <struct SEnumBinContext *,struct SEnumBinContext **> const &)
??4?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *> &__thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::operator =(class <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *> const &)
??4?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: class <unsigned __int64,unsigned __int64 *> &__thiscall <unsigned __int64,unsigned __int64 *>::operator =(class <unsigned __int64,unsigned __int64 *> const &)
??4?$CDynamicArray@_KPA_K@@QAEAAV0@ABV0@@Z ; has WINAPI (@4)
; public: struct SEnumBinContext *&__thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::operator[](unsigned int)
??A?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAEAAPAUSEnumBinContext@@I@Z ; has WINAPI (@4)
; public: unsigned __int64 &__thiscall <unsigned __int64,unsigned __int64 *>::operator[](unsigned int)
??A?$CDynamicArray@_KPA_K@@QAEAA_KI@Z ; has WINAPI (@4)
; public: void __thiscall <unsigned char,struct SKey *>::operator struct SKey *(...)const throw()
??B?$CDynamicArray@EPAUSKey@@@@QBEPAUSKey@@XZ
; public: void __thiscall <unsigned char,struct SValue *>::operator struct SValue *(...)const throw()
??B?$CDynamicArray@EPAUSValue@@@@QBEPAUSValue@@XZ
; public: void __thiscall <unsigned short,unsigned short *>::operator unsigned short *(...)const throw()
??B?$CDynamicArray@GPAG@@QBEPAGXZ
; public: struct SKey *__thiscall <unsigned char,struct SKey *>::operator ->(void)const 
??C?$CDynamicArray@EPAUSKey@@@@QBEPAUSKey@@XZ
; public: struct SValue *__thiscall <unsigned char,struct SValue *>::operator ->(void)const 
??C?$CDynamicArray@EPAUSValue@@@@QBEPAUSValue@@XZ
; public: void __thiscall <unsigned char,unsigned char *>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@EPAE@@QAEXXZ
; public: void __thiscall <unsigned char,struct SKey *>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@EPAUSKey@@@@QAEXXZ
; public: void __thiscall <unsigned char,struct SValue *>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@EPAUSValue@@@@QAEXXZ
; public: void __thiscall <unsigned short,unsigned short *>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@GPAG@@QAEXXZ
; public: void __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAEXXZ
; public: void __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEXXZ
; public: void __thiscall <unsigned __int64,unsigned __int64 *>::__dflt_ctor_closure(void)
??_F?$CDynamicArray@_KPA_K@@QAEXXZ
; public: int __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::Add(struct SEnumBinContext *&)
?Add@?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAEHAAPAUSEnumBinContext@@@Z ; has WINAPI (@4)
; public: int __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::Add(struct CBlackboardFactory::SKeeperEntry &)
?Add@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEHAAUSKeeperEntry@CBlackboardFactory@@@Z ; has WINAPI (@4)
; public: int __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::Add(struct CBlackboardFactory::SKeeperEntry &,unsigned int &)
?Add@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEHAAUSKeeperEntry@CBlackboardFactory@@AAI@Z ; has WINAPI (@8)
; public: int __thiscall <unsigned __int64,unsigned __int64 *>::Add(unsigned __int64 &)
?Add@?$CDynamicArray@_KPA_K@@QAEHAA_K@Z ; has WINAPI (@4)
; public: unsigned short &__thiscall <unsigned short,unsigned short *>::ElementAt(unsigned int)
?ElementAt@?$CDynamicArray@GPAG@@QAEAAGI@Z ; has WINAPI (@4)
; public: struct CBlackboardFactory::SKeeperEntry &__thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::ElementAt(unsigned int)
?ElementAt@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEAAUSKeeperEntry@CBlackboardFactory@@I@Z ; has WINAPI (@4)
; public: unsigned char *__thiscall <unsigned char,unsigned char *>::GetBuffer(unsigned int)
?GetBuffer@?$CDynamicArray@EPAE@@QAEPAEI@Z ; has WINAPI (@4)
; public: struct SValue *__thiscall <unsigned char,struct SValue *>::GetBuffer(unsigned int)
?GetBuffer@?$CDynamicArray@EPAUSValue@@@@QAEPAUSValue@@I@Z ; has WINAPI (@4)
; public: unsigned short *__thiscall <unsigned short,unsigned short *>::GetBuffer(unsigned int)
?GetBuffer@?$CDynamicArray@GPAG@@QAEPAGI@Z ; has WINAPI (@4)
; public: unsigned int __thiscall <unsigned char,unsigned char *>::GetSize(void)const 
?GetSize@?$CDynamicArray@EPAE@@QBEIXZ
; public: unsigned int __thiscall <unsigned short,unsigned short *>::GetSize(void)const 
?GetSize@?$CDynamicArray@GPAG@@QBEIXZ
; public: unsigned int __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::GetSize(void)const 
?GetSize@?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QBEIXZ
; public: unsigned int __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::GetSize(void)const 
?GetSize@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QBEIXZ
; public: unsigned int __thiscall <unsigned __int64,unsigned __int64 *>::GetSize(void)const 
?GetSize@?$CDynamicArray@_KPA_K@@QBEIXZ
; protected: void __thiscall <unsigned char,unsigned char *>::Init(unsigned int)
?Init@?$CDynamicArray@EPAE@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall <unsigned char,struct SKey *>::Init(unsigned int)
?Init@?$CDynamicArray@EPAUSKey@@@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall <unsigned char,struct SValue *>::Init(unsigned int)
?Init@?$CDynamicArray@EPAUSValue@@@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall <unsigned short,unsigned short *>::Init(unsigned int)
?Init@?$CDynamicArray@GPAG@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::Init(unsigned int)
?Init@?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::Init(unsigned int)
?Init@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@IAEXI@Z ; has WINAPI (@4)
; protected: void __thiscall <unsigned __int64,unsigned __int64 *>::Init(unsigned int)
?Init@?$CDynamicArray@_KPA_K@@IAEXI@Z ; has WINAPI (@4)
; public: void __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::RemoveAll(void)
?RemoveAll@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEXXZ
; public: void __thiscall <unsigned __int64,unsigned __int64 *>::RemoveAll(void)
?RemoveAll@?$CDynamicArray@_KPA_K@@QAEXXZ
; public: void __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::RemoveItemFromTail(void)
?RemoveItemFromTail@?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAEXXZ
; public: int __thiscall <unsigned char,unsigned char *>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@EPAE@@QAEHK@Z ; has WINAPI (@4)
; public: int __thiscall <unsigned char,struct SKey *>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@EPAUSKey@@@@QAEHK@Z ; has WINAPI (@4)
; public: int __thiscall <unsigned char,struct SValue *>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@EPAUSValue@@@@QAEHK@Z ; has WINAPI (@4)
; public: int __thiscall <unsigned short,unsigned short *>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@GPAG@@QAEHK@Z ; has WINAPI (@4)
; public: int __thiscall <struct SEnumBinContext *,struct SEnumBinContext **>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@PAUSEnumBinContext@@PAPAU1@@@QAEHK@Z ; has WINAPI (@4)
; public: int __thiscall <struct CBlackboardFactory::SKeeperEntry,struct CBlackboardFactory::SKeeperEntry *>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@USKeeperEntry@CBlackboardFactory@@PAU12@@@QAEHK@Z ; has WINAPI (@4)
; public: int __thiscall <unsigned __int64,unsigned __int64 *>::SetSize(unsigned long)
?SetSize@?$CDynamicArray@_KPA_K@@QAEHK@Z ; has WINAPI (@4)
WdsGetPointer@4
g_Kernel32 DATA
g_bEnableDiagnosticMode DATA
ConstructPartialMsgIfA
ConstructPartialMsgIfW
ConstructPartialMsgVA@12
ConstructPartialMsgVW@12
CurrentIP
EndMajorTask
EndMinorTask
GetMajorTask
GetMajorTaskA
GetMinorTask
GetMinorTaskA
StartMajorTask@4
StartMinorTask@4
WdsAbortBlackboardItemEnum@4
WdsAddModule@16
WdsAddUsmtLogStack@12
WdsAllocCollection
WdsCollectionAddValue@12
WdsCollectionGetValue@12
WdsCopyBlackboardItems@16
WdsCopyBlackboardItemsEx@24
WdsCreateBlackboard@12
WdsDeleteBlackboardValue@12
WdsDeleteEvent@4
WdsDestroyBlackboard@4
WdsDuplicateData@8
WdsEnableDiagnosticMode@4
WdsEnableExit@4
WdsEnableExitEx@8
WdsEnumFirstBlackboardItem@20
WdsEnumFirstCollectionValue@8
WdsEnumNextBlackboardItem@4
WdsEnumNextCollectionValue@4
WdsExecuteWorkQueue2@24
WdsExecuteWorkQueue@24
WdsExecuteWorkQueueEx@28
WdsExitImmediately@4
WdsExitImmediatelyEx@8
WdsFreeCollection@4
WdsFreeData@4
WdsGenericSetupLogInit@8
WdsGetAssertFlags
WdsGetBlackboardBinaryData@24
WdsGetBlackboardStringA@20
WdsGetBlackboardStringW@20
WdsGetBlackboardUintPtr@16
WdsGetBlackboardValue@16
WdsGetCurrentExecutionGroup@8
WdsGetSetupLog
WdsGetTempDir@12
WdsInitialize@28
WdsInitializeCallbackArray@12
WdsInitializeDataBinary@12
WdsInitializeDataStringA@8
WdsInitializeDataStringW@8
WdsInitializeDataUInt32@8
WdsInitializeDataUInt64@12
WdsIsDiagnosticModeEnabled
WdsIterateOfflineQueue@28
WdsIterateQueue@28
WdsLockBlackboardValue@16
WdsLockExecutionGroup
WdsLogCreate@12
WdsLogDestroy@4
WdsLogRegStockProviders
WdsLogRegisterProvider@8
WdsLogStructuredException@4
WdsLogUnRegStockProviders
WdsLogUnRegisterProvider@4
WdsPackCollection@8
WdsPublish@24
WdsPublishEx@28
WdsPublishImmediateAsync@24
WdsPublishImmediateEx@20
WdsPublishOffline@24
WdsSeqAlloc@8
WdsSeqFree@4
WdsSetAssertFlags@4
WdsSetBlackboardValue@16
WdsSetNextExecutionGroup@4
WdsSetUILanguage@4
WdsSetupLogDestroy
WdsSetupLogInit@12
WdsSetupLogMessageA@44
WdsSetupLogMessageW@44
WdsSubscribeEx@20
WdsTerminate
WdsUnlockExecutionGroup
WdsUnpackCollection@4
WdsUnsubscribe@4
WdsUnsubscribeEx@16
WdsValidBlackboard@4
