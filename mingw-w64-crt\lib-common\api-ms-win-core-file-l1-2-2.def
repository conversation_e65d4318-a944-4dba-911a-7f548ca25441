LIBRARY api-ms-win-core-file-l1-2-2

EXPORTS

AreFileApisANSI
CompareFileTime
CreateDirectoryA
CreateDirectoryW
CreateFile2
CreateFileA
CreateFileW
DeleteFileA
DeleteFileW
DeleteVolumeMountPointW
FileTimeToLocalFileTime
FindClose
FindFirstFileA
FindFirstFileExA
FindFirstFileExW
FindFirstFileW
FindNextFileA
FindNextFileW
FlushFileBuffers
GetDiskFreeSpaceA
GetDiskFreeSpaceExA
GetDiskFreeSpaceExW
GetDiskFreeSpaceW
GetDriveTypeA
GetDriveTypeW
GetFileAttributesA
GetFileAttributesExA
GetFileAttributesExW
GetFileAttributesW
GetFileInformationByHandle
GetFileSize
GetFileSizeEx
GetFileTime
GetFileType
GetFinalPathNameByHandleA
GetFinalPathNameByHandleW
GetFullPathNameA
GetFullPathNameW
GetLogicalDrives
GetLogicalDriveStringsW
GetLongPathNameW
GetShortPathNameW
GetTempFileNameA
GetTempFileNameW
GetTempPathA
GetTempPathW
GetVolumeInformationA
GetVolumeInformationW
GetVolumePathNamesForVolumeNameW
LocalFileTimeToFileTime
LockFile
LockFileEx
ReadFile
ReadFileEx
RemoveDirectoryA
RemoveDirectoryW
SetEndOfFile
SetFileAttributesA
SetFileAttributesW
SetFileInformationByHandle
SetFilePointer
SetFilePointerEx
SetFileTime
UnlockFile
UnlockFileEx
WriteFile
WriteFileEx
