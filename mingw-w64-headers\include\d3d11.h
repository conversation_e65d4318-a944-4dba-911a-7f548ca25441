/*** Autogenerated by WIDL 8.5 from include/d3d11.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d11_h__
#define __d3d11_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __ID3D11DeviceChild_FWD_DEFINED__
#define __ID3D11DeviceChild_FWD_DEFINED__
typedef interface ID3D11DeviceChild ID3D11DeviceChild;
#ifdef __cplusplus
interface ID3D11DeviceChild;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Asynchronous_FWD_DEFINED__
#define __ID3D11Asynchronous_FWD_DEFINED__
typedef interface ID3D11Asynchronous ID3D11Asynchronous;
#ifdef __cplusplus
interface ID3D11Asynchronous;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Query_FWD_DEFINED__
#define __ID3D11Query_FWD_DEFINED__
typedef interface ID3D11Query ID3D11Query;
#ifdef __cplusplus
interface ID3D11Query;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Resource_FWD_DEFINED__
#define __ID3D11Resource_FWD_DEFINED__
typedef interface ID3D11Resource ID3D11Resource;
#ifdef __cplusplus
interface ID3D11Resource;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11View_FWD_DEFINED__
#define __ID3D11View_FWD_DEFINED__
typedef interface ID3D11View ID3D11View;
#ifdef __cplusplus
interface ID3D11View;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11BlendState_FWD_DEFINED__
#define __ID3D11BlendState_FWD_DEFINED__
typedef interface ID3D11BlendState ID3D11BlendState;
#ifdef __cplusplus
interface ID3D11BlendState;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Buffer_FWD_DEFINED__
#define __ID3D11Buffer_FWD_DEFINED__
typedef interface ID3D11Buffer ID3D11Buffer;
#ifdef __cplusplus
interface ID3D11Buffer;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11ClassInstance_FWD_DEFINED__
#define __ID3D11ClassInstance_FWD_DEFINED__
typedef interface ID3D11ClassInstance ID3D11ClassInstance;
#ifdef __cplusplus
interface ID3D11ClassInstance;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11ClassLinkage_FWD_DEFINED__
#define __ID3D11ClassLinkage_FWD_DEFINED__
typedef interface ID3D11ClassLinkage ID3D11ClassLinkage;
#ifdef __cplusplus
interface ID3D11ClassLinkage;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11CommandList_FWD_DEFINED__
#define __ID3D11CommandList_FWD_DEFINED__
typedef interface ID3D11CommandList ID3D11CommandList;
#ifdef __cplusplus
interface ID3D11CommandList;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11ComputeShader_FWD_DEFINED__
#define __ID3D11ComputeShader_FWD_DEFINED__
typedef interface ID3D11ComputeShader ID3D11ComputeShader;
#ifdef __cplusplus
interface ID3D11ComputeShader;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Counter_FWD_DEFINED__
#define __ID3D11Counter_FWD_DEFINED__
typedef interface ID3D11Counter ID3D11Counter;
#ifdef __cplusplus
interface ID3D11Counter;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DepthStencilState_FWD_DEFINED__
#define __ID3D11DepthStencilState_FWD_DEFINED__
typedef interface ID3D11DepthStencilState ID3D11DepthStencilState;
#ifdef __cplusplus
interface ID3D11DepthStencilState;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DepthStencilView_FWD_DEFINED__
#define __ID3D11DepthStencilView_FWD_DEFINED__
typedef interface ID3D11DepthStencilView ID3D11DepthStencilView;
#ifdef __cplusplus
interface ID3D11DepthStencilView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DomainShader_FWD_DEFINED__
#define __ID3D11DomainShader_FWD_DEFINED__
typedef interface ID3D11DomainShader ID3D11DomainShader;
#ifdef __cplusplus
interface ID3D11DomainShader;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11GeometryShader_FWD_DEFINED__
#define __ID3D11GeometryShader_FWD_DEFINED__
typedef interface ID3D11GeometryShader ID3D11GeometryShader;
#ifdef __cplusplus
interface ID3D11GeometryShader;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11HullShader_FWD_DEFINED__
#define __ID3D11HullShader_FWD_DEFINED__
typedef interface ID3D11HullShader ID3D11HullShader;
#ifdef __cplusplus
interface ID3D11HullShader;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11InputLayout_FWD_DEFINED__
#define __ID3D11InputLayout_FWD_DEFINED__
typedef interface ID3D11InputLayout ID3D11InputLayout;
#ifdef __cplusplus
interface ID3D11InputLayout;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11PixelShader_FWD_DEFINED__
#define __ID3D11PixelShader_FWD_DEFINED__
typedef interface ID3D11PixelShader ID3D11PixelShader;
#ifdef __cplusplus
interface ID3D11PixelShader;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Predicate_FWD_DEFINED__
#define __ID3D11Predicate_FWD_DEFINED__
typedef interface ID3D11Predicate ID3D11Predicate;
#ifdef __cplusplus
interface ID3D11Predicate;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11RasterizerState_FWD_DEFINED__
#define __ID3D11RasterizerState_FWD_DEFINED__
typedef interface ID3D11RasterizerState ID3D11RasterizerState;
#ifdef __cplusplus
interface ID3D11RasterizerState;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11RenderTargetView_FWD_DEFINED__
#define __ID3D11RenderTargetView_FWD_DEFINED__
typedef interface ID3D11RenderTargetView ID3D11RenderTargetView;
#ifdef __cplusplus
interface ID3D11RenderTargetView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11SamplerState_FWD_DEFINED__
#define __ID3D11SamplerState_FWD_DEFINED__
typedef interface ID3D11SamplerState ID3D11SamplerState;
#ifdef __cplusplus
interface ID3D11SamplerState;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11ShaderResourceView_FWD_DEFINED__
#define __ID3D11ShaderResourceView_FWD_DEFINED__
typedef interface ID3D11ShaderResourceView ID3D11ShaderResourceView;
#ifdef __cplusplus
interface ID3D11ShaderResourceView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Texture1D_FWD_DEFINED__
#define __ID3D11Texture1D_FWD_DEFINED__
typedef interface ID3D11Texture1D ID3D11Texture1D;
#ifdef __cplusplus
interface ID3D11Texture1D;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Texture2D_FWD_DEFINED__
#define __ID3D11Texture2D_FWD_DEFINED__
typedef interface ID3D11Texture2D ID3D11Texture2D;
#ifdef __cplusplus
interface ID3D11Texture2D;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Texture3D_FWD_DEFINED__
#define __ID3D11Texture3D_FWD_DEFINED__
typedef interface ID3D11Texture3D ID3D11Texture3D;
#ifdef __cplusplus
interface ID3D11Texture3D;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11UnorderedAccessView_FWD_DEFINED__
#define __ID3D11UnorderedAccessView_FWD_DEFINED__
typedef interface ID3D11UnorderedAccessView ID3D11UnorderedAccessView;
#ifdef __cplusplus
interface ID3D11UnorderedAccessView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VertexShader_FWD_DEFINED__
#define __ID3D11VertexShader_FWD_DEFINED__
typedef interface ID3D11VertexShader ID3D11VertexShader;
#ifdef __cplusplus
interface ID3D11VertexShader;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DeviceContext_FWD_DEFINED__
#define __ID3D11DeviceContext_FWD_DEFINED__
typedef interface ID3D11DeviceContext ID3D11DeviceContext;
#ifdef __cplusplus
interface ID3D11DeviceContext;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11AuthenticatedChannel_FWD_DEFINED__
#define __ID3D11AuthenticatedChannel_FWD_DEFINED__
typedef interface ID3D11AuthenticatedChannel ID3D11AuthenticatedChannel;
#ifdef __cplusplus
interface ID3D11AuthenticatedChannel;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11CryptoSession_FWD_DEFINED__
#define __ID3D11CryptoSession_FWD_DEFINED__
typedef interface ID3D11CryptoSession ID3D11CryptoSession;
#ifdef __cplusplus
interface ID3D11CryptoSession;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoDecoder_FWD_DEFINED__
#define __ID3D11VideoDecoder_FWD_DEFINED__
typedef interface ID3D11VideoDecoder ID3D11VideoDecoder;
#ifdef __cplusplus
interface ID3D11VideoDecoder;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoProcessorEnumerator_FWD_DEFINED__
#define __ID3D11VideoProcessorEnumerator_FWD_DEFINED__
typedef interface ID3D11VideoProcessorEnumerator ID3D11VideoProcessorEnumerator;
#ifdef __cplusplus
interface ID3D11VideoProcessorEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoProcessor_FWD_DEFINED__
#define __ID3D11VideoProcessor_FWD_DEFINED__
typedef interface ID3D11VideoProcessor ID3D11VideoProcessor;
#ifdef __cplusplus
interface ID3D11VideoProcessor;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoDecoderOutputView_FWD_DEFINED__
#define __ID3D11VideoDecoderOutputView_FWD_DEFINED__
typedef interface ID3D11VideoDecoderOutputView ID3D11VideoDecoderOutputView;
#ifdef __cplusplus
interface ID3D11VideoDecoderOutputView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoProcessorInputView_FWD_DEFINED__
#define __ID3D11VideoProcessorInputView_FWD_DEFINED__
typedef interface ID3D11VideoProcessorInputView ID3D11VideoProcessorInputView;
#ifdef __cplusplus
interface ID3D11VideoProcessorInputView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoProcessorOutputView_FWD_DEFINED__
#define __ID3D11VideoProcessorOutputView_FWD_DEFINED__
typedef interface ID3D11VideoProcessorOutputView ID3D11VideoProcessorOutputView;
#ifdef __cplusplus
interface ID3D11VideoProcessorOutputView;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoDevice_FWD_DEFINED__
#define __ID3D11VideoDevice_FWD_DEFINED__
typedef interface ID3D11VideoDevice ID3D11VideoDevice;
#ifdef __cplusplus
interface ID3D11VideoDevice;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoContext_FWD_DEFINED__
#define __ID3D11VideoContext_FWD_DEFINED__
typedef interface ID3D11VideoContext ID3D11VideoContext;
#ifdef __cplusplus
interface ID3D11VideoContext;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Device_FWD_DEFINED__
#define __ID3D11Device_FWD_DEFINED__
typedef interface ID3D11Device ID3D11Device;
#ifdef __cplusplus
interface ID3D11Device;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgi.h>
#include <d3dcommon.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef D3D_PRIMITIVE D3D11_PRIMITIVE;
typedef D3D_PRIMITIVE_TOPOLOGY D3D11_PRIMITIVE_TOPOLOGY;
typedef D3D_SRV_DIMENSION D3D11_SRV_DIMENSION;
typedef RECT D3D11_RECT;
#ifndef __ID3D11Device_FWD_DEFINED__
#define __ID3D11Device_FWD_DEFINED__
typedef interface ID3D11Device ID3D11Device;
#ifdef __cplusplus
interface ID3D11Device;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11ClassLinkage_FWD_DEFINED__
#define __ID3D11ClassLinkage_FWD_DEFINED__
typedef interface ID3D11ClassLinkage ID3D11ClassLinkage;
#ifdef __cplusplus
interface ID3D11ClassLinkage;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Resource_FWD_DEFINED__
#define __ID3D11Resource_FWD_DEFINED__
typedef interface ID3D11Resource ID3D11Resource;
#ifdef __cplusplus
interface ID3D11Resource;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoProcessorInputView_FWD_DEFINED__
#define __ID3D11VideoProcessorInputView_FWD_DEFINED__
typedef interface ID3D11VideoProcessorInputView ID3D11VideoProcessorInputView;
#ifdef __cplusplus
interface ID3D11VideoProcessorInputView;
#endif /* __cplusplus */
#endif

#ifndef _D3D11_CONSTANTS
#define _D3D11_CONSTANTS
#define D3D11_16BIT_INDEX_STRIP_CUT_VALUE (0xffff)

#define D3D11_32BIT_INDEX_STRIP_CUT_VALUE (0xffffffff)

#define D3D11_8BIT_INDEX_STRIP_CUT_VALUE (0xff)

#define D3D11_ARRAY_AXIS_ADDRESS_RANGE_BIT_COUNT (9)

#define D3D11_CLIP_OR_CULL_DISTANCE_COUNT (8)

#define D3D11_CLIP_OR_CULL_DISTANCE_ELEMENT_COUNT (2)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT (14)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_COMPONENTS (4)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_COMPONENT_BIT_COUNT (32)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_HW_SLOT_COUNT (15)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_PARTIAL_UPDATE_EXTENTS_BYTE_ALIGNMENT (16)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COMPONENTS (4)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COUNT (15)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READS_PER_INST (1)

#define D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READ_PORTS (1)

#define D3D11_COMMONSHADER_FLOWCONTROL_NESTING_LIMIT (64)

#define D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COMPONENTS (4)

#define D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COUNT (1)

#define D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READS_PER_INST (1)

#define D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READ_PORTS (1)

#define D3D11_COMMONSHADER_IMMEDIATE_VALUE_COMPONENT_BIT_COUNT (32)

#define D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_COMPONENTS (1)

#define D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_COUNT (128)

#define D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_READS_PER_INST (1)

#define D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_READ_PORTS (1)

#define D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT (128)

#define D3D11_COMMONSHADER_SAMPLER_REGISTER_COMPONENTS (1)

#define D3D11_COMMONSHADER_SAMPLER_REGISTER_COUNT (16)

#define D3D11_COMMONSHADER_SAMPLER_REGISTER_READS_PER_INST (1)

#define D3D11_COMMONSHADER_SAMPLER_REGISTER_READ_PORTS (1)

#define D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT (16)

#define D3D11_COMMONSHADER_SUBROUTINE_NESTING_LIMIT (32)

#define D3D11_COMMONSHADER_TEMP_REGISTER_COMPONENTS (4)

#define D3D11_COMMONSHADER_TEMP_REGISTER_COMPONENT_BIT_COUNT (32)

#define D3D11_COMMONSHADER_TEMP_REGISTER_COUNT (4096)

#define D3D11_COMMONSHADER_TEMP_REGISTER_READS_PER_INST (3)

#define D3D11_COMMONSHADER_TEMP_REGISTER_READ_PORTS (3)

#define D3D11_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MAX (10)

#define D3D11_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MIN (-10)

#define D3D11_COMMONSHADER_TEXEL_OFFSET_MAX_NEGATIVE (-8)

#define D3D11_COMMONSHADER_TEXEL_OFFSET_MAX_POSITIVE (7)

#define D3D11_CS_4_X_BUCKET00_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (256)

#define D3D11_CS_4_X_BUCKET00_MAX_NUM_THREADS_PER_GROUP (64)

#define D3D11_CS_4_X_BUCKET01_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (240)

#define D3D11_CS_4_X_BUCKET01_MAX_NUM_THREADS_PER_GROUP (68)

#define D3D11_CS_4_X_BUCKET02_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (224)

#define D3D11_CS_4_X_BUCKET02_MAX_NUM_THREADS_PER_GROUP (72)

#define D3D11_CS_4_X_BUCKET03_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (208)

#define D3D11_CS_4_X_BUCKET03_MAX_NUM_THREADS_PER_GROUP (76)

#define D3D11_CS_4_X_BUCKET04_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (192)

#define D3D11_CS_4_X_BUCKET04_MAX_NUM_THREADS_PER_GROUP (84)

#define D3D11_CS_4_X_BUCKET05_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (176)

#define D3D11_CS_4_X_BUCKET05_MAX_NUM_THREADS_PER_GROUP (92)

#define D3D11_CS_4_X_BUCKET06_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (160)

#define D3D11_CS_4_X_BUCKET06_MAX_NUM_THREADS_PER_GROUP (100)

#define D3D11_CS_4_X_BUCKET07_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (144)

#define D3D11_CS_4_X_BUCKET07_MAX_NUM_THREADS_PER_GROUP (112)

#define D3D11_CS_4_X_BUCKET08_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (128)

#define D3D11_CS_4_X_BUCKET08_MAX_NUM_THREADS_PER_GROUP (128)

#define D3D11_CS_4_X_BUCKET09_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (112)

#define D3D11_CS_4_X_BUCKET09_MAX_NUM_THREADS_PER_GROUP (144)

#define D3D11_CS_4_X_BUCKET10_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (96)

#define D3D11_CS_4_X_BUCKET10_MAX_NUM_THREADS_PER_GROUP (168)

#define D3D11_CS_4_X_BUCKET11_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (80)

#define D3D11_CS_4_X_BUCKET11_MAX_NUM_THREADS_PER_GROUP (204)

#define D3D11_CS_4_X_BUCKET12_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (64)

#define D3D11_CS_4_X_BUCKET12_MAX_NUM_THREADS_PER_GROUP (256)

#define D3D11_CS_4_X_BUCKET13_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (48)

#define D3D11_CS_4_X_BUCKET13_MAX_NUM_THREADS_PER_GROUP (340)

#define D3D11_CS_4_X_BUCKET14_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (32)

#define D3D11_CS_4_X_BUCKET14_MAX_NUM_THREADS_PER_GROUP (512)

#define D3D11_CS_4_X_BUCKET15_MAX_BYTES_TGSM_WRITABLE_PER_THREAD (16)

#define D3D11_CS_4_X_BUCKET15_MAX_NUM_THREADS_PER_GROUP (768)

#define D3D11_CS_4_X_DISPATCH_MAX_THREAD_GROUPS_IN_Z_DIMENSION (1)

#define D3D11_CS_4_X_RAW_UAV_BYTE_ALIGNMENT (256)

#define D3D11_CS_4_X_THREAD_GROUP_MAX_THREADS_PER_GROUP (768)

#define D3D11_CS_4_X_THREAD_GROUP_MAX_X (768)

#define D3D11_CS_4_X_THREAD_GROUP_MAX_Y (768)

#define D3D11_CS_4_X_UAV_REGISTER_COUNT (1)

#define D3D11_CS_DISPATCH_MAX_THREAD_GROUPS_PER_DIMENSION (65535)

#define D3D11_CS_TGSM_REGISTER_COUNT (8192)

#define D3D11_CS_TGSM_REGISTER_READS_PER_INST (1)

#define D3D11_CS_TGSM_RESOURCE_REGISTER_COMPONENTS (1)

#define D3D11_CS_TGSM_RESOURCE_REGISTER_READ_PORTS (1)

#define D3D11_CS_THREAD_GROUP_MAX_THREADS_PER_GROUP (1024)

#define D3D11_CS_THREAD_GROUP_MAX_X (1024)

#define D3D11_CS_THREAD_GROUP_MAX_Y (1024)

#define D3D11_CS_THREAD_GROUP_MAX_Z (64)

#define D3D11_CS_THREAD_GROUP_MIN_X (1)

#define D3D11_CS_THREAD_GROUP_MIN_Y (1)

#define D3D11_CS_THREAD_GROUP_MIN_Z (1)

#define D3D11_CS_THREAD_LOCAL_TEMP_REGISTER_POOL (16384)

#define D3D11_DEFAULT_DEPTH_BIAS (0)

#define D3D11_DEFAULT_DEPTH_BIAS_CLAMP 0.0f
#define D3D11_DEFAULT_MAX_ANISOTROPY (16)

#define D3D11_DEFAULT_MIP_LOD_BIAS 0.0f
#define D3D11_DEFAULT_RENDER_TARGET_ARRAY_INDEX (0)

#define D3D11_DEFAULT_SAMPLE_MASK (0xffffffff)

#define D3D11_DEFAULT_SCISSOR_ENDX (0)

#define D3D11_DEFAULT_SCISSOR_ENDY (0)

#define D3D11_DEFAULT_SCISSOR_STARTX (0)

#define D3D11_DEFAULT_SCISSOR_STARTY (0)

#define D3D11_DEFAULT_SLOPE_SCALED_DEPTH_BIAS 0.0f
#define D3D11_DEFAULT_STENCIL_READ_MASK (0xff)

#define D3D11_DEFAULT_STENCIL_REFERENCE (0)

#define D3D11_DEFAULT_STENCIL_WRITE_MASK (0xff)

#define D3D11_DEFAULT_VIEWPORT_AND_SCISSORRECT_INDEX (0)

#define D3D11_DEFAULT_VIEWPORT_HEIGHT (0)

#define D3D11_DEFAULT_VIEWPORT_MAX_DEPTH 0.0f
#define D3D11_DEFAULT_VIEWPORT_MIN_DEPTH 0.0f
#define D3D11_DEFAULT_VIEWPORT_TOPLEFTX (0)

#define D3D11_DEFAULT_VIEWPORT_TOPLEFTY (0)

#define D3D11_DEFAULT_VIEWPORT_WIDTH (0)

#define D3D11_FLOAT32_MAX         (3.402823466e+38f)
#define D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT (32)

#define D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT (8)

#define D3D11_MAX_MAXANISOTROPY (16)

#define D3D11_MAX_MULTISAMPLE_SAMPLE_COUNT (32)

#define D3D11_VIEWPORT_BOUNDS_MAX (32767)

#define D3D11_VIEWPORT_BOUNDS_MIN (-32768)

#define D3D11_VIEWPORT_AND_SCISSORRECT_MAX_INDEX (15)

#define D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE (16)

#define D3D11_KEEP_RENDER_TARGETS_AND_DEPTH_STENCIL (0xffffffff)

#define D3D11_KEEP_UNORDERED_ACCESS_VIEWS (0xffffffff)

#define D3D11_SHADER_MAJOR_VERSION (5)

#define D3D11_SHADER_MAX_INSTANCES (65535)

#define D3D11_SHADER_MAX_INTERFACES (253)

#define D3D11_SHADER_MAX_INTERFACE_CALL_SITES (4096)

#define D3D11_SHADER_MAX_TYPES (65535)

#define D3D11_SHADER_MINOR_VERSION (0)

#define D3D11_VS_OUTPUT_REGISTER_COUNT (32)

#define D3D11_OMAC_SIZE (16)

#define D3D11_PS_CS_UAV_REGISTER_COMPONENTS (1)

#define D3D11_PS_CS_UAV_REGISTER_COUNT (8)

#define D3D11_PS_CS_UAV_REGISTER_READS_PER_INST (1)

#define D3D11_PS_CS_UAV_REGISTER_READ_PORTS (1)

#define D3D11_PS_FRONTFACING_DEFAULT_VALUE (0xffffffff)

#define D3D11_PS_FRONTFACING_FALSE_VALUE (0)

#define D3D11_PS_FRONTFACING_TRUE_VALUE (0xffffffff)

#define D3D11_PS_INPUT_REGISTER_COMPONENTS (4)

#define D3D11_PS_INPUT_REGISTER_COMPONENT_BIT_COUNT (32)

#define D3D11_PS_INPUT_REGISTER_COUNT (32)

#define D3D11_PS_INPUT_REGISTER_READS_PER_INST (2)

#define D3D11_PS_INPUT_REGISTER_READ_PORTS (1)

#define D3D11_PS_LEGACY_PIXEL_CENTER_FRACTIONAL_COMPONENT (0.0f)
#define D3D11_PS_OUTPUT_DEPTH_REGISTER_COMPONENTS (1)

#define D3D11_PS_OUTPUT_DEPTH_REGISTER_COMPONENT_BIT_COUNT (32)

#define D3D11_PS_OUTPUT_DEPTH_REGISTER_COUNT (1)

#define D3D11_PS_OUTPUT_MASK_REGISTER_COMPONENTS (1)

#define D3D11_PS_OUTPUT_MASK_REGISTER_COMPONENT_BIT_COUNT (32)

#define D3D11_PS_OUTPUT_MASK_REGISTER_COUNT (1)

#define D3D11_PS_OUTPUT_REGISTER_COMPONENTS (4)

#define D3D11_PS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT (32)

#define D3D11_PS_OUTPUT_REGISTER_COUNT (8)

#define D3D11_PS_PIXEL_CENTER_FRACTIONAL_COMPONENT (0.5f)
#define D3D11_RAW_UAV_SRV_BYTE_ALIGNMENT (16)

#define D3D11_REQ_BLEND_OBJECT_COUNT_PER_DEVICE (4096)

#define D3D11_REQ_BUFFER_RESOURCE_TEXEL_COUNT_2_TO_EXP (27)

#define D3D11_REQ_CONSTANT_BUFFER_ELEMENT_COUNT (4096)

#define D3D11_REQ_DEPTH_STENCIL_OBJECT_COUNT_PER_DEVICE (4096)

#define D3D11_REQ_DRAWINDEXED_INDEX_COUNT_2_TO_EXP (32)

#define D3D11_REQ_DRAW_VERTEX_COUNT_2_TO_EXP (32)

#define D3D11_REQ_FILTERING_HW_ADDRESSABLE_RESOURCE_DIMENSION (16384)

#define D3D11_REQ_GS_INVOCATION_32BIT_OUTPUT_COMPONENT_LIMIT (1024)

#define D3D11_REQ_IMMEDIATE_CONSTANT_BUFFER_ELEMENT_COUNT (4096)

#define D3D11_REQ_MAXANISOTROPY (16)

#define D3D11_REQ_MIP_LEVELS (15)

#define D3D11_REQ_MULTI_ELEMENT_STRUCTURE_SIZE_IN_BYTES (2048)

#define D3D11_REQ_RASTERIZER_OBJECT_COUNT_PER_DEVICE (4096)

#define D3D11_REQ_RENDER_TO_BUFFER_WINDOW_WIDTH (16384)

#define D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_A_TERM (128)

#define D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_B_TERM (0.25f)
#define D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_C_TERM (2048)

#define D3D11_REQ_RESOURCE_VIEW_COUNT_PER_DEVICE_2_TO_EXP (20)

#define D3D11_REQ_SAMPLER_OBJECT_COUNT_PER_DEVICE (4096)

#define D3D11_REQ_TEXTURE1D_ARRAY_AXIS_DIMENSION (2048)

#define D3D11_REQ_TEXTURE1D_U_DIMENSION (16384)

#define D3D11_REQ_TEXTURE2D_ARRAY_AXIS_DIMENSION (2048)

#define D3D11_REQ_TEXTURE2D_U_OR_V_DIMENSION (16384)

#define D3D11_REQ_TEXTURE3D_U_V_OR_W_DIMENSION (2048)

#define D3D11_REQ_TEXTURECUBE_DIMENSION (16384)

#define D3D11_RESINFO_INSTRUCTION_MISSING_COMPONENT_RETVAL (0)

#define D3D11_SHIFT_INSTRUCTION_PAD_VALUE (0)

#define D3D11_SHIFT_INSTRUCTION_SHIFT_VALUE_BIT_COUNT (5)

#define D3D11_SO_BUFFER_MAX_STRIDE_IN_BYTES (2048)

#define D3D11_SO_BUFFER_MAX_WRITE_WINDOW_IN_BYTES (512)

#define D3D11_SO_BUFFER_SLOT_COUNT (4)

#define D3D11_SO_DDI_REGISTER_INDEX_DENOTING_GAP (0xffffffff)

#define D3D11_SO_NO_RASTERIZED_STREAM (0xffffffff)

#define D3D11_SO_OUTPUT_COMPONENT_COUNT (128)

#define D3D11_SO_STREAM_COUNT (4)

#define D3D11_SPEC_DATE_DAY (16)

#define D3D11_SPEC_DATE_MONTH (5)

#define D3D11_SPEC_DATE_YEAR (2011)

#define D3D11_SPEC_VERSION                   (1.07)
#define D3D11_SRGB_GAMMA                     (2.2f)
#define D3D11_SRGB_TO_FLOAT_DENOMINATOR_1    (12.92f)
#define D3D11_SRGB_TO_FLOAT_DENOMINATOR_2    (1.055f)
#define D3D11_SRGB_TO_FLOAT_EXPONENT         (2.4f)
#define D3D11_SRGB_TO_FLOAT_OFFSET           (0.055f)
#define D3D11_SRGB_TO_FLOAT_THRESHOLD        (0.04045f)
#define D3D11_SRGB_TO_FLOAT_TOLERANCE_IN_ULP (0.5f)
#define D3D11_STANDARD_COMPONENT_BIT_COUNT (32)

#define D3D11_STANDARD_COMPONENT_BIT_COUNT_DOUBLED (64)

#define D3D11_STANDARD_MAXIMUM_ELEMENT_ALIGNMENT_BYTE_MULTIPLE (4)

#define D3D11_STANDARD_PIXEL_COMPONENT_COUNT (128)

#define D3D11_STANDARD_PIXEL_ELEMENT_COUNT (32)

#define D3D11_STANDARD_VECTOR_SIZE (4)

#define D3D11_STANDARD_VERTEX_ELEMENT_COUNT (32)

#define D3D11_STANDARD_VERTEX_TOTAL_COMPONENT_COUNT (64)

#endif
#ifndef _D3D11_1_CONSTANTS
#define _D3D11_1_CONSTANTS
#define D3D11_1_UAV_SLOT_COUNT (64)

#endif
#ifndef _D3D11_2_CONSTANTS
#define _D3D11_2_CONSTANTS
#define D3D11_2_TILED_RESOURCE_TILE_SIZE_IN_BYTES (0x10000)

#endif
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_DEFAULT {};
extern const DECLSPEC_SELECTANY CD3D11_DEFAULT D3D11_DEFAULT;
#endif
typedef enum D3D11_BLEND {
    D3D11_BLEND_ZERO = 1,
    D3D11_BLEND_ONE = 2,
    D3D11_BLEND_SRC_COLOR = 3,
    D3D11_BLEND_INV_SRC_COLOR = 4,
    D3D11_BLEND_SRC_ALPHA = 5,
    D3D11_BLEND_INV_SRC_ALPHA = 6,
    D3D11_BLEND_DEST_ALPHA = 7,
    D3D11_BLEND_INV_DEST_ALPHA = 8,
    D3D11_BLEND_DEST_COLOR = 9,
    D3D11_BLEND_INV_DEST_COLOR = 10,
    D3D11_BLEND_SRC_ALPHA_SAT = 11,
    D3D11_BLEND_BLEND_FACTOR = 14,
    D3D11_BLEND_INV_BLEND_FACTOR = 15,
    D3D11_BLEND_SRC1_COLOR = 16,
    D3D11_BLEND_INV_SRC1_COLOR = 17,
    D3D11_BLEND_SRC1_ALPHA = 18,
    D3D11_BLEND_INV_SRC1_ALPHA = 19
} D3D11_BLEND;
typedef enum D3D11_BLEND_OP {
    D3D11_BLEND_OP_ADD = 1,
    D3D11_BLEND_OP_SUBTRACT = 2,
    D3D11_BLEND_OP_REV_SUBTRACT = 3,
    D3D11_BLEND_OP_MIN = 4,
    D3D11_BLEND_OP_MAX = 5
} D3D11_BLEND_OP;
typedef enum D3D11_VIDEO_DECODER_BUFFER_TYPE {
    D3D11_VIDEO_DECODER_BUFFER_PICTURE_PARAMETERS = 0,
    D3D11_VIDEO_DECODER_BUFFER_MACROBLOCK_CONTROL = 1,
    D3D11_VIDEO_DECODER_BUFFER_RESIDUAL_DIFFERENCE = 2,
    D3D11_VIDEO_DECODER_BUFFER_DEBLOCKING_CONTROL = 3,
    D3D11_VIDEO_DECODER_BUFFER_INVERSE_QUANTIZATION_MATRIX = 4,
    D3D11_VIDEO_DECODER_BUFFER_SLICE_CONTROL = 5,
    D3D11_VIDEO_DECODER_BUFFER_BITSTREAM = 6,
    D3D11_VIDEO_DECODER_BUFFER_MOTION_VECTOR = 7,
    D3D11_VIDEO_DECODER_BUFFER_FILM_GRAIN = 8
} D3D11_VIDEO_DECODER_BUFFER_TYPE;
typedef enum D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE {
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_OPAQUE = 0,
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_BACKGROUND = 1,
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_DESTINATION = 2,
    D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE_SOURCE_STREAM = 3
} D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE;
typedef enum D3D11_VIDEO_PROCESSOR_OUTPUT_RATE {
    D3D11_VIDEO_PROCESSOR_OUTPUT_RATE_NORMAL = 0,
    D3D11_VIDEO_PROCESSOR_OUTPUT_RATE_HALF = 1,
    D3D11_VIDEO_PROCESSOR_OUTPUT_RATE_CUSTOM = 2
} D3D11_VIDEO_PROCESSOR_OUTPUT_RATE;
typedef enum D3D11_VIDEO_PROCESSOR_STEREO_FORMAT {
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_MONO = 0,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_HORIZONTAL = 1,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_VERTICAL = 2,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_SEPARATE = 3,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_MONO_OFFSET = 4,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_ROW_INTERLEAVED = 5,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_COLUMN_INTERLEAVED = 6,
    D3D11_VIDEO_PROCESSOR_STEREO_FORMAT_CHECKERBOARD = 7
} D3D11_VIDEO_PROCESSOR_STEREO_FORMAT;
typedef enum D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE {
    D3D11_VIDEO_PROCESSOR_STEREO_FLIP_NONE = 0,
    D3D11_VIDEO_PROCESSOR_STEREO_FLIP_FRAME0 = 1,
    D3D11_VIDEO_PROCESSOR_STEREO_FLIP_FRAME1 = 2
} D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE;
typedef enum D3D11_VIDEO_PROCESSOR_ROTATION {
    D3D11_VIDEO_PROCESSOR_ROTATION_IDENTITY = 0,
    D3D11_VIDEO_PROCESSOR_ROTATION_90 = 1,
    D3D11_VIDEO_PROCESSOR_ROTATION_180 = 2,
    D3D11_VIDEO_PROCESSOR_ROTATION_270 = 3
} D3D11_VIDEO_PROCESSOR_ROTATION;
typedef enum D3D11_VIDEO_PROCESSOR_DEVICE_CAPS {
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_LINEAR_SPACE = 0x1,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_xvYCC = 0x2,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_RGB_RANGE_CONVERSION = 0x4,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_YCbCr_MATRIX_CONVERSION = 0x8,
    D3D11_VIDEO_PROCESSOR_DEVICE_CAPS_NOMINAL_RANGE = 0x10
} D3D11_VIDEO_PROCESSOR_DEVICE_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_FEATURE_CAPS {
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ALPHA_FILL = 0x1,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_CONSTRICTION = 0x2,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_LUMA_KEY = 0x4,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ALPHA_PALETTE = 0x8,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_LEGACY = 0x10,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_STEREO = 0x20,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ROTATION = 0x40,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_ALPHA_STREAM = 0x80,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_PIXEL_ASPECT_RATIO = 0x100,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_MIRROR = 0x200,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_SHADER_USAGE = 0x400,
    D3D11_VIDEO_PROCESSOR_FEATURE_CAPS_METADATA_HDR10 = 0x800
} D3D11_VIDEO_PROCESSOR_FEATURE_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_FILTER_CAPS {
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_BRIGHTNESS = 0x1,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_CONTRAST = 0x2,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_HUE = 0x4,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_SATURATION = 0x8,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_NOISE_REDUCTION = 0x10,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_EDGE_ENHANCEMENT = 0x20,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_ANAMORPHIC_SCALING = 0x40,
    D3D11_VIDEO_PROCESSOR_FILTER_CAPS_STEREO_ADJUSTMENT = 0x80
} D3D11_VIDEO_PROCESSOR_FILTER_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_FORMAT_CAPS {
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_RGB_INTERLACED = 0x1,
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_RGB_PROCAMP = 0x2,
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_RGB_LUMA_KEY = 0x4,
    D3D11_VIDEO_PROCESSOR_FORMAT_CAPS_PALETTE_INTERLACED = 0x8
} D3D11_VIDEO_PROCESSOR_FORMAT_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS {
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_DENOISE = 0x1,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_DERINGING = 0x2,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_EDGE_ENHANCEMENT = 0x4,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_COLOR_CORRECTION = 0x8,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_FLESH_TONE_MAPPING = 0x10,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_IMAGE_STABILIZATION = 0x20,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_SUPER_RESOLUTION = 0x40,
    D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS_ANAMORPHIC_SCALING = 0x80
} D3D11_VIDEO_PROCESSOR_AUTO_STREAM_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_STEREO_CAPS {
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_MONO_OFFSET = 0x1,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_ROW_INTERLEAVED = 0x2,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_COLUMN_INTERLEAVED = 0x4,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_CHECKERBOARD = 0x8,
    D3D11_VIDEO_PROCESSOR_STEREO_CAPS_FLIP_MODE = 0x10
} D3D11_VIDEO_PROCESSOR_STEREO_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS {
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_BLEND = 0x1,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_BOB = 0x2,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_ADAPTIVE = 0x4,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_DEINTERLACE_MOTION_COMPENSATION = 0x8,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_INVERSE_TELECINE = 0x10,
    D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS_FRAME_RATE_CONVERSION = 0x20
} D3D11_VIDEO_PROCESSOR_PROCESSOR_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS {
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_32 = 0x1,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_22 = 0x2,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_2224 = 0x4,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_2332 = 0x8,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_32322 = 0x10,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_55 = 0x20,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_64 = 0x40,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_87 = 0x80,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_222222222223 = 0x100,
    D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS_OTHER = 0x80000000
} D3D11_VIDEO_PROCESSOR_ITELECINE_CAPS;
typedef enum D3D11_CONTENT_PROTECTION_CAPS {
    D3D11_CONTENT_PROTECTION_CAPS_SOFTWARE = 0x1,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE = 0x2,
    D3D11_CONTENT_PROTECTION_CAPS_PROTECTION_ALWAYS_ON = 0x4,
    D3D11_CONTENT_PROTECTION_CAPS_PARTIAL_DECRYPTION = 0x8,
    D3D11_CONTENT_PROTECTION_CAPS_CONTENT_KEY = 0x10,
    D3D11_CONTENT_PROTECTION_CAPS_FRESHEN_SESSION_KEY = 0x20,
    D3D11_CONTENT_PROTECTION_CAPS_ENCRYPTED_READ_BACK = 0x40,
    D3D11_CONTENT_PROTECTION_CAPS_ENCRYPTED_READ_BACK_KEY = 0x80,
    D3D11_CONTENT_PROTECTION_CAPS_SEQUENTIAL_CTR_IV = 0x100,
    D3D11_CONTENT_PROTECTION_CAPS_ENCRYPT_SLICEDATA_ONLY = 0x200,
    D3D11_CONTENT_PROTECTION_CAPS_DECRYPTION_BLT = 0x400,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_PROTECT_UNCOMPRESSED = 0x800,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_PROTECTED_MEMORY_PAGEABLE = 0x1000,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_TEARDOWN = 0x2000,
    D3D11_CONTENT_PROTECTION_CAPS_HARDWARE_DRM_COMMUNICATION = 0x4000
} D3D11_CONTENT_PROTECTION_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE {
    D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE_UNDEFINED = 0x0,
    D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE_16_235 = 0x1,
    D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE_0_255 = 0x2
} D3D11_VIDEO_PROCESSOR_NOMINAL_RANGE;
typedef enum D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE {
    D3D11_PROCESSIDTYPE_UNKNOWN = 0x0,
    D3D11_PROCESSIDTYPE_DWM = 0x1,
    D3D11_PROCESSIDTYPE_HANDLE = 0x2
} D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE;
typedef enum D3D11_BUS_TYPE {
    D3D11_BUS_TYPE_OTHER = 0x0,
    D3D11_BUS_TYPE_PCI = 0x1,
    D3D11_BUS_TYPE_PCIX = 0x2,
    D3D11_BUS_TYPE_PCIEXPRESS = 0x3,
    D3D11_BUS_TYPE_AGP = 0x4,
    D3D11_BUS_IMPL_MODIFIER_INSIDE_OF_CHIPSET = 0x10000,
    D3D11_BUS_IMPL_MODIFIER_TRACKS_ON_MOTHER_BOARD_TO_CHIP = 0x20000,
    D3D11_BUS_IMPL_MODIFIER_TRACKS_ON_MOTHER_BOARD_TO_SOCKET = 0x30000,
    D3D11_BUS_IMPL_MODIFIER_DAUGHTER_BOARD_CONNECTOR = 0x40000,
    D3D11_BUS_IMPL_MODIFIER_DAUGHTER_BOARD_CONNECTOR_INSIDE_OF_NUAE = 0x50000,
    D3D11_BUS_IMPL_MODIFIER_NON_STANDARD = 0x80000000
} D3D11_BUS_TYPE;
typedef struct D3D11_BOX {
    UINT left;
    UINT top;
    UINT front;
    UINT right;
    UINT bottom;
    UINT back;
} D3D11_BOX;
typedef struct D3D11_BUFFER_RTV {
    __C89_NAMELESS union {
        UINT FirstElement;
        UINT ElementOffset;
    } __C89_NAMELESSUNIONNAME1;
    __C89_NAMELESS union {
        UINT NumElements;
        UINT ElementWidth;
    } __C89_NAMELESSUNIONNAME2;
} D3D11_BUFFER_RTV;
typedef struct D3D11_BUFFER_SRV {
    __C89_NAMELESS union {
        UINT FirstElement;
        UINT ElementOffset;
    } __C89_NAMELESSUNIONNAME1;
    __C89_NAMELESS union {
        UINT NumElements;
        UINT ElementWidth;
    } __C89_NAMELESSUNIONNAME2;
} D3D11_BUFFER_SRV;
typedef struct D3D11_BUFFER_UAV {
    UINT FirstElement;
    UINT NumElements;
    UINT Flags;
} D3D11_BUFFER_UAV;
typedef struct D3D11_BUFFEREX_SRV {
    UINT FirstElement;
    UINT NumElements;
    UINT Flags;
} D3D11_BUFFEREX_SRV;
typedef struct D3D11_CLASS_INSTANCE_DESC {
    UINT InstanceId;
    UINT InstanceIndex;
    UINT TypeId;
    UINT ConstantBuffer;
    UINT BaseConstantBufferOffset;
    UINT BaseTexture;
    UINT BaseSampler;
    WINBOOL Created;
} D3D11_CLASS_INSTANCE_DESC;
typedef enum D3D11_COMPARISON_FUNC {
    D3D11_COMPARISON_NEVER = 1,
    D3D11_COMPARISON_LESS = 2,
    D3D11_COMPARISON_EQUAL = 3,
    D3D11_COMPARISON_LESS_EQUAL = 4,
    D3D11_COMPARISON_GREATER = 5,
    D3D11_COMPARISON_NOT_EQUAL = 6,
    D3D11_COMPARISON_GREATER_EQUAL = 7,
    D3D11_COMPARISON_ALWAYS = 8
} D3D11_COMPARISON_FUNC;
typedef enum D3D11_COUNTER {
    D3D11_COUNTER_DEVICE_DEPENDENT_0 = 0x40000000
} D3D11_COUNTER;
typedef struct D3D11_COUNTER_DESC {
    D3D11_COUNTER Counter;
    UINT MiscFlags;
} D3D11_COUNTER_DESC;
typedef struct D3D11_COUNTER_INFO {
    D3D11_COUNTER LastDeviceDependentCounter;
    UINT NumSimultaneousCounters;
    UINT8 NumDetectableParallelUnits;
} D3D11_COUNTER_INFO;
typedef enum D3D11_COUNTER_TYPE {
    D3D11_COUNTER_TYPE_FLOAT32 = 0,
    D3D11_COUNTER_TYPE_UINT16 = 1,
    D3D11_COUNTER_TYPE_UINT32 = 2,
    D3D11_COUNTER_TYPE_UINT64 = 3
} D3D11_COUNTER_TYPE;
typedef enum D3D11_CULL_MODE {
    D3D11_CULL_NONE = 1,
    D3D11_CULL_FRONT = 2,
    D3D11_CULL_BACK = 3
} D3D11_CULL_MODE;
typedef enum D3D11_DEPTH_WRITE_MASK {
    D3D11_DEPTH_WRITE_MASK_ZERO = 0,
    D3D11_DEPTH_WRITE_MASK_ALL = 1
} D3D11_DEPTH_WRITE_MASK;
typedef enum D3D11_STANDARD_MULTISAMPLE_QUALITY_LEVELS {
    D3D11_STANDARD_MULTISAMPLE_PATTERN = 0xffffffff,
    D3D11_CENTER_MULTISAMPLE_PATTERN = 0xfffffffe
} D3D11_STANDARD_MULTISAMPLE_QUALITY_LEVELS;
typedef enum D3D11_DEVICE_CONTEXT_TYPE {
    D3D11_DEVICE_CONTEXT_IMMEDIATE = 0,
    D3D11_DEVICE_CONTEXT_DEFERRED = 1
} D3D11_DEVICE_CONTEXT_TYPE;
typedef enum D3D11_DSV_DIMENSION {
    D3D11_DSV_DIMENSION_UNKNOWN = 0,
    D3D11_DSV_DIMENSION_TEXTURE1D = 1,
    D3D11_DSV_DIMENSION_TEXTURE1DARRAY = 2,
    D3D11_DSV_DIMENSION_TEXTURE2D = 3,
    D3D11_DSV_DIMENSION_TEXTURE2DARRAY = 4,
    D3D11_DSV_DIMENSION_TEXTURE2DMS = 5,
    D3D11_DSV_DIMENSION_TEXTURE2DMSARRAY = 6
} D3D11_DSV_DIMENSION;
typedef enum D3D11_FEATURE {
    D3D11_FEATURE_THREADING = 0,
    D3D11_FEATURE_DOUBLES = 1,
    D3D11_FEATURE_FORMAT_SUPPORT = 2,
    D3D11_FEATURE_FORMAT_SUPPORT2 = 3,
    D3D11_FEATURE_D3D10_X_HARDWARE_OPTIONS = 4,
    D3D11_FEATURE_D3D11_OPTIONS = 5,
    D3D11_FEATURE_ARCHITECTURE_INFO = 6,
    D3D11_FEATURE_D3D9_OPTIONS = 7,
    D3D11_FEATURE_SHADER_MIN_PRECISION_SUPPORT = 8,
    D3D11_FEATURE_D3D9_SHADOW_SUPPORT = 9,
    D3D11_FEATURE_D3D11_OPTIONS1 = 10,
    D3D11_FEATURE_D3D9_SIMPLE_INSTANCING_SUPPORT = 11,
    D3D11_FEATURE_MARKER_SUPPORT = 12,
    D3D11_FEATURE_D3D9_OPTIONS1 = 13,
    D3D11_FEATURE_D3D11_OPTIONS2 = 14,
    D3D11_FEATURE_D3D11_OPTIONS3 = 15,
    D3D11_FEATURE_GPU_VIRTUAL_ADDRESS_SUPPORT = 16,
    D3D11_FEATURE_D3D11_OPTIONS4 = 17,
    D3D11_FEATURE_SHADER_CACHE = 18,
    D3D11_FEATURE_D3D11_OPTIONS5 = 19
} D3D11_FEATURE;
typedef struct D3D11_FEATURE_DATA_THREADING {
    WINBOOL DriverConcurrentCreates;
    WINBOOL DriverCommandLists;
} D3D11_FEATURE_DATA_THREADING;
typedef struct D3D11_FEATURE_DATA_DOUBLES {
    WINBOOL DoublePrecisionFloatShaderOps;
} D3D11_FEATURE_DATA_DOUBLES;
typedef struct D3D11_FEATURE_DATA_FORMAT_SUPPORT {
    DXGI_FORMAT InFormat;
    UINT OutFormatSupport;
} D3D11_FEATURE_DATA_FORMAT_SUPPORT;
typedef struct D3D11_FEATURE_DATA_FORMAT_SUPPORT2 {
    DXGI_FORMAT InFormat;
    UINT OutFormatSupport2;
} D3D11_FEATURE_DATA_FORMAT_SUPPORT2;
typedef struct D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS {
    WINBOOL ComputeShaders_Plus_RawAndStructuredBuffers_Via_Shader_4_x;
} D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS;
typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS {
    WINBOOL OutputMergerLogicOp;
    WINBOOL UAVOnlyRenderingForcedSampleCount;
    WINBOOL DiscardAPIsSeenByDriver;
    WINBOOL FlagsForUpdateAndCopySeenByDriver;
    WINBOOL ClearView;
    WINBOOL CopyWithOverlap;
    WINBOOL ConstantBufferPartialUpdate;
    WINBOOL ConstantBufferOffsetting;
    WINBOOL MapNoOverwriteOnDynamicConstantBuffer;
    WINBOOL MapNoOverwriteOnDynamicBufferSRV;
    WINBOOL MultisampleRTVWithForcedSampleCountOne;
    WINBOOL SAD4ShaderInstructions;
    WINBOOL ExtendedDoublesShaderInstructions;
    WINBOOL ExtendedResourceSharing;
} D3D11_FEATURE_DATA_D3D11_OPTIONS;
typedef struct D3D11_FEATURE_DATA_ARCHITECTURE_INFO {
    WINBOOL TileBasedDeferredRenderer;
} D3D11_FEATURE_DATA_ARCHITECTURE_INFO;
typedef struct D3D11_FEATURE_DATA_D3D9_OPTIONS {
    WINBOOL FullNonPow2TextureSupport;
} D3D11_FEATURE_DATA_D3D9_OPTIONS;
typedef struct D3D11_FEATURE_DATA_D3D9_SHADOW_SUPPORT {
    WINBOOL SupportsDepthAsTextureWithLessEqualComparisonFilter;
} D3D11_FEATURE_DATA_D3D9_SHADOW_SUPPORT;
typedef enum D3D11_SHADER_MIN_PRECISION_SUPPORT {
    D3D11_SHADER_MIN_PRECISION_10_BIT = 0x1,
    D3D11_SHADER_MIN_PRECISION_16_BIT = 0x2
} D3D11_SHADER_MIN_PRECISION_SUPPORT;
typedef struct D3D11_FEATURE_DATA_SHADER_MIN_PRECISION_SUPPORT {
    UINT PixelShaderMinPrecision;
    UINT AllOtherShaderStagesMinPrecision;
} D3D11_FEATURE_DATA_SHADER_MIN_PRECISION_SUPPORT;
typedef enum D3D11_TILED_RESOURCES_TIER {
    D3D11_TILED_RESOURCES_NOT_SUPPORTED = 0x0,
    D3D11_TILED_RESOURCES_TIER_1 = 0x1,
    D3D11_TILED_RESOURCES_TIER_2 = 0x2,
    D3D11_TILED_RESOURCES_TIER_3 = 0x3
} D3D11_TILED_RESOURCES_TIER;
typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS1 {
    D3D11_TILED_RESOURCES_TIER TiledResourcesTier;
    WINBOOL MinMaxFiltering;
    WINBOOL ClearViewAlsoSupportsDepthOnlyFormats;
    WINBOOL MapOnDefaultBuffers;
} D3D11_FEATURE_DATA_D3D11_OPTIONS1;
typedef struct D3D11_FEATURE_DATA_D3D9_SIMPLE_INSTANCING_SUPPORT {
    WINBOOL SimpleInstancingSupported;
} D3D11_FEATURE_DATA_D3D9_SIMPLE_INSTANCING_SUPPORT;
typedef struct D3D11_FEATURE_DATA_MARKER_SUPPORT {
    WINBOOL Profile;
} D3D11_FEATURE_DATA_MARKER_SUPPORT;
typedef struct D3D11_FEATURE_DATA_D3D9_OPTIONS1 {
    WINBOOL FullNonPow2TextureSupported;
    WINBOOL DepthAsTextureWithLessEqualComparisonFilterSupported;
    WINBOOL SimpleInstancingSupported;
    WINBOOL TextureCubeFaceRenderTargetWithNonCubeDepthStencilSupported;
} D3D11_FEATURE_DATA_D3D9_OPTIONS1;
typedef enum D3D11_CONSERVATIVE_RASTERIZATION_TIER {
    D3D11_CONSERVATIVE_RASTERIZATION_NOT_SUPPORTED = 0x0,
    D3D11_CONSERVATIVE_RASTERIZATION_TIER_1 = 0x1,
    D3D11_CONSERVATIVE_RASTERIZATION_TIER_2 = 0x2,
    D3D11_CONSERVATIVE_RASTERIZATION_TIER_3 = 0x3
} D3D11_CONSERVATIVE_RASTERIZATION_TIER;
typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS2 {
    WINBOOL PSSpecifiedStencilRefSupported;
    WINBOOL TypedUAVLoadAdditionalFormats;
    WINBOOL ROVsSupported;
    D3D11_CONSERVATIVE_RASTERIZATION_TIER ConservativeRasterizationTier;
    D3D11_TILED_RESOURCES_TIER TiledResourcesTier;
    WINBOOL MapOnDefaultTextures;
    WINBOOL StandardSwizzle;
    WINBOOL UnifiedMemoryArchitecture;
} D3D11_FEATURE_DATA_D3D11_OPTIONS2;
typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS3 {
    WINBOOL VPAndRTArrayIndexFromAnyShaderFeedingRasterizer;
} D3D11_FEATURE_DATA_D3D11_OPTIONS3;
typedef struct D3D11_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT {
    UINT MaxGPUVirtualAddressBitsPerResource;
    UINT MaxGPUVirtualAddressBitsPerProcess;
} D3D11_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT;
typedef enum D3D11_SHADER_CACHE_SUPPORT_FLAGS {
    D3D11_SHADER_CACHE_SUPPORT_NONE = 0x0,
    D3D11_SHADER_CACHE_SUPPORT_AUTOMATIC_INPROC_CACHE = 0x1,
    D3D11_SHADER_CACHE_SUPPORT_AUTOMATIC_DISK_CACHE = 0x2
} D3D11_SHADER_CACHE_SUPPORT_FLAGS;
typedef struct D3D11_FEATURE_DATA_SHADER_CACHE {
    UINT SupportFlags;
} D3D11_FEATURE_DATA_SHADER_CACHE;
typedef enum D3D11_SHARED_RESOURCE_TIER {
    D3D11_SHARED_RESOURCE_TIER_0 = 0,
    D3D11_SHARED_RESOURCE_TIER_1 = 1,
    D3D11_SHARED_RESOURCE_TIER_2 = 2,
    D3D11_SHARED_RESOURCE_TIER_3 = 3
} D3D11_SHARED_RESOURCE_TIER;
typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS5 {
    D3D11_SHARED_RESOURCE_TIER SharedResourceTier;
} D3D11_FEATURE_DATA_D3D11_OPTIONS5;
typedef enum D3D11_FILL_MODE {
    D3D11_FILL_WIREFRAME = 2,
    D3D11_FILL_SOLID = 3
} D3D11_FILL_MODE;
typedef enum D3D11_FILTER_TYPE {
    D3D11_FILTER_TYPE_POINT = 0,
    D3D11_FILTER_TYPE_LINEAR = 1
} D3D11_FILTER_TYPE;
#define D3D11_MIN_FILTER_SHIFT (4)

#define D3D11_MAG_FILTER_SHIFT (2)

#define D3D11_MIP_FILTER_SHIFT (0)

#define D3D11_FILTER_TYPE_MASK (0x3)

#define D3D11_COMPARISON_FILTERING_BIT (0x80)

#define D3D11_ANISOTROPIC_FILTERING_BIT (0x40)

#define D3D11_ENCODE_BASIC_FILTER(min, mag, mip, bComparison) \
    ((D3D11_FILTER)(((bComparison) ? D3D11_COMPARISON_FILTERING_BIT : 0 ) | \
                    (((min)&D3D11_FILTER_TYPE_MASK) << D3D11_MIN_FILTER_SHIFT) | \
                    (((mag)&D3D11_FILTER_TYPE_MASK) << D3D11_MAG_FILTER_SHIFT) | \
                    (((mip)&D3D11_FILTER_TYPE_MASK) << D3D11_MIP_FILTER_SHIFT)))
#define D3D11_ENCODE_ANISOTROPIC_FILTER(bComparison) \
    ((D3D11_FILTER)(D3D11_ANISOTROPIC_FILTERING_BIT | \
                    D3D11_ENCODE_BASIC_FILTER(D3D11_FILTER_TYPE_LINEAR,D3D11_FILTER_TYPE_LINEAR, \
                                              D3D11_FILTER_TYPE_LINEAR,bComparison)))
#define D3D11_DECODE_MIN_FILTER(d3d11Filter) \
    ((D3D11_FILTER_TYPE)(((d3d11Filter) >> D3D11_MIN_FILTER_SHIFT) & D3D11_FILTER_TYPE_MASK))
#define D3D11_DECODE_MAG_FILTER(d3d11Filter) \
    ((D3D11_FILTER_TYPE)(((d3d11Filter) >> D3D11_MAG_FILTER_SHIFT) & D3D11_FILTER_TYPE_MASK))
#define D3D11_DECODE_MIP_FILTER(d3d11Filter) \
    ((D3D11_FILTER_TYPE)(((d3d11Filter) >> D3D11_MIP_FILTER_SHIFT) & D3D11_FILTER_TYPE_MASK))
#define D3D11_DECODE_IS_COMPARISON_FILTER(d3d11Filter) ((d3d11Filter) & D3D11_COMPARISON_FILTERING_BIT)
#define D3D11_DECODE_IS_ANISOTROPIC_FILTER(d3d11Filter) \
    (((d3d11Filter) & D3D11_ANISOTROPIC_FILTERING_BIT ) \
     && (D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MIN_FILTER(d3d11Filter)) \
     && (D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MAG_FILTER(d3d11Filter)) \
     && (D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MIP_FILTER(d3d11Filter)))
typedef enum D3D11_FILTER {
    D3D11_FILTER_MIN_MAG_MIP_POINT = 0x0,
    D3D11_FILTER_MIN_MAG_POINT_MIP_LINEAR = 0x1,
    D3D11_FILTER_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x4,
    D3D11_FILTER_MIN_POINT_MAG_MIP_LINEAR = 0x5,
    D3D11_FILTER_MIN_LINEAR_MAG_MIP_POINT = 0x10,
    D3D11_FILTER_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x11,
    D3D11_FILTER_MIN_MAG_LINEAR_MIP_POINT = 0x14,
    D3D11_FILTER_MIN_MAG_MIP_LINEAR = 0x15,
    D3D11_FILTER_ANISOTROPIC = 0x55,
    D3D11_FILTER_COMPARISON_MIN_MAG_MIP_POINT = 0x80,
    D3D11_FILTER_COMPARISON_MIN_MAG_POINT_MIP_LINEAR = 0x81,
    D3D11_FILTER_COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x84,
    D3D11_FILTER_COMPARISON_MIN_POINT_MAG_MIP_LINEAR = 0x85,
    D3D11_FILTER_COMPARISON_MIN_LINEAR_MAG_MIP_POINT = 0x90,
    D3D11_FILTER_COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x91,
    D3D11_FILTER_COMPARISON_MIN_MAG_LINEAR_MIP_POINT = 0x94,
    D3D11_FILTER_COMPARISON_MIN_MAG_MIP_LINEAR = 0x95,
    D3D11_FILTER_COMPARISON_ANISOTROPIC = 0xd5,
    D3D11_FILTER_MINIMUM_MIN_MAG_MIP_POINT = 0x100,
    D3D11_FILTER_MINIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x101,
    D3D11_FILTER_MINIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x104,
    D3D11_FILTER_MINIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x105,
    D3D11_FILTER_MINIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x110,
    D3D11_FILTER_MINIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x111,
    D3D11_FILTER_MINIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x114,
    D3D11_FILTER_MINIMUM_MIN_MAG_MIP_LINEAR = 0x115,
    D3D11_FILTER_MINIMUM_ANISOTROPIC = 0x155,
    D3D11_FILTER_MAXIMUM_MIN_MAG_MIP_POINT = 0x180,
    D3D11_FILTER_MAXIMUM_MIN_MAG_POINT_MIP_LINEAR = 0x181,
    D3D11_FILTER_MAXIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT = 0x184,
    D3D11_FILTER_MAXIMUM_MIN_POINT_MAG_MIP_LINEAR = 0x185,
    D3D11_FILTER_MAXIMUM_MIN_LINEAR_MAG_MIP_POINT = 0x190,
    D3D11_FILTER_MAXIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR = 0x191,
    D3D11_FILTER_MAXIMUM_MIN_MAG_LINEAR_MIP_POINT = 0x194,
    D3D11_FILTER_MAXIMUM_MIN_MAG_MIP_LINEAR = 0x195,
    D3D11_FILTER_MAXIMUM_ANISOTROPIC = 0x1d5
} D3D11_FILTER;
typedef enum D3D11_DSV_FLAG {
    D3D11_DSV_READ_ONLY_DEPTH = 0x1,
    D3D11_DSV_READ_ONLY_STENCIL = 0x2
} D3D11_DSV_FLAG;
typedef enum D3D11_BUFFEREX_SRV_FLAG {
    D3D11_BUFFEREX_SRV_FLAG_RAW = 0x1
} D3D11_BUFFEREX_SRV_FLAG;
typedef enum D3D11_UAV_FLAG {
    D3D11_BUFFER_UAV_FLAG_RAW = 0x1,
    D3D11_BUFFER_UAV_FLAG_APPEND = 0x2,
    D3D11_BUFFER_UAV_FLAG_COUNTER = 0x4
} D3D11_UAV_FLAG;
typedef enum D3D11_INPUT_CLASSIFICATION {
    D3D11_INPUT_PER_VERTEX_DATA = 0,
    D3D11_INPUT_PER_INSTANCE_DATA = 1
} D3D11_INPUT_CLASSIFICATION;
#define D3D11_APPEND_ALIGNED_ELEMENT (0xffffffff)

typedef struct D3D11_INPUT_ELEMENT_DESC {
    LPCSTR SemanticName;
    UINT SemanticIndex;
    DXGI_FORMAT Format;
    UINT InputSlot;
    UINT AlignedByteOffset;
    D3D11_INPUT_CLASSIFICATION InputSlotClass;
    UINT InstanceDataStepRate;
} D3D11_INPUT_ELEMENT_DESC;
typedef enum D3D11_MAP {
    D3D11_MAP_READ = 1,
    D3D11_MAP_WRITE = 2,
    D3D11_MAP_READ_WRITE = 3,
    D3D11_MAP_WRITE_DISCARD = 4,
    D3D11_MAP_WRITE_NO_OVERWRITE = 5
} D3D11_MAP;
typedef enum D3D11_MAP_FLAG {
    D3D11_MAP_FLAG_DO_NOT_WAIT = 0x100000
} D3D11_MAP_FLAG;
typedef enum D3D11_RAISE_FLAG {
    D3D11_RAISE_FLAG_DRIVER_INTERNAL_ERROR = 0x1
} D3D11_RAISE_FLAG;
typedef struct D3D11_QUERY_DATA_SO_STATISTICS {
    UINT64 NumPrimitivesWritten;
    UINT64 PrimitivesStorageNeeded;
} D3D11_QUERY_DATA_SO_STATISTICS;
typedef struct D3D11_MAPPED_SUBRESOURCE {
    void *pData;
    UINT RowPitch;
    UINT DepthPitch;
} D3D11_MAPPED_SUBRESOURCE;
typedef enum D3D11_QUERY {
    D3D11_QUERY_EVENT = 0,
    D3D11_QUERY_OCCLUSION = 1,
    D3D11_QUERY_TIMESTAMP = 2,
    D3D11_QUERY_TIMESTAMP_DISJOINT = 3,
    D3D11_QUERY_PIPELINE_STATISTICS = 4,
    D3D11_QUERY_OCCLUSION_PREDICATE = 5,
    D3D11_QUERY_SO_STATISTICS = 6,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE = 7,
    D3D11_QUERY_SO_STATISTICS_STREAM0 = 8,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM0 = 9,
    D3D11_QUERY_SO_STATISTICS_STREAM1 = 10,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM1 = 11,
    D3D11_QUERY_SO_STATISTICS_STREAM2 = 12,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM2 = 13,
    D3D11_QUERY_SO_STATISTICS_STREAM3 = 14,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM3 = 15
} D3D11_QUERY;
typedef enum D3D11_QUERY_MISC_FLAG {
    D3D11_QUERY_MISC_PREDICATEHINT = 0x1
} D3D11_QUERY_MISC_FLAG;
typedef enum D3D11_ASYNC_GETDATA_FLAG {
    D3D11_ASYNC_GETDATA_DONOTFLUSH = 0x1
} D3D11_ASYNC_GETDATA_FLAG;
typedef enum D3D11_RESOURCE_MISC_FLAG {
    D3D11_RESOURCE_MISC_GENERATE_MIPS = 0x1,
    D3D11_RESOURCE_MISC_SHARED = 0x2,
    D3D11_RESOURCE_MISC_TEXTURECUBE = 0x4,
    D3D11_RESOURCE_MISC_DRAWINDIRECT_ARGS = 0x10,
    D3D11_RESOURCE_MISC_BUFFER_ALLOW_RAW_VIEWS = 0x20,
    D3D11_RESOURCE_MISC_BUFFER_STRUCTURED = 0x40,
    D3D11_RESOURCE_MISC_RESOURCE_CLAMP = 0x80,
    D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX = 0x100,
    D3D11_RESOURCE_MISC_GDI_COMPATIBLE = 0x200,
    D3D11_RESOURCE_MISC_SHARED_NTHANDLE = 0x800,
    D3D11_RESOURCE_MISC_RESTRICTED_CONTENT = 0x1000,
    D3D11_RESOURCE_MISC_RESTRICT_SHARED_RESOURCE = 0x2000,
    D3D11_RESOURCE_MISC_RESTRICT_SHARED_RESOURCE_DRIVER = 0x4000,
    D3D11_RESOURCE_MISC_GUARDED = 0x8000,
    D3D11_RESOURCE_MISC_TILE_POOL = 0x20000,
    D3D11_RESOURCE_MISC_TILED = 0x40000,
    D3D11_RESOURCE_MISC_HW_PROTECTED = 0x80000
} D3D11_RESOURCE_MISC_FLAG;
typedef struct D3D11_QUERY_DESC {
    D3D11_QUERY Query;
    UINT MiscFlags;
} D3D11_QUERY_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_QUERY_DESC : public D3D11_QUERY_DESC {
    CD3D11_QUERY_DESC() {}
    ~CD3D11_QUERY_DESC() {}
    explicit CD3D11_QUERY_DESC(const D3D11_QUERY_DESC &other) : D3D11_QUERY_DESC(other) {}
    explicit CD3D11_QUERY_DESC(D3D11_QUERY query, UINT misc_flags = 0) {
        Query = query;
        MiscFlags = misc_flags;
    }
    operator const D3D11_QUERY_DESC&() const {
        return *this;
    }
};
#endif
typedef struct D3D11_RASTERIZER_DESC {
    D3D11_FILL_MODE FillMode;
    D3D11_CULL_MODE CullMode;
    WINBOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    WINBOOL DepthClipEnable;
    WINBOOL ScissorEnable;
    WINBOOL MultisampleEnable;
    WINBOOL AntialiasedLineEnable;
} D3D11_RASTERIZER_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_RASTERIZER_DESC : public D3D11_RASTERIZER_DESC {
    CD3D11_RASTERIZER_DESC() {}
    explicit CD3D11_RASTERIZER_DESC(const D3D11_RASTERIZER_DESC &o) : D3D11_RASTERIZER_DESC(o) {}
    explicit CD3D11_RASTERIZER_DESC(CD3D11_DEFAULT) {
        FillMode = D3D11_FILL_SOLID;
        CullMode = D3D11_CULL_BACK;
        FrontCounterClockwise = FALSE;
        DepthBias = D3D11_DEFAULT_DEPTH_BIAS;
        DepthBiasClamp = D3D11_DEFAULT_DEPTH_BIAS_CLAMP;
        SlopeScaledDepthBias = D3D11_DEFAULT_SLOPE_SCALED_DEPTH_BIAS;
        DepthClipEnable = TRUE;
        ScissorEnable = FALSE;
        MultisampleEnable = FALSE;
        AntialiasedLineEnable = FALSE;
    }
    explicit CD3D11_RASTERIZER_DESC(D3D11_FILL_MODE fillMode, D3D11_CULL_MODE cullMode,
            WINBOOL frontCounterClockwise, INT depthBias, FLOAT depthBiasClamp, FLOAT slopeScaledDepthBias,
            BOOL depthClipEnable, WINBOOL scissorEnable, WINBOOL multisampleEnable, WINBOOL antialiasedLineEnable) {
        FillMode = fillMode;
        CullMode = cullMode;
        FrontCounterClockwise = frontCounterClockwise;
        DepthBias = depthBias;
        DepthBiasClamp = depthBiasClamp;
        SlopeScaledDepthBias = slopeScaledDepthBias;
        DepthClipEnable = depthClipEnable;
        ScissorEnable = scissorEnable;
        MultisampleEnable = multisampleEnable;
        AntialiasedLineEnable = antialiasedLineEnable;
    }
    ~CD3D11_RASTERIZER_DESC() {}
    operator const D3D11_RASTERIZER_DESC&() const { return *this; }
};
#endif
typedef enum D3D11_RESOURCE_DIMENSION {
    D3D11_RESOURCE_DIMENSION_UNKNOWN = 0,
    D3D11_RESOURCE_DIMENSION_BUFFER = 1,
    D3D11_RESOURCE_DIMENSION_TEXTURE1D = 2,
    D3D11_RESOURCE_DIMENSION_TEXTURE2D = 3,
    D3D11_RESOURCE_DIMENSION_TEXTURE3D = 4
} D3D11_RESOURCE_DIMENSION;
typedef enum D3D11_RTV_DIMENSION {
    D3D11_RTV_DIMENSION_UNKNOWN = 0,
    D3D11_RTV_DIMENSION_BUFFER = 1,
    D3D11_RTV_DIMENSION_TEXTURE1D = 2,
    D3D11_RTV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D11_RTV_DIMENSION_TEXTURE2D = 4,
    D3D11_RTV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D11_RTV_DIMENSION_TEXTURE2DMS = 6,
    D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D11_RTV_DIMENSION_TEXTURE3D = 8
} D3D11_RTV_DIMENSION;
typedef struct D3D11_SO_DECLARATION_ENTRY {
    UINT Stream;
    LPCSTR SemanticName;
    UINT SemanticIndex;
    BYTE StartComponent;
    BYTE ComponentCount;
    BYTE OutputSlot;
} D3D11_SO_DECLARATION_ENTRY;
typedef enum D3D11_STENCIL_OP {
    D3D11_STENCIL_OP_KEEP = 1,
    D3D11_STENCIL_OP_ZERO = 2,
    D3D11_STENCIL_OP_REPLACE = 3,
    D3D11_STENCIL_OP_INCR_SAT = 4,
    D3D11_STENCIL_OP_DECR_SAT = 5,
    D3D11_STENCIL_OP_INVERT = 6,
    D3D11_STENCIL_OP_INCR = 7,
    D3D11_STENCIL_OP_DECR = 8
} D3D11_STENCIL_OP;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
}
inline UINT D3D11CalcSubresource(UINT MipSlice, UINT ArraySlice, UINT MipLevels) {
    return MipSlice + ArraySlice * MipLevels;
}
extern "C"{
#endif
typedef struct D3D11_SUBRESOURCE_DATA {
    const void *pSysMem;
    UINT SysMemPitch;
    UINT SysMemSlicePitch;
} D3D11_SUBRESOURCE_DATA;
typedef struct D3D11_TEX1D_ARRAY_DSV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_DSV;
typedef struct D3D11_TEX1D_ARRAY_RTV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_RTV;
typedef struct D3D11_TEX1D_ARRAY_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_SRV;
typedef struct D3D11_TEX1D_ARRAY_UAV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_UAV;
typedef struct D3D11_TEX1D_DSV {
    UINT MipSlice;
} D3D11_TEX1D_DSV;
typedef struct D3D11_TEX1D_RTV {
    UINT MipSlice;
} D3D11_TEX1D_RTV;
typedef struct D3D11_TEX1D_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX1D_SRV;
typedef struct D3D11_TEX1D_UAV {
    UINT MipSlice;
} D3D11_TEX1D_UAV;
typedef struct D3D11_TEX2D_ARRAY_DSV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_DSV;
typedef struct D3D11_TEX2D_ARRAY_RTV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_RTV;
typedef struct D3D11_TEX2D_ARRAY_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_SRV;
typedef struct D3D11_TEX2D_ARRAY_UAV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_UAV;
typedef struct D3D11_TEX2D_DSV {
    UINT MipSlice;
} D3D11_TEX2D_DSV;
typedef struct D3D11_TEX2D_RTV {
    UINT MipSlice;
} D3D11_TEX2D_RTV;
typedef struct D3D11_TEX2D_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX2D_SRV;
typedef struct D3D11_TEX2D_UAV {
    UINT MipSlice;
} D3D11_TEX2D_UAV;
typedef struct D3D11_TEX2DMS_ARRAY_DSV {
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_DSV;
typedef struct D3D11_TEX2DMS_ARRAY_RTV {
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_RTV;
typedef struct D3D11_TEX2DMS_ARRAY_SRV {
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_SRV;
typedef struct D3D11_TEX2DMS_DSV {
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_DSV;
typedef struct D3D11_TEX2DMS_RTV {
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_RTV;
typedef struct D3D11_TEX2DMS_SRV {
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_SRV;
typedef struct D3D11_TEX3D_RTV {
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D11_TEX3D_RTV;
typedef struct D3D11_TEX3D_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX3D_SRV;
typedef struct D3D11_TEX3D_UAV {
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D11_TEX3D_UAV;
typedef struct D3D11_TEXCUBE_ARRAY_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;
    UINT NumCubes;
} D3D11_TEXCUBE_ARRAY_SRV;
typedef struct D3D11_TEXCUBE_SRV {
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEXCUBE_SRV;
typedef enum D3D11_TEXTURE_ADDRESS_MODE {
    D3D11_TEXTURE_ADDRESS_WRAP = 1,
    D3D11_TEXTURE_ADDRESS_MIRROR = 2,
    D3D11_TEXTURE_ADDRESS_CLAMP = 3,
    D3D11_TEXTURE_ADDRESS_BORDER = 4,
    D3D11_TEXTURE_ADDRESS_MIRROR_ONCE = 5
} D3D11_TEXTURE_ADDRESS_MODE;
typedef enum D3D11_UAV_DIMENSION {
    D3D11_UAV_DIMENSION_UNKNOWN = 0,
    D3D11_UAV_DIMENSION_BUFFER = 1,
    D3D11_UAV_DIMENSION_TEXTURE1D = 2,
    D3D11_UAV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D11_UAV_DIMENSION_TEXTURE2D = 4,
    D3D11_UAV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D11_UAV_DIMENSION_TEXTURE3D = 8
} D3D11_UAV_DIMENSION;
typedef struct D3D11_UNORDERED_ACCESS_VIEW_DESC {
    DXGI_FORMAT Format;
    D3D11_UAV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_BUFFER_UAV Buffer;
        D3D11_TEX1D_UAV Texture1D;
        D3D11_TEX1D_ARRAY_UAV Texture1DArray;
        D3D11_TEX2D_UAV Texture2D;
        D3D11_TEX2D_ARRAY_UAV Texture2DArray;
        D3D11_TEX3D_UAV Texture3D;
    } __C89_NAMELESSUNIONNAME;
} D3D11_UNORDERED_ACCESS_VIEW_DESC;
typedef enum D3D11_USAGE {
    D3D11_USAGE_DEFAULT = 0,
    D3D11_USAGE_IMMUTABLE = 1,
    D3D11_USAGE_DYNAMIC = 2,
    D3D11_USAGE_STAGING = 3
} D3D11_USAGE;
typedef enum D3D11_BIND_FLAG {
    D3D11_BIND_VERTEX_BUFFER = 0x1,
    D3D11_BIND_INDEX_BUFFER = 0x2,
    D3D11_BIND_CONSTANT_BUFFER = 0x4,
    D3D11_BIND_SHADER_RESOURCE = 0x8,
    D3D11_BIND_STREAM_OUTPUT = 0x10,
    D3D11_BIND_RENDER_TARGET = 0x20,
    D3D11_BIND_DEPTH_STENCIL = 0x40,
    D3D11_BIND_UNORDERED_ACCESS = 0x80,
    D3D11_BIND_DECODER = 0x200,
    D3D11_BIND_VIDEO_ENCODER = 0x400
} D3D11_BIND_FLAG;
typedef enum D3D11_CPU_ACCESS_FLAG {
    D3D11_CPU_ACCESS_WRITE = 0x10000,
    D3D11_CPU_ACCESS_READ = 0x20000
} D3D11_CPU_ACCESS_FLAG;
typedef struct D3D11_VIEWPORT {
    FLOAT TopLeftX;
    FLOAT TopLeftY;
    FLOAT Width;
    FLOAT Height;
    FLOAT MinDepth;
    FLOAT MaxDepth;
} D3D11_VIEWPORT;
typedef enum D3D11_COLOR_WRITE_ENABLE {
    D3D11_COLOR_WRITE_ENABLE_RED = 1,
    D3D11_COLOR_WRITE_ENABLE_GREEN = 2,
    D3D11_COLOR_WRITE_ENABLE_BLUE = 4,
    D3D11_COLOR_WRITE_ENABLE_ALPHA = 8,
    D3D11_COLOR_WRITE_ENABLE_ALL = ((D3D11_COLOR_WRITE_ENABLE_RED | D3D11_COLOR_WRITE_ENABLE_GREEN) | D3D11_COLOR_WRITE_ENABLE_BLUE) | D3D11_COLOR_WRITE_ENABLE_ALPHA
} D3D11_COLOR_WRITE_ENABLE;
typedef enum D3D11_FORMAT_SUPPORT {
    D3D11_FORMAT_SUPPORT_BUFFER = 0x1,
    D3D11_FORMAT_SUPPORT_IA_VERTEX_BUFFER = 0x2,
    D3D11_FORMAT_SUPPORT_IA_INDEX_BUFFER = 0x4,
    D3D11_FORMAT_SUPPORT_SO_BUFFER = 0x8,
    D3D11_FORMAT_SUPPORT_TEXTURE1D = 0x10,
    D3D11_FORMAT_SUPPORT_TEXTURE2D = 0x20,
    D3D11_FORMAT_SUPPORT_TEXTURE3D = 0x40,
    D3D11_FORMAT_SUPPORT_TEXTURECUBE = 0x80,
    D3D11_FORMAT_SUPPORT_SHADER_LOAD = 0x100,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE = 0x200,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE_COMPARISON = 0x400,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE_MONO_TEXT = 0x800,
    D3D11_FORMAT_SUPPORT_MIP = 0x1000,
    D3D11_FORMAT_SUPPORT_MIP_AUTOGEN = 0x2000,
    D3D11_FORMAT_SUPPORT_RENDER_TARGET = 0x4000,
    D3D11_FORMAT_SUPPORT_BLENDABLE = 0x8000,
    D3D11_FORMAT_SUPPORT_DEPTH_STENCIL = 0x10000,
    D3D11_FORMAT_SUPPORT_CPU_LOCKABLE = 0x20000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_RESOLVE = 0x40000,
    D3D11_FORMAT_SUPPORT_DISPLAY = 0x80000,
    D3D11_FORMAT_SUPPORT_CAST_WITHIN_BIT_LAYOUT = 0x100000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_RENDERTARGET = 0x200000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_LOAD = 0x400000,
    D3D11_FORMAT_SUPPORT_SHADER_GATHER = 0x800000,
    D3D11_FORMAT_SUPPORT_BACK_BUFFER_CAST = 0x1000000,
    D3D11_FORMAT_SUPPORT_TYPED_UNORDERED_ACCESS_VIEW = 0x2000000,
    D3D11_FORMAT_SUPPORT_SHADER_GATHER_COMPARISON = 0x4000000,
    D3D11_FORMAT_SUPPORT_DECODER_OUTPUT = 0x8000000,
    D3D11_FORMAT_SUPPORT_VIDEO_PROCESSOR_OUTPUT = 0x10000000,
    D3D11_FORMAT_SUPPORT_VIDEO_PROCESSOR_INPUT = 0x20000000,
    D3D11_FORMAT_SUPPORT_VIDEO_ENCODER = 0x40000000
} D3D11_FORMAT_SUPPORT;
typedef enum D3D11_FORMAT_SUPPORT2 {
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_ADD = 0x1,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS = 0x2,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE = 0x4,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE = 0x8,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX = 0x10,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX = 0x20,
    D3D11_FORMAT_SUPPORT2_UAV_TYPED_LOAD = 0x40,
    D3D11_FORMAT_SUPPORT2_UAV_TYPED_STORE = 0x80,
    D3D11_FORMAT_SUPPORT2_OUTPUT_MERGER_LOGIC_OP = 0x100,
    D3D11_FORMAT_SUPPORT2_TILED = 0x200,
    D3D11_FORMAT_SUPPORT2_SHAREABLE = 0x400,
    D3D11_FORMAT_SUPPORT2_MULTIPLANE_OVERLAY = 0x4000
} D3D11_FORMAT_SUPPORT2;
typedef enum D3D11_CLEAR_FLAG {
    D3D11_CLEAR_DEPTH = 0x1,
    D3D11_CLEAR_STENCIL = 0x2
} D3D11_CLEAR_FLAG;
typedef struct D3D11_RENDER_TARGET_BLEND_DESC {
    WINBOOL BlendEnable;
    D3D11_BLEND SrcBlend;
    D3D11_BLEND DestBlend;
    D3D11_BLEND_OP BlendOp;
    D3D11_BLEND SrcBlendAlpha;
    D3D11_BLEND DestBlendAlpha;
    D3D11_BLEND_OP BlendOpAlpha;
    UINT8 RenderTargetWriteMask;
} D3D11_RENDER_TARGET_BLEND_DESC;
typedef struct D3D11_BLEND_DESC {
    WINBOOL AlphaToCoverageEnable;
    WINBOOL IndependentBlendEnable;
    D3D11_RENDER_TARGET_BLEND_DESC RenderTarget[8];
} D3D11_BLEND_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_BLEND_DESC : public D3D11_BLEND_DESC {
    CD3D11_BLEND_DESC() {}
    explicit CD3D11_BLEND_DESC(const D3D11_BLEND_DESC &o) : D3D11_BLEND_DESC(o) {}
    explicit CD3D11_BLEND_DESC(CD3D11_DEFAULT) {
        AlphaToCoverageEnable = FALSE;
        IndependentBlendEnable = FALSE;
        for(D3D11_RENDER_TARGET_BLEND_DESC *target = RenderTarget;
                target < RenderTarget + D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT;
                target++) {
            target->BlendEnable = FALSE;
            target->SrcBlend = target->SrcBlendAlpha = D3D11_BLEND_ONE;
            target->DestBlend = target->DestBlendAlpha = D3D11_BLEND_ZERO;
            target->BlendOp = target->BlendOpAlpha = D3D11_BLEND_OP_ADD;
            target->RenderTargetWriteMask = D3D11_COLOR_WRITE_ENABLE_ALL;
        }
    }
    ~CD3D11_BLEND_DESC() {}
    operator const D3D11_BLEND_DESC&() const { return *this; }
};
#endif
typedef struct D3D11_BUFFER_DESC {
    UINT ByteWidth;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    UINT StructureByteStride;
} D3D11_BUFFER_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_BUFFER_DESC : public D3D11_BUFFER_DESC {
    CD3D11_BUFFER_DESC() {}
    explicit CD3D11_BUFFER_DESC(const D3D11_BUFFER_DESC &o) : D3D11_BUFFER_DESC(o) {}
    explicit CD3D11_BUFFER_DESC(UINT byteWidth,UINT bindFlags,
            D3D11_USAGE usage = D3D11_USAGE_DEFAULT, UINT cpuaccessFlags = 0,
            UINT miscFlags = 0, UINT structureByteStride = 0 ) {
        ByteWidth = byteWidth;
        Usage = usage;
        BindFlags = bindFlags;
        CPUAccessFlags = cpuaccessFlags;
        MiscFlags = miscFlags;
        StructureByteStride = structureByteStride;
    }
    ~CD3D11_BUFFER_DESC() {}
    operator const D3D11_BUFFER_DESC&() const { return *this; }
};
#endif
typedef struct D3D11_DEPTH_STENCIL_VIEW_DESC {
    DXGI_FORMAT Format;
    D3D11_DSV_DIMENSION ViewDimension;
    UINT Flags;
    __C89_NAMELESS union {
        D3D11_TEX1D_DSV Texture1D;
        D3D11_TEX1D_ARRAY_DSV Texture1DArray;
        D3D11_TEX2D_DSV Texture2D;
        D3D11_TEX2D_ARRAY_DSV Texture2DArray;
        D3D11_TEX2DMS_DSV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_DSV Texture2DMSArray;
    } __C89_NAMELESSUNIONNAME;
} D3D11_DEPTH_STENCIL_VIEW_DESC;
typedef struct D3D11_DEPTH_STENCILOP_DESC {
    D3D11_STENCIL_OP StencilFailOp;
    D3D11_STENCIL_OP StencilDepthFailOp;
    D3D11_STENCIL_OP StencilPassOp;
    D3D11_COMPARISON_FUNC StencilFunc;
} D3D11_DEPTH_STENCILOP_DESC;
typedef struct D3D11_DEPTH_STENCIL_DESC {
    WINBOOL DepthEnable;
    D3D11_DEPTH_WRITE_MASK DepthWriteMask;
    D3D11_COMPARISON_FUNC DepthFunc;
    WINBOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D11_DEPTH_STENCILOP_DESC FrontFace;
    D3D11_DEPTH_STENCILOP_DESC BackFace;
} D3D11_DEPTH_STENCIL_DESC;
#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )
struct CD3D11_DEPTH_STENCIL_DESC : public D3D11_DEPTH_STENCIL_DESC {
    CD3D11_DEPTH_STENCIL_DESC() {}
    explicit CD3D11_DEPTH_STENCIL_DESC(const D3D11_DEPTH_STENCIL_DESC &other) : D3D11_DEPTH_STENCIL_DESC(other) {}
    explicit CD3D11_DEPTH_STENCIL_DESC(CD3D11_DEFAULT) {
        const D3D11_DEPTH_STENCILOP_DESC default_op =
            {D3D11_STENCIL_OP_KEEP, D3D11_STENCIL_OP_KEEP, D3D11_STENCIL_OP_KEEP, D3D11_COMPARISON_ALWAYS};
        DepthEnable = TRUE;
        DepthWriteMask = D3D11_DEPTH_WRITE_MASK_ALL;
        DepthFunc = D3D11_COMPARISON_LESS;
        StencilEnable = FALSE;
        StencilReadMask = D3D11_DEFAULT_STENCIL_READ_MASK;
        StencilWriteMask = D3D11_DEFAULT_STENCIL_WRITE_MASK;
        FrontFace = default_op;
        BackFace = default_op;
    }
    explicit CD3D11_DEPTH_STENCIL_DESC(
            WINBOOL depth_enable,
            D3D11_DEPTH_WRITE_MASK depth_write_mask,
            D3D11_COMPARISON_FUNC depth_func,
            WINBOOL stencil_enable,
            UINT8 stencil_read_mask,
            UINT8 stencil_write_mask,
            D3D11_STENCIL_OP front_stencil_fail_op,
            D3D11_STENCIL_OP front_stencil_depth_fail_op,
            D3D11_STENCIL_OP front_stencil_pass_op,
            D3D11_COMPARISON_FUNC front_stencil_func,
            D3D11_STENCIL_OP back_stencil_fail_op,
            D3D11_STENCIL_OP back_stencil_depth_fail_op,
            D3D11_STENCIL_OP back_stencil_pass_op,
            D3D11_COMPARISON_FUNC back_stencil_func) {
        DepthEnable = depth_enable;
        DepthWriteMask = depth_write_mask;
        DepthFunc = depth_func;
        StencilEnable = stencil_enable;
        StencilReadMask = stencil_read_mask;
        StencilWriteMask = stencil_write_mask;
        FrontFace.StencilFailOp = front_stencil_fail_op;
        FrontFace.StencilDepthFailOp = front_stencil_depth_fail_op;
        FrontFace.StencilPassOp = front_stencil_pass_op;
        FrontFace.StencilFunc = front_stencil_func;
        BackFace.StencilFailOp = back_stencil_fail_op;
        BackFace.StencilDepthFailOp = back_stencil_depth_fail_op;
        BackFace.StencilPassOp = back_stencil_pass_op;
        BackFace.StencilFunc = back_stencil_func;
    }
    ~CD3D11_DEPTH_STENCIL_DESC() {}
    operator const D3D11_DEPTH_STENCIL_DESC&() const { return *this; }
};
#endif
typedef struct D3D11_RENDER_TARGET_VIEW_DESC {
    DXGI_FORMAT Format;
    D3D11_RTV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_BUFFER_RTV Buffer;
        D3D11_TEX1D_RTV Texture1D;
        D3D11_TEX1D_ARRAY_RTV Texture1DArray;
        D3D11_TEX2D_RTV Texture2D;
        D3D11_TEX2D_ARRAY_RTV Texture2DArray;
        D3D11_TEX2DMS_RTV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D11_TEX3D_RTV Texture3D;
    } __C89_NAMELESSUNIONNAME;
} D3D11_RENDER_TARGET_VIEW_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_RENDER_TARGET_VIEW_DESC : public D3D11_RENDER_TARGET_VIEW_DESC {
    CD3D11_RENDER_TARGET_VIEW_DESC() {}
    explicit CD3D11_RENDER_TARGET_VIEW_DESC(D3D11_RTV_DIMENSION dim, DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN,
            UINT mip_slice = 0, UINT first_slice = 0, UINT array_size = -1) {
        Format = format;
        ViewDimension = dim;
        switch(dim) {
        case D3D11_RTV_DIMENSION_BUFFER:
            Buffer.FirstElement = mip_slice;
            Buffer.NumElements = first_slice;
            break;
        case D3D11_RTV_DIMENSION_TEXTURE1D:
            Texture1D.MipSlice = mip_slice;
            break;
        case D3D11_RTV_DIMENSION_TEXTURE1DARRAY:
            Texture1DArray.MipSlice = mip_slice;
            Texture1DArray.FirstArraySlice = first_slice;
            Texture1DArray.ArraySize = array_size;
            break;
        case D3D11_RTV_DIMENSION_TEXTURE2D:
            Texture2D.MipSlice = mip_slice;
            break;
        case D3D11_RTV_DIMENSION_TEXTURE2DARRAY:
            Texture2DArray.MipSlice = mip_slice;
            Texture2DArray.FirstArraySlice = first_slice;
            Texture2DArray.ArraySize = array_size;
            break;
        case D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY:
            Texture2DMSArray.FirstArraySlice = first_slice;
            Texture2DMSArray.ArraySize = array_size;
            break;
        case D3D11_RTV_DIMENSION_TEXTURE3D:
            Texture3D.MipSlice = mip_slice;
            Texture3D.FirstWSlice = first_slice;
            Texture3D.WSize = array_size;
            break;
        default:
            break;
        }
    }
    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Buffer*, DXGI_FORMAT format, UINT first_elem,
            UINT elem_cnt) {
        Format = format;
        ViewDimension = D3D11_RTV_DIMENSION_BUFFER;
        Buffer.FirstElement = first_elem;
        Buffer.NumElements = elem_cnt;
    }
    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Texture1D *texture, D3D11_RTV_DIMENSION dim,
            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT mip_slice = 0, UINT first_slice = 0,
            UINT array_size = -1);
    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Texture2D *texture, D3D11_RTV_DIMENSION dim,
            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT mip_slice = 0, UINT first_slice = 0,
            UINT array_size = -1);
    explicit CD3D11_RENDER_TARGET_VIEW_DESC(ID3D11Texture3D *texture, DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN,
            UINT mip_slice = 0, UINT first_w_slice = 0, UINT w_slice = -1 );
    ~CD3D11_RENDER_TARGET_VIEW_DESC() {}
    explicit CD3D11_RENDER_TARGET_VIEW_DESC(const D3D11_RENDER_TARGET_VIEW_DESC &other)
        : D3D11_RENDER_TARGET_VIEW_DESC(other) {}
    operator const D3D11_RENDER_TARGET_VIEW_DESC&() const {
        return *this;
    }
};
#endif
typedef struct D3D11_SAMPLER_DESC {
    D3D11_FILTER Filter;
    D3D11_TEXTURE_ADDRESS_MODE AddressU;
    D3D11_TEXTURE_ADDRESS_MODE AddressV;
    D3D11_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D11_COMPARISON_FUNC ComparisonFunc;
    FLOAT BorderColor[4];
    FLOAT MinLOD;
    FLOAT MaxLOD;
} D3D11_SAMPLER_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_SAMPLER_DESC : public D3D11_SAMPLER_DESC {
    CD3D11_SAMPLER_DESC() {}
    explicit CD3D11_SAMPLER_DESC(const D3D11_SAMPLER_DESC &o) : D3D11_SAMPLER_DESC(o) {}
    explicit CD3D11_SAMPLER_DESC(CD3D11_DEFAULT) {
        Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
        AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
        AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
        AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
        MipLODBias = 0;
        MaxAnisotropy = 1;
        ComparisonFunc = D3D11_COMPARISON_NEVER;
        BorderColor[0] = BorderColor[1] = BorderColor[2] = BorderColor[3] = 1.0f;
        MinLOD = -3.402823466e+38f;
        MaxLOD = 3.402823466e+38f;
    }
    explicit CD3D11_SAMPLER_DESC(D3D11_FILTER filter, D3D11_TEXTURE_ADDRESS_MODE addressU,
            D3D11_TEXTURE_ADDRESS_MODE addressV, D3D11_TEXTURE_ADDRESS_MODE addressW,
            FLOAT mipLODBias, UINT maxAnisotropy, D3D11_COMPARISON_FUNC comparisonFunc,
            const FLOAT *borderColor, FLOAT minLOD, FLOAT maxLOD) {
        Filter = filter;
        AddressU = addressU;
        AddressV = addressV;
        AddressW = addressW;
        MipLODBias = mipLODBias;
        MaxAnisotropy = maxAnisotropy;
        ComparisonFunc = comparisonFunc;
        if(borderColor) {
            BorderColor[0] = borderColor[0];
            BorderColor[1] = borderColor[1];
            BorderColor[2] = borderColor[2];
            BorderColor[3] = borderColor[3];
        }else {
            BorderColor[0] = BorderColor[1] = BorderColor[2] = BorderColor[3] = 1.0f;
        }
        MinLOD = minLOD;
        MaxLOD = maxLOD;
    }
    ~CD3D11_SAMPLER_DESC() {}
    operator const D3D11_SAMPLER_DESC&() const { return *this; }
};
#endif
typedef struct D3D11_SHADER_RESOURCE_VIEW_DESC {
    DXGI_FORMAT Format;
    D3D11_SRV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_BUFFER_SRV Buffer;
        D3D11_TEX1D_SRV Texture1D;
        D3D11_TEX1D_ARRAY_SRV Texture1DArray;
        D3D11_TEX2D_SRV Texture2D;
        D3D11_TEX2D_ARRAY_SRV Texture2DArray;
        D3D11_TEX2DMS_SRV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D11_TEX3D_SRV Texture3D;
        D3D11_TEXCUBE_SRV TextureCube;
        D3D11_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D11_BUFFEREX_SRV BufferEx;
    } __C89_NAMELESSUNIONNAME;
} D3D11_SHADER_RESOURCE_VIEW_DESC;
#if !defined(D3D11_NO_HELPERS) && defined( __cplusplus )
struct CD3D11_SHADER_RESOURCE_VIEW_DESC : public D3D11_SHADER_RESOURCE_VIEW_DESC {
    CD3D11_SHADER_RESOURCE_VIEW_DESC() {}
    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(D3D11_SRV_DIMENSION dim,
            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT most_detailed_mip = 0,
            UINT mip_levels = -1, UINT first_slice = 0, UINT array_size = -1, UINT flags = 0) {
        Format = format;
        ViewDimension = dim;
        switch(ViewDimension) {
        case D3D11_SRV_DIMENSION_BUFFER:
            Buffer.FirstElement = most_detailed_mip;
            Buffer.NumElements = mip_levels;
            break;
        case D3D11_SRV_DIMENSION_TEXTURE1D:
            Texture1D.MostDetailedMip = most_detailed_mip;
            Texture1D.MipLevels = mip_levels;
            break;
        case D3D11_SRV_DIMENSION_TEXTURE1DARRAY:
            Texture1DArray.MostDetailedMip = most_detailed_mip;
            Texture1DArray.MipLevels = mip_levels;
            Texture1DArray.FirstArraySlice = first_slice;
            Texture1DArray.ArraySize = array_size;
            break;
        case D3D11_SRV_DIMENSION_TEXTURE2D:
            Texture2D.MostDetailedMip = most_detailed_mip;
            Texture2D.MipLevels = mip_levels;
            break;
        case D3D11_SRV_DIMENSION_TEXTURE2DARRAY:
            Texture2DArray.MostDetailedMip = most_detailed_mip;
            Texture2DArray.MipLevels = mip_levels;
            Texture2DArray.FirstArraySlice = first_slice;
            Texture2DArray.ArraySize = array_size;
            break;
        case D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY:
            Texture2DMSArray.FirstArraySlice = first_slice;
            Texture2DMSArray.ArraySize = array_size;
            break;
        case D3D11_SRV_DIMENSION_TEXTURE3D:
            Texture3D.MostDetailedMip = most_detailed_mip;
            Texture3D.MipLevels = mip_levels;
            break;
        case D3D11_SRV_DIMENSION_TEXTURECUBE:
            TextureCube.MostDetailedMip = most_detailed_mip;
            TextureCube.MipLevels = mip_levels;
            break;
        case D3D11_SRV_DIMENSION_TEXTURECUBEARRAY:
            TextureCubeArray.MostDetailedMip = most_detailed_mip;
            TextureCubeArray.MipLevels = mip_levels;
            TextureCubeArray.First2DArrayFace = first_slice;
            TextureCubeArray.NumCubes = array_size;
            break;
        case D3D11_SRV_DIMENSION_BUFFEREX:
            BufferEx.FirstElement = most_detailed_mip;
            BufferEx.NumElements = mip_levels;
            BufferEx.Flags = flags;
            break;
        default:
            break;
        }
    }
    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Buffer*, DXGI_FORMAT format, UINT first_elem,
            UINT elem_cnt, UINT flags = 0);
    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Texture1D *texture, D3D11_SRV_DIMENSION dim,
            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT most_detailed_mip = 0, UINT mip_levels = -1,
            UINT first_slice = 0, UINT array_size = -1 );
    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Texture2D *texture, D3D11_SRV_DIMENSION dim,
            DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN, UINT most_detailed_mip = 0, UINT mip_levels = -1,
            UINT first_slice = 0, UINT array_size = -1 );
    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(ID3D11Texture3D *texture, DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN,
            UINT most_detailed_mip = 0, UINT mip_levels = -1 );
    ~CD3D11_SHADER_RESOURCE_VIEW_DESC() {}
    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(const D3D11_SHADER_RESOURCE_VIEW_DESC &other)
        : D3D11_SHADER_RESOURCE_VIEW_DESC(other) {}
    operator const D3D11_SHADER_RESOURCE_VIEW_DESC&() const {
        return *this;
    }
};
#endif
typedef struct D3D11_TEXTURE1D_DESC {
    UINT Width;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE1D_DESC;
typedef struct D3D11_TEXTURE2D_DESC {
    UINT Width;
    UINT Height;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE2D_DESC;
#if !defined(D3D11_NO_HELPERS) && defined(__cplusplus)
struct CD3D11_TEXTURE2D_DESC : public D3D11_TEXTURE2D_DESC {
    CD3D11_TEXTURE2D_DESC() {}
    explicit CD3D11_TEXTURE2D_DESC(const D3D11_TEXTURE2D_DESC &o) : D3D11_TEXTURE2D_DESC(o) {}
    explicit CD3D11_TEXTURE2D_DESC(DXGI_FORMAT format, UINT width, UINT height, UINT arraySize = 1,
            UINT mipLevels = 0, UINT bindFlags = D3D11_BIND_SHADER_RESOURCE,
            D3D11_USAGE usage = D3D11_USAGE_DEFAULT, UINT cpuaccessFlags = 0, UINT sampleCount = 1,
            UINT sampleQuality = 0, UINT miscFlags = 0) {
        Width = width;
        Height = height;
        MipLevels = mipLevels;
        ArraySize = arraySize;
        Format = format;
        SampleDesc.Count = sampleCount;
        SampleDesc.Quality = sampleQuality;
        Usage = usage;
        BindFlags = bindFlags;
        CPUAccessFlags = cpuaccessFlags;
        MiscFlags = miscFlags;
    }
    ~CD3D11_TEXTURE2D_DESC() {}
    operator const D3D11_TEXTURE2D_DESC&() const { return *this; }
};
#endif
typedef struct D3D11_TEXTURE3D_DESC {
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE3D_DESC;
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2_MOCOMP,                      0xe6a9f44b,0x61b0,0x4563,0x9e,0xa4,0x63,0xd2,0xa3,0xc6,0xfe,0x66);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2_IDCT,                        0xbf22ad00,0x03ea,0x4690,0x80,0x77,0x47,0x33,0x46,0x20,0x9b,0x7e);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2_VLD,                         0xee27417f,0x5e28,0x4e65,0xbe,0xea,0x1d,0x26,0xb5,0x08,0xad,0xc9);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG1_VLD,                         0x6f3ec719,0x3735,0x42cc,0x80,0x63,0x65,0xcc,0x3c,0xb3,0x66,0x16);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG2and1_VLD,                     0x86695f12,0x340e,0x4f04,0x9f,0xd3,0x92,0x53,0xdd,0x32,0x74,0x60);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_MOCOMP_NOFGT,                 0x1b81be64,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_MOCOMP_FGT,                   0x1b81be65,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_IDCT_NOFGT,                   0x1b81be66,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_IDCT_FGT,                     0x1b81be67,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_NOFGT,                    0x1b81be68,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_FGT,                      0x1b81be69,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_WITHFMOASO_NOFGT,         0xd5f04ff9,0x3418,0x45d8,0x95,0x61,0x32,0xa7,0x6a,0xae,0x2d,0xdd);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_STEREO_PROGRESSIVE_NOFGT, 0xd79be8da,0x0cf1,0x4c81,0xb8,0x2a,0x69,0xa4,0xe2,0x36,0xf4,0x3d);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_STEREO_NOFGT,             0xf9aaccbb,0xc2b6,0x4cfc,0x87,0x79,0x57,0x07,0xb1,0x76,0x05,0x52);
DEFINE_GUID(D3D11_DECODER_PROFILE_H264_VLD_MULTIVIEW_NOFGT,          0x705b9d82,0x76cf,0x49d6,0xb7,0xe6,0xac,0x88,0x72,0xdb,0x01,0x3c);
DEFINE_GUID(D3D11_DECODER_PROFILE_WMV8_POSTPROC,                     0x1b81be80,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_WMV8_MOCOMP,                       0x1b81be81,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_WMV9_POSTPROC,                     0x1b81be90,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_WMV9_MOCOMP,                       0x1b81be91,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_WMV9_IDCT,                         0x1b81be94,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_POSTPROC,                      0x1b81beA0,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_MOCOMP,                        0x1b81beA1,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_IDCT,                          0x1b81beA2,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_VLD,                           0x1b81beA3,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_VC1_D2010,                         0x1b81beA4,0xa0c7,0x11d3,0xb9,0x84,0x00,0xc0,0x4f,0x2e,0x73,0xc5);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG4PT2_VLD_SIMPLE,               0xefd64d74,0xc9e8,0x41d7,0xa5,0xe9,0xe9,0xb0,0xe3,0x9f,0xa3,0x19);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG4PT2_VLD_ADVSIMPLE_NOGMC,      0xed418a9f,0x010d,0x4eda,0x9a,0xe3,0x9a,0x65,0x35,0x8d,0x8d,0x2e);
DEFINE_GUID(D3D11_DECODER_PROFILE_MPEG4PT2_VLD_ADVSIMPLE_GMC,        0xab998b5b,0x4258,0x44a9,0x9f,0xeb,0x94,0xe5,0x97,0xa6,0xba,0xae);
DEFINE_GUID(D3D11_DECODER_PROFILE_HEVC_VLD_MAIN,                     0x5b11d51b,0x2f4c,0x4452,0xbc,0xc3,0x09,0xf2,0xa1,0x16,0x0c,0xc0);
DEFINE_GUID(D3D11_DECODER_PROFILE_HEVC_VLD_MAIN10,                   0x107af0e0,0xef1a,0x4d19,0xab,0xa8,0x67,0xa1,0x63,0x07,0x3d,0x13);
DEFINE_GUID(D3D11_DECODER_PROFILE_VP9_VLD_PROFILE0,                  0x463707f8,0xa1d0,0x4585,0x87,0x6d,0x83,0xaa,0x6d,0x60,0xb8,0x9e);
DEFINE_GUID(D3D11_DECODER_PROFILE_VP9_VLD_10BIT_PROFILE2,            0xa4c749ef,0x6ecf,0x48aa,0x84,0x48,0x50,0xa7,0xa1,0x16,0x5f,0xf7);
DEFINE_GUID(D3D11_DECODER_PROFILE_VP8_VLD,                           0x90b899ea,0x3a62,0x4705,0x88,0xb3,0x8d,0xf0,0x4b,0x27,0x44,0xe7);
typedef struct D3D11_VIDEO_DECODER_DESC {
    GUID Guid;
    UINT SampleWidth;
    UINT SampleHeight;
    DXGI_FORMAT OutputFormat;
} D3D11_VIDEO_DECODER_DESC;
typedef struct D3D11_VIDEO_DECODER_CONFIG {
    GUID guidConfigBitstreamEncryption;
    GUID guidConfigMBcontrolEncryption;
    GUID guidConfigResidDiffEncryption;
    UINT ConfigBitstreamRaw;
    UINT ConfigMBcontrolRasterOrder;
    UINT ConfigResidDiffHost;
    UINT ConfigSpatialResid8;
    UINT ConfigResid8Subtraction;
    UINT ConfigSpatialHost8or9Clipping;
    UINT ConfigSpatialResidInterleaved;
    UINT ConfigIntraResidUnsigned;
    UINT ConfigResidDiffAccelerator;
    UINT ConfigHostInverseScan;
    UINT ConfigSpecificIDCT;
    UINT Config4GroupedCoefs;
    USHORT ConfigMinRenderTargetBuffCount;
    USHORT ConfigDecoderSpecific;
} D3D11_VIDEO_DECODER_CONFIG;
typedef enum D3D11_VIDEO_FRAME_FORMAT {
    D3D11_VIDEO_FRAME_FORMAT_PROGRESSIVE = 0,
    D3D11_VIDEO_FRAME_FORMAT_INTERLACED_TOP_FIELD_FIRST = 1,
    D3D11_VIDEO_FRAME_FORMAT_INTERLACED_BOTTOM_FIELD_FIRST = 2
} D3D11_VIDEO_FRAME_FORMAT;
typedef enum D3D11_VIDEO_USAGE {
    D3D11_VIDEO_USAGE_PLAYBACK_NORMAL = 0,
    D3D11_VIDEO_USAGE_OPTIMAL_SPEED = 1,
    D3D11_VIDEO_USAGE_OPTIMAL_QUALITY = 2
} D3D11_VIDEO_USAGE;
typedef struct D3D11_VIDEO_PROCESSOR_CONTENT_DESC {
    D3D11_VIDEO_FRAME_FORMAT InputFrameFormat;
    DXGI_RATIONAL InputFrameRate;
    UINT InputWidth;
    UINT InputHeight;
    DXGI_RATIONAL OutputFrameRate;
    UINT OutputWidth;
    UINT OutputHeight;
    D3D11_VIDEO_USAGE Usage;
} D3D11_VIDEO_PROCESSOR_CONTENT_DESC;
typedef struct D3D11_VIDEO_PROCESSOR_CAPS {
    UINT DeviceCaps;
    UINT FeatureCaps;
    UINT FilterCaps;
    UINT InputFormatCaps;
    UINT AutoStreamCaps;
    UINT StereoCaps;
    UINT RateConversionCapsCount;
    UINT MaxInputStreams;
    UINT MaxStreamStates;
} D3D11_VIDEO_PROCESSOR_CAPS;
typedef struct D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS {
    UINT PastFrames;
    UINT FutureFrames;
    UINT ProcessorCaps;
    UINT ITelecineCaps;
    UINT CustomRateCount;
} D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS;
typedef struct D3D11_VIDEO_PROCESSOR_CUSTOM_RATE {
    DXGI_RATIONAL CustomRate;
    UINT OutputFrames;
    WINBOOL InputInterlaced;
    UINT InputFramesOrFields;
} D3D11_VIDEO_PROCESSOR_CUSTOM_RATE;
typedef enum D3D11_VIDEO_PROCESSOR_FILTER {
    D3D11_VIDEO_PROCESSOR_FILTER_BRIGHTNESS = 0,
    D3D11_VIDEO_PROCESSOR_FILTER_CONTRAST = 1,
    D3D11_VIDEO_PROCESSOR_FILTER_HUE = 2,
    D3D11_VIDEO_PROCESSOR_FILTER_SATURATION = 3,
    D3D11_VIDEO_PROCESSOR_FILTER_NOISE_REDUCTION = 4,
    D3D11_VIDEO_PROCESSOR_FILTER_EDGE_ENHANCEMENT = 5,
    D3D11_VIDEO_PROCESSOR_FILTER_ANAMORPHIC_SCALING = 6,
    D3D11_VIDEO_PROCESSOR_FILTER_STEREO_ADJUSTMENT = 7
} D3D11_VIDEO_PROCESSOR_FILTER;
typedef struct D3D11_VIDEO_PROCESSOR_FILTER_RANGE {
    int Minimum;
    int Maximum;
    int Default;
    float Multiplier;
} D3D11_VIDEO_PROCESSOR_FILTER_RANGE;
typedef enum D3D11_AUTHENTICATED_CHANNEL_TYPE {
    D3D11_AUTHENTICATED_CHANNEL_D3D11 = 1,
    D3D11_AUTHENTICATED_CHANNEL_DRIVER_SOFTWARE = 2,
    D3D11_AUTHENTICATED_CHANNEL_DRIVER_HARDWARE = 3
} D3D11_AUTHENTICATED_CHANNEL_TYPE;
typedef enum D3D11_VDOV_DIMENSION {
    D3D11_VDOV_DIMENSION_UNKNOWN = 0,
    D3D11_VDOV_DIMENSION_TEXTURE2D = 1
} D3D11_VDOV_DIMENSION;
typedef struct D3D11_TEX2D_VDOV {
    UINT ArraySlice;
} D3D11_TEX2D_VDOV;
typedef struct D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC {
    GUID DecodeProfile;
    D3D11_VDOV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_TEX2D_VDOV Texture2D;
    } __C89_NAMELESSUNIONNAME;
} D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC;
typedef enum D3D11_VPIV_DIMENSION {
    D3D11_VPIV_DIMENSION_UNKNOWN = 0,
    D3D11_VPIV_DIMENSION_TEXTURE2D = 1
} D3D11_VPIV_DIMENSION;
typedef struct D3D11_TEX2D_VPIV {
    UINT MipSlice;
    UINT ArraySlice;
} D3D11_TEX2D_VPIV;
typedef struct D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC {
    UINT FourCC;
    D3D11_VPIV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_TEX2D_VPIV Texture2D;
    } __C89_NAMELESSUNIONNAME;
} D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC;
typedef enum D3D11_VPOV_DIMENSION {
    D3D11_VPOV_DIMENSION_UNKNOWN = 0,
    D3D11_VPOV_DIMENSION_TEXTURE2D = 1,
    D3D11_VPOV_DIMENSION_TEXTURE2DARRAY = 2
} D3D11_VPOV_DIMENSION;
typedef struct D3D11_TEX2D_VPOV {
    UINT MipSlice;
} D3D11_TEX2D_VPOV;
typedef struct D3D11_TEX2D_ARRAY_VPOV {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_VPOV;
typedef struct D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC {
    D3D11_VPOV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_TEX2D_VPOV Texture2D;
        D3D11_TEX2D_ARRAY_VPOV Texture2DArray;
    } __C89_NAMELESSUNIONNAME;
} D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC;
typedef struct D3D11_VIDEO_CONTENT_PROTECTION_CAPS {
    UINT Caps;
    UINT KeyExchangeTypeCount;
    UINT BlockAlignmentSize;
    ULONGLONG ProtectedMemorySize;
} D3D11_VIDEO_CONTENT_PROTECTION_CAPS;
typedef struct D3D11_ENCRYPTED_BLOCK_INFO {
    UINT NumEncryptedBytesAtBeginning;
    UINT NumBytesInSkipPattern;
    UINT NumBytesInEncryptPattern;
} D3D11_ENCRYPTED_BLOCK_INFO;
typedef struct D3D11_VIDEO_DECODER_BUFFER_DESC {
    D3D11_VIDEO_DECODER_BUFFER_TYPE BufferType;
    UINT BufferIndex;
    UINT DataOffset;
    UINT DataSize;
    UINT FirstMBaddress;
    UINT NumMBsInBuffer;
    UINT Width;
    UINT Height;
    UINT Stride;
    UINT ReservedBits;
    void *pIV;
    UINT IVSize;
    WINBOOL PartialEncryption;
    D3D11_ENCRYPTED_BLOCK_INFO EncryptedBlockInfo;
} D3D11_VIDEO_DECODER_BUFFER_DESC;
typedef struct D3D11_VIDEO_DECODER_EXTENSION {
    UINT Function;
    void *pPrivateInputData;
    UINT PrivateInputDataSize;
    void *pPrivateOutputData;
    UINT PrivateOutputDataSize;
    UINT ResourceCount;
    ID3D11Resource **ppResourceList;
} D3D11_VIDEO_DECODER_EXTENSION;
typedef struct D3D11_VIDEO_COLOR_YCbCrA {
    float Y;
    float Cb;
    float Cr;
    float A;
} D3D11_VIDEO_COLOR_YCbCrA;
typedef struct D3D11_VIDEO_COLOR_RGBA {
    float R;
    float G;
    float B;
    float A;
} D3D11_VIDEO_COLOR_RGBA;
typedef struct D3D11_VIDEO_COLOR {
    __C89_NAMELESS union {
        D3D11_VIDEO_COLOR_YCbCrA YCbCr;
        D3D11_VIDEO_COLOR_RGBA RGBA;
    } __C89_NAMELESSUNIONNAME;
} D3D11_VIDEO_COLOR;
typedef struct D3D11_VIDEO_PROCESSOR_COLOR_SPACE {
    UINT Usage : 1;
    UINT RGB_Range : 1;
    UINT YCbCr_Matrix : 1;
    UINT YCbCr_xvYCC : 1;
    UINT Nominal_Range : 2;
    UINT Reserved : 26;
} D3D11_VIDEO_PROCESSOR_COLOR_SPACE;
typedef struct D3D11_VIDEO_PROCESSOR_STREAM {
    WINBOOL Enable;
    UINT OutputIndex;
    UINT InputFrameOrField;
    UINT PastFrames;
    UINT FutureFrames;
    ID3D11VideoProcessorInputView **ppPastSurfaces;
    ID3D11VideoProcessorInputView *pInputSurface;
    ID3D11VideoProcessorInputView **ppFutureSurfaces;
    ID3D11VideoProcessorInputView **ppPastSurfacesRight;
    ID3D11VideoProcessorInputView *pInputSurfaceRight;
    ID3D11VideoProcessorInputView **ppFutureSurfacesRight;
} D3D11_VIDEO_PROCESSOR_STREAM;
typedef struct D3D11_OMAC {
    BYTE Omac[16];
} D3D11_OMAC;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_OUTPUT {
    D3D11_OMAC omac;
    GUID ConfigureType;
    HANDLE hChannel;
    UINT SequenceNumber;
    HRESULT ReturnCode;
} D3D11_AUTHENTICATED_CONFIGURE_OUTPUT;
typedef struct D3D11_QUERY_DATA_TIMESTAMP_DISJOINT {
    UINT64 Frequency;
    WINBOOL Disjoint;
} D3D11_QUERY_DATA_TIMESTAMP_DISJOINT;
typedef struct D3D11_QUERY_DATA_PIPELINE_STATISTICS {
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
} D3D11_QUERY_DATA_PIPELINE_STATISTICS;
typedef struct D3D11_DRAW_INSTANCED_INDIRECT_ARGS {
    UINT VertexCountPerInstance;
    UINT InstanceCount;
    UINT StartVertexLocation;
    UINT StartInstanceLocation;
} D3D11_DRAW_INSTANCED_INDIRECT_ARGS;
typedef struct D3D11_DRAW_INDEXED_INSTANCED_INDIRECT_ARGS {
    UINT IndexCountPerInstance;
    UINT InstanceCount;
    UINT StartIndexLocation;
    INT BaseVertexLocation;
    UINT StartInstanceLocation;
} D3D11_DRAW_INDEXED_INSTANCED_INDIRECT_ARGS;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_INPUT {
    D3D11_OMAC omac;
    GUID ConfigureType;
    HANDLE hChannel;
    UINT SequenceNumber;
} D3D11_AUTHENTICATED_CONFIGURE_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_INPUT {
    GUID QueryType;
    HANDLE hChannel;
    UINT SequenceNumber;
} D3D11_AUTHENTICATED_QUERY_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT {
    D3D11_OMAC omac;
    GUID QueryType;
    HANDLE hChannel;
    UINT SequenceNumber;
    HRESULT ReturnCode;
} D3D11_AUTHENTICATED_QUERY_OUTPUT;
typedef union D3D11_AUTHENTICATED_PROTECTION_FLAGS {
    struct {
        UINT ProtectionEnabled : 1;
        UINT OverlayOrFullscreenRequired : 1;
        UINT Reserved : 30;
    } Flags;
    UINT Value;
} D3D11_AUTHENTICATED_PROTECTION_FLAGS;
typedef struct D3D11_AUTHENTICATED_QUERY_PROTECTION_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    D3D11_AUTHENTICATED_PROTECTION_FLAGS ProtectionFlags;
} D3D11_AUTHENTICATED_QUERY_PROTECTION_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_CHANNEL_TYPE_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType;
} D3D11_AUTHENTICATED_QUERY_CHANNEL_TYPE_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_DEVICE_HANDLE_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DeviceHandle;
} D3D11_AUTHENTICATED_QUERY_DEVICE_HANDLE_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_INPUT {
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    HANDLE DecoderHandle;
} D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DecoderHandle;
    HANDLE CryptoSessionHandle;
    HANDLE DeviceHandle;
} D3D11_AUTHENTICATED_QUERY_CRYPTO_SESSION_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_COUNT_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT RestrictedSharedResourceProcessCount;
} D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_COUNT_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_INPUT {
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    UINT ProcessIndex;
} D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT ProcessIndex;
    D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE ProcessIdentifier;
    HANDLE ProcessHandle;
} D3D11_AUTHENTICATED_QUERY_RESTRICTED_SHARED_RESOURCE_PROCESS_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_UNRESTRICTED_PROTECTED_SHARED_RESOURCE_COUNT_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT UnrestrictedProtectedSharedResourceCount;
} D3D11_AUTHENTICATED_QUERY_UNRESTRICTED_PROTECTED_SHARED_RESOURCE_COUNT_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_INPUT {
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
    UINT OutputIDCount;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_COUNT_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_INPUT {
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
    UINT OutputIDIndex;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    HANDLE DeviceHandle;
    HANDLE CryptoSessionHandle;
    UINT OutputIDIndex;
    UINT64 OutputID;
} D3D11_AUTHENTICATED_QUERY_OUTPUT_ID_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_ACESSIBILITY_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    D3D11_BUS_TYPE BusType;
    WINBOOL AccessibleInContiguousBlocks;
    WINBOOL AccessibleInNonContiguousBlocks;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_COUNT_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT EncryptionGuidCount;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_COUNT_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_INPUT {
    D3D11_AUTHENTICATED_QUERY_INPUT Input;
    UINT EncryptionGuidIndex;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_INPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    UINT EncryptionGuidIndex;
    GUID EncryptionGuid;
} D3D11_AUTHENTICATED_QUERY_ACCESSIBILITY_ENCRYPTION_GUID_OUTPUT;
typedef struct D3D11_AUTHENTICATED_QUERY_CURRENT_ACCESSIBILITY_ENCRYPTION_OUTPUT {
    D3D11_AUTHENTICATED_QUERY_OUTPUT Output;
    GUID EncryptionGuid;
} D3D11_AUTHENTICATED_QUERY_CURRENT_ACCESSIBILITY_ENCRYPTION_OUTPUT;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_INITIALIZE_INPUT {
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    UINT StartSequenceQuery;
    UINT StartSequenceConfigure;
} D3D11_AUTHENTICATED_CONFIGURE_INITIALIZE_INPUT;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_PROTECTION_INPUT {
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    D3D11_AUTHENTICATED_PROTECTION_FLAGS Protections;
} D3D11_AUTHENTICATED_CONFIGURE_PROTECTION_INPUT;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_CRYPTO_SESSION_INPUT {
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    HANDLE DecoderHandle;
    HANDLE CryptoSessionHandle;
    HANDLE DeviceHandle;
} D3D11_AUTHENTICATED_CONFIGURE_CRYPTO_SESSION_INPUT;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_SHARED_RESOURCE_INPUT {
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    D3D11_AUTHENTICATED_PROCESS_IDENTIFIER_TYPE ProcessType;
    HANDLE ProcessHandle;
    WINBOOL AllowAccess;
} D3D11_AUTHENTICATED_CONFIGURE_SHARED_RESOURCE_INPUT;
typedef struct D3D11_AUTHENTICATED_CONFIGURE_ACCESSIBLE_ENCRYPTION_INPUT {
    D3D11_AUTHENTICATED_CONFIGURE_INPUT Parameters;
    GUID EncryptionGuid;
} D3D11_AUTHENTICATED_CONFIGURE_ACCESSIBLE_ENCRYPTION_INPUT;
/*****************************************************************************
 * ID3D11DeviceChild interface
 */
#ifndef __ID3D11DeviceChild_INTERFACE_DEFINED__
#define __ID3D11DeviceChild_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DeviceChild, 0x1841e5c8, 0x16b0, 0x489b, 0xbc,0xc8, 0x44,0xcf,0xb0,0xd5,0xde,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1841e5c8-16b0-489b-bcc8-44cfb0d5deae")
ID3D11DeviceChild : public IUnknown
{
    virtual void STDMETHODCALLTYPE GetDevice(
        ID3D11Device **ppDevice) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrivateData(
        REFGUID guid,
        UINT *pDataSize,
        void *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateData(
        REFGUID guid,
        UINT DataSize,
        const void *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(
        REFGUID guid,
        const IUnknown *pData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DeviceChild, 0x1841e5c8, 0x16b0, 0x489b, 0xbc,0xc8, 0x44,0xcf,0xb0,0xd5,0xde,0xae)
#endif
#else
typedef struct ID3D11DeviceChildVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DeviceChild *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DeviceChild *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DeviceChild *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DeviceChild *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DeviceChild *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DeviceChild *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DeviceChild *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11DeviceChildVtbl;

interface ID3D11DeviceChild {
    CONST_VTBL ID3D11DeviceChildVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DeviceChild_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DeviceChild_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DeviceChild_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DeviceChild_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DeviceChild_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DeviceChild_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DeviceChild_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11DeviceChild_QueryInterface(ID3D11DeviceChild* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11DeviceChild_AddRef(ID3D11DeviceChild* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11DeviceChild_Release(ID3D11DeviceChild* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11DeviceChild_GetDevice(ID3D11DeviceChild* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11DeviceChild_GetPrivateData(ID3D11DeviceChild* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DeviceChild_SetPrivateData(ID3D11DeviceChild* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DeviceChild_SetPrivateDataInterface(ID3D11DeviceChild* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11DeviceChild_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Asynchronous interface
 */
#ifndef __ID3D11Asynchronous_INTERFACE_DEFINED__
#define __ID3D11Asynchronous_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Asynchronous, 0x4b35d0cd, 0x1e15, 0x4258, 0x9c,0x98, 0x1b,0x13,0x33,0xf6,0xdd,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4b35d0cd-1e15-4258-9c98-1b1333f6dd3b")
ID3D11Asynchronous : public ID3D11DeviceChild
{
    virtual UINT STDMETHODCALLTYPE GetDataSize(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Asynchronous, 0x4b35d0cd, 0x1e15, 0x4258, 0x9c,0x98, 0x1b,0x13,0x33,0xf6,0xdd,0x3b)
#endif
#else
typedef struct ID3D11AsynchronousVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Asynchronous *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Asynchronous *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Asynchronous *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Asynchronous *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Asynchronous *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Asynchronous *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Asynchronous *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Asynchronous methods ***/
    UINT (STDMETHODCALLTYPE *GetDataSize)(
        ID3D11Asynchronous *This);

    END_INTERFACE
} ID3D11AsynchronousVtbl;

interface ID3D11Asynchronous {
    CONST_VTBL ID3D11AsynchronousVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Asynchronous_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Asynchronous_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Asynchronous_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Asynchronous_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Asynchronous_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Asynchronous_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Asynchronous_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Asynchronous methods ***/
#define ID3D11Asynchronous_GetDataSize(This) (This)->lpVtbl->GetDataSize(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Asynchronous_QueryInterface(ID3D11Asynchronous* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Asynchronous_AddRef(ID3D11Asynchronous* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Asynchronous_Release(ID3D11Asynchronous* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Asynchronous_GetDevice(ID3D11Asynchronous* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Asynchronous_GetPrivateData(ID3D11Asynchronous* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Asynchronous_SetPrivateData(ID3D11Asynchronous* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Asynchronous_SetPrivateDataInterface(ID3D11Asynchronous* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Asynchronous methods ***/
static __WIDL_INLINE UINT ID3D11Asynchronous_GetDataSize(ID3D11Asynchronous* This) {
    return This->lpVtbl->GetDataSize(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11Asynchronous_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Query interface
 */
#ifndef __ID3D11Query_INTERFACE_DEFINED__
#define __ID3D11Query_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Query, 0xd6c00747, 0x87b7, 0x425e, 0xb8,0x4d, 0x44,0xd1,0x08,0x56,0x0a,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d6c00747-87b7-425e-b84d-44d108560afd")
ID3D11Query : public ID3D11Asynchronous
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_QUERY_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Query, 0xd6c00747, 0x87b7, 0x425e, 0xb8,0x4d, 0x44,0xd1,0x08,0x56,0x0a,0xfd)
#endif
#else
typedef struct ID3D11QueryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Query *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Query *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Query *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Query *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Query *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Query *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Query *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Asynchronous methods ***/
    UINT (STDMETHODCALLTYPE *GetDataSize)(
        ID3D11Query *This);

    /*** ID3D11Query methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Query *This,
        D3D11_QUERY_DESC *pDesc);

    END_INTERFACE
} ID3D11QueryVtbl;

interface ID3D11Query {
    CONST_VTBL ID3D11QueryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Query_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Query_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Query_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Query_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Query_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Query_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Query_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Asynchronous methods ***/
#define ID3D11Query_GetDataSize(This) (This)->lpVtbl->GetDataSize(This)
/*** ID3D11Query methods ***/
#define ID3D11Query_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Query_QueryInterface(ID3D11Query* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Query_AddRef(ID3D11Query* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Query_Release(ID3D11Query* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Query_GetDevice(ID3D11Query* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Query_GetPrivateData(ID3D11Query* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Query_SetPrivateData(ID3D11Query* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Query_SetPrivateDataInterface(ID3D11Query* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Asynchronous methods ***/
static __WIDL_INLINE UINT ID3D11Query_GetDataSize(ID3D11Query* This) {
    return This->lpVtbl->GetDataSize(This);
}
/*** ID3D11Query methods ***/
static __WIDL_INLINE void ID3D11Query_GetDesc(ID3D11Query* This,D3D11_QUERY_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Query_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Resource interface
 */
#ifndef __ID3D11Resource_INTERFACE_DEFINED__
#define __ID3D11Resource_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Resource, 0xdc8e63f3, 0xd12b, 0x4952, 0xb4,0x7b, 0x5e,0x45,0x02,0x6a,0x86,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc8e63f3-d12b-4952-b47b-5e45026a862d")
ID3D11Resource : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetType(
        D3D11_RESOURCE_DIMENSION *pResourceDimension) = 0;

    virtual void STDMETHODCALLTYPE SetEvictionPriority(
        UINT EvictionPriority) = 0;

    virtual UINT STDMETHODCALLTYPE GetEvictionPriority(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Resource, 0xdc8e63f3, 0xd12b, 0x4952, 0xb4,0x7b, 0x5e,0x45,0x02,0x6a,0x86,0x2d)
#endif
#else
typedef struct ID3D11ResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Resource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Resource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Resource *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Resource *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Resource *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Resource *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Resource *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Resource *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Resource *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Resource *This);

    END_INTERFACE
} ID3D11ResourceVtbl;

interface ID3D11Resource {
    CONST_VTBL ID3D11ResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Resource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Resource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Resource_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Resource_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Resource_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Resource_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Resource_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Resource_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Resource_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Resource_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Resource_QueryInterface(ID3D11Resource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Resource_AddRef(ID3D11Resource* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Resource_Release(ID3D11Resource* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Resource_GetDevice(ID3D11Resource* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Resource_GetPrivateData(ID3D11Resource* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Resource_SetPrivateData(ID3D11Resource* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Resource_SetPrivateDataInterface(ID3D11Resource* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static __WIDL_INLINE void ID3D11Resource_GetType(ID3D11Resource* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static __WIDL_INLINE void ID3D11Resource_SetEvictionPriority(ID3D11Resource* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static __WIDL_INLINE UINT ID3D11Resource_GetEvictionPriority(ID3D11Resource* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11Resource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11View interface
 */
#ifndef __ID3D11View_INTERFACE_DEFINED__
#define __ID3D11View_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11View, 0x839d1216, 0xbb2e, 0x412b, 0xb7,0xf4, 0xa9,0xdb,0xeb,0xe0,0x8e,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("839d1216-bb2e-412b-b7f4-a9dbebe08ed1")
ID3D11View : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetResource(
        ID3D11Resource **ppResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11View, 0x839d1216, 0xbb2e, 0x412b, 0xb7,0xf4, 0xa9,0xdb,0xeb,0xe0,0x8e,0xd1)
#endif
#else
typedef struct ID3D11ViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11View *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11View *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11View *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11View *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11View *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11View *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11View *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11View *This,
        ID3D11Resource **ppResource);

    END_INTERFACE
} ID3D11ViewVtbl;

interface ID3D11View {
    CONST_VTBL ID3D11ViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11View_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11View_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11View_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11View_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11View_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11View_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11View_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11View_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11View_QueryInterface(ID3D11View* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11View_AddRef(ID3D11View* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11View_Release(ID3D11View* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11View_GetDevice(ID3D11View* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11View_GetPrivateData(ID3D11View* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11View_SetPrivateData(ID3D11View* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11View_SetPrivateDataInterface(ID3D11View* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11View_GetResource(ID3D11View* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
#endif
#endif

#endif


#endif  /* __ID3D11View_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11BlendState interface
 */
#ifndef __ID3D11BlendState_INTERFACE_DEFINED__
#define __ID3D11BlendState_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11BlendState, 0x75b68faa, 0x347d, 0x4159, 0x8f,0x45, 0xa0,0x64,0x0f,0x01,0xcd,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("75b68faa-347d-4159-8f45-a0640f01cd9a")
ID3D11BlendState : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_BLEND_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11BlendState, 0x75b68faa, 0x347d, 0x4159, 0x8f,0x45, 0xa0,0x64,0x0f,0x01,0xcd,0x9a)
#endif
#else
typedef struct ID3D11BlendStateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11BlendState *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11BlendState *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11BlendState *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11BlendState *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11BlendState *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11BlendState *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11BlendState *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11BlendState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11BlendState *This,
        D3D11_BLEND_DESC *pDesc);

    END_INTERFACE
} ID3D11BlendStateVtbl;

interface ID3D11BlendState {
    CONST_VTBL ID3D11BlendStateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11BlendState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11BlendState_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11BlendState_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11BlendState_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11BlendState_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11BlendState_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11BlendState_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11BlendState methods ***/
#define ID3D11BlendState_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11BlendState_QueryInterface(ID3D11BlendState* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11BlendState_AddRef(ID3D11BlendState* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11BlendState_Release(ID3D11BlendState* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11BlendState_GetDevice(ID3D11BlendState* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11BlendState_GetPrivateData(ID3D11BlendState* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11BlendState_SetPrivateData(ID3D11BlendState* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11BlendState_SetPrivateDataInterface(ID3D11BlendState* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11BlendState methods ***/
static __WIDL_INLINE void ID3D11BlendState_GetDesc(ID3D11BlendState* This,D3D11_BLEND_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11BlendState_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Buffer interface
 */
#ifndef __ID3D11Buffer_INTERFACE_DEFINED__
#define __ID3D11Buffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Buffer, 0x48570b85, 0xd1ee, 0x4fcd, 0xa2,0x50, 0xeb,0x35,0x07,0x22,0xb0,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("48570b85-d1ee-4fcd-a250-eb350722b037")
ID3D11Buffer : public ID3D11Resource
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_BUFFER_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Buffer, 0x48570b85, 0xd1ee, 0x4fcd, 0xa2,0x50, 0xeb,0x35,0x07,0x22,0xb0,0x37)
#endif
#else
typedef struct ID3D11BufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Buffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Buffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Buffer *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Buffer *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Buffer *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Buffer *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Buffer *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Buffer *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Buffer *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Buffer *This);

    /*** ID3D11Buffer methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Buffer *This,
        D3D11_BUFFER_DESC *pDesc);

    END_INTERFACE
} ID3D11BufferVtbl;

interface ID3D11Buffer {
    CONST_VTBL ID3D11BufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Buffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Buffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Buffer_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Buffer_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Buffer_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Buffer_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Buffer_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Buffer_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Buffer_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Buffer_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
/*** ID3D11Buffer methods ***/
#define ID3D11Buffer_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Buffer_QueryInterface(ID3D11Buffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Buffer_AddRef(ID3D11Buffer* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Buffer_Release(ID3D11Buffer* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Buffer_GetDevice(ID3D11Buffer* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Buffer_GetPrivateData(ID3D11Buffer* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Buffer_SetPrivateData(ID3D11Buffer* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Buffer_SetPrivateDataInterface(ID3D11Buffer* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static __WIDL_INLINE void ID3D11Buffer_GetType(ID3D11Buffer* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static __WIDL_INLINE void ID3D11Buffer_SetEvictionPriority(ID3D11Buffer* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static __WIDL_INLINE UINT ID3D11Buffer_GetEvictionPriority(ID3D11Buffer* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
/*** ID3D11Buffer methods ***/
static __WIDL_INLINE void ID3D11Buffer_GetDesc(ID3D11Buffer* This,D3D11_BUFFER_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Buffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11ClassInstance interface
 */
#ifndef __ID3D11ClassInstance_INTERFACE_DEFINED__
#define __ID3D11ClassInstance_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11ClassInstance, 0xa6cd7faa, 0xb0b7, 0x4a2f, 0x94,0x36, 0x86,0x62,0xa6,0x57,0x97,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a6cd7faa-b0b7-4a2f-9436-8662a65797cb")
ID3D11ClassInstance : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetClassLinkage(
        ID3D11ClassLinkage **ppLinkage) = 0;

    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_CLASS_INSTANCE_DESC *pDesc) = 0;

    virtual void STDMETHODCALLTYPE GetInstanceName(
        LPSTR pInstanceName,
        SIZE_T *pBufferLength) = 0;

    virtual void STDMETHODCALLTYPE GetTypeName(
        LPSTR pTypeName,
        SIZE_T *pBufferLength) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11ClassInstance, 0xa6cd7faa, 0xb0b7, 0x4a2f, 0x94,0x36, 0x86,0x62,0xa6,0x57,0x97,0xcb)
#endif
#else
typedef struct ID3D11ClassInstanceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11ClassInstance *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11ClassInstance *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11ClassInstance *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11ClassInstance *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11ClassInstance *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11ClassInstance *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11ClassInstance *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11ClassInstance methods ***/
    void (STDMETHODCALLTYPE *GetClassLinkage)(
        ID3D11ClassInstance *This,
        ID3D11ClassLinkage **ppLinkage);

    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11ClassInstance *This,
        D3D11_CLASS_INSTANCE_DESC *pDesc);

    void (STDMETHODCALLTYPE *GetInstanceName)(
        ID3D11ClassInstance *This,
        LPSTR pInstanceName,
        SIZE_T *pBufferLength);

    void (STDMETHODCALLTYPE *GetTypeName)(
        ID3D11ClassInstance *This,
        LPSTR pTypeName,
        SIZE_T *pBufferLength);

    END_INTERFACE
} ID3D11ClassInstanceVtbl;

interface ID3D11ClassInstance {
    CONST_VTBL ID3D11ClassInstanceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11ClassInstance_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11ClassInstance_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11ClassInstance_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11ClassInstance_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11ClassInstance_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11ClassInstance_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11ClassInstance_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11ClassInstance methods ***/
#define ID3D11ClassInstance_GetClassLinkage(This,ppLinkage) (This)->lpVtbl->GetClassLinkage(This,ppLinkage)
#define ID3D11ClassInstance_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#define ID3D11ClassInstance_GetInstanceName(This,pInstanceName,pBufferLength) (This)->lpVtbl->GetInstanceName(This,pInstanceName,pBufferLength)
#define ID3D11ClassInstance_GetTypeName(This,pTypeName,pBufferLength) (This)->lpVtbl->GetTypeName(This,pTypeName,pBufferLength)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11ClassInstance_QueryInterface(ID3D11ClassInstance* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11ClassInstance_AddRef(ID3D11ClassInstance* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11ClassInstance_Release(ID3D11ClassInstance* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11ClassInstance_GetDevice(ID3D11ClassInstance* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11ClassInstance_GetPrivateData(ID3D11ClassInstance* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ClassInstance_SetPrivateData(ID3D11ClassInstance* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ClassInstance_SetPrivateDataInterface(ID3D11ClassInstance* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11ClassInstance methods ***/
static __WIDL_INLINE void ID3D11ClassInstance_GetClassLinkage(ID3D11ClassInstance* This,ID3D11ClassLinkage **ppLinkage) {
    This->lpVtbl->GetClassLinkage(This,ppLinkage);
}
static __WIDL_INLINE void ID3D11ClassInstance_GetDesc(ID3D11ClassInstance* This,D3D11_CLASS_INSTANCE_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
static __WIDL_INLINE void ID3D11ClassInstance_GetInstanceName(ID3D11ClassInstance* This,LPSTR pInstanceName,SIZE_T *pBufferLength) {
    This->lpVtbl->GetInstanceName(This,pInstanceName,pBufferLength);
}
static __WIDL_INLINE void ID3D11ClassInstance_GetTypeName(ID3D11ClassInstance* This,LPSTR pTypeName,SIZE_T *pBufferLength) {
    This->lpVtbl->GetTypeName(This,pTypeName,pBufferLength);
}
#endif
#endif

#endif


#endif  /* __ID3D11ClassInstance_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11ClassLinkage interface
 */
#ifndef __ID3D11ClassLinkage_INTERFACE_DEFINED__
#define __ID3D11ClassLinkage_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11ClassLinkage, 0xddf57cba, 0x9543, 0x46e4, 0xa1,0x2b, 0xf2,0x07,0xa0,0xfe,0x7f,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ddf57cba-9543-46e4-a12b-f207a0fe7fed")
ID3D11ClassLinkage : public ID3D11DeviceChild
{
    virtual HRESULT STDMETHODCALLTYPE GetClassInstance(
        LPCSTR pClassInstanceName,
        UINT InstanceIndex,
        ID3D11ClassInstance **ppInstance) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateClassInstance(
        LPCSTR pClassTypeName,
        UINT ConstantBufferOffset,
        UINT ConstantVectorOffset,
        UINT TextureOffset,
        UINT SamplerOffset,
        ID3D11ClassInstance **ppInstance) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11ClassLinkage, 0xddf57cba, 0x9543, 0x46e4, 0xa1,0x2b, 0xf2,0x07,0xa0,0xfe,0x7f,0xed)
#endif
#else
typedef struct ID3D11ClassLinkageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11ClassLinkage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11ClassLinkage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11ClassLinkage *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11ClassLinkage *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11ClassLinkage *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11ClassLinkage *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11ClassLinkage *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11ClassLinkage methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassInstance)(
        ID3D11ClassLinkage *This,
        LPCSTR pClassInstanceName,
        UINT InstanceIndex,
        ID3D11ClassInstance **ppInstance);

    HRESULT (STDMETHODCALLTYPE *CreateClassInstance)(
        ID3D11ClassLinkage *This,
        LPCSTR pClassTypeName,
        UINT ConstantBufferOffset,
        UINT ConstantVectorOffset,
        UINT TextureOffset,
        UINT SamplerOffset,
        ID3D11ClassInstance **ppInstance);

    END_INTERFACE
} ID3D11ClassLinkageVtbl;

interface ID3D11ClassLinkage {
    CONST_VTBL ID3D11ClassLinkageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11ClassLinkage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11ClassLinkage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11ClassLinkage_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11ClassLinkage_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11ClassLinkage_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11ClassLinkage_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11ClassLinkage_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11ClassLinkage methods ***/
#define ID3D11ClassLinkage_GetClassInstance(This,pClassInstanceName,InstanceIndex,ppInstance) (This)->lpVtbl->GetClassInstance(This,pClassInstanceName,InstanceIndex,ppInstance)
#define ID3D11ClassLinkage_CreateClassInstance(This,pClassTypeName,ConstantBufferOffset,ConstantVectorOffset,TextureOffset,SamplerOffset,ppInstance) (This)->lpVtbl->CreateClassInstance(This,pClassTypeName,ConstantBufferOffset,ConstantVectorOffset,TextureOffset,SamplerOffset,ppInstance)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11ClassLinkage_QueryInterface(ID3D11ClassLinkage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11ClassLinkage_AddRef(ID3D11ClassLinkage* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11ClassLinkage_Release(ID3D11ClassLinkage* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11ClassLinkage_GetDevice(ID3D11ClassLinkage* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11ClassLinkage_GetPrivateData(ID3D11ClassLinkage* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ClassLinkage_SetPrivateData(ID3D11ClassLinkage* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ClassLinkage_SetPrivateDataInterface(ID3D11ClassLinkage* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11ClassLinkage methods ***/
static __WIDL_INLINE HRESULT ID3D11ClassLinkage_GetClassInstance(ID3D11ClassLinkage* This,LPCSTR pClassInstanceName,UINT InstanceIndex,ID3D11ClassInstance **ppInstance) {
    return This->lpVtbl->GetClassInstance(This,pClassInstanceName,InstanceIndex,ppInstance);
}
static __WIDL_INLINE HRESULT ID3D11ClassLinkage_CreateClassInstance(ID3D11ClassLinkage* This,LPCSTR pClassTypeName,UINT ConstantBufferOffset,UINT ConstantVectorOffset,UINT TextureOffset,UINT SamplerOffset,ID3D11ClassInstance **ppInstance) {
    return This->lpVtbl->CreateClassInstance(This,pClassTypeName,ConstantBufferOffset,ConstantVectorOffset,TextureOffset,SamplerOffset,ppInstance);
}
#endif
#endif

#endif


#endif  /* __ID3D11ClassLinkage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11CommandList interface
 */
#ifndef __ID3D11CommandList_INTERFACE_DEFINED__
#define __ID3D11CommandList_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11CommandList, 0xa24bc4d1, 0x769e, 0x43f7, 0x80,0x13, 0x98,0xff,0x56,0x6c,0x18,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a24bc4d1-769e-43f7-8013-98ff566c18e2")
ID3D11CommandList : public ID3D11DeviceChild
{
    virtual UINT STDMETHODCALLTYPE GetContextFlags(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11CommandList, 0xa24bc4d1, 0x769e, 0x43f7, 0x80,0x13, 0x98,0xff,0x56,0x6c,0x18,0xe2)
#endif
#else
typedef struct ID3D11CommandListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11CommandList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11CommandList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11CommandList *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11CommandList *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11CommandList *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11CommandList *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11CommandList *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11CommandList methods ***/
    UINT (STDMETHODCALLTYPE *GetContextFlags)(
        ID3D11CommandList *This);

    END_INTERFACE
} ID3D11CommandListVtbl;

interface ID3D11CommandList {
    CONST_VTBL ID3D11CommandListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11CommandList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11CommandList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11CommandList_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11CommandList_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11CommandList_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11CommandList_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11CommandList_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11CommandList methods ***/
#define ID3D11CommandList_GetContextFlags(This) (This)->lpVtbl->GetContextFlags(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11CommandList_QueryInterface(ID3D11CommandList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11CommandList_AddRef(ID3D11CommandList* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11CommandList_Release(ID3D11CommandList* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11CommandList_GetDevice(ID3D11CommandList* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11CommandList_GetPrivateData(ID3D11CommandList* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11CommandList_SetPrivateData(ID3D11CommandList* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11CommandList_SetPrivateDataInterface(ID3D11CommandList* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11CommandList methods ***/
static __WIDL_INLINE UINT ID3D11CommandList_GetContextFlags(ID3D11CommandList* This) {
    return This->lpVtbl->GetContextFlags(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11CommandList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11ComputeShader interface
 */
#ifndef __ID3D11ComputeShader_INTERFACE_DEFINED__
#define __ID3D11ComputeShader_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11ComputeShader, 0x4f5b196e, 0xc2bd, 0x495e, 0xbd,0x01, 0x1f,0xde,0xd3,0x8e,0x49,0x69);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4f5b196e-c2bd-495e-bd01-1fded38e4969")
ID3D11ComputeShader : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11ComputeShader, 0x4f5b196e, 0xc2bd, 0x495e, 0xbd,0x01, 0x1f,0xde,0xd3,0x8e,0x49,0x69)
#endif
#else
typedef struct ID3D11ComputeShaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11ComputeShader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11ComputeShader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11ComputeShader *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11ComputeShader *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11ComputeShader *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11ComputeShader *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11ComputeShader *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11ComputeShaderVtbl;

interface ID3D11ComputeShader {
    CONST_VTBL ID3D11ComputeShaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11ComputeShader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11ComputeShader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11ComputeShader_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11ComputeShader_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11ComputeShader_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11ComputeShader_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11ComputeShader_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11ComputeShader_QueryInterface(ID3D11ComputeShader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11ComputeShader_AddRef(ID3D11ComputeShader* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11ComputeShader_Release(ID3D11ComputeShader* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11ComputeShader_GetDevice(ID3D11ComputeShader* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11ComputeShader_GetPrivateData(ID3D11ComputeShader* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ComputeShader_SetPrivateData(ID3D11ComputeShader* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ComputeShader_SetPrivateDataInterface(ID3D11ComputeShader* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11ComputeShader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Counter interface
 */
#ifndef __ID3D11Counter_INTERFACE_DEFINED__
#define __ID3D11Counter_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Counter, 0x6e8c49fb, 0xa371, 0x4770, 0xb4,0x40, 0x29,0x08,0x60,0x22,0xb7,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6e8c49fb-a371-4770-b440-29086022b741")
ID3D11Counter : public ID3D11Asynchronous
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_COUNTER_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Counter, 0x6e8c49fb, 0xa371, 0x4770, 0xb4,0x40, 0x29,0x08,0x60,0x22,0xb7,0x41)
#endif
#else
typedef struct ID3D11CounterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Counter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Counter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Counter *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Counter *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Counter *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Counter *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Counter *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Asynchronous methods ***/
    UINT (STDMETHODCALLTYPE *GetDataSize)(
        ID3D11Counter *This);

    /*** ID3D11Counter methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Counter *This,
        D3D11_COUNTER_DESC *pDesc);

    END_INTERFACE
} ID3D11CounterVtbl;

interface ID3D11Counter {
    CONST_VTBL ID3D11CounterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Counter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Counter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Counter_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Counter_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Counter_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Counter_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Counter_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Asynchronous methods ***/
#define ID3D11Counter_GetDataSize(This) (This)->lpVtbl->GetDataSize(This)
/*** ID3D11Counter methods ***/
#define ID3D11Counter_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Counter_QueryInterface(ID3D11Counter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Counter_AddRef(ID3D11Counter* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Counter_Release(ID3D11Counter* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Counter_GetDevice(ID3D11Counter* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Counter_GetPrivateData(ID3D11Counter* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Counter_SetPrivateData(ID3D11Counter* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Counter_SetPrivateDataInterface(ID3D11Counter* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Asynchronous methods ***/
static __WIDL_INLINE UINT ID3D11Counter_GetDataSize(ID3D11Counter* This) {
    return This->lpVtbl->GetDataSize(This);
}
/*** ID3D11Counter methods ***/
static __WIDL_INLINE void ID3D11Counter_GetDesc(ID3D11Counter* This,D3D11_COUNTER_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Counter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DepthStencilState interface
 */
#ifndef __ID3D11DepthStencilState_INTERFACE_DEFINED__
#define __ID3D11DepthStencilState_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DepthStencilState, 0x03823efb, 0x8d8f, 0x4e1c, 0x9a,0xa2, 0xf6,0x4b,0xb2,0xcb,0xfd,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("03823efb-8d8f-4e1c-9aa2-f64bb2cbfdf1")
ID3D11DepthStencilState : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_DEPTH_STENCIL_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DepthStencilState, 0x03823efb, 0x8d8f, 0x4e1c, 0x9a,0xa2, 0xf6,0x4b,0xb2,0xcb,0xfd,0xf1)
#endif
#else
typedef struct ID3D11DepthStencilStateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DepthStencilState *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DepthStencilState *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DepthStencilState *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DepthStencilState *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DepthStencilState *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DepthStencilState *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DepthStencilState *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11DepthStencilState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11DepthStencilState *This,
        D3D11_DEPTH_STENCIL_DESC *pDesc);

    END_INTERFACE
} ID3D11DepthStencilStateVtbl;

interface ID3D11DepthStencilState {
    CONST_VTBL ID3D11DepthStencilStateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DepthStencilState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DepthStencilState_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DepthStencilState_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DepthStencilState_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DepthStencilState_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DepthStencilState_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DepthStencilState_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11DepthStencilState methods ***/
#define ID3D11DepthStencilState_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11DepthStencilState_QueryInterface(ID3D11DepthStencilState* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11DepthStencilState_AddRef(ID3D11DepthStencilState* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11DepthStencilState_Release(ID3D11DepthStencilState* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11DepthStencilState_GetDevice(ID3D11DepthStencilState* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11DepthStencilState_GetPrivateData(ID3D11DepthStencilState* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DepthStencilState_SetPrivateData(ID3D11DepthStencilState* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DepthStencilState_SetPrivateDataInterface(ID3D11DepthStencilState* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11DepthStencilState methods ***/
static __WIDL_INLINE void ID3D11DepthStencilState_GetDesc(ID3D11DepthStencilState* This,D3D11_DEPTH_STENCIL_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11DepthStencilState_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DepthStencilView interface
 */
#ifndef __ID3D11DepthStencilView_INTERFACE_DEFINED__
#define __ID3D11DepthStencilView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DepthStencilView, 0x9fdac92a, 0x1876, 0x48c3, 0xaf,0xad, 0x25,0xb9,0x4f,0x84,0xa9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9fdac92a-1876-48c3-afad-25b94f84a9b6")
ID3D11DepthStencilView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DepthStencilView, 0x9fdac92a, 0x1876, 0x48c3, 0xaf,0xad, 0x25,0xb9,0x4f,0x84,0xa9,0xb6)
#endif
#else
typedef struct ID3D11DepthStencilViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DepthStencilView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DepthStencilView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DepthStencilView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DepthStencilView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DepthStencilView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DepthStencilView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DepthStencilView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11DepthStencilView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11DepthStencilView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11DepthStencilView *This,
        D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11DepthStencilViewVtbl;

interface ID3D11DepthStencilView {
    CONST_VTBL ID3D11DepthStencilViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DepthStencilView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DepthStencilView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DepthStencilView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DepthStencilView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DepthStencilView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DepthStencilView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DepthStencilView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11DepthStencilView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11DepthStencilView methods ***/
#define ID3D11DepthStencilView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11DepthStencilView_QueryInterface(ID3D11DepthStencilView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11DepthStencilView_AddRef(ID3D11DepthStencilView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11DepthStencilView_Release(ID3D11DepthStencilView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11DepthStencilView_GetDevice(ID3D11DepthStencilView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11DepthStencilView_GetPrivateData(ID3D11DepthStencilView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DepthStencilView_SetPrivateData(ID3D11DepthStencilView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DepthStencilView_SetPrivateDataInterface(ID3D11DepthStencilView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11DepthStencilView_GetResource(ID3D11DepthStencilView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11DepthStencilView methods ***/
static __WIDL_INLINE void ID3D11DepthStencilView_GetDesc(ID3D11DepthStencilView* This,D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11DepthStencilView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DomainShader interface
 */
#ifndef __ID3D11DomainShader_INTERFACE_DEFINED__
#define __ID3D11DomainShader_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DomainShader, 0xf582c508, 0x0f36, 0x490c, 0x99,0x77, 0x31,0xee,0xce,0x26,0x8c,0xfa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f582c508-0f36-490c-9977-31eece268cfa")
ID3D11DomainShader : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DomainShader, 0xf582c508, 0x0f36, 0x490c, 0x99,0x77, 0x31,0xee,0xce,0x26,0x8c,0xfa)
#endif
#else
typedef struct ID3D11DomainShaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DomainShader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DomainShader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DomainShader *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DomainShader *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DomainShader *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DomainShader *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DomainShader *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11DomainShaderVtbl;

interface ID3D11DomainShader {
    CONST_VTBL ID3D11DomainShaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DomainShader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DomainShader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DomainShader_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DomainShader_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DomainShader_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DomainShader_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DomainShader_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11DomainShader_QueryInterface(ID3D11DomainShader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11DomainShader_AddRef(ID3D11DomainShader* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11DomainShader_Release(ID3D11DomainShader* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11DomainShader_GetDevice(ID3D11DomainShader* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11DomainShader_GetPrivateData(ID3D11DomainShader* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DomainShader_SetPrivateData(ID3D11DomainShader* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DomainShader_SetPrivateDataInterface(ID3D11DomainShader* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11DomainShader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11GeometryShader interface
 */
#ifndef __ID3D11GeometryShader_INTERFACE_DEFINED__
#define __ID3D11GeometryShader_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11GeometryShader, 0x38325b96, 0xeffb, 0x4022, 0xba,0x02, 0x2e,0x79,0x5b,0x70,0x27,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("38325b96-effb-4022-ba02-2e795b70275c")
ID3D11GeometryShader : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11GeometryShader, 0x38325b96, 0xeffb, 0x4022, 0xba,0x02, 0x2e,0x79,0x5b,0x70,0x27,0x5c)
#endif
#else
typedef struct ID3D11GeometryShaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11GeometryShader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11GeometryShader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11GeometryShader *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11GeometryShader *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11GeometryShader *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11GeometryShader *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11GeometryShader *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11GeometryShaderVtbl;

interface ID3D11GeometryShader {
    CONST_VTBL ID3D11GeometryShaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11GeometryShader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11GeometryShader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11GeometryShader_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11GeometryShader_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11GeometryShader_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11GeometryShader_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11GeometryShader_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11GeometryShader_QueryInterface(ID3D11GeometryShader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11GeometryShader_AddRef(ID3D11GeometryShader* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11GeometryShader_Release(ID3D11GeometryShader* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11GeometryShader_GetDevice(ID3D11GeometryShader* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11GeometryShader_GetPrivateData(ID3D11GeometryShader* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11GeometryShader_SetPrivateData(ID3D11GeometryShader* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11GeometryShader_SetPrivateDataInterface(ID3D11GeometryShader* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11GeometryShader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11HullShader interface
 */
#ifndef __ID3D11HullShader_INTERFACE_DEFINED__
#define __ID3D11HullShader_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11HullShader, 0x8e5c6061, 0x628a, 0x4c8e, 0x82,0x64, 0xbb,0xe4,0x5c,0xb3,0xd5,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8e5c6061-628a-4c8e-8264-bbe45cb3d5dd")
ID3D11HullShader : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11HullShader, 0x8e5c6061, 0x628a, 0x4c8e, 0x82,0x64, 0xbb,0xe4,0x5c,0xb3,0xd5,0xdd)
#endif
#else
typedef struct ID3D11HullShaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11HullShader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11HullShader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11HullShader *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11HullShader *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11HullShader *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11HullShader *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11HullShader *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11HullShaderVtbl;

interface ID3D11HullShader {
    CONST_VTBL ID3D11HullShaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11HullShader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11HullShader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11HullShader_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11HullShader_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11HullShader_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11HullShader_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11HullShader_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11HullShader_QueryInterface(ID3D11HullShader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11HullShader_AddRef(ID3D11HullShader* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11HullShader_Release(ID3D11HullShader* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11HullShader_GetDevice(ID3D11HullShader* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11HullShader_GetPrivateData(ID3D11HullShader* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11HullShader_SetPrivateData(ID3D11HullShader* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11HullShader_SetPrivateDataInterface(ID3D11HullShader* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11HullShader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11InputLayout interface
 */
#ifndef __ID3D11InputLayout_INTERFACE_DEFINED__
#define __ID3D11InputLayout_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11InputLayout, 0xe4819ddc, 0x4cf0, 0x4025, 0xbd,0x26, 0x5d,0xe8,0x2a,0x3e,0x07,0xb7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e4819ddc-4cf0-4025-bd26-5de82a3e07b7")
ID3D11InputLayout : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11InputLayout, 0xe4819ddc, 0x4cf0, 0x4025, 0xbd,0x26, 0x5d,0xe8,0x2a,0x3e,0x07,0xb7)
#endif
#else
typedef struct ID3D11InputLayoutVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11InputLayout *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11InputLayout *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11InputLayout *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11InputLayout *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11InputLayout *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11InputLayout *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11InputLayout *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11InputLayoutVtbl;

interface ID3D11InputLayout {
    CONST_VTBL ID3D11InputLayoutVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11InputLayout_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11InputLayout_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11InputLayout_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11InputLayout_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11InputLayout_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11InputLayout_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11InputLayout_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11InputLayout_QueryInterface(ID3D11InputLayout* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11InputLayout_AddRef(ID3D11InputLayout* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11InputLayout_Release(ID3D11InputLayout* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11InputLayout_GetDevice(ID3D11InputLayout* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11InputLayout_GetPrivateData(ID3D11InputLayout* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11InputLayout_SetPrivateData(ID3D11InputLayout* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11InputLayout_SetPrivateDataInterface(ID3D11InputLayout* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11InputLayout_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11PixelShader interface
 */
#ifndef __ID3D11PixelShader_INTERFACE_DEFINED__
#define __ID3D11PixelShader_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11PixelShader, 0xea82e40d, 0x51dc, 0x4f33, 0x93,0xd4, 0xdb,0x7c,0x91,0x25,0xae,0x8c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea82e40d-51dc-4f33-93d4-db7c9125ae8c")
ID3D11PixelShader : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11PixelShader, 0xea82e40d, 0x51dc, 0x4f33, 0x93,0xd4, 0xdb,0x7c,0x91,0x25,0xae,0x8c)
#endif
#else
typedef struct ID3D11PixelShaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11PixelShader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11PixelShader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11PixelShader *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11PixelShader *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11PixelShader *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11PixelShader *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11PixelShader *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11PixelShaderVtbl;

interface ID3D11PixelShader {
    CONST_VTBL ID3D11PixelShaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11PixelShader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11PixelShader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11PixelShader_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11PixelShader_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11PixelShader_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11PixelShader_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11PixelShader_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11PixelShader_QueryInterface(ID3D11PixelShader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11PixelShader_AddRef(ID3D11PixelShader* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11PixelShader_Release(ID3D11PixelShader* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11PixelShader_GetDevice(ID3D11PixelShader* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11PixelShader_GetPrivateData(ID3D11PixelShader* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11PixelShader_SetPrivateData(ID3D11PixelShader* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11PixelShader_SetPrivateDataInterface(ID3D11PixelShader* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11PixelShader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Predicate interface
 */
#ifndef __ID3D11Predicate_INTERFACE_DEFINED__
#define __ID3D11Predicate_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Predicate, 0x9eb576dd, 0x9f77, 0x4d86, 0x81,0xaa, 0x8b,0xab,0x5f,0xe4,0x90,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9eb576dd-9f77-4d86-81aa-8bab5fe490e2")
ID3D11Predicate : public ID3D11Query
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Predicate, 0x9eb576dd, 0x9f77, 0x4d86, 0x81,0xaa, 0x8b,0xab,0x5f,0xe4,0x90,0xe2)
#endif
#else
typedef struct ID3D11PredicateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Predicate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Predicate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Predicate *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Predicate *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Predicate *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Predicate *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Predicate *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Asynchronous methods ***/
    UINT (STDMETHODCALLTYPE *GetDataSize)(
        ID3D11Predicate *This);

    /*** ID3D11Query methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Predicate *This,
        D3D11_QUERY_DESC *pDesc);

    END_INTERFACE
} ID3D11PredicateVtbl;

interface ID3D11Predicate {
    CONST_VTBL ID3D11PredicateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Predicate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Predicate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Predicate_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Predicate_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Predicate_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Predicate_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Predicate_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Asynchronous methods ***/
#define ID3D11Predicate_GetDataSize(This) (This)->lpVtbl->GetDataSize(This)
/*** ID3D11Query methods ***/
#define ID3D11Predicate_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Predicate_QueryInterface(ID3D11Predicate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Predicate_AddRef(ID3D11Predicate* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Predicate_Release(ID3D11Predicate* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Predicate_GetDevice(ID3D11Predicate* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Predicate_GetPrivateData(ID3D11Predicate* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Predicate_SetPrivateData(ID3D11Predicate* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Predicate_SetPrivateDataInterface(ID3D11Predicate* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Asynchronous methods ***/
static __WIDL_INLINE UINT ID3D11Predicate_GetDataSize(ID3D11Predicate* This) {
    return This->lpVtbl->GetDataSize(This);
}
/*** ID3D11Query methods ***/
static __WIDL_INLINE void ID3D11Predicate_GetDesc(ID3D11Predicate* This,D3D11_QUERY_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Predicate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11RasterizerState interface
 */
#ifndef __ID3D11RasterizerState_INTERFACE_DEFINED__
#define __ID3D11RasterizerState_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11RasterizerState, 0x9bb4ab81, 0xab1a, 0x4d8f, 0xb5,0x06, 0xfc,0x04,0x20,0x0b,0x6e,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9bb4ab81-ab1a-4d8f-b506-fc04200b6ee7")
ID3D11RasterizerState : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_RASTERIZER_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11RasterizerState, 0x9bb4ab81, 0xab1a, 0x4d8f, 0xb5,0x06, 0xfc,0x04,0x20,0x0b,0x6e,0xe7)
#endif
#else
typedef struct ID3D11RasterizerStateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11RasterizerState *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11RasterizerState *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11RasterizerState *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11RasterizerState *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11RasterizerState *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11RasterizerState *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11RasterizerState *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11RasterizerState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11RasterizerState *This,
        D3D11_RASTERIZER_DESC *pDesc);

    END_INTERFACE
} ID3D11RasterizerStateVtbl;

interface ID3D11RasterizerState {
    CONST_VTBL ID3D11RasterizerStateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11RasterizerState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11RasterizerState_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11RasterizerState_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11RasterizerState_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11RasterizerState_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11RasterizerState_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11RasterizerState_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11RasterizerState methods ***/
#define ID3D11RasterizerState_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11RasterizerState_QueryInterface(ID3D11RasterizerState* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11RasterizerState_AddRef(ID3D11RasterizerState* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11RasterizerState_Release(ID3D11RasterizerState* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11RasterizerState_GetDevice(ID3D11RasterizerState* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11RasterizerState_GetPrivateData(ID3D11RasterizerState* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11RasterizerState_SetPrivateData(ID3D11RasterizerState* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11RasterizerState_SetPrivateDataInterface(ID3D11RasterizerState* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11RasterizerState methods ***/
static __WIDL_INLINE void ID3D11RasterizerState_GetDesc(ID3D11RasterizerState* This,D3D11_RASTERIZER_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11RasterizerState_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11RenderTargetView interface
 */
#ifndef __ID3D11RenderTargetView_INTERFACE_DEFINED__
#define __ID3D11RenderTargetView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11RenderTargetView, 0xdfdba067, 0x0b8d, 0x4865, 0x87,0x5b, 0xd7,0xb4,0x51,0x6c,0xc1,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dfdba067-0b8d-4865-875b-d7b4516cc164")
ID3D11RenderTargetView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_RENDER_TARGET_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11RenderTargetView, 0xdfdba067, 0x0b8d, 0x4865, 0x87,0x5b, 0xd7,0xb4,0x51,0x6c,0xc1,0x64)
#endif
#else
typedef struct ID3D11RenderTargetViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11RenderTargetView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11RenderTargetView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11RenderTargetView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11RenderTargetView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11RenderTargetView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11RenderTargetView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11RenderTargetView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11RenderTargetView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11RenderTargetView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11RenderTargetView *This,
        D3D11_RENDER_TARGET_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11RenderTargetViewVtbl;

interface ID3D11RenderTargetView {
    CONST_VTBL ID3D11RenderTargetViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11RenderTargetView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11RenderTargetView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11RenderTargetView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11RenderTargetView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11RenderTargetView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11RenderTargetView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11RenderTargetView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11RenderTargetView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11RenderTargetView methods ***/
#define ID3D11RenderTargetView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11RenderTargetView_QueryInterface(ID3D11RenderTargetView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11RenderTargetView_AddRef(ID3D11RenderTargetView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11RenderTargetView_Release(ID3D11RenderTargetView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11RenderTargetView_GetDevice(ID3D11RenderTargetView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11RenderTargetView_GetPrivateData(ID3D11RenderTargetView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11RenderTargetView_SetPrivateData(ID3D11RenderTargetView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11RenderTargetView_SetPrivateDataInterface(ID3D11RenderTargetView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11RenderTargetView_GetResource(ID3D11RenderTargetView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11RenderTargetView methods ***/
static __WIDL_INLINE void ID3D11RenderTargetView_GetDesc(ID3D11RenderTargetView* This,D3D11_RENDER_TARGET_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11RenderTargetView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11SamplerState interface
 */
#ifndef __ID3D11SamplerState_INTERFACE_DEFINED__
#define __ID3D11SamplerState_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11SamplerState, 0xda6fea51, 0x564c, 0x4487, 0x98,0x10, 0xf0,0xd0,0xf9,0xb4,0xe3,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("da6fea51-564c-4487-9810-f0d0f9b4e3a5")
ID3D11SamplerState : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_SAMPLER_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11SamplerState, 0xda6fea51, 0x564c, 0x4487, 0x98,0x10, 0xf0,0xd0,0xf9,0xb4,0xe3,0xa5)
#endif
#else
typedef struct ID3D11SamplerStateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11SamplerState *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11SamplerState *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11SamplerState *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11SamplerState *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11SamplerState *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11SamplerState *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11SamplerState *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11SamplerState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11SamplerState *This,
        D3D11_SAMPLER_DESC *pDesc);

    END_INTERFACE
} ID3D11SamplerStateVtbl;

interface ID3D11SamplerState {
    CONST_VTBL ID3D11SamplerStateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11SamplerState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11SamplerState_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11SamplerState_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11SamplerState_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11SamplerState_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11SamplerState_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11SamplerState_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11SamplerState methods ***/
#define ID3D11SamplerState_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11SamplerState_QueryInterface(ID3D11SamplerState* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11SamplerState_AddRef(ID3D11SamplerState* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11SamplerState_Release(ID3D11SamplerState* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11SamplerState_GetDevice(ID3D11SamplerState* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11SamplerState_GetPrivateData(ID3D11SamplerState* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11SamplerState_SetPrivateData(ID3D11SamplerState* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11SamplerState_SetPrivateDataInterface(ID3D11SamplerState* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11SamplerState methods ***/
static __WIDL_INLINE void ID3D11SamplerState_GetDesc(ID3D11SamplerState* This,D3D11_SAMPLER_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11SamplerState_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11ShaderResourceView interface
 */
#ifndef __ID3D11ShaderResourceView_INTERFACE_DEFINED__
#define __ID3D11ShaderResourceView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11ShaderResourceView, 0xb0e06fe0, 0x8192, 0x4e1a, 0xb1,0xca, 0x36,0xd7,0x41,0x47,0x10,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b0e06fe0-8192-4e1a-b1ca-36d7414710b2")
ID3D11ShaderResourceView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11ShaderResourceView, 0xb0e06fe0, 0x8192, 0x4e1a, 0xb1,0xca, 0x36,0xd7,0x41,0x47,0x10,0xb2)
#endif
#else
typedef struct ID3D11ShaderResourceViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11ShaderResourceView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11ShaderResourceView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11ShaderResourceView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11ShaderResourceView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11ShaderResourceView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11ShaderResourceView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11ShaderResourceView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11ShaderResourceView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11ShaderResourceView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11ShaderResourceView *This,
        D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11ShaderResourceViewVtbl;

interface ID3D11ShaderResourceView {
    CONST_VTBL ID3D11ShaderResourceViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11ShaderResourceView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11ShaderResourceView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11ShaderResourceView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11ShaderResourceView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11ShaderResourceView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11ShaderResourceView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11ShaderResourceView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11ShaderResourceView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11ShaderResourceView methods ***/
#define ID3D11ShaderResourceView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11ShaderResourceView_QueryInterface(ID3D11ShaderResourceView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11ShaderResourceView_AddRef(ID3D11ShaderResourceView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11ShaderResourceView_Release(ID3D11ShaderResourceView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11ShaderResourceView_GetDevice(ID3D11ShaderResourceView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11ShaderResourceView_GetPrivateData(ID3D11ShaderResourceView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ShaderResourceView_SetPrivateData(ID3D11ShaderResourceView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11ShaderResourceView_SetPrivateDataInterface(ID3D11ShaderResourceView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11ShaderResourceView_GetResource(ID3D11ShaderResourceView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11ShaderResourceView methods ***/
static __WIDL_INLINE void ID3D11ShaderResourceView_GetDesc(ID3D11ShaderResourceView* This,D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11ShaderResourceView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Texture1D interface
 */
#ifndef __ID3D11Texture1D_INTERFACE_DEFINED__
#define __ID3D11Texture1D_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Texture1D, 0xf8fb5c27, 0xc6b3, 0x4f75, 0xa4,0xc8, 0x43,0x9a,0xf2,0xef,0x56,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f8fb5c27-c6b3-4f75-a4c8-439af2ef564c")
ID3D11Texture1D : public ID3D11Resource
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_TEXTURE1D_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Texture1D, 0xf8fb5c27, 0xc6b3, 0x4f75, 0xa4,0xc8, 0x43,0x9a,0xf2,0xef,0x56,0x4c)
#endif
#else
typedef struct ID3D11Texture1DVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Texture1D *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Texture1D *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Texture1D *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Texture1D *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Texture1D *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Texture1D *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Texture1D *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Texture1D *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Texture1D *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Texture1D *This);

    /*** ID3D11Texture1D methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Texture1D *This,
        D3D11_TEXTURE1D_DESC *pDesc);

    END_INTERFACE
} ID3D11Texture1DVtbl;

interface ID3D11Texture1D {
    CONST_VTBL ID3D11Texture1DVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Texture1D_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Texture1D_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Texture1D_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Texture1D_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Texture1D_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Texture1D_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Texture1D_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Texture1D_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Texture1D_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Texture1D_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
/*** ID3D11Texture1D methods ***/
#define ID3D11Texture1D_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Texture1D_QueryInterface(ID3D11Texture1D* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Texture1D_AddRef(ID3D11Texture1D* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Texture1D_Release(ID3D11Texture1D* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Texture1D_GetDevice(ID3D11Texture1D* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Texture1D_GetPrivateData(ID3D11Texture1D* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Texture1D_SetPrivateData(ID3D11Texture1D* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Texture1D_SetPrivateDataInterface(ID3D11Texture1D* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static __WIDL_INLINE void ID3D11Texture1D_GetType(ID3D11Texture1D* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static __WIDL_INLINE void ID3D11Texture1D_SetEvictionPriority(ID3D11Texture1D* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static __WIDL_INLINE UINT ID3D11Texture1D_GetEvictionPriority(ID3D11Texture1D* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
/*** ID3D11Texture1D methods ***/
static __WIDL_INLINE void ID3D11Texture1D_GetDesc(ID3D11Texture1D* This,D3D11_TEXTURE1D_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Texture1D_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Texture2D interface
 */
#ifndef __ID3D11Texture2D_INTERFACE_DEFINED__
#define __ID3D11Texture2D_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Texture2D, 0x6f15aaf2, 0xd208, 0x4e89, 0x9a,0xb4, 0x48,0x95,0x35,0xd3,0x4f,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6f15aaf2-d208-4e89-9ab4-489535d34f9c")
ID3D11Texture2D : public ID3D11Resource
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_TEXTURE2D_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Texture2D, 0x6f15aaf2, 0xd208, 0x4e89, 0x9a,0xb4, 0x48,0x95,0x35,0xd3,0x4f,0x9c)
#endif
#else
typedef struct ID3D11Texture2DVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Texture2D *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Texture2D *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Texture2D *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Texture2D *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Texture2D *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Texture2D *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Texture2D *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Texture2D *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Texture2D *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Texture2D *This);

    /*** ID3D11Texture2D methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Texture2D *This,
        D3D11_TEXTURE2D_DESC *pDesc);

    END_INTERFACE
} ID3D11Texture2DVtbl;

interface ID3D11Texture2D {
    CONST_VTBL ID3D11Texture2DVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Texture2D_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Texture2D_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Texture2D_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Texture2D_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Texture2D_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Texture2D_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Texture2D_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Texture2D_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Texture2D_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Texture2D_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
/*** ID3D11Texture2D methods ***/
#define ID3D11Texture2D_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Texture2D_QueryInterface(ID3D11Texture2D* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Texture2D_AddRef(ID3D11Texture2D* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Texture2D_Release(ID3D11Texture2D* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Texture2D_GetDevice(ID3D11Texture2D* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Texture2D_GetPrivateData(ID3D11Texture2D* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Texture2D_SetPrivateData(ID3D11Texture2D* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Texture2D_SetPrivateDataInterface(ID3D11Texture2D* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static __WIDL_INLINE void ID3D11Texture2D_GetType(ID3D11Texture2D* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static __WIDL_INLINE void ID3D11Texture2D_SetEvictionPriority(ID3D11Texture2D* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static __WIDL_INLINE UINT ID3D11Texture2D_GetEvictionPriority(ID3D11Texture2D* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
/*** ID3D11Texture2D methods ***/
static __WIDL_INLINE void ID3D11Texture2D_GetDesc(ID3D11Texture2D* This,D3D11_TEXTURE2D_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Texture2D_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Texture3D interface
 */
#ifndef __ID3D11Texture3D_INTERFACE_DEFINED__
#define __ID3D11Texture3D_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Texture3D, 0x037e866e, 0xf56d, 0x4357, 0xa8,0xaf, 0x9d,0xab,0xbe,0x6e,0x25,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("037e866e-f56d-4357-a8af-9dabbe6e250e")
ID3D11Texture3D : public ID3D11Resource
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_TEXTURE3D_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Texture3D, 0x037e866e, 0xf56d, 0x4357, 0xa8,0xaf, 0x9d,0xab,0xbe,0x6e,0x25,0x0e)
#endif
#else
typedef struct ID3D11Texture3DVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Texture3D *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Texture3D *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Texture3D *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Texture3D *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Texture3D *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Texture3D *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Texture3D *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Texture3D *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Texture3D *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Texture3D *This);

    /*** ID3D11Texture3D methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Texture3D *This,
        D3D11_TEXTURE3D_DESC *pDesc);

    END_INTERFACE
} ID3D11Texture3DVtbl;

interface ID3D11Texture3D {
    CONST_VTBL ID3D11Texture3DVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Texture3D_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Texture3D_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Texture3D_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Texture3D_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Texture3D_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Texture3D_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Texture3D_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Texture3D_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Texture3D_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Texture3D_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
/*** ID3D11Texture3D methods ***/
#define ID3D11Texture3D_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Texture3D_QueryInterface(ID3D11Texture3D* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Texture3D_AddRef(ID3D11Texture3D* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Texture3D_Release(ID3D11Texture3D* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11Texture3D_GetDevice(ID3D11Texture3D* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11Texture3D_GetPrivateData(ID3D11Texture3D* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Texture3D_SetPrivateData(ID3D11Texture3D* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Texture3D_SetPrivateDataInterface(ID3D11Texture3D* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static __WIDL_INLINE void ID3D11Texture3D_GetType(ID3D11Texture3D* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static __WIDL_INLINE void ID3D11Texture3D_SetEvictionPriority(ID3D11Texture3D* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static __WIDL_INLINE UINT ID3D11Texture3D_GetEvictionPriority(ID3D11Texture3D* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
/*** ID3D11Texture3D methods ***/
static __WIDL_INLINE void ID3D11Texture3D_GetDesc(ID3D11Texture3D* This,D3D11_TEXTURE3D_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Texture3D_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11UnorderedAccessView interface
 */
#ifndef __ID3D11UnorderedAccessView_INTERFACE_DEFINED__
#define __ID3D11UnorderedAccessView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11UnorderedAccessView, 0x28acf509, 0x7f5c, 0x48f6, 0x86,0x11, 0xf3,0x16,0x01,0x0a,0x63,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("28acf509-7f5c-48f6-8611-f316010a6380")
ID3D11UnorderedAccessView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11UnorderedAccessView, 0x28acf509, 0x7f5c, 0x48f6, 0x86,0x11, 0xf3,0x16,0x01,0x0a,0x63,0x80)
#endif
#else
typedef struct ID3D11UnorderedAccessViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11UnorderedAccessView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11UnorderedAccessView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11UnorderedAccessView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11UnorderedAccessView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11UnorderedAccessView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11UnorderedAccessView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11UnorderedAccessView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11UnorderedAccessView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11UnorderedAccessView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11UnorderedAccessView *This,
        D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11UnorderedAccessViewVtbl;

interface ID3D11UnorderedAccessView {
    CONST_VTBL ID3D11UnorderedAccessViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11UnorderedAccessView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11UnorderedAccessView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11UnorderedAccessView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11UnorderedAccessView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11UnorderedAccessView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11UnorderedAccessView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11UnorderedAccessView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11UnorderedAccessView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11UnorderedAccessView methods ***/
#define ID3D11UnorderedAccessView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11UnorderedAccessView_QueryInterface(ID3D11UnorderedAccessView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11UnorderedAccessView_AddRef(ID3D11UnorderedAccessView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11UnorderedAccessView_Release(ID3D11UnorderedAccessView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11UnorderedAccessView_GetDevice(ID3D11UnorderedAccessView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11UnorderedAccessView_GetPrivateData(ID3D11UnorderedAccessView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11UnorderedAccessView_SetPrivateData(ID3D11UnorderedAccessView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11UnorderedAccessView_SetPrivateDataInterface(ID3D11UnorderedAccessView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11UnorderedAccessView_GetResource(ID3D11UnorderedAccessView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11UnorderedAccessView methods ***/
static __WIDL_INLINE void ID3D11UnorderedAccessView_GetDesc(ID3D11UnorderedAccessView* This,D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11UnorderedAccessView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VertexShader interface
 */
#ifndef __ID3D11VertexShader_INTERFACE_DEFINED__
#define __ID3D11VertexShader_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VertexShader, 0x3b301d64, 0xd678, 0x4289, 0x88,0x97, 0x22,0xf8,0x92,0x8b,0x72,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3b301d64-d678-4289-8897-22f8928b72f3")
ID3D11VertexShader : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VertexShader, 0x3b301d64, 0xd678, 0x4289, 0x88,0x97, 0x22,0xf8,0x92,0x8b,0x72,0xf3)
#endif
#else
typedef struct ID3D11VertexShaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VertexShader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VertexShader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VertexShader *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VertexShader *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VertexShader *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VertexShader *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VertexShader *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11VertexShaderVtbl;

interface ID3D11VertexShader {
    CONST_VTBL ID3D11VertexShaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VertexShader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VertexShader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VertexShader_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VertexShader_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VertexShader_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VertexShader_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VertexShader_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VertexShader_QueryInterface(ID3D11VertexShader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VertexShader_AddRef(ID3D11VertexShader* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VertexShader_Release(ID3D11VertexShader* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VertexShader_GetDevice(ID3D11VertexShader* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VertexShader_GetPrivateData(ID3D11VertexShader* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VertexShader_SetPrivateData(ID3D11VertexShader* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VertexShader_SetPrivateDataInterface(ID3D11VertexShader* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11VertexShader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DeviceContext interface
 */
#ifndef __ID3D11DeviceContext_INTERFACE_DEFINED__
#define __ID3D11DeviceContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DeviceContext, 0xc0bfa96c, 0xe089, 0x44fb, 0x8e,0xaf, 0x26,0xf8,0x79,0x61,0x90,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0bfa96c-e089-44fb-8eaf-26f8796190da")
ID3D11DeviceContext : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE VSSetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE PSSetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE PSSetShader(
        ID3D11PixelShader *pPixelShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE PSSetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE VSSetShader(
        ID3D11VertexShader *pVertexShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE DrawIndexed(
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation) = 0;

    virtual void STDMETHODCALLTYPE Draw(
        UINT VertexCount,
        UINT StartVertexLocation) = 0;

    virtual HRESULT STDMETHODCALLTYPE Map(
        ID3D11Resource *pResource,
        UINT Subresource,
        D3D11_MAP MapType,
        UINT MapFlags,
        D3D11_MAPPED_SUBRESOURCE *pMappedResource) = 0;

    virtual void STDMETHODCALLTYPE Unmap(
        ID3D11Resource *pResource,
        UINT Subresource) = 0;

    virtual void STDMETHODCALLTYPE PSSetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE IASetInputLayout(
        ID3D11InputLayout *pInputLayout) = 0;

    virtual void STDMETHODCALLTYPE IASetVertexBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets) = 0;

    virtual void STDMETHODCALLTYPE IASetIndexBuffer(
        ID3D11Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset) = 0;

    virtual void STDMETHODCALLTYPE DrawIndexedInstanced(
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation) = 0;

    virtual void STDMETHODCALLTYPE DrawInstanced(
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation) = 0;

    virtual void STDMETHODCALLTYPE GSSetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE GSSetShader(
        ID3D11GeometryShader *pShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE IASetPrimitiveTopology(
        D3D11_PRIMITIVE_TOPOLOGY Topology) = 0;

    virtual void STDMETHODCALLTYPE VSSetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE VSSetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE Begin(
        ID3D11Asynchronous *pAsync) = 0;

    virtual void STDMETHODCALLTYPE End(
        ID3D11Asynchronous *pAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetData(
        ID3D11Asynchronous *pAsync,
        void *pData,
        UINT DataSize,
        UINT GetDataFlags) = 0;

    virtual void STDMETHODCALLTYPE SetPredication(
        ID3D11Predicate *pPredicate,
        WINBOOL PredicateValue) = 0;

    virtual void STDMETHODCALLTYPE GSSetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE GSSetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE OMSetRenderTargets(
        UINT NumViews,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView) = 0;

    virtual void STDMETHODCALLTYPE OMSetRenderTargetsAndUnorderedAccessViews(
        UINT NumRTVs,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts) = 0;

    virtual void STDMETHODCALLTYPE OMSetBlendState(
        ID3D11BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask) = 0;

    virtual void STDMETHODCALLTYPE OMSetDepthStencilState(
        ID3D11DepthStencilState *pDepthStencilState,
        UINT StencilRef) = 0;

    virtual void STDMETHODCALLTYPE SOSetTargets(
        UINT NumBuffers,
        ID3D11Buffer *const *ppSOTargets,
        const UINT *pOffsets) = 0;

    virtual void STDMETHODCALLTYPE DrawAuto(
        ) = 0;

    virtual void STDMETHODCALLTYPE DrawIndexedInstancedIndirect(
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs) = 0;

    virtual void STDMETHODCALLTYPE DrawInstancedIndirect(
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs) = 0;

    virtual void STDMETHODCALLTYPE Dispatch(
        UINT ThreadGroupCountX,
        UINT ThreadGroupCountY,
        UINT ThreadGroupCountZ) = 0;

    virtual void STDMETHODCALLTYPE DispatchIndirect(
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs) = 0;

    virtual void STDMETHODCALLTYPE RSSetState(
        ID3D11RasterizerState *pRasterizerState) = 0;

    virtual void STDMETHODCALLTYPE RSSetViewports(
        UINT NumViewports,
        const D3D11_VIEWPORT *pViewports) = 0;

    virtual void STDMETHODCALLTYPE RSSetScissorRects(
        UINT NumRects,
        const D3D11_RECT *pRects) = 0;

    virtual void STDMETHODCALLTYPE CopySubresourceRegion(
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox) = 0;

    virtual void STDMETHODCALLTYPE CopyResource(
        ID3D11Resource *pDstResource,
        ID3D11Resource *pSrcResource) = 0;

    virtual void STDMETHODCALLTYPE UpdateSubresource(
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch) = 0;

    virtual void STDMETHODCALLTYPE CopyStructureCount(
        ID3D11Buffer *pDstBuffer,
        UINT DstAlignedByteOffset,
        ID3D11UnorderedAccessView *pSrcView) = 0;

    virtual void STDMETHODCALLTYPE ClearRenderTargetView(
        ID3D11RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]) = 0;

    virtual void STDMETHODCALLTYPE ClearUnorderedAccessViewUint(
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const UINT Values[4]) = 0;

    virtual void STDMETHODCALLTYPE ClearUnorderedAccessViewFloat(
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const FLOAT Values[4]) = 0;

    virtual void STDMETHODCALLTYPE ClearDepthStencilView(
        ID3D11DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil) = 0;

    virtual void STDMETHODCALLTYPE GenerateMips(
        ID3D11ShaderResourceView *pShaderResourceView) = 0;

    virtual void STDMETHODCALLTYPE SetResourceMinLOD(
        ID3D11Resource *pResource,
        FLOAT MinLOD) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetResourceMinLOD(
        ID3D11Resource *pResource) = 0;

    virtual void STDMETHODCALLTYPE ResolveSubresource(
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format) = 0;

    virtual void STDMETHODCALLTYPE ExecuteCommandList(
        ID3D11CommandList *pCommandList,
        WINBOOL RestoreContextState) = 0;

    virtual void STDMETHODCALLTYPE HSSetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE HSSetShader(
        ID3D11HullShader *pHullShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE HSSetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE HSSetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE DSSetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE DSSetShader(
        ID3D11DomainShader *pDomainShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE DSSetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE DSSetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE CSSetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE CSSetUnorderedAccessViews(
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts) = 0;

    virtual void STDMETHODCALLTYPE CSSetShader(
        ID3D11ComputeShader *pComputeShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE CSSetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE CSSetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE VSGetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE PSGetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE PSGetShader(
        ID3D11PixelShader **ppPixelShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE PSGetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE VSGetShader(
        ID3D11VertexShader **ppVertexShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE PSGetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE IAGetInputLayout(
        ID3D11InputLayout **ppInputLayout) = 0;

    virtual void STDMETHODCALLTYPE IAGetVertexBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets) = 0;

    virtual void STDMETHODCALLTYPE IAGetIndexBuffer(
        ID3D11Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset) = 0;

    virtual void STDMETHODCALLTYPE GSGetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE GSGetShader(
        ID3D11GeometryShader **ppGeometryShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE IAGetPrimitiveTopology(
        D3D11_PRIMITIVE_TOPOLOGY *pTopology) = 0;

    virtual void STDMETHODCALLTYPE VSGetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE VSGetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE GetPredication(
        ID3D11Predicate **ppPredicate,
        WINBOOL *pPredicateValue) = 0;

    virtual void STDMETHODCALLTYPE GSGetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE GSGetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE OMGetRenderTargets(
        UINT NumViews,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView) = 0;

    virtual void STDMETHODCALLTYPE OMGetRenderTargetsAndUnorderedAccessViews(
        UINT NumRTVs,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews) = 0;

    virtual void STDMETHODCALLTYPE OMGetBlendState(
        ID3D11BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask) = 0;

    virtual void STDMETHODCALLTYPE OMGetDepthStencilState(
        ID3D11DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef) = 0;

    virtual void STDMETHODCALLTYPE SOGetTargets(
        UINT NumBuffers,
        ID3D11Buffer **ppSOTargets) = 0;

    virtual void STDMETHODCALLTYPE RSGetState(
        ID3D11RasterizerState **ppRasterizerState) = 0;

    virtual void STDMETHODCALLTYPE RSGetViewports(
        UINT *pNumViewports,
        D3D11_VIEWPORT *pViewports) = 0;

    virtual void STDMETHODCALLTYPE RSGetScissorRects(
        UINT *pNumRects,
        D3D11_RECT *pRects) = 0;

    virtual void STDMETHODCALLTYPE HSGetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE HSGetShader(
        ID3D11HullShader **ppHullShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE HSGetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE HSGetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE DSGetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE DSGetShader(
        ID3D11DomainShader **ppDomainShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE DSGetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE DSGetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE CSGetShaderResources(
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews) = 0;

    virtual void STDMETHODCALLTYPE CSGetUnorderedAccessViews(
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews) = 0;

    virtual void STDMETHODCALLTYPE CSGetShader(
        ID3D11ComputeShader **ppComputeShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances) = 0;

    virtual void STDMETHODCALLTYPE CSGetSamplers(
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers) = 0;

    virtual void STDMETHODCALLTYPE CSGetConstantBuffers(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers) = 0;

    virtual void STDMETHODCALLTYPE ClearState(
        ) = 0;

    virtual void STDMETHODCALLTYPE Flush(
        ) = 0;

    virtual D3D11_DEVICE_CONTEXT_TYPE STDMETHODCALLTYPE GetType(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetContextFlags(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FinishCommandList(
        WINBOOL RestoreDeferredContextState,
        ID3D11CommandList **ppCommandList) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DeviceContext, 0xc0bfa96c, 0xe089, 0x44fb, 0x8e,0xaf, 0x26,0xf8,0x79,0x61,0x90,0xda)
#endif
#else
typedef struct ID3D11DeviceContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DeviceContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DeviceContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DeviceContext *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DeviceContext *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DeviceContext *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DeviceContext *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DeviceContext *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11DeviceContext methods ***/
    void (STDMETHODCALLTYPE *VSSetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSSetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSSetShader)(
        ID3D11DeviceContext *This,
        ID3D11PixelShader *pPixelShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *PSSetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *VSSetShader)(
        ID3D11DeviceContext *This,
        ID3D11VertexShader *pVertexShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DrawIndexed)(
        ID3D11DeviceContext *This,
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation);

    void (STDMETHODCALLTYPE *Draw)(
        ID3D11DeviceContext *This,
        UINT VertexCount,
        UINT StartVertexLocation);

    HRESULT (STDMETHODCALLTYPE *Map)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pResource,
        UINT Subresource,
        D3D11_MAP MapType,
        UINT MapFlags,
        D3D11_MAPPED_SUBRESOURCE *pMappedResource);

    void (STDMETHODCALLTYPE *Unmap)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pResource,
        UINT Subresource);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *IASetInputLayout)(
        ID3D11DeviceContext *This,
        ID3D11InputLayout *pInputLayout);

    void (STDMETHODCALLTYPE *IASetVertexBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *IASetIndexBuffer)(
        ID3D11DeviceContext *This,
        ID3D11Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset);

    void (STDMETHODCALLTYPE *DrawIndexedInstanced)(
        ID3D11DeviceContext *This,
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *DrawInstanced)(
        ID3D11DeviceContext *This,
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSSetShader)(
        ID3D11DeviceContext *This,
        ID3D11GeometryShader *pShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *IASetPrimitiveTopology)(
        ID3D11DeviceContext *This,
        D3D11_PRIMITIVE_TOPOLOGY Topology);

    void (STDMETHODCALLTYPE *VSSetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSSetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *Begin)(
        ID3D11DeviceContext *This,
        ID3D11Asynchronous *pAsync);

    void (STDMETHODCALLTYPE *End)(
        ID3D11DeviceContext *This,
        ID3D11Asynchronous *pAsync);

    HRESULT (STDMETHODCALLTYPE *GetData)(
        ID3D11DeviceContext *This,
        ID3D11Asynchronous *pAsync,
        void *pData,
        UINT DataSize,
        UINT GetDataFlags);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D11DeviceContext *This,
        ID3D11Predicate *pPredicate,
        WINBOOL PredicateValue);

    void (STDMETHODCALLTYPE *GSSetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSSetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *OMSetRenderTargets)(
        ID3D11DeviceContext *This,
        UINT NumViews,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView);

    void (STDMETHODCALLTYPE *OMSetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext *This,
        UINT NumRTVs,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *OMSetBlendState)(
        ID3D11DeviceContext *This,
        ID3D11BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask);

    void (STDMETHODCALLTYPE *OMSetDepthStencilState)(
        ID3D11DeviceContext *This,
        ID3D11DepthStencilState *pDepthStencilState,
        UINT StencilRef);

    void (STDMETHODCALLTYPE *SOSetTargets)(
        ID3D11DeviceContext *This,
        UINT NumBuffers,
        ID3D11Buffer *const *ppSOTargets,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *DrawAuto)(
        ID3D11DeviceContext *This);

    void (STDMETHODCALLTYPE *DrawIndexedInstancedIndirect)(
        ID3D11DeviceContext *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *DrawInstancedIndirect)(
        ID3D11DeviceContext *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *Dispatch)(
        ID3D11DeviceContext *This,
        UINT ThreadGroupCountX,
        UINT ThreadGroupCountY,
        UINT ThreadGroupCountZ);

    void (STDMETHODCALLTYPE *DispatchIndirect)(
        ID3D11DeviceContext *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *RSSetState)(
        ID3D11DeviceContext *This,
        ID3D11RasterizerState *pRasterizerState);

    void (STDMETHODCALLTYPE *RSSetViewports)(
        ID3D11DeviceContext *This,
        UINT NumViewports,
        const D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSSetScissorRects)(
        ID3D11DeviceContext *This,
        UINT NumRects,
        const D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *CopySubresourceRegion)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox);

    void (STDMETHODCALLTYPE *CopyResource)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pDstResource,
        ID3D11Resource *pSrcResource);

    void (STDMETHODCALLTYPE *UpdateSubresource)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch);

    void (STDMETHODCALLTYPE *CopyStructureCount)(
        ID3D11DeviceContext *This,
        ID3D11Buffer *pDstBuffer,
        UINT DstAlignedByteOffset,
        ID3D11UnorderedAccessView *pSrcView);

    void (STDMETHODCALLTYPE *ClearRenderTargetView)(
        ID3D11DeviceContext *This,
        ID3D11RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewUint)(
        ID3D11DeviceContext *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const UINT Values[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewFloat)(
        ID3D11DeviceContext *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const FLOAT Values[4]);

    void (STDMETHODCALLTYPE *ClearDepthStencilView)(
        ID3D11DeviceContext *This,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil);

    void (STDMETHODCALLTYPE *GenerateMips)(
        ID3D11DeviceContext *This,
        ID3D11ShaderResourceView *pShaderResourceView);

    void (STDMETHODCALLTYPE *SetResourceMinLOD)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pResource,
        FLOAT MinLOD);

    FLOAT (STDMETHODCALLTYPE *GetResourceMinLOD)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *ResolveSubresource)(
        ID3D11DeviceContext *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format);

    void (STDMETHODCALLTYPE *ExecuteCommandList)(
        ID3D11DeviceContext *This,
        ID3D11CommandList *pCommandList,
        WINBOOL RestoreContextState);

    void (STDMETHODCALLTYPE *HSSetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSSetShader)(
        ID3D11DeviceContext *This,
        ID3D11HullShader *pHullShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *HSSetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSSetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSSetShader)(
        ID3D11DeviceContext *This,
        ID3D11DomainShader *pDomainShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DSSetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSSetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSSetUnorderedAccessViews)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *CSSetShader)(
        ID3D11DeviceContext *This,
        ID3D11ComputeShader *pComputeShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *CSSetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSGetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSGetShader)(
        ID3D11DeviceContext *This,
        ID3D11PixelShader **ppPixelShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *VSGetShader)(
        ID3D11DeviceContext *This,
        ID3D11VertexShader **ppVertexShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *IAGetInputLayout)(
        ID3D11DeviceContext *This,
        ID3D11InputLayout **ppInputLayout);

    void (STDMETHODCALLTYPE *IAGetVertexBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *IAGetIndexBuffer)(
        ID3D11DeviceContext *This,
        ID3D11Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSGetShader)(
        ID3D11DeviceContext *This,
        ID3D11GeometryShader **ppGeometryShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *IAGetPrimitiveTopology)(
        ID3D11DeviceContext *This,
        D3D11_PRIMITIVE_TOPOLOGY *pTopology);

    void (STDMETHODCALLTYPE *VSGetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSGetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *GetPredication)(
        ID3D11DeviceContext *This,
        ID3D11Predicate **ppPredicate,
        WINBOOL *pPredicateValue);

    void (STDMETHODCALLTYPE *GSGetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSGetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *OMGetRenderTargets)(
        ID3D11DeviceContext *This,
        UINT NumViews,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView);

    void (STDMETHODCALLTYPE *OMGetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext *This,
        UINT NumRTVs,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *OMGetBlendState)(
        ID3D11DeviceContext *This,
        ID3D11BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask);

    void (STDMETHODCALLTYPE *OMGetDepthStencilState)(
        ID3D11DeviceContext *This,
        ID3D11DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef);

    void (STDMETHODCALLTYPE *SOGetTargets)(
        ID3D11DeviceContext *This,
        UINT NumBuffers,
        ID3D11Buffer **ppSOTargets);

    void (STDMETHODCALLTYPE *RSGetState)(
        ID3D11DeviceContext *This,
        ID3D11RasterizerState **ppRasterizerState);

    void (STDMETHODCALLTYPE *RSGetViewports)(
        ID3D11DeviceContext *This,
        UINT *pNumViewports,
        D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSGetScissorRects)(
        ID3D11DeviceContext *This,
        UINT *pNumRects,
        D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *HSGetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSGetShader)(
        ID3D11DeviceContext *This,
        ID3D11HullShader **ppHullShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *HSGetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSGetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSGetShader)(
        ID3D11DeviceContext *This,
        ID3D11DomainShader **ppDomainShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *DSGetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSGetShaderResources)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSGetUnorderedAccessViews)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *CSGetShader)(
        ID3D11DeviceContext *This,
        ID3D11ComputeShader **ppComputeShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *CSGetSamplers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers)(
        ID3D11DeviceContext *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D11DeviceContext *This);

    void (STDMETHODCALLTYPE *Flush)(
        ID3D11DeviceContext *This);

    D3D11_DEVICE_CONTEXT_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D11DeviceContext *This);

    UINT (STDMETHODCALLTYPE *GetContextFlags)(
        ID3D11DeviceContext *This);

    HRESULT (STDMETHODCALLTYPE *FinishCommandList)(
        ID3D11DeviceContext *This,
        WINBOOL RestoreDeferredContextState,
        ID3D11CommandList **ppCommandList);

    END_INTERFACE
} ID3D11DeviceContextVtbl;

interface ID3D11DeviceContext {
    CONST_VTBL ID3D11DeviceContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DeviceContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DeviceContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DeviceContext_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DeviceContext_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DeviceContext_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DeviceContext_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DeviceContext_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11DeviceContext methods ***/
#define ID3D11DeviceContext_VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext_PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext_DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation) (This)->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation)
#define ID3D11DeviceContext_Draw(This,VertexCount,StartVertexLocation) (This)->lpVtbl->Draw(This,VertexCount,StartVertexLocation)
#define ID3D11DeviceContext_Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource) (This)->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource)
#define ID3D11DeviceContext_Unmap(This,pResource,Subresource) (This)->lpVtbl->Unmap(This,pResource,Subresource)
#define ID3D11DeviceContext_PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_IASetInputLayout(This,pInputLayout) (This)->lpVtbl->IASetInputLayout(This,pInputLayout)
#define ID3D11DeviceContext_IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext_IASetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext_DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext_DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext_GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_GSSetShader(This,pShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext_IASetPrimitiveTopology(This,Topology) (This)->lpVtbl->IASetPrimitiveTopology(This,Topology)
#define ID3D11DeviceContext_VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_Begin(This,pAsync) (This)->lpVtbl->Begin(This,pAsync)
#define ID3D11DeviceContext_End(This,pAsync) (This)->lpVtbl->End(This,pAsync)
#define ID3D11DeviceContext_GetData(This,pAsync,pData,DataSize,GetDataFlags) (This)->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags)
#define ID3D11DeviceContext_SetPredication(This,pPredicate,PredicateValue) (This)->lpVtbl->SetPredication(This,pPredicate,PredicateValue)
#define ID3D11DeviceContext_GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView) (This)->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView)
#define ID3D11DeviceContext_OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext_OMSetBlendState(This,pBlendState,BlendFactor,SampleMask) (This)->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask)
#define ID3D11DeviceContext_OMSetDepthStencilState(This,pDepthStencilState,StencilRef) (This)->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef)
#define ID3D11DeviceContext_SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D11DeviceContext_DrawAuto(This) (This)->lpVtbl->DrawAuto(This)
#define ID3D11DeviceContext_DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext_DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext_Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ) (This)->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ)
#define ID3D11DeviceContext_DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext_RSSetState(This,pRasterizerState) (This)->lpVtbl->RSSetState(This,pRasterizerState)
#define ID3D11DeviceContext_RSSetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSSetViewports(This,NumViewports,pViewports)
#define ID3D11DeviceContext_RSSetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSSetScissorRects(This,NumRects,pRects)
#define ID3D11DeviceContext_CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox) (This)->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox)
#define ID3D11DeviceContext_CopyResource(This,pDstResource,pSrcResource) (This)->lpVtbl->CopyResource(This,pDstResource,pSrcResource)
#define ID3D11DeviceContext_UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch) (This)->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch)
#define ID3D11DeviceContext_CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView) (This)->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView)
#define ID3D11DeviceContext_ClearRenderTargetView(This,pRenderTargetView,ColorRGBA) (This)->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA)
#define ID3D11DeviceContext_ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext_ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext_ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil) (This)->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil)
#define ID3D11DeviceContext_GenerateMips(This,pShaderResourceView) (This)->lpVtbl->GenerateMips(This,pShaderResourceView)
#define ID3D11DeviceContext_SetResourceMinLOD(This,pResource,MinLOD) (This)->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD)
#define ID3D11DeviceContext_GetResourceMinLOD(This,pResource) (This)->lpVtbl->GetResourceMinLOD(This,pResource)
#define ID3D11DeviceContext_ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format) (This)->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format)
#define ID3D11DeviceContext_ExecuteCommandList(This,pCommandList,RestoreContextState) (This)->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState)
#define ID3D11DeviceContext_HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext_HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext_DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext_CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext_CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext_PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext_PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_IAGetInputLayout(This,ppInputLayout) (This)->lpVtbl->IAGetInputLayout(This,ppInputLayout)
#define ID3D11DeviceContext_IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext_IAGetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext_GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext_IAGetPrimitiveTopology(This,pTopology) (This)->lpVtbl->IAGetPrimitiveTopology(This,pTopology)
#define ID3D11DeviceContext_VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_GetPredication(This,ppPredicate,pPredicateValue) (This)->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue)
#define ID3D11DeviceContext_GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView) (This)->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView)
#define ID3D11DeviceContext_OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext_OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask) (This)->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask)
#define ID3D11DeviceContext_OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef) (This)->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef)
#define ID3D11DeviceContext_SOGetTargets(This,NumBuffers,ppSOTargets) (This)->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets)
#define ID3D11DeviceContext_RSGetState(This,ppRasterizerState) (This)->lpVtbl->RSGetState(This,ppRasterizerState)
#define ID3D11DeviceContext_RSGetViewports(This,pNumViewports,pViewports) (This)->lpVtbl->RSGetViewports(This,pNumViewports,pViewports)
#define ID3D11DeviceContext_RSGetScissorRects(This,pNumRects,pRects) (This)->lpVtbl->RSGetScissorRects(This,pNumRects,pRects)
#define ID3D11DeviceContext_HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext_HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext_DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext_CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext_CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext_CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext_CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D11DeviceContext_Flush(This) (This)->lpVtbl->Flush(This)
#define ID3D11DeviceContext_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D11DeviceContext_GetContextFlags(This) (This)->lpVtbl->GetContextFlags(This)
#define ID3D11DeviceContext_FinishCommandList(This,RestoreDeferredContextState,ppCommandList) (This)->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11DeviceContext_QueryInterface(ID3D11DeviceContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11DeviceContext_AddRef(ID3D11DeviceContext* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11DeviceContext_Release(ID3D11DeviceContext* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11DeviceContext_GetDevice(ID3D11DeviceContext* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11DeviceContext_GetPrivateData(ID3D11DeviceContext* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DeviceContext_SetPrivateData(ID3D11DeviceContext* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11DeviceContext_SetPrivateDataInterface(ID3D11DeviceContext* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11DeviceContext methods ***/
static __WIDL_INLINE void ID3D11DeviceContext_VSSetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSSetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSSetShader(ID3D11DeviceContext* This,ID3D11PixelShader *pPixelShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSSetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSSetShader(ID3D11DeviceContext* This,ID3D11VertexShader *pVertexShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_DrawIndexed(ID3D11DeviceContext* This,UINT IndexCount,UINT StartIndexLocation,INT BaseVertexLocation) {
    This->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation);
}
static __WIDL_INLINE void ID3D11DeviceContext_Draw(ID3D11DeviceContext* This,UINT VertexCount,UINT StartVertexLocation) {
    This->lpVtbl->Draw(This,VertexCount,StartVertexLocation);
}
static __WIDL_INLINE HRESULT ID3D11DeviceContext_Map(ID3D11DeviceContext* This,ID3D11Resource *pResource,UINT Subresource,D3D11_MAP MapType,UINT MapFlags,D3D11_MAPPED_SUBRESOURCE *pMappedResource) {
    return This->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource);
}
static __WIDL_INLINE void ID3D11DeviceContext_Unmap(ID3D11DeviceContext* This,ID3D11Resource *pResource,UINT Subresource) {
    This->lpVtbl->Unmap(This,pResource,Subresource);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSSetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_IASetInputLayout(ID3D11DeviceContext* This,ID3D11InputLayout *pInputLayout) {
    This->lpVtbl->IASetInputLayout(This,pInputLayout);
}
static __WIDL_INLINE void ID3D11DeviceContext_IASetVertexBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppVertexBuffers,const UINT *pStrides,const UINT *pOffsets) {
    This->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static __WIDL_INLINE void ID3D11DeviceContext_IASetIndexBuffer(ID3D11DeviceContext* This,ID3D11Buffer *pIndexBuffer,DXGI_FORMAT Format,UINT Offset) {
    This->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static __WIDL_INLINE void ID3D11DeviceContext_DrawIndexedInstanced(ID3D11DeviceContext* This,UINT IndexCountPerInstance,UINT InstanceCount,UINT StartIndexLocation,INT BaseVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation);
}
static __WIDL_INLINE void ID3D11DeviceContext_DrawInstanced(ID3D11DeviceContext* This,UINT VertexCountPerInstance,UINT InstanceCount,UINT StartVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSSetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSSetShader(ID3D11DeviceContext* This,ID3D11GeometryShader *pShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_IASetPrimitiveTopology(ID3D11DeviceContext* This,D3D11_PRIMITIVE_TOPOLOGY Topology) {
    This->lpVtbl->IASetPrimitiveTopology(This,Topology);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSSetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSSetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_Begin(ID3D11DeviceContext* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->Begin(This,pAsync);
}
static __WIDL_INLINE void ID3D11DeviceContext_End(ID3D11DeviceContext* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->End(This,pAsync);
}
static __WIDL_INLINE HRESULT ID3D11DeviceContext_GetData(ID3D11DeviceContext* This,ID3D11Asynchronous *pAsync,void *pData,UINT DataSize,UINT GetDataFlags) {
    return This->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags);
}
static __WIDL_INLINE void ID3D11DeviceContext_SetPredication(ID3D11DeviceContext* This,ID3D11Predicate *pPredicate,WINBOOL PredicateValue) {
    This->lpVtbl->SetPredication(This,pPredicate,PredicateValue);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSSetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSSetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMSetRenderTargets(ID3D11DeviceContext* This,UINT NumViews,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView) {
    This->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMSetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext* This,UINT NumRTVs,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMSetBlendState(ID3D11DeviceContext* This,ID3D11BlendState *pBlendState,const FLOAT BlendFactor[4],UINT SampleMask) {
    This->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMSetDepthStencilState(ID3D11DeviceContext* This,ID3D11DepthStencilState *pDepthStencilState,UINT StencilRef) {
    This->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef);
}
static __WIDL_INLINE void ID3D11DeviceContext_SOSetTargets(ID3D11DeviceContext* This,UINT NumBuffers,ID3D11Buffer *const *ppSOTargets,const UINT *pOffsets) {
    This->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static __WIDL_INLINE void ID3D11DeviceContext_DrawAuto(ID3D11DeviceContext* This) {
    This->lpVtbl->DrawAuto(This);
}
static __WIDL_INLINE void ID3D11DeviceContext_DrawIndexedInstancedIndirect(ID3D11DeviceContext* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static __WIDL_INLINE void ID3D11DeviceContext_DrawInstancedIndirect(ID3D11DeviceContext* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static __WIDL_INLINE void ID3D11DeviceContext_Dispatch(ID3D11DeviceContext* This,UINT ThreadGroupCountX,UINT ThreadGroupCountY,UINT ThreadGroupCountZ) {
    This->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ);
}
static __WIDL_INLINE void ID3D11DeviceContext_DispatchIndirect(ID3D11DeviceContext* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static __WIDL_INLINE void ID3D11DeviceContext_RSSetState(ID3D11DeviceContext* This,ID3D11RasterizerState *pRasterizerState) {
    This->lpVtbl->RSSetState(This,pRasterizerState);
}
static __WIDL_INLINE void ID3D11DeviceContext_RSSetViewports(ID3D11DeviceContext* This,UINT NumViewports,const D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSSetViewports(This,NumViewports,pViewports);
}
static __WIDL_INLINE void ID3D11DeviceContext_RSSetScissorRects(ID3D11DeviceContext* This,UINT NumRects,const D3D11_RECT *pRects) {
    This->lpVtbl->RSSetScissorRects(This,NumRects,pRects);
}
static __WIDL_INLINE void ID3D11DeviceContext_CopySubresourceRegion(ID3D11DeviceContext* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox) {
    This->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox);
}
static __WIDL_INLINE void ID3D11DeviceContext_CopyResource(ID3D11DeviceContext* This,ID3D11Resource *pDstResource,ID3D11Resource *pSrcResource) {
    This->lpVtbl->CopyResource(This,pDstResource,pSrcResource);
}
static __WIDL_INLINE void ID3D11DeviceContext_UpdateSubresource(ID3D11DeviceContext* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch) {
    This->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch);
}
static __WIDL_INLINE void ID3D11DeviceContext_CopyStructureCount(ID3D11DeviceContext* This,ID3D11Buffer *pDstBuffer,UINT DstAlignedByteOffset,ID3D11UnorderedAccessView *pSrcView) {
    This->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView);
}
static __WIDL_INLINE void ID3D11DeviceContext_ClearRenderTargetView(ID3D11DeviceContext* This,ID3D11RenderTargetView *pRenderTargetView,const FLOAT ColorRGBA[4]) {
    This->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA);
}
static __WIDL_INLINE void ID3D11DeviceContext_ClearUnorderedAccessViewUint(ID3D11DeviceContext* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const UINT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values);
}
static __WIDL_INLINE void ID3D11DeviceContext_ClearUnorderedAccessViewFloat(ID3D11DeviceContext* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const FLOAT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values);
}
static __WIDL_INLINE void ID3D11DeviceContext_ClearDepthStencilView(ID3D11DeviceContext* This,ID3D11DepthStencilView *pDepthStencilView,UINT ClearFlags,FLOAT Depth,UINT8 Stencil) {
    This->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil);
}
static __WIDL_INLINE void ID3D11DeviceContext_GenerateMips(ID3D11DeviceContext* This,ID3D11ShaderResourceView *pShaderResourceView) {
    This->lpVtbl->GenerateMips(This,pShaderResourceView);
}
static __WIDL_INLINE void ID3D11DeviceContext_SetResourceMinLOD(ID3D11DeviceContext* This,ID3D11Resource *pResource,FLOAT MinLOD) {
    This->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD);
}
static __WIDL_INLINE FLOAT ID3D11DeviceContext_GetResourceMinLOD(ID3D11DeviceContext* This,ID3D11Resource *pResource) {
    return This->lpVtbl->GetResourceMinLOD(This,pResource);
}
static __WIDL_INLINE void ID3D11DeviceContext_ResolveSubresource(ID3D11DeviceContext* This,ID3D11Resource *pDstResource,UINT DstSubresource,ID3D11Resource *pSrcResource,UINT SrcSubresource,DXGI_FORMAT Format) {
    This->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format);
}
static __WIDL_INLINE void ID3D11DeviceContext_ExecuteCommandList(ID3D11DeviceContext* This,ID3D11CommandList *pCommandList,WINBOOL RestoreContextState) {
    This->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSSetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSSetShader(ID3D11DeviceContext* This,ID3D11HullShader *pHullShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSSetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSSetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSSetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSSetShader(ID3D11DeviceContext* This,ID3D11DomainShader *pDomainShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSSetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSSetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSSetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSSetUnorderedAccessViews(ID3D11DeviceContext* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSSetShader(ID3D11DeviceContext* This,ID3D11ComputeShader *pComputeShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSSetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSSetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSGetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSGetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSGetShader(ID3D11DeviceContext* This,ID3D11PixelShader **ppPixelShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSGetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSGetShader(ID3D11DeviceContext* This,ID3D11VertexShader **ppVertexShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_PSGetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_IAGetInputLayout(ID3D11DeviceContext* This,ID3D11InputLayout **ppInputLayout) {
    This->lpVtbl->IAGetInputLayout(This,ppInputLayout);
}
static __WIDL_INLINE void ID3D11DeviceContext_IAGetVertexBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppVertexBuffers,UINT *pStrides,UINT *pOffsets) {
    This->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static __WIDL_INLINE void ID3D11DeviceContext_IAGetIndexBuffer(ID3D11DeviceContext* This,ID3D11Buffer **pIndexBuffer,DXGI_FORMAT *Format,UINT *Offset) {
    This->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSGetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSGetShader(ID3D11DeviceContext* This,ID3D11GeometryShader **ppGeometryShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_IAGetPrimitiveTopology(ID3D11DeviceContext* This,D3D11_PRIMITIVE_TOPOLOGY *pTopology) {
    This->lpVtbl->IAGetPrimitiveTopology(This,pTopology);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSGetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_VSGetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_GetPredication(ID3D11DeviceContext* This,ID3D11Predicate **ppPredicate,WINBOOL *pPredicateValue) {
    This->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSGetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_GSGetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMGetRenderTargets(ID3D11DeviceContext* This,UINT NumViews,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView) {
    This->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMGetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext* This,UINT NumRTVs,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMGetBlendState(ID3D11DeviceContext* This,ID3D11BlendState **ppBlendState,FLOAT BlendFactor[4],UINT *pSampleMask) {
    This->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask);
}
static __WIDL_INLINE void ID3D11DeviceContext_OMGetDepthStencilState(ID3D11DeviceContext* This,ID3D11DepthStencilState **ppDepthStencilState,UINT *pStencilRef) {
    This->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef);
}
static __WIDL_INLINE void ID3D11DeviceContext_SOGetTargets(ID3D11DeviceContext* This,UINT NumBuffers,ID3D11Buffer **ppSOTargets) {
    This->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets);
}
static __WIDL_INLINE void ID3D11DeviceContext_RSGetState(ID3D11DeviceContext* This,ID3D11RasterizerState **ppRasterizerState) {
    This->lpVtbl->RSGetState(This,ppRasterizerState);
}
static __WIDL_INLINE void ID3D11DeviceContext_RSGetViewports(ID3D11DeviceContext* This,UINT *pNumViewports,D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSGetViewports(This,pNumViewports,pViewports);
}
static __WIDL_INLINE void ID3D11DeviceContext_RSGetScissorRects(ID3D11DeviceContext* This,UINT *pNumRects,D3D11_RECT *pRects) {
    This->lpVtbl->RSGetScissorRects(This,pNumRects,pRects);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSGetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSGetShader(ID3D11DeviceContext* This,ID3D11HullShader **ppHullShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSGetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_HSGetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSGetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSGetShader(ID3D11DeviceContext* This,ID3D11DomainShader **ppDomainShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSGetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_DSGetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSGetShaderResources(ID3D11DeviceContext* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSGetUnorderedAccessViews(ID3D11DeviceContext* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSGetShader(ID3D11DeviceContext* This,ID3D11ComputeShader **ppComputeShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSGetSamplers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static __WIDL_INLINE void ID3D11DeviceContext_CSGetConstantBuffers(ID3D11DeviceContext* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static __WIDL_INLINE void ID3D11DeviceContext_ClearState(ID3D11DeviceContext* This) {
    This->lpVtbl->ClearState(This);
}
static __WIDL_INLINE void ID3D11DeviceContext_Flush(ID3D11DeviceContext* This) {
    This->lpVtbl->Flush(This);
}
static __WIDL_INLINE D3D11_DEVICE_CONTEXT_TYPE ID3D11DeviceContext_GetType(ID3D11DeviceContext* This) {
    return This->lpVtbl->GetType(This);
}
static __WIDL_INLINE UINT ID3D11DeviceContext_GetContextFlags(ID3D11DeviceContext* This) {
    return This->lpVtbl->GetContextFlags(This);
}
static __WIDL_INLINE HRESULT ID3D11DeviceContext_FinishCommandList(ID3D11DeviceContext* This,WINBOOL RestoreDeferredContextState,ID3D11CommandList **ppCommandList) {
    return This->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList);
}
#endif
#endif

#endif


#endif  /* __ID3D11DeviceContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11AuthenticatedChannel interface
 */
#ifndef __ID3D11AuthenticatedChannel_INTERFACE_DEFINED__
#define __ID3D11AuthenticatedChannel_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11AuthenticatedChannel, 0x3015a308, 0xdcbd, 0x47aa, 0xa7,0x47, 0x19,0x24,0x86,0xd1,0x4d,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3015a308-dcbd-47aa-a747-192486d14d4a")
ID3D11AuthenticatedChannel : public ID3D11DeviceChild
{
    virtual HRESULT STDMETHODCALLTYPE GetCertificateSize(
        UINT *pCertificateSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificate(
        UINT CertificateSize,
        BYTE *pCertificate) = 0;

    virtual void STDMETHODCALLTYPE GetChannelHandle(
        HANDLE *pChannelHandle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11AuthenticatedChannel, 0x3015a308, 0xdcbd, 0x47aa, 0xa7,0x47, 0x19,0x24,0x86,0xd1,0x4d,0x4a)
#endif
#else
typedef struct ID3D11AuthenticatedChannelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11AuthenticatedChannel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11AuthenticatedChannel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11AuthenticatedChannel *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11AuthenticatedChannel *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11AuthenticatedChannel *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11AuthenticatedChannel *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11AuthenticatedChannel *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11AuthenticatedChannel methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCertificateSize)(
        ID3D11AuthenticatedChannel *This,
        UINT *pCertificateSize);

    HRESULT (STDMETHODCALLTYPE *GetCertificate)(
        ID3D11AuthenticatedChannel *This,
        UINT CertificateSize,
        BYTE *pCertificate);

    void (STDMETHODCALLTYPE *GetChannelHandle)(
        ID3D11AuthenticatedChannel *This,
        HANDLE *pChannelHandle);

    END_INTERFACE
} ID3D11AuthenticatedChannelVtbl;

interface ID3D11AuthenticatedChannel {
    CONST_VTBL ID3D11AuthenticatedChannelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11AuthenticatedChannel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11AuthenticatedChannel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11AuthenticatedChannel_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11AuthenticatedChannel_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11AuthenticatedChannel_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11AuthenticatedChannel_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11AuthenticatedChannel_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11AuthenticatedChannel methods ***/
#define ID3D11AuthenticatedChannel_GetCertificateSize(This,pCertificateSize) (This)->lpVtbl->GetCertificateSize(This,pCertificateSize)
#define ID3D11AuthenticatedChannel_GetCertificate(This,CertificateSize,pCertificate) (This)->lpVtbl->GetCertificate(This,CertificateSize,pCertificate)
#define ID3D11AuthenticatedChannel_GetChannelHandle(This,pChannelHandle) (This)->lpVtbl->GetChannelHandle(This,pChannelHandle)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11AuthenticatedChannel_QueryInterface(ID3D11AuthenticatedChannel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11AuthenticatedChannel_AddRef(ID3D11AuthenticatedChannel* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11AuthenticatedChannel_Release(ID3D11AuthenticatedChannel* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11AuthenticatedChannel_GetDevice(ID3D11AuthenticatedChannel* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11AuthenticatedChannel_GetPrivateData(ID3D11AuthenticatedChannel* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11AuthenticatedChannel_SetPrivateData(ID3D11AuthenticatedChannel* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11AuthenticatedChannel_SetPrivateDataInterface(ID3D11AuthenticatedChannel* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11AuthenticatedChannel methods ***/
static __WIDL_INLINE HRESULT ID3D11AuthenticatedChannel_GetCertificateSize(ID3D11AuthenticatedChannel* This,UINT *pCertificateSize) {
    return This->lpVtbl->GetCertificateSize(This,pCertificateSize);
}
static __WIDL_INLINE HRESULT ID3D11AuthenticatedChannel_GetCertificate(ID3D11AuthenticatedChannel* This,UINT CertificateSize,BYTE *pCertificate) {
    return This->lpVtbl->GetCertificate(This,CertificateSize,pCertificate);
}
static __WIDL_INLINE void ID3D11AuthenticatedChannel_GetChannelHandle(ID3D11AuthenticatedChannel* This,HANDLE *pChannelHandle) {
    This->lpVtbl->GetChannelHandle(This,pChannelHandle);
}
#endif
#endif

#endif


#endif  /* __ID3D11AuthenticatedChannel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11CryptoSession interface
 */
#ifndef __ID3D11CryptoSession_INTERFACE_DEFINED__
#define __ID3D11CryptoSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11CryptoSession, 0x9b32f9ad, 0xbdcc, 0x40a6, 0xa3,0x9d, 0xd5,0xc8,0x65,0x84,0x57,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b32f9ad-bdcc-40a6-a39d-d5c865845720")
ID3D11CryptoSession : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetCryptoType(
        GUID *pCryptoType) = 0;

    virtual void STDMETHODCALLTYPE GetDecoderProfile(
        GUID *pDecoderProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificateSize(
        UINT *pCertificateSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificate(
        UINT CertificateSize,
        BYTE *pCertificate) = 0;

    virtual void STDMETHODCALLTYPE GetCryptoSessionHandle(
        HANDLE *pCryptoSessionHandle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11CryptoSession, 0x9b32f9ad, 0xbdcc, 0x40a6, 0xa3,0x9d, 0xd5,0xc8,0x65,0x84,0x57,0x20)
#endif
#else
typedef struct ID3D11CryptoSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11CryptoSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11CryptoSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11CryptoSession *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11CryptoSession *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11CryptoSession *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11CryptoSession *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11CryptoSession *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11CryptoSession methods ***/
    void (STDMETHODCALLTYPE *GetCryptoType)(
        ID3D11CryptoSession *This,
        GUID *pCryptoType);

    void (STDMETHODCALLTYPE *GetDecoderProfile)(
        ID3D11CryptoSession *This,
        GUID *pDecoderProfile);

    HRESULT (STDMETHODCALLTYPE *GetCertificateSize)(
        ID3D11CryptoSession *This,
        UINT *pCertificateSize);

    HRESULT (STDMETHODCALLTYPE *GetCertificate)(
        ID3D11CryptoSession *This,
        UINT CertificateSize,
        BYTE *pCertificate);

    void (STDMETHODCALLTYPE *GetCryptoSessionHandle)(
        ID3D11CryptoSession *This,
        HANDLE *pCryptoSessionHandle);

    END_INTERFACE
} ID3D11CryptoSessionVtbl;

interface ID3D11CryptoSession {
    CONST_VTBL ID3D11CryptoSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11CryptoSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11CryptoSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11CryptoSession_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11CryptoSession_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11CryptoSession_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11CryptoSession_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11CryptoSession_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11CryptoSession methods ***/
#define ID3D11CryptoSession_GetCryptoType(This,pCryptoType) (This)->lpVtbl->GetCryptoType(This,pCryptoType)
#define ID3D11CryptoSession_GetDecoderProfile(This,pDecoderProfile) (This)->lpVtbl->GetDecoderProfile(This,pDecoderProfile)
#define ID3D11CryptoSession_GetCertificateSize(This,pCertificateSize) (This)->lpVtbl->GetCertificateSize(This,pCertificateSize)
#define ID3D11CryptoSession_GetCertificate(This,CertificateSize,pCertificate) (This)->lpVtbl->GetCertificate(This,CertificateSize,pCertificate)
#define ID3D11CryptoSession_GetCryptoSessionHandle(This,pCryptoSessionHandle) (This)->lpVtbl->GetCryptoSessionHandle(This,pCryptoSessionHandle)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11CryptoSession_QueryInterface(ID3D11CryptoSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11CryptoSession_AddRef(ID3D11CryptoSession* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11CryptoSession_Release(ID3D11CryptoSession* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11CryptoSession_GetDevice(ID3D11CryptoSession* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11CryptoSession_GetPrivateData(ID3D11CryptoSession* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11CryptoSession_SetPrivateData(ID3D11CryptoSession* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11CryptoSession_SetPrivateDataInterface(ID3D11CryptoSession* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11CryptoSession methods ***/
static __WIDL_INLINE void ID3D11CryptoSession_GetCryptoType(ID3D11CryptoSession* This,GUID *pCryptoType) {
    This->lpVtbl->GetCryptoType(This,pCryptoType);
}
static __WIDL_INLINE void ID3D11CryptoSession_GetDecoderProfile(ID3D11CryptoSession* This,GUID *pDecoderProfile) {
    This->lpVtbl->GetDecoderProfile(This,pDecoderProfile);
}
static __WIDL_INLINE HRESULT ID3D11CryptoSession_GetCertificateSize(ID3D11CryptoSession* This,UINT *pCertificateSize) {
    return This->lpVtbl->GetCertificateSize(This,pCertificateSize);
}
static __WIDL_INLINE HRESULT ID3D11CryptoSession_GetCertificate(ID3D11CryptoSession* This,UINT CertificateSize,BYTE *pCertificate) {
    return This->lpVtbl->GetCertificate(This,CertificateSize,pCertificate);
}
static __WIDL_INLINE void ID3D11CryptoSession_GetCryptoSessionHandle(ID3D11CryptoSession* This,HANDLE *pCryptoSessionHandle) {
    This->lpVtbl->GetCryptoSessionHandle(This,pCryptoSessionHandle);
}
#endif
#endif

#endif


#endif  /* __ID3D11CryptoSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoDecoder interface
 */
#ifndef __ID3D11VideoDecoder_INTERFACE_DEFINED__
#define __ID3D11VideoDecoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoDecoder, 0x3c9c5b51, 0x995d, 0x48d1, 0x9b,0x8d, 0xfa,0x5c,0xae,0xde,0xd6,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3c9c5b51-995d-48d1-9b8d-fa5caeded65c")
ID3D11VideoDecoder : public ID3D11DeviceChild
{
    virtual HRESULT STDMETHODCALLTYPE GetCreationParameters(
        D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        D3D11_VIDEO_DECODER_CONFIG *pConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDriverHandle(
        HANDLE *pDriverHandle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoDecoder, 0x3c9c5b51, 0x995d, 0x48d1, 0x9b,0x8d, 0xfa,0x5c,0xae,0xde,0xd6,0x5c)
#endif
#else
typedef struct ID3D11VideoDecoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoDecoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoDecoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoDecoder *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoDecoder *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoDecoder *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoDecoder *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoDecoder *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoDecoder methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCreationParameters)(
        ID3D11VideoDecoder *This,
        D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        D3D11_VIDEO_DECODER_CONFIG *pConfig);

    HRESULT (STDMETHODCALLTYPE *GetDriverHandle)(
        ID3D11VideoDecoder *This,
        HANDLE *pDriverHandle);

    END_INTERFACE
} ID3D11VideoDecoderVtbl;

interface ID3D11VideoDecoder {
    CONST_VTBL ID3D11VideoDecoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoDecoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoDecoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoDecoder_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoDecoder_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoDecoder_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoDecoder_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoDecoder_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoDecoder methods ***/
#define ID3D11VideoDecoder_GetCreationParameters(This,pVideoDesc,pConfig) (This)->lpVtbl->GetCreationParameters(This,pVideoDesc,pConfig)
#define ID3D11VideoDecoder_GetDriverHandle(This,pDriverHandle) (This)->lpVtbl->GetDriverHandle(This,pDriverHandle)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoDecoder_QueryInterface(ID3D11VideoDecoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoDecoder_AddRef(ID3D11VideoDecoder* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoDecoder_Release(ID3D11VideoDecoder* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoDecoder_GetDevice(ID3D11VideoDecoder* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoder_GetPrivateData(ID3D11VideoDecoder* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoder_SetPrivateData(ID3D11VideoDecoder* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoder_SetPrivateDataInterface(ID3D11VideoDecoder* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoDecoder methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoDecoder_GetCreationParameters(ID3D11VideoDecoder* This,D3D11_VIDEO_DECODER_DESC *pVideoDesc,D3D11_VIDEO_DECODER_CONFIG *pConfig) {
    return This->lpVtbl->GetCreationParameters(This,pVideoDesc,pConfig);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoder_GetDriverHandle(ID3D11VideoDecoder* This,HANDLE *pDriverHandle) {
    return This->lpVtbl->GetDriverHandle(This,pDriverHandle);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoDecoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoProcessorEnumerator interface
 */
#ifndef __ID3D11VideoProcessorEnumerator_INTERFACE_DEFINED__
#define __ID3D11VideoProcessorEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoProcessorEnumerator, 0x31627037, 0x53ab, 0x4200, 0x90,0x61, 0x05,0xfa,0xa9,0xab,0x45,0xf9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("31627037-53ab-4200-9061-05faa9ab45f9")
ID3D11VideoProcessorEnumerator : public ID3D11DeviceChild
{
    virtual HRESULT STDMETHODCALLTYPE GetVideoProcessorContentDesc(
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pContentDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckVideoProcessorFormat(
        DXGI_FORMAT Format,
        UINT *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoProcessorCaps(
        D3D11_VIDEO_PROCESSOR_CAPS *pCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoProcessorRateConversionCaps(
        UINT TypeIndex,
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoProcessorCustomRate(
        UINT TypeIndex,
        UINT CustomRateIndex,
        D3D11_VIDEO_PROCESSOR_CUSTOM_RATE *pRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoProcessorFilterRange(
        D3D11_VIDEO_PROCESSOR_FILTER Filter,
        D3D11_VIDEO_PROCESSOR_FILTER_RANGE *pRange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoProcessorEnumerator, 0x31627037, 0x53ab, 0x4200, 0x90,0x61, 0x05,0xfa,0xa9,0xab,0x45,0xf9)
#endif
#else
typedef struct ID3D11VideoProcessorEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoProcessorEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoProcessorEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoProcessorEnumerator *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoProcessorEnumerator *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoProcessorEnumerator *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoProcessorEnumerator *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoProcessorEnumerator *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoProcessorEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorContentDesc)(
        ID3D11VideoProcessorEnumerator *This,
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pContentDesc);

    HRESULT (STDMETHODCALLTYPE *CheckVideoProcessorFormat)(
        ID3D11VideoProcessorEnumerator *This,
        DXGI_FORMAT Format,
        UINT *pFlags);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorCaps)(
        ID3D11VideoProcessorEnumerator *This,
        D3D11_VIDEO_PROCESSOR_CAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorRateConversionCaps)(
        ID3D11VideoProcessorEnumerator *This,
        UINT TypeIndex,
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorCustomRate)(
        ID3D11VideoProcessorEnumerator *This,
        UINT TypeIndex,
        UINT CustomRateIndex,
        D3D11_VIDEO_PROCESSOR_CUSTOM_RATE *pRate);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorFilterRange)(
        ID3D11VideoProcessorEnumerator *This,
        D3D11_VIDEO_PROCESSOR_FILTER Filter,
        D3D11_VIDEO_PROCESSOR_FILTER_RANGE *pRange);

    END_INTERFACE
} ID3D11VideoProcessorEnumeratorVtbl;

interface ID3D11VideoProcessorEnumerator {
    CONST_VTBL ID3D11VideoProcessorEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoProcessorEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoProcessorEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoProcessorEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoProcessorEnumerator_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoProcessorEnumerator_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoProcessorEnumerator_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoProcessorEnumerator_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoProcessorEnumerator methods ***/
#define ID3D11VideoProcessorEnumerator_GetVideoProcessorContentDesc(This,pContentDesc) (This)->lpVtbl->GetVideoProcessorContentDesc(This,pContentDesc)
#define ID3D11VideoProcessorEnumerator_CheckVideoProcessorFormat(This,Format,pFlags) (This)->lpVtbl->CheckVideoProcessorFormat(This,Format,pFlags)
#define ID3D11VideoProcessorEnumerator_GetVideoProcessorCaps(This,pCaps) (This)->lpVtbl->GetVideoProcessorCaps(This,pCaps)
#define ID3D11VideoProcessorEnumerator_GetVideoProcessorRateConversionCaps(This,TypeIndex,pCaps) (This)->lpVtbl->GetVideoProcessorRateConversionCaps(This,TypeIndex,pCaps)
#define ID3D11VideoProcessorEnumerator_GetVideoProcessorCustomRate(This,TypeIndex,CustomRateIndex,pRate) (This)->lpVtbl->GetVideoProcessorCustomRate(This,TypeIndex,CustomRateIndex,pRate)
#define ID3D11VideoProcessorEnumerator_GetVideoProcessorFilterRange(This,Filter,pRange) (This)->lpVtbl->GetVideoProcessorFilterRange(This,Filter,pRange)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_QueryInterface(ID3D11VideoProcessorEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessorEnumerator_AddRef(ID3D11VideoProcessorEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessorEnumerator_Release(ID3D11VideoProcessorEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorEnumerator_GetDevice(ID3D11VideoProcessorEnumerator* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_GetPrivateData(ID3D11VideoProcessorEnumerator* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_SetPrivateData(ID3D11VideoProcessorEnumerator* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_SetPrivateDataInterface(ID3D11VideoProcessorEnumerator* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoProcessorEnumerator methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_GetVideoProcessorContentDesc(ID3D11VideoProcessorEnumerator* This,D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pContentDesc) {
    return This->lpVtbl->GetVideoProcessorContentDesc(This,pContentDesc);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_CheckVideoProcessorFormat(ID3D11VideoProcessorEnumerator* This,DXGI_FORMAT Format,UINT *pFlags) {
    return This->lpVtbl->CheckVideoProcessorFormat(This,Format,pFlags);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_GetVideoProcessorCaps(ID3D11VideoProcessorEnumerator* This,D3D11_VIDEO_PROCESSOR_CAPS *pCaps) {
    return This->lpVtbl->GetVideoProcessorCaps(This,pCaps);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_GetVideoProcessorRateConversionCaps(ID3D11VideoProcessorEnumerator* This,UINT TypeIndex,D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps) {
    return This->lpVtbl->GetVideoProcessorRateConversionCaps(This,TypeIndex,pCaps);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_GetVideoProcessorCustomRate(ID3D11VideoProcessorEnumerator* This,UINT TypeIndex,UINT CustomRateIndex,D3D11_VIDEO_PROCESSOR_CUSTOM_RATE *pRate) {
    return This->lpVtbl->GetVideoProcessorCustomRate(This,TypeIndex,CustomRateIndex,pRate);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorEnumerator_GetVideoProcessorFilterRange(ID3D11VideoProcessorEnumerator* This,D3D11_VIDEO_PROCESSOR_FILTER Filter,D3D11_VIDEO_PROCESSOR_FILTER_RANGE *pRange) {
    return This->lpVtbl->GetVideoProcessorFilterRange(This,Filter,pRange);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoProcessorEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoProcessor interface
 */
#ifndef __ID3D11VideoProcessor_INTERFACE_DEFINED__
#define __ID3D11VideoProcessor_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoProcessor, 0x1d7b0652, 0x185f, 0x41c6, 0x85,0xce, 0x0c,0x5b,0xe3,0xd4,0xae,0x6c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1d7b0652-185f-41c6-85ce-0c5be3d4ae6c")
ID3D11VideoProcessor : public ID3D11DeviceChild
{
    virtual void STDMETHODCALLTYPE GetContentDesc(
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc) = 0;

    virtual void STDMETHODCALLTYPE GetRateConversionCaps(
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoProcessor, 0x1d7b0652, 0x185f, 0x41c6, 0x85,0xce, 0x0c,0x5b,0xe3,0xd4,0xae,0x6c)
#endif
#else
typedef struct ID3D11VideoProcessorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoProcessor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoProcessor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoProcessor *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoProcessor *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoProcessor *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoProcessor *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoProcessor *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoProcessor methods ***/
    void (STDMETHODCALLTYPE *GetContentDesc)(
        ID3D11VideoProcessor *This,
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc);

    void (STDMETHODCALLTYPE *GetRateConversionCaps)(
        ID3D11VideoProcessor *This,
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps);

    END_INTERFACE
} ID3D11VideoProcessorVtbl;

interface ID3D11VideoProcessor {
    CONST_VTBL ID3D11VideoProcessorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoProcessor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoProcessor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoProcessor_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoProcessor_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoProcessor_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoProcessor_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoProcessor_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoProcessor methods ***/
#define ID3D11VideoProcessor_GetContentDesc(This,pDesc) (This)->lpVtbl->GetContentDesc(This,pDesc)
#define ID3D11VideoProcessor_GetRateConversionCaps(This,pCaps) (This)->lpVtbl->GetRateConversionCaps(This,pCaps)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoProcessor_QueryInterface(ID3D11VideoProcessor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessor_AddRef(ID3D11VideoProcessor* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessor_Release(ID3D11VideoProcessor* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoProcessor_GetDevice(ID3D11VideoProcessor* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessor_GetPrivateData(ID3D11VideoProcessor* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessor_SetPrivateData(ID3D11VideoProcessor* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessor_SetPrivateDataInterface(ID3D11VideoProcessor* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoProcessor methods ***/
static __WIDL_INLINE void ID3D11VideoProcessor_GetContentDesc(ID3D11VideoProcessor* This,D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc) {
    This->lpVtbl->GetContentDesc(This,pDesc);
}
static __WIDL_INLINE void ID3D11VideoProcessor_GetRateConversionCaps(ID3D11VideoProcessor* This,D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps) {
    This->lpVtbl->GetRateConversionCaps(This,pCaps);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoProcessor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoDecoderOutputView interface
 */
#ifndef __ID3D11VideoDecoderOutputView_INTERFACE_DEFINED__
#define __ID3D11VideoDecoderOutputView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoDecoderOutputView, 0xc2931aea, 0x2a85, 0x4f20, 0x86,0x0f, 0xfb,0xa1,0xfd,0x25,0x6e,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c2931aea-2a85-4f20-860f-fba1fd256e18")
ID3D11VideoDecoderOutputView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoDecoderOutputView, 0xc2931aea, 0x2a85, 0x4f20, 0x86,0x0f, 0xfb,0xa1,0xfd,0x25,0x6e,0x18)
#endif
#else
typedef struct ID3D11VideoDecoderOutputViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoDecoderOutputView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoDecoderOutputView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoDecoderOutputView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoDecoderOutputView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoDecoderOutputView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoDecoderOutputView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoDecoderOutputView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11VideoDecoderOutputView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11VideoDecoderOutputView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11VideoDecoderOutputView *This,
        D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11VideoDecoderOutputViewVtbl;

interface ID3D11VideoDecoderOutputView {
    CONST_VTBL ID3D11VideoDecoderOutputViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoDecoderOutputView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoDecoderOutputView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoDecoderOutputView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoDecoderOutputView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoDecoderOutputView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoDecoderOutputView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoDecoderOutputView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11VideoDecoderOutputView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11VideoDecoderOutputView methods ***/
#define ID3D11VideoDecoderOutputView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoDecoderOutputView_QueryInterface(ID3D11VideoDecoderOutputView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoDecoderOutputView_AddRef(ID3D11VideoDecoderOutputView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoDecoderOutputView_Release(ID3D11VideoDecoderOutputView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoDecoderOutputView_GetDevice(ID3D11VideoDecoderOutputView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoderOutputView_GetPrivateData(ID3D11VideoDecoderOutputView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoderOutputView_SetPrivateData(ID3D11VideoDecoderOutputView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoDecoderOutputView_SetPrivateDataInterface(ID3D11VideoDecoderOutputView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11VideoDecoderOutputView_GetResource(ID3D11VideoDecoderOutputView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11VideoDecoderOutputView methods ***/
static __WIDL_INLINE void ID3D11VideoDecoderOutputView_GetDesc(ID3D11VideoDecoderOutputView* This,D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoDecoderOutputView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoProcessorInputView interface
 */
#ifndef __ID3D11VideoProcessorInputView_INTERFACE_DEFINED__
#define __ID3D11VideoProcessorInputView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoProcessorInputView, 0x11ec5a5f, 0x51dc, 0x4945, 0xab,0x34, 0x6e,0x8c,0x21,0x30,0x0e,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("11ec5a5f-51dc-4945-ab34-6e8c21300ea5")
ID3D11VideoProcessorInputView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoProcessorInputView, 0x11ec5a5f, 0x51dc, 0x4945, 0xab,0x34, 0x6e,0x8c,0x21,0x30,0x0e,0xa5)
#endif
#else
typedef struct ID3D11VideoProcessorInputViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoProcessorInputView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoProcessorInputView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoProcessorInputView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoProcessorInputView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoProcessorInputView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoProcessorInputView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoProcessorInputView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11VideoProcessorInputView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11VideoProcessorInputView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11VideoProcessorInputView *This,
        D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11VideoProcessorInputViewVtbl;

interface ID3D11VideoProcessorInputView {
    CONST_VTBL ID3D11VideoProcessorInputViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoProcessorInputView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoProcessorInputView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoProcessorInputView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoProcessorInputView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoProcessorInputView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoProcessorInputView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoProcessorInputView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11VideoProcessorInputView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11VideoProcessorInputView methods ***/
#define ID3D11VideoProcessorInputView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoProcessorInputView_QueryInterface(ID3D11VideoProcessorInputView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessorInputView_AddRef(ID3D11VideoProcessorInputView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessorInputView_Release(ID3D11VideoProcessorInputView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorInputView_GetDevice(ID3D11VideoProcessorInputView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorInputView_GetPrivateData(ID3D11VideoProcessorInputView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorInputView_SetPrivateData(ID3D11VideoProcessorInputView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorInputView_SetPrivateDataInterface(ID3D11VideoProcessorInputView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorInputView_GetResource(ID3D11VideoProcessorInputView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11VideoProcessorInputView methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorInputView_GetDesc(ID3D11VideoProcessorInputView* This,D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoProcessorInputView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoProcessorOutputView interface
 */
#ifndef __ID3D11VideoProcessorOutputView_INTERFACE_DEFINED__
#define __ID3D11VideoProcessorOutputView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoProcessorOutputView, 0xa048285e, 0x25a9, 0x4527, 0xbd,0x93, 0xd6,0x8b,0x68,0xc4,0x42,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a048285e-25a9-4527-bd93-d68b68c44254")
ID3D11VideoProcessorOutputView : public ID3D11View
{
    virtual void STDMETHODCALLTYPE GetDesc(
        D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoProcessorOutputView, 0xa048285e, 0x25a9, 0x4527, 0xbd,0x93, 0xd6,0x8b,0x68,0xc4,0x42,0x54)
#endif
#else
typedef struct ID3D11VideoProcessorOutputViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoProcessorOutputView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoProcessorOutputView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoProcessorOutputView *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoProcessorOutputView *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoProcessorOutputView *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoProcessorOutputView *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoProcessorOutputView *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11VideoProcessorOutputView *This,
        ID3D11Resource **ppResource);

    /*** ID3D11VideoProcessorOutputView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11VideoProcessorOutputView *This,
        D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc);

    END_INTERFACE
} ID3D11VideoProcessorOutputViewVtbl;

interface ID3D11VideoProcessorOutputView {
    CONST_VTBL ID3D11VideoProcessorOutputViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoProcessorOutputView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoProcessorOutputView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoProcessorOutputView_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoProcessorOutputView_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoProcessorOutputView_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoProcessorOutputView_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoProcessorOutputView_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11VideoProcessorOutputView_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11VideoProcessorOutputView methods ***/
#define ID3D11VideoProcessorOutputView_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoProcessorOutputView_QueryInterface(ID3D11VideoProcessorOutputView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessorOutputView_AddRef(ID3D11VideoProcessorOutputView* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoProcessorOutputView_Release(ID3D11VideoProcessorOutputView* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorOutputView_GetDevice(ID3D11VideoProcessorOutputView* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorOutputView_GetPrivateData(ID3D11VideoProcessorOutputView* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorOutputView_SetPrivateData(ID3D11VideoProcessorOutputView* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoProcessorOutputView_SetPrivateDataInterface(ID3D11VideoProcessorOutputView* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorOutputView_GetResource(ID3D11VideoProcessorOutputView* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11VideoProcessorOutputView methods ***/
static __WIDL_INLINE void ID3D11VideoProcessorOutputView_GetDesc(ID3D11VideoProcessorOutputView* This,D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoProcessorOutputView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoDevice interface
 */
#ifndef __ID3D11VideoDevice_INTERFACE_DEFINED__
#define __ID3D11VideoDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoDevice, 0x10ec4d5b, 0x975a, 0x4689, 0xb9,0xe4, 0xd0,0xaa,0xc3,0x0f,0xe3,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("10ec4d5b-975a-4689-b9e4-d0aac30fe333")
ID3D11VideoDevice : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateVideoDecoder(
        const D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        const D3D11_VIDEO_DECODER_CONFIG *pConfig,
        ID3D11VideoDecoder **ppDecoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoProcessor(
        ID3D11VideoProcessorEnumerator *pEnum,
        UINT RateConversionIndex,
        ID3D11VideoProcessor **ppVideoProcessor) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAuthenticatedChannel(
        D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType,
        ID3D11AuthenticatedChannel **ppAuthenticatedChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCryptoSession(
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        const GUID *pKeyExchangeType,
        ID3D11CryptoSession **ppCryptoSession) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoDecoderOutputView(
        ID3D11Resource *pResource,
        const D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoDecoderOutputView **ppVDOVView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoProcessorInputView(
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorInputView **ppVPIView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoProcessorOutputView(
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorOutputView **ppVPOView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoProcessorEnumerator(
        const D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc,
        ID3D11VideoProcessorEnumerator **ppEnum) = 0;

    virtual UINT STDMETHODCALLTYPE GetVideoDecoderProfileCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoDecoderProfile(
        UINT Index,
        GUID *pDecoderProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckVideoDecoderFormat(
        const GUID *pDecoderProfile,
        DXGI_FORMAT Format,
        WINBOOL *pSupported) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoDecoderConfigCount(
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoDecoderConfig(
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT Index,
        D3D11_VIDEO_DECODER_CONFIG *pConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentProtectionCaps(
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        D3D11_VIDEO_CONTENT_PROTECTION_CAPS *pCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckCryptoKeyExchange(
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        UINT Index,
        GUID *pKeyExchangeType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateData(
        REFGUID guid,
        UINT DataSize,
        const void *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(
        REFGUID guid,
        const IUnknown *pData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoDevice, 0x10ec4d5b, 0x975a, 0x4689, 0xb9,0xe4, 0xd0,0xaa,0xc3,0x0f,0xe3,0x33)
#endif
#else
typedef struct ID3D11VideoDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoDevice *This);

    /*** ID3D11VideoDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder)(
        ID3D11VideoDevice *This,
        const D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        const D3D11_VIDEO_DECODER_CONFIG *pConfig,
        ID3D11VideoDecoder **ppDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor)(
        ID3D11VideoDevice *This,
        ID3D11VideoProcessorEnumerator *pEnum,
        UINT RateConversionIndex,
        ID3D11VideoProcessor **ppVideoProcessor);

    HRESULT (STDMETHODCALLTYPE *CreateAuthenticatedChannel)(
        ID3D11VideoDevice *This,
        D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType,
        ID3D11AuthenticatedChannel **ppAuthenticatedChannel);

    HRESULT (STDMETHODCALLTYPE *CreateCryptoSession)(
        ID3D11VideoDevice *This,
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        const GUID *pKeyExchangeType,
        ID3D11CryptoSession **ppCryptoSession);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderOutputView)(
        ID3D11VideoDevice *This,
        ID3D11Resource *pResource,
        const D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoDecoderOutputView **ppVDOVView);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessorInputView)(
        ID3D11VideoDevice *This,
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorInputView **ppVPIView);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessorOutputView)(
        ID3D11VideoDevice *This,
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorOutputView **ppVPOView);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessorEnumerator)(
        ID3D11VideoDevice *This,
        const D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc,
        ID3D11VideoProcessorEnumerator **ppEnum);

    UINT (STDMETHODCALLTYPE *GetVideoDecoderProfileCount)(
        ID3D11VideoDevice *This);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderProfile)(
        ID3D11VideoDevice *This,
        UINT Index,
        GUID *pDecoderProfile);

    HRESULT (STDMETHODCALLTYPE *CheckVideoDecoderFormat)(
        ID3D11VideoDevice *This,
        const GUID *pDecoderProfile,
        DXGI_FORMAT Format,
        WINBOOL *pSupported);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderConfigCount)(
        ID3D11VideoDevice *This,
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderConfig)(
        ID3D11VideoDevice *This,
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT Index,
        D3D11_VIDEO_DECODER_CONFIG *pConfig);

    HRESULT (STDMETHODCALLTYPE *GetContentProtectionCaps)(
        ID3D11VideoDevice *This,
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        D3D11_VIDEO_CONTENT_PROTECTION_CAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *CheckCryptoKeyExchange)(
        ID3D11VideoDevice *This,
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        UINT Index,
        GUID *pKeyExchangeType);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoDevice *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoDevice *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3D11VideoDeviceVtbl;

interface ID3D11VideoDevice {
    CONST_VTBL ID3D11VideoDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoDevice_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11VideoDevice methods ***/
#define ID3D11VideoDevice_CreateVideoDecoder(This,pVideoDesc,pConfig,ppDecoder) (This)->lpVtbl->CreateVideoDecoder(This,pVideoDesc,pConfig,ppDecoder)
#define ID3D11VideoDevice_CreateVideoProcessor(This,pEnum,RateConversionIndex,ppVideoProcessor) (This)->lpVtbl->CreateVideoProcessor(This,pEnum,RateConversionIndex,ppVideoProcessor)
#define ID3D11VideoDevice_CreateAuthenticatedChannel(This,ChannelType,ppAuthenticatedChannel) (This)->lpVtbl->CreateAuthenticatedChannel(This,ChannelType,ppAuthenticatedChannel)
#define ID3D11VideoDevice_CreateCryptoSession(This,pCryptoType,pDecoderProfile,pKeyExchangeType,ppCryptoSession) (This)->lpVtbl->CreateCryptoSession(This,pCryptoType,pDecoderProfile,pKeyExchangeType,ppCryptoSession)
#define ID3D11VideoDevice_CreateVideoDecoderOutputView(This,pResource,pDesc,ppVDOVView) (This)->lpVtbl->CreateVideoDecoderOutputView(This,pResource,pDesc,ppVDOVView)
#define ID3D11VideoDevice_CreateVideoProcessorInputView(This,pResource,pEnum,pDesc,ppVPIView) (This)->lpVtbl->CreateVideoProcessorInputView(This,pResource,pEnum,pDesc,ppVPIView)
#define ID3D11VideoDevice_CreateVideoProcessorOutputView(This,pResource,pEnum,pDesc,ppVPOView) (This)->lpVtbl->CreateVideoProcessorOutputView(This,pResource,pEnum,pDesc,ppVPOView)
#define ID3D11VideoDevice_CreateVideoProcessorEnumerator(This,pDesc,ppEnum) (This)->lpVtbl->CreateVideoProcessorEnumerator(This,pDesc,ppEnum)
#define ID3D11VideoDevice_GetVideoDecoderProfileCount(This) (This)->lpVtbl->GetVideoDecoderProfileCount(This)
#define ID3D11VideoDevice_GetVideoDecoderProfile(This,Index,pDecoderProfile) (This)->lpVtbl->GetVideoDecoderProfile(This,Index,pDecoderProfile)
#define ID3D11VideoDevice_CheckVideoDecoderFormat(This,pDecoderProfile,Format,pSupported) (This)->lpVtbl->CheckVideoDecoderFormat(This,pDecoderProfile,Format,pSupported)
#define ID3D11VideoDevice_GetVideoDecoderConfigCount(This,pDesc,pCount) (This)->lpVtbl->GetVideoDecoderConfigCount(This,pDesc,pCount)
#define ID3D11VideoDevice_GetVideoDecoderConfig(This,pDesc,Index,pConfig) (This)->lpVtbl->GetVideoDecoderConfig(This,pDesc,Index,pConfig)
#define ID3D11VideoDevice_GetContentProtectionCaps(This,pCryptoType,pDecoderProfile,pCaps) (This)->lpVtbl->GetContentProtectionCaps(This,pCryptoType,pDecoderProfile,pCaps)
#define ID3D11VideoDevice_CheckCryptoKeyExchange(This,pCryptoType,pDecoderProfile,Index,pKeyExchangeType) (This)->lpVtbl->CheckCryptoKeyExchange(This,pCryptoType,pDecoderProfile,Index,pKeyExchangeType)
#define ID3D11VideoDevice_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoDevice_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoDevice_QueryInterface(ID3D11VideoDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoDevice_AddRef(ID3D11VideoDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoDevice_Release(ID3D11VideoDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11VideoDevice methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateVideoDecoder(ID3D11VideoDevice* This,const D3D11_VIDEO_DECODER_DESC *pVideoDesc,const D3D11_VIDEO_DECODER_CONFIG *pConfig,ID3D11VideoDecoder **ppDecoder) {
    return This->lpVtbl->CreateVideoDecoder(This,pVideoDesc,pConfig,ppDecoder);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateVideoProcessor(ID3D11VideoDevice* This,ID3D11VideoProcessorEnumerator *pEnum,UINT RateConversionIndex,ID3D11VideoProcessor **ppVideoProcessor) {
    return This->lpVtbl->CreateVideoProcessor(This,pEnum,RateConversionIndex,ppVideoProcessor);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateAuthenticatedChannel(ID3D11VideoDevice* This,D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType,ID3D11AuthenticatedChannel **ppAuthenticatedChannel) {
    return This->lpVtbl->CreateAuthenticatedChannel(This,ChannelType,ppAuthenticatedChannel);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateCryptoSession(ID3D11VideoDevice* This,const GUID *pCryptoType,const GUID *pDecoderProfile,const GUID *pKeyExchangeType,ID3D11CryptoSession **ppCryptoSession) {
    return This->lpVtbl->CreateCryptoSession(This,pCryptoType,pDecoderProfile,pKeyExchangeType,ppCryptoSession);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateVideoDecoderOutputView(ID3D11VideoDevice* This,ID3D11Resource *pResource,const D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc,ID3D11VideoDecoderOutputView **ppVDOVView) {
    return This->lpVtbl->CreateVideoDecoderOutputView(This,pResource,pDesc,ppVDOVView);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateVideoProcessorInputView(ID3D11VideoDevice* This,ID3D11Resource *pResource,ID3D11VideoProcessorEnumerator *pEnum,const D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc,ID3D11VideoProcessorInputView **ppVPIView) {
    return This->lpVtbl->CreateVideoProcessorInputView(This,pResource,pEnum,pDesc,ppVPIView);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateVideoProcessorOutputView(ID3D11VideoDevice* This,ID3D11Resource *pResource,ID3D11VideoProcessorEnumerator *pEnum,const D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc,ID3D11VideoProcessorOutputView **ppVPOView) {
    return This->lpVtbl->CreateVideoProcessorOutputView(This,pResource,pEnum,pDesc,ppVPOView);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CreateVideoProcessorEnumerator(ID3D11VideoDevice* This,const D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc,ID3D11VideoProcessorEnumerator **ppEnum) {
    return This->lpVtbl->CreateVideoProcessorEnumerator(This,pDesc,ppEnum);
}
static __WIDL_INLINE UINT ID3D11VideoDevice_GetVideoDecoderProfileCount(ID3D11VideoDevice* This) {
    return This->lpVtbl->GetVideoDecoderProfileCount(This);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_GetVideoDecoderProfile(ID3D11VideoDevice* This,UINT Index,GUID *pDecoderProfile) {
    return This->lpVtbl->GetVideoDecoderProfile(This,Index,pDecoderProfile);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CheckVideoDecoderFormat(ID3D11VideoDevice* This,const GUID *pDecoderProfile,DXGI_FORMAT Format,WINBOOL *pSupported) {
    return This->lpVtbl->CheckVideoDecoderFormat(This,pDecoderProfile,Format,pSupported);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_GetVideoDecoderConfigCount(ID3D11VideoDevice* This,const D3D11_VIDEO_DECODER_DESC *pDesc,UINT *pCount) {
    return This->lpVtbl->GetVideoDecoderConfigCount(This,pDesc,pCount);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_GetVideoDecoderConfig(ID3D11VideoDevice* This,const D3D11_VIDEO_DECODER_DESC *pDesc,UINT Index,D3D11_VIDEO_DECODER_CONFIG *pConfig) {
    return This->lpVtbl->GetVideoDecoderConfig(This,pDesc,Index,pConfig);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_GetContentProtectionCaps(ID3D11VideoDevice* This,const GUID *pCryptoType,const GUID *pDecoderProfile,D3D11_VIDEO_CONTENT_PROTECTION_CAPS *pCaps) {
    return This->lpVtbl->GetContentProtectionCaps(This,pCryptoType,pDecoderProfile,pCaps);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_CheckCryptoKeyExchange(ID3D11VideoDevice* This,const GUID *pCryptoType,const GUID *pDecoderProfile,UINT Index,GUID *pKeyExchangeType) {
    return This->lpVtbl->CheckCryptoKeyExchange(This,pCryptoType,pDecoderProfile,Index,pKeyExchangeType);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_SetPrivateData(ID3D11VideoDevice* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoDevice_SetPrivateDataInterface(ID3D11VideoDevice* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoContext interface
 */
#ifndef __ID3D11VideoContext_INTERFACE_DEFINED__
#define __ID3D11VideoContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoContext, 0x61f21c45, 0x3c0e, 0x4a74, 0x9c,0xea, 0x67,0x10,0x0d,0x9a,0xd5,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("61f21c45-3c0e-4a74-9cea-67100d9ad5e4")
ID3D11VideoContext : public ID3D11DeviceChild
{
    virtual HRESULT STDMETHODCALLTYPE GetDecoderBuffer(
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type,
        UINT *buffer_size,
        void **buffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseDecoderBuffer(
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE DecoderBeginFrame(
        ID3D11VideoDecoder *decoder,
        ID3D11VideoDecoderOutputView *view,
        UINT key_size,
        const void *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE DecoderEndFrame(
        ID3D11VideoDecoder *decoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubmitDecoderBuffers(
        ID3D11VideoDecoder *decoder,
        UINT buffers_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE DecoderExtension(
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_DECODER_EXTENSION *extension) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputTargetRect(
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        const RECT *rect) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputBackgroundColor(
        ID3D11VideoProcessor *processor,
        WINBOOL y_cb_cr,
        const D3D11_VIDEO_COLOR *color) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputColorSpace(
        ID3D11VideoProcessor *processor,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputAlphaFillMode(
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,
        UINT stream_idx) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputConstriction(
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        SIZE size) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputStereoMode(
        ID3D11VideoProcessor *processor,
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoProcessorSetOutputExtension(
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputTargetRect(
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        RECT *rect) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputBackgroundColor(
        ID3D11VideoProcessor *processor,
        WINBOOL *y_cb_cr,
        D3D11_VIDEO_COLOR *color) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputColorSpace(
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputAlphaFillMode(
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,
        UINT *stream_idx) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputConstriction(
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        SIZE *size) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputStereoMode(
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoProcessorGetOutputExtension(
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamFrameFormat(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT format) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamColorSpace(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamOutputRate(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,
        WINBOOL repeat,
        const DXGI_RATIONAL *custom_rate) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamSourceRect(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamDestRect(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamAlpha(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float alpha) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamPalette(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        const UINT *entries) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamPixelAspectRatio(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const DXGI_RATIONAL *src_aspect_ratio,
        const DXGI_RATIONAL *dst_aspect_ratio) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamLumaKey(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float lower,
        float upper) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamStereoFormat(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,
        WINBOOL left_view_frame0,
        WINBOOL base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,
        int mono_offset) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamAutoProcessingMode(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamFilter(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL enable,
        int level) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoProcessorSetStreamExtension(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamFrameFormat(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT *format) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamColorSpace(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamOutputRate(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,
        WINBOOL *repeat,
        DXGI_RATIONAL *custom_rate) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamSourceRect(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamDestRect(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamAlpha(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *alpha) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamPalette(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        UINT *entries) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamPixelAspectRatio(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        DXGI_RATIONAL *src_aspect_ratio,
        DXGI_RATIONAL *dst_aspect_ratio) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamLumaKey(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *lower,
        float *upper) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamStereoFormat(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,
        WINBOOL *left_view_frame0,
        WINBOOL *base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,
        int *mono_offset) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamAutoProcessingMode(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamFilter(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL *enabled,
        int *level) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoProcessorGetStreamExtension(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoProcessorBlt(
        ID3D11VideoProcessor *processor,
        ID3D11VideoProcessorOutputView *view,
        UINT frame_idx,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM *streams) = 0;

    virtual HRESULT STDMETHODCALLTYPE NegotiateCryptoSessionKeyExchange(
        ID3D11CryptoSession *session,
        UINT data_size,
        void *data) = 0;

    virtual void STDMETHODCALLTYPE EncryptionBlt(
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        UINT iv_size,
        void *iv) = 0;

    virtual void STDMETHODCALLTYPE DecryptionBlt(
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        D3D11_ENCRYPTED_BLOCK_INFO *block_info,
        UINT key_size,
        const void *key,
        UINT iv_size,
        void *iv) = 0;

    virtual void STDMETHODCALLTYPE StartSessionKeyRefresh(
        ID3D11CryptoSession *session,
        UINT random_number_size,
        void *random_number) = 0;

    virtual void STDMETHODCALLTYPE FinishSessionKeyRefresh(
        ID3D11CryptoSession *session) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEncryptionBltKey(
        ID3D11CryptoSession *session,
        UINT key_size,
        void *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE NegotiateAuthenticatedChannelKeyExchange(
        ID3D11AuthenticatedChannel *channel,
        UINT data_size,
        void *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryAuthenticatedChannel(
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        UINT output_size,
        void *output) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConfigureAuthenticatedChannel(
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamRotation(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_ROTATION rotation) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamRotation(
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enable,
        D3D11_VIDEO_PROCESSOR_ROTATION *rotation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoContext, 0x61f21c45, 0x3c0e, 0x4a74, 0x9c,0xea, 0x67,0x10,0x0d,0x9a,0xd5,0xe4)
#endif
#else
typedef struct ID3D11VideoContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoContext *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoContext *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoContext *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoContext *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoContext *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoContext methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDecoderBuffer)(
        ID3D11VideoContext *This,
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type,
        UINT *buffer_size,
        void **buffer);

    HRESULT (STDMETHODCALLTYPE *ReleaseDecoderBuffer)(
        ID3D11VideoContext *This,
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type);

    HRESULT (STDMETHODCALLTYPE *DecoderBeginFrame)(
        ID3D11VideoContext *This,
        ID3D11VideoDecoder *decoder,
        ID3D11VideoDecoderOutputView *view,
        UINT key_size,
        const void *key);

    HRESULT (STDMETHODCALLTYPE *DecoderEndFrame)(
        ID3D11VideoContext *This,
        ID3D11VideoDecoder *decoder);

    HRESULT (STDMETHODCALLTYPE *SubmitDecoderBuffers)(
        ID3D11VideoContext *This,
        ID3D11VideoDecoder *decoder,
        UINT buffers_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc);

    HRESULT (STDMETHODCALLTYPE *DecoderExtension)(
        ID3D11VideoContext *This,
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_DECODER_EXTENSION *extension);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputTargetRect)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputBackgroundColor)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL y_cb_cr,
        const D3D11_VIDEO_COLOR *color);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputColorSpace)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputAlphaFillMode)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,
        UINT stream_idx);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputConstriction)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        SIZE size);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputStereoMode)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorSetOutputExtension)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputTargetRect)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputBackgroundColor)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *y_cb_cr,
        D3D11_VIDEO_COLOR *color);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputColorSpace)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputAlphaFillMode)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,
        UINT *stream_idx);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputConstriction)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        SIZE *size);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputStereoMode)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetOutputExtension)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamFrameFormat)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT format);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamColorSpace)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamOutputRate)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,
        WINBOOL repeat,
        const DXGI_RATIONAL *custom_rate);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamSourceRect)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamDestRect)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamAlpha)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float alpha);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamPalette)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        const UINT *entries);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamPixelAspectRatio)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const DXGI_RATIONAL *src_aspect_ratio,
        const DXGI_RATIONAL *dst_aspect_ratio);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamLumaKey)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float lower,
        float upper);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamStereoFormat)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,
        WINBOOL left_view_frame0,
        WINBOOL base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,
        int mono_offset);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamAutoProcessingMode)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamFilter)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL enable,
        int level);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorSetStreamExtension)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamFrameFormat)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT *format);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamColorSpace)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamOutputRate)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,
        WINBOOL *repeat,
        DXGI_RATIONAL *custom_rate);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamSourceRect)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamDestRect)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamAlpha)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *alpha);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamPalette)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        UINT *entries);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamPixelAspectRatio)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        DXGI_RATIONAL *src_aspect_ratio,
        DXGI_RATIONAL *dst_aspect_ratio);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamLumaKey)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *lower,
        float *upper);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamStereoFormat)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,
        WINBOOL *left_view_frame0,
        WINBOOL *base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,
        int *mono_offset);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamAutoProcessingMode)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamFilter)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL *enabled,
        int *level);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetStreamExtension)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorBlt)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        ID3D11VideoProcessorOutputView *view,
        UINT frame_idx,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM *streams);

    HRESULT (STDMETHODCALLTYPE *NegotiateCryptoSessionKeyExchange)(
        ID3D11VideoContext *This,
        ID3D11CryptoSession *session,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *EncryptionBlt)(
        ID3D11VideoContext *This,
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        UINT iv_size,
        void *iv);

    void (STDMETHODCALLTYPE *DecryptionBlt)(
        ID3D11VideoContext *This,
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        D3D11_ENCRYPTED_BLOCK_INFO *block_info,
        UINT key_size,
        const void *key,
        UINT iv_size,
        void *iv);

    void (STDMETHODCALLTYPE *StartSessionKeyRefresh)(
        ID3D11VideoContext *This,
        ID3D11CryptoSession *session,
        UINT random_number_size,
        void *random_number);

    void (STDMETHODCALLTYPE *FinishSessionKeyRefresh)(
        ID3D11VideoContext *This,
        ID3D11CryptoSession *session);

    HRESULT (STDMETHODCALLTYPE *GetEncryptionBltKey)(
        ID3D11VideoContext *This,
        ID3D11CryptoSession *session,
        UINT key_size,
        void *key);

    HRESULT (STDMETHODCALLTYPE *NegotiateAuthenticatedChannelKeyExchange)(
        ID3D11VideoContext *This,
        ID3D11AuthenticatedChannel *channel,
        UINT data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *QueryAuthenticatedChannel)(
        ID3D11VideoContext *This,
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        UINT output_size,
        void *output);

    HRESULT (STDMETHODCALLTYPE *ConfigureAuthenticatedChannel)(
        ID3D11VideoContext *This,
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamRotation)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_ROTATION rotation);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamRotation)(
        ID3D11VideoContext *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enable,
        D3D11_VIDEO_PROCESSOR_ROTATION *rotation);

    END_INTERFACE
} ID3D11VideoContextVtbl;

interface ID3D11VideoContext {
    CONST_VTBL ID3D11VideoContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoContext_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoContext_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoContext_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoContext_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoContext_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoContext methods ***/
#define ID3D11VideoContext_GetDecoderBuffer(This,decoder,type,buffer_size,buffer) (This)->lpVtbl->GetDecoderBuffer(This,decoder,type,buffer_size,buffer)
#define ID3D11VideoContext_ReleaseDecoderBuffer(This,decoder,type) (This)->lpVtbl->ReleaseDecoderBuffer(This,decoder,type)
#define ID3D11VideoContext_DecoderBeginFrame(This,decoder,view,key_size,key) (This)->lpVtbl->DecoderBeginFrame(This,decoder,view,key_size,key)
#define ID3D11VideoContext_DecoderEndFrame(This,decoder) (This)->lpVtbl->DecoderEndFrame(This,decoder)
#define ID3D11VideoContext_SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc) (This)->lpVtbl->SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc)
#define ID3D11VideoContext_DecoderExtension(This,decoder,extension) (This)->lpVtbl->DecoderExtension(This,decoder,extension)
#define ID3D11VideoContext_VideoProcessorSetOutputTargetRect(This,processor,enable,rect) (This)->lpVtbl->VideoProcessorSetOutputTargetRect(This,processor,enable,rect)
#define ID3D11VideoContext_VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color) (This)->lpVtbl->VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color)
#define ID3D11VideoContext_VideoProcessorSetOutputColorSpace(This,processor,color_space) (This)->lpVtbl->VideoProcessorSetOutputColorSpace(This,processor,color_space)
#define ID3D11VideoContext_VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx) (This)->lpVtbl->VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx)
#define ID3D11VideoContext_VideoProcessorSetOutputConstriction(This,processor,enable,size) (This)->lpVtbl->VideoProcessorSetOutputConstriction(This,processor,enable,size)
#define ID3D11VideoContext_VideoProcessorSetOutputStereoMode(This,processor,enable) (This)->lpVtbl->VideoProcessorSetOutputStereoMode(This,processor,enable)
#define ID3D11VideoContext_VideoProcessorSetOutputExtension(This,processor,guid,data_size,data) (This)->lpVtbl->VideoProcessorSetOutputExtension(This,processor,guid,data_size,data)
#define ID3D11VideoContext_VideoProcessorGetOutputTargetRect(This,processor,enabled,rect) (This)->lpVtbl->VideoProcessorGetOutputTargetRect(This,processor,enabled,rect)
#define ID3D11VideoContext_VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color) (This)->lpVtbl->VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color)
#define ID3D11VideoContext_VideoProcessorGetOutputColorSpace(This,processor,color_space) (This)->lpVtbl->VideoProcessorGetOutputColorSpace(This,processor,color_space)
#define ID3D11VideoContext_VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx) (This)->lpVtbl->VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx)
#define ID3D11VideoContext_VideoProcessorGetOutputConstriction(This,processor,enabled,size) (This)->lpVtbl->VideoProcessorGetOutputConstriction(This,processor,enabled,size)
#define ID3D11VideoContext_VideoProcessorGetOutputStereoMode(This,processor,enabled) (This)->lpVtbl->VideoProcessorGetOutputStereoMode(This,processor,enabled)
#define ID3D11VideoContext_VideoProcessorGetOutputExtension(This,processor,guid,data_size,data) (This)->lpVtbl->VideoProcessorGetOutputExtension(This,processor,guid,data_size,data)
#define ID3D11VideoContext_VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format) (This)->lpVtbl->VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format)
#define ID3D11VideoContext_VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space) (This)->lpVtbl->VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space)
#define ID3D11VideoContext_VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate) (This)->lpVtbl->VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate)
#define ID3D11VideoContext_VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect) (This)->lpVtbl->VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect)
#define ID3D11VideoContext_VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect) (This)->lpVtbl->VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect)
#define ID3D11VideoContext_VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha) (This)->lpVtbl->VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha)
#define ID3D11VideoContext_VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries) (This)->lpVtbl->VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries)
#define ID3D11VideoContext_VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio) (This)->lpVtbl->VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio)
#define ID3D11VideoContext_VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper) (This)->lpVtbl->VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper)
#define ID3D11VideoContext_VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset) (This)->lpVtbl->VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset)
#define ID3D11VideoContext_VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable) (This)->lpVtbl->VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable)
#define ID3D11VideoContext_VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level) (This)->lpVtbl->VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level)
#define ID3D11VideoContext_VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data) (This)->lpVtbl->VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data)
#define ID3D11VideoContext_VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format) (This)->lpVtbl->VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format)
#define ID3D11VideoContext_VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space) (This)->lpVtbl->VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space)
#define ID3D11VideoContext_VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate) (This)->lpVtbl->VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate)
#define ID3D11VideoContext_VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect) (This)->lpVtbl->VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect)
#define ID3D11VideoContext_VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect) (This)->lpVtbl->VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect)
#define ID3D11VideoContext_VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha) (This)->lpVtbl->VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha)
#define ID3D11VideoContext_VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries) (This)->lpVtbl->VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries)
#define ID3D11VideoContext_VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio) (This)->lpVtbl->VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio)
#define ID3D11VideoContext_VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper) (This)->lpVtbl->VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper)
#define ID3D11VideoContext_VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset) (This)->lpVtbl->VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset)
#define ID3D11VideoContext_VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled) (This)->lpVtbl->VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled)
#define ID3D11VideoContext_VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level) (This)->lpVtbl->VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level)
#define ID3D11VideoContext_VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data) (This)->lpVtbl->VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data)
#define ID3D11VideoContext_VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams) (This)->lpVtbl->VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams)
#define ID3D11VideoContext_NegotiateCryptoSessionKeyExchange(This,session,data_size,data) (This)->lpVtbl->NegotiateCryptoSessionKeyExchange(This,session,data_size,data)
#define ID3D11VideoContext_EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv) (This)->lpVtbl->EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv)
#define ID3D11VideoContext_DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv) (This)->lpVtbl->DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv)
#define ID3D11VideoContext_StartSessionKeyRefresh(This,session,random_number_size,random_number) (This)->lpVtbl->StartSessionKeyRefresh(This,session,random_number_size,random_number)
#define ID3D11VideoContext_FinishSessionKeyRefresh(This,session) (This)->lpVtbl->FinishSessionKeyRefresh(This,session)
#define ID3D11VideoContext_GetEncryptionBltKey(This,session,key_size,key) (This)->lpVtbl->GetEncryptionBltKey(This,session,key_size,key)
#define ID3D11VideoContext_NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data) (This)->lpVtbl->NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data)
#define ID3D11VideoContext_QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output) (This)->lpVtbl->QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output)
#define ID3D11VideoContext_ConfigureAuthenticatedChannel(This,channel,input_size,input,output) (This)->lpVtbl->ConfigureAuthenticatedChannel(This,channel,input_size,input,output)
#define ID3D11VideoContext_VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation) (This)->lpVtbl->VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation)
#define ID3D11VideoContext_VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation) (This)->lpVtbl->VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoContext_QueryInterface(ID3D11VideoContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11VideoContext_AddRef(ID3D11VideoContext* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11VideoContext_Release(ID3D11VideoContext* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static __WIDL_INLINE void ID3D11VideoContext_GetDevice(ID3D11VideoContext* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_GetPrivateData(ID3D11VideoContext* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_SetPrivateData(ID3D11VideoContext* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_SetPrivateDataInterface(ID3D11VideoContext* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoContext methods ***/
static __WIDL_INLINE HRESULT ID3D11VideoContext_GetDecoderBuffer(ID3D11VideoContext* This,ID3D11VideoDecoder *decoder,D3D11_VIDEO_DECODER_BUFFER_TYPE type,UINT *buffer_size,void **buffer) {
    return This->lpVtbl->GetDecoderBuffer(This,decoder,type,buffer_size,buffer);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_ReleaseDecoderBuffer(ID3D11VideoContext* This,ID3D11VideoDecoder *decoder,D3D11_VIDEO_DECODER_BUFFER_TYPE type) {
    return This->lpVtbl->ReleaseDecoderBuffer(This,decoder,type);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_DecoderBeginFrame(ID3D11VideoContext* This,ID3D11VideoDecoder *decoder,ID3D11VideoDecoderOutputView *view,UINT key_size,const void *key) {
    return This->lpVtbl->DecoderBeginFrame(This,decoder,view,key_size,key);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_DecoderEndFrame(ID3D11VideoContext* This,ID3D11VideoDecoder *decoder) {
    return This->lpVtbl->DecoderEndFrame(This,decoder);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_SubmitDecoderBuffers(ID3D11VideoContext* This,ID3D11VideoDecoder *decoder,UINT buffers_count,const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc) {
    return This->lpVtbl->SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_DecoderExtension(ID3D11VideoContext* This,ID3D11VideoDecoder *decoder,const D3D11_VIDEO_DECODER_EXTENSION *extension) {
    return This->lpVtbl->DecoderExtension(This,decoder,extension);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetOutputTargetRect(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetOutputTargetRect(This,processor,enable,rect);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetOutputBackgroundColor(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL y_cb_cr,const D3D11_VIDEO_COLOR *color) {
    This->lpVtbl->VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetOutputColorSpace(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorSetOutputColorSpace(This,processor,color_space);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetOutputAlphaFillMode(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,UINT stream_idx) {
    This->lpVtbl->VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetOutputConstriction(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL enable,SIZE size) {
    This->lpVtbl->VideoProcessorSetOutputConstriction(This,processor,enable,size);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetOutputStereoMode(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL enable) {
    This->lpVtbl->VideoProcessorSetOutputStereoMode(This,processor,enable);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_VideoProcessorSetOutputExtension(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorSetOutputExtension(This,processor,guid,data_size,data);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetOutputTargetRect(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetOutputTargetRect(This,processor,enabled,rect);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetOutputBackgroundColor(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL *y_cb_cr,D3D11_VIDEO_COLOR *color) {
    This->lpVtbl->VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetOutputColorSpace(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorGetOutputColorSpace(This,processor,color_space);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetOutputAlphaFillMode(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,UINT *stream_idx) {
    This->lpVtbl->VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetOutputConstriction(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL *enabled,SIZE *size) {
    This->lpVtbl->VideoProcessorGetOutputConstriction(This,processor,enabled,size);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetOutputStereoMode(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,WINBOOL *enabled) {
    This->lpVtbl->VideoProcessorGetOutputStereoMode(This,processor,enabled);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_VideoProcessorGetOutputExtension(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorGetOutputExtension(This,processor,guid,data_size,data);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamFrameFormat(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_FRAME_FORMAT format) {
    This->lpVtbl->VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamColorSpace(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamOutputRate(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,WINBOOL repeat,const DXGI_RATIONAL *custom_rate) {
    This->lpVtbl->VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamSourceRect(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamDestRect(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamAlpha(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,float alpha) {
    This->lpVtbl->VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamPalette(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,UINT entry_count,const UINT *entries) {
    This->lpVtbl->VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamPixelAspectRatio(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const DXGI_RATIONAL *src_aspect_ratio,const DXGI_RATIONAL *dst_aspect_ratio) {
    This->lpVtbl->VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamLumaKey(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,float lower,float upper) {
    This->lpVtbl->VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamStereoFormat(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,WINBOOL left_view_frame0,WINBOOL base_view_frame0,D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,int mono_offset) {
    This->lpVtbl->VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamAutoProcessingMode(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable) {
    This->lpVtbl->VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamFilter(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_FILTER filter,WINBOOL enable,int level) {
    This->lpVtbl->VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_VideoProcessorSetStreamExtension(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamFrameFormat(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_FRAME_FORMAT *format) {
    This->lpVtbl->VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamColorSpace(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamOutputRate(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,WINBOOL *repeat,DXGI_RATIONAL *custom_rate) {
    This->lpVtbl->VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamSourceRect(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamDestRect(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamAlpha(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,float *alpha) {
    This->lpVtbl->VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamPalette(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,UINT entry_count,UINT *entries) {
    This->lpVtbl->VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamPixelAspectRatio(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,DXGI_RATIONAL *src_aspect_ratio,DXGI_RATIONAL *dst_aspect_ratio) {
    This->lpVtbl->VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamLumaKey(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,float *lower,float *upper) {
    This->lpVtbl->VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamStereoFormat(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,WINBOOL *left_view_frame0,WINBOOL *base_view_frame0,D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,int *mono_offset) {
    This->lpVtbl->VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamAutoProcessingMode(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled) {
    This->lpVtbl->VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamFilter(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_FILTER filter,WINBOOL *enabled,int *level) {
    This->lpVtbl->VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_VideoProcessorGetStreamExtension(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_VideoProcessorBlt(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,ID3D11VideoProcessorOutputView *view,UINT frame_idx,UINT stream_count,const D3D11_VIDEO_PROCESSOR_STREAM *streams) {
    return This->lpVtbl->VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_NegotiateCryptoSessionKeyExchange(ID3D11VideoContext* This,ID3D11CryptoSession *session,UINT data_size,void *data) {
    return This->lpVtbl->NegotiateCryptoSessionKeyExchange(This,session,data_size,data);
}
static __WIDL_INLINE void ID3D11VideoContext_EncryptionBlt(ID3D11VideoContext* This,ID3D11CryptoSession *session,ID3D11Texture2D *src_surface,ID3D11Texture2D *dst_surface,UINT iv_size,void *iv) {
    This->lpVtbl->EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv);
}
static __WIDL_INLINE void ID3D11VideoContext_DecryptionBlt(ID3D11VideoContext* This,ID3D11CryptoSession *session,ID3D11Texture2D *src_surface,ID3D11Texture2D *dst_surface,D3D11_ENCRYPTED_BLOCK_INFO *block_info,UINT key_size,const void *key,UINT iv_size,void *iv) {
    This->lpVtbl->DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv);
}
static __WIDL_INLINE void ID3D11VideoContext_StartSessionKeyRefresh(ID3D11VideoContext* This,ID3D11CryptoSession *session,UINT random_number_size,void *random_number) {
    This->lpVtbl->StartSessionKeyRefresh(This,session,random_number_size,random_number);
}
static __WIDL_INLINE void ID3D11VideoContext_FinishSessionKeyRefresh(ID3D11VideoContext* This,ID3D11CryptoSession *session) {
    This->lpVtbl->FinishSessionKeyRefresh(This,session);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_GetEncryptionBltKey(ID3D11VideoContext* This,ID3D11CryptoSession *session,UINT key_size,void *key) {
    return This->lpVtbl->GetEncryptionBltKey(This,session,key_size,key);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_NegotiateAuthenticatedChannelKeyExchange(ID3D11VideoContext* This,ID3D11AuthenticatedChannel *channel,UINT data_size,void *data) {
    return This->lpVtbl->NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_QueryAuthenticatedChannel(ID3D11VideoContext* This,ID3D11AuthenticatedChannel *channel,UINT input_size,const void *input,UINT output_size,void *output) {
    return This->lpVtbl->QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output);
}
static __WIDL_INLINE HRESULT ID3D11VideoContext_ConfigureAuthenticatedChannel(ID3D11VideoContext* This,ID3D11AuthenticatedChannel *channel,UINT input_size,const void *input,D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output) {
    return This->lpVtbl->ConfigureAuthenticatedChannel(This,channel,input_size,input,output);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorSetStreamRotation(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,D3D11_VIDEO_PROCESSOR_ROTATION rotation) {
    This->lpVtbl->VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation);
}
static __WIDL_INLINE void ID3D11VideoContext_VideoProcessorGetStreamRotation(ID3D11VideoContext* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enable,D3D11_VIDEO_PROCESSOR_ROTATION *rotation) {
    This->lpVtbl->VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Device interface
 */
#ifndef __ID3D11Device_INTERFACE_DEFINED__
#define __ID3D11Device_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Device, 0xdb6f6ddb, 0xac77, 0x4e88, 0x82,0x53, 0x81,0x9d,0xf9,0xbb,0xf1,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db6f6ddb-ac77-4e88-8253-819df9bbf140")
ID3D11Device : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateBuffer(
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTexture1D(
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTexture2D(
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTexture3D(
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateShaderResourceView(
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUnorderedAccessView(
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRenderTargetView(
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDepthStencilView(
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInputLayout(
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVertexShader(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGeometryShader(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGeometryShaderWithStreamOutput(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePixelShader(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateHullShader(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDomainShader(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateComputeShader(
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateClassLinkage(
        ID3D11ClassLinkage **ppLinkage) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBlendState(
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDepthStencilState(
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRasterizerState(
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSamplerState(
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQuery(
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePredicate(
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCounter(
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDeferredContext(
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenSharedResource(
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckFormatSupport(
        DXGI_FORMAT Format,
        UINT *pFormatSupport) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckMultisampleQualityLevels(
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels) = 0;

    virtual void STDMETHODCALLTYPE CheckCounterInfo(
        D3D11_COUNTER_INFO *pCounterInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckCounter(
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckFeatureSupport(
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrivateData(
        REFGUID guid,
        UINT *pDataSize,
        void *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateData(
        REFGUID guid,
        UINT DataSize,
        const void *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(
        REFGUID guid,
        const IUnknown *pData) = 0;

    virtual D3D_FEATURE_LEVEL STDMETHODCALLTYPE GetFeatureLevel(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetCreationFlags(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceRemovedReason(
        ) = 0;

    virtual void STDMETHODCALLTYPE GetImmediateContext(
        ID3D11DeviceContext **ppImmediateContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetExceptionMode(
        UINT RaiseFlags) = 0;

    virtual UINT STDMETHODCALLTYPE GetExceptionMode(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Device, 0xdb6f6ddb, 0xac77, 0x4e88, 0x82,0x53, 0x81,0x9d,0xf9,0xbb,0xf1,0x40)
#endif
#else
typedef struct ID3D11DeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Device *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Device *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Device *This);

    /*** ID3D11Device methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D11Device *This,
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D11Device *This,
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D11Device *This,
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D11Device *This,
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D11Device *This,
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView)(
        ID3D11Device *This,
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D11Device *This,
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D11Device *This,
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D11Device *This,
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateHullShader)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader);

    HRESULT (STDMETHODCALLTYPE *CreateDomainShader)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader);

    HRESULT (STDMETHODCALLTYPE *CreateComputeShader)(
        ID3D11Device *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader);

    HRESULT (STDMETHODCALLTYPE *CreateClassLinkage)(
        ID3D11Device *This,
        ID3D11ClassLinkage **ppLinkage);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D11Device *This,
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D11Device *This,
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D11Device *This,
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D11Device *This,
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D11Device *This,
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D11Device *This,
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D11Device *This,
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext)(
        ID3D11Device *This,
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D11Device *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D11Device *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D11Device *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D11Device *This,
        D3D11_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D11Device *This,
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength);

    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D11Device *This,
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Device *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Device *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Device *This,
        REFGUID guid,
        const IUnknown *pData);

    D3D_FEATURE_LEVEL (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D11Device *This);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D11Device *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D11Device *This);

    void (STDMETHODCALLTYPE *GetImmediateContext)(
        ID3D11Device *This,
        ID3D11DeviceContext **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D11Device *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D11Device *This);

    END_INTERFACE
} ID3D11DeviceVtbl;

interface ID3D11Device {
    CONST_VTBL ID3D11DeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Device_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Device_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Device_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Device methods ***/
#define ID3D11Device_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D11Device_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D11Device_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D11Device_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D11Device_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D11Device_CreateUnorderedAccessView(This,pResource,pDesc,ppUAView) (This)->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView)
#define ID3D11Device_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D11Device_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D11Device_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D11Device_CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader)
#define ID3D11Device_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader)
#define ID3D11Device_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader)
#define ID3D11Device_CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader)
#define ID3D11Device_CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader) (This)->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader)
#define ID3D11Device_CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader) (This)->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader)
#define ID3D11Device_CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader) (This)->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader)
#define ID3D11Device_CreateClassLinkage(This,ppLinkage) (This)->lpVtbl->CreateClassLinkage(This,ppLinkage)
#define ID3D11Device_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D11Device_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D11Device_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D11Device_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D11Device_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D11Device_CreateDeferredContext(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext)
#define ID3D11Device_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D11Device_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D11Device_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D11Device_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D11Device_CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength)
#define ID3D11Device_CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize) (This)->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize)
#define ID3D11Device_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Device_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Device_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D11Device_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#define ID3D11Device_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D11Device_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D11Device_GetImmediateContext(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext(This,ppImmediateContext)
#define ID3D11Device_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D11Device_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D11Device_QueryInterface(ID3D11Device* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D11Device_AddRef(ID3D11Device* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D11Device_Release(ID3D11Device* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Device methods ***/
static __WIDL_INLINE HRESULT ID3D11Device_CreateBuffer(ID3D11Device* This,const D3D11_BUFFER_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateTexture1D(ID3D11Device* This,const D3D11_TEXTURE1D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateTexture2D(ID3D11Device* This,const D3D11_TEXTURE2D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateTexture3D(ID3D11Device* This,const D3D11_TEXTURE3D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateShaderResourceView(ID3D11Device* This,ID3D11Resource *pResource,const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D11ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateUnorderedAccessView(ID3D11Device* This,ID3D11Resource *pResource,const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,ID3D11UnorderedAccessView **ppUAView) {
    return This->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateRenderTargetView(ID3D11Device* This,ID3D11Resource *pResource,const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,ID3D11RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateDepthStencilView(ID3D11Device* This,ID3D11Resource *pResource,const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D11DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateInputLayout(ID3D11Device* This,const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D11InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateVertexShader(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateGeometryShader(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateGeometryShaderWithStreamOutput(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,const UINT *pBufferStrides,UINT NumStrides,UINT RasterizedStream,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreatePixelShader(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateHullShader(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11HullShader **ppHullShader) {
    return This->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateDomainShader(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11DomainShader **ppDomainShader) {
    return This->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateComputeShader(ID3D11Device* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11ComputeShader **ppComputeShader) {
    return This->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateClassLinkage(ID3D11Device* This,ID3D11ClassLinkage **ppLinkage) {
    return This->lpVtbl->CreateClassLinkage(This,ppLinkage);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateBlendState(ID3D11Device* This,const D3D11_BLEND_DESC *pBlendStateDesc,ID3D11BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateDepthStencilState(ID3D11Device* This,const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D11DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateRasterizerState(ID3D11Device* This,const D3D11_RASTERIZER_DESC *pRasterizerDesc,ID3D11RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateSamplerState(ID3D11Device* This,const D3D11_SAMPLER_DESC *pSamplerDesc,ID3D11SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateQuery(ID3D11Device* This,const D3D11_QUERY_DESC *pQueryDesc,ID3D11Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreatePredicate(ID3D11Device* This,const D3D11_QUERY_DESC *pPredicateDesc,ID3D11Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateCounter(ID3D11Device* This,const D3D11_COUNTER_DESC *pCounterDesc,ID3D11Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static __WIDL_INLINE HRESULT ID3D11Device_CreateDeferredContext(ID3D11Device* This,UINT ContextFlags,ID3D11DeviceContext **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext);
}
static __WIDL_INLINE HRESULT ID3D11Device_OpenSharedResource(ID3D11Device* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static __WIDL_INLINE HRESULT ID3D11Device_CheckFormatSupport(ID3D11Device* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static __WIDL_INLINE HRESULT ID3D11Device_CheckMultisampleQualityLevels(ID3D11Device* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static __WIDL_INLINE void ID3D11Device_CheckCounterInfo(ID3D11Device* This,D3D11_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static __WIDL_INLINE HRESULT ID3D11Device_CheckCounter(ID3D11Device* This,const D3D11_COUNTER_DESC *pDesc,D3D11_COUNTER_TYPE *pType,UINT *pActiveCounters,LPSTR szName,UINT *pNameLength,LPSTR szUnits,UINT *pUnitsLength,LPSTR szDescription,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength);
}
static __WIDL_INLINE HRESULT ID3D11Device_CheckFeatureSupport(ID3D11Device* This,D3D11_FEATURE Feature,void *pFeatureSupportData,UINT FeatureSupportDataSize) {
    return This->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize);
}
static __WIDL_INLINE HRESULT ID3D11Device_GetPrivateData(ID3D11Device* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Device_SetPrivateData(ID3D11Device* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static __WIDL_INLINE HRESULT ID3D11Device_SetPrivateDataInterface(ID3D11Device* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static __WIDL_INLINE D3D_FEATURE_LEVEL ID3D11Device_GetFeatureLevel(ID3D11Device* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
static __WIDL_INLINE UINT ID3D11Device_GetCreationFlags(ID3D11Device* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static __WIDL_INLINE HRESULT ID3D11Device_GetDeviceRemovedReason(ID3D11Device* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static __WIDL_INLINE void ID3D11Device_GetImmediateContext(ID3D11Device* This,ID3D11DeviceContext **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext(This,ppImmediateContext);
}
static __WIDL_INLINE HRESULT ID3D11Device_SetExceptionMode(ID3D11Device* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static __WIDL_INLINE UINT ID3D11Device_GetExceptionMode(ID3D11Device* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11Device_INTERFACE_DEFINED__ */

typedef enum D3D11_CREATE_DEVICE_FLAG {
    D3D11_CREATE_DEVICE_SINGLETHREADED = 0x1,
    D3D11_CREATE_DEVICE_DEBUG = 0x2,
    D3D11_CREATE_DEVICE_SWITCH_TO_REF = 0x4,
    D3D11_CREATE_DEVICE_PREVENT_INTERNAL_THREADING_OPTIMIZATIONS = 0x8,
    D3D11_CREATE_DEVICE_BGRA_SUPPORT = 0x20,
    D3D11_CREATE_DEVICE_DEBUGGABLE = 0x40,
    D3D11_CREATE_DEVICE_PREVENT_ALTERING_LAYER_SETTINGS_FROM_REGISTRY = 0x80,
    D3D11_CREATE_DEVICE_DISABLE_GPU_TIMEOUT = 0x100,
    D3D11_CREATE_DEVICE_VIDEO_SUPPORT = 0x800
} D3D11_CREATE_DEVICE_FLAG;
typedef enum D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT {
    D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT_INPUT = 0x1,
    D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT_OUTPUT = 0x2
} D3D11_VIDEO_PROCESSOR_FORMAT_SUPPORT;
#define D3D11_SDK_VERSION (7)

#include <d3d10_1.h>
#ifndef D3D11_IGNORE_SDK_LAYERS
# include <d3d11sdklayers.h>
#endif
#include <d3d10misc.h>
#include <d3d10shader.h>
#include <d3d10effect.h>
#include <d3d10_1shader.h>
#define _FACD3D11 (0x87c)

#define MAKE_D3D11_HRESULT(code) MAKE_HRESULT(SEVERITY_ERROR, _FACD3D11, code)
typedef HRESULT (WINAPI* PFN_D3D11_CREATE_DEVICE)(IDXGIAdapter*,D3D_DRIVER_TYPE,HMODULE,UINT,
    const D3D_FEATURE_LEVEL*,UINT,UINT,ID3D11Device**,D3D_FEATURE_LEVEL*,ID3D11DeviceContext**);
HRESULT WINAPI D3D11CreateDevice(IDXGIAdapter*,D3D_DRIVER_TYPE,HMODULE,UINT,const D3D_FEATURE_LEVEL*,
    UINT,UINT,ID3D11Device**,D3D_FEATURE_LEVEL*,ID3D11DeviceContext**);
typedef HRESULT (WINAPI *PFN_D3D11_CREATE_DEVICE_AND_SWAP_CHAIN)(IDXGIAdapter*,D3D_DRIVER_TYPE,HMODULE,UINT,
    const D3D_FEATURE_LEVEL*,UINT,UINT,const DXGI_SWAP_CHAIN_DESC*,IDXGISwapChain**,ID3D11Device**,
    D3D_FEATURE_LEVEL*,ID3D11DeviceContext**);
HRESULT __stdcall  D3D11CreateDeviceAndSwapChain(IDXGIAdapter *adapter,D3D_DRIVER_TYPE driver_type,HMODULE swrast,UINT flags,const D3D_FEATURE_LEVEL *feature_levels,UINT levels,UINT sdk_version,const DXGI_SWAP_CHAIN_DESC *swapchain_desc,IDXGISwapChain **swapchain,ID3D11Device **device,D3D_FEATURE_LEVEL *obtained_feature_level,ID3D11DeviceContext **immediate_context);

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d11_h__ */
