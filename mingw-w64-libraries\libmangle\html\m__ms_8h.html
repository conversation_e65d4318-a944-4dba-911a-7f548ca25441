<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: src/m_ms.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>src/m_ms.h File Reference</h1><code>#include &quot;<a class="el" href="m__token_8h_source.html">m_token.h</a>&quot;</code><br/>

<p><a href="m__ms_8h_source.html">Go to the source code of this file.</a></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Structures</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_cached.html">sCached</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_s_ctx.html">sMSCtx</a></td></tr>
<tr><td colspan="2"><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#aef5f6ad4353a2cf2321c074dbfaa9aac">ENCODING_TYPE_MS</a>&nbsp;&nbsp;&nbsp;1</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#a0abbf2a725f45243f4292bf3e764973c">GET_CHAR</a>(CTX)&nbsp;&nbsp;&nbsp;((CTX)-&gt;pos == (CTX)-&gt;end ? 0 : (CTX)-&gt;pos[0])</td></tr>
<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Get currently marked character from <em>CTX</em>.  <a href="#a0abbf2a725f45243f4292bf3e764973c"></a><br/></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#adfca56cc6bed709fa84cc0b26430100d">INC_CHAR</a>(CTX)&nbsp;&nbsp;&nbsp;do { if ((CTX)-&gt;pos != (CTX)-&gt;end) (CTX)-&gt;pos++; } while (0)</td></tr>
<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Increments <em>ctx</em> position.  <a href="#adfca56cc6bed709fa84cc0b26430100d"></a><br/></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#aac4ed973666f5f09c3e866666326fb05">DEC_CHAR</a>(CTX)&nbsp;&nbsp;&nbsp;do { if ((CTX)-&gt;pos != (CTX)-&gt;name) (CTX)-&gt;pos--; } while (0)</td></tr>
<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Decrements <em>ctx</em> position.  <a href="#aac4ed973666f5f09c3e866666326fb05"></a><br/></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#a1a18b928484e6e93526a252d4b7e3532">SKIP_CHAR</a>(CTX, LEN)&nbsp;&nbsp;&nbsp;do { (CTX)-&gt;pos += (LEN); if ((CTX)-&gt;pos &gt; (CTX)-&gt;end) (CTX)-&gt;pos=(CTX)-&gt;end; } while (0)</td></tr>
<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Increments <em>CTX</em> position by LEN.  <a href="#a1a18b928484e6e93526a252d4b7e3532"></a><br/></td></tr>
<tr><td colspan="2"><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#a53be44f77ef7b80bfc16250da927a99e">libmangle_decode_ms_name</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, const char *name)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8h.html#a0872a8e6f16a49ccfc3e8663ed003354">libmangle_encode_ms_name</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <a class="el" href="unionu_m_token.html">uMToken</a> *tok)</td></tr>
</table>
<hr/><h2>Define Documentation</h2>
<a class="anchor" id="aac4ed973666f5f09c3e866666326fb05"></a><!-- doxytag: member="m_ms.h::DEC_CHAR" ref="aac4ed973666f5f09c3e866666326fb05" args="(CTX)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DEC_CHAR</td>
          <td>(</td>
          <td class="paramtype">CTX&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;do { if ((CTX)-&gt;pos != (CTX)-&gt;name) (CTX)-&gt;pos--; } while (0)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>Decrements <em>ctx</em> position. </p>
<p>Move marker to previous character if it is currently not the first via <em>CTX</em>. </p>

</div>
</div>
<a class="anchor" id="aef5f6ad4353a2cf2321c074dbfaa9aac"></a><!-- doxytag: member="m_ms.h::ENCODING_TYPE_MS" ref="aef5f6ad4353a2cf2321c074dbfaa9aac" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define ENCODING_TYPE_MS&nbsp;&nbsp;&nbsp;1</td>
        </tr>
      </table>
</div>
<div class="memdoc">

</div>
</div>
<a class="anchor" id="a0abbf2a725f45243f4292bf3e764973c"></a><!-- doxytag: member="m_ms.h::GET_CHAR" ref="a0abbf2a725f45243f4292bf3e764973c" args="(CTX)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GET_CHAR</td>
          <td>(</td>
          <td class="paramtype">CTX&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((CTX)-&gt;pos == (CTX)-&gt;end ? 0 : (CTX)-&gt;pos[0])</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>Get currently marked character from <em>CTX</em>. </p>
<p>Get character from at current possition via <em>CTX</em>. </p>

</div>
</div>
<a class="anchor" id="adfca56cc6bed709fa84cc0b26430100d"></a><!-- doxytag: member="m_ms.h::INC_CHAR" ref="adfca56cc6bed709fa84cc0b26430100d" args="(CTX)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define INC_CHAR</td>
          <td>(</td>
          <td class="paramtype">CTX&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;do { if ((CTX)-&gt;pos != (CTX)-&gt;end) (CTX)-&gt;pos++; } while (0)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>Increments <em>ctx</em> position. </p>
<p>Move marker to next character if it is currently not the last via <em>CTX</em>. </p>

</div>
</div>
<a class="anchor" id="a1a18b928484e6e93526a252d4b7e3532"></a><!-- doxytag: member="m_ms.h::SKIP_CHAR" ref="a1a18b928484e6e93526a252d4b7e3532" args="(CTX, LEN)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SKIP_CHAR</td>
          <td>(</td>
          <td class="paramtype">CTX, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">LEN&nbsp;</td>
          <td class="paramname"></td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;do { (CTX)-&gt;pos += (LEN); if ((CTX)-&gt;pos &gt; (CTX)-&gt;end) (CTX)-&gt;pos=(CTX)-&gt;end; } while (0)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>Increments <em>CTX</em> position by LEN. </p>
<p>Increments <em>CTX</em> marker by LEN characters, points to last character if marker is moved out of bounds. </p>

</div>
</div>
<hr/><h2>Function Documentation</h2>
<a class="anchor" id="a53be44f77ef7b80bfc16250da927a99e"></a><!-- doxytag: member="m_ms.h::libmangle_decode_ms_name" ref="a53be44f77ef7b80bfc16250da927a99e" args="(libmangle_gc_context_t *gc, const char *name)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* libmangle_decode_ms_name </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>name</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Decodes an MSVC export name. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td>sGcCtx pointer for collecting memory allocations. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>name</em>&nbsp;</td><td>MSVC C++ mangled export string. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#abf4af1b2e483a32beb147474853e696b">libmangle_sprint_decl()</a> </dd>
<dd>
<a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> </dd>
<dd>
<a class="el" href="unionu_m_token.html">uMToken</a> </dd></dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Token containing information about the mangled string, use <a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> to free after use.</dd></dl>
<p>Decodes an MSVC export name. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> pointer for collecting memory allocations. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>name</em>&nbsp;</td><td>MSVC C++ mangled export string. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#abf4af1b2e483a32beb147474853e696b">libmangle_sprint_decl()</a> </dd>
<dd>
<a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> </dd>
<dd>
<a class="el" href="libmangle_8h.html#a7c9c7d368eb1f52cac14457766a01cc7">libmangle_tokens_t</a> </dd></dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Token containing information about the mangled string, use <a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> to free after use. </dd></dl>

</div>
</div>
<a class="anchor" id="a0872a8e6f16a49ccfc3e8663ed003354"></a><!-- doxytag: member="m_ms.h::libmangle_encode_ms_name" ref="a0872a8e6f16a49ccfc3e8663ed003354" args="(libmangle_gc_context_t *gc, uMToken *tok)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* libmangle_encode_ms_name </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>tok</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

</div>
</div>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
