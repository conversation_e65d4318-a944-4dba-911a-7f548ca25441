#include "func.def.in"

LIBRARY "ntdll.dll"
EXPORTS
#ifdef DEF_X64
PropertyLengthAsVariant
RtlConvertPropertyToVariant
RtlConvertVariantToProperty
#endif
A_SHAFinal
A_SHAInit
A_SHAUpdate
AlpcAdjustCompletionListConcurrencyCount
AlpcFreeCompletionListMessage
AlpcGetCompletionListLastMessageInformation
AlpcGetCompletionListMessageAttributes
AlpcGetHeaderSize
AlpcGetMessageAttribute
AlpcGetMessageFromCompletionList
AlpcGetOutstandingCompletionListMessageCount
AlpcInitializeMessageAttribute
AlpcMaxAllowedMessageLength
AlpcRegisterCompletionList
AlpcRegisterCompletionListWorkerThread
AlpcRundownCompletionList
AlpcUnregisterCompletionList
AlpcUnregisterCompletionListWorkerThread
ApiSetQueryApiSetPresence
ApiSetQueryApiSetPresenceEx
CsrAllocateCaptureBuffer
CsrAllocateMessagePointer
CsrCaptureMessageBuffer
CsrCaptureMessageMultiUnicodeStringsInPlace
CsrCaptureMessageString
CsrCaptureTimeout
CsrClientCallServer
CsrClientConnectToServer
CsrFreeCaptureBuffer
CsrGetProcessId
CsrIdentifyAlertableThread
#if defined(DEF_I386) || defined(DEF_X64)
CsrNewThread
CsrProbeForRead
CsrProbeForWrite
#endif
CsrSetPriorityClass
CsrVerifyRegion
DbgBreakPoint
DbgPrint
DbgPrintEx
DbgPrintReturnControlC
DbgPrompt
DbgQueryDebugFilterState
DbgSetDebugFilterState
DbgUiConnectToDbg
DbgUiContinue
DbgUiConvertStateChangeStructure
DbgUiConvertStateChangeStructureEx
DbgUiDebugActiveProcess
DbgUiGetThreadDebugObject
DbgUiIssueRemoteBreakin
DbgUiRemoteBreakin
DbgUiSetThreadDebugObject
DbgUiStopDebugging
DbgUiWaitStateChange
DbgUserBreakPoint
EtwCheckCoverage
#ifdef DEF_X64
EtwControlTraceA
EtwControlTraceW
#endif
EtwCreateTraceInstanceId
#ifdef DEF_X64
EtwEnableTrace
EtwEnumerateTraceGuids
EtwFlushTraceA
EtwFlushTraceW
#endif
EtwDeliverDataBlock
EtwEnumerateProcessRegGuids
EtwEventActivityIdControl
EtwEventEnabled
EtwEventProviderEnabled
EtwEventRegister
EtwEventSetInformation
EtwEventUnregister
EtwEventWrite
EtwEventWriteEndScenario
EtwEventWriteEx
EtwEventWriteFull
EtwEventWriteNoRegistration
EtwEventWriteStartScenario
EtwEventWriteString
EtwEventWriteTransfer
EtwGetTraceEnableFlags
EtwGetTraceEnableLevel
EtwGetTraceLoggerHandle
#ifdef DEF_X64
EtwNotificationRegistrationA
EtwNotificationRegistrationW
EtwQueryAllTracesA
EtwQueryAllTracesW
EtwQueryTraceA
EtwQueryTraceW
EtwReceiveNotificationsA
EtwReceiveNotificationsW
#endif
EtwLogTraceEvent
EtwNotificationRegister
EtwNotificationUnregister
EtwProcessPrivateLoggerRequest
EtwRegisterSecurityProvider
EtwRegisterTraceGuidsA
EtwRegisterTraceGuidsW
#ifdef DEF_X64
EtwStartTraceA
EtwStartTraceW
EtwStopTraceA
EtwStopTraceW
EtwTraceEvent
#endif
EtwReplyNotification
EtwSendNotification
EtwSetMark
EtwTraceEventInstance
EtwTraceMessage
EtwTraceMessageVa
EtwUnregisterTraceGuids
#ifdef DEF_X64
EtwUpdateTraceA
EtwUpdateTraceW
EtwpGetTraceBuffer
EtwpSetHWConfigFunction
#endif
EtwWriteUMSecurityEvent
EtwpCreateEtwThread
EtwpGetCpuSpeed
F_X64(EtwpNotificationThread)
EvtIntReportAuthzEventAndSourceAsync
EvtIntReportEventAndSourceAsync
#ifndef DEF_ARM32
ExpInterlockedPopEntrySListEnd
F_X64(ExpInterlockedPopEntrySListEnd16)
ExpInterlockedPopEntrySListFault
F_X64(ExpInterlockedPopEntrySListFault16)
ExpInterlockedPopEntrySListResume
F_X64(ExpInterlockedPopEntrySListResume16)
#endif
KiRaiseUserExceptionDispatcher
KiUserApcDispatcher
KiUserCallbackDispatcher
F_ARM_ANY(KiUserCallbackDispatcherReturn)
KiUserExceptionDispatcher
KiUserInvertedFunctionTable F_ARM_ANY(DATA)
F_X64(LdrAccessOutOfProcessResource)
LdrAccessResource
LdrAddDllDirectory
LdrAddLoadAsDataTable
LdrAddRefDll
F_X86_ANY(LdrAlternateResourcesEnabled)
LdrAppxHandleIntegrityFailure
LdrCallEnclave
LdrControlFlowGuardEnforced
LdrCreateEnclave
F_X64(LdrCreateOutOfProcessImage)
LdrDeleteEnclave
F_X64(LdrDestroyOutOfProcessImage)
LdrDisableThreadCalloutsForDll
LdrEnumResources
LdrEnumerateLoadedModules
LdrFastFailInLoaderCallout
F_X64(LdrFindCreateProcessManifest)
LdrFindEntryForAddress
LdrFindResourceDirectory_U
LdrFindResourceEx_U
LdrFindResource_U
LdrFlushAlternateResourceModules
LdrGetDllDirectory
LdrGetDllFullName
LdrGetDllHandle
LdrGetDllHandleByMapping
LdrGetDllHandleByName
LdrGetDllHandleEx
LdrGetDllPath
LdrGetFailureData
LdrGetFileNameFromLoadAsDataTable
F64(LdrGetKnownDllSectionHandle)
LdrGetProcedureAddress
LdrGetProcedureAddressEx
LdrGetProcedureAddressForCaller
F_X86_ANY(LdrHotPatchRoutine)
LdrInitShimEngineDynamic
LdrInitializeEnclave
LdrInitializeThunk
LdrIsModuleSxsRedirected
LdrLoadAlternateResourceModule
LdrLoadAlternateResourceModuleEx
LdrLoadDll
LdrLoadEnclaveModule
LdrLockLoaderLock
LdrOpenImageFileOptionsKey
F64(LdrProcessInitializationComplete)
LdrProcessRelocationBlock
LdrProcessRelocationBlockEx
LdrQueryImageFileExecutionOptions
LdrQueryImageFileExecutionOptionsEx
LdrQueryImageFileKeyOption
LdrQueryModuleServiceTags
LdrQueryOptionalDelayLoadedAPI
LdrQueryProcessModuleInformation
LdrRegisterDllNotification
LdrRemoveDllDirectory
LdrRemoveLoadAsDataTable
LdrResFindResource
LdrResFindResourceDirectory
LdrResGetRCConfig
LdrResRelease
LdrResSearchResource
LdrResolveDelayLoadedAPI
LdrResolveDelayLoadsFromDll
LdrRscIsTypeExist
LdrSetAppCompatDllRedirectionCallback
LdrSetDefaultDllDirectories
LdrSetDllDirectory
LdrSetDllManifestProber
LdrSetImplicitPathOptions
LdrSetMUICacheType
LdrShutdownProcess
LdrShutdownThread
LdrStandardizeSystemPath
LdrSystemDllInitBlock F_ARM_ANY(DATA)
LdrUnloadAlternateResourceModule
LdrUnloadAlternateResourceModuleEx
LdrUnloadDll
LdrUnlockLoaderLock
LdrUnregisterDllNotification
LdrUpdatePackageSearchPath
LdrVerifyImageMatchesChecksum
LdrVerifyImageMatchesChecksumEx
LdrpResGetMappingSize
LdrpResGetResourceDirectory
MD4Final
MD4Init
MD4Update
MD5Final
MD5Init
MD5Update
NlsAnsiCodePage DATA
NlsMbCodePageTag DATA
NlsMbOemCodePageTag DATA
NtAcceptConnectPort
NtAccessCheck
NtAccessCheckAndAuditAlarm
NtAccessCheckByType
NtAccessCheckByTypeAndAuditAlarm
NtAccessCheckByTypeResultList
NtAccessCheckByTypeResultListAndAuditAlarm
NtAccessCheckByTypeResultListAndAuditAlarmByHandle
NtAcquireCrossVmMutant
NtAcquireProcessActivityReference
NtAddAtom
NtAddAtomEx
NtAddBootEntry
NtAddDriverEntry
NtAdjustGroupsToken
NtAdjustPrivilegesToken
NtAdjustTokenClaimsAndDeviceGroups
NtAlertResumeThread
NtAlertThread
NtAlertThreadByThreadId
NtAllocateLocallyUniqueId
NtAllocateReserveObject
NtAllocateUserPhysicalPages
F_X86_ANY(NtAllocateUserPhysicalPagesEx)
NtAllocateUuids
NtAllocateVirtualMemory
NtAllocateVirtualMemoryEx
NtAlpcAcceptConnectPort
NtAlpcCancelMessage
NtAlpcConnectPort
NtAlpcConnectPortEx
NtAlpcCreatePort
NtAlpcCreatePortSection
NtAlpcCreateResourceReserve
NtAlpcCreateSectionView
NtAlpcCreateSecurityContext
NtAlpcDeletePortSection
NtAlpcDeleteResourceReserve
NtAlpcDeleteSectionView
NtAlpcDeleteSecurityContext
NtAlpcDisconnectPort
NtAlpcImpersonateClientContainerOfPort
NtAlpcImpersonateClientOfPort
NtAlpcOpenSenderProcess
NtAlpcOpenSenderThread
NtAlpcQueryInformation
NtAlpcQueryInformationMessage
NtAlpcRevokeSecurityContext
NtAlpcSendWaitReceivePort
NtAlpcSetInformation
NtApphelpCacheControl
NtAreMappedFilesTheSame
NtAssignProcessToJobObject
NtAssociateWaitCompletionPacket
NtCallEnclave
NtCallbackReturn
F_X86_ANY(NtCancelDeviceWakeupRequest)
NtCancelIoFile
NtCancelIoFileEx
NtCancelSynchronousIoFile
NtCancelTimer
NtCancelTimer2
NtCancelWaitCompletionPacket
NtChangeProcessState
NtChangeThreadState
NtClearEvent
NtClose
NtCloseObjectAuditAlarm
NtCommitComplete
NtCommitEnlistment
NtCommitRegistryTransaction
NtCommitTransaction
NtCompactKeys
NtCompareObjects
NtCompareSigningLevels
NtCompareTokens
NtCompleteConnectPort
NtCompressKey
NtConnectPort
NtContinue
NtContinueEx
NtConvertBetweenAuxiliaryCounterAndPerformanceCounter
NtCreateCrossVmEvent
NtCreateCrossVmMutant
NtCreateDebugObject
NtCreateDirectoryObject
NtCreateDirectoryObjectEx
NtCreateEnclave
NtCreateEnlistment
NtCreateEvent
NtCreateEventPair
NtCreateFile
NtCreateIRTimer
NtCreateIoCompletion
NtCreateIoRing
NtCreateJobObject
NtCreateJobSet
NtCreateKey
NtCreateKeyTransacted
NtCreateKeyedEvent
NtCreateLowBoxToken
NtCreateMailslotFile
NtCreateMutant
NtCreateNamedPipeFile
NtCreatePagingFile
NtCreatePartition
NtCreatePort
NtCreatePrivateNamespace
NtCreateProcess
NtCreateProcessEx
NtCreateProcessStateChange
NtCreateProfile
NtCreateProfileEx
NtCreateRegistryTransaction
NtCreateResourceManager
NtCreateSection
NtCreateSectionEx
NtCreateSemaphore
NtCreateSymbolicLinkObject
NtCreateThread
NtCreateThreadEx
NtCreateThreadStateChange
NtCreateTimer
NtCreateTimer2
NtCreateToken
NtCreateTokenEx
NtCreateTransaction
NtCreateTransactionManager
NtCreateUserProcess
NtCreateWaitCompletionPacket
NtCreateWaitablePort
NtCreateWnfStateName
NtCreateWorkerFactory
NtDebugActiveProcess
NtDebugContinue
NtDelayExecution
NtDeleteAtom
NtDeleteBootEntry
NtDeleteDriverEntry
NtDeleteFile
NtDeleteKey
NtDeleteObjectAuditAlarm
NtDeletePrivateNamespace
NtDeleteValueKey
NtDeleteWnfStateData
NtDeleteWnfStateName
NtDeviceIoControlFile
NtDirectGraphicsCall
NtDisableLastKnownGood
NtDisplayString
NtDrawText
NtDuplicateObject
NtDuplicateToken
NtEnableLastKnownGood
NtEnumerateBootEntries
NtEnumerateDriverEntries
NtEnumerateKey
NtEnumerateSystemEnvironmentValuesEx
NtEnumerateTransactionObject
NtEnumerateValueKey
NtExtendSection
NtFilterBootOption
NtFilterToken
NtFilterTokenEx
NtFindAtom
NtFlushBuffersFile
NtFlushBuffersFileEx
NtFlushInstallUILanguage
NtFlushInstructionCache
NtFlushKey
NtFlushProcessWriteBuffers
NtFlushVirtualMemory
NtFlushWriteBuffer
NtFreeUserPhysicalPages
NtFreeVirtualMemory
NtFreezeRegistry
NtFreezeTransactions
NtFsControlFile
NtGetCachedSigningLevel
NtGetCompleteWnfStateSubscription
NtGetContextThread
NtGetCurrentProcessorNumber
NtGetCurrentProcessorNumberEx
NtGetDevicePowerState
NtGetMUIRegistryInfo
NtGetNextProcess
NtGetNextThread
NtGetNlsSectionPtr
NtGetNotificationResourceManager
F_X86_ANY(NtGetPlugPlayEvent)
NtGetTickCount
NtGetWriteWatch
NtImpersonateAnonymousToken
NtImpersonateClientOfPort
NtImpersonateThread
NtInitializeEnclave
NtInitializeNlsFiles
NtInitializeRegistry
NtInitiatePowerAction
NtIsProcessInJob
NtIsSystemResumeAutomatic
NtIsUILanguageComitted
NtListenPort
NtLoadDriver
NtLoadEnclaveData
NtLoadKey
NtLoadKey2
F_ARM_ANY(NtLoadKey3)
NtLoadKeyEx
NtLockFile
NtLockProductActivationKeys
NtLockRegistryKey
NtLockVirtualMemory
NtMakePermanentObject
NtMakeTemporaryObject
NtManageHotPatch
NtManagePartition
NtMapCMFModule
NtMapUserPhysicalPages
NtMapUserPhysicalPagesScatter
NtMapViewOfSection
NtMapViewOfSectionEx
NtModifyBootEntry
NtModifyDriverEntry
NtNotifyChangeDirectoryFile
NtNotifyChangeDirectoryFileEx
NtNotifyChangeKey
NtNotifyChangeMultipleKeys
NtNotifyChangeSession
NtOpenDirectoryObject
NtOpenEnlistment
NtOpenEvent
NtOpenEventPair
NtOpenFile
NtOpenIoCompletion
NtOpenJobObject
NtOpenKey
NtOpenKeyEx
NtOpenKeyTransacted
NtOpenKeyTransactedEx
NtOpenKeyedEvent
NtOpenMutant
NtOpenObjectAuditAlarm
NtOpenPartition
NtOpenPrivateNamespace
NtOpenProcess
NtOpenProcessToken
NtOpenProcessTokenEx
NtOpenRegistryTransaction
NtOpenResourceManager
NtOpenSection
NtOpenSemaphore
NtOpenSession
NtOpenSymbolicLinkObject
NtOpenThread
NtOpenThreadToken
NtOpenThreadTokenEx
NtOpenTimer
NtOpenTransaction
NtOpenTransactionManager
NtPlugPlayControl
NtPowerInformation
NtPrePrepareComplete
NtPrePrepareEnlistment
NtPrepareComplete
NtPrepareEnlistment
NtPrivilegeCheck
NtPrivilegeObjectAuditAlarm
NtPrivilegedServiceAuditAlarm
NtPropagationComplete
NtPropagationFailed
NtProtectVirtualMemory
F_X86_ANY(NtPssCaptureVaSpaceBulk)
NtPulseEvent
NtQueryAttributesFile
NtQueryAuxiliaryCounterFrequency
NtQueryBootEntryOrder
NtQueryBootOptions
NtQueryDebugFilterState
NtQueryDefaultLocale
NtQueryDefaultUILanguage
NtQueryDirectoryFile
NtQueryDirectoryFileEx
NtQueryDirectoryObject
NtQueryDriverEntryOrder
NtQueryEaFile
NtQueryEvent
NtQueryFullAttributesFile
NtQueryInformationAtom
NtQueryInformationByName
NtQueryInformationEnlistment
NtQueryInformationFile
NtQueryInformationJobObject
NtQueryInformationPort
NtQueryInformationProcess
NtQueryInformationResourceManager
NtQueryInformationThread
NtQueryInformationToken
NtQueryInformationTransaction
NtQueryInformationTransactionManager
NtQueryInformationWorkerFactory
NtQueryInstallUILanguage
NtQueryIntervalProfile
NtQueryIoCompletion
NtQueryIoRingCapabilities
NtQueryKey
NtQueryLicenseValue
NtQueryMultipleValueKey
NtQueryMutant
NtQueryObject
NtQueryOpenSubKeys
NtQueryOpenSubKeysEx
NtQueryPerformanceCounter
NtQueryPortInformationProcess
NtQueryQuotaInformationFile
NtQuerySection
NtQuerySecurityAttributesToken
NtQuerySecurityObject
NtQuerySecurityPolicy
NtQuerySemaphore
NtQuerySymbolicLinkObject
NtQuerySystemEnvironmentValue
NtQuerySystemEnvironmentValueEx
NtQuerySystemInformation
NtQuerySystemInformationEx
NtQuerySystemTime
NtQueryTimer
NtQueryTimerResolution
NtQueryValueKey
NtQueryVirtualMemory
NtQueryVolumeInformationFile
NtQueryWnfStateData
NtQueryWnfStateNameInformation
NtQueueApcThread
NtQueueApcThreadEx
NtQueueApcThreadEx2
NtRaiseException
NtRaiseHardError
NtReadFile
NtReadFileScatter
NtReadOnlyEnlistment
NtReadRequestData
NtReadVirtualMemory
NtReadVirtualMemoryEx
NtRecoverEnlistment
NtRecoverResourceManager
NtRecoverTransactionManager
NtRegisterProtocolAddressInformation
NtRegisterThreadTerminatePort
NtReleaseKeyedEvent
NtReleaseMutant
NtReleaseSemaphore
NtReleaseWorkerFactoryWorker
NtRemoveIoCompletion
NtRemoveIoCompletionEx
NtRemoveProcessDebug
NtRenameKey
NtRenameTransactionManager
NtReplaceKey
NtReplacePartitionUnit
NtReplyPort
NtReplyWaitReceivePort
NtReplyWaitReceivePortEx
NtReplyWaitReplyPort
F_X86_ANY(NtRequestDeviceWakeup)
NtRequestPort
NtRequestWaitReplyPort
F_X86_ANY(NtRequestWakeupLatency)
NtResetEvent
NtResetWriteWatch
NtRestoreKey
NtResumeProcess
NtResumeThread
NtRevertContainerImpersonation
NtRollbackComplete
NtRollbackEnlistment
NtRollbackRegistryTransaction
NtRollbackTransaction
NtRollforwardTransactionManager
NtSaveKey
NtSaveKeyEx
NtSaveMergedKeys
NtSecureConnectPort
NtSerializeBoot
NtSetBootEntryOrder
NtSetBootOptions
NtSetCachedSigningLevel
NtSetCachedSigningLevel2
NtSetContextThread
NtSetDebugFilterState
NtSetDefaultHardErrorPort
NtSetDefaultLocale
NtSetDefaultUILanguage
NtSetDriverEntryOrder
NtSetEaFile
NtSetEvent
NtSetEventBoostPriority
NtSetHighEventPair
NtSetHighWaitLowEventPair
NtSetIRTimer
NtSetInformationDebugObject
NtSetInformationEnlistment
NtSetInformationFile
NtSetInformationIoRing
NtSetInformationJobObject
NtSetInformationKey
NtSetInformationObject
NtSetInformationProcess
NtSetInformationResourceManager
NtSetInformationSymbolicLink
NtSetInformationThread
NtSetInformationToken
NtSetInformationTransaction
NtSetInformationTransactionManager
NtSetInformationVirtualMemory
NtSetInformationWorkerFactory
NtSetIntervalProfile
NtSetIoCompletion
NtSetIoCompletionEx
NtSetLdtEntries
NtSetLowEventPair
NtSetLowWaitHighEventPair
NtSetQuotaInformationFile
NtSetSecurityObject
NtSetSystemEnvironmentValue
NtSetSystemEnvironmentValueEx
NtSetSystemInformation
NtSetSystemPowerState
NtSetSystemTime
NtSetThreadExecutionState
NtSetTimer
NtSetTimer2
NtSetTimerEx
NtSetTimerResolution
NtSetUuidSeed
NtSetValueKey
NtSetVolumeInformationFile
NtSetWnfProcessNotificationEvent
NtShutdownSystem
NtShutdownWorkerFactory
NtSignalAndWaitForSingleObject
NtSinglePhaseReject
NtStartProfile
NtStopProfile
NtSubmitIoRing
NtSubscribeWnfStateChange
NtSuspendProcess
NtSuspendThread
NtSystemDebugControl
NtTerminateEnclave
NtTerminateJobObject
NtTerminateProcess
NtTerminateThread
NtTestAlert
NtThawRegistry
NtThawTransactions
NtTraceControl
NtTraceEvent
NtTranslateFilePath
NtUmsThreadYield
NtUnloadDriver
NtUnloadKey
NtUnloadKey2
NtUnloadKeyEx
NtUnlockFile
NtUnlockVirtualMemory
NtUnmapViewOfSection
NtUnmapViewOfSectionEx
NtUnsubscribeWnfStateChange
NtUpdateWnfStateData
NtVdmControl
NtWaitForAlertByThreadId
NtWaitForDebugEvent
NtWaitForKeyedEvent
NtWaitForMultipleObjects
NtWaitForMultipleObjects32
NtWaitForSingleObject
NtWaitForWorkViaWorkerFactory
NtWaitHighEventPair
NtWaitLowEventPair
NtWorkerFactoryWorkerReady
#ifdef DEF_ARM32
NtWow64AllocateVirtualMemory64
NtWow64CallFunction64
NtWow64CsrAllocateCaptureBuffer
NtWow64CsrAllocateMessagePointer
NtWow64CsrCaptureMessageBuffer
NtWow64CsrCaptureMessageString
NtWow64CsrClientCallServer
NtWow64CsrClientConnectToServer
NtWow64CsrFreeCaptureBuffer
NtWow64CsrGetProcessId
NtWow64CsrIdentifyAlertableThread
NtWow64CsrVerifyRegion
NtWow64DebuggerCall
NtWow64GetCurrentProcessorNumberEx
NtWow64GetNativeSystemInformation
NtWow64IsProcessorFeaturePresent
NtWow64QueryInformationProcess64
NtWow64ReadVirtualMemory64
NtWow64WriteVirtualMemory64
#endif
NtWriteFile
NtWriteFileGather
NtWriteRequestData
NtWriteVirtualMemory
NtYieldExecution
NtdllDefWindowProc_A
NtdllDefWindowProc_W
NtdllDialogWndProc_A
NtdllDialogWndProc_W
PfxFindPrefix
PfxInitialize
PfxInsertPrefix
PfxRemovePrefix
PssNtCaptureSnapshot
PssNtDuplicateSnapshot
PssNtFreeRemoteSnapshot
PssNtFreeSnapshot
PssNtFreeWalkMarker
PssNtQuerySnapshot
PssNtValidateDescriptor
PssNtWalkSnapshot
F_ARM32(ReadTimeStampCounter)
RtlAbortRXact
RtlAbsoluteToSelfRelativeSD
RtlAcquirePebLock
RtlAcquirePrivilege
RtlAcquireReleaseSRWLockExclusive
RtlAcquireResourceExclusive
RtlAcquireResourceShared
RtlAcquireSRWLockExclusive
RtlAcquireSRWLockShared
RtlActivateActivationContext
RtlActivateActivationContextEx
RtlActivateActivationContextUnsafeFast
RtlAddAccessAllowedAce
RtlAddAccessAllowedAceEx
RtlAddAccessAllowedObjectAce
RtlAddAccessDeniedAce
RtlAddAccessDeniedAceEx
RtlAddAccessDeniedObjectAce
RtlAddAccessFilterAce
RtlAddAce
RtlAddActionToRXact
RtlAddAtomToAtomTable
RtlAddAttributeActionToRXact
RtlAddAuditAccessAce
RtlAddAuditAccessAceEx
RtlAddAuditAccessObjectAce
RtlAddCompoundAce
RtlAddFunctionTable
RtlAddGrowableFunctionTable
RtlAddIntegrityLabelToBoundaryDescriptor
RtlAddMandatoryAce
RtlAddProcessTrustLabelAce
RtlAddRefActivationContext
RtlAddRefMemoryStream
RtlAddResourceAttributeAce
RtlAddSIDToBoundaryDescriptor
RtlAddScopedPolicyIDAce
RtlAddVectoredContinueHandler
RtlAddVectoredExceptionHandler
RtlAddressInSectionTable
RtlAdjustPrivilege
RtlAllocateActivationContextStack
RtlAllocateAndInitializeSid
RtlAllocateAndInitializeSidEx
RtlAllocateHandle
RtlAllocateHeap
RtlAllocateMemoryBlockLookaside
RtlAllocateMemoryZone
RtlAllocateWnfSerializationGroup
RtlAnsiCharToUnicodeChar
RtlAnsiStringToUnicodeSize
RtlAnsiStringToUnicodeString
RtlAppendAsciizToString
RtlAppendPathElement
RtlAppendStringToString
RtlAppendUnicodeStringToString
RtlAppendUnicodeToString
RtlApplicationVerifierStop
RtlApplyRXact
RtlApplyRXactNoFlush
RtlAppxIsFileOwnedByTrustedInstaller
RtlAreAllAccessesGranted
RtlAreAnyAccessesGranted
RtlAreBitsClear
F_X64(RtlAreBitsClearEx)
RtlAreBitsSet
RtlAreLongPathsEnabled
RtlAssert
RtlAvlInsertNodeEx
RtlAvlRemoveNode
RtlBarrier
RtlBarrierForDelete
F_X64(RtlCallEnclaveReturn)
RtlCancelTimer
RtlCanonicalizeDomainName
RtlCapabilityCheck
RtlCapabilityCheckForSingleSessionSku
RtlCaptureContext
RtlCaptureContext2
RtlCaptureStackBackTrace
RtlCharToInteger
RtlCheckBootStatusIntegrity
RtlCheckForOrphanedCriticalSections
RtlCheckPortableOperatingSystem
F_X64(RtlCheckProcessParameters)
RtlCheckRegistryKey
RtlCheckSandboxedToken
RtlCheckSystemBootStatusIntegrity
RtlCheckTokenCapability
RtlCheckTokenMembership
RtlCheckTokenMembershipEx
RtlCleanUpTEBLangLists
RtlClearAllBits
F_X64(RtlClearAllBitsEx)
RtlClearBit
F_X64(RtlClearBitEx)
RtlClearBits
F_X64(RtlClearBitsEx)
RtlClearThreadWorkOnBehalfTicket
RtlCloneMemoryStream
RtlCloneUserProcess
RtlCmDecodeMemIoResource
RtlCmEncodeMemIoResource
RtlCommitDebugInfo
RtlCommitMemoryStream
RtlCompactHeap
RtlCompareAltitudes
RtlCompareExchangePointerMapping
RtlCompareExchangePropertyStore
RtlCompareMemory
RtlCompareMemoryUlong
RtlCompareString
RtlCompareUnicodeString
RtlCompareUnicodeStrings
F64(RtlCompleteProcessCloning)
RtlCompressBuffer
RtlComputeCrc32
RtlComputeImportTableHash
RtlComputePrivatizedDllName_U
RtlConnectToSm
RtlConsoleMultiByteToUnicodeN
RtlConstructCrossVmEventPath
RtlConstructCrossVmMutexPath
RtlContractHashTable
RtlConvertDeviceFamilyInfoToString
RtlConvertExclusiveToShared
RtlConvertLCIDToString
RtlConvertSRWLockExclusiveToShared
RtlConvertSharedToExclusive
RtlConvertSidToUnicodeString
RtlConvertToAutoInheritSecurityObject
F_X86_ANY(RtlConvertUiListToApiList)
RtlCopyBitMap
RtlCopyContext
RtlCopyExtendedContext
RtlCopyLuid
RtlCopyLuidAndAttributesArray
RtlCopyMappedMemory
RtlCopyMemory
F_X64(RtlCopyMemoryNonTemporal)
RtlCopyMemoryStreamTo
RtlCopyOutOfProcessMemoryStreamTo
RtlCopySecurityDescriptor
RtlCopySid
RtlCopySidAndAttributesArray
RtlCopyString
RtlCopyUnicodeString
RtlCrc32
RtlCrc64
RtlCreateAcl
RtlCreateActivationContext
RtlCreateAndSetSD
RtlCreateAtomTable
RtlCreateBootStatusDataFile
RtlCreateBoundaryDescriptor
RtlCreateEnvironment
RtlCreateEnvironmentEx
RtlCreateHashTable
RtlCreateHashTableEx
RtlCreateHeap
RtlCreateMemoryBlockLookaside
RtlCreateMemoryZone
RtlCreateProcessParameters
RtlCreateProcessParametersEx
RtlCreateProcessParametersWithTemplate
RtlCreateProcessReflection
RtlCreateQueryDebugBuffer
RtlCreateRegistryKey
RtlCreateSecurityDescriptor
RtlCreateServiceSid
RtlCreateSystemVolumeInformationFolder
RtlCreateTagHeap
RtlCreateTimer
RtlCreateTimerQueue
#ifdef DEF_X64
RtlCreateUmsCompletionList
RtlCreateUmsThread
RtlCreateUmsThreadContext
#endif
RtlCreateUnicodeString
RtlCreateUnicodeStringFromAsciiz
F_X64(RtlCreateUserFiberShadowStack)
RtlCreateUserProcess
RtlCreateUserProcessEx
RtlCreateUserSecurityObject
RtlCreateUserStack
RtlCreateUserThread
RtlCreateVirtualAccountSid
RtlCultureNameToLCID
RtlCustomCPToUnicodeN
RtlCutoverTimeToSystemTime
RtlDeCommitDebugInfo
RtlDeNormalizeProcessParams
RtlDeactivateActivationContext
RtlDeactivateActivationContextUnsafeFast
RtlDebugPrintTimes
RtlDecodePointer
RtlDecodeRemotePointer
RtlDecodeSystemPointer
RtlDecompressBuffer
RtlDecompressBufferEx
RtlDecompressFragment
RtlDefaultNpAcl
RtlDelayExecution
RtlDelete
RtlDeleteAce
RtlDeleteAtomFromAtomTable
RtlDeleteBarrier
RtlDeleteBoundaryDescriptor
RtlDeleteCriticalSection
RtlDeleteElementGenericTable
RtlDeleteElementGenericTableAvl
RtlDeleteElementGenericTableAvlEx
RtlDeleteFunctionTable
RtlDeleteGrowableFunctionTable
RtlDeleteHashTable
RtlDeleteNoSplay
RtlDeleteRegistryValue
RtlDeleteResource
RtlDeleteSecurityObject
RtlDeleteTimer
RtlDeleteTimerQueue
RtlDeleteTimerQueueEx
#ifdef DEF_X64
RtlDeleteUmsCompletionList
RtlDeleteUmsThreadContext
RtlDequeueUmsCompletionListItems
#endif
RtlDeregisterSecureMemoryCacheCallback
RtlDeregisterWait
RtlDeregisterWaitEx
RtlDeriveCapabilitySidsFromName
RtlDestroyAtomTable
RtlDestroyEnvironment
RtlDestroyHandleTable
RtlDestroyHeap
RtlDestroyMemoryBlockLookaside
RtlDestroyMemoryZone
RtlDestroyProcessParameters
RtlDestroyQueryDebugBuffer
RtlDetectHeapLeaks
RtlDetermineDosPathNameType_U
RtlDisableThreadProfiling
RtlDllShutdownInProgress
RtlDnsHostNameToComputerName
RtlDoesFileExists_U
RtlDoesNameContainWildCards
RtlDosApplyFileIsolationRedirection_Ustr
RtlDosLongPathNameToNtPathName_U_WithStatus
RtlDosLongPathNameToRelativeNtPathName_U_WithStatus
RtlDosPathNameToNtPathName_U
RtlDosPathNameToNtPathName_U_WithStatus
RtlDosPathNameToRelativeNtPathName_U
RtlDosPathNameToRelativeNtPathName_U_WithStatus
RtlDosSearchPath_U
RtlDosSearchPath_Ustr
RtlDowncaseUnicodeChar
RtlDowncaseUnicodeString
F64(RtlDrainNonVolatileFlush)
RtlDumpResource
RtlDuplicateUnicodeString
RtlEmptyAtomTable
RtlEnableEarlyCriticalSectionEventCreation
RtlEnableThreadProfiling
F_X64(RtlEnclaveCallDispatch)
F_X64(RtlEnclaveCallDispatchReturn)
RtlEncodePointer
RtlEncodeRemotePointer
RtlEncodeSystemPointer
RtlEndEnumerationHashTable
RtlEndStrongEnumerationHashTable
RtlEndWeakEnumerationHashTable
RtlEnterCriticalSection
F_X64(RtlEnterUmsSchedulingMode)
RtlEnumProcessHeaps
RtlEnumerateEntryHashTable
RtlEnumerateGenericTable
RtlEnumerateGenericTableAvl
RtlEnumerateGenericTableLikeADirectory
RtlEnumerateGenericTableWithoutSplaying
RtlEnumerateGenericTableWithoutSplayingAvl
RtlEqualComputerName
RtlEqualDomainName
RtlEqualLuid
RtlEqualPrefixSid
RtlEqualSid
RtlEqualString
RtlEqualUnicodeString
RtlEqualWnfChangeStamps
RtlEraseUnicodeString
RtlEthernetAddressToStringA
RtlEthernetAddressToStringW
RtlEthernetStringToAddressA
RtlEthernetStringToAddressW
F_X64(RtlExecuteUmsThread)
RtlExitUserProcess
RtlExitUserThread
RtlExpandEnvironmentStrings
RtlExpandEnvironmentStrings_U
F_X86_ANY(RtlExtendHeap)
RtlExpandHashTable
RtlExtendCorrelationVector
RtlExtendMemoryBlockLookaside
RtlExtendMemoryZone
F_ARM32(RtlExtendedMagicDivide)
RtlExtractBitMap
RtlFillMemory
F_X64(RtlFillMemoryNonTemporal)
F_ARM_ANY(RtlFillMemoryUlong)
F_ARM_ANY(RtlFillMemoryUlonglong)
F64(RtlFillNonVolatileMemory)
RtlFinalReleaseOutOfProcessMemoryStream
RtlFindAceByType
RtlFindActivationContextSectionGuid
RtlFindActivationContextSectionString
RtlFindCharInUnicodeString
RtlFindClearBits
RtlFindClearBitsAndSet
RtlFindClearBitsAndSetEx
F_X64(RtlFindClearBitsEx)
RtlFindClearRuns
RtlFindClosestEncodableLength
RtlFindExportedRoutineByName
RtlFindLastBackwardRunClear
RtlFindLeastSignificantBit
RtlFindLongestRunClear
RtlFindMessage
RtlFindMostSignificantBit
RtlFindNextForwardRunClear
RtlFindSetBits
RtlFindSetBitsAndClear
F_X64(RtlFindSetBitsAndClearEx)
F_X64(RtlFindSetBitsEx)
RtlFindUnicodeSubstring
RtlFirstEntrySList
RtlFirstFreeAce
RtlFlsAlloc
RtlFlsFree
RtlFlsGetValue
RtlFlsSetValue
RtlFlushHeaps
F64(RtlFlushNonVolatileMemory)
F64(RtlFlushNonVolatileMemoryRanges)
RtlFlushSecureMemoryCache
RtlFormatCurrentUserKeyPath
RtlFormatMessage
RtlFormatMessageEx
RtlFreeActivationContextStack
RtlFreeAnsiString
RtlFreeHandle
RtlFreeHeap
RtlFreeMemoryBlockLookaside
F64(RtlFreeNonVolatileToken)
RtlFreeOemString
RtlFreeSid
RtlFreeThreadActivationContextStack
F_X86_ANY(RtlFreeUTF8String)
RtlFreeUnicodeString
F_X64(RtlFreeUserFiberShadowStack)
F_X86_ANY(RtlFreeUserThreadStack)
RtlFreeUserStack
RtlGUIDFromString
RtlGenerate8dot3Name
RtlGetAce
RtlGetActiveActivationContext
RtlGetActiveConsoleId
RtlGetAppContainerNamedObjectPath
RtlGetAppContainerParent
RtlGetAppContainerSidType
RtlGetCallersAddress
RtlGetCompressionWorkSpaceSize
RtlGetConsoleSessionForegroundProcessId
RtlGetControlSecurityDescriptor
RtlGetCriticalSectionRecursionCount
RtlGetCurrentDirectory_U
RtlGetCurrentPeb
RtlGetCurrentProcessorNumber
RtlGetCurrentProcessorNumberEx
RtlGetCurrentServiceSessionId
RtlGetCurrentTransaction
F_X64(RtlGetCurrentUmsThread)
RtlGetDaclSecurityDescriptor
RtlGetDeviceFamilyInfoEnum
RtlGetElementGenericTable
RtlGetElementGenericTableAvl
RtlGetEnabledExtendedFeatures
RtlGetExePath
RtlGetExtendedContextLength
RtlGetExtendedContextLength2
RtlGetExtendedFeaturesMask
RtlGetFileMUIPath
RtlGetFrame
RtlGetFullPathName_U
RtlGetFullPathName_UEx
RtlGetFullPathName_UstrEx
RtlGetFunctionTableListHead
RtlGetGroupSecurityDescriptor
RtlGetImageFileMachines
RtlGetIntegerAtom
RtlGetInterruptTimePrecise
RtlGetLastNtStatus
RtlGetLastWin32Error
RtlGetLengthWithoutLastFullDosOrNtPathElement
RtlGetLengthWithoutTrailingPathSeperators
RtlGetLocaleFileMappingAddress
RtlGetLongestNtPathLength
RtlGetMultiTimePrecise
RtlGetNativeSystemInformation
RtlGetNextEntryHashTable
F_X64(RtlGetNextUmsListItem)
F64(RtlGetNonVolatileToken)
RtlGetNtGlobalFlags
RtlGetNtProductType
RtlGetNtSystemRoot
RtlGetNtVersionNumbers
RtlGetOwnerSecurityDescriptor
RtlGetParentLocaleName
RtlGetPersistedStateLocation
RtlGetProcessHeaps
RtlGetProcessPreferredUILanguages
RtlGetProductInfo
RtlGetReturnAddressHijackTarget
RtlGetSaclSecurityDescriptor
RtlGetSearchPath
RtlGetSecurityDescriptorRMControl
RtlGetSessionProperties
RtlGetSetBootStatusData
RtlGetSuiteMask
RtlGetSystemBootStatus
RtlGetSystemBootStatusEx
RtlGetSystemGlobalData
RtlGetSystemPreferredUILanguages
RtlGetSystemTimeAndBias
RtlGetSystemTimePrecise
RtlGetThreadErrorMode
RtlGetThreadLangIdByIndex
RtlGetThreadPreferredUILanguages
RtlGetThreadWorkOnBehalfTicket
RtlGetTokenNamedObjectPath
RtlGetUILanguageInfo
F_X64(RtlGetUmsCompletionListEvent)
RtlGetUnloadEventTrace
RtlGetUnloadEventTraceEx
RtlGetUserInfoHeap
RtlGetUserPreferredUILanguages
RtlGetVersion
RtlGrowFunctionTable
RtlGuardCheckLongJumpTarget
RtlHashUnicodeString
RtlHeapTrkInitialize
RtlIdentifierAuthoritySid
RtlIdnToAscii
RtlIdnToNameprepUnicode
RtlIdnToUnicode
RtlImageDirectoryEntryToData
RtlImageNtHeader
RtlImageNtHeaderEx
RtlImageRvaToSection
RtlImageRvaToVa
RtlImpersonateSelf
RtlImpersonateSelfEx
RtlIncrementCorrelationVector
RtlInitAnsiString
RtlInitAnsiStringEx
RtlInitBarrier
RtlInitCodePageTable
RtlInitEnumerationHashTable
RtlInitMemoryStream
RtlInitNlsTables
RtlInitOutOfProcessMemoryStream
RtlInitString
RtlInitStringEx
RtlInitStrongEnumerationHashTable
F_X86_ANY(RtlInitUTF8String)
F_X86_ANY(RtlInitUTF8StringEx)
RtlInitUnicodeString
RtlInitUnicodeStringEx
RtlInitWeakEnumerationHashTable
RtlInitializeAtomPackage
RtlInitializeBitMap
F64(RtlInitializeBitMapEx)
RtlInitializeConditionVariable
RtlInitializeContext
RtlInitializeCorrelationVector
RtlInitializeCriticalSection
RtlInitializeCriticalSectionAndSpinCount
RtlInitializeCriticalSectionEx
RtlInitializeExtendedContext
RtlInitializeExtendedContext2
RtlInitializeGenericTable
RtlInitializeGenericTableAvl
RtlInitializeHandleTable
RtlInitializeNtUserPfn
RtlInitializeRXact
RtlInitializeResource
RtlInitializeSListHead
RtlInitializeSRWLock
RtlInitializeSid
RtlInitializeSidEx
RtlInsertElementGenericTable
RtlInsertElementGenericTableAvl
RtlInsertElementGenericTableFull
RtlInsertElementGenericTableFullAvl
RtlInsertEntryHashTable
RtlInstallFunctionTableCallback
RtlInt64ToUnicodeString
RtlIntegerToChar
RtlIntegerToUnicodeString
RtlInterlockedClearBitRun
RtlInterlockedFlushSList
RtlInterlockedPopEntrySList
RtlInterlockedPushEntrySList
RtlInterlockedPushListSList
RtlInterlockedPushListSListEx
RtlInterlockedSetBitRun
RtlIoDecodeMemIoResource
RtlIoEncodeMemIoResource
RtlIpv4AddressToStringA
RtlIpv4AddressToStringExA
RtlIpv4AddressToStringExW
RtlIpv4AddressToStringW
RtlIpv4StringToAddressA
RtlIpv4StringToAddressExA
RtlIpv4StringToAddressExW
RtlIpv4StringToAddressW
RtlIpv6AddressToStringA
RtlIpv6AddressToStringExA
RtlIpv6AddressToStringExW
RtlIpv6AddressToStringW
RtlIpv6StringToAddressA
RtlIpv6StringToAddressExA
RtlIpv6StringToAddressExW
RtlIpv6StringToAddressW
RtlIsActivationContextActive
RtlIsApiSetImplemented
RtlIsCapabilitySid
RtlIsCloudFilesPlaceholder
RtlIsCriticalSectionLocked
RtlIsCriticalSectionLockedByThread
RtlIsCurrentProcess
RtlIsCurrentThread
RtlIsCurrentThreadAttachExempt
RtlIsDosDeviceName_U
RtlIsEcCode
RtlIsElevatedRid
RtlIsEnclaveFeaturePresent
RtlIsGenericTableEmpty
RtlIsGenericTableEmptyAvl
RtlIsMultiSessionSku
RtlIsMultiUsersInSessionSku
RtlIsNameInExpression
RtlIsNameInUnUpcasedExpression
RtlIsNameLegalDOS8Dot3
RtlIsNonEmptyDirectoryReparsePointAllowed
RtlIsNormalizedString
RtlIsPackageSid
RtlIsParentOfChildAppContainer
RtlIsPartialPlaceholder
RtlIsPartialPlaceholderFileHandle
RtlIsPartialPlaceholderFileInfo
RtlIsProcessorFeaturePresent
RtlIsStateSeparationEnabled
RtlIsTextUnicode
RtlIsThreadWithinLoaderCallout
RtlIsUntrustedObject
RtlIsValidHandle
RtlIsValidIndexHandle
RtlIsValidLocaleName
RtlIsValidProcessTrustLabelSid
F_X86_ANY(RtlIsZeroMemory)
RtlKnownExceptionFilter
RtlLCIDToCultureName
RtlLargeIntegerToChar
RtlLcidToLocaleName
RtlLeaveCriticalSection
RtlLengthRequiredSid
RtlLengthSecurityDescriptor
RtlLengthSid
RtlLengthSidAsUnicodeString
RtlLoadString
RtlLocalTimeToSystemTime
RtlLocaleNameToLcid
RtlLocateExtendedFeature
RtlLocateExtendedFeature2
RtlLocateLegacyContext
RtlLockBootStatusData
RtlLockCurrentThread
RtlLockHeap
RtlLockMemoryBlockLookaside
RtlLockMemoryStreamRegion
RtlLockMemoryZone
RtlLockModuleSection
RtlLogStackBackTrace
RtlLookupAtomInAtomTable
RtlLookupElementGenericTable
RtlLookupElementGenericTableAvl
RtlLookupElementGenericTableFull
RtlLookupElementGenericTableFullAvl
RtlLookupEntryHashTable
RtlLookupFirstMatchingElementGenericTableAvl
RtlLookupFunctionEntry
RtlLookupFunctionTable
RtlMakeSelfRelativeSD
RtlMapGenericMask
RtlMapSecurityErrorToNtStatus
RtlMoveMemory
RtlMultiAppendUnicodeStringBuffer
RtlMultiByteToUnicodeN
RtlMultiByteToUnicodeSize
RtlMultipleAllocateHeap
RtlMultipleFreeHeap
RtlNewInstanceSecurityObject
RtlNewSecurityGrantedAccess
RtlNewSecurityObject
RtlNewSecurityObjectEx
RtlNewSecurityObjectWithMultipleInheritance
RtlNormalizeProcessParams
F_X86_ANY(RtlNormalizeSecurityDescriptor)
RtlNormalizeString
RtlNotifyFeatureUsage
RtlNtPathNameToDosPathName
RtlNtStatusToDosError
RtlNtStatusToDosErrorNoTeb
F64(RtlNtdllName DATA)
RtlNumberGenericTableElements
RtlNumberGenericTableElementsAvl
RtlNumberOfClearBits
F_X64(RtlNumberOfClearBitsEx)
RtlNumberOfClearBitsInRange
RtlNumberOfSetBits
F_X64(RtlNumberOfSetBitsEx)
RtlNumberOfSetBitsInRange
RtlNumberOfSetBitsUlongPtr
RtlOemStringToUnicodeSize
RtlOemStringToUnicodeString
RtlOemToUnicodeN
RtlOpenCrossProcessEmulatorWorkConnection
RtlOpenCurrentUser
RtlOsDeploymentState
RtlOwnerAcesPresent
RtlPcToFileHeader
RtlPinAtomInAtomTable
RtlPopFrame
RtlPrefixString
RtlPrefixUnicodeString
F64(RtlPrepareForProcessCloning)
RtlProcessFlsData
RtlProtectHeap
RtlPublishWnfStateData
RtlPushFrame
RtlQueryActivationContextApplicationSettings
RtlQueryAllFeatureConfigurations
RtlQueryAtomInAtomTable
RtlQueryCriticalSectionOwner
RtlQueryDepthSList
RtlQueryDynamicTimeZoneInformation
RtlQueryElevationFlags
RtlQueryEnvironmentVariable
RtlQueryEnvironmentVariable_U
RtlQueryFeatureConfiguration
RtlQueryFeatureConfigurationChangeStamp
RtlQueryFeatureUsageNotificationSubscriptions
RtlQueryHeapInformation
RtlQueryImageMitigationPolicy
RtlQueryInformationAcl
RtlQueryInformationActivationContext
RtlQueryInformationActiveActivationContext
RtlQueryInterfaceMemoryStream
RtlQueryModuleInformation
RtlQueryPackageClaims
RtlQueryPackageIdentity
RtlQueryPackageIdentityEx
RtlQueryPerformanceCounter
RtlQueryPerformanceFrequency
RtlQueryPointerMapping
RtlQueryProcessBackTraceInformation
RtlQueryProcessDebugInformation
RtlQueryProcessHeapInformation
RtlQueryProcessLockInformation
RtlQueryProcessPlaceholderCompatibilityMode
RtlQueryPropertyStore
RtlQueryProtectedPolicy
RtlQueryRegistryValueWithFallback
RtlQueryRegistryValues
RtlQueryRegistryValuesEx
RtlQueryResourcePolicy
RtlQuerySecurityObject
RtlQueryTagHeap
RtlQueryThreadPlaceholderCompatibilityMode
RtlQueryThreadProfiling
RtlQueryTimeZoneInformation
RtlQueryTokenHostIdAsUlong64
F_X64(RtlQueryUmsThreadInformation)
RtlQueryUnbiasedInterruptTime
RtlQueryValidationRunlevel
RtlQueryWnfMetaNotification
RtlQueryWnfStateData
RtlQueryWnfStateDataWithExplicitScope
RtlQueueApcWow64Thread
RtlQueueWorkItem
RtlRaiseCustomSystemEventTrigger
RtlRaiseException
RtlRaiseNoncontinuableException
RtlRaiseStatus
RtlRandom
RtlRandomEx
RtlRbInsertNodeEx
RtlRbRemoveNode
RtlReAllocateHeap
RtlReadMemoryStream
RtlReadOutOfProcessMemoryStream
RtlReadThreadProfilingData
RtlRealPredecessor
RtlRealSuccessor
RtlRegisterFeatureConfigurationChangeNotification
RtlRegisterForWnfMetaNotification
RtlRegisterSecureMemoryCacheCallback
RtlRegisterThreadWithCsrss
RtlRegisterWait
RtlReleaseActivationContext
RtlReleaseMemoryStream
RtlReleasePath
RtlReleasePebLock
RtlReleasePrivilege
RtlReleaseRelativeName
RtlReleaseResource
RtlReleaseSRWLockExclusive
RtlReleaseSRWLockShared
RtlRemoteCall
RtlRemoveEntryHashTable
RtlRemovePointerMapping
RtlRemovePrivileges
RtlRemovePropertyStore
RtlRemoveVectoredContinueHandler
RtlRemoveVectoredExceptionHandler
RtlReplaceSidInSd
RtlReplaceSystemDirectoryInPath
RtlReportException
RtlReportExceptionEx
RtlReportSilentProcessExit
RtlReportSqmEscalation
RtlResetMemoryBlockLookaside
RtlResetMemoryZone
RtlResetNtUserPfn
RtlResetRtlTranslations
RtlRestoreBootStatusDefaults
RtlRestoreContext
RtlRestoreLastWin32Error
RtlRestoreSystemBootStatusDefaults
F_X86_ANY(RtlRestoreThreadPreferredUILanguages)
RtlRetrieveNtUserPfn
RtlRevertMemoryStream
RtlRunDecodeUnicodeString
RtlRunEncodeUnicodeString
RtlRunOnceBeginInitialize
RtlRunOnceComplete
RtlRunOnceExecuteOnce
RtlRunOnceInitialize
RtlSecondsSince1970ToTime
RtlSecondsSince1980ToTime
RtlSeekMemoryStream
RtlSelfRelativeToAbsoluteSD
RtlSelfRelativeToAbsoluteSD2
RtlSendMsgToSm
RtlSetAllBits
F_X64(RtlSetAllBitsEx)
RtlSetAttributesSecurityDescriptor
RtlSetBit
F_X64(RtlSetBitEx)
RtlSetBits
F_X64(RtlSetBitsEx)
RtlSetControlSecurityDescriptor
RtlSetCriticalSectionSpinCount
RtlSetCurrentDirectory_U
RtlSetCurrentEnvironment
RtlSetCurrentTransaction
RtlSetDaclSecurityDescriptor
RtlSetDynamicTimeZoneInformation
RtlSetEnvironmentStrings
RtlSetEnvironmentVar
RtlSetEnvironmentVariable
RtlSetExtendedFeaturesMask
RtlSetFeatureConfigurations
RtlSetGroupSecurityDescriptor
RtlSetHeapInformation
RtlSetImageMitigationPolicy
RtlSetInformationAcl
RtlSetIoCompletionCallback
RtlSetLastWin32Error
RtlSetLastWin32ErrorAndNtStatusFromNtStatus
RtlSetMemoryStreamSize
RtlSetOwnerSecurityDescriptor
RtlSetPortableOperatingSystem
RtlSetProcessDebugInformation
RtlSetProcessIsCritical
RtlSetProcessPlaceholderCompatibilityMode
RtlSetProcessPreferredUILanguages
RtlSetProtectedPolicy
RtlSetProxiedProcessId
RtlSetSaclSecurityDescriptor
RtlSetSearchPathMode
RtlSetSecurityDescriptorRMControl
RtlSetSecurityObject
RtlSetSecurityObjectEx
RtlSetSystemBootStatus
RtlSetSystemBootStatusEx
RtlSetThreadErrorMode
RtlSetThreadIsCritical
RtlSetThreadPlaceholderCompatibilityMode
RtlSetThreadPoolStartFunc
RtlSetThreadPreferredUILanguages
F_X86_ANY(RtlSetThreadPreferredUILanguages2)
RtlSetThreadSubProcessTag
RtlSetThreadWorkOnBehalfTicket
RtlSetTimeZoneInformation
RtlSetTimer
F_X64(RtlSetUmsThreadInformation)
RtlSetUnhandledExceptionFilter
F_X64(RtlSetUnicodeCallouts)
F32(RtlSetUserCallbackExceptionFilter)
RtlSetUserFlagsHeap
RtlSetUserValueHeap
RtlSidDominates
RtlSidDominatesForTrust
RtlSidEqualLevel
RtlSidHashInitialize
RtlSidHashLookup
RtlSidIsHigherLevel
RtlSizeHeap
RtlSleepConditionVariableCS
RtlSleepConditionVariableSRW
RtlSplay
RtlStartRXact
RtlStatMemoryStream
RtlStringFromGUID
RtlStringFromGUIDEx
RtlStronglyEnumerateEntryHashTable
RtlSubAuthorityCountSid
RtlSubAuthoritySid
RtlSubscribeForFeatureUsageNotification
RtlSubscribeWnfStateChangeNotification
RtlSubtreePredecessor
RtlSubtreeSuccessor
RtlSwitchedVVI
RtlSystemTimeToLocalTime
RtlTestAndPublishWnfStateData
RtlTestBit
F64(RtlTestBitEx)
RtlTestProtectedAccess
RtlTimeFieldsToTime
RtlTimeToElapsedTimeFields
RtlTimeToSecondsSince1970
RtlTimeToSecondsSince1980
RtlTimeToTimeFields
RtlTraceDatabaseAdd
RtlTraceDatabaseCreate
RtlTraceDatabaseDestroy
RtlTraceDatabaseEnumerate
RtlTraceDatabaseFind
RtlTraceDatabaseLock
RtlTraceDatabaseUnlock
RtlTraceDatabaseValidate
RtlTryAcquirePebLock
RtlTryAcquireSRWLockExclusive
RtlTryAcquireSRWLockShared
RtlTryConvertSRWLockSharedToExclusiveOrRelease
RtlTryEnterCriticalSection
F_X86_ANY(RtlUTF8StringToUnicodeString)
RtlUTF8ToUnicodeN
RtlUdiv128
F_X64(RtlUmsThreadYield)
F_ARM_ANY(RtlUlongByteSwap)
F_ARM_ANY(RtlUlonglongByteSwap)
RtlUnhandledExceptionFilter
RtlUnhandledExceptionFilter2
RtlUnicodeStringToAnsiSize
RtlUnicodeStringToAnsiString
RtlUnicodeStringToCountedOemString
RtlUnicodeStringToInteger
RtlUnicodeStringToOemSize
RtlUnicodeStringToOemString
F_X86_ANY(RtlUnicodeStringToUTF8String)
RtlUnicodeToCustomCPN
RtlUnicodeToMultiByteN
RtlUnicodeToMultiByteSize
RtlUnicodeToOemN
RtlUnicodeToUTF8N
RtlUniform
RtlUnlockBootStatusData
RtlUnlockCurrentThread
RtlUnlockHeap
RtlUnlockMemoryBlockLookaside
RtlUnlockMemoryStreamRegion
RtlUnlockMemoryZone
RtlUnlockModuleSection
RtlUnregisterFeatureConfigurationChangeNotification
RtlUnsubscribeFromFeatureUsageNotifications
RtlUnsubscribeWnfNotificationWaitForCompletion
RtlUnsubscribeWnfNotificationWithCompletionCallback
RtlUnsubscribeWnfStateChangeNotification
RtlUnwind
RtlUnwindEx
RtlUpcaseUnicodeChar
RtlUpcaseUnicodeString
RtlUpcaseUnicodeStringToAnsiString
RtlUpcaseUnicodeStringToCountedOemString
RtlUpcaseUnicodeStringToOemString
RtlUpcaseUnicodeToCustomCPN
RtlUpcaseUnicodeToMultiByteN
RtlUpcaseUnicodeToOemN
RtlUpdateClonedCriticalSection
RtlUpdateClonedSRWLock
RtlUpdateTimer
RtlUpperChar
RtlUpperString
F_X86_ANY(RtlUsageHeap)
RtlUserFiberStart
RtlUserThreadStart
F_ARM_ANY(RtlUshortByteSwap)
RtlValidAcl
RtlValidProcessProtection
RtlValidRelativeSecurityDescriptor
RtlValidSecurityDescriptor
RtlValidSid
RtlValidateCorrelationVector
RtlValidateHeap
RtlValidateProcessHeaps
RtlValidateUnicodeString
RtlVerifyVersionInfo
RtlVirtualUnwind
RtlVirtualUnwind2
RtlWaitForWnfMetaNotification
RtlWaitOnAddress
RtlWakeAddressAll
RtlWakeAddressAllNoFence
RtlWakeAddressSingle
RtlWakeAddressSingleNoFence
RtlWakeAllConditionVariable
RtlWakeConditionVariable
RtlWalkFrameChain
RtlWalkHeap
RtlWeaklyEnumerateEntryHashTable
RtlWerpReportException
RtlWnfCompareChangeStamp
RtlWnfDllUnloadCallback
RtlWow64CallFunction64
RtlWow64ChangeProcessState
RtlWow64ChangeThreadState
RtlWow64EnableFsRedirection
RtlWow64EnableFsRedirectionEx
F64(RtlWow64GetCpuAreaInfo)
F64(RtlWow64GetCurrentCpuArea)
RtlWow64GetCurrentMachine
RtlWow64GetEquivalentMachineCHPE
RtlWow64GetProcessMachines
RtlWow64GetSharedInfoProcess
F64(RtlWow64GetThreadContext)
F64(RtlWow64GetThreadSelectorEntry)
RtlWow64IsWowGuestMachineSupported
RtlWow64LogMessageInEventLogger
#if defined(DEF_X64) || defined(DEF_ARM64)
RtlWow64PopAllCrossProcessWorkFromWorkList
RtlWow64PopCrossProcessWorkFromFreeList
RtlWow64PushCrossProcessWorkOntoFreeList
RtlWow64PushCrossProcessWorkOntoWorkList
RtlWow64RequestCrossProcessHeavyFlush
RtlWow64SetThreadContext
RtlWow64SuspendProcess
RtlWow64SuspendThread
#endif
RtlWriteMemoryStream
F64(RtlWriteNonVolatileMemory)
RtlWriteRegistryValue
RtlZeroHeap
RtlZeroMemory
RtlZombifyActivationContext
RtlpApplyLengthFunction
RtlpCheckDynamicTimeZoneInformation
RtlpCleanupRegistryKeys
RtlpConvertAbsoluteToRelativeSecurityAttribute
RtlpConvertCultureNamesToLCIDs
RtlpConvertLCIDsToCultureNames
RtlpConvertRelativeToAbsoluteSecurityAttribute
RtlpCreateProcessRegistryInfo
RtlpEnsureBufferSize
F_X64(RtlpExecuteUmsThread)
RtlpFreezeTimeBias F_ARM_ANY(DATA)
RtlpGetDeviceFamilyInfoEnum
RtlpGetLCIDFromLangInfoNode
RtlpGetNameFromLangInfoNode
RtlpGetSystemDefaultUILanguage
RtlpGetUserOrMachineUILanguage4NLS
RtlpInitializeLangRegistryInfo
RtlpIsQualifiedLanguage
RtlpLoadMachineUIByPolicy
RtlpLoadUserUIByPolicy
RtlpMergeSecurityAttributeInformation
RtlpMuiFreeLangRegistryInfo
RtlpMuiRegCreateRegistryInfo
RtlpMuiRegFreeRegistryInfo
RtlpMuiRegLoadRegistryInfo
RtlpNotOwnerCriticalSection
RtlpNtCreateKey
RtlpNtEnumerateSubKey
RtlpNtMakeTemporaryKey
RtlpNtOpenKey
RtlpNtQueryValueKey
RtlpNtSetValueKey
RtlpQueryDefaultUILanguage
F64(RtlpQueryProcessDebugInformationFromWow64)
RtlpQueryProcessDebugInformationRemote
RtlpRefreshCachedUILanguage
RtlpSetInstallLanguage
RtlpSetPreferredUILanguages
RtlpSetUserPreferredUILanguages
RtlpTimeFieldsToTime
RtlpTimeToTimeFields
F_X64(RtlpUmsExecuteYieldThreadEnd)
F_X64(RtlpUmsThreadYield)
RtlpUnWaitCriticalSection
RtlpVerifyAndCommitUILanguageSettings
RtlpWaitForCriticalSection
#ifdef DEF_X64
RtlpWow64CtxFromAmd64
RtlpWow64GetContextOnAmd64
RtlpWow64SetContextOnAmd64
#endif
F_ARM64(RtlpWow64CtxFromArm64)
RtlxAnsiStringToUnicodeSize
RtlxOemStringToUnicodeSize
RtlxUnicodeStringToAnsiSize
RtlxUnicodeStringToOemSize
SbExecuteProcedure
SbSelectProcedure
ShipAssert
ShipAssertGetBufferInfo
ShipAssertMsgA
ShipAssertMsgW
TpAllocAlpcCompletion
TpAllocAlpcCompletionEx
TpAllocCleanupGroup
TpAllocIoCompletion
TpAllocJobNotification
TpAllocPool
TpAllocTimer
TpAllocWait
TpAllocWork
TpAlpcRegisterCompletionList
TpAlpcUnregisterCompletionList
TpCallbackDetectedUnrecoverableError
TpCallbackIndependent
TpCallbackLeaveCriticalSectionOnCompletion
TpCallbackMayRunLong
TpCallbackReleaseMutexOnCompletion
TpCallbackReleaseSemaphoreOnCompletion
TpCallbackSendAlpcMessageOnCompletion
TpCallbackSendPendingAlpcMessage
TpCallbackSetEventOnCompletion
TpCallbackUnloadDllOnCompletion
TpCancelAsyncIoOperation
TpCaptureCaller
TpCheckTerminateWorker
TpDbgDumpHeapUsage
F_X86_ANY(TpDbgGetFreeInfo)
TpDbgSetLogRoutine
TpDisablePoolCallbackChecks
TpDisassociateCallback
TpIsTimerSet
F_X86_ANY(TpPoolFreeUnusedNodes)
TpPostWork
TpQueryPoolStackInformation
TpReleaseAlpcCompletion
TpReleaseCleanupGroup
TpReleaseCleanupGroupMembers
TpReleaseIoCompletion
TpReleaseJobNotification
TpReleasePool
TpReleaseTimer
TpReleaseWait
TpReleaseWork
TpSetDefaultPoolMaxThreads
TpSetDefaultPoolStackInformation
TpSetPoolMaxThreads
TpSetPoolMaxThreadsSoftLimit
TpSetPoolMinThreads
TpSetPoolStackInformation
TpSetPoolThreadBasePriority
TpSetPoolThreadCpuSets
TpSetPoolWorkerThreadIdleTimeout
TpSetTimer
TpSetTimerEx
TpSetWait
TpSetWaitEx
TpSimpleTryPost
TpStartAsyncIoOperation
TpTimerOutstandingCallbackCount
TpTrimPools
TpWaitForAlpcCompletion
TpWaitForIoCompletion
TpWaitForJobNotification
TpWaitForTimer
TpWaitForWait
TpWaitForWork
VerSetConditionMask
WerReportExceptionWorker
WerReportSQMEvent
WinSqmAddToAverageDWORD
WinSqmAddToStream
WinSqmAddToStreamEx
WinSqmCheckEscalationAddToStreamEx
WinSqmCheckEscalationSetDWORD
WinSqmCheckEscalationSetDWORD64
WinSqmCheckEscalationSetString
WinSqmCommonDatapointDelete
WinSqmCommonDatapointSetDWORD
WinSqmCommonDatapointSetDWORD64
WinSqmCommonDatapointSetStreamEx
WinSqmCommonDatapointSetString
WinSqmEndSession
WinSqmEventEnabled
WinSqmEventWrite
WinSqmGetEscalationRuleStatus
WinSqmGetInstrumentationProperty
WinSqmIncrementDWORD
WinSqmIsOptedIn
WinSqmIsOptedInEx
WinSqmIsSessionDisabled
WinSqmSetDWORD
WinSqmSetDWORD64
WinSqmSetEscalationInfo
WinSqmSetIfMaxDWORD
WinSqmSetIfMinDWORD
WinSqmSetString
WinSqmStartSession
WinSqmStartSessionForPartner
WinSqmStartSqmOptinListener
ZwAcceptConnectPort
ZwAccessCheck
ZwAccessCheckAndAuditAlarm
ZwAccessCheckByType
ZwAccessCheckByTypeAndAuditAlarm
ZwAccessCheckByTypeResultList
ZwAccessCheckByTypeResultListAndAuditAlarm
ZwAccessCheckByTypeResultListAndAuditAlarmByHandle
ZwAcquireCrossVmMutant
ZwAcquireProcessActivityReference
ZwAddAtom
ZwAddAtomEx
ZwAddBootEntry
ZwAddDriverEntry
ZwAdjustGroupsToken
ZwAdjustPrivilegesToken
ZwAdjustTokenClaimsAndDeviceGroups
ZwAlertResumeThread
ZwAlertThread
ZwAlertThreadByThreadId
ZwAllocateLocallyUniqueId
ZwAllocateReserveObject
ZwAllocateUserPhysicalPages
F_X86_ANY(ZwAllocateUserPhysicalPagesEx)
ZwAllocateUuids
ZwAllocateVirtualMemory
ZwAllocateVirtualMemoryEx
ZwAlpcAcceptConnectPort
ZwAlpcCancelMessage
ZwAlpcConnectPort
ZwAlpcConnectPortEx
ZwAlpcCreatePort
ZwAlpcCreatePortSection
ZwAlpcCreateResourceReserve
ZwAlpcCreateSectionView
ZwAlpcCreateSecurityContext
ZwAlpcDeletePortSection
ZwAlpcDeleteResourceReserve
ZwAlpcDeleteSectionView
ZwAlpcDeleteSecurityContext
ZwAlpcDisconnectPort
ZwAlpcImpersonateClientContainerOfPort
ZwAlpcImpersonateClientOfPort
ZwAlpcOpenSenderProcess
ZwAlpcOpenSenderThread
ZwAlpcQueryInformation
ZwAlpcQueryInformationMessage
ZwAlpcRevokeSecurityContext
ZwAlpcSendWaitReceivePort
ZwAlpcSetInformation
ZwApphelpCacheControl
ZwAreMappedFilesTheSame
ZwAssignProcessToJobObject
ZwAssociateWaitCompletionPacket
ZwCallEnclave
ZwCallbackReturn
F_X86_ANY(ZwCancelDeviceWakeupRequest)
ZwCancelIoFile
ZwCancelIoFileEx
ZwCancelSynchronousIoFile
ZwCancelTimer
ZwCancelTimer2
ZwCancelWaitCompletionPacket
ZwChangeProcessState
ZwChangeThreadState
ZwClearEvent
ZwClose
ZwCloseObjectAuditAlarm
ZwCommitComplete
ZwCommitEnlistment
ZwCommitRegistryTransaction
ZwCommitTransaction
ZwCompactKeys
ZwCompareObjects
ZwCompareSigningLevels
ZwCompareTokens
ZwCompleteConnectPort
ZwCompressKey
ZwConnectPort
ZwContinue
ZwContinueEx
ZwConvertBetweenAuxiliaryCounterAndPerformanceCounter
ZwCreateCrossVmEvent
ZwCreateCrossVmMutant
ZwCreateDebugObject
ZwCreateDirectoryObject
ZwCreateDirectoryObjectEx
ZwCreateEnclave
ZwCreateEnlistment
ZwCreateEvent
ZwCreateEventPair
ZwCreateFile
ZwCreateIRTimer
ZwCreateIoCompletion
ZwCreateIoRing
ZwCreateJobObject
ZwCreateJobSet
ZwCreateKey
ZwCreateKeyTransacted
ZwCreateKeyedEvent
ZwCreateLowBoxToken
ZwCreateMailslotFile
ZwCreateMutant
ZwCreateNamedPipeFile
ZwCreatePagingFile
ZwCreatePartition
ZwCreatePort
ZwCreatePrivateNamespace
ZwCreateProcess
ZwCreateProcessEx
ZwCreateProcessStateChange
ZwCreateProfile
ZwCreateProfileEx
ZwCreateRegistryTransaction
ZwCreateResourceManager
ZwCreateSection
ZwCreateSectionEx
ZwCreateSemaphore
ZwCreateSymbolicLinkObject
ZwCreateThread
ZwCreateThreadEx
ZwCreateThreadStateChange
ZwCreateTimer
ZwCreateTimer2
ZwCreateToken
ZwCreateTokenEx
ZwCreateTransaction
ZwCreateTransactionManager
ZwCreateUserProcess
ZwCreateWaitCompletionPacket
ZwCreateWaitablePort
ZwCreateWnfStateName
ZwCreateWorkerFactory
ZwDebugActiveProcess
ZwDebugContinue
ZwDelayExecution
ZwDeleteAtom
ZwDeleteBootEntry
ZwDeleteDriverEntry
ZwDeleteFile
ZwDeleteKey
ZwDeleteObjectAuditAlarm
ZwDeletePrivateNamespace
ZwDeleteValueKey
ZwDeleteWnfStateData
ZwDeleteWnfStateName
ZwDeviceIoControlFile
ZwDirectGraphicsCall
ZwDisableLastKnownGood
ZwDisplayString
ZwDrawText
ZwDuplicateObject
ZwDuplicateToken
ZwEnableLastKnownGood
ZwEnumerateBootEntries
ZwEnumerateDriverEntries
ZwEnumerateKey
ZwEnumerateSystemEnvironmentValuesEx
ZwEnumerateTransactionObject
ZwEnumerateValueKey
ZwExtendSection
ZwFilterBootOption
ZwFilterToken
ZwFilterTokenEx
ZwFindAtom
ZwFlushBuffersFile
ZwFlushBuffersFileEx
ZwFlushInstallUILanguage
ZwFlushInstructionCache
ZwFlushKey
ZwFlushProcessWriteBuffers
ZwFlushVirtualMemory
ZwFlushWriteBuffer
ZwFreeUserPhysicalPages
ZwFreeVirtualMemory
ZwFreezeRegistry
ZwFreezeTransactions
ZwFsControlFile
ZwGetCachedSigningLevel
ZwGetCompleteWnfStateSubscription
ZwGetContextThread
ZwGetCurrentProcessorNumber
ZwGetCurrentProcessorNumberEx
ZwGetDevicePowerState
ZwGetMUIRegistryInfo
ZwGetNextProcess
ZwGetNextThread
ZwGetNlsSectionPtr
ZwGetNotificationResourceManager
F_X86_ANY(ZwGetPlugPlayEvent)
ZwGetWriteWatch
ZwImpersonateAnonymousToken
ZwImpersonateClientOfPort
ZwImpersonateThread
ZwInitializeEnclave
ZwInitializeNlsFiles
ZwInitializeRegistry
ZwInitiatePowerAction
ZwIsProcessInJob
ZwIsSystemResumeAutomatic
ZwIsUILanguageComitted
ZwListenPort
ZwLoadDriver
ZwLoadEnclaveData
ZwLoadKey
ZwLoadKey2
F_ARM_ANY(ZwLoadKey3)
ZwLoadKeyEx
ZwLockFile
ZwLockProductActivationKeys
ZwLockRegistryKey
ZwLockVirtualMemory
ZwMakePermanentObject
ZwMakeTemporaryObject
ZwManageHotPatch
ZwManagePartition
ZwMapCMFModule
ZwMapUserPhysicalPages
ZwMapUserPhysicalPagesScatter
ZwMapViewOfSection
ZwMapViewOfSectionEx
ZwModifyBootEntry
ZwModifyDriverEntry
ZwNotifyChangeDirectoryFile
ZwNotifyChangeDirectoryFileEx
ZwNotifyChangeKey
ZwNotifyChangeMultipleKeys
ZwNotifyChangeSession
ZwOpenDirectoryObject
ZwOpenEnlistment
ZwOpenEvent
ZwOpenEventPair
ZwOpenFile
ZwOpenIoCompletion
ZwOpenJobObject
ZwOpenKey
ZwOpenKeyEx
ZwOpenKeyTransacted
ZwOpenKeyTransactedEx
ZwOpenKeyedEvent
ZwOpenMutant
ZwOpenObjectAuditAlarm
ZwOpenPartition
ZwOpenPrivateNamespace
ZwOpenProcess
ZwOpenProcessToken
ZwOpenProcessTokenEx
ZwOpenRegistryTransaction
ZwOpenResourceManager
ZwOpenSection
ZwOpenSemaphore
ZwOpenSession
ZwOpenSymbolicLinkObject
ZwOpenThread
ZwOpenThreadToken
ZwOpenThreadTokenEx
ZwOpenTimer
ZwOpenTransaction
ZwOpenTransactionManager
ZwPlugPlayControl
ZwPowerInformation
ZwPrePrepareComplete
ZwPrePrepareEnlistment
ZwPrepareComplete
ZwPrepareEnlistment
ZwPrivilegeCheck
ZwPrivilegeObjectAuditAlarm
ZwPrivilegedServiceAuditAlarm
ZwPropagationComplete
ZwPropagationFailed
ZwProtectVirtualMemory
F_X86_ANY(ZwPssCaptureVaSpaceBulk)
ZwPulseEvent
ZwQueryAttributesFile
ZwQueryAuxiliaryCounterFrequency
ZwQueryBootEntryOrder
ZwQueryBootOptions
ZwQueryDebugFilterState
ZwQueryDefaultLocale
ZwQueryDefaultUILanguage
ZwQueryDirectoryFile
ZwQueryDirectoryFileEx
ZwQueryDirectoryObject
ZwQueryDriverEntryOrder
ZwQueryEaFile
ZwQueryEvent
ZwQueryFullAttributesFile
ZwQueryInformationAtom
ZwQueryInformationByName
ZwQueryInformationEnlistment
ZwQueryInformationFile
ZwQueryInformationJobObject
ZwQueryInformationPort
ZwQueryInformationProcess
ZwQueryInformationResourceManager
ZwQueryInformationThread
ZwQueryInformationToken
ZwQueryInformationTransaction
ZwQueryInformationTransactionManager
ZwQueryInformationWorkerFactory
ZwQueryInstallUILanguage
ZwQueryIntervalProfile
ZwQueryIoCompletion
ZwQueryIoRingCapabilities
ZwQueryKey
ZwQueryLicenseValue
ZwQueryMultipleValueKey
ZwQueryMutant
ZwQueryObject
ZwQueryOpenSubKeys
ZwQueryOpenSubKeysEx
ZwQueryPerformanceCounter
ZwQueryPortInformationProcess
ZwQueryQuotaInformationFile
ZwQuerySection
ZwQuerySecurityAttributesToken
ZwQuerySecurityObject
ZwQuerySecurityPolicy
ZwQuerySemaphore
ZwQuerySymbolicLinkObject
ZwQuerySystemEnvironmentValue
ZwQuerySystemEnvironmentValueEx
ZwQuerySystemInformation
ZwQuerySystemInformationEx
ZwQuerySystemTime
ZwQueryTimer
ZwQueryTimerResolution
ZwQueryValueKey
ZwQueryVirtualMemory
ZwQueryVolumeInformationFile
ZwQueryWnfStateData
ZwQueryWnfStateNameInformation
ZwQueueApcThread
ZwQueueApcThreadEx
ZwQueueApcThreadEx2
ZwRaiseException
ZwRaiseHardError
ZwReadFile
ZwReadFileScatter
ZwReadOnlyEnlistment
ZwReadRequestData
ZwReadVirtualMemory
ZwReadVirtualMemoryEx
ZwRecoverEnlistment
ZwRecoverResourceManager
ZwRecoverTransactionManager
ZwRegisterProtocolAddressInformation
ZwRegisterThreadTerminatePort
ZwReleaseKeyedEvent
ZwReleaseMutant
ZwReleaseSemaphore
ZwReleaseWorkerFactoryWorker
ZwRemoveIoCompletion
ZwRemoveIoCompletionEx
ZwRemoveProcessDebug
ZwRenameKey
ZwRenameTransactionManager
ZwReplaceKey
ZwReplacePartitionUnit
ZwReplyPort
ZwReplyWaitReceivePort
ZwReplyWaitReceivePortEx
ZwReplyWaitReplyPort
F_X86_ANY(ZwRequestDeviceWakeup)
ZwRequestPort
ZwRequestWaitReplyPort
F_X86_ANY(ZwRequestWakeupLatency)
ZwResetEvent
ZwResetWriteWatch
ZwRestoreKey
ZwResumeProcess
ZwResumeThread
ZwRevertContainerImpersonation
ZwRollbackComplete
ZwRollbackEnlistment
ZwRollbackRegistryTransaction
ZwRollbackTransaction
ZwRollforwardTransactionManager
ZwSaveKey
ZwSaveKeyEx
ZwSaveMergedKeys
ZwSecureConnectPort
ZwSerializeBoot
ZwSetBootEntryOrder
ZwSetBootOptions
ZwSetCachedSigningLevel
ZwSetCachedSigningLevel2
ZwSetContextThread
ZwSetDebugFilterState
ZwSetDefaultHardErrorPort
ZwSetDefaultLocale
ZwSetDefaultUILanguage
ZwSetDriverEntryOrder
ZwSetEaFile
ZwSetEvent
ZwSetEventBoostPriority
ZwSetHighEventPair
ZwSetHighWaitLowEventPair
ZwSetIRTimer
ZwSetInformationDebugObject
ZwSetInformationEnlistment
ZwSetInformationFile
ZwSetInformationIoRing
ZwSetInformationJobObject
ZwSetInformationKey
ZwSetInformationObject
ZwSetInformationProcess
ZwSetInformationResourceManager
ZwSetInformationSymbolicLink
ZwSetInformationThread
ZwSetInformationToken
ZwSetInformationTransaction
ZwSetInformationTransactionManager
ZwSetInformationVirtualMemory
ZwSetInformationWorkerFactory
ZwSetIntervalProfile
ZwSetIoCompletion
ZwSetIoCompletionEx
ZwSetLdtEntries
ZwSetLowEventPair
ZwSetLowWaitHighEventPair
ZwSetQuotaInformationFile
ZwSetSecurityObject
ZwSetSystemEnvironmentValue
ZwSetSystemEnvironmentValueEx
ZwSetSystemInformation
ZwSetSystemPowerState
ZwSetSystemTime
ZwSetThreadExecutionState
ZwSetTimer
ZwSetTimer2
ZwSetTimerEx
ZwSetTimerResolution
ZwSetUuidSeed
ZwSetValueKey
ZwSetVolumeInformationFile
ZwSetWnfProcessNotificationEvent
ZwShutdownSystem
ZwShutdownWorkerFactory
ZwSignalAndWaitForSingleObject
ZwSinglePhaseReject
ZwStartProfile
ZwStopProfile
ZwSubmitIoRing
ZwSubscribeWnfStateChange
ZwSuspendProcess
ZwSuspendThread
ZwSystemDebugControl
ZwTerminateEnclave
ZwTerminateJobObject
ZwTerminateProcess
ZwTerminateThread
ZwTestAlert
ZwThawRegistry
ZwThawTransactions
ZwTraceControl
ZwTraceEvent
ZwTranslateFilePath
ZwUmsThreadYield
ZwUnloadDriver
ZwUnloadKey
ZwUnloadKey2
ZwUnloadKeyEx
ZwUnlockFile
ZwUnlockVirtualMemory
ZwUnmapViewOfSection
ZwUnmapViewOfSectionEx
ZwUnsubscribeWnfStateChange
ZwUpdateWnfStateData
ZwVdmControl
ZwWaitForAlertByThreadId
ZwWaitForDebugEvent
ZwWaitForKeyedEvent
ZwWaitForMultipleObjects
ZwWaitForMultipleObjects32
ZwWaitForSingleObject
ZwWaitForWorkViaWorkerFactory
ZwWaitHighEventPair
ZwWaitLowEventPair
ZwWorkerFactoryWorkerReady
#ifdef DEF_ARM32
ZwWow64AllocateVirtualMemory64
ZwWow64CallFunction64
ZwWow64CsrAllocateCaptureBuffer
ZwWow64CsrAllocateMessagePointer
ZwWow64CsrCaptureMessageBuffer
ZwWow64CsrCaptureMessageString
ZwWow64CsrClientCallServer
ZwWow64CsrClientConnectToServer
ZwWow64CsrFreeCaptureBuffer
ZwWow64CsrGetProcessId
ZwWow64CsrIdentifyAlertableThread
ZwWow64CsrVerifyRegion
ZwWow64DebuggerCall
ZwWow64GetCurrentProcessorNumberEx
ZwWow64GetNativeSystemInformation
ZwWow64IsProcessorFeaturePresent
ZwWow64QueryInformationProcess64
ZwWow64ReadVirtualMemory64
ZwWow64WriteVirtualMemory64
#endif
ZwWriteFile
ZwWriteFileGather
ZwWriteRequestData
ZwWriteVirtualMemory
ZwYieldExecution
vDbgPrintEx
vDbgPrintExWithPrefix
