;
; Definition file of pcwum.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "pcwum.dll"
EXPORTS
PcwAddQueryItem
PcwClearCounterSetSecurity
PcwCollectData
PcwCompleteNotification
PcwCreateNotifier
PcwCreateQuery
PcwDisconnectCounterSet
PcwEnumerateInstances
PcwIsNotifierAlive
PcwQueryCounterSetSecurity
PcwReadNotificationData
PcwRegisterCounterSet
PcwRemoveQueryItem
PcwSendNotification
PcwSendStatelessNotification
PcwSetCounterSetSecurity
PcwSetQueryItemUserData
PerfCreateInstance
PerfDecrementULongCounterValue
PerfDecrementULongLongCounterValue
PerfDeleteInstance
PerfIncrementULongCounterValue
PerfIncrementULongLongCounterValue
PerfQueryInstance
PerfSetCounterRefValue
PerfSetCounterSetInfo
PerfSetULongCounterValue
PerfSetULongLongCounterValue
PerfStartProvider
PerfStartProviderEx
PerfStopProvider
StmAlignSize
StmAllocateFlat
StmCoalesceChunks
StmDeinitialize
StmInitialize
StmReduceSize
StmReserve
StmWrite
