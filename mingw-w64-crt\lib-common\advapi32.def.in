#include "func.def.in"

LIBRARY "ADVAPI32.dll"
EXPORTS
ord_1000 @1000
I_ScGetCurrentGroupStateW
A_SHAFinal
A_SHAInit
A_SHAUpdate
AbortSystemShutdownA
AbortSystemShutdownW
AccessCheck
AccessCheckAndAuditAlarmA
AccessCheckAndAuditAlarmW
AccessCheckByType
AccessCheckByTypeAndAuditAlarmA
AccessCheckByTypeAndAuditAlarmW
AccessCheckByTypeResultList
AccessCheckByTypeResultListAndAuditAlarmA
AccessCheckByTypeResultListAndAuditAlarmByHandleA
AccessCheckByTypeResultListAndAuditAlarmByHandleW
AccessCheckByTypeResultListAndAuditAlarmW
AddAccessAllowedAce
AddAccessAllowedAceEx
AddAccessAllowedObjectAce
AddAccessDeniedAce
AddAccessDeniedAceEx
AddAccessDeniedObjectAce
AddAce
AddAuditAccessAce
AddAuditAccessAceEx
AddAuditAccessObjectAce
AddConditionalAce
AddMandatoryAce
AddUsersToEncryptedFile
AddUsersToEncryptedFileEx
AdjustTokenGroups
AdjustTokenPrivileges
AllocateAndInitializeSid
AllocateLocallyUniqueId
AreAllAccessesGranted
AreAnyAccessesGranted
AuditComputeEffectivePolicyBySid
AuditComputeEffectivePolicyByToken
AuditEnumerateCategories
AuditEnumeratePerUserPolicy
AuditEnumerateSubCategories
AuditFree
AuditLookupCategoryGuidFromCategoryId
AuditLookupCategoryIdFromCategoryGuid
AuditLookupCategoryNameA
AuditLookupCategoryNameW
AuditLookupSubCategoryNameA
AuditLookupSubCategoryNameW
AuditQueryGlobalSaclA
AuditQueryGlobalSaclW
AuditQueryPerUserPolicy
AuditQuerySecurity
AuditQuerySystemPolicy
AuditSetGlobalSaclA
AuditSetGlobalSaclW
AuditSetPerUserPolicy
AuditSetSecurity
AuditSetSystemPolicy
BackupEventLogA
BackupEventLogW
BaseRegCloseKey
BaseRegCreateKey
BaseRegDeleteKeyEx
BaseRegDeleteValue
BaseRegFlushKey
BaseRegGetVersion
BaseRegLoadKey
BaseRegOpenKey
BaseRegRestoreKey
BaseRegSaveKeyEx
BaseRegSetKeySecurity
BaseRegSetValue
BaseRegUnLoadKey
BuildExplicitAccessWithNameA
BuildExplicitAccessWithNameW
BuildImpersonateExplicitAccessWithNameA
BuildImpersonateExplicitAccessWithNameW
BuildImpersonateTrusteeA
BuildImpersonateTrusteeW
BuildSecurityDescriptorA
BuildSecurityDescriptorW
BuildTrusteeWithNameA
BuildTrusteeWithNameW
BuildTrusteeWithObjectsAndNameA
BuildTrusteeWithObjectsAndNameW
BuildTrusteeWithObjectsAndSidA
BuildTrusteeWithObjectsAndSidW
BuildTrusteeWithSidA
BuildTrusteeWithSidW
CancelOverlappedAccess
ChangeServiceConfig2A
ChangeServiceConfig2W
ChangeServiceConfigA
ChangeServiceConfigW
CheckForHiberboot
CheckTokenMembership
ClearEventLogA
ClearEventLogW
CloseCodeAuthzLevel
CloseEncryptedFileRaw
CloseEventLog
CloseServiceHandle
CloseThreadWaitChainSession
CloseTrace
CommandLineFromMsiDescriptor
ComputeAccessTokenFromCodeAuthzLevel
ControlService
ControlServiceExA
ControlServiceExW
ControlTraceA
ControlTraceW
ConvertAccessToSecurityDescriptorA
ConvertAccessToSecurityDescriptorW
ConvertSDToStringSDDomainW
ConvertSDToStringSDRootDomainA
ConvertSDToStringSDRootDomainW
ConvertSecurityDescriptorToAccessA
ConvertSecurityDescriptorToAccessNamedA
ConvertSecurityDescriptorToAccessNamedW
ConvertSecurityDescriptorToAccessW
ConvertSecurityDescriptorToStringSecurityDescriptorA
ConvertSecurityDescriptorToStringSecurityDescriptorW
ConvertSidToStringSidA
ConvertSidToStringSidW
ConvertStringSDToSDDomainA
ConvertStringSDToSDDomainW
ConvertStringSDToSDRootDomainA
ConvertStringSDToSDRootDomainW
ConvertStringSecurityDescriptorToSecurityDescriptorA
ConvertStringSecurityDescriptorToSecurityDescriptorW
ConvertStringSidToSidA
ConvertStringSidToSidW
ConvertToAutoInheritPrivateObjectSecurity
CopySid
CreateCodeAuthzLevel
CreatePrivateObjectSecurity
CreatePrivateObjectSecurityEx
CreatePrivateObjectSecurityWithMultipleInheritance
CreateProcessAsUserA
CreateProcessAsUserW
CreateProcessWithLogonW
CreateProcessWithTokenW
CreateRestrictedToken
CreateServiceA
CreateServiceW
CreateServiceEx
CreateTraceInstanceId
CreateWellKnownSid
CredBackupCredentials
CredDeleteA
CredDeleteW
CredEncryptAndMarshalBinaryBlob
CredEnumerateA
CredEnumerateW
CredFindBestCredentialA
CredFindBestCredentialW
CredFree
CredGetSessionTypes
CredGetTargetInfoA
CredGetTargetInfoW
CredIsMarshaledCredentialA
CredIsMarshaledCredentialW
CredIsProtectedA
CredIsProtectedW
CredMarshalCredentialA
CredMarshalCredentialW
CredProfileLoaded
CredProfileLoadedEx
CredProfileUnloaded
CredProtectA
CredProtectW
CredReadA
CredReadByTokenHandle
CredReadDomainCredentialsA
CredReadDomainCredentialsW
CredReadW
CredRenameA
CredRenameW
CredRestoreCredentials
CredUnmarshalCredentialA
CredUnmarshalCredentialW
CredUnprotectA
CredUnprotectW
CredWriteA
CredWriteDomainCredentialsA
CredWriteDomainCredentialsW
CredWriteW
CredpConvertCredential
CredpConvertOneCredentialSize
CredpConvertTargetInfo
CredpDecodeCredential
CredpEncodeCredential
CredpEncodeSecret
CryptAcquireContextA
CryptAcquireContextW
CryptContextAddRef
CryptCreateHash
CryptDecrypt
CryptDeriveKey
CryptDestroyHash
CryptDestroyKey
CryptDuplicateHash
CryptDuplicateKey
CryptEncrypt
CryptEnumProviderTypesA
CryptEnumProviderTypesW
CryptEnumProvidersA
CryptEnumProvidersW
CryptExportKey
CryptGenKey
CryptGenRandom
CryptGetDefaultProviderA
CryptGetDefaultProviderW
CryptGetHashParam
CryptGetKeyParam
CryptGetProvParam
CryptGetUserKey
CryptHashData
CryptHashSessionKey
CryptImportKey
CryptReleaseContext
CryptSetHashParam
CryptSetKeyParam
CryptSetProvParam
CryptSetProviderA
CryptSetProviderExA
CryptSetProviderExW
CryptSetProviderW
CryptSignHashA
CryptSignHashW
CryptVerifySignatureA
CryptVerifySignatureW
CveEventWrite
DecryptFileA
DecryptFileW
DeleteAce
DeleteService
DeregisterEventSource
DestroyPrivateObjectSecurity
DuplicateEncryptionInfoFile
DuplicateToken
DuplicateTokenEx
ElfBackupEventLogFileA
ElfBackupEventLogFileW
ElfChangeNotify
ElfClearEventLogFileA
ElfClearEventLogFileW
ElfCloseEventLog
ElfDeregisterEventSource
ElfFlushEventLog
ElfNumberOfRecords
ElfOldestRecord
ElfOpenBackupEventLogA
ElfOpenBackupEventLogW
ElfOpenEventLogA
ElfOpenEventLogW
ElfReadEventLogA
ElfReadEventLogW
ElfRegisterEventSourceA
ElfRegisterEventSourceW
ElfReportEventA
ElfReportEventAndSourceW
ElfReportEventW
EnableTrace
EnableTraceEx
EnableTraceEx2
EncryptFileA
EncryptFileW
EncryptedFileKeyInfo
EncryptionDisable
EnumDependentServicesA
EnumDependentServicesW
EnumDynamicTimeZoneInformation
EnumServiceGroupW
EnumServicesStatusA
EnumServicesStatusExA
EnumServicesStatusExW
EnumServicesStatusW
EnumerateTraceGuids
EnumerateTraceGuidsEx
EqualDomainSid
EqualPrefixSid
EqualSid
EtwLogSysConfigExtension
EventAccessControl
EventAccessQuery
EventAccessRemove
EventActivityIdControl
EventEnabled
EventProviderEnabled
EventRegister
EventSetInformation
EventUnregister
EventWrite
EventWriteEndScenario
EventWriteEx
EventWriteStartScenario
EventWriteString
EventWriteTransfer
FileEncryptionStatusA
FileEncryptionStatusW
FindFirstFreeAce
FlushEfsCache
FlushTraceA
FlushTraceW
FreeEncryptedFileKeyInfo
FreeEncryptedFileMetadata
FreeEncryptionCertificateHashList
FreeInheritedFromArray
FreeSid
GetAccessPermissionsForObjectA
GetAccessPermissionsForObjectW
GetAce
GetAclInformation
GetAuditedPermissionsFromAclA
GetAuditedPermissionsFromAclW
GetCurrentHwProfileA
GetCurrentHwProfileW
GetDynamicTimeZoneInformationEffectiveYears
GetEffectiveRightsFromAclA
GetEffectiveRightsFromAclW
GetEncryptedFileMetadata
GetEventLogInformation
GetExplicitEntriesFromAclA
GetExplicitEntriesFromAclW
GetFileSecurityA
GetFileSecurityW
GetInformationCodeAuthzLevelW
GetInformationCodeAuthzPolicyW
GetInheritanceSourceA
GetInheritanceSourceW
GetKernelObjectSecurity
GetLengthSid
GetLocalManagedApplicationData
GetLocalManagedApplications
GetManagedApplicationCategories
GetManagedApplications
GetMultipleTrusteeA
GetMultipleTrusteeOperationA
GetMultipleTrusteeOperationW
GetMultipleTrusteeW
GetNamedSecurityInfoA
GetNamedSecurityInfoExA
GetNamedSecurityInfoExW
GetNamedSecurityInfoW
GetNumberOfEventLogRecords
GetOldestEventLogRecord
GetOverlappedAccessResults
GetPrivateObjectSecurity
GetSecurityDescriptorControl
GetSecurityDescriptorDacl
GetSecurityDescriptorGroup
GetSecurityDescriptorLength
GetSecurityDescriptorOwner
GetSecurityDescriptorRMControl
GetSecurityDescriptorSacl
GetSecurityInfo
GetSecurityInfoExA
GetSecurityInfoExW
GetServiceDisplayNameA
GetServiceDisplayNameW
GetServiceKeyNameA
GetServiceKeyNameW
GetSidIdentifierAuthority
GetSidLengthRequired
GetSidSubAuthority
GetSidSubAuthorityCount
GetStringConditionFromBinary
GetThreadWaitChain
GetTokenInformation
GetTraceEnableFlags
GetTraceEnableLevel
GetTraceLoggerHandle
GetTrusteeFormA
GetTrusteeFormW
GetTrusteeNameA
GetTrusteeNameW
GetTrusteeTypeA
GetTrusteeTypeW
GetUserNameA
GetUserNameW
GetWindowsAccountDomainSid
I_QueryTagInformation
I_ScIsSecurityProcess
I_ScPnPGetServiceName
I_ScQueryServiceConfig
I_ScRegisterPreshutdownRestart
I_ScSendPnPMessage
I_ScSendTSMessage
I_ScSetServiceBitsA
I_ScSetServiceBitsW
I_ScValidatePnPService
IdentifyCodeAuthzLevelW
ImpersonateAnonymousToken
ImpersonateLoggedOnUser
ImpersonateNamedPipeClient
ImpersonateSelf
InitializeAcl
InitializeSecurityDescriptor
InitializeSid
InitiateShutdownA
InitiateShutdownW
InitiateSystemShutdownA
InitiateSystemShutdownExA
InitiateSystemShutdownExW
InitiateSystemShutdownW
InstallApplication
IsTextUnicode
IsTokenRestricted
IsTokenUntrusted
IsValidAcl
IsValidRelativeSecurityDescriptor
IsValidSecurityDescriptor
IsValidSid
IsWellKnownSid
LockServiceDatabase
LogonUserA
LogonUserExA
LogonUserExExW
LogonUserExW
LogonUserW
LookupAccountNameA
LookupAccountNameW
LookupAccountSidA
LookupAccountSidW
LookupPrivilegeDisplayNameA
LookupPrivilegeDisplayNameW
LookupPrivilegeNameA
LookupPrivilegeNameW
LookupPrivilegeValueA
LookupPrivilegeValueW
LookupSecurityDescriptorPartsA
LookupSecurityDescriptorPartsW
LsaAddAccountRights
LsaAddPrivilegesToAccount
LsaClearAuditLog
LsaClose
LsaCreateAccount
LsaCreateSecret
LsaCreateTrustedDomain
LsaCreateTrustedDomainEx
LsaDelete
LsaDeleteTrustedDomain
LsaEnumerateAccountRights
LsaEnumerateAccounts
LsaEnumerateAccountsWithUserRight
LsaEnumeratePrivileges
LsaEnumeratePrivilegesOfAccount
LsaEnumerateTrustedDomains
LsaEnumerateTrustedDomainsEx
LsaFreeMemory
LsaGetAppliedCAPIDs
LsaGetQuotasForAccount
LsaGetRemoteUserName
LsaGetSystemAccessAccount
LsaGetUserName
LsaICLookupNames
LsaICLookupNamesWithCreds
LsaICLookupSids
LsaICLookupSidsWithCreds
LsaLookupNames
LsaLookupNames2
LsaLookupPrivilegeDisplayName
LsaLookupPrivilegeName
LsaLookupPrivilegeValue
LsaLookupSids
LsaLookupSids2
LsaManageSidNameMapping
LsaNtStatusToWinError
LsaOpenAccount
LsaOpenPolicy
LsaOpenPolicySce
LsaOpenSecret
LsaOpenTrustedDomain
LsaOpenTrustedDomainByName
LsaQueryCAPs
LsaQueryDomainInformationPolicy
LsaQueryForestTrustInformation
LsaQueryInfoTrustedDomain
LsaQueryInformationPolicy
LsaQuerySecret
LsaQuerySecurityObject
LsaQueryTrustedDomainInfo
LsaQueryTrustedDomainInfoByName
LsaRemoveAccountRights
LsaRemovePrivilegesFromAccount
LsaRetrievePrivateData
LsaSetCAPs
LsaSetDomainInformationPolicy
LsaSetForestTrustInformation
LsaSetInformationPolicy
LsaSetInformationTrustedDomain
LsaSetQuotasForAccount
LsaSetSecret
LsaSetSecurityObject
LsaSetSystemAccessAccount
LsaSetTrustedDomainInfoByName
LsaSetTrustedDomainInformation
LsaStorePrivateData
MD4Final
MD4Init
MD4Update
MD5Final
MD5Init
MD5Update
MIDL_user_free_Ext
MSChapSrvChangePassword
MSChapSrvChangePassword2
MakeAbsoluteSD
MakeAbsoluteSD2
MakeSelfRelativeSD
MapGenericMask
NotifyBootConfigStatus
NotifyChangeEventLog
NotifyServiceStatusChange
NotifyServiceStatusChangeA
NotifyServiceStatusChangeW
NpGetUserName
ObjectCloseAuditAlarmA
ObjectCloseAuditAlarmW
ObjectDeleteAuditAlarmA
ObjectDeleteAuditAlarmW
ObjectOpenAuditAlarmA
ObjectOpenAuditAlarmW
ObjectPrivilegeAuditAlarmA
ObjectPrivilegeAuditAlarmW
OpenBackupEventLogA
OpenBackupEventLogW
OpenEncryptedFileRawA
OpenEncryptedFileRawW
OpenEventLogA
OpenEventLogW
OpenProcessToken
OpenSCManagerA
OpenSCManagerW
OpenServiceA
OpenServiceW
OpenThreadToken
OpenThreadWaitChainSession
OpenTraceA
OpenTraceW
OperationEnd
OperationStart
PerfAddCounters
PerfCloseQueryHandle
PerfCreateInstance
PerfDecrementULongCounterValue
PerfDecrementULongLongCounterValue
PerfDeleteCounters
PerfDeleteInstance
PerfEnumerateCounterSet
PerfEnumerateCounterSetInstances
PerfIncrementULongCounterValue
PerfIncrementULongLongCounterValue
PerfOpenQueryHandle
PerfQueryCounterData
PerfQueryCounterInfo
PerfQueryCounterSetRegistrationInfo
PerfQueryInstance
PerfRegCloseKey
PerfRegEnumKey
PerfRegEnumValue
PerfRegQueryInfoKey
PerfRegQueryValue
PerfRegSetValue
PerfSetCounterRefValue
PerfSetCounterSetInfo
PerfSetULongCounterValue
PerfSetULongLongCounterValue
PerfStartProvider
PerfStartProviderEx
PerfStopProvider
PrivilegeCheck
PrivilegedServiceAuditAlarmA
PrivilegedServiceAuditAlarmW
ProcessIdleTasks
ProcessIdleTasksW
ProcessTrace
QueryAllTracesA
QueryAllTracesW
QueryLocalUserServiceName
QueryRecoveryAgentsOnEncryptedFile
QuerySecurityAccessMask
QueryServiceConfig2A
QueryServiceConfig2W
QueryServiceConfigA
QueryServiceConfigW
QueryServiceDynamicInformation
QueryServiceLockStatusA
QueryServiceLockStatusW
QueryServiceObjectSecurity
QueryServiceStatus
QueryServiceStatusEx
QueryTraceA
QueryTraceW
QueryTraceProcessingHandle
QueryUserServiceName
QueryUserServiceNameForContext
QueryUsersOnEncryptedFile
ReadEncryptedFileRaw
ReadEventLogA
ReadEventLogW
RegCloseKey
RegConnectRegistryA
RegConnectRegistryExA
RegConnectRegistryExW
RegConnectRegistryW
RegCopyTreeA
RegCopyTreeW
RegCreateKeyA
RegCreateKeyExA
RegCreateKeyExW
RegCreateKeyTransactedA
RegCreateKeyTransactedW
RegCreateKeyW
RegDeleteKeyA
RegDeleteKeyExA
RegDeleteKeyExW
RegDeleteKeyTransactedA
RegDeleteKeyTransactedW
RegDeleteKeyValueA
RegDeleteKeyValueW
RegDeleteKeyW
RegDeleteTreeA
RegDeleteTreeW
RegDeleteValueA
RegDeleteValueW
RegDisablePredefinedCache
RegDisablePredefinedCacheEx
RegDisableReflectionKey
RegEnableReflectionKey
RegEnumKeyA
RegEnumKeyExA
RegEnumKeyExW
RegEnumKeyW
RegEnumValueA
RegEnumValueW
RegFlushKey
RegGetKeySecurity
RegGetValueA
RegGetValueW
RegLoadAppKeyA
RegLoadAppKeyW
RegLoadKeyA
RegLoadKeyW
RegLoadMUIStringA
RegLoadMUIStringW
RegNotifyChangeKeyValue
RegOpenCurrentUser
RegOpenKeyA
RegOpenKeyExA
RegOpenKeyExW
RegOpenKeyTransactedA
RegOpenKeyTransactedW
RegOpenKeyW
RegOpenUserClassesRoot
RegOverridePredefKey
RegQueryInfoKeyA
RegQueryInfoKeyW
RegQueryMultipleValuesA
RegQueryMultipleValuesW
RegQueryReflectionKey
RegQueryValueA
RegQueryValueExA
RegQueryValueExW
RegQueryValueW
RegRenameKey
RegReplaceKeyA
RegReplaceKeyW
RegRestoreKeyA
RegRestoreKeyW
RegSaveKeyA
RegSaveKeyExA
RegSaveKeyExW
RegSaveKeyW
RegSetKeySecurity
RegSetKeyValueA
RegSetKeyValueW
RegSetValueA
RegSetValueExA
RegSetValueExW
RegSetValueW
RegUnLoadKeyA
RegUnLoadKeyW
RegisterEventSourceA
RegisterEventSourceW
RegisterIdleTask
RegisterServiceCtrlHandlerA
RegisterServiceCtrlHandlerExA
RegisterServiceCtrlHandlerExW
RegisterServiceCtrlHandlerW
RegisterTraceGuidsA
RegisterTraceGuidsW
RegisterWaitChainCOMCallback
RemoteRegEnumKeyWrapper
RemoteRegEnumValueWrapper
RemoteRegQueryInfoKeyWrapper
RemoteRegQueryMultipleValues2Wrapper
RemoteRegQueryMultipleValuesWrapper
RemoteRegQueryValueWrapper
RemoveTraceCallback
RemoveUsersFromEncryptedFile
ReportEventA
ReportEventW
RevertToSelf
SafeBaseRegGetKeySecurity
SaferCloseLevel
SaferComputeTokenFromLevel
SaferCreateLevel
SaferGetLevelInformation
SaferGetPolicyInformation
SaferIdentifyLevel
SaferRecordEventLogEntry
SaferSetLevelInformation
SaferSetPolicyInformation
SaferiChangeRegistryScope
SaferiCompareTokenLevels
SaferiIsDllAllowed
SaferiIsExecutableFileType
SaferiPopulateDefaultsInRegistry
SaferiRecordEventLogEntry
SaferiReplaceProcessThreadTokens
SaferiSearchMatchingHashRules
SetAclInformation
SetEncryptedFileMetadata
SetEntriesInAccessListA
SetEntriesInAccessListW
SetEntriesInAclA
SetEntriesInAclW
SetEntriesInAuditListA
SetEntriesInAuditListW
SetFileSecurityA
SetFileSecurityW
SetInformationCodeAuthzLevelW
SetInformationCodeAuthzPolicyW
SetKernelObjectSecurity
SetNamedSecurityInfoA
SetNamedSecurityInfoExA
SetNamedSecurityInfoExW
SetNamedSecurityInfoW
SetPrivateObjectSecurity
SetPrivateObjectSecurityEx
SetSecurityAccessMask
SetSecurityDescriptorControl
SetSecurityDescriptorDacl
SetSecurityDescriptorGroup
SetSecurityDescriptorOwner
SetSecurityDescriptorRMControl
SetSecurityDescriptorSacl
SetSecurityInfo
SetSecurityInfoExA
SetSecurityInfoExW
SetServiceBits
SetServiceObjectSecurity
SetServiceStatus
SetThreadToken
SetTokenInformation
SetTraceCallback
SetUserFileEncryptionKey
SetUserFileEncryptionKeyEx
StartServiceA
StartServiceCtrlDispatcherA
StartServiceCtrlDispatcherW
StartServiceW
StartTraceA
StartTraceW
StopTraceA
StopTraceW
SystemFunction001
SystemFunction002
SystemFunction003
SystemFunction004
SystemFunction005
SystemFunction006
SystemFunction007
SystemFunction008
SystemFunction009
SystemFunction010
SystemFunction011
SystemFunction012
SystemFunction013
SystemFunction014
SystemFunction015
SystemFunction016
SystemFunction017
SystemFunction018
SystemFunction019
SystemFunction020
SystemFunction021
SystemFunction022
SystemFunction023
SystemFunction024
SystemFunction025
SystemFunction026
SystemFunction027
SystemFunction028
SystemFunction029
SystemFunction030
SystemFunction031
SystemFunction032
SystemFunction033
SystemFunction034
SystemFunction035
SystemFunction036
SystemFunction040
SystemFunction041
TraceEvent
TraceEventInstance
TraceMessage
TraceMessageVa
TraceQueryInformation
TraceSetInformation
TreeResetNamedSecurityInfoA
TreeResetNamedSecurityInfoW
TreeSetNamedSecurityInfoA
TreeSetNamedSecurityInfoW
TrusteeAccessToObjectA
TrusteeAccessToObjectW
UninstallApplication
UnlockServiceDatabase
UnregisterIdleTask
UnregisterTraceGuids
UpdateTraceA
UpdateTraceW
WdmWmiServiceMain
UsePinForEncryptedFilesA
UsePinForEncryptedFilesW
WaitServiceState
WmiCloseBlock
WmiCloseTraceWithCursor
WmiConvertTimestamp
WmiDevInstToInstanceNameA
WmiDevInstToInstanceNameW
WmiEnumerateGuids
WmiExecuteMethodA
WmiExecuteMethodW
WmiFileHandleToInstanceNameA
WmiFileHandleToInstanceNameW
WmiFreeBuffer
WmiGetFirstTraceOffset
WmiGetNextEvent
WmiGetTraceHeader
WmiMofEnumerateResourcesA
WmiMofEnumerateResourcesW
WmiNotificationRegistrationA
WmiNotificationRegistrationW
WmiOpenBlock
WmiOpenTraceWithCursor
WmiParseTraceEvent
WmiQueryAllDataA
WmiQueryAllDataMultipleA
WmiQueryAllDataMultipleW
WmiQueryAllDataW
WmiQueryGuidInformation
WmiQuerySingleInstanceA
WmiQuerySingleInstanceMultipleA
WmiQuerySingleInstanceMultipleW
WmiQuerySingleInstanceW
WmiReceiveNotificationsA
WmiReceiveNotificationsW
WmiSetSingleInstanceA
WmiSetSingleInstanceW
WmiSetSingleItemA
WmiSetSingleItemW
F64(Wow64Win32ApiEntry)
WriteEncryptedFileRaw
