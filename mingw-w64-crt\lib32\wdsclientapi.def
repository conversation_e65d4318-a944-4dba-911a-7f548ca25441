;
; Definition file of WDSCLIENTAPI.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WDSCLIENTAPI.dll"
EXPORTS
WdsCliAuthorizeSession@8
WdsCliCancelTransfer@4
WdsCliClose@4
WdsCliCreateSession@12
WdsCliFindFirstImage@8
WdsCliFindNextImage@4
WdsCliFreeDomainJoinInformation@4
WdsCliFreeStringArray@8
WdsCliFreeUnattendVariables@8
WdsCliGetClientUnattend@20
WdsCliGetDomainJoinInformation@16
WdsCliGetEnumerationFlags@8
WdsCliGetImageArchitecture@8
WdsCliGetImageDescription@8
WdsCliGetImageFiles@12
WdsCliGetImageGroup@8
WdsCliGetImageHalName@8
WdsCliGetImageHandleFromF<PERSON>Handle@8
WdsCliGetImageHandleFromTransferHandle@8
WdsCliGetImageIndex@8
WdsCliGetImageLanguage@8
WdsCliGetImageLanguages@12
WdsCliGetImageLastModifiedTime@8
WdsCliGetImageName@8
WdsCliGetImageNamespace@8
WdsCliGetImageParameter@16
WdsCliGetImagePath@8
WdsCliGetImageSize@8
WdsCliGetImageType@8
WdsCliGetImageVersion@8
WdsCliGetTransferSize@8
WdsCliGetUnattendVariables@20
WdsCliInitializeLog@16
WdsCliLog
WdsCliObtainDriverPackages@16
WdsCliRegisterTrace@4
WdsCliTransferFile@36
WdsCliTransferImage@28
WdsCliWaitForTransfer@4
