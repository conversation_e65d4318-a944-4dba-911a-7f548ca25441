;
; Definition file of MSCTF.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "MSCTF.dll"
EXPORTS
TF_GetLangDescriptionFromHKL
TF_GetLangIcon
TF_GetMlngHKL
TF_GetMlngIconIndex
TF_GetThreadFlags
TF_InatExtractIcon
TF_InitMlngInfo
TF_IsInMarshaling
TF_MlngInfoCount
TF_GetLangIconFromHKL
TF_RunInputCPL
CtfImeAssociateFocus
CtfImeConfigure
CtfImeConversionList
CtfImeCreateInputContext
CtfImeCreateThreadMgr
CtfImeDestroy
CtfImeDestroyInputContext
CtfImeDestroyThreadMgr
CtfImeDispatchDefImeMessage
CtfImeEnumRegisterWord
CtfImeEscape
CtfImeEscapeEx
CtfImeGetGuidAtom
CtfImeGetR<PERSON>tyle
CtfImeInquire
CtfImeInquireExW
CtfImeIsGuidMapEnable
CtfImeIsIME
CtfImeProcessCicHotkey
CtfImeProcessKey
CtfImeRegisterWord
CtfImeSelect
CtfImeSelectEx
CtfImeSetActiveContext
CtfImeSetCompositionString
CtfImeSetFocus
CtfImeToAsciiEx
CtfImeUnregisterWord
CtfNotifyIME
DllCanUnloadNow
DllGetClassObject
DllRegisterServer
DllUnregisterServer
SetInputScope
SetInputScopeXML
SetInputScopes
SetInputScopes2
TF_AttachThreadInput
TF_CUASAppFix
TF_CanUninitialize
TF_CheckThreadInputIdle
TF_CleanUpPrivateMessages
TF_ClearLangBarAddIns
TF_CreateCategoryMgr
TF_CreateCicLoadMutex
TF_CreateCicLoadWinStaMutex
TF_CreateDisplayAttributeMgr
TF_CreateInputProcessorProfiles
TF_CreateLangBarItemMgr
TF_CreateLangBarMgr
TF_CreateThreadMgr
TF_DllDetachInOther
TF_GetAppCompatFlags
TF_GetCompatibleKeyboardLayout
TF_GetGlobalCompartment
TF_GetInitSystemFlags
TF_GetInputScope
TF_GetShowFloatingStatus
TF_GetThreadMgr
TF_InitSystem
TF_InvalidAssemblyListCache
TF_InvalidAssemblyListCacheIfExist
TF_IsCtfmonRunning
TF_IsFullScreenWindowActivated
TF_IsThreadWithFlags
TF_MapCompatibleHKL
TF_MapCompatibleKeyboardTip
TF_Notify
TF_PostAllThreadMsg
TF_RegisterLangBarAddIn
TF_SendLangBandMsg
TF_SetDefaultRemoteKeyboardLayout
TF_SetShowFloatingStatus
TF_SetThreadFlags
TF_UninitSystem
TF_UnregisterLangBarAddIn
TF_WaitForInitialized
