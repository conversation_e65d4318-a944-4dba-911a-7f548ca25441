LIBRARY "PROPSYS.dll"
EXPORTS
SHGetPropertyStoreForWindow
ClearPropVariantArray
ClearVariantArray
DllCanUnloadNow
DllGetClassObject
DllRegisterServer
DllUnregisterServer
GetProxyDllInfo
InitPropVariantFromBooleanVector
InitPropVariantFromBuffer
InitPropVariantFromCLSID
InitPropVariantFromDoubleVector
InitPropVariantFromFileTime
InitPropVariantFromFileTimeVector
InitPropVariantFromGUIDAsString
InitPropVariantFromInt16Vector
InitPropVariantFromInt32Vector
InitPropVariantFromInt64Vector
InitPropVariantFromPropVariantVectorElem
InitPropVariantFromResource
InitPropVariantFromStrRet
InitPropVariantFromStringAsVector
InitPropVariantFromStringVector
InitPropVariantFromUInt16Vector
InitPropVariantFromUInt32Vector
InitPropVariantFromUInt64Vector
InitPropVariantVectorFromPropVariant
InitVariantFromBooleanArray
InitVariantFromBuffer
InitVariantFromDoubleArray
InitVariantFromFileTime
InitVariantFromFileTimeArray
InitVariantFromGUIDAsString
InitVariantFromInt16Array
InitVariantFromInt32Array
InitVariantFromInt64Array
InitVariantFromResource
InitVariantFromStrRet
InitVariantFromStringArray
InitVariantFromUInt16Array
InitVariantFromUInt32Array
InitVariantFromUInt64Array
InitVariantFromVariantArrayElem
PSCoerceToCanonicalValue
PSCreateAdapterFromPropertyStore
PSCreateDelayedMultiplexPropertyStore
PSCreateMemoryPropertyStore
PSCreateMultiplexPropertyStore
PSCreatePropertyChangeArray
PSCreatePropertyStoreFromObject
PSCreatePropertyStoreFromPropertySetStorage
PSCreateSimplePropertyChange
PSEnumeratePropertyDescriptions
PSFormatForDisplay
PSFormatForDisplayAlloc
PSFormatPropertyValue
PSGetImageReferenceForValue
PSGetItemPropertyHandler
PSGetItemPropertyHandlerWithCreateObject
PSGetNameFromPropertyKey
PSGetNamedPropertyFromPropertyStorage
PSGetPropertyDescription
PSGetPropertyDescriptionByName
PSGetPropertyDescriptionListFromString
PSGetPropertyFromPropertyStorage
PSGetPropertyKeyFromName
PSGetPropertySystem
PSGetPropertyValue
PSLookupPropertyHandlerCLSID
PSPropertyBag_Delete
PSPropertyBag_ReadBOOL
PSPropertyBag_ReadBSTR
PSPropertyBag_ReadDWORD
PSPropertyBag_ReadGUID
PSPropertyBag_ReadInt
PSPropertyBag_ReadLONG
PSPropertyBag_ReadPOINTL
PSPropertyBag_ReadPOINTS
PSPropertyBag_ReadPropertyKey
PSPropertyBag_ReadRECTL
PSPropertyBag_ReadSHORT
PSPropertyBag_ReadStr
PSPropertyBag_ReadStrAlloc
PSPropertyBag_ReadStream
PSPropertyBag_ReadType
PSPropertyBag_ReadULONGLONG
PSPropertyBag_ReadUnknown
PSPropertyBag_WriteBOOL
PSPropertyBag_WriteBSTR
PSPropertyBag_WriteDWORD
PSPropertyBag_WriteGUID
PSPropertyBag_WriteInt
PSPropertyBag_WriteLONG
PSPropertyBag_WritePOINTL
PSPropertyBag_WritePOINTS
PSPropertyBag_WritePropertyKey
PSPropertyBag_WriteRECTL
PSPropertyBag_WriteSHORT
PSPropertyBag_WriteStr
PSPropertyBag_WriteStream
PSPropertyBag_WriteULONGLONG
PSPropertyBag_WriteUnknown
PSPropertyKeyFromString
PSRefreshPropertySchema
PSRegisterPropertySchema
PSSetPropertyValue
PSStringFromPropertyKey
PSUnregisterPropertySchema
PropVariantChangeType
PropVariantCompareEx
PropVariantGetBooleanElem
PropVariantGetDoubleElem
PropVariantGetElementCount
PropVariantGetFileTimeElem
PropVariantGetInt16Elem
PropVariantGetInt32Elem
PropVariantGetInt64Elem
PropVariantGetStringElem
PropVariantGetUInt16Elem
PropVariantGetUInt32Elem
PropVariantGetUInt64Elem
PropVariantToBSTR
PropVariantToBoolean
PropVariantToBooleanVector
PropVariantToBooleanVectorAlloc
PropVariantToBooleanWithDefault
PropVariantToBuffer
PropVariantToDouble
PropVariantToDoubleVector
PropVariantToDoubleVectorAlloc
PropVariantToDoubleWithDefault
PropVariantToFileTime
PropVariantToFileTimeVector
PropVariantToFileTimeVectorAlloc
PropVariantToGUID
PropVariantToInt16
PropVariantToInt16Vector
PropVariantToInt16VectorAlloc
PropVariantToInt16WithDefault
PropVariantToInt32
PropVariantToInt32Vector
PropVariantToInt32VectorAlloc
PropVariantToInt32WithDefault
PropVariantToInt64
PropVariantToInt64Vector
PropVariantToInt64VectorAlloc
PropVariantToInt64WithDefault
PropVariantToStrRet
PropVariantToString
PropVariantToStringAlloc
PropVariantToStringVector
PropVariantToStringVectorAlloc
PropVariantToStringWithDefault
PropVariantToUInt16
PropVariantToUInt16Vector
PropVariantToUInt16VectorAlloc
PropVariantToUInt16WithDefault
PropVariantToUInt32
PropVariantToUInt32Vector
PropVariantToUInt32VectorAlloc
PropVariantToUInt32WithDefault
PropVariantToUInt64
PropVariantToUInt64Vector
PropVariantToUInt64VectorAlloc
PropVariantToUInt64WithDefault
PropVariantToVariant
PropVariantToWinRTPropertyValue
StgDeserializePropVariant
StgSerializePropVariant
VariantCompare
VariantGetBooleanElem
VariantGetDoubleElem
VariantGetElementCount
VariantGetInt16Elem
VariantGetInt32Elem
VariantGetInt64Elem
VariantGetStringElem
VariantGetUInt16Elem
VariantGetUInt32Elem
VariantGetUInt64Elem
VariantToBoolean
VariantToBooleanArray
VariantToBooleanArrayAlloc
VariantToBooleanWithDefault
VariantToBuffer
VariantToDosDateTime
VariantToDouble
VariantToDoubleArray
VariantToDoubleArrayAlloc
VariantToDoubleWithDefault
VariantToFileTime
VariantToGUID
VariantToInt16
VariantToInt16Array
VariantToInt16ArrayAlloc
VariantToInt16WithDefault
VariantToInt32
VariantToInt32Array
VariantToInt32ArrayAlloc
VariantToInt32WithDefault
VariantToInt64
VariantToInt64Array
VariantToInt64ArrayAlloc
VariantToInt64WithDefault
VariantToPropVariant
VariantToStrRet
VariantToString
VariantToStringAlloc
VariantToStringArray
VariantToStringArrayAlloc
VariantToStringWithDefault
VariantToUInt16
VariantToUInt16Array
VariantToUInt16ArrayAlloc
VariantToUInt16WithDefault
VariantToUInt32
VariantToUInt32Array
VariantToUInt32ArrayAlloc
VariantToUInt32WithDefault
VariantToUInt64
VariantToUInt64Array
VariantToUInt64ArrayAlloc
VariantToUInt64WithDefault
WinRTPropertyValueToPropVariant
