; 
; Exports of file msls31.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY msls31.dll
EXPORTS
LsCreateContext
LsDestroyContext
LsCreateLine
LsModifyLineHeight
LsDestroyLine
LsCreateSubline
LsFetchAppendToCurrentSubline
LsAppendRunToCurrentSubline
LsResetRMInCurrentSubline
LsFinishCurrentSubline
LsTruncateSubline
LsFindPrevBreakSubline
LsFindNextBreakSubline
LsForceBreakSubline
LsSetBreakSubline
LsDestroySubline
LsMatchPresSubline
LsExpandSubline
LsGetSpecialEffectsSubline
LsdnFinishRegular
LsdnFinishRegularAddAdvancePen
LsdnFinishDelete
LsdnFinishByPen
LsdnFinishBySubline
LsdnFinishDeleteAll
LsdnFinishByOneChar
LsdnQueryObjDimRange
LsdnResetObjDim
LsdnQueryPenNode
LsdnResetPenNode
LsdnSetRigidDup
LsdnGetDup
LsdnSetAbsBaseLine
LsdnResolvePrevTab
LsdnGetCurTabInfo
LsdnSkipCurTab
LsdnDistribute
LsdnSubmitSublines
LsDisplayLine
LsDisplaySubline
LsQueryLineCpPpoint
LsQueryLinePointPcp
LsQueryLineDup
LsQueryFLineEmpty
LsQueryPointPcpSubline
LsQueryCpPpointSubline
LsSetDoc
LsSetModWidthPairs
LsSetCompression
LsSetExpansion
LsSetBreaking
LssbGetObjDimSubline
LssbGetDupSubline
LssbFDonePresSubline
LssbGetPlsrunsFromSubline
LssbGetNumberDnodesInSubline
LssbGetVisibleDcpInSubline
LsPointXYFromPointUV
LsPointUV2FromPointUV1
LsGetWarichuLsimethods
LsGetRubyLsimethods
LsGetTatenakayokoLsimethods
LsSqueezeSubline
LsCompressSubline
LsGetHihLsimethods
LsQueryTextCellDetails
LsFetchAppendToCurrentSublineResume
LsdnGetFormatDepth
LssbFDoneDisplay
LsGetReverseLsimethods
LsEnumLine
LsGetMinDurBreaks
LsGetLineDur
LsEnumSubline
LsdnModifyParaEnding
LssbGetDurTrailInSubline
LssbGetDurTrailWithPensInSubline
LssbFIsSublineEmpty
LsLwMultDivR
