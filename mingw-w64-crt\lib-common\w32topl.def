; 
; Exports of file W32TOPL.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY W32TOPL.dll
EXPORTS
ToplAddEdgeSetToGraph
ToplAddEdgeToGraph
ToplDeleteComponents
ToplDeleteGraphState
ToplDeleteSpanningTreeEdges
ToplEdgeAssociate
ToplEdgeCreate
ToplEdgeDestroy
ToplEdgeDisassociate
ToplEdgeFree
ToplEdgeGetFromVertex
ToplEdgeGetToVertex
ToplEdgeGetWeight
ToplEdgeInit
ToplEdgeSetFromVertex
ToplEdgeSetToVertex
ToplEdgeSetVtx
ToplEdgeSetWeight
ToplFree
ToplGetAlwaysSchedule
ToplGetSpanningTreeEdgesForVtx
ToplGraphAddVertex
ToplGraphCreate
ToplGraphDestroy
ToplGraphFindEdgesForMST
ToplGraphFree
ToplGraphInit
ToplGraphMakeRing
ToplGraphNumberOfVertices
ToplGraphRemoveVertex
ToplGraphSetVertexIter
ToplHeapCreate
ToplHeapDestroy
ToplHeapExtractMin
ToplHeapInsert
ToplHeapIsElementOf
ToplHeapIsEmpty
ToplIsToplException
ToplIterAdvance
ToplIterCreate
ToplIterFree
ToplIterGetObject
ToplListAddElem
ToplListCreate
ToplListFree
ToplListNumberOfElements
ToplListRemoveElem
ToplListSetIter
ToplMakeGraphState
ToplPScheduleValid
ToplSTHeapAdd
ToplSTHeapCostReduced
ToplSTHeapDestroy
ToplSTHeapExtractMin
ToplSTHeapInit
ToplScheduleCacheCreate
ToplScheduleCacheDestroy
ToplScheduleCreate
ToplScheduleDuration
ToplScheduleExportReadonly
ToplScheduleImport
ToplScheduleIsEqual
ToplScheduleMaxUnavailable
ToplScheduleMerge
ToplScheduleNumEntries
ToplScheduleValid
ToplSetAllocator
ToplVertexCreate
ToplVertexDestroy
ToplVertexFree
ToplVertexGetId
ToplVertexGetInEdge
ToplVertexGetOutEdge
ToplVertexGetParent
ToplVertexInit
ToplVertexNumberOfInEdges
ToplVertexNumberOfOutEdges
ToplVertexSetId
ToplVertexSetParent
