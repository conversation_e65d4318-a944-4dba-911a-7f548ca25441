; 
; Exports of file SQLSRV32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY SQLSRV32.dll
EXPORTS
SQLBindCol
SQLCancel
SQLColAttributeW
SQLConnectW
SQLDescribeColW
SQLDisconnect
SQLExecDirectW
SQLExecute
SQLFetch
SQLFreeStmt
SQLGetCursorNameW
SQLNumResultCols
SQLPrepareW
SQLRowCount
SQLSetCursorNameW
SQLBulkOperations
SQLColumnsW
SQLDriverConnectW
SQLGetConnectOptionW
SQLGetData
SQLGetFunctions
SQLGetInfoW
SQLGetTypeInfoW
SQLParamData
SQLPutData
SQLSetConnectOptionW
SQLSpecialColumnsW
SQLStatisticsW
SQLTablesW
SQLBrowseConnectW
SQLColumnPrivilegesW
SQLDescribeParam
SQLExtendedFetch
SQLForeignKeysW
SQLMoreResults
SQLNativeSqlW
SQLNumParams
SQLParamOptions
SQLPrimaryKeysW
SQLProcedureColumnsW
SQLProceduresW
SQLSetPos
SQLSetScrollOptions
SQLTablePrivilegesW
SQLBindParameter
SQLAllocHandle
SQLCloseCursor
SQLCopyDesc
SQLEndTran
SQLFreeHandle
SQLGetConnectAttrW
SQLGetDescFieldW
SQLGetDescRecW
SQLGetDiagFieldW
SQLGetDiagRecW
SQLGetEnvAttr
SQLGetStmtAttrW
SQLSetConnectAttrW
SQLSetDescFieldW
SQLSetDescRec
SQLSetEnvAttr
SQLSetStmtAttrW
SQLFetchScroll
LibMain
ConfigDSNW
ConfigDriverW
SQLDebug
BCP_batch
BCP_bind
BCP_colfmt
BCP_collen
BCP_colptr
BCP_columns
BCP_control
BCP_done
BCP_init
BCP_exec
BCP_moretext
BCP_sendrow
BCP_readfmt
BCP_writefmt
ConnectDlgProc
WizDSNDlgProc
WizIntSecurityDlgProc
WizDatabaseDlgProc
WizLanguageDlgProc
FinishDlgProc
TestDlgProc
BCP_getcolfmt
BCP_setcolfmt
