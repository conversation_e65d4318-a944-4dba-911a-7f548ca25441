/*** Autogenerated by WIDL 8.5 from include/comadmin.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __comadmin_h__
#define __comadmin_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __ICOMAdminCatalog_FWD_DEFINED__
#define __ICOMAdminCatalog_FWD_DEFINED__
typedef interface ICOMAdminCatalog ICOMAdminCatalog;
#ifdef __cplusplus
interface ICOMAdminCatalog;
#endif /* __cplusplus */
#endif

#ifndef __ICOMAdminCatalog2_FWD_DEFINED__
#define __ICOMAdminCatalog2_FWD_DEFINED__
typedef interface ICOMAdminCatalog2 ICOMAdminCatalog2;
#ifdef __cplusplus
interface ICOMAdminCatalog2;
#endif /* __cplusplus */
#endif

#ifndef __ICatalogObject_FWD_DEFINED__
#define __ICatalogObject_FWD_DEFINED__
typedef interface ICatalogObject ICatalogObject;
#ifdef __cplusplus
interface ICatalogObject;
#endif /* __cplusplus */
#endif

#ifndef __ICatalogCollection_FWD_DEFINED__
#define __ICatalogCollection_FWD_DEFINED__
typedef interface ICatalogCollection ICatalogCollection;
#ifdef __cplusplus
interface ICatalogCollection;
#endif /* __cplusplus */
#endif

#ifndef __COMAdminCatalog_FWD_DEFINED__
#define __COMAdminCatalog_FWD_DEFINED__
#ifdef __cplusplus
typedef class COMAdminCatalog COMAdminCatalog;
#else
typedef struct COMAdminCatalog COMAdminCatalog;
#endif /* defined __cplusplus */
#endif /* defined __COMAdminCatalog_FWD_DEFINED__ */

#ifndef __COMAdminCatalogObject_FWD_DEFINED__
#define __COMAdminCatalogObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class COMAdminCatalogObject COMAdminCatalogObject;
#else
typedef struct COMAdminCatalogObject COMAdminCatalogObject;
#endif /* defined __cplusplus */
#endif /* defined __COMAdminCatalogObject_FWD_DEFINED__ */

#ifndef __COMAdminCatalogCollection_FWD_DEFINED__
#define __COMAdminCatalogCollection_FWD_DEFINED__
#ifdef __cplusplus
typedef class COMAdminCatalogCollection COMAdminCatalogCollection;
#else
typedef struct COMAdminCatalogCollection COMAdminCatalogCollection;
#endif /* defined __cplusplus */
#endif /* defined __COMAdminCatalogCollection_FWD_DEFINED__ */

/* Headers for imported files */

#include <unknwn.h>
#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#include <objbase.h>

typedef enum COMAdminInUse {
    COMAdminNotInUse = 0,
    COMAdminInUseByCatalog = 1,
    COMAdminInUseByRegistryUnknown = 2,
    COMAdminInUseByRegistryProxyStub = 3,
    COMAdminInUseByRegistryTypeLib = 4,
    COMAdminInUseByRegistryClsid = 5
} COMAdminInUse;
typedef enum COMAdminErrorCodes {
    COMAdminErrObjectErrors = 0x80110401,
    COMAdminErrObjectInvalid = 0x80110402,
    COMAdminErrKeyMissing = 0x80110403,
    COMAdminErrAlreadyInstalled = 0x80110404,
    COMAdminErrAppFileWriteFail = 0x80110407,
    COMAdminErrAppFileReadFail = 0x80110408,
    COMAdminErrAppFileVersion = 0x80110409,
    COMAdminErrBadPath = 0x8011040a,
    COMAdminErrApplicationExists = 0x8011040b,
    COMAdminErrRoleExists = 0x8011040c,
    COMAdminErrCantCopyFile = 0x8011040d,
    COMAdminErrNoUser = 0x8011040f,
    COMAdminErrInvalidUserids = 0x80110410,
    COMAdminErrNoRegistryCLSID = 0x80110411,
    COMAdminErrBadRegistryProgID = 0x80110412,
    COMAdminErrAuthenticationLevel = 0x80110413,
    COMAdminErrUserPasswdNotValid = 0x80110414,
    COMAdminErrCLSIDOrIIDMismatch = 0x80110418,
    COMAdminErrRemoteInterface = 0x80110419,
    COMAdminErrDllRegisterServer = 0x8011041a,
    COMAdminErrNoServerShare = 0x8011041b,
    COMAdminErrDllLoadFailed = 0x8011041d,
    COMAdminErrBadRegistryLibID = 0x8011041e,
    COMAdminErrAppDirNotFound = 0x8011041f,
    COMAdminErrRegistrarFailed = 0x80110423,
    COMAdminErrCompFileDoesNotExist = 0x80110424,
    COMAdminErrCompFileLoadDLLFail = 0x80110425,
    COMAdminErrCompFileGetClassObj = 0x80110426,
    COMAdminErrCompFileClassNotAvail = 0x80110427,
    COMAdminErrCompFileBadTLB = 0x80110428,
    COMAdminErrCompFileNotInstallable = 0x80110429,
    COMAdminErrNotChangeable = 0x8011042a,
    COMAdminErrNotDeletable = 0x8011042b,
    COMAdminErrSession = 0x8011042c,
    COMAdminErrCompMoveLocked = 0x8011042d,
    COMAdminErrCompMoveBadDest = 0x8011042e,
    COMAdminErrRegisterTLB = 0x80110430,
    COMAdminErrSystemApp = 0x80110433,
    COMAdminErrCompFileNoRegistrar = 0x80110434,
    COMAdminErrCoReqCompInstalled = 0x80110435,
    COMAdminErrServiceNotInstalled = 0x80110436,
    COMAdminErrPropertySaveFailed = 0x80110437,
    COMAdminErrObjectExists = 0x80110438,
    COMAdminErrComponentExists = 0x80110439,
    COMAdminErrRegFileCorrupt = 0x8011043b,
    COMAdminErrPropertyOverflow = 0x8011043c,
    COMAdminErrNotInRegistry = 0x8011043e,
    COMAdminErrObjectNotPoolable = 0x8011043f,
    COMAdminErrApplidMatchesClsid = 0x80110446,
    COMAdminErrRoleDoesNotExist = 0x80110447,
    COMAdminErrStartAppNeedsComponents = 0x80110448,
    COMAdminErrRequiresDifferentPlatform = 0x80110449,
    COMAdminErrQueuingServiceNotAvailable = 0x80110602,
    COMAdminErrObjectParentMissing = 0x80110808,
    COMAdminErrObjectDoesNotExist = 0x80110809,
    COMAdminErrCanNotExportAppProxy = 0x8011044a,
    COMAdminErrCanNotStartApp = 0x8011044b,
    COMAdminErrCanNotExportSystemApp = 0x8011044c,
    COMAdminErrCanNotSubscribeToComponent = 0x8011044d,
    COMAdminErrAppNotRunning = 0x8011080a,
    COMAdminErrEventClassCannotBeSubscriber = 0x8011044e,
    COMAdminErrLibAppProxyIncompatible = 0x8011044f,
    COMAdminErrBasePartitionOnly = 0x80110450,
    COMAdminErrDuplicatePartitionName = 0x80110457,
    COMAdminErrPartitionInUse = 0x80110459,
    COMAdminErrImportedComponentsNotAllowed = 0x8011045b,
    COMAdminErrRegdbNotInitialized = 0x80110472,
    COMAdminErrRegdbNotOpen = 0x80110473,
    COMAdminErrRegdbSystemErr = 0x80110474,
    COMAdminErrRegdbAlreadyRunning = 0x80110475,
    COMAdminErrMigVersionNotSupported = 0x80110480,
    COMAdminErrMigSchemaNotFound = 0x80110481,
    COMAdminErrCatBitnessMismatch = 0x80110482,
    COMAdminErrCatUnacceptableBitness = 0x80110483,
    COMAdminErrCatWrongAppBitnessBitness = 0x80110484,
    COMAdminErrCatPauseResumeNotSupported = 0x80110485,
    COMAdminErrCatServerFault = 0x80110486,
    COMAdminErrCantRecycleLibraryApps = 0x8011080f,
    COMAdminErrCantRecycleServiceApps = 0x80110811,
    COMAdminErrProcessAlreadyRecycled = 0x80110812,
    COMAdminErrPausedProcessMayNotBeRecycled = 0x80110813,
    COMAdminErrInvalidPartition = 0x8011080b,
    COMAdminErrPartitionMsiOnly = 0x80110819,
    COMAdminErrStartAppDisabled = 0x80110451,
    COMAdminErrCompMoveSource = 0x8011081c,
    COMAdminErrCompMoveDest = 0x8011081d,
    COMAdminErrCompMovePrivate = 0x8011081e,
    COMAdminErrCannotCopyEventClass = 0x80110820
} COMAdminErrorCodes;
typedef enum COMAdminComponentType {
    COMAdmin32BitComponent = 0x1,
    COMAdmin64BitComponent = 0x2
} COMAdminComponentType;
typedef enum COMAdminApplicationInstallOptions {
    COMAdminInstallNoUsers = 0,
    COMAdminInstallUsers = 1,
    COMAdminInstallForceOverwriteOfFiles = 2
} COMAdminApplicationInstallOptions;
typedef enum COMAdminApplicationExportOptions {
    COMAdminExportNoUsers = 0x0,
    COMAdminExportUsers = 0x1,
    COMAdminExportApplicationProxy = 0x2,
    COMAdminExportForceOverwriteOfFiles = 0x4,
    COMAdminExportIn10Format = 0x10
} COMAdminApplicationExportOptions;
typedef enum COMAdminThreadingModels {
    COMAdminThreadingModelApartment = 0,
    COMAdminThreadingModelFree = 1,
    COMAdminThreadingModelMain = 2,
    COMAdminThreadingModelBoth = 3,
    COMAdminThreadingModelNeutral = 4,
    COMAdminThreadingModelNotSpecified = 5
} COMAdminThreadingModels;
typedef enum COMAdminTransactionOptions {
    COMAdminTransactionIgnored = 0,
    COMAdminTransactionNone = 1,
    COMAdminTransactionSupported = 2,
    COMAdminTransactionRequired = 3,
    COMAdminTransactionRequiresNew = 4
} COMAdminTransactionOptions;
typedef enum COMAdminTxIsolationLevelOptions {
    COMAdminTxIsolationLevelAny = 0,
    COMAdminTxIsolationLevelReadUnCommitted = 1,
    COMAdminTxIsolationLevelReadCommitted = 2,
    COMAdminTxIsolationLevelRepeatableRead = 3,
    COMAdminTxIsolationLevelSerializable = 4
} COMAdminTxIsolationLevelOptions;
typedef enum COMAdminSynchronizationOptions {
    COMAdminSynchronizationIgnored = 0,
    COMAdminSynchronizationNone = 1,
    COMAdminSynchronizationSupported = 2,
    COMAdminSynchronizationRequired = 3,
    COMAdminSynchronizationRequiresNew = 4
} COMAdminSynchronizationOptions;
typedef enum COMAdminActivationOptions {
    COMAdminActivationInproc = 0,
    COMAdminActivationLocal = 1
} COMAdminActivationOptions;
typedef enum COMAdminAccessChecksLevelOptions {
    COMAdminAccessChecksApplicationLevel = 0,
    COMAdminAccessChecksApplicationComponentLevel = 1
} COMAdminAccessChecksLevelOptions;
typedef enum COMAdminAuthenticationLevelOptions {
    COMAdminAuthenticationDefault = 0,
    COMAdminAuthenticationNone = 1,
    COMAdminAuthenticationConnect = 2,
    COMAdminAuthenticationCall = 3,
    COMAdminAuthenticationPacket = 4,
    COMAdminAuthenticationIntegrity = 5,
    COMAdminAuthenticationPrivacy = 6
} COMAdminAuthenticationLevelOptions;
typedef enum COMAdminImpersonationLevelOptions {
    COMAdminImpersonationAnonymous = 1,
    COMAdminImpersonationIdentify = 2,
    COMAdminImpersonationImpersonate = 3,
    COMAdminImpersonationDelegate = 4
} COMAdminImpersonationLevelOptions;
typedef enum COMAdminAuthenticationCapabilitiesOptions {
    COMAdminAuthenticationCapabilitiesNone = 0x0,
    COMAdminAuthenticationCapabilitiesSecureReference = 0x2,
    COMAdminAuthenticationCapabilitiesStaticCloaking = 0x20,
    COMAdminAuthenticationCapabilitiesDynamicCloaking = 0x40
} COMAdminAuthenticationCapabilitiesOptions;
typedef enum COMAdminOS {
    COMAdminOSNotInitialized = 0,
    COMAdminOSWindows3_1 = 1,
    COMAdminOSWindows9x = 2,
    COMAdminOSWindows2000 = 3,
    COMAdminOSWindows2000AdvancedServer = 4,
    COMAdminOSWindows2000Unknown = 5,
    COMAdminOSUnknown = 6,
    COMAdminOSWindowsXPPersonal = 11,
    COMAdminOSWindowsXPProfessional = 12,
    COMAdminOSWindowsNETStandardServer = 13,
    COMAdminOSWindowsNETEnterpriseServer = 14,
    COMAdminOSWindowsNETDatacenterServer = 15,
    COMAdminOSWindowsNETWebServer = 16,
    COMAdminOSWindowsLonghornPersonal = 17,
    COMAdminOSWindowsLonghornProfessional = 18,
    COMAdminOSWindowsLonghornStandardServer = 19,
    COMAdminOSWindowsLonghornEnterpriseServer = 20,
    COMAdminOSWindowsLonghornDatacenterServer = 21,
    COMAdminOSWindowsLonghornWebServer = 22,
    COMAdminOSWindows7Personal = 23,
    COMAdminOSWindows7Professional = 24,
    COMAdminOSWindows7StandardServer = 25,
    COMAdminOSWindows7EnterpriseServer = 26,
    COMAdminOSWindows7DatacenterServer = 27,
    COMAdminOSWindows7WebServer = 28,
    COMAdminOSWindows8Personal = 29,
    COMAdminOSWindows8Professional = 30,
    COMAdminOSWindows8StandardServer = 31,
    COMAdminOSWindows8EnterpriseServer = 32,
    COMAdminOSWindows8DatacenterServer = 33,
    COMAdminOSWindows8WebServer = 34
} COMAdminOS;
typedef enum COMAdminServiceOptions {
    COMAdminServiceLoadBalanceRouter = 1
} COMAdminServiceOptions;
typedef enum COMAdminServiceStatusOptions {
    COMAdminServiceStopped = 0,
    COMAdminServiceStartPending = 1,
    COMAdminServiceStopPending = 2,
    COMAdminServiceRunning = 3,
    COMAdminServiceContinuePending = 4,
    COMAdminServicePausePending = 5,
    COMAdminServicePaused = 6,
    COMAdminServiceUnknownState = 7
} COMAdminServiceStatusOptions;
typedef enum COMAdminComponentFlags {
    COMAdminCompFlagTypeInfoFound = 0x1,
    COMAdminCompFlagCOMPlusPropertiesFound = 0x2,
    COMAdminCompFlagProxyFound = 0x4,
    COMAdminCompFlagInterfacesFound = 0x8,
    COMAdminCompFlagAlreadyInstalled = 0x10,
    COMAdminCompFlagNotInApplication = 0x20
} COMAdminComponentFlags;
typedef enum COMAdminQCMessageAuthenticateOptions {
    COMAdminQCMessageAuthenticateSecureApps = 0,
    COMAdminQCMessageAuthenticateOff = 1,
    COMAdminQCMessageAuthenticateOn = 2
} COMAdminQCMessageAuthenticateOptions;
typedef enum COMAdminFileFlags {
    COMAdminFileFlagLoadable = 0x1,
    COMAdminFileFlagCOM = 0x2,
    COMAdminFileFlagContainsPS = 0x4,
    COMAdminFileFlagContainsComp = 0x8,
    COMAdminFileFlagContainsTLB = 0x10,
    COMAdminFileFlagSelfReg = 0x20,
    COMAdminFileFlagSelfUnReg = 0x40,
    COMAdminFileFlagUnloadableDLL = 0x80,
    COMAdminFileFlagDoesNotExist = 0x100,
    COMAdminFileFlagAlreadyInstalled = 0x200,
    COMAdminFileFlagBadTLB = 0x400,
    COMAdminFileFlagGetClassObjFailed = 0x800,
    COMAdminFileFlagClassNotAvailable = 0x1000,
    COMAdminFileFlagRegistrar = 0x2000,
    COMAdminFileFlagNoRegistrar = 0x4000,
    COMAdminFileFlagDLLRegsvrFailed = 0x8000,
    COMAdminFileFlagRegTLBFailed = 0x10000,
    COMAdminFileFlagRegistrarFailed = 0x20000,
    COMAdminFileFlagError = 0x40000
} COMAdminFileFlags;
/*****************************************************************************
 * ICOMAdminCatalog interface
 */
#ifndef __ICOMAdminCatalog_INTERFACE_DEFINED__
#define __ICOMAdminCatalog_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICOMAdminCatalog, 0xdd662187, 0xdfc2, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dd662187-dfc2-11d1-a2cf-00805fc79235")
ICOMAdminCatalog : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetCollection(
        BSTR bstrCollName,
        IDispatch **ppCatalogCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE Connect(
        BSTR bstrCatalogServerName,
        IDispatch **ppCatalogCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MajorVersion(
        LONG *plMajorVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MinorVersion(
        LONG *plMinorVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCollectionByQuery(
        BSTR bstrCollName,
        SAFEARRAY **ppsaVarQuery,
        IDispatch **ppCatalogCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportComponent(
        BSTR bstrApplIDOrName,
        BSTR bstrCLSIDOrProgID) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallComponent(
        BSTR bstrApplIDOrName,
        BSTR bstrDLL,
        BSTR bstrTLB,
        BSTR bstrPSDLL) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShutdownApplication(
        BSTR bstrApplIDOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportApplication(
        BSTR bstrApplIDOrName,
        BSTR bstrApplicationFile,
        LONG lOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallApplication(
        BSTR bstrApplicationFile,
        BSTR bstrDestinationDirectory,
        LONG lOptions,
        BSTR bstrUserId,
        BSTR bstrPassword,
        BSTR bstrRSN) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopRouter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshRouter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartRouter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reserved1(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reserved2(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallMultipleComponents(
        BSTR bstrApplIDOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMultipleComponentsInfo(
        BSTR bstrApplIdOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDs,
        SAFEARRAY **ppsaVarClassNames,
        SAFEARRAY **ppsaVarFileFlags,
        SAFEARRAY **ppsaVarComponentFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshComponents(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE BackupREGDB(
        BSTR bstrBackupFilePath) = 0;

    virtual HRESULT STDMETHODCALLTYPE RestoreREGDB(
        BSTR bstrBackupFilePath) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryApplicationFile(
        BSTR bstrApplicationFile,
        BSTR *pbstrApplicationName,
        BSTR *pbstrApplicationDescription,
        VARIANT_BOOL *pbHasUsers,
        VARIANT_BOOL *pbIsProxy,
        SAFEARRAY **ppsaVarFileNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartApplication(
        BSTR bstrApplIdOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE ServiceCheck(
        LONG lService,
        LONG *plStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallMultipleEventClasses(
        BSTR bstrApplIdOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDS) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallEventClass(
        BSTR bstrApplIdOrName,
        BSTR bstrDLL,
        BSTR bstrTLB,
        BSTR bstrPSDLL) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventClassesForIID(
        BSTR bstrIID,
        SAFEARRAY **ppsaVarCLSIDs,
        SAFEARRAY **ppsaVarProgIDs,
        SAFEARRAY **ppsaVarDescriptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICOMAdminCatalog, 0xdd662187, 0xdfc2, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35)
#endif
#else
typedef struct ICOMAdminCatalogVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICOMAdminCatalog *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICOMAdminCatalog *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICOMAdminCatalog *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ICOMAdminCatalog *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ICOMAdminCatalog *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ICOMAdminCatalog *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ICOMAdminCatalog *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ICOMAdminCatalog methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCollection)(
        ICOMAdminCatalog *This,
        BSTR bstrCollName,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *Connect)(
        ICOMAdminCatalog *This,
        BSTR bstrCatalogServerName,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *get_MajorVersion)(
        ICOMAdminCatalog *This,
        LONG *plMajorVersion);

    HRESULT (STDMETHODCALLTYPE *get_MinorVersion)(
        ICOMAdminCatalog *This,
        LONG *plMinorVersion);

    HRESULT (STDMETHODCALLTYPE *GetCollectionByQuery)(
        ICOMAdminCatalog *This,
        BSTR bstrCollName,
        SAFEARRAY **ppsaVarQuery,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *ImportComponent)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIDOrName,
        BSTR bstrCLSIDOrProgID);

    HRESULT (STDMETHODCALLTYPE *InstallComponent)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIDOrName,
        BSTR bstrDLL,
        BSTR bstrTLB,
        BSTR bstrPSDLL);

    HRESULT (STDMETHODCALLTYPE *ShutdownApplication)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIDOrName);

    HRESULT (STDMETHODCALLTYPE *ExportApplication)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIDOrName,
        BSTR bstrApplicationFile,
        LONG lOptions);

    HRESULT (STDMETHODCALLTYPE *InstallApplication)(
        ICOMAdminCatalog *This,
        BSTR bstrApplicationFile,
        BSTR bstrDestinationDirectory,
        LONG lOptions,
        BSTR bstrUserId,
        BSTR bstrPassword,
        BSTR bstrRSN);

    HRESULT (STDMETHODCALLTYPE *StopRouter)(
        ICOMAdminCatalog *This);

    HRESULT (STDMETHODCALLTYPE *RefreshRouter)(
        ICOMAdminCatalog *This);

    HRESULT (STDMETHODCALLTYPE *StartRouter)(
        ICOMAdminCatalog *This);

    HRESULT (STDMETHODCALLTYPE *Reserved1)(
        ICOMAdminCatalog *This);

    HRESULT (STDMETHODCALLTYPE *Reserved2)(
        ICOMAdminCatalog *This);

    HRESULT (STDMETHODCALLTYPE *InstallMultipleComponents)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIDOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDs);

    HRESULT (STDMETHODCALLTYPE *GetMultipleComponentsInfo)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIdOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDs,
        SAFEARRAY **ppsaVarClassNames,
        SAFEARRAY **ppsaVarFileFlags,
        SAFEARRAY **ppsaVarComponentFlags);

    HRESULT (STDMETHODCALLTYPE *RefreshComponents)(
        ICOMAdminCatalog *This);

    HRESULT (STDMETHODCALLTYPE *BackupREGDB)(
        ICOMAdminCatalog *This,
        BSTR bstrBackupFilePath);

    HRESULT (STDMETHODCALLTYPE *RestoreREGDB)(
        ICOMAdminCatalog *This,
        BSTR bstrBackupFilePath);

    HRESULT (STDMETHODCALLTYPE *QueryApplicationFile)(
        ICOMAdminCatalog *This,
        BSTR bstrApplicationFile,
        BSTR *pbstrApplicationName,
        BSTR *pbstrApplicationDescription,
        VARIANT_BOOL *pbHasUsers,
        VARIANT_BOOL *pbIsProxy,
        SAFEARRAY **ppsaVarFileNames);

    HRESULT (STDMETHODCALLTYPE *StartApplication)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIdOrName);

    HRESULT (STDMETHODCALLTYPE *ServiceCheck)(
        ICOMAdminCatalog *This,
        LONG lService,
        LONG *plStatus);

    HRESULT (STDMETHODCALLTYPE *InstallMultipleEventClasses)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIdOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDS);

    HRESULT (STDMETHODCALLTYPE *InstallEventClass)(
        ICOMAdminCatalog *This,
        BSTR bstrApplIdOrName,
        BSTR bstrDLL,
        BSTR bstrTLB,
        BSTR bstrPSDLL);

    HRESULT (STDMETHODCALLTYPE *GetEventClassesForIID)(
        ICOMAdminCatalog *This,
        BSTR bstrIID,
        SAFEARRAY **ppsaVarCLSIDs,
        SAFEARRAY **ppsaVarProgIDs,
        SAFEARRAY **ppsaVarDescriptions);

    END_INTERFACE
} ICOMAdminCatalogVtbl;

interface ICOMAdminCatalog {
    CONST_VTBL ICOMAdminCatalogVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICOMAdminCatalog_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICOMAdminCatalog_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICOMAdminCatalog_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ICOMAdminCatalog_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ICOMAdminCatalog_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ICOMAdminCatalog_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ICOMAdminCatalog_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ICOMAdminCatalog methods ***/
#define ICOMAdminCatalog_GetCollection(This,bstrCollName,ppCatalogCollection) (This)->lpVtbl->GetCollection(This,bstrCollName,ppCatalogCollection)
#define ICOMAdminCatalog_Connect(This,bstrCatalogServerName,ppCatalogCollection) (This)->lpVtbl->Connect(This,bstrCatalogServerName,ppCatalogCollection)
#define ICOMAdminCatalog_get_MajorVersion(This,plMajorVersion) (This)->lpVtbl->get_MajorVersion(This,plMajorVersion)
#define ICOMAdminCatalog_get_MinorVersion(This,plMinorVersion) (This)->lpVtbl->get_MinorVersion(This,plMinorVersion)
#define ICOMAdminCatalog_GetCollectionByQuery(This,bstrCollName,ppsaVarQuery,ppCatalogCollection) (This)->lpVtbl->GetCollectionByQuery(This,bstrCollName,ppsaVarQuery,ppCatalogCollection)
#define ICOMAdminCatalog_ImportComponent(This,bstrApplIDOrName,bstrCLSIDOrProgID) (This)->lpVtbl->ImportComponent(This,bstrApplIDOrName,bstrCLSIDOrProgID)
#define ICOMAdminCatalog_InstallComponent(This,bstrApplIDOrName,bstrDLL,bstrTLB,bstrPSDLL) (This)->lpVtbl->InstallComponent(This,bstrApplIDOrName,bstrDLL,bstrTLB,bstrPSDLL)
#define ICOMAdminCatalog_ShutdownApplication(This,bstrApplIDOrName) (This)->lpVtbl->ShutdownApplication(This,bstrApplIDOrName)
#define ICOMAdminCatalog_ExportApplication(This,bstrApplIDOrName,bstrApplicationFile,lOptions) (This)->lpVtbl->ExportApplication(This,bstrApplIDOrName,bstrApplicationFile,lOptions)
#define ICOMAdminCatalog_InstallApplication(This,bstrApplicationFile,bstrDestinationDirectory,lOptions,bstrUserId,bstrPassword,bstrRSN) (This)->lpVtbl->InstallApplication(This,bstrApplicationFile,bstrDestinationDirectory,lOptions,bstrUserId,bstrPassword,bstrRSN)
#define ICOMAdminCatalog_StopRouter(This) (This)->lpVtbl->StopRouter(This)
#define ICOMAdminCatalog_RefreshRouter(This) (This)->lpVtbl->RefreshRouter(This)
#define ICOMAdminCatalog_StartRouter(This) (This)->lpVtbl->StartRouter(This)
#define ICOMAdminCatalog_Reserved1(This) (This)->lpVtbl->Reserved1(This)
#define ICOMAdminCatalog_Reserved2(This) (This)->lpVtbl->Reserved2(This)
#define ICOMAdminCatalog_InstallMultipleComponents(This,bstrApplIDOrName,ppsaVarFileNames,ppsaVarCLSIDs) (This)->lpVtbl->InstallMultipleComponents(This,bstrApplIDOrName,ppsaVarFileNames,ppsaVarCLSIDs)
#define ICOMAdminCatalog_GetMultipleComponentsInfo(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDs,ppsaVarClassNames,ppsaVarFileFlags,ppsaVarComponentFlags) (This)->lpVtbl->GetMultipleComponentsInfo(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDs,ppsaVarClassNames,ppsaVarFileFlags,ppsaVarComponentFlags)
#define ICOMAdminCatalog_RefreshComponents(This) (This)->lpVtbl->RefreshComponents(This)
#define ICOMAdminCatalog_BackupREGDB(This,bstrBackupFilePath) (This)->lpVtbl->BackupREGDB(This,bstrBackupFilePath)
#define ICOMAdminCatalog_RestoreREGDB(This,bstrBackupFilePath) (This)->lpVtbl->RestoreREGDB(This,bstrBackupFilePath)
#define ICOMAdminCatalog_QueryApplicationFile(This,bstrApplicationFile,pbstrApplicationName,pbstrApplicationDescription,pbHasUsers,pbIsProxy,ppsaVarFileNames) (This)->lpVtbl->QueryApplicationFile(This,bstrApplicationFile,pbstrApplicationName,pbstrApplicationDescription,pbHasUsers,pbIsProxy,ppsaVarFileNames)
#define ICOMAdminCatalog_StartApplication(This,bstrApplIdOrName) (This)->lpVtbl->StartApplication(This,bstrApplIdOrName)
#define ICOMAdminCatalog_ServiceCheck(This,lService,plStatus) (This)->lpVtbl->ServiceCheck(This,lService,plStatus)
#define ICOMAdminCatalog_InstallMultipleEventClasses(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDS) (This)->lpVtbl->InstallMultipleEventClasses(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDS)
#define ICOMAdminCatalog_InstallEventClass(This,bstrApplIdOrName,bstrDLL,bstrTLB,bstrPSDLL) (This)->lpVtbl->InstallEventClass(This,bstrApplIdOrName,bstrDLL,bstrTLB,bstrPSDLL)
#define ICOMAdminCatalog_GetEventClassesForIID(This,bstrIID,ppsaVarCLSIDs,ppsaVarProgIDs,ppsaVarDescriptions) (This)->lpVtbl->GetEventClassesForIID(This,bstrIID,ppsaVarCLSIDs,ppsaVarProgIDs,ppsaVarDescriptions)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog_QueryInterface(ICOMAdminCatalog* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ICOMAdminCatalog_AddRef(ICOMAdminCatalog* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ICOMAdminCatalog_Release(ICOMAdminCatalog* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetTypeInfoCount(ICOMAdminCatalog* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetTypeInfo(ICOMAdminCatalog* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetIDsOfNames(ICOMAdminCatalog* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_Invoke(ICOMAdminCatalog* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ICOMAdminCatalog methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetCollection(ICOMAdminCatalog* This,BSTR bstrCollName,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->GetCollection(This,bstrCollName,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_Connect(ICOMAdminCatalog* This,BSTR bstrCatalogServerName,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->Connect(This,bstrCatalogServerName,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_get_MajorVersion(ICOMAdminCatalog* This,LONG *plMajorVersion) {
    return This->lpVtbl->get_MajorVersion(This,plMajorVersion);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_get_MinorVersion(ICOMAdminCatalog* This,LONG *plMinorVersion) {
    return This->lpVtbl->get_MinorVersion(This,plMinorVersion);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetCollectionByQuery(ICOMAdminCatalog* This,BSTR bstrCollName,SAFEARRAY **ppsaVarQuery,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->GetCollectionByQuery(This,bstrCollName,ppsaVarQuery,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_ImportComponent(ICOMAdminCatalog* This,BSTR bstrApplIDOrName,BSTR bstrCLSIDOrProgID) {
    return This->lpVtbl->ImportComponent(This,bstrApplIDOrName,bstrCLSIDOrProgID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_InstallComponent(ICOMAdminCatalog* This,BSTR bstrApplIDOrName,BSTR bstrDLL,BSTR bstrTLB,BSTR bstrPSDLL) {
    return This->lpVtbl->InstallComponent(This,bstrApplIDOrName,bstrDLL,bstrTLB,bstrPSDLL);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_ShutdownApplication(ICOMAdminCatalog* This,BSTR bstrApplIDOrName) {
    return This->lpVtbl->ShutdownApplication(This,bstrApplIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_ExportApplication(ICOMAdminCatalog* This,BSTR bstrApplIDOrName,BSTR bstrApplicationFile,LONG lOptions) {
    return This->lpVtbl->ExportApplication(This,bstrApplIDOrName,bstrApplicationFile,lOptions);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_InstallApplication(ICOMAdminCatalog* This,BSTR bstrApplicationFile,BSTR bstrDestinationDirectory,LONG lOptions,BSTR bstrUserId,BSTR bstrPassword,BSTR bstrRSN) {
    return This->lpVtbl->InstallApplication(This,bstrApplicationFile,bstrDestinationDirectory,lOptions,bstrUserId,bstrPassword,bstrRSN);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_StopRouter(ICOMAdminCatalog* This) {
    return This->lpVtbl->StopRouter(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_RefreshRouter(ICOMAdminCatalog* This) {
    return This->lpVtbl->RefreshRouter(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_StartRouter(ICOMAdminCatalog* This) {
    return This->lpVtbl->StartRouter(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_Reserved1(ICOMAdminCatalog* This) {
    return This->lpVtbl->Reserved1(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_Reserved2(ICOMAdminCatalog* This) {
    return This->lpVtbl->Reserved2(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_InstallMultipleComponents(ICOMAdminCatalog* This,BSTR bstrApplIDOrName,SAFEARRAY **ppsaVarFileNames,SAFEARRAY **ppsaVarCLSIDs) {
    return This->lpVtbl->InstallMultipleComponents(This,bstrApplIDOrName,ppsaVarFileNames,ppsaVarCLSIDs);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetMultipleComponentsInfo(ICOMAdminCatalog* This,BSTR bstrApplIdOrName,SAFEARRAY **ppsaVarFileNames,SAFEARRAY **ppsaVarCLSIDs,SAFEARRAY **ppsaVarClassNames,SAFEARRAY **ppsaVarFileFlags,SAFEARRAY **ppsaVarComponentFlags) {
    return This->lpVtbl->GetMultipleComponentsInfo(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDs,ppsaVarClassNames,ppsaVarFileFlags,ppsaVarComponentFlags);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_RefreshComponents(ICOMAdminCatalog* This) {
    return This->lpVtbl->RefreshComponents(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_BackupREGDB(ICOMAdminCatalog* This,BSTR bstrBackupFilePath) {
    return This->lpVtbl->BackupREGDB(This,bstrBackupFilePath);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_RestoreREGDB(ICOMAdminCatalog* This,BSTR bstrBackupFilePath) {
    return This->lpVtbl->RestoreREGDB(This,bstrBackupFilePath);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_QueryApplicationFile(ICOMAdminCatalog* This,BSTR bstrApplicationFile,BSTR *pbstrApplicationName,BSTR *pbstrApplicationDescription,VARIANT_BOOL *pbHasUsers,VARIANT_BOOL *pbIsProxy,SAFEARRAY **ppsaVarFileNames) {
    return This->lpVtbl->QueryApplicationFile(This,bstrApplicationFile,pbstrApplicationName,pbstrApplicationDescription,pbHasUsers,pbIsProxy,ppsaVarFileNames);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_StartApplication(ICOMAdminCatalog* This,BSTR bstrApplIdOrName) {
    return This->lpVtbl->StartApplication(This,bstrApplIdOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_ServiceCheck(ICOMAdminCatalog* This,LONG lService,LONG *plStatus) {
    return This->lpVtbl->ServiceCheck(This,lService,plStatus);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_InstallMultipleEventClasses(ICOMAdminCatalog* This,BSTR bstrApplIdOrName,SAFEARRAY **ppsaVarFileNames,SAFEARRAY **ppsaVarCLSIDS) {
    return This->lpVtbl->InstallMultipleEventClasses(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDS);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_InstallEventClass(ICOMAdminCatalog* This,BSTR bstrApplIdOrName,BSTR bstrDLL,BSTR bstrTLB,BSTR bstrPSDLL) {
    return This->lpVtbl->InstallEventClass(This,bstrApplIdOrName,bstrDLL,bstrTLB,bstrPSDLL);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog_GetEventClassesForIID(ICOMAdminCatalog* This,BSTR bstrIID,SAFEARRAY **ppsaVarCLSIDs,SAFEARRAY **ppsaVarProgIDs,SAFEARRAY **ppsaVarDescriptions) {
    return This->lpVtbl->GetEventClassesForIID(This,bstrIID,ppsaVarCLSIDs,ppsaVarProgIDs,ppsaVarDescriptions);
}
#endif
#endif

#endif


#endif  /* __ICOMAdminCatalog_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICOMAdminCatalog2 interface
 */
#ifndef __ICOMAdminCatalog2_INTERFACE_DEFINED__
#define __ICOMAdminCatalog2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICOMAdminCatalog2, 0x790c6e0b, 0x9194, 0x4cc9, 0x94,0x26, 0xa4,0x8a,0x63,0x18,0x56,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("790c6e0b-9194-4cc9-9426-a48a63185696")
ICOMAdminCatalog2 : public ICOMAdminCatalog
{
    virtual HRESULT STDMETHODCALLTYPE GetCollectionByQuery2(
        BSTR bstrCollectionName,
        VARIANT *pVarQueryStrings,
        IDispatch **ppCatalogCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetApplicationInstanceIDFromProcessID(
        LONG lProcessID,
        BSTR *pbstrApplicationInstanceID) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShutdownApplicationInstances(
        VARIANT *pVarApplicationInstanceID) = 0;

    virtual HRESULT STDMETHODCALLTYPE PauseApplicationInstances(
        VARIANT *pVarApplicationInstanceID) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResumeApplicationInstances(
        VARIANT *pVarApplicationInstanceID) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecycleApplicationInstances(
        VARIANT *pVarApplicationInstanceID,
        LONG lReasonCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE AreApplicationInstancesPaused(
        VARIANT *pVarApplicationInstanceID,
        VARIANT_BOOL *pVarBoolPaused) = 0;

    virtual HRESULT STDMETHODCALLTYPE DumpApplicationInstance(
        BSTR bstrApplicationInstanceID,
        BSTR bstrDirectory,
        LONG lMaxImages,
        BSTR *pbstrDumpFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsApplicationInstanceDumpSupported(
        VARIANT_BOOL *pVarBoolDumpSupported) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateServiceForApplication(
        BSTR bstrApplicationIDOrName,
        BSTR bstrServiceName,
        BSTR bstrStartType,
        BSTR bstrErrorControl,
        BSTR bstrDependencies,
        BSTR bstrRunAs,
        BSTR bstrPassword,
        VARIANT_BOOL bDesktopOk) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteServiceForApplication(
        BSTR bstrApplicationIDOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartitionID(
        BSTR bstrApplicationIDOrName,
        BSTR *pbstrPartitionID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartitionName(
        BSTR bstrApplicationIDOrName,
        BSTR *pbstrPartitionName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CurrentPartition(
        BSTR bstrPartitionIDOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentPartitionID(
        BSTR *pbstrPartitionID) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentPartitionName(
        BSTR *pbstrPartitionName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GlobalPartitionID(
        BSTR *pbstrGlobalPartitionID) = 0;

    virtual HRESULT STDMETHODCALLTYPE FlushPartitionCache(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyApplications(
        BSTR bstrSourcePartitionIDOrName,
        VARIANT *pVarApplicationID,
        BSTR bstrDestinationPartitionIDOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyComponents(
        BSTR bstrSourceApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        BSTR bstrDestinationApplicationIDOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveComponents(
        BSTR bstrSourceApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        BSTR bstrDestinationApplicationIDOrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE AliasComponent(
        BSTR bstrSrcApplicationIDOrName,
        BSTR bstrCLSIDOrProgID,
        BSTR bstrDestApplicationIDOrName,
        BSTR bstrNewProgId,
        BSTR bstrNewClsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSafeToDelete(
        BSTR bstrDllName,
        COMAdminInUse *pCOMAdminInUse) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportUnconfiguredComponents(
        BSTR bstrApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        VARIANT *pVarComponentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE PromoteUnconfiguredComponents(
        BSTR bstrApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        VARIANT *pVarComponentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportComponents(
        BSTR bstrApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        VARIANT *pVarComponentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Is64BitCatalogServer(
        VARIANT_BOOL *pbIs64Bit) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportPartition(
        BSTR bstrPartitionIDOrName,
        BSTR bstrPartitionFileName,
        LONG lOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallPartition(
        BSTR bstrFileName,
        BSTR bstrDestDirectory,
        LONG lOptions,
        BSTR bstrUserID,
        BSTR bstrPassword,
        BSTR bstrRSN) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryApplicationFile2(
        BSTR bstrApplicationFile,
        IDispatch **ppFilesForImport) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComponentVersionCount(
        BSTR bstrCLSIDOrProgID,
        LONG *plVersionCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICOMAdminCatalog2, 0x790c6e0b, 0x9194, 0x4cc9, 0x94,0x26, 0xa4,0x8a,0x63,0x18,0x56,0x96)
#endif
#else
typedef struct ICOMAdminCatalog2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICOMAdminCatalog2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICOMAdminCatalog2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICOMAdminCatalog2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ICOMAdminCatalog2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ICOMAdminCatalog2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ICOMAdminCatalog2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ICOMAdminCatalog2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ICOMAdminCatalog methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCollection)(
        ICOMAdminCatalog2 *This,
        BSTR bstrCollName,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *Connect)(
        ICOMAdminCatalog2 *This,
        BSTR bstrCatalogServerName,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *get_MajorVersion)(
        ICOMAdminCatalog2 *This,
        LONG *plMajorVersion);

    HRESULT (STDMETHODCALLTYPE *get_MinorVersion)(
        ICOMAdminCatalog2 *This,
        LONG *plMinorVersion);

    HRESULT (STDMETHODCALLTYPE *GetCollectionByQuery)(
        ICOMAdminCatalog2 *This,
        BSTR bstrCollName,
        SAFEARRAY **ppsaVarQuery,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *ImportComponent)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIDOrName,
        BSTR bstrCLSIDOrProgID);

    HRESULT (STDMETHODCALLTYPE *InstallComponent)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIDOrName,
        BSTR bstrDLL,
        BSTR bstrTLB,
        BSTR bstrPSDLL);

    HRESULT (STDMETHODCALLTYPE *ShutdownApplication)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIDOrName);

    HRESULT (STDMETHODCALLTYPE *ExportApplication)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIDOrName,
        BSTR bstrApplicationFile,
        LONG lOptions);

    HRESULT (STDMETHODCALLTYPE *InstallApplication)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationFile,
        BSTR bstrDestinationDirectory,
        LONG lOptions,
        BSTR bstrUserId,
        BSTR bstrPassword,
        BSTR bstrRSN);

    HRESULT (STDMETHODCALLTYPE *StopRouter)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *RefreshRouter)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *StartRouter)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *Reserved1)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *Reserved2)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *InstallMultipleComponents)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIDOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDs);

    HRESULT (STDMETHODCALLTYPE *GetMultipleComponentsInfo)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIdOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDs,
        SAFEARRAY **ppsaVarClassNames,
        SAFEARRAY **ppsaVarFileFlags,
        SAFEARRAY **ppsaVarComponentFlags);

    HRESULT (STDMETHODCALLTYPE *RefreshComponents)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *BackupREGDB)(
        ICOMAdminCatalog2 *This,
        BSTR bstrBackupFilePath);

    HRESULT (STDMETHODCALLTYPE *RestoreREGDB)(
        ICOMAdminCatalog2 *This,
        BSTR bstrBackupFilePath);

    HRESULT (STDMETHODCALLTYPE *QueryApplicationFile)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationFile,
        BSTR *pbstrApplicationName,
        BSTR *pbstrApplicationDescription,
        VARIANT_BOOL *pbHasUsers,
        VARIANT_BOOL *pbIsProxy,
        SAFEARRAY **ppsaVarFileNames);

    HRESULT (STDMETHODCALLTYPE *StartApplication)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIdOrName);

    HRESULT (STDMETHODCALLTYPE *ServiceCheck)(
        ICOMAdminCatalog2 *This,
        LONG lService,
        LONG *plStatus);

    HRESULT (STDMETHODCALLTYPE *InstallMultipleEventClasses)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIdOrName,
        SAFEARRAY **ppsaVarFileNames,
        SAFEARRAY **ppsaVarCLSIDS);

    HRESULT (STDMETHODCALLTYPE *InstallEventClass)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplIdOrName,
        BSTR bstrDLL,
        BSTR bstrTLB,
        BSTR bstrPSDLL);

    HRESULT (STDMETHODCALLTYPE *GetEventClassesForIID)(
        ICOMAdminCatalog2 *This,
        BSTR bstrIID,
        SAFEARRAY **ppsaVarCLSIDs,
        SAFEARRAY **ppsaVarProgIDs,
        SAFEARRAY **ppsaVarDescriptions);

    /*** ICOMAdminCatalog2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCollectionByQuery2)(
        ICOMAdminCatalog2 *This,
        BSTR bstrCollectionName,
        VARIANT *pVarQueryStrings,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *GetApplicationInstanceIDFromProcessID)(
        ICOMAdminCatalog2 *This,
        LONG lProcessID,
        BSTR *pbstrApplicationInstanceID);

    HRESULT (STDMETHODCALLTYPE *ShutdownApplicationInstances)(
        ICOMAdminCatalog2 *This,
        VARIANT *pVarApplicationInstanceID);

    HRESULT (STDMETHODCALLTYPE *PauseApplicationInstances)(
        ICOMAdminCatalog2 *This,
        VARIANT *pVarApplicationInstanceID);

    HRESULT (STDMETHODCALLTYPE *ResumeApplicationInstances)(
        ICOMAdminCatalog2 *This,
        VARIANT *pVarApplicationInstanceID);

    HRESULT (STDMETHODCALLTYPE *RecycleApplicationInstances)(
        ICOMAdminCatalog2 *This,
        VARIANT *pVarApplicationInstanceID,
        LONG lReasonCode);

    HRESULT (STDMETHODCALLTYPE *AreApplicationInstancesPaused)(
        ICOMAdminCatalog2 *This,
        VARIANT *pVarApplicationInstanceID,
        VARIANT_BOOL *pVarBoolPaused);

    HRESULT (STDMETHODCALLTYPE *DumpApplicationInstance)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationInstanceID,
        BSTR bstrDirectory,
        LONG lMaxImages,
        BSTR *pbstrDumpFile);

    HRESULT (STDMETHODCALLTYPE *get_IsApplicationInstanceDumpSupported)(
        ICOMAdminCatalog2 *This,
        VARIANT_BOOL *pVarBoolDumpSupported);

    HRESULT (STDMETHODCALLTYPE *CreateServiceForApplication)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName,
        BSTR bstrServiceName,
        BSTR bstrStartType,
        BSTR bstrErrorControl,
        BSTR bstrDependencies,
        BSTR bstrRunAs,
        BSTR bstrPassword,
        VARIANT_BOOL bDesktopOk);

    HRESULT (STDMETHODCALLTYPE *DeleteServiceForApplication)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName);

    HRESULT (STDMETHODCALLTYPE *GetPartitionID)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName,
        BSTR *pbstrPartitionID);

    HRESULT (STDMETHODCALLTYPE *GetPartitionName)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName,
        BSTR *pbstrPartitionName);

    HRESULT (STDMETHODCALLTYPE *put_CurrentPartition)(
        ICOMAdminCatalog2 *This,
        BSTR bstrPartitionIDOrName);

    HRESULT (STDMETHODCALLTYPE *get_CurrentPartitionID)(
        ICOMAdminCatalog2 *This,
        BSTR *pbstrPartitionID);

    HRESULT (STDMETHODCALLTYPE *get_CurrentPartitionName)(
        ICOMAdminCatalog2 *This,
        BSTR *pbstrPartitionName);

    HRESULT (STDMETHODCALLTYPE *get_GlobalPartitionID)(
        ICOMAdminCatalog2 *This,
        BSTR *pbstrGlobalPartitionID);

    HRESULT (STDMETHODCALLTYPE *FlushPartitionCache)(
        ICOMAdminCatalog2 *This);

    HRESULT (STDMETHODCALLTYPE *CopyApplications)(
        ICOMAdminCatalog2 *This,
        BSTR bstrSourcePartitionIDOrName,
        VARIANT *pVarApplicationID,
        BSTR bstrDestinationPartitionIDOrName);

    HRESULT (STDMETHODCALLTYPE *CopyComponents)(
        ICOMAdminCatalog2 *This,
        BSTR bstrSourceApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        BSTR bstrDestinationApplicationIDOrName);

    HRESULT (STDMETHODCALLTYPE *MoveComponents)(
        ICOMAdminCatalog2 *This,
        BSTR bstrSourceApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        BSTR bstrDestinationApplicationIDOrName);

    HRESULT (STDMETHODCALLTYPE *AliasComponent)(
        ICOMAdminCatalog2 *This,
        BSTR bstrSrcApplicationIDOrName,
        BSTR bstrCLSIDOrProgID,
        BSTR bstrDestApplicationIDOrName,
        BSTR bstrNewProgId,
        BSTR bstrNewClsid);

    HRESULT (STDMETHODCALLTYPE *IsSafeToDelete)(
        ICOMAdminCatalog2 *This,
        BSTR bstrDllName,
        COMAdminInUse *pCOMAdminInUse);

    HRESULT (STDMETHODCALLTYPE *ImportUnconfiguredComponents)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        VARIANT *pVarComponentType);

    HRESULT (STDMETHODCALLTYPE *PromoteUnconfiguredComponents)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        VARIANT *pVarComponentType);

    HRESULT (STDMETHODCALLTYPE *ImportComponents)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationIDOrName,
        VARIANT *pVarCLSIDOrProgID,
        VARIANT *pVarComponentType);

    HRESULT (STDMETHODCALLTYPE *get_Is64BitCatalogServer)(
        ICOMAdminCatalog2 *This,
        VARIANT_BOOL *pbIs64Bit);

    HRESULT (STDMETHODCALLTYPE *ExportPartition)(
        ICOMAdminCatalog2 *This,
        BSTR bstrPartitionIDOrName,
        BSTR bstrPartitionFileName,
        LONG lOptions);

    HRESULT (STDMETHODCALLTYPE *InstallPartition)(
        ICOMAdminCatalog2 *This,
        BSTR bstrFileName,
        BSTR bstrDestDirectory,
        LONG lOptions,
        BSTR bstrUserID,
        BSTR bstrPassword,
        BSTR bstrRSN);

    HRESULT (STDMETHODCALLTYPE *QueryApplicationFile2)(
        ICOMAdminCatalog2 *This,
        BSTR bstrApplicationFile,
        IDispatch **ppFilesForImport);

    HRESULT (STDMETHODCALLTYPE *GetComponentVersionCount)(
        ICOMAdminCatalog2 *This,
        BSTR bstrCLSIDOrProgID,
        LONG *plVersionCount);

    END_INTERFACE
} ICOMAdminCatalog2Vtbl;

interface ICOMAdminCatalog2 {
    CONST_VTBL ICOMAdminCatalog2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICOMAdminCatalog2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICOMAdminCatalog2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICOMAdminCatalog2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ICOMAdminCatalog2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ICOMAdminCatalog2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ICOMAdminCatalog2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ICOMAdminCatalog2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ICOMAdminCatalog methods ***/
#define ICOMAdminCatalog2_GetCollection(This,bstrCollName,ppCatalogCollection) (This)->lpVtbl->GetCollection(This,bstrCollName,ppCatalogCollection)
#define ICOMAdminCatalog2_Connect(This,bstrCatalogServerName,ppCatalogCollection) (This)->lpVtbl->Connect(This,bstrCatalogServerName,ppCatalogCollection)
#define ICOMAdminCatalog2_get_MajorVersion(This,plMajorVersion) (This)->lpVtbl->get_MajorVersion(This,plMajorVersion)
#define ICOMAdminCatalog2_get_MinorVersion(This,plMinorVersion) (This)->lpVtbl->get_MinorVersion(This,plMinorVersion)
#define ICOMAdminCatalog2_GetCollectionByQuery(This,bstrCollName,ppsaVarQuery,ppCatalogCollection) (This)->lpVtbl->GetCollectionByQuery(This,bstrCollName,ppsaVarQuery,ppCatalogCollection)
#define ICOMAdminCatalog2_ImportComponent(This,bstrApplIDOrName,bstrCLSIDOrProgID) (This)->lpVtbl->ImportComponent(This,bstrApplIDOrName,bstrCLSIDOrProgID)
#define ICOMAdminCatalog2_InstallComponent(This,bstrApplIDOrName,bstrDLL,bstrTLB,bstrPSDLL) (This)->lpVtbl->InstallComponent(This,bstrApplIDOrName,bstrDLL,bstrTLB,bstrPSDLL)
#define ICOMAdminCatalog2_ShutdownApplication(This,bstrApplIDOrName) (This)->lpVtbl->ShutdownApplication(This,bstrApplIDOrName)
#define ICOMAdminCatalog2_ExportApplication(This,bstrApplIDOrName,bstrApplicationFile,lOptions) (This)->lpVtbl->ExportApplication(This,bstrApplIDOrName,bstrApplicationFile,lOptions)
#define ICOMAdminCatalog2_InstallApplication(This,bstrApplicationFile,bstrDestinationDirectory,lOptions,bstrUserId,bstrPassword,bstrRSN) (This)->lpVtbl->InstallApplication(This,bstrApplicationFile,bstrDestinationDirectory,lOptions,bstrUserId,bstrPassword,bstrRSN)
#define ICOMAdminCatalog2_StopRouter(This) (This)->lpVtbl->StopRouter(This)
#define ICOMAdminCatalog2_RefreshRouter(This) (This)->lpVtbl->RefreshRouter(This)
#define ICOMAdminCatalog2_StartRouter(This) (This)->lpVtbl->StartRouter(This)
#define ICOMAdminCatalog2_Reserved1(This) (This)->lpVtbl->Reserved1(This)
#define ICOMAdminCatalog2_Reserved2(This) (This)->lpVtbl->Reserved2(This)
#define ICOMAdminCatalog2_InstallMultipleComponents(This,bstrApplIDOrName,ppsaVarFileNames,ppsaVarCLSIDs) (This)->lpVtbl->InstallMultipleComponents(This,bstrApplIDOrName,ppsaVarFileNames,ppsaVarCLSIDs)
#define ICOMAdminCatalog2_GetMultipleComponentsInfo(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDs,ppsaVarClassNames,ppsaVarFileFlags,ppsaVarComponentFlags) (This)->lpVtbl->GetMultipleComponentsInfo(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDs,ppsaVarClassNames,ppsaVarFileFlags,ppsaVarComponentFlags)
#define ICOMAdminCatalog2_RefreshComponents(This) (This)->lpVtbl->RefreshComponents(This)
#define ICOMAdminCatalog2_BackupREGDB(This,bstrBackupFilePath) (This)->lpVtbl->BackupREGDB(This,bstrBackupFilePath)
#define ICOMAdminCatalog2_RestoreREGDB(This,bstrBackupFilePath) (This)->lpVtbl->RestoreREGDB(This,bstrBackupFilePath)
#define ICOMAdminCatalog2_QueryApplicationFile(This,bstrApplicationFile,pbstrApplicationName,pbstrApplicationDescription,pbHasUsers,pbIsProxy,ppsaVarFileNames) (This)->lpVtbl->QueryApplicationFile(This,bstrApplicationFile,pbstrApplicationName,pbstrApplicationDescription,pbHasUsers,pbIsProxy,ppsaVarFileNames)
#define ICOMAdminCatalog2_StartApplication(This,bstrApplIdOrName) (This)->lpVtbl->StartApplication(This,bstrApplIdOrName)
#define ICOMAdminCatalog2_ServiceCheck(This,lService,plStatus) (This)->lpVtbl->ServiceCheck(This,lService,plStatus)
#define ICOMAdminCatalog2_InstallMultipleEventClasses(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDS) (This)->lpVtbl->InstallMultipleEventClasses(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDS)
#define ICOMAdminCatalog2_InstallEventClass(This,bstrApplIdOrName,bstrDLL,bstrTLB,bstrPSDLL) (This)->lpVtbl->InstallEventClass(This,bstrApplIdOrName,bstrDLL,bstrTLB,bstrPSDLL)
#define ICOMAdminCatalog2_GetEventClassesForIID(This,bstrIID,ppsaVarCLSIDs,ppsaVarProgIDs,ppsaVarDescriptions) (This)->lpVtbl->GetEventClassesForIID(This,bstrIID,ppsaVarCLSIDs,ppsaVarProgIDs,ppsaVarDescriptions)
/*** ICOMAdminCatalog2 methods ***/
#define ICOMAdminCatalog2_GetCollectionByQuery2(This,bstrCollectionName,pVarQueryStrings,ppCatalogCollection) (This)->lpVtbl->GetCollectionByQuery2(This,bstrCollectionName,pVarQueryStrings,ppCatalogCollection)
#define ICOMAdminCatalog2_GetApplicationInstanceIDFromProcessID(This,lProcessID,pbstrApplicationInstanceID) (This)->lpVtbl->GetApplicationInstanceIDFromProcessID(This,lProcessID,pbstrApplicationInstanceID)
#define ICOMAdminCatalog2_ShutdownApplicationInstances(This,pVarApplicationInstanceID) (This)->lpVtbl->ShutdownApplicationInstances(This,pVarApplicationInstanceID)
#define ICOMAdminCatalog2_PauseApplicationInstances(This,pVarApplicationInstanceID) (This)->lpVtbl->PauseApplicationInstances(This,pVarApplicationInstanceID)
#define ICOMAdminCatalog2_ResumeApplicationInstances(This,pVarApplicationInstanceID) (This)->lpVtbl->ResumeApplicationInstances(This,pVarApplicationInstanceID)
#define ICOMAdminCatalog2_RecycleApplicationInstances(This,pVarApplicationInstanceID,lReasonCode) (This)->lpVtbl->RecycleApplicationInstances(This,pVarApplicationInstanceID,lReasonCode)
#define ICOMAdminCatalog2_AreApplicationInstancesPaused(This,pVarApplicationInstanceID,pVarBoolPaused) (This)->lpVtbl->AreApplicationInstancesPaused(This,pVarApplicationInstanceID,pVarBoolPaused)
#define ICOMAdminCatalog2_DumpApplicationInstance(This,bstrApplicationInstanceID,bstrDirectory,lMaxImages,pbstrDumpFile) (This)->lpVtbl->DumpApplicationInstance(This,bstrApplicationInstanceID,bstrDirectory,lMaxImages,pbstrDumpFile)
#define ICOMAdminCatalog2_get_IsApplicationInstanceDumpSupported(This,pVarBoolDumpSupported) (This)->lpVtbl->get_IsApplicationInstanceDumpSupported(This,pVarBoolDumpSupported)
#define ICOMAdminCatalog2_CreateServiceForApplication(This,bstrApplicationIDOrName,bstrServiceName,bstrStartType,bstrErrorControl,bstrDependencies,bstrRunAs,bstrPassword,bDesktopOk) (This)->lpVtbl->CreateServiceForApplication(This,bstrApplicationIDOrName,bstrServiceName,bstrStartType,bstrErrorControl,bstrDependencies,bstrRunAs,bstrPassword,bDesktopOk)
#define ICOMAdminCatalog2_DeleteServiceForApplication(This,bstrApplicationIDOrName) (This)->lpVtbl->DeleteServiceForApplication(This,bstrApplicationIDOrName)
#define ICOMAdminCatalog2_GetPartitionID(This,bstrApplicationIDOrName,pbstrPartitionID) (This)->lpVtbl->GetPartitionID(This,bstrApplicationIDOrName,pbstrPartitionID)
#define ICOMAdminCatalog2_GetPartitionName(This,bstrApplicationIDOrName,pbstrPartitionName) (This)->lpVtbl->GetPartitionName(This,bstrApplicationIDOrName,pbstrPartitionName)
#define ICOMAdminCatalog2_put_CurrentPartition(This,bstrPartitionIDOrName) (This)->lpVtbl->put_CurrentPartition(This,bstrPartitionIDOrName)
#define ICOMAdminCatalog2_get_CurrentPartitionID(This,pbstrPartitionID) (This)->lpVtbl->get_CurrentPartitionID(This,pbstrPartitionID)
#define ICOMAdminCatalog2_get_CurrentPartitionName(This,pbstrPartitionName) (This)->lpVtbl->get_CurrentPartitionName(This,pbstrPartitionName)
#define ICOMAdminCatalog2_get_GlobalPartitionID(This,pbstrGlobalPartitionID) (This)->lpVtbl->get_GlobalPartitionID(This,pbstrGlobalPartitionID)
#define ICOMAdminCatalog2_FlushPartitionCache(This) (This)->lpVtbl->FlushPartitionCache(This)
#define ICOMAdminCatalog2_CopyApplications(This,bstrSourcePartitionIDOrName,pVarApplicationID,bstrDestinationPartitionIDOrName) (This)->lpVtbl->CopyApplications(This,bstrSourcePartitionIDOrName,pVarApplicationID,bstrDestinationPartitionIDOrName)
#define ICOMAdminCatalog2_CopyComponents(This,bstrSourceApplicationIDOrName,pVarCLSIDOrProgID,bstrDestinationApplicationIDOrName) (This)->lpVtbl->CopyComponents(This,bstrSourceApplicationIDOrName,pVarCLSIDOrProgID,bstrDestinationApplicationIDOrName)
#define ICOMAdminCatalog2_MoveComponents(This,bstrSourceApplicationIDOrName,pVarCLSIDOrProgID,bstrDestinationApplicationIDOrName) (This)->lpVtbl->MoveComponents(This,bstrSourceApplicationIDOrName,pVarCLSIDOrProgID,bstrDestinationApplicationIDOrName)
#define ICOMAdminCatalog2_AliasComponent(This,bstrSrcApplicationIDOrName,bstrCLSIDOrProgID,bstrDestApplicationIDOrName,bstrNewProgId,bstrNewClsid) (This)->lpVtbl->AliasComponent(This,bstrSrcApplicationIDOrName,bstrCLSIDOrProgID,bstrDestApplicationIDOrName,bstrNewProgId,bstrNewClsid)
#define ICOMAdminCatalog2_IsSafeToDelete(This,bstrDllName,pCOMAdminInUse) (This)->lpVtbl->IsSafeToDelete(This,bstrDllName,pCOMAdminInUse)
#define ICOMAdminCatalog2_ImportUnconfiguredComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType) (This)->lpVtbl->ImportUnconfiguredComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType)
#define ICOMAdminCatalog2_PromoteUnconfiguredComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType) (This)->lpVtbl->PromoteUnconfiguredComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType)
#define ICOMAdminCatalog2_ImportComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType) (This)->lpVtbl->ImportComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType)
#define ICOMAdminCatalog2_get_Is64BitCatalogServer(This,pbIs64Bit) (This)->lpVtbl->get_Is64BitCatalogServer(This,pbIs64Bit)
#define ICOMAdminCatalog2_ExportPartition(This,bstrPartitionIDOrName,bstrPartitionFileName,lOptions) (This)->lpVtbl->ExportPartition(This,bstrPartitionIDOrName,bstrPartitionFileName,lOptions)
#define ICOMAdminCatalog2_InstallPartition(This,bstrFileName,bstrDestDirectory,lOptions,bstrUserID,bstrPassword,bstrRSN) (This)->lpVtbl->InstallPartition(This,bstrFileName,bstrDestDirectory,lOptions,bstrUserID,bstrPassword,bstrRSN)
#define ICOMAdminCatalog2_QueryApplicationFile2(This,bstrApplicationFile,ppFilesForImport) (This)->lpVtbl->QueryApplicationFile2(This,bstrApplicationFile,ppFilesForImport)
#define ICOMAdminCatalog2_GetComponentVersionCount(This,bstrCLSIDOrProgID,plVersionCount) (This)->lpVtbl->GetComponentVersionCount(This,bstrCLSIDOrProgID,plVersionCount)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_QueryInterface(ICOMAdminCatalog2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ICOMAdminCatalog2_AddRef(ICOMAdminCatalog2* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ICOMAdminCatalog2_Release(ICOMAdminCatalog2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetTypeInfoCount(ICOMAdminCatalog2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetTypeInfo(ICOMAdminCatalog2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetIDsOfNames(ICOMAdminCatalog2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_Invoke(ICOMAdminCatalog2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ICOMAdminCatalog methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetCollection(ICOMAdminCatalog2* This,BSTR bstrCollName,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->GetCollection(This,bstrCollName,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_Connect(ICOMAdminCatalog2* This,BSTR bstrCatalogServerName,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->Connect(This,bstrCatalogServerName,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_MajorVersion(ICOMAdminCatalog2* This,LONG *plMajorVersion) {
    return This->lpVtbl->get_MajorVersion(This,plMajorVersion);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_MinorVersion(ICOMAdminCatalog2* This,LONG *plMinorVersion) {
    return This->lpVtbl->get_MinorVersion(This,plMinorVersion);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetCollectionByQuery(ICOMAdminCatalog2* This,BSTR bstrCollName,SAFEARRAY **ppsaVarQuery,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->GetCollectionByQuery(This,bstrCollName,ppsaVarQuery,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ImportComponent(ICOMAdminCatalog2* This,BSTR bstrApplIDOrName,BSTR bstrCLSIDOrProgID) {
    return This->lpVtbl->ImportComponent(This,bstrApplIDOrName,bstrCLSIDOrProgID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_InstallComponent(ICOMAdminCatalog2* This,BSTR bstrApplIDOrName,BSTR bstrDLL,BSTR bstrTLB,BSTR bstrPSDLL) {
    return This->lpVtbl->InstallComponent(This,bstrApplIDOrName,bstrDLL,bstrTLB,bstrPSDLL);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ShutdownApplication(ICOMAdminCatalog2* This,BSTR bstrApplIDOrName) {
    return This->lpVtbl->ShutdownApplication(This,bstrApplIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ExportApplication(ICOMAdminCatalog2* This,BSTR bstrApplIDOrName,BSTR bstrApplicationFile,LONG lOptions) {
    return This->lpVtbl->ExportApplication(This,bstrApplIDOrName,bstrApplicationFile,lOptions);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_InstallApplication(ICOMAdminCatalog2* This,BSTR bstrApplicationFile,BSTR bstrDestinationDirectory,LONG lOptions,BSTR bstrUserId,BSTR bstrPassword,BSTR bstrRSN) {
    return This->lpVtbl->InstallApplication(This,bstrApplicationFile,bstrDestinationDirectory,lOptions,bstrUserId,bstrPassword,bstrRSN);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_StopRouter(ICOMAdminCatalog2* This) {
    return This->lpVtbl->StopRouter(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_RefreshRouter(ICOMAdminCatalog2* This) {
    return This->lpVtbl->RefreshRouter(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_StartRouter(ICOMAdminCatalog2* This) {
    return This->lpVtbl->StartRouter(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_Reserved1(ICOMAdminCatalog2* This) {
    return This->lpVtbl->Reserved1(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_Reserved2(ICOMAdminCatalog2* This) {
    return This->lpVtbl->Reserved2(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_InstallMultipleComponents(ICOMAdminCatalog2* This,BSTR bstrApplIDOrName,SAFEARRAY **ppsaVarFileNames,SAFEARRAY **ppsaVarCLSIDs) {
    return This->lpVtbl->InstallMultipleComponents(This,bstrApplIDOrName,ppsaVarFileNames,ppsaVarCLSIDs);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetMultipleComponentsInfo(ICOMAdminCatalog2* This,BSTR bstrApplIdOrName,SAFEARRAY **ppsaVarFileNames,SAFEARRAY **ppsaVarCLSIDs,SAFEARRAY **ppsaVarClassNames,SAFEARRAY **ppsaVarFileFlags,SAFEARRAY **ppsaVarComponentFlags) {
    return This->lpVtbl->GetMultipleComponentsInfo(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDs,ppsaVarClassNames,ppsaVarFileFlags,ppsaVarComponentFlags);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_RefreshComponents(ICOMAdminCatalog2* This) {
    return This->lpVtbl->RefreshComponents(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_BackupREGDB(ICOMAdminCatalog2* This,BSTR bstrBackupFilePath) {
    return This->lpVtbl->BackupREGDB(This,bstrBackupFilePath);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_RestoreREGDB(ICOMAdminCatalog2* This,BSTR bstrBackupFilePath) {
    return This->lpVtbl->RestoreREGDB(This,bstrBackupFilePath);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_QueryApplicationFile(ICOMAdminCatalog2* This,BSTR bstrApplicationFile,BSTR *pbstrApplicationName,BSTR *pbstrApplicationDescription,VARIANT_BOOL *pbHasUsers,VARIANT_BOOL *pbIsProxy,SAFEARRAY **ppsaVarFileNames) {
    return This->lpVtbl->QueryApplicationFile(This,bstrApplicationFile,pbstrApplicationName,pbstrApplicationDescription,pbHasUsers,pbIsProxy,ppsaVarFileNames);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_StartApplication(ICOMAdminCatalog2* This,BSTR bstrApplIdOrName) {
    return This->lpVtbl->StartApplication(This,bstrApplIdOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ServiceCheck(ICOMAdminCatalog2* This,LONG lService,LONG *plStatus) {
    return This->lpVtbl->ServiceCheck(This,lService,plStatus);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_InstallMultipleEventClasses(ICOMAdminCatalog2* This,BSTR bstrApplIdOrName,SAFEARRAY **ppsaVarFileNames,SAFEARRAY **ppsaVarCLSIDS) {
    return This->lpVtbl->InstallMultipleEventClasses(This,bstrApplIdOrName,ppsaVarFileNames,ppsaVarCLSIDS);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_InstallEventClass(ICOMAdminCatalog2* This,BSTR bstrApplIdOrName,BSTR bstrDLL,BSTR bstrTLB,BSTR bstrPSDLL) {
    return This->lpVtbl->InstallEventClass(This,bstrApplIdOrName,bstrDLL,bstrTLB,bstrPSDLL);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetEventClassesForIID(ICOMAdminCatalog2* This,BSTR bstrIID,SAFEARRAY **ppsaVarCLSIDs,SAFEARRAY **ppsaVarProgIDs,SAFEARRAY **ppsaVarDescriptions) {
    return This->lpVtbl->GetEventClassesForIID(This,bstrIID,ppsaVarCLSIDs,ppsaVarProgIDs,ppsaVarDescriptions);
}
/*** ICOMAdminCatalog2 methods ***/
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetCollectionByQuery2(ICOMAdminCatalog2* This,BSTR bstrCollectionName,VARIANT *pVarQueryStrings,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->GetCollectionByQuery2(This,bstrCollectionName,pVarQueryStrings,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetApplicationInstanceIDFromProcessID(ICOMAdminCatalog2* This,LONG lProcessID,BSTR *pbstrApplicationInstanceID) {
    return This->lpVtbl->GetApplicationInstanceIDFromProcessID(This,lProcessID,pbstrApplicationInstanceID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ShutdownApplicationInstances(ICOMAdminCatalog2* This,VARIANT *pVarApplicationInstanceID) {
    return This->lpVtbl->ShutdownApplicationInstances(This,pVarApplicationInstanceID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_PauseApplicationInstances(ICOMAdminCatalog2* This,VARIANT *pVarApplicationInstanceID) {
    return This->lpVtbl->PauseApplicationInstances(This,pVarApplicationInstanceID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ResumeApplicationInstances(ICOMAdminCatalog2* This,VARIANT *pVarApplicationInstanceID) {
    return This->lpVtbl->ResumeApplicationInstances(This,pVarApplicationInstanceID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_RecycleApplicationInstances(ICOMAdminCatalog2* This,VARIANT *pVarApplicationInstanceID,LONG lReasonCode) {
    return This->lpVtbl->RecycleApplicationInstances(This,pVarApplicationInstanceID,lReasonCode);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_AreApplicationInstancesPaused(ICOMAdminCatalog2* This,VARIANT *pVarApplicationInstanceID,VARIANT_BOOL *pVarBoolPaused) {
    return This->lpVtbl->AreApplicationInstancesPaused(This,pVarApplicationInstanceID,pVarBoolPaused);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_DumpApplicationInstance(ICOMAdminCatalog2* This,BSTR bstrApplicationInstanceID,BSTR bstrDirectory,LONG lMaxImages,BSTR *pbstrDumpFile) {
    return This->lpVtbl->DumpApplicationInstance(This,bstrApplicationInstanceID,bstrDirectory,lMaxImages,pbstrDumpFile);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_IsApplicationInstanceDumpSupported(ICOMAdminCatalog2* This,VARIANT_BOOL *pVarBoolDumpSupported) {
    return This->lpVtbl->get_IsApplicationInstanceDumpSupported(This,pVarBoolDumpSupported);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_CreateServiceForApplication(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName,BSTR bstrServiceName,BSTR bstrStartType,BSTR bstrErrorControl,BSTR bstrDependencies,BSTR bstrRunAs,BSTR bstrPassword,VARIANT_BOOL bDesktopOk) {
    return This->lpVtbl->CreateServiceForApplication(This,bstrApplicationIDOrName,bstrServiceName,bstrStartType,bstrErrorControl,bstrDependencies,bstrRunAs,bstrPassword,bDesktopOk);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_DeleteServiceForApplication(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName) {
    return This->lpVtbl->DeleteServiceForApplication(This,bstrApplicationIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetPartitionID(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName,BSTR *pbstrPartitionID) {
    return This->lpVtbl->GetPartitionID(This,bstrApplicationIDOrName,pbstrPartitionID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetPartitionName(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName,BSTR *pbstrPartitionName) {
    return This->lpVtbl->GetPartitionName(This,bstrApplicationIDOrName,pbstrPartitionName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_put_CurrentPartition(ICOMAdminCatalog2* This,BSTR bstrPartitionIDOrName) {
    return This->lpVtbl->put_CurrentPartition(This,bstrPartitionIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_CurrentPartitionID(ICOMAdminCatalog2* This,BSTR *pbstrPartitionID) {
    return This->lpVtbl->get_CurrentPartitionID(This,pbstrPartitionID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_CurrentPartitionName(ICOMAdminCatalog2* This,BSTR *pbstrPartitionName) {
    return This->lpVtbl->get_CurrentPartitionName(This,pbstrPartitionName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_GlobalPartitionID(ICOMAdminCatalog2* This,BSTR *pbstrGlobalPartitionID) {
    return This->lpVtbl->get_GlobalPartitionID(This,pbstrGlobalPartitionID);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_FlushPartitionCache(ICOMAdminCatalog2* This) {
    return This->lpVtbl->FlushPartitionCache(This);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_CopyApplications(ICOMAdminCatalog2* This,BSTR bstrSourcePartitionIDOrName,VARIANT *pVarApplicationID,BSTR bstrDestinationPartitionIDOrName) {
    return This->lpVtbl->CopyApplications(This,bstrSourcePartitionIDOrName,pVarApplicationID,bstrDestinationPartitionIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_CopyComponents(ICOMAdminCatalog2* This,BSTR bstrSourceApplicationIDOrName,VARIANT *pVarCLSIDOrProgID,BSTR bstrDestinationApplicationIDOrName) {
    return This->lpVtbl->CopyComponents(This,bstrSourceApplicationIDOrName,pVarCLSIDOrProgID,bstrDestinationApplicationIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_MoveComponents(ICOMAdminCatalog2* This,BSTR bstrSourceApplicationIDOrName,VARIANT *pVarCLSIDOrProgID,BSTR bstrDestinationApplicationIDOrName) {
    return This->lpVtbl->MoveComponents(This,bstrSourceApplicationIDOrName,pVarCLSIDOrProgID,bstrDestinationApplicationIDOrName);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_AliasComponent(ICOMAdminCatalog2* This,BSTR bstrSrcApplicationIDOrName,BSTR bstrCLSIDOrProgID,BSTR bstrDestApplicationIDOrName,BSTR bstrNewProgId,BSTR bstrNewClsid) {
    return This->lpVtbl->AliasComponent(This,bstrSrcApplicationIDOrName,bstrCLSIDOrProgID,bstrDestApplicationIDOrName,bstrNewProgId,bstrNewClsid);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_IsSafeToDelete(ICOMAdminCatalog2* This,BSTR bstrDllName,COMAdminInUse *pCOMAdminInUse) {
    return This->lpVtbl->IsSafeToDelete(This,bstrDllName,pCOMAdminInUse);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ImportUnconfiguredComponents(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName,VARIANT *pVarCLSIDOrProgID,VARIANT *pVarComponentType) {
    return This->lpVtbl->ImportUnconfiguredComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_PromoteUnconfiguredComponents(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName,VARIANT *pVarCLSIDOrProgID,VARIANT *pVarComponentType) {
    return This->lpVtbl->PromoteUnconfiguredComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ImportComponents(ICOMAdminCatalog2* This,BSTR bstrApplicationIDOrName,VARIANT *pVarCLSIDOrProgID,VARIANT *pVarComponentType) {
    return This->lpVtbl->ImportComponents(This,bstrApplicationIDOrName,pVarCLSIDOrProgID,pVarComponentType);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_get_Is64BitCatalogServer(ICOMAdminCatalog2* This,VARIANT_BOOL *pbIs64Bit) {
    return This->lpVtbl->get_Is64BitCatalogServer(This,pbIs64Bit);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_ExportPartition(ICOMAdminCatalog2* This,BSTR bstrPartitionIDOrName,BSTR bstrPartitionFileName,LONG lOptions) {
    return This->lpVtbl->ExportPartition(This,bstrPartitionIDOrName,bstrPartitionFileName,lOptions);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_InstallPartition(ICOMAdminCatalog2* This,BSTR bstrFileName,BSTR bstrDestDirectory,LONG lOptions,BSTR bstrUserID,BSTR bstrPassword,BSTR bstrRSN) {
    return This->lpVtbl->InstallPartition(This,bstrFileName,bstrDestDirectory,lOptions,bstrUserID,bstrPassword,bstrRSN);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_QueryApplicationFile2(ICOMAdminCatalog2* This,BSTR bstrApplicationFile,IDispatch **ppFilesForImport) {
    return This->lpVtbl->QueryApplicationFile2(This,bstrApplicationFile,ppFilesForImport);
}
static __WIDL_INLINE HRESULT ICOMAdminCatalog2_GetComponentVersionCount(ICOMAdminCatalog2* This,BSTR bstrCLSIDOrProgID,LONG *plVersionCount) {
    return This->lpVtbl->GetComponentVersionCount(This,bstrCLSIDOrProgID,plVersionCount);
}
#endif
#endif

#endif


#endif  /* __ICOMAdminCatalog2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICatalogObject interface
 */
#ifndef __ICatalogObject_INTERFACE_DEFINED__
#define __ICatalogObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICatalogObject, 0x6eb22871, 0x8a19, 0x11d0, 0x81,0xb6, 0x00,0xa0,0xc9,0x23,0x1c,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6eb22871-8a19-11d0-81b6-00a0c9231c29")
ICatalogObject : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Value(
        BSTR bstrPropName,
        VARIANT *pvarRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        BSTR bstrPropName,
        VARIANT val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Key(
        VARIANT *pvarRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        VARIANT *pvarRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPropertyReadOnly(
        BSTR bstrPropName,
        VARIANT_BOOL *pbRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Valid(
        VARIANT_BOOL *pbRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPropertyWriteOnly(
        BSTR bstrPropName,
        VARIANT_BOOL *pbRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICatalogObject, 0x6eb22871, 0x8a19, 0x11d0, 0x81,0xb6, 0x00,0xa0,0xc9,0x23,0x1c,0x29)
#endif
#else
typedef struct ICatalogObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICatalogObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICatalogObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICatalogObject *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ICatalogObject *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ICatalogObject *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ICatalogObject *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ICatalogObject *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ICatalogObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ICatalogObject *This,
        BSTR bstrPropName,
        VARIANT *pvarRetVal);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        ICatalogObject *This,
        BSTR bstrPropName,
        VARIANT val);

    HRESULT (STDMETHODCALLTYPE *get_Key)(
        ICatalogObject *This,
        VARIANT *pvarRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ICatalogObject *This,
        VARIANT *pvarRetVal);

    HRESULT (STDMETHODCALLTYPE *IsPropertyReadOnly)(
        ICatalogObject *This,
        BSTR bstrPropName,
        VARIANT_BOOL *pbRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Valid)(
        ICatalogObject *This,
        VARIANT_BOOL *pbRetVal);

    HRESULT (STDMETHODCALLTYPE *IsPropertyWriteOnly)(
        ICatalogObject *This,
        BSTR bstrPropName,
        VARIANT_BOOL *pbRetVal);

    END_INTERFACE
} ICatalogObjectVtbl;

interface ICatalogObject {
    CONST_VTBL ICatalogObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICatalogObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICatalogObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICatalogObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ICatalogObject_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ICatalogObject_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ICatalogObject_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ICatalogObject_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ICatalogObject methods ***/
#define ICatalogObject_get_Value(This,bstrPropName,pvarRetVal) (This)->lpVtbl->get_Value(This,bstrPropName,pvarRetVal)
#define ICatalogObject_put_Value(This,bstrPropName,val) (This)->lpVtbl->put_Value(This,bstrPropName,val)
#define ICatalogObject_get_Key(This,pvarRetVal) (This)->lpVtbl->get_Key(This,pvarRetVal)
#define ICatalogObject_get_Name(This,pvarRetVal) (This)->lpVtbl->get_Name(This,pvarRetVal)
#define ICatalogObject_IsPropertyReadOnly(This,bstrPropName,pbRetVal) (This)->lpVtbl->IsPropertyReadOnly(This,bstrPropName,pbRetVal)
#define ICatalogObject_get_Valid(This,pbRetVal) (This)->lpVtbl->get_Valid(This,pbRetVal)
#define ICatalogObject_IsPropertyWriteOnly(This,bstrPropName,pbRetVal) (This)->lpVtbl->IsPropertyWriteOnly(This,bstrPropName,pbRetVal)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ICatalogObject_QueryInterface(ICatalogObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ICatalogObject_AddRef(ICatalogObject* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ICatalogObject_Release(ICatalogObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static __WIDL_INLINE HRESULT ICatalogObject_GetTypeInfoCount(ICatalogObject* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static __WIDL_INLINE HRESULT ICatalogObject_GetTypeInfo(ICatalogObject* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static __WIDL_INLINE HRESULT ICatalogObject_GetIDsOfNames(ICatalogObject* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static __WIDL_INLINE HRESULT ICatalogObject_Invoke(ICatalogObject* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ICatalogObject methods ***/
static __WIDL_INLINE HRESULT ICatalogObject_get_Value(ICatalogObject* This,BSTR bstrPropName,VARIANT *pvarRetVal) {
    return This->lpVtbl->get_Value(This,bstrPropName,pvarRetVal);
}
static __WIDL_INLINE HRESULT ICatalogObject_put_Value(ICatalogObject* This,BSTR bstrPropName,VARIANT val) {
    return This->lpVtbl->put_Value(This,bstrPropName,val);
}
static __WIDL_INLINE HRESULT ICatalogObject_get_Key(ICatalogObject* This,VARIANT *pvarRetVal) {
    return This->lpVtbl->get_Key(This,pvarRetVal);
}
static __WIDL_INLINE HRESULT ICatalogObject_get_Name(ICatalogObject* This,VARIANT *pvarRetVal) {
    return This->lpVtbl->get_Name(This,pvarRetVal);
}
static __WIDL_INLINE HRESULT ICatalogObject_IsPropertyReadOnly(ICatalogObject* This,BSTR bstrPropName,VARIANT_BOOL *pbRetVal) {
    return This->lpVtbl->IsPropertyReadOnly(This,bstrPropName,pbRetVal);
}
static __WIDL_INLINE HRESULT ICatalogObject_get_Valid(ICatalogObject* This,VARIANT_BOOL *pbRetVal) {
    return This->lpVtbl->get_Valid(This,pbRetVal);
}
static __WIDL_INLINE HRESULT ICatalogObject_IsPropertyWriteOnly(ICatalogObject* This,BSTR bstrPropName,VARIANT_BOOL *pbRetVal) {
    return This->lpVtbl->IsPropertyWriteOnly(This,bstrPropName,pbRetVal);
}
#endif
#endif

#endif


#endif  /* __ICatalogObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICatalogCollection interface
 */
#ifndef __ICatalogCollection_INTERFACE_DEFINED__
#define __ICatalogCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICatalogCollection, 0x6eb22872, 0x8a19, 0x11d0, 0x81,0xb6, 0x00,0xa0,0xc9,0x23,0x1c,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6eb22872-8a19-11d0-81b6-00a0c9231c29")
ICatalogCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **ppEnumVariant) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG lIndex,
        IDispatch **ppCatalogObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *plObjectCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG lIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IDispatch **ppCatalogObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE Populate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveChanges(
        LONG *pcChanges) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCollection(
        BSTR bstrCollName,
        VARIANT varObjectKey,
        IDispatch **ppCatalogCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        VARIANT *pVarNamel) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AddEnabled(
        VARIANT_BOOL *pVarBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoveEnabled(
        VARIANT_BOOL *pVarBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUtilInterface(
        IDispatch **ppIDispatch) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DataStoreMajorVersion(
        LONG *plMajorVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DataStoreMinorVersion(
        LONG *plMinorVersionl) = 0;

    virtual HRESULT STDMETHODCALLTYPE PopulateByKey(
        SAFEARRAY *psaKeys) = 0;

    virtual HRESULT STDMETHODCALLTYPE PopulateByQuery(
        BSTR bstrQueryString,
        LONG lQueryType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICatalogCollection, 0x6eb22872, 0x8a19, 0x11d0, 0x81,0xb6, 0x00,0xa0,0xc9,0x23,0x1c,0x29)
#endif
#else
typedef struct ICatalogCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICatalogCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICatalogCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICatalogCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ICatalogCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ICatalogCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ICatalogCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ICatalogCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ICatalogCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ICatalogCollection *This,
        IUnknown **ppEnumVariant);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        ICatalogCollection *This,
        LONG lIndex,
        IDispatch **ppCatalogObject);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ICatalogCollection *This,
        LONG *plObjectCount);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ICatalogCollection *This,
        LONG lIndex);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ICatalogCollection *This,
        IDispatch **ppCatalogObject);

    HRESULT (STDMETHODCALLTYPE *Populate)(
        ICatalogCollection *This);

    HRESULT (STDMETHODCALLTYPE *SaveChanges)(
        ICatalogCollection *This,
        LONG *pcChanges);

    HRESULT (STDMETHODCALLTYPE *GetCollection)(
        ICatalogCollection *This,
        BSTR bstrCollName,
        VARIANT varObjectKey,
        IDispatch **ppCatalogCollection);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ICatalogCollection *This,
        VARIANT *pVarNamel);

    HRESULT (STDMETHODCALLTYPE *get_AddEnabled)(
        ICatalogCollection *This,
        VARIANT_BOOL *pVarBool);

    HRESULT (STDMETHODCALLTYPE *get_RemoveEnabled)(
        ICatalogCollection *This,
        VARIANT_BOOL *pVarBool);

    HRESULT (STDMETHODCALLTYPE *GetUtilInterface)(
        ICatalogCollection *This,
        IDispatch **ppIDispatch);

    HRESULT (STDMETHODCALLTYPE *get_DataStoreMajorVersion)(
        ICatalogCollection *This,
        LONG *plMajorVersion);

    HRESULT (STDMETHODCALLTYPE *get_DataStoreMinorVersion)(
        ICatalogCollection *This,
        LONG *plMinorVersionl);

    HRESULT (STDMETHODCALLTYPE *PopulateByKey)(
        ICatalogCollection *This,
        SAFEARRAY *psaKeys);

    HRESULT (STDMETHODCALLTYPE *PopulateByQuery)(
        ICatalogCollection *This,
        BSTR bstrQueryString,
        LONG lQueryType);

    END_INTERFACE
} ICatalogCollectionVtbl;

interface ICatalogCollection {
    CONST_VTBL ICatalogCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICatalogCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICatalogCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICatalogCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ICatalogCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ICatalogCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ICatalogCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ICatalogCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ICatalogCollection methods ***/
#define ICatalogCollection_get__NewEnum(This,ppEnumVariant) (This)->lpVtbl->get__NewEnum(This,ppEnumVariant)
#define ICatalogCollection_get_Item(This,lIndex,ppCatalogObject) (This)->lpVtbl->get_Item(This,lIndex,ppCatalogObject)
#define ICatalogCollection_get_Count(This,plObjectCount) (This)->lpVtbl->get_Count(This,plObjectCount)
#define ICatalogCollection_Remove(This,lIndex) (This)->lpVtbl->Remove(This,lIndex)
#define ICatalogCollection_Add(This,ppCatalogObject) (This)->lpVtbl->Add(This,ppCatalogObject)
#define ICatalogCollection_Populate(This) (This)->lpVtbl->Populate(This)
#define ICatalogCollection_SaveChanges(This,pcChanges) (This)->lpVtbl->SaveChanges(This,pcChanges)
#define ICatalogCollection_GetCollection(This,bstrCollName,varObjectKey,ppCatalogCollection) (This)->lpVtbl->GetCollection(This,bstrCollName,varObjectKey,ppCatalogCollection)
#define ICatalogCollection_get_Name(This,pVarNamel) (This)->lpVtbl->get_Name(This,pVarNamel)
#define ICatalogCollection_get_AddEnabled(This,pVarBool) (This)->lpVtbl->get_AddEnabled(This,pVarBool)
#define ICatalogCollection_get_RemoveEnabled(This,pVarBool) (This)->lpVtbl->get_RemoveEnabled(This,pVarBool)
#define ICatalogCollection_GetUtilInterface(This,ppIDispatch) (This)->lpVtbl->GetUtilInterface(This,ppIDispatch)
#define ICatalogCollection_get_DataStoreMajorVersion(This,plMajorVersion) (This)->lpVtbl->get_DataStoreMajorVersion(This,plMajorVersion)
#define ICatalogCollection_get_DataStoreMinorVersion(This,plMinorVersionl) (This)->lpVtbl->get_DataStoreMinorVersion(This,plMinorVersionl)
#define ICatalogCollection_PopulateByKey(This,psaKeys) (This)->lpVtbl->PopulateByKey(This,psaKeys)
#define ICatalogCollection_PopulateByQuery(This,bstrQueryString,lQueryType) (This)->lpVtbl->PopulateByQuery(This,bstrQueryString,lQueryType)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ICatalogCollection_QueryInterface(ICatalogCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ICatalogCollection_AddRef(ICatalogCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ICatalogCollection_Release(ICatalogCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static __WIDL_INLINE HRESULT ICatalogCollection_GetTypeInfoCount(ICatalogCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static __WIDL_INLINE HRESULT ICatalogCollection_GetTypeInfo(ICatalogCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static __WIDL_INLINE HRESULT ICatalogCollection_GetIDsOfNames(ICatalogCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static __WIDL_INLINE HRESULT ICatalogCollection_Invoke(ICatalogCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ICatalogCollection methods ***/
static __WIDL_INLINE HRESULT ICatalogCollection_get__NewEnum(ICatalogCollection* This,IUnknown **ppEnumVariant) {
    return This->lpVtbl->get__NewEnum(This,ppEnumVariant);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_Item(ICatalogCollection* This,LONG lIndex,IDispatch **ppCatalogObject) {
    return This->lpVtbl->get_Item(This,lIndex,ppCatalogObject);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_Count(ICatalogCollection* This,LONG *plObjectCount) {
    return This->lpVtbl->get_Count(This,plObjectCount);
}
static __WIDL_INLINE HRESULT ICatalogCollection_Remove(ICatalogCollection* This,LONG lIndex) {
    return This->lpVtbl->Remove(This,lIndex);
}
static __WIDL_INLINE HRESULT ICatalogCollection_Add(ICatalogCollection* This,IDispatch **ppCatalogObject) {
    return This->lpVtbl->Add(This,ppCatalogObject);
}
static __WIDL_INLINE HRESULT ICatalogCollection_Populate(ICatalogCollection* This) {
    return This->lpVtbl->Populate(This);
}
static __WIDL_INLINE HRESULT ICatalogCollection_SaveChanges(ICatalogCollection* This,LONG *pcChanges) {
    return This->lpVtbl->SaveChanges(This,pcChanges);
}
static __WIDL_INLINE HRESULT ICatalogCollection_GetCollection(ICatalogCollection* This,BSTR bstrCollName,VARIANT varObjectKey,IDispatch **ppCatalogCollection) {
    return This->lpVtbl->GetCollection(This,bstrCollName,varObjectKey,ppCatalogCollection);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_Name(ICatalogCollection* This,VARIANT *pVarNamel) {
    return This->lpVtbl->get_Name(This,pVarNamel);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_AddEnabled(ICatalogCollection* This,VARIANT_BOOL *pVarBool) {
    return This->lpVtbl->get_AddEnabled(This,pVarBool);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_RemoveEnabled(ICatalogCollection* This,VARIANT_BOOL *pVarBool) {
    return This->lpVtbl->get_RemoveEnabled(This,pVarBool);
}
static __WIDL_INLINE HRESULT ICatalogCollection_GetUtilInterface(ICatalogCollection* This,IDispatch **ppIDispatch) {
    return This->lpVtbl->GetUtilInterface(This,ppIDispatch);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_DataStoreMajorVersion(ICatalogCollection* This,LONG *plMajorVersion) {
    return This->lpVtbl->get_DataStoreMajorVersion(This,plMajorVersion);
}
static __WIDL_INLINE HRESULT ICatalogCollection_get_DataStoreMinorVersion(ICatalogCollection* This,LONG *plMinorVersionl) {
    return This->lpVtbl->get_DataStoreMinorVersion(This,plMinorVersionl);
}
static __WIDL_INLINE HRESULT ICatalogCollection_PopulateByKey(ICatalogCollection* This,SAFEARRAY *psaKeys) {
    return This->lpVtbl->PopulateByKey(This,psaKeys);
}
static __WIDL_INLINE HRESULT ICatalogCollection_PopulateByQuery(ICatalogCollection* This,BSTR bstrQueryString,LONG lQueryType) {
    return This->lpVtbl->PopulateByQuery(This,bstrQueryString,lQueryType);
}
#endif
#endif

#endif


#endif  /* __ICatalogCollection_INTERFACE_DEFINED__ */

#ifndef __COMAdmin_LIBRARY_DEFINED__
#define __COMAdmin_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_COMAdmin, 0xf618c513, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35);

/*****************************************************************************
 * COMAdminCatalog coclass
 */

DEFINE_GUID(CLSID_COMAdminCatalog, 0xf618c514, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35);

#ifdef __cplusplus
class DECLSPEC_UUID("f618c514-dfb8-11d1-a2cf-00805fc79235") COMAdminCatalog;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(COMAdminCatalog, 0xf618c514, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35)
#endif
#endif

/*****************************************************************************
 * COMAdminCatalogObject coclass
 */

DEFINE_GUID(CLSID_COMAdminCatalogObject, 0xf618c515, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35);

#ifdef __cplusplus
class DECLSPEC_UUID("f618c515-dfb8-11d1-a2cf-00805fc79235") COMAdminCatalogObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(COMAdminCatalogObject, 0xf618c515, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35)
#endif
#endif

/*****************************************************************************
 * COMAdminCatalogCollection coclass
 */

DEFINE_GUID(CLSID_COMAdminCatalogCollection, 0xf618c516, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35);

#ifdef __cplusplus
class DECLSPEC_UUID("f618c516-dfb8-11d1-a2cf-00805fc79235") COMAdminCatalogCollection;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(COMAdminCatalogCollection, 0xf618c516, 0xdfb8, 0x11d1, 0xa2,0xcf, 0x00,0x80,0x5f,0xc7,0x92,0x35)
#endif
#endif

#define COMAdminCollectionRoot ("Root")

#define COMAdminCollectionApplications ("Applications")

#define COMAdminCollectionComponents ("Components")

#define COMAdminCollectionComputerList ("ComputerList")

#define COMAdminCollectionApplicationCluster ("ApplicationCluster")

#define COMAdminCollectionLocalComputer ("LocalComputer")

#define COMAdminCollectionInprocServers ("InprocServers")

#define COMAdminCollectionRelatedCollectionInfo ("RelatedCollectionInfo")

#define COMAdminCollectionPropertyInfo ("PropertyInfo")

#define COMAdminCollectionRoles ("Roles")

#define COMAdminCollectionErrorInfo ("ErrorInfo")

#define COMAdminCollectionInterfacesForComponent ("InterfacesForComponent")

#define COMAdminCollectionRolesForComponent ("RolesForComponent")

#define COMAdminCollectionMethodsForInterface ("MethodsForInterface")

#define COMAdminCollectionRolesForInterface ("RolesForInterface")

#define COMAdminCollectionRolesForMethod ("RolesForMethod")

#define COMAdminCollectionUsersInRole ("UsersInRole")

#define COMAdminCollectionDCOMProtocols ("DCOMProtocols")

#define COMAdminCollectionPartitions ("Partitions")

#endif /* __COMAdmin_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __comadmin_h__ */
