LIBRARY api-ms-win-crt-filesystem-l1-1-0

EXPORTS

_access
; access is provided as an alias for __mingw_access
; access == _access
_access_s
_chdir
chdir == _chdir
_chdrive
_chmod
chmod == _chmod
_findclose
_findfirst == _findfirst64
_findfirst32
_findfirst32i64
_findfirst64
_findfirst64i32
_findnext == _findnext64
_findnext32
_findnext32i64
_findnext64
_findnext64i32
_fstat32
_fstat32i64
_fstat64
_fstat64i32
_fullpath
_getdiskfree
_getdrive
_getdrives
_lock_file
_makepath
_makepath_s
_mkdir
mkdir == _mkdir
_rmdir
rmdir == _rmdir
_splitpath
_splitpath_s
_stat32
_stat32i64
_stat64
_stat64i32
_umask
umask == _umask
_umask_s
_unlink
unlink == _unlink
_unlock_file
_waccess
_waccess_s
_wchdir
_wchmod
_wfindfirst32
_wfindfirst32i64
_wfindfirst64
_wfindfirst64i32
_wfindnext32
_wfindnext32i64
_wfindnext64
_wfindnext64i32
_wfullpath
_wmakepath
_wmakepath_s
_wmkdir
_wremove
_wrename
_wrmdir
_wsplitpath
_wsplitpath_s
_wstat32
_wstat32i64
_wstat64
_wstat64i32
_wunlink
remove
rename
