;
; Definition file of WIMGAPI.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WIMGAPI.DLL"
EXPORTS
;DllCanUnloadNow@0
;Dll<PERSON>ain@12
WIMAddImagePath@16
WIMAddImagePaths@20
WIMAddWimbootEntry@16
WIMApplyImage@12
WIMCaptureImage@12
WIM<PERSON><PERSON><PERSON><PERSON>le@4
WIMCommitImageHandle@12
WIMCopyFile@24
WIMCreateFile@24
WIMCreateImageFile@20
WIMCreateWofCompressedFile@12
WIMDeleteImage@8
WIMDeleteImageMounts@4
WIMEnumImageFiles@16
WIMExportImage@12
WIMExtractImageDirectory@16
WIMExtractImagePath@16
WIMFindFirstImageFile@12
WIMFindNextImageFile@8
WIMGetAttributes@12
WIMGetImageCount@4
WIMGetImageInformation@12
WIMGetMessageCallbackCount@4
WIMGetMounted<PERSON><PERSON><PERSON><PERSON>@16
WIMGetMountedImageInfo@20
WIMGetMountedImageInfoFromHandle@20
WIMGetMountedImages@8
WIMGetWIMBootEntries@12
WIMGetWIMBootWIMPath@8
WIMInitFileIOCallbacks@4
WIMInitializeWofDriver@8
WIMIsCurrentSystemWimboot@0
WIMIsReferenceWim@20
WIMLoadImage@8
WIMMountImage@16
WIMMountImageHandle@12
WIMProcessCustomImage@12
WIMReadFileEx@20
WIMReadImageFile@20
WIMRedirectFolderBeforeApply@12
WIMRegisterLogFile@8
WIMRegisterMessageCallback@12
WIMRemountImage@8
WIMSetBootImage@8
WIMSetFileIOCallbackTemporaryPath@4
WIMSetImageInformation@12
WIMSetImageUserSpecifiedCreationTime@8
WIMSetReferenceFile@12
WIMSetTemporaryPath@8
WIMSetWimGuid@8
WIMSingleInstanceFile@16
WIMSplitFile@16
WIMUnmountImage@16
WIMUnmountImageHandle@8
WIMUnregisterLogFile@4
WIMUnregisterMessageCallback@8
WIMUpdateWIMBootEntry@16
WIMWriteFileWithIntegrity@16
