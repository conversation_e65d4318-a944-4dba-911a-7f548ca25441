;
; Definition file of FeClient.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "FeClient.dll"
EXPORTS
EfsUtilGetCurrentKey
EdpContainerizeFile
EdpCredentialCreate
EdpCredentialDelete
EdpCredentialExists
EdpCredentialQuery
EdpDecontainerizeFile
EdpDplPolicyEnabledForUser
EdpDplUpgradePinInfo
EdpDplUpgradeVerifyUser
EdpDplUserCredentialsSet
EdpDplUserUnlockComplete
EdpDplUserUnlockStart
EdpFree
EdpGetContainerIdentity
EdpGetCredServiceState
EdpQueryCredServiceInfo
EdpQueryDplEnforcedPolicyOwnerIds
EdpQueryRevokedPolicyOwnerIds
EdpRmsClearKeys
EdpSetCredServiceInfo
EfsClientCloseFileRaw
EfsClientDecryptFile
EfsClientDuplicateEncryptionInfo
EfsClientE<PERSON>FileEx
EfsClientFileEncryptionStatus
EfsClientFreeProtectorList
EfsClientGetEncryptedFileVersion
EfsClientOpenFileRaw
EfsClientQueryProtectors
EfsClientReadFileRaw
EfsClientWriteFileRaw
EfsClientWriteFileWithHeaderRaw
FeClientInitialize
GetLockSessionUnwrappedKey
GetLockSessionWrappedKey
