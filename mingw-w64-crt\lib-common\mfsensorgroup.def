;
; Definition file of MFSENSORGROUP.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "MFSENSORGROUP.dll"
EXPORTS
MFCheckProcessCapabilities
MFCleanupVirtualCameraEntries
MFCloneSensorProfile
MFCreatePackageFamilyNameTag
MFCreatePassthroughTranslatedMediaType
MFCreateRelativePanelWatcher
MFCreateSensorActivityMonitor
MFCreateSensorDeviceBlobByObject
MFCreateSensorGroup
MFCreateSensorGroupById
MFCreateSensorGroupCollection
MFCreateSensorGroupIdManager
MFCreateSensorProfile
MFCreateSensorProfileCollection
MFCreateSensorProfileWithFlags
MFCreateSensorStream
MFCreateTranslatedMediaType
MFCreateTranslatedMediaType2
MFDeleteSensorGroupById
MFGetDeviceFromFSUniqueId
MFGetDeviceFromSGHash
MFGetSGCH
MFGetSensorDeviceProperty
MFGetSensorDeviceRegistryProperty
MFGetSensorGroupAttributesFromId
MFGetSensorGroupPropertyName
MFGetSensorOrientation
MFInitializeSensorGroupStore
MFIsSensorGroupName
MFIsStreamAvailableToAppPackage
MFLoadSensorGroupFromRegistry
MFLoadSensorProfiles
MFPublishSensorProfiles
MFSensorProfileParseFilterSetString
MFValidateSensorProfile
MFWriteSensorGroupDataToRegistry
