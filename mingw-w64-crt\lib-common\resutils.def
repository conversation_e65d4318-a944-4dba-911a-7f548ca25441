;
; Definition file of RESUTILS.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "RESUTILS.dll"
EXPORTS
CloseClusterCryptProvider
ClusWorkerCheckTerminate
ClusWorkerCreate
ClusWorkerStart
ClusWorkerTerminate
ClusterClearBackupStateForSharedVolume
ClusterDecrypt
ClusterEncrypt
ClusterEnumTasks
ClusterFileShareCreate
ClusterFileShareDelete
ClusterFileShareUpdate
ClusterFreeTaskInfo
ClusterFreeTaskList
ClusterGetTaskNode
ClusterGetVolumeNameForVolumeMountPoint
ClusterGetVolumePathName
ClusterIsClusterDisk
ClusterIsPathOnSharedVolume
ClusterPrepareSharedVolumeForBackup
ClusterSharedVolumeCheckSnapshotPresence
ClusterSharedVolumeCreateSnapshot
ClusterSharedVolumeReleaseSnapshot
ClusterTaskChangeFromXML
ClusterTaskChangeFromXMLFile
ClusterTask<PERSON><PERSON>e_TS_V1
ClusterTaskCreateFromXML
ClusterTaskCreateFromXMLFile
ClusterTaskCreate_TS_V1
ClusterTaskDelete
ClusterTaskDelete_TS_V1
ClusterTaskExists_TS_V1
ClusterTaskQuery
CreateClusterStorageSpacesClustering
CreateClusterStorageSpacesResourceLocator
CreateClusterStorageSpacesSubProvider
FreeClusterCrypt
OpenClusterCryptProvider
ResUtilAddUnknownProperties
ResUtilCreateDirectoryTree
ResUtilDupParameterBlock
ResUtilDupString
ResUtilEnumPrivateProperties
ResUtilEnumProperties
ResUtilEnumResources
ResUtilEnumResourcesEx
ResUtilEnumResourcesEx2
ResUtilExpandEnvironmentStrings
ResUtilFindBinaryProperty
ResUtilFindDependentDiskResourceDriveLetter
ResUtilFindDwordProperty
ResUtilFindExpandSzProperty
ResUtilFindExpandedSzProperty
ResUtilFindFileTimeProperty
ResUtilFindLongProperty
ResUtilFindMultiSzProperty
ResUtilFindSzProperty
ResUtilFreeEnvironment
ResUtilFreeParameterBlock
ResUtilGetAllProperties
ResUtilGetBinaryProperty
ResUtilGetBinaryValue
ResUtilGetClusterRoleState
ResUtilGetCoreClusterResources
ResUtilGetCoreClusterResourcesEx
ResUtilGetDwordProperty
ResUtilGetDwordValue
ResUtilGetEnvironmentWithNetName
ResUtilGetFileTimeProperty
ResUtilGetLongProperty
ResUtilGetMultiSzProperty
ResUtilGetPrivateProperties
ResUtilGetProperties
ResUtilGetPropertiesToParameterBlock
ResUtilGetProperty
ResUtilGetPropertyFormats
ResUtilGetPropertySize
ResUtilGetQwordValue
ResUtilGetResourceDependency
ResUtilGetResourceDependencyByClass
ResUtilGetResourceDependencyByClassEx
ResUtilGetResourceDependencyByName
ResUtilGetResourceDependencyByNameAndClass
ResUtilGetResourceDependencyByNameEx
ResUtilGetResourceDependencyEx
ResUtilGetResourceDependentIPAddressProps
ResUtilGetResourceName
ResUtilGetResourceNameDependency
ResUtilGetResourceNameDependencyEx
ResUtilGetSzProperty
ResUtilGetSzValue
ResUtilIsPathValid
ResUtilIsResourceClassEqual
ResUtilPropertyListFromParameterBlock
ResUtilRemoveResourceServiceEnvironment
ResUtilResourceTypesEqual
ResUtilResourcesEqual
ResUtilSetBinaryValue
ResUtilSetDwordValue
ResUtilSetExpandSzValue
ResUtilSetMultiSzValue
ResUtilSetPrivatePropertyList
ResUtilSetPropertyParameterBlock
ResUtilSetPropertyParameterBlockEx
ResUtilSetPropertyTable
ResUtilSetPropertyTableEx
ResUtilSetQwordValue
ResUtilSetResourceServiceEnvironment
ResUtilSetResourceServiceStartParameters
ResUtilSetResourceServiceStartParametersEx
ResUtilSetSzValue
ResUtilSetUnknownProperties
ResUtilSetValueEx
ResUtilStartResourceService
ResUtilStopResourceService
ResUtilStopService
ResUtilTerminateServiceProcessFromResDll
ResUtilVerifyPrivatePropertyList
ResUtilVerifyPropertyTable
ResUtilVerifyResourceService
ResUtilVerifyService
