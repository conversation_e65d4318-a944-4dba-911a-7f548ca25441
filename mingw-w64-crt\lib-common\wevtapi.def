;
; Definition file of wevtapi.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "wevtapi.dll"
EXPORTS
EvtIntSysprepCleanup
EvtSetObjectArrayProperty
EvtArchiveExportedLog
EvtCancel
EvtClearLog
EvtClose
EvtCreateBookmark
EvtCreateRenderContext
EvtExportLog
EvtFormatMessage
EvtGetChannelConfigProperty
EvtGetEventInfo
EvtGetEventMetadataProperty
EvtGetExtendedStatus
EvtGetLogInfo
EvtGetObjectArrayProperty
EvtGetObjectArraySize
EvtGetPublisherMetadataProperty
EvtGetQueryInfo
EvtIntAssertConfig
EvtIntCreateBinXMLFromCustomXML
EvtIntCreateLocalLogfile
EvtIntGetClassicLogDisplayName
EvtIntRenderRes<PERSON>emplate
EvtIntReportAuthzEventAndSourceAsync
EvtIntReportEventAndSourceAsync
EvtIntRetractConfig
EvtIntWriteXmlEventToLocalLogfile
EvtNext
EvtNextChannelPath
EvtNextEventMetadata
EvtNextPublisherId
EvtOpenChannelConfig
EvtOpenChannelEnum
EvtOpenEventMetadataEnum
EvtOpenLog
EvtOpenPublisherEnum
EvtOpenPublisherMetadata
EvtOpenSession
EvtQuery
EvtRender
EvtSaveChannelConfig
EvtSeek
EvtSetChannelConfigProperty
EvtSubscribe
EvtUpdateBookmark
