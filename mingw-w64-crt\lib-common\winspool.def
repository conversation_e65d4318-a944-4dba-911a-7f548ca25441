;
; Definition file of WINSPO<PERSON>.DRV
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WINSPOOL.DRV"
EXPORTS
ADVANCEDSETUPDIALOG
AdvancedSetupDialog
ConvertAnsiDevModeToUnicodeDevmode
ConvertUnicodeDevModeToAnsiDevmode
DEVICEMODE
DeviceMode
DocumentEvent
PerfClose
PerfCollect
PerfOpen
QueryColorProfile
QueryRemoteFonts
QuerySpoolMode
SpoolerDevQueryPrintW
StartDocDlgW
AbortPrinter
AddFormA
AddFormW
AddJobA
AddJobW
AddMonitorA
AddMonitorW
AddPortA
AddPortExA
AddPortExW
AddPortW
AddPrintProcessorA
AddPrintProcessorW
AddPrintProvidorA
AddPrintProvidorW
AddPrinterA
AddPrinterConnection2A
AddPrinterConnection2W
AddPrinterConnectionA
AddPrinterConnectionW
<PERSON>DriverA
AddPrinterDriverExA
AddPrinterDriverExW
AddPrinterDriverW
AddPrinterW
AdvancedDocumentPropertiesA
AdvancedDocumentPropertiesW
ClosePrinter
CloseSpoolFileHandle
CommitSpoolData
ConfigurePortA
ConfigurePortW
ConnectToPrinterDlg
CorePrinterDriverInstalledA
CorePrinterDriverInstalledW
CreatePrintAsyncNotifyChannel
CreatePrinterIC
DEVICECAPABILITIES
DeleteFormA
DeleteFormW
DeleteJobNamedProperty
DeleteMonitorA
DeleteMonitorW
DeletePortA
DeletePortW
DeletePrintProcessorA
DeletePrintProcessorW
DeletePrintProvidorA
DeletePrintProvidorW
DeletePrinter
DeletePrinterConnectionA
DeletePrinterConnectionW
DeletePrinterDataA
DeletePrinterDataExA
DeletePrinterDataExW
DeletePrinterDataW
DeletePrinterDriverA
DeletePrinterDriverExA
DeletePrinterDriverExW
DeletePrinterDriverPackageA
DeletePrinterDriverPackageW
DeletePrinterDriverW
DeletePrinterIC
DeletePrinterKeyA
DeletePrinterKeyW
DevQueryPrint
DevQueryPrintEx
DeviceCapabilities
DeviceCapabilitiesA
DeviceCapabilitiesW
DevicePropertySheets
DocumentPropertiesA
DocumentPropertiesW
DocumentPropertySheets
EXTDEVICEMODE
EndDocPrinter
EndPagePrinter
EnumFormsA
EnumFormsW
EnumJobNamedProperties
EnumJobsA
EnumJobsW
EnumMonitorsA
EnumMonitorsW
EnumPortsA
GetDefaultPrinterA
SetDefaultPrinterA
GetDefaultPrinterW
SetDefaultPrinterW
EnumPortsW
EnumPrintProcessorDatatypesA
EnumPrintProcessorDatatypesW
EnumPrintProcessorsA
EnumPrintProcessorsW
EnumPrinterDataA
EnumPrinterDataExA
EnumPrinterDataExW
EnumPrinterDataW
EnumPrinterDriversA
EnumPrinterDriversW
EnumPrinterKeyA
EnumPrinterKeyW
EnumPrintersA
EnumPrintersW
ExtDeviceMode
FindClosePrinterChangeNotification
FindFirstPrinterChangeNotification
FindNextPrinterChangeNotification
FlushPrinter
FreePrintNamedPropertyArray
FreePrintPropertyValue
FreePrinterNotifyInfo
GetCorePrinterDriversA
GetCorePrinterDriversW
GetFormA
GetFormW
GetJobA
GetJobNamedPropertyValue
GetJobW
GetPrintExecutionData
GetPrintOutputInfo
GetPrintProcessorDirectoryA
GetPrintProcessorDirectoryW
GetPrinterA
GetPrinterDataA
GetPrinterDataExA
GetPrinterDataExW
GetPrinterDataW
GetPrinterDriver2A
GetPrinterDriver2W
GetPrinterDriverA
GetPrinterDriverDirectoryA
GetPrinterDriverDirectoryW
GetPrinterDriverPackagePathA
GetPrinterDriverPackagePathW
GetPrinterDriverW
GetPrinterW
GetSpoolFileHandle
InstallPrinterDriverFromPackageA
InstallPrinterDriverFromPackageW
IsValidDevmodeA
IsValidDevmodeW
OpenPrinter2A
OpenPrinter2W
OpenPrinterA
OpenPrinterW
PlayGdiScriptOnPrinterIC
PrinterMessageBoxA
PrinterMessageBoxW
PrinterProperties
ReadPrinter
RegisterForPrintAsyncNotifications
ReportJobProcessingProgress
ResetPrinterA
ResetPrinterW
ScheduleJob
SeekPrinter
SetAllocFailCount
SetFormA
SetFormW
SetJobA
SetJobNamedProperty
SetJobW
SetPortA
SetPortW
SetPrinterA
SetPrinterDataA
SetPrinterDataExA
SetPrinterDataExW
SetPrinterDataW
SetPrinterW
SplDriverUnloadComplete
SpoolerInit
SpoolerPrinterEvent
StartDocDlgA
StartDocPrinterA
StartDocPrinterW
StartPagePrinter
UnRegisterForPrintAsyncNotifications
UploadPrinterDriverPackageA
UploadPrinterDriverPackageW
WaitForPrinterChange
WritePrinter
XcvDataW
