;
; Definition file of MAPI32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "MAPI32.dll"
EXPORTS
ord_8 @8
MAPILogonEx
MAPIAllocateBuffer
MAPIAllocateMore
MAPIFreeBuffer
MAPIAdminProfiles
MAPIInitialize
MAPIUninitialize
PRProviderInit
LAUNCHWIZARD
LaunchWizard
MAPIOpenFormMgr
MAPIOpenLocalFormContainer
ScInitMapiUtil
DeinitMapiUtil
ScGenerateMuid
HrAllocAdviseSink
WrapProgress
HrThisThreadAdviseSink
ScBinFromHexBounded
FBinFromHex
HexFromBin
BuildDisplayTable
SwapPlong
SwapPword
MAPIInitIdle
MAPIDeinitIdle
InstallFilterHook
FtgRegisterIdleRoutine
EnableIdleRoutine
DeregisterIdleRoutine
ChangeIdleRoutine
MAPIGetDefaultMalloc
CreateIProp
CreateTable
MNLS_lstrlenW
MNLS_lstrcmp<PERSON>_lstrcpyW
MNLS_CompareStringW
MNLS_MultiByteToWideChar
MNLS_WideCharToMultiByte
MNLS_IsBadStringPtrW
FEqualNames
WrapStoreEntryID
IsBadBoundedStringPtr
HrQueryAllRows
PropCopyMore
UlPropSize
FPropContainsProp
FPropCompareProp
LPropCompareProp
HrAddColumns
HrAddColumnsEx
FtAddFt
FtAdcFt
FtSubFt
FtMulDw
FtMulDwDw
FtNegFt
FtDivFtBogus
UlAddRef
UlRelease
SzFindCh
SzFindLastCh
SzFindSz
UFromSz
HrGetOneProp
HrSetOneProp
FPropExists
PpropFindProp
FreePadrlist
FreeProws
HrSzFromEntryID
HrEntryIDFromSz
HrComposeEID
HrDecomposeEID
HrComposeMsgID
HrDecomposeMsgID
OpenStreamOnFile
OpenTnefStream
OpenTnefStreamEx
GetTnefStreamCodepage
UlFromSzHex
UNKOBJ_ScAllocate
UNKOBJ_ScAllocateMore
UNKOBJ_Free
UNKOBJ_FreeRows
UNKOBJ_ScCOAllocate
UNKOBJ_ScCOReallocate
UNKOBJ_COFree
UNKOBJ_ScSzFromIdsAlloc
ScCountNotifications
ScCopyNotifications
ScRelocNotifications
ScCountProps
ScCopyProps
ScRelocProps
LpValFindProp
ScDupPropset
FBadRglpszA
FBadRglpszW
FBadRowSet
FBadRglpNameID
FBadPropTag
FBadRow
FBadProp
FBadColumnSet
RTFSync
WrapCompressedRTFStream
__ValidateParameters
__CPPValidateParameters
FBadSortOrderSet
FBadEntryList
FBadRestriction
ScUNCFromLocalPath
ScLocalPathFromUNC
HrIStorageFromStream
HrValidateIPMSubtree
OpenIMsgSession
CloseIMsgSession
OpenIMsgOnIStg
SetAttribIMsgOnIStg
GetAttribIMsgOnIStg
MapStorageSCode
ScMAPIXFromCMC
ScMAPIXFromSMAPI
EncodeID
FDecodeID
CchOfEncoding
CbOfEncoded
MAPISendDocuments
MAPILogon
MAPILogoff
MAPISendMail
MAPISaveMail
MAPIReadMail
MAPIFindNext
MAPIDeleteMail
MAPIAddress
MAPIDetails
MAPIResolveName
BMAPISendMail
BMAPISaveMail
BMAPIReadMail
BMAPIGetReadMail
BMAPIFindNext
BMAPIAddress
BMAPIGetAddress
BMAPIDetails
BMAPIResolveName
cmc_act_on
cmc_free
cmc_list
cmc_logoff
cmc_logon
cmc_look_up
cmc_query_configuration
cmc_read
cmc_send
cmc_send_documents
HrDispatchNotifications
HrValidateParametersV
HrValidateParametersValist
ScCreateConversationIndex
HrGetOmiProvidersFlags
HrSetOmiProvidersFlagsInvalid
GetOutlookVersion
FixMAPI
FGetComponentPath
MAPISendMailW
