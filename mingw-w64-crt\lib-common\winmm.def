LIBRARY "WINMM.dll"
EXPORTS
mciExecute
CloseDriver
DefDriverProc
DriverCallback
DrvGetModuleHandle
GetDriverModuleHandle
MigrateAllDrivers
MigrateSoundEvents
NotifyCallbackData
OpenDriver
PlaySound
PlaySoundA
PlaySoundW
SendDriverMessage
WOW32DriverCallback
WOW32ResolveMultiMediaHandle
WOWAppExit
WinmmLogoff
WinmmLogon
aux32Message
auxGetDevCapsA
auxGetDevCapsW
auxGetNumDevs
auxGetVolume
auxOutMessage
auxSetVolume
gfxAddGfx
gfxBatchChange
gfxCreateGfxFactoriesList
gfxCreateZoneFactoriesList
gfxDestroyDeviceInterfaceList
gfxEnumerateGfxs
gfxLogoff
gfxLogon
gfxModifyGfx
gfxOpenGfx
gfxRemoveGfx
joy32Message
joyConfigChanged
joyGetDevCapsA
joyGetDevCapsW
joyGetNumDevs
joyGetPos
joyGetPosEx
joyGetThreshold
joyReleaseCapture
joySetCapture
joySetThreshold
mci32Message
mciDriverNotify
mciDriverYield
mciFreeCommandResource
mciGetCreatorTask
mciGetDeviceIDA
mciGetDeviceIDFromElementIDA
mciGetDeviceIDFromElementIDW
mciGetDeviceIDW
mciGetDriverData
mciGetErrorStringA
mciGetErrorStringW
mciGetYieldProc
mciLoadCommandResource
mciSendCommandA
mciSendCommandW
mciSendStringA
mciSendStringW
mciSetDriverData
mciSetYieldProc
mid32Message
midiConnect
midiDisconnect
midiInAddBuffer
midiInClose
midiInGetDevCapsA
midiInGetDevCapsW
midiInGetErrorTextA
midiInGetErrorTextW
midiInGetID
midiInGetNumDevs
midiInMessage
midiInOpen
midiInPrepareHeader
midiInReset
midiInStart
midiInStop
midiInUnprepareHeader
midiOutCacheDrumPatches
midiOutCachePatches
midiOutClose
midiOutGetDevCapsA
midiOutGetDevCapsW
midiOutGetErrorTextA
midiOutGetErrorTextW
midiOutGetID
midiOutGetNumDevs
midiOutGetVolume
midiOutLongMsg
midiOutMessage
midiOutOpen
midiOutPrepareHeader
midiOutReset
midiOutSetVolume
midiOutShortMsg
midiOutUnprepareHeader
midiStreamClose
midiStreamOpen
midiStreamOut
midiStreamPause
midiStreamPosition
midiStreamProperty
midiStreamRestart
midiStreamStop
mixerClose
mixerGetControlDetailsA
mixerGetControlDetailsW
mixerGetDevCapsA
mixerGetDevCapsW
mixerGetID
mixerGetLineControlsA
mixerGetLineControlsW
mixerGetLineInfoA
mixerGetLineInfoW
mixerGetNumDevs
mixerMessage
mixerOpen
mixerSetControlDetails
mmDrvInstall
mmGetCurrentTask
mmTaskBlock
mmTaskCreate
mmTaskSignal
mmTaskYield
mmioAdvance
mmioAscend
mmioClose
mmioCreateChunk
mmioDescend
mmioFlush
mmioGetInfo
mmioInstallIOProcA
mmioInstallIOProcW
mmioOpenA
mmioOpenW
mmioRead
mmioRenameA
mmioRenameW
mmioSeek
mmioSendMessage
mmioSetBuffer
mmioSetInfo
mmioStringToFOURCCA
mmioStringToFOURCCW
mmioWrite
mmsystemGetVersion
mod32Message
mxd32Message
sndPlaySoundA
sndPlaySoundW
tid32Message
timeBeginPeriod
timeEndPeriod
timeGetDevCaps
timeGetSystemTime
timeGetTime
timeKillEvent
timeSetEvent
waveInAddBuffer
waveInClose
waveInGetDevCapsA
waveInGetDevCapsW
waveInGetErrorTextA
waveInGetErrorTextW
waveInGetID
waveInGetNumDevs
waveInGetPosition
waveInMessage
waveInOpen
waveInPrepareHeader
waveInReset
waveInStart
waveInStop
waveInUnprepareHeader
waveOutBreakLoop
waveOutClose
waveOutGetDevCapsA
waveOutGetDevCapsW
waveOutGetErrorTextA
waveOutGetErrorTextW
waveOutGetID
waveOutGetNumDevs
waveOutGetPitch
waveOutGetPlaybackRate
waveOutGetPosition
waveOutGetVolume
waveOutMessage
waveOutOpen
waveOutPause
waveOutPrepareHeader
waveOutReset
waveOutRestart
waveOutSetPitch
waveOutSetPlaybackRate
waveOutSetVolume
waveOutUnprepareHeader
waveOutWrite
winmmDbgOut
winmmSetDebugLevel
wid32Message
wod32Message
