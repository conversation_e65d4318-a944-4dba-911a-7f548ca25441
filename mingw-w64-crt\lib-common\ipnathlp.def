;
; Definition file of IPNATHLP.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "IPNATHLP.dll"
EXPORTS
NhAcceptStreamSocket
NhAcquireFixedLengthBuffer
NhAcquireVariableLengthBuffer
NhCreateDatagramSocket
NhCreateStreamSocket
NhDeleteSocket
NhInitializeBufferManagement
NhReadDatagramSocket
NhReadStreamSocket
NhReleaseBuffer
NhWriteDatagramSocket
NhWriteStreamSocket
RegisterProtocol
SvchostPushServiceGlobals
NatAcquirePortReservation
NatCancelDynamicRedirect
NatCancelRedirect
NatCreateDynamicFullRedirect
NatCreateDynamicRedirect
NatCreateDynamicRedirectEx
NatCreateRedirect
NatCreateRedirectEx
NatInitializePortReservation
NatInitializeTranslator
NatLookupAndQueryInformationSessionMapping
NatQueryInformationRedirect
NatQueryIn<PERSON>R<PERSON>irectHandle
NatReleasePortReservation
NatShutdownPortReservation
NatShutdownTranslator
NhInitializeTraceManagement
ServiceMain
