/*** Autogenerated by WIDL 8.5 from include/d3d12shader.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d12shader_h__
#define __d3d12shader_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __ID3D12ShaderReflectionType_FWD_DEFINED__
#define __ID3D12ShaderReflectionType_FWD_DEFINED__
typedef interface ID3D12ShaderReflectionType ID3D12ShaderReflectionType;
#ifdef __cplusplus
interface ID3D12ShaderReflectionType;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12ShaderReflectionVariable_FWD_DEFINED__
#define __ID3D12ShaderReflectionVariable_FWD_DEFINED__
typedef interface ID3D12ShaderReflectionVariable ID3D12ShaderReflectionVariable;
#ifdef __cplusplus
interface ID3D12ShaderReflectionVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12ShaderReflectionConstantBuffer_FWD_DEFINED__
#define __ID3D12ShaderReflectionConstantBuffer_FWD_DEFINED__
typedef interface ID3D12ShaderReflectionConstantBuffer ID3D12ShaderReflectionConstantBuffer;
#ifdef __cplusplus
interface ID3D12ShaderReflectionConstantBuffer;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12ShaderReflection_FWD_DEFINED__
#define __ID3D12ShaderReflection_FWD_DEFINED__
typedef interface ID3D12ShaderReflection ID3D12ShaderReflection;
#ifdef __cplusplus
interface ID3D12ShaderReflection;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12FunctionParameterReflection_FWD_DEFINED__
#define __ID3D12FunctionParameterReflection_FWD_DEFINED__
typedef interface ID3D12FunctionParameterReflection ID3D12FunctionParameterReflection;
#ifdef __cplusplus
interface ID3D12FunctionParameterReflection;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12FunctionReflection_FWD_DEFINED__
#define __ID3D12FunctionReflection_FWD_DEFINED__
typedef interface ID3D12FunctionReflection ID3D12FunctionReflection;
#ifdef __cplusplus
interface ID3D12FunctionReflection;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12LibraryReflection_FWD_DEFINED__
#define __ID3D12LibraryReflection_FWD_DEFINED__
typedef interface ID3D12LibraryReflection ID3D12LibraryReflection;
#ifdef __cplusplus
interface ID3D12LibraryReflection;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <d3dcommon.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum D3D12_SHADER_VERSION_TYPE {
    D3D12_SHVER_PIXEL_SHADER = 0x0,
    D3D12_SHVER_VERTEX_SHADER = 0x1,
    D3D12_SHVER_GEOMETRY_SHADER = 0x2,
    D3D12_SHVER_HULL_SHADER = 0x3,
    D3D12_SHVER_DOMAIN_SHADER = 0x4,
    D3D12_SHVER_COMPUTE_SHADER = 0x5,
    D3D12_SHVER_RESERVED0 = 0xfff0
} D3D12_SHADER_VERSION_TYPE;
typedef struct _D3D12_SHADER_DESC {
    UINT Version;
    const char *Creator;
    UINT Flags;
    UINT ConstantBuffers;
    UINT BoundResources;
    UINT InputParameters;
    UINT OutputParameters;
    UINT InstructionCount;
    UINT TempRegisterCount;
    UINT TempArrayCount;
    UINT DefCount;
    UINT DclCount;
    UINT TextureNormalInstructions;
    UINT TextureLoadInstructions;
    UINT TextureCompInstructions;
    UINT TextureBiasInstructions;
    UINT TextureGradientInstructions;
    UINT FloatInstructionCount;
    UINT IntInstructionCount;
    UINT UintInstructionCount;
    UINT StaticFlowControlCount;
    UINT DynamicFlowControlCount;
    UINT MacroInstructionCount;
    UINT ArrayInstructionCount;
    UINT CutInstructionCount;
    UINT EmitInstructionCount;
    D3D_PRIMITIVE_TOPOLOGY GSOutputTopology;
    UINT GSMaxOutputVertexCount;
    D3D_PRIMITIVE InputPrimitive;
    UINT PatchConstantParameters;
    UINT cGSInstanceCount;
    UINT cControlPoints;
    D3D_TESSELLATOR_OUTPUT_PRIMITIVE HSOutputPrimitive;
    D3D_TESSELLATOR_PARTITIONING HSPartitioning;
    D3D_TESSELLATOR_DOMAIN TessellatorDomain;
    UINT cBarrierInstructions;
    UINT cInterlockedInstructions;
    UINT cTextureStoreInstructions;
} D3D12_SHADER_DESC;
typedef struct _D3D12_SHADER_VARIABLE_DESC {
    const char *Name;
    UINT StartOffset;
    UINT Size;
    UINT uFlags;
    void *DefaultValue;
    UINT StartTexture;
    UINT TextureSize;
    UINT StartSampler;
    UINT SamplerSize;
} D3D12_SHADER_VARIABLE_DESC;
typedef struct _D3D12_SHADER_TYPE_DESC {
    D3D_SHADER_VARIABLE_CLASS Class;
    D3D_SHADER_VARIABLE_TYPE Type;
    UINT Rows;
    UINT Columns;
    UINT Elements;
    UINT Members;
    UINT Offset;
    const char *Name;
} D3D12_SHADER_TYPE_DESC;
typedef struct _D3D12_SHADER_BUFFER_DESC {
    const char *Name;
    D3D_CBUFFER_TYPE Type;
    UINT Variables;
    UINT Size;
    UINT uFlags;
} D3D12_SHADER_BUFFER_DESC;
typedef struct _D3D12_SHADER_INPUT_BIND_DESC {
    const char *Name;
    D3D_SHADER_INPUT_TYPE Type;
    UINT BindPoint;
    UINT BindCount;
    UINT uFlags;
    D3D_RESOURCE_RETURN_TYPE ReturnType;
    D3D_SRV_DIMENSION Dimension;
    UINT NumSamples;
    UINT Space;
    UINT uID;
} D3D12_SHADER_INPUT_BIND_DESC;
typedef struct _D3D12_SIGNATURE_PARAMETER_DESC {
    const char *SemanticName;
    UINT SemanticIndex;
    UINT Register;
    D3D_NAME SystemValueType;
    D3D_REGISTER_COMPONENT_TYPE ComponentType;
    BYTE Mask;
    BYTE ReadWriteMask;
    UINT Stream;
    D3D_MIN_PRECISION MinPrecision;
} D3D12_SIGNATURE_PARAMETER_DESC;
typedef struct _D3D12_PARAMETER_DESC {
    const char *Name;
    const char *SemanticName;
    D3D_SHADER_VARIABLE_TYPE Type;
    D3D_SHADER_VARIABLE_CLASS Class;
    UINT Rows;
    UINT Columns;
    D3D_INTERPOLATION_MODE InterpolationMode;
    D3D_PARAMETER_FLAGS Flags;
    UINT FirstInRegister;
    UINT FirstInComponent;
    UINT FirstOutRegister;
    UINT FirstOutComponent;
} D3D12_PARAMETER_DESC;
typedef struct _D3D12_FUNCTION_DESC {
    UINT Version;
    const char *Creator;
    UINT Flags;
    UINT ConstantBuffers;
    UINT BoundResources;
    UINT InstructionCount;
    UINT TempRegisterCount;
    UINT TempArrayCount;
    UINT DefCount;
    UINT DclCount;
    UINT TextureNormalInstructions;
    UINT TextureLoadInstructions;
    UINT TextureCompInstructions;
    UINT TextureBiasInstructions;
    UINT TextureGradientInstructions;
    UINT FloatInstructionCount;
    UINT IntInstructionCount;
    UINT UintInstructionCount;
    UINT StaticFlowControlCount;
    UINT DynamicFlowControlCount;
    UINT MacroInstructionCount;
    UINT ArrayInstructionCount;
    UINT MovInstructionCount;
    UINT MovcInstructionCount;
    UINT ConversionInstructionCount;
    UINT BitwiseInstructionCount;
    D3D_FEATURE_LEVEL MinFeatureLevel;
    UINT64 RequiredFeatureFlags;
    const char *Name;
    INT FunctionParameterCount;
    WINBOOL HasReturn;
    WINBOOL Has10Level9VertexShader;
    WINBOOL Has10Level9PixelShader;
} D3D12_FUNCTION_DESC;
typedef struct _D3D12_LIBRARY_DESC {
    const char *Creator;
    UINT Flags;
    UINT FunctionCount;
} D3D12_LIBRARY_DESC;
#ifndef __ID3D12ShaderReflectionConstantBuffer_FWD_DEFINED__
#define __ID3D12ShaderReflectionConstantBuffer_FWD_DEFINED__
typedef interface ID3D12ShaderReflectionConstantBuffer ID3D12ShaderReflectionConstantBuffer;
#ifdef __cplusplus
interface ID3D12ShaderReflectionConstantBuffer;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ID3D12ShaderReflectionType interface
 */
#ifndef __ID3D12ShaderReflectionType_INTERFACE_DEFINED__
#define __ID3D12ShaderReflectionType_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12ShaderReflectionType, 0xe913c351, 0x783d, 0x48ca, 0xa1,0xd1, 0x4f,0x30,0x62,0x84,0xad,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e913c351-783d-48ca-a1d1-4f306284ad56")
ID3D12ShaderReflectionType
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_SHADER_TYPE_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionType * STDMETHODCALLTYPE GetMemberTypeByIndex(
        UINT index) = 0;

    virtual ID3D12ShaderReflectionType * STDMETHODCALLTYPE GetMemberTypeByName(
        const char *name) = 0;

    virtual const char * STDMETHODCALLTYPE GetMemberTypeName(
        UINT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqual(
        ID3D12ShaderReflectionType *type) = 0;

    virtual ID3D12ShaderReflectionType * STDMETHODCALLTYPE GetSubType(
        ) = 0;

    virtual ID3D12ShaderReflectionType * STDMETHODCALLTYPE GetBaseClass(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetNumInterfaces(
        ) = 0;

    virtual ID3D12ShaderReflectionType * STDMETHODCALLTYPE GetInterfaceByIndex(
        UINT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsOfType(
        ID3D12ShaderReflectionType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImplementsInterface(
        ID3D12ShaderReflectionType *base) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12ShaderReflectionType, 0xe913c351, 0x783d, 0x48ca, 0xa1,0xd1, 0x4f,0x30,0x62,0x84,0xad,0x56)
#endif
#else
typedef struct ID3D12ShaderReflectionTypeVtbl {
    BEGIN_INTERFACE

    /*** ID3D12ShaderReflectionType methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12ShaderReflectionType *This,
        D3D12_SHADER_TYPE_DESC *desc);

    ID3D12ShaderReflectionType * (STDMETHODCALLTYPE *GetMemberTypeByIndex)(
        ID3D12ShaderReflectionType *This,
        UINT index);

    ID3D12ShaderReflectionType * (STDMETHODCALLTYPE *GetMemberTypeByName)(
        ID3D12ShaderReflectionType *This,
        const char *name);

    const char * (STDMETHODCALLTYPE *GetMemberTypeName)(
        ID3D12ShaderReflectionType *This,
        UINT index);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        ID3D12ShaderReflectionType *This,
        ID3D12ShaderReflectionType *type);

    ID3D12ShaderReflectionType * (STDMETHODCALLTYPE *GetSubType)(
        ID3D12ShaderReflectionType *This);

    ID3D12ShaderReflectionType * (STDMETHODCALLTYPE *GetBaseClass)(
        ID3D12ShaderReflectionType *This);

    UINT (STDMETHODCALLTYPE *GetNumInterfaces)(
        ID3D12ShaderReflectionType *This);

    ID3D12ShaderReflectionType * (STDMETHODCALLTYPE *GetInterfaceByIndex)(
        ID3D12ShaderReflectionType *This,
        UINT index);

    HRESULT (STDMETHODCALLTYPE *IsOfType)(
        ID3D12ShaderReflectionType *This,
        ID3D12ShaderReflectionType *type);

    HRESULT (STDMETHODCALLTYPE *ImplementsInterface)(
        ID3D12ShaderReflectionType *This,
        ID3D12ShaderReflectionType *base);

    END_INTERFACE
} ID3D12ShaderReflectionTypeVtbl;

interface ID3D12ShaderReflectionType {
    CONST_VTBL ID3D12ShaderReflectionTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D12ShaderReflectionType methods ***/
#define ID3D12ShaderReflectionType_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D12ShaderReflectionType_GetMemberTypeByIndex(This,index) (This)->lpVtbl->GetMemberTypeByIndex(This,index)
#define ID3D12ShaderReflectionType_GetMemberTypeByName(This,name) (This)->lpVtbl->GetMemberTypeByName(This,name)
#define ID3D12ShaderReflectionType_GetMemberTypeName(This,index) (This)->lpVtbl->GetMemberTypeName(This,index)
#define ID3D12ShaderReflectionType_IsEqual(This,type) (This)->lpVtbl->IsEqual(This,type)
#define ID3D12ShaderReflectionType_GetSubType(This) (This)->lpVtbl->GetSubType(This)
#define ID3D12ShaderReflectionType_GetBaseClass(This) (This)->lpVtbl->GetBaseClass(This)
#define ID3D12ShaderReflectionType_GetNumInterfaces(This) (This)->lpVtbl->GetNumInterfaces(This)
#define ID3D12ShaderReflectionType_GetInterfaceByIndex(This,index) (This)->lpVtbl->GetInterfaceByIndex(This,index)
#define ID3D12ShaderReflectionType_IsOfType(This,type) (This)->lpVtbl->IsOfType(This,type)
#define ID3D12ShaderReflectionType_ImplementsInterface(This,base) (This)->lpVtbl->ImplementsInterface(This,base)
#else
/*** ID3D12ShaderReflectionType methods ***/
static __WIDL_INLINE HRESULT ID3D12ShaderReflectionType_GetDesc(ID3D12ShaderReflectionType* This,D3D12_SHADER_TYPE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionType * ID3D12ShaderReflectionType_GetMemberTypeByIndex(ID3D12ShaderReflectionType* This,UINT index) {
    return This->lpVtbl->GetMemberTypeByIndex(This,index);
}
static __WIDL_INLINE ID3D12ShaderReflectionType * ID3D12ShaderReflectionType_GetMemberTypeByName(ID3D12ShaderReflectionType* This,const char *name) {
    return This->lpVtbl->GetMemberTypeByName(This,name);
}
static __WIDL_INLINE const char * ID3D12ShaderReflectionType_GetMemberTypeName(ID3D12ShaderReflectionType* This,UINT index) {
    return This->lpVtbl->GetMemberTypeName(This,index);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflectionType_IsEqual(ID3D12ShaderReflectionType* This,ID3D12ShaderReflectionType *type) {
    return This->lpVtbl->IsEqual(This,type);
}
static __WIDL_INLINE ID3D12ShaderReflectionType * ID3D12ShaderReflectionType_GetSubType(ID3D12ShaderReflectionType* This) {
    return This->lpVtbl->GetSubType(This);
}
static __WIDL_INLINE ID3D12ShaderReflectionType * ID3D12ShaderReflectionType_GetBaseClass(ID3D12ShaderReflectionType* This) {
    return This->lpVtbl->GetBaseClass(This);
}
static __WIDL_INLINE UINT ID3D12ShaderReflectionType_GetNumInterfaces(ID3D12ShaderReflectionType* This) {
    return This->lpVtbl->GetNumInterfaces(This);
}
static __WIDL_INLINE ID3D12ShaderReflectionType * ID3D12ShaderReflectionType_GetInterfaceByIndex(ID3D12ShaderReflectionType* This,UINT index) {
    return This->lpVtbl->GetInterfaceByIndex(This,index);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflectionType_IsOfType(ID3D12ShaderReflectionType* This,ID3D12ShaderReflectionType *type) {
    return This->lpVtbl->IsOfType(This,type);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflectionType_ImplementsInterface(ID3D12ShaderReflectionType* This,ID3D12ShaderReflectionType *base) {
    return This->lpVtbl->ImplementsInterface(This,base);
}
#endif
#endif

#endif


#endif  /* __ID3D12ShaderReflectionType_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12ShaderReflectionVariable interface
 */
#ifndef __ID3D12ShaderReflectionVariable_INTERFACE_DEFINED__
#define __ID3D12ShaderReflectionVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12ShaderReflectionVariable, 0x8337a8a6, 0xa216, 0x444a, 0xb2,0xf4, 0x31,0x47,0x33,0xa7,0x3a,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8337a8a6-a216-444a-b2f4-314733a73aea")
ID3D12ShaderReflectionVariable
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_SHADER_VARIABLE_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionType * STDMETHODCALLTYPE GetType(
        ) = 0;

    virtual ID3D12ShaderReflectionConstantBuffer * STDMETHODCALLTYPE GetBuffer(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetInterfaceSlot(
        UINT index) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12ShaderReflectionVariable, 0x8337a8a6, 0xa216, 0x444a, 0xb2,0xf4, 0x31,0x47,0x33,0xa7,0x3a,0xea)
#endif
#else
typedef struct ID3D12ShaderReflectionVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D12ShaderReflectionVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12ShaderReflectionVariable *This,
        D3D12_SHADER_VARIABLE_DESC *desc);

    ID3D12ShaderReflectionType * (STDMETHODCALLTYPE *GetType)(
        ID3D12ShaderReflectionVariable *This);

    ID3D12ShaderReflectionConstantBuffer * (STDMETHODCALLTYPE *GetBuffer)(
        ID3D12ShaderReflectionVariable *This);

    UINT (STDMETHODCALLTYPE *GetInterfaceSlot)(
        ID3D12ShaderReflectionVariable *This,
        UINT index);

    END_INTERFACE
} ID3D12ShaderReflectionVariableVtbl;

interface ID3D12ShaderReflectionVariable {
    CONST_VTBL ID3D12ShaderReflectionVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D12ShaderReflectionVariable methods ***/
#define ID3D12ShaderReflectionVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D12ShaderReflectionVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D12ShaderReflectionVariable_GetBuffer(This) (This)->lpVtbl->GetBuffer(This)
#define ID3D12ShaderReflectionVariable_GetInterfaceSlot(This,index) (This)->lpVtbl->GetInterfaceSlot(This,index)
#else
/*** ID3D12ShaderReflectionVariable methods ***/
static __WIDL_INLINE HRESULT ID3D12ShaderReflectionVariable_GetDesc(ID3D12ShaderReflectionVariable* This,D3D12_SHADER_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionType * ID3D12ShaderReflectionVariable_GetType(ID3D12ShaderReflectionVariable* This) {
    return This->lpVtbl->GetType(This);
}
static __WIDL_INLINE ID3D12ShaderReflectionConstantBuffer * ID3D12ShaderReflectionVariable_GetBuffer(ID3D12ShaderReflectionVariable* This) {
    return This->lpVtbl->GetBuffer(This);
}
static __WIDL_INLINE UINT ID3D12ShaderReflectionVariable_GetInterfaceSlot(ID3D12ShaderReflectionVariable* This,UINT index) {
    return This->lpVtbl->GetInterfaceSlot(This,index);
}
#endif
#endif

#endif


#endif  /* __ID3D12ShaderReflectionVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12ShaderReflectionConstantBuffer interface
 */
#ifndef __ID3D12ShaderReflectionConstantBuffer_INTERFACE_DEFINED__
#define __ID3D12ShaderReflectionConstantBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12ShaderReflectionConstantBuffer, 0xc59598b4, 0x48b3, 0x4869, 0xb9,0xb1, 0xb1,0x61,0x8b,0x14,0xa8,0xb7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c59598b4-48b3-4869-b9b1-b1618b14a8b7")
ID3D12ShaderReflectionConstantBuffer
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_SHADER_BUFFER_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionVariable * STDMETHODCALLTYPE GetVariableByIndex(
        UINT index) = 0;

    virtual ID3D12ShaderReflectionVariable * STDMETHODCALLTYPE GetVariableByName(
        const char *name) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12ShaderReflectionConstantBuffer, 0xc59598b4, 0x48b3, 0x4869, 0xb9,0xb1, 0xb1,0x61,0x8b,0x14,0xa8,0xb7)
#endif
#else
typedef struct ID3D12ShaderReflectionConstantBufferVtbl {
    BEGIN_INTERFACE

    /*** ID3D12ShaderReflectionConstantBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12ShaderReflectionConstantBuffer *This,
        D3D12_SHADER_BUFFER_DESC *desc);

    ID3D12ShaderReflectionVariable * (STDMETHODCALLTYPE *GetVariableByIndex)(
        ID3D12ShaderReflectionConstantBuffer *This,
        UINT index);

    ID3D12ShaderReflectionVariable * (STDMETHODCALLTYPE *GetVariableByName)(
        ID3D12ShaderReflectionConstantBuffer *This,
        const char *name);

    END_INTERFACE
} ID3D12ShaderReflectionConstantBufferVtbl;

interface ID3D12ShaderReflectionConstantBuffer {
    CONST_VTBL ID3D12ShaderReflectionConstantBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D12ShaderReflectionConstantBuffer methods ***/
#define ID3D12ShaderReflectionConstantBuffer_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D12ShaderReflectionConstantBuffer_GetVariableByIndex(This,index) (This)->lpVtbl->GetVariableByIndex(This,index)
#define ID3D12ShaderReflectionConstantBuffer_GetVariableByName(This,name) (This)->lpVtbl->GetVariableByName(This,name)
#else
/*** ID3D12ShaderReflectionConstantBuffer methods ***/
static __WIDL_INLINE HRESULT ID3D12ShaderReflectionConstantBuffer_GetDesc(ID3D12ShaderReflectionConstantBuffer* This,D3D12_SHADER_BUFFER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionVariable * ID3D12ShaderReflectionConstantBuffer_GetVariableByIndex(ID3D12ShaderReflectionConstantBuffer* This,UINT index) {
    return This->lpVtbl->GetVariableByIndex(This,index);
}
static __WIDL_INLINE ID3D12ShaderReflectionVariable * ID3D12ShaderReflectionConstantBuffer_GetVariableByName(ID3D12ShaderReflectionConstantBuffer* This,const char *name) {
    return This->lpVtbl->GetVariableByName(This,name);
}
#endif
#endif

#endif


#endif  /* __ID3D12ShaderReflectionConstantBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12ShaderReflection interface
 */
#ifndef __ID3D12ShaderReflection_INTERFACE_DEFINED__
#define __ID3D12ShaderReflection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12ShaderReflection, 0x5a58797d, 0xa72c, 0x478d, 0x8b,0xa2, 0xef,0xc6,0xb0,0xef,0xe8,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5a58797d-a72c-478d-8ba2-efc6b0efe88e")
ID3D12ShaderReflection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_SHADER_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionConstantBuffer * STDMETHODCALLTYPE GetConstantBufferByIndex(
        UINT index) = 0;

    virtual ID3D12ShaderReflectionConstantBuffer * STDMETHODCALLTYPE GetConstantBufferByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceBindingDesc(
        UINT index,
        D3D12_SHADER_INPUT_BIND_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputParameterDesc(
        UINT index,
        D3D12_SIGNATURE_PARAMETER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputParameterDesc(
        UINT index,
        D3D12_SIGNATURE_PARAMETER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPatchConstantParameterDesc(
        UINT index,
        D3D12_SIGNATURE_PARAMETER_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionVariable * STDMETHODCALLTYPE GetVariableByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceBindingDescByName(
        const char *name,
        D3D12_SHADER_INPUT_BIND_DESC *desc) = 0;

    virtual UINT STDMETHODCALLTYPE GetMovInstructionCount(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetMovcInstructionCount(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetConversionInstructionCount(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetBitwiseInstructionCount(
        ) = 0;

    virtual D3D_PRIMITIVE STDMETHODCALLTYPE GetGSInputPrimitive(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsSampleFrequencyShader(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetNumInterfaceSlots(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMinFeatureLevel(
        D3D_FEATURE_LEVEL *level) = 0;

    virtual UINT STDMETHODCALLTYPE GetThreadGroupSize(
        UINT *sizex,
        UINT *sizey,
        UINT *sizez) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetRequiresFlags(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12ShaderReflection, 0x5a58797d, 0xa72c, 0x478d, 0x8b,0xa2, 0xef,0xc6,0xb0,0xef,0xe8,0x8e)
#endif
#else
typedef struct ID3D12ShaderReflectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12ShaderReflection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12ShaderReflection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12ShaderReflection *This);

    /*** ID3D12ShaderReflection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12ShaderReflection *This,
        D3D12_SHADER_DESC *desc);

    ID3D12ShaderReflectionConstantBuffer * (STDMETHODCALLTYPE *GetConstantBufferByIndex)(
        ID3D12ShaderReflection *This,
        UINT index);

    ID3D12ShaderReflectionConstantBuffer * (STDMETHODCALLTYPE *GetConstantBufferByName)(
        ID3D12ShaderReflection *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *GetResourceBindingDesc)(
        ID3D12ShaderReflection *This,
        UINT index,
        D3D12_SHADER_INPUT_BIND_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetInputParameterDesc)(
        ID3D12ShaderReflection *This,
        UINT index,
        D3D12_SIGNATURE_PARAMETER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetOutputParameterDesc)(
        ID3D12ShaderReflection *This,
        UINT index,
        D3D12_SIGNATURE_PARAMETER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetPatchConstantParameterDesc)(
        ID3D12ShaderReflection *This,
        UINT index,
        D3D12_SIGNATURE_PARAMETER_DESC *desc);

    ID3D12ShaderReflectionVariable * (STDMETHODCALLTYPE *GetVariableByName)(
        ID3D12ShaderReflection *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *GetResourceBindingDescByName)(
        ID3D12ShaderReflection *This,
        const char *name,
        D3D12_SHADER_INPUT_BIND_DESC *desc);

    UINT (STDMETHODCALLTYPE *GetMovInstructionCount)(
        ID3D12ShaderReflection *This);

    UINT (STDMETHODCALLTYPE *GetMovcInstructionCount)(
        ID3D12ShaderReflection *This);

    UINT (STDMETHODCALLTYPE *GetConversionInstructionCount)(
        ID3D12ShaderReflection *This);

    UINT (STDMETHODCALLTYPE *GetBitwiseInstructionCount)(
        ID3D12ShaderReflection *This);

    D3D_PRIMITIVE (STDMETHODCALLTYPE *GetGSInputPrimitive)(
        ID3D12ShaderReflection *This);

    WINBOOL (STDMETHODCALLTYPE *IsSampleFrequencyShader)(
        ID3D12ShaderReflection *This);

    UINT (STDMETHODCALLTYPE *GetNumInterfaceSlots)(
        ID3D12ShaderReflection *This);

    HRESULT (STDMETHODCALLTYPE *GetMinFeatureLevel)(
        ID3D12ShaderReflection *This,
        D3D_FEATURE_LEVEL *level);

    UINT (STDMETHODCALLTYPE *GetThreadGroupSize)(
        ID3D12ShaderReflection *This,
        UINT *sizex,
        UINT *sizey,
        UINT *sizez);

    UINT64 (STDMETHODCALLTYPE *GetRequiresFlags)(
        ID3D12ShaderReflection *This);

    END_INTERFACE
} ID3D12ShaderReflectionVtbl;

interface ID3D12ShaderReflection {
    CONST_VTBL ID3D12ShaderReflectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12ShaderReflection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12ShaderReflection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12ShaderReflection_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12ShaderReflection methods ***/
#define ID3D12ShaderReflection_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D12ShaderReflection_GetConstantBufferByIndex(This,index) (This)->lpVtbl->GetConstantBufferByIndex(This,index)
#define ID3D12ShaderReflection_GetConstantBufferByName(This,name) (This)->lpVtbl->GetConstantBufferByName(This,name)
#define ID3D12ShaderReflection_GetResourceBindingDesc(This,index,desc) (This)->lpVtbl->GetResourceBindingDesc(This,index,desc)
#define ID3D12ShaderReflection_GetInputParameterDesc(This,index,desc) (This)->lpVtbl->GetInputParameterDesc(This,index,desc)
#define ID3D12ShaderReflection_GetOutputParameterDesc(This,index,desc) (This)->lpVtbl->GetOutputParameterDesc(This,index,desc)
#define ID3D12ShaderReflection_GetPatchConstantParameterDesc(This,index,desc) (This)->lpVtbl->GetPatchConstantParameterDesc(This,index,desc)
#define ID3D12ShaderReflection_GetVariableByName(This,name) (This)->lpVtbl->GetVariableByName(This,name)
#define ID3D12ShaderReflection_GetResourceBindingDescByName(This,name,desc) (This)->lpVtbl->GetResourceBindingDescByName(This,name,desc)
#define ID3D12ShaderReflection_GetMovInstructionCount(This) (This)->lpVtbl->GetMovInstructionCount(This)
#define ID3D12ShaderReflection_GetMovcInstructionCount(This) (This)->lpVtbl->GetMovcInstructionCount(This)
#define ID3D12ShaderReflection_GetConversionInstructionCount(This) (This)->lpVtbl->GetConversionInstructionCount(This)
#define ID3D12ShaderReflection_GetBitwiseInstructionCount(This) (This)->lpVtbl->GetBitwiseInstructionCount(This)
#define ID3D12ShaderReflection_GetGSInputPrimitive(This) (This)->lpVtbl->GetGSInputPrimitive(This)
#define ID3D12ShaderReflection_IsSampleFrequencyShader(This) (This)->lpVtbl->IsSampleFrequencyShader(This)
#define ID3D12ShaderReflection_GetNumInterfaceSlots(This) (This)->lpVtbl->GetNumInterfaceSlots(This)
#define ID3D12ShaderReflection_GetMinFeatureLevel(This,level) (This)->lpVtbl->GetMinFeatureLevel(This,level)
#define ID3D12ShaderReflection_GetThreadGroupSize(This,sizex,sizey,sizez) (This)->lpVtbl->GetThreadGroupSize(This,sizex,sizey,sizez)
#define ID3D12ShaderReflection_GetRequiresFlags(This) (This)->lpVtbl->GetRequiresFlags(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_QueryInterface(ID3D12ShaderReflection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D12ShaderReflection_AddRef(ID3D12ShaderReflection* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D12ShaderReflection_Release(ID3D12ShaderReflection* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12ShaderReflection methods ***/
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetDesc(ID3D12ShaderReflection* This,D3D12_SHADER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionConstantBuffer * ID3D12ShaderReflection_GetConstantBufferByIndex(ID3D12ShaderReflection* This,UINT index) {
    return This->lpVtbl->GetConstantBufferByIndex(This,index);
}
static __WIDL_INLINE ID3D12ShaderReflectionConstantBuffer * ID3D12ShaderReflection_GetConstantBufferByName(ID3D12ShaderReflection* This,const char *name) {
    return This->lpVtbl->GetConstantBufferByName(This,name);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetResourceBindingDesc(ID3D12ShaderReflection* This,UINT index,D3D12_SHADER_INPUT_BIND_DESC *desc) {
    return This->lpVtbl->GetResourceBindingDesc(This,index,desc);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetInputParameterDesc(ID3D12ShaderReflection* This,UINT index,D3D12_SIGNATURE_PARAMETER_DESC *desc) {
    return This->lpVtbl->GetInputParameterDesc(This,index,desc);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetOutputParameterDesc(ID3D12ShaderReflection* This,UINT index,D3D12_SIGNATURE_PARAMETER_DESC *desc) {
    return This->lpVtbl->GetOutputParameterDesc(This,index,desc);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetPatchConstantParameterDesc(ID3D12ShaderReflection* This,UINT index,D3D12_SIGNATURE_PARAMETER_DESC *desc) {
    return This->lpVtbl->GetPatchConstantParameterDesc(This,index,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionVariable * ID3D12ShaderReflection_GetVariableByName(ID3D12ShaderReflection* This,const char *name) {
    return This->lpVtbl->GetVariableByName(This,name);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetResourceBindingDescByName(ID3D12ShaderReflection* This,const char *name,D3D12_SHADER_INPUT_BIND_DESC *desc) {
    return This->lpVtbl->GetResourceBindingDescByName(This,name,desc);
}
static __WIDL_INLINE UINT ID3D12ShaderReflection_GetMovInstructionCount(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetMovInstructionCount(This);
}
static __WIDL_INLINE UINT ID3D12ShaderReflection_GetMovcInstructionCount(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetMovcInstructionCount(This);
}
static __WIDL_INLINE UINT ID3D12ShaderReflection_GetConversionInstructionCount(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetConversionInstructionCount(This);
}
static __WIDL_INLINE UINT ID3D12ShaderReflection_GetBitwiseInstructionCount(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetBitwiseInstructionCount(This);
}
static __WIDL_INLINE D3D_PRIMITIVE ID3D12ShaderReflection_GetGSInputPrimitive(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetGSInputPrimitive(This);
}
static __WIDL_INLINE WINBOOL ID3D12ShaderReflection_IsSampleFrequencyShader(ID3D12ShaderReflection* This) {
    return This->lpVtbl->IsSampleFrequencyShader(This);
}
static __WIDL_INLINE UINT ID3D12ShaderReflection_GetNumInterfaceSlots(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetNumInterfaceSlots(This);
}
static __WIDL_INLINE HRESULT ID3D12ShaderReflection_GetMinFeatureLevel(ID3D12ShaderReflection* This,D3D_FEATURE_LEVEL *level) {
    return This->lpVtbl->GetMinFeatureLevel(This,level);
}
static __WIDL_INLINE UINT ID3D12ShaderReflection_GetThreadGroupSize(ID3D12ShaderReflection* This,UINT *sizex,UINT *sizey,UINT *sizez) {
    return This->lpVtbl->GetThreadGroupSize(This,sizex,sizey,sizez);
}
static __WIDL_INLINE UINT64 ID3D12ShaderReflection_GetRequiresFlags(ID3D12ShaderReflection* This) {
    return This->lpVtbl->GetRequiresFlags(This);
}
#endif
#endif

#endif


#endif  /* __ID3D12ShaderReflection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12FunctionParameterReflection interface
 */
#ifndef __ID3D12FunctionParameterReflection_INTERFACE_DEFINED__
#define __ID3D12FunctionParameterReflection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12FunctionParameterReflection, 0xec25f42d, 0x7006, 0x4f2b, 0xb3,0x3e, 0x02,0xcc,0x33,0x75,0x73,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ec25f42d-7006-4f2b-b33e-02cc3375733f")
ID3D12FunctionParameterReflection
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_PARAMETER_DESC *desc) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12FunctionParameterReflection, 0xec25f42d, 0x7006, 0x4f2b, 0xb3,0x3e, 0x02,0xcc,0x33,0x75,0x73,0x3f)
#endif
#else
typedef struct ID3D12FunctionParameterReflectionVtbl {
    BEGIN_INTERFACE

    /*** ID3D12FunctionParameterReflection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12FunctionParameterReflection *This,
        D3D12_PARAMETER_DESC *desc);

    END_INTERFACE
} ID3D12FunctionParameterReflectionVtbl;

interface ID3D12FunctionParameterReflection {
    CONST_VTBL ID3D12FunctionParameterReflectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D12FunctionParameterReflection methods ***/
#define ID3D12FunctionParameterReflection_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#else
/*** ID3D12FunctionParameterReflection methods ***/
static __WIDL_INLINE HRESULT ID3D12FunctionParameterReflection_GetDesc(ID3D12FunctionParameterReflection* This,D3D12_PARAMETER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D12FunctionParameterReflection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12FunctionReflection interface
 */
#ifndef __ID3D12FunctionReflection_INTERFACE_DEFINED__
#define __ID3D12FunctionReflection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12FunctionReflection, 0x1108795c, 0x2772, 0x4ba9, 0xb2,0xa8, 0xd4,0x64,0xdc,0x7e,0x27,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1108795c-2772-4ba9-b2a8-d464dc7e2799")
ID3D12FunctionReflection
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_FUNCTION_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionConstantBuffer * STDMETHODCALLTYPE GetConstantBufferByIndex(
        UINT index) = 0;

    virtual ID3D12ShaderReflectionConstantBuffer * STDMETHODCALLTYPE GetConstantBufferByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceBindingDesc(
        UINT index,
        D3D12_SHADER_INPUT_BIND_DESC *desc) = 0;

    virtual ID3D12ShaderReflectionVariable * STDMETHODCALLTYPE GetVariableByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceBindingDescByName(
        const char *name,
        D3D12_SHADER_INPUT_BIND_DESC *desc) = 0;

    virtual ID3D12FunctionParameterReflection * STDMETHODCALLTYPE GetFunctionParameter(
        INT index) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12FunctionReflection, 0x1108795c, 0x2772, 0x4ba9, 0xb2,0xa8, 0xd4,0x64,0xdc,0x7e,0x27,0x99)
#endif
#else
typedef struct ID3D12FunctionReflectionVtbl {
    BEGIN_INTERFACE

    /*** ID3D12FunctionReflection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12FunctionReflection *This,
        D3D12_FUNCTION_DESC *desc);

    ID3D12ShaderReflectionConstantBuffer * (STDMETHODCALLTYPE *GetConstantBufferByIndex)(
        ID3D12FunctionReflection *This,
        UINT index);

    ID3D12ShaderReflectionConstantBuffer * (STDMETHODCALLTYPE *GetConstantBufferByName)(
        ID3D12FunctionReflection *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *GetResourceBindingDesc)(
        ID3D12FunctionReflection *This,
        UINT index,
        D3D12_SHADER_INPUT_BIND_DESC *desc);

    ID3D12ShaderReflectionVariable * (STDMETHODCALLTYPE *GetVariableByName)(
        ID3D12FunctionReflection *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *GetResourceBindingDescByName)(
        ID3D12FunctionReflection *This,
        const char *name,
        D3D12_SHADER_INPUT_BIND_DESC *desc);

    ID3D12FunctionParameterReflection * (STDMETHODCALLTYPE *GetFunctionParameter)(
        ID3D12FunctionReflection *This,
        INT index);

    END_INTERFACE
} ID3D12FunctionReflectionVtbl;

interface ID3D12FunctionReflection {
    CONST_VTBL ID3D12FunctionReflectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D12FunctionReflection methods ***/
#define ID3D12FunctionReflection_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D12FunctionReflection_GetConstantBufferByIndex(This,index) (This)->lpVtbl->GetConstantBufferByIndex(This,index)
#define ID3D12FunctionReflection_GetConstantBufferByName(This,name) (This)->lpVtbl->GetConstantBufferByName(This,name)
#define ID3D12FunctionReflection_GetResourceBindingDesc(This,index,desc) (This)->lpVtbl->GetResourceBindingDesc(This,index,desc)
#define ID3D12FunctionReflection_GetVariableByName(This,name) (This)->lpVtbl->GetVariableByName(This,name)
#define ID3D12FunctionReflection_GetResourceBindingDescByName(This,name,desc) (This)->lpVtbl->GetResourceBindingDescByName(This,name,desc)
#define ID3D12FunctionReflection_GetFunctionParameter(This,index) (This)->lpVtbl->GetFunctionParameter(This,index)
#else
/*** ID3D12FunctionReflection methods ***/
static __WIDL_INLINE HRESULT ID3D12FunctionReflection_GetDesc(ID3D12FunctionReflection* This,D3D12_FUNCTION_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionConstantBuffer * ID3D12FunctionReflection_GetConstantBufferByIndex(ID3D12FunctionReflection* This,UINT index) {
    return This->lpVtbl->GetConstantBufferByIndex(This,index);
}
static __WIDL_INLINE ID3D12ShaderReflectionConstantBuffer * ID3D12FunctionReflection_GetConstantBufferByName(ID3D12FunctionReflection* This,const char *name) {
    return This->lpVtbl->GetConstantBufferByName(This,name);
}
static __WIDL_INLINE HRESULT ID3D12FunctionReflection_GetResourceBindingDesc(ID3D12FunctionReflection* This,UINT index,D3D12_SHADER_INPUT_BIND_DESC *desc) {
    return This->lpVtbl->GetResourceBindingDesc(This,index,desc);
}
static __WIDL_INLINE ID3D12ShaderReflectionVariable * ID3D12FunctionReflection_GetVariableByName(ID3D12FunctionReflection* This,const char *name) {
    return This->lpVtbl->GetVariableByName(This,name);
}
static __WIDL_INLINE HRESULT ID3D12FunctionReflection_GetResourceBindingDescByName(ID3D12FunctionReflection* This,const char *name,D3D12_SHADER_INPUT_BIND_DESC *desc) {
    return This->lpVtbl->GetResourceBindingDescByName(This,name,desc);
}
static __WIDL_INLINE ID3D12FunctionParameterReflection * ID3D12FunctionReflection_GetFunctionParameter(ID3D12FunctionReflection* This,INT index) {
    return This->lpVtbl->GetFunctionParameter(This,index);
}
#endif
#endif

#endif


#endif  /* __ID3D12FunctionReflection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12LibraryReflection interface
 */
#ifndef __ID3D12LibraryReflection_INTERFACE_DEFINED__
#define __ID3D12LibraryReflection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12LibraryReflection, 0x8e349d19, 0x54db, 0x4a56, 0x9d,0xc9, 0x11,0x9d,0x87,0xbd,0xb8,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8e349d19-54db-4a56-9dc9-119d87bdb804")
ID3D12LibraryReflection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D12_LIBRARY_DESC *desc) = 0;

    virtual ID3D12FunctionReflection * STDMETHODCALLTYPE GetFunctionByIndex(
        INT index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12LibraryReflection, 0x8e349d19, 0x54db, 0x4a56, 0x9d,0xc9, 0x11,0x9d,0x87,0xbd,0xb8,0x04)
#endif
#else
typedef struct ID3D12LibraryReflectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12LibraryReflection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12LibraryReflection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12LibraryReflection *This);

    /*** ID3D12LibraryReflection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D12LibraryReflection *This,
        D3D12_LIBRARY_DESC *desc);

    ID3D12FunctionReflection * (STDMETHODCALLTYPE *GetFunctionByIndex)(
        ID3D12LibraryReflection *This,
        INT index);

    END_INTERFACE
} ID3D12LibraryReflectionVtbl;

interface ID3D12LibraryReflection {
    CONST_VTBL ID3D12LibraryReflectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12LibraryReflection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12LibraryReflection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12LibraryReflection_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12LibraryReflection methods ***/
#define ID3D12LibraryReflection_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D12LibraryReflection_GetFunctionByIndex(This,index) (This)->lpVtbl->GetFunctionByIndex(This,index)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT ID3D12LibraryReflection_QueryInterface(ID3D12LibraryReflection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG ID3D12LibraryReflection_AddRef(ID3D12LibraryReflection* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG ID3D12LibraryReflection_Release(ID3D12LibraryReflection* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12LibraryReflection methods ***/
static __WIDL_INLINE HRESULT ID3D12LibraryReflection_GetDesc(ID3D12LibraryReflection* This,D3D12_LIBRARY_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static __WIDL_INLINE ID3D12FunctionReflection * ID3D12LibraryReflection_GetFunctionByIndex(ID3D12LibraryReflection* This,INT index) {
    return This->lpVtbl->GetFunctionByIndex(This,index);
}
#endif
#endif

#endif


#endif  /* __ID3D12LibraryReflection_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d12shader_h__ */
