;
; Definition file of wsdapi.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "wsdapi.dll"
EXPORTS
WSDCancelAddrChangeNotify@4
WSDCreateHttpAddressAdvanced@8
WSDNotifyAddrChange@12
WSDAllocateLinkedMemory@8
WSDAttachLinkedMemory@8
WSDCreateDeviceHost@12
WSDCreateDeviceHostAdvanced@20
WSDCreateDeviceProxy@16
WSDCreateDeviceProxyAdvanced@20
WSDCreateDiscoveryProvider@8
WSDCreateDiscoveryPublisher@8
WSDCreateHttpAddress@4
WSDCreateHttpMessageParameters@4
WSDCreateHttpTransport@8
WSDCreateMetadataAgent@12
WSDCreateOutboundAttachment@4
WSDCreateUdpAddress@4
WSDCreateUdpMessageParameters@4
WSDCreateUdpTransport@4
WSDDetachLinkedMemory@4
WSD<PERSON><PERSON>Link<PERSON><PERSON><PERSON>@4
WSDGenerate<PERSON>ault@24
WSDGenerateFaultEx@20
WSDGenerateRandomDelay@8
WSDGetConfigurationOption@12
WSDProcessFault@12
WSDSetConfigurationOption@12
WSDXMLAddChild@8
WSDXMLAddSibling@8
WSDXMLBuildAnyForSingleElement@12
WSDXMLCleanupElement@4
WSDXMLCreateContext@4
WSDXMLGetNameFromBuiltinNamespace@12
WSDXMLGetValueFromAny@16
