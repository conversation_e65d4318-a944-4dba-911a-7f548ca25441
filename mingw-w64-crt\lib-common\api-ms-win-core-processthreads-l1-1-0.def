LIBRARY api-ms-win-core-processthreads-l1-1-0

EXPORTS

CreateProcessA
CreateProcessAsUserW
CreateProcessW
CreateThread
ExitProcess
ExitThread
FlushProcessWriteBuffers
GetCurrentProcess
GetCurrentProcessId
GetCurrentThread
GetCurrentThreadId
GetExitCodeProcess
GetExitCodeThread
GetPriorityClass
GetProcessId
GetProcessTimes
GetStartupInfoW
GetThreadId
GetThreadPriority
GetThreadPriorityBoost
OpenProcessToken
OpenThread
OpenThreadToken
ProcessIdToSessionId
QueueUserAPC
ResumeThread
SetPriorityClass
SetThreadPriority
SetThreadPriorityBoost
SetThreadStackGuarantee
SetThreadToken
SuspendThread
SwitchToThread
TerminateProcess
TerminateThread
TlsAlloc
TlsFree
TlsGetValue
TlsSetValue
