; 
; Exports of file MSCAT32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY MSCAT32.dll
EXPORTS
CryptCATVerifyMember
CatalogCompactHashDatabase
CryptCATAdminAcquireContext
CryptCATAdminAddCatalog
CryptCATAdminCalcHashFromFileHandle
CryptCATAdminEnumCatalogFromHash
CryptCATAdminReleaseCatalogContext
CryptCATAdminReleaseContext
CryptCATCDFClose
CryptCATCDFEnumAttributes
CryptCATCDFEnumAttributesWithCDFTag
CryptCATCDFEnumCatAttributes
CryptCATCDFEnumMembers
CryptCATCDFEnumMembersByCDFTag
CryptCATCDFEnumMembersByCDFTagEx
CryptCATCDFOpen
CryptCATCatalogInfoFromContext
CryptCATClose
CryptCATEnumerateAttr
CryptCATEnumerateCatAttr
CryptCATEnumerateMember
CryptCATGetAttrInfo
CryptCATGetCatAttrInfo
CryptCATGetMemberInfo
CryptCATHandleFromStore
CryptCATOpen
CryptCATPersistStore
CryptCATPutAttrInfo
CryptCATPutCatAttrInfo
CryptCATPutMemberInfo
CryptCATStoreFromHandle
DllRegisterServer
DllUnregisterServer
IsCatalogFile
MsCatConstructHashTag
MsCatFreeHashTag
