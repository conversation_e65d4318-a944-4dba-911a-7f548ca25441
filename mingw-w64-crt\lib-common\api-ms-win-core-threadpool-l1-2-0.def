LIBRARY api-ms-win-core-threadpool-l1-2-0

EXPORTS

CallbackMayRunLong
CancelThreadpoolIo
CloseThreadpool
CloseThreadpoolCleanupGroup
CloseThreadpoolCleanupGroupMembers
CloseThreadpoolIo
CloseThreadpoolTimer
CloseThreadpoolWait
CloseThreadpoolWork
CreateThreadpool
CreateThreadpoolCleanupGroup
CreateThreadpoolIo
CreateThreadpoolTimer
CreateThreadpoolWait
CreateThreadpoolWork
DisassociateCurrentThreadFromCallback
FreeLibraryWhenCallbackReturns
IsThreadpoolTimerSet
LeaveCriticalSectionWhenCallbackReturns
QueryThreadpoolStackInformation
ReleaseMutexWhenCallbackReturns
ReleaseSemaphoreWhenCallbackReturns
SetEventWhenCallbackReturns
SetThreadpoolStackInformation
SetThreadpoolThreadMaximum
SetThreadpoolThreadMinimum
SetThreadpoolTimer
SetThreadpoolTimerEx
SetThreadpoolWait
SetThreadpoolWaitEx
StartThreadpoolIo
SubmitThreadpoolWork
TrySubmitThreadpoolCallback
WaitForThreadpoolIoCallbacks
WaitForThreadpoolTimerCallbacks
WaitForThreadpoolWaitCallbacks
WaitForThreadpoolWorkCallbacks
