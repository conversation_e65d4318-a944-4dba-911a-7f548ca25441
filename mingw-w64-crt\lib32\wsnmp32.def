LIBRARY wsnmp32.dll
EXPORTS
SnmpCancelMsg@8
SnmpCleanup@0
SnmpClose@4
SnmpContextToStr@8
SnmpCountVbl@4
SnmpCreatePdu@24
SnmpCreateSession@16
SnmpCreateVbl@12
SnmpDecodeMsg@24
SnmpDeleteVb@8
SnmpDuplicatePdu@8
SnmpDuplicateVbl@8
SnmpEncodeMsg@24
SnmpEntityToStr@12
SnmpFreeContext@4
SnmpFreeDescriptor@8
SnmpFreeEntity@4
SnmpFreePdu@4
SnmpFreeVbl@4
SnmpGetLastError@4
SnmpGetPduData@24
SnmpGetRetransmitMode@4
SnmpGetRetry@12
SnmpGetTimeout@12
SnmpGetTranslateMode@4
SnmpGetVb@16
SnmpGetVendorInfo@4
SnmpListen@8
SnmpOidCompare@16
SnmpOidCopy@8
SnmpOidToStr@12
SnmpOpen@8
SnmpRecvMsg@20
SnmpRegister@24
SnmpSendMsg@20
SnmpSetPduData@24
SnmpSetPort@8
SnmpSetRetransmitMode@4
SnmpSetRetry@8
SnmpSetTimeout@8
SnmpSetTranslateMode@4
SnmpSetVb@16
SnmpStartup@20
SnmpStrToContext@8
SnmpStrToEntity@8
SnmpStrToOid@8
