#include "func.def.in"

LIBRARY "ntdll.dll"
EXPORTS
#ifdef DEF_I386
_CIcos
_CIlog
_CIpow
_CIsin
_CIsqrt
#endif
F_NON_I386(__C_specific_handler)
F_NON_I386(;__chkstk)
__isascii
__iscsym
__iscsymf
F_X64(__misaligned_access)
F_ARM32(__jump_unwind)
__toascii
#ifdef DEF_I386
_alldiv
_alldvrm@16
_allmul@16
_alloca_probe
_alloca_probe_16
_alloca_probe_8
_allrem@16
_allshl
_allshr
#endif
_atoi64
#ifdef DEF_I386
_aulldiv@16
_aulldvrm@16
_aullrem@16
_aullshr
;_chkstk
#endif
_errno
F_I386(_except_handler4_common)
_fltused DATA
#ifdef DEF_I386
_ftol
_ftol2
_ftol2_sse
#endif
_i64toa
_i64toa_s
_i64tow
_i64tow_s
_itoa
_itoa_s
_itow
_itow_s
_lfind
F64(_local_unwind)
F_I386(_local_unwind4)
_ltoa
_ltoa_s
_ltow
_ltow_s
_makepath_s
_memccpy
_memicmp
F_X64(_setjmp)
F_ARM32(_setjmp)
F_NON_I386(_setjmpex)
_snprintf
_snprintf_s
_snscanf_s
_snwprintf
_snwprintf_s
_snwscanf_s
_splitpath
_splitpath_s
_strcmpi
_stricmp
_strlwr
strlwr == _strlwr
_strlwr_s
_strnicmp
_strnset_s
_strset_s
_strupr
_strupr_s
_swprintf
F_X86_ANY(_tolower)
F_X86_ANY(_toupper)
_ui64toa
_ui64toa_s
_ui64tow
_ui64tow_s
_ultoa
_ultoa_s
_ultow
_ultow_s
_vscprintf
_vscwprintf
_vsnprintf
_vsnprintf_s
_vsnwprintf
_vsnwprintf_s
_vswprintf
_wcsicmp
_wcslwr
wcslwr == _wcslwr
_wcslwr_s
_wcsnicmp
_wcsnset_s
_wcsset_s
_wcstoi64
_wcstoui64
_wcsupr
_wcsupr_s
_wmakepath_s
_wsplitpath_s
_wtoi
_wtoi64
_wtol
abs
atan F_X86_ANY(DATA)
atan2
atoi
atol
bsearch
bsearch_s
ceil
cos F_X86_ANY(DATA)
fabs F_X86_ANY(DATA)
floor F_X86_ANY(DATA)
isalnum
isalpha
iscntrl
isdigit
isgraph
islower
isprint
ispunct
isspace
isupper
iswalnum
iswalpha
iswascii
iswctype
iswdigit
iswgraph
iswlower
iswprint
iswspace
iswxdigit
isxdigit
labs
log
F_NON_I386(longjmp)
mbstowcs
memchr
memcmp
memcpy
memcpy_s
memmove
memmove_s
memset
pow
qsort
qsort_s
sin
sprintf
sprintf_s
sqrt
sscanf
sscanf_s
strcat
strcat_s
strchr
strcmp
strcpy
strcpy_s
strcspn
strlen
strncat
strncat_s
strncmp
strncpy
strncpy_s
strnlen
strpbrk
strrchr
strspn
strstr
strtok_s
strtol
strtoul
swprintf
swprintf_s
swscanf_s
tan
tolower
toupper
towlower
towupper
vsprintf
vsprintf_s
vswprintf_s
wcscat
wcscat_s
wcschr
wcscmp
wcscpy
wcscpy_s
wcscspn
wcslen
wcsncat
wcsncat_s
wcsncmp
wcsncpy
wcsncpy_s
wcsnlen
wcspbrk
wcsrchr
wcsspn
wcsstr
wcstok_s
wcstol
wcstombs
wcstoul
