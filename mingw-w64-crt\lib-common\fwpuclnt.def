;
; Definition file of fwpuclnt.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "fwpuclnt.dll"
EXPORTS
FwpiExpandCriteria0
FwpiFreeCriteria0
FwpiVpnTriggerAddAppSids
FwpiVpnTriggerAddFilePaths
FwpiVpnTriggerAddSecurityDescriptor
FwpiVpnTriggerConfigureParameters
FwpiVpnTriggerEventSubscribe0
FwpiVpnTriggerEventUnsubscribe0
FwpiVpnTriggerInitializeNrptTriggering
FwpiVpnTriggerRemoveAppSids
FwpiVpnTriggerRemoveFilePaths
FwpiVpnTriggerRemoveSecurityDescriptor
FwpiVpnTriggerResetNrptTriggering
FwpiVpnTriggerSetStateDisconnected
FwpiVpnTriggerUninitializeNrptTriggering
FwpmCalloutAdd0
FwpmCalloutCreateEnumHandle0
FwpmCallo<PERSON>eleteById0
FwpmCalloutDeleteByKey0
FwpmCalloutDestroyEnumHandle0
FwpmCalloutEnum0
FwpmCalloutGetById0
FwpmCalloutGetByKey0
FwpmCalloutGetSecurityInfoByKey0
FwpmCalloutSetSecurityInfoByKey0
FwpmCalloutSubscribeChanges0
FwpmCalloutSubscriptionsGet0
FwpmCalloutUnsubscribeChanges0
FwpmConnectionCreateEnumHandle0
FwpmConnectionDestroyEnumHandle0
FwpmConnectionEnum0
FwpmConnectionGetById0
FwpmConnectionGetSecurityInfo0
FwpmConnectionSetSecurityInfo0
FwpmConnectionSubscribe0
FwpmConnectionUnsubscribe0
FwpmDiagnoseNetFailure0
FwpmDynamicKeywordSubscribe0
FwpmDynamicKeywordUnsubscribe0
FwpmEngineClose0
FwpmEngineGetOption0
FwpmEngineGetSecurityInfo0
FwpmEngineOpen0
FwpmEngineSetOption0
FwpmEngineSetSecurityInfo0
FwpmEventProviderCreate0
FwpmEventProviderDestroy0
FwpmEventProviderFireNetEvent0
FwpmEventProviderFireNetEventEx0
FwpmEventProviderIsNetEventTypeEnabled0
FwpmFilterAdd0
FwpmFilterCreateEnumHandle0
FwpmFilterDeleteById0
FwpmFilterDeleteByKey0
FwpmFilterDestroyEnumHandle0
FwpmFilterEnum0
FwpmFilterGetById0
FwpmFilterGetByKey0
FwpmFilterGetSecurityInfoByKey0
FwpmFilterSetSecurityInfoByKey0
FwpmFilterSubscribeChanges0
FwpmFilterSubscriptionsGet0
FwpmFilterUnsubscribeChanges0
FwpmFreeMemory0
FwpmGetAppIdFromFileName0
FwpmGetSidFromOnlineId0
FwpmIPsecS2STunnelAddConditions0
FwpmIPsecS2STunnelAddInterfaceToCompartment0
FwpmIPsecS2STunnelGetInterfaceForCompartment0
FwpmIPsecS2STunnelRemoveConditions0
FwpmIPsecS2STunnelRemoveInterfaceFromCompartment0
FwpmIPsecTunnelAdd0
FwpmIPsecTunnelAdd1
FwpmIPsecTunnelAdd2
FwpmIPsecTunnelAdd3
FwpmIPsecTunnelAddConditions0
FwpmIPsecTunnelDeleteByKey0
FwpmLayerCreateEnumHandle0
FwpmLayerDestroyEnumHandle0
FwpmLayerEnum0
FwpmLayerGetById0
FwpmLayerGetByKey0
FwpmLayerGetSecurityInfoByKey0
FwpmLayerSetSecurityInfoByKey0
FwpmNetEventCreateEnumHandle0
FwpmNetEventCreateEnumHandleEx
FwpmNetEventDestroyEnumHandle0
FwpmNetEventEnum0
FwpmNetEventEnum1
FwpmNetEventEnum2
FwpmNetEventEnum3
FwpmNetEventEnum4
FwpmNetEventEnum5
FwpmNetEventSubscribe0
FwpmNetEventSubscribe1
FwpmNetEventSubscribe2
FwpmNetEventSubscribe3
FwpmNetEventSubscribe4
FwpmNetEventSubscriptionsGet0
FwpmNetEventUnsubscribe0
FwpmNetEventsGetSecurityInfo0
FwpmNetEventsLost0
FwpmNetEventsSetSecurityInfo0
FwpmProcessNameResolutionEvent0
FwpmProviderAdd0
FwpmProviderContextAdd0
FwpmProviderContextAdd1
FwpmProviderContextAdd2
FwpmProviderContextAdd3
FwpmProviderContextCreateEnumHandle0
FwpmProviderContextDeleteById0
FwpmProviderContextDeleteByKey0
FwpmProviderContextDestroyEnumHandle0
FwpmProviderContextEnum0
FwpmProviderContextEnum1
FwpmProviderContextEnum2
FwpmProviderContextEnum3
FwpmProviderContextGetById0
FwpmProviderContextGetById1
FwpmProviderContextGetById2
FwpmProviderContextGetById3
FwpmProviderContextGetByKey0
FwpmProviderContextGetByKey1
FwpmProviderContextGetByKey2
FwpmProviderContextGetByKey3
FwpmProviderContextGetSecurityInfoByKey0
FwpmProviderContextSetSecurityInfoByKey0
FwpmProviderContextSubscribeChanges0
FwpmProviderContextSubscriptionsGet0
FwpmProviderContextUnsubscribeChanges0
FwpmProviderCreateEnumHandle0
FwpmProviderDeleteByKey0
FwpmProviderDestroyEnumHandle0
FwpmProviderEnum0
FwpmProviderGetByKey0
FwpmProviderGetSecurityInfoByKey0
FwpmProviderSetSecurityInfoByKey0
FwpmProviderSubscribeChanges0
FwpmProviderSubscriptionsGet0
FwpmProviderUnsubscribeChanges0
FwpmSessionCreateEnumHandle0
FwpmSessionDestroyEnumHandle0
FwpmSessionEnum0
FwpmSubLayerAdd0
FwpmSubLayerCreateEnumHandle0
FwpmSubLayerDeleteByKey0
FwpmSubLayerDestroyEnumHandle0
FwpmSubLayerEnum0
FwpmSubLayerGetByKey0
FwpmSubLayerGetSecurityInfoByKey0
FwpmSubLayerSetSecurityInfoByKey0
FwpmSubLayerSubscribeChanges0
FwpmSubLayerSubscriptionsGet0
FwpmSubLayerUnsubscribeChanges0
FwpmSystemPortsGet0
FwpmSystemPortsSubscribe0
FwpmSystemPortsUnsubscribe0
FwpmTraceRestoreDefaults0
FwpmTransactionAbort0
FwpmTransactionBegin0
FwpmTransactionCommit0
FwpmvSwitchEventSubscribe0
FwpmvSwitchEventUnsubscribe0
FwpmvSwitchEventsGetSecurityInfo0
FwpmvSwitchEventsSetSecurityInfo0
FwppConnectionGetByIPsecInfo
FwppConnectionGetByS2STunnelId
FwppConnectionGetS2STunnelId
FwppGetMD5HashBytes
FwppIPsecSaContextCreate
FwpsAleEndpointCreateEnumHandle0
FwpsAleEndpointDestroyEnumHandle0
FwpsAleEndpointEnum0
FwpsAleEndpointGetById0
FwpsAleEndpointGetSecurityInfo0
FwpsAleEndpointSetSecurityInfo0
FwpsAleExplicitCredentialsQuery0
FwpsAleGetPortStatus0
FwpsClassifyUser0
FwpsFreeMemory0
FwpsGetInProcReplicaOffset0
FwpsLayerCreateInProcReplica0
FwpsLayerReleaseInProcReplica0
FwpsOpenToken0
FwpsQueryIPsecDosFWUsed0
FwpsQueryIPsecOffloadDone0
GetUnifiedTraceHandle
IPsecDospGetSecurityInfo0
IPsecDospGetStatistics0
IPsecDospSetSecurityInfo0
IPsecDospStateCreateEnumHandle0
IPsecDospStateDestroyEnumHandle0
IPsecDospStateEnum0
IPsecGetKeyFromDictator0
IPsecGetStatistics0
IPsecGetStatistics1
IPsecKeyDictationCheck0
IPsecKeyManagerAddAndRegister0
IPsecKeyManagerGetSecurityInfoByKey0
IPsecKeyManagerSetSecurityInfoByKey0
IPsecKeyManagerUnregisterAndDelete0
IPsecKeyManagersGet0
IPsecKeyModuleAdd0
IPsecKeyModuleDelete0
IPsecKeyModuleUpdateAcquire0
IPsecKeyNotification0
IPsecSaContextAddInbound0
IPsecSaContextAddInbound1
IPsecSaContextAddInboundAndTrackConnection
IPsecSaContextAddOutbound0
IPsecSaContextAddOutbound1
IPsecSaContextAddOutboundAndTrackConnection
IPsecSaContextCreate0
IPsecSaContextCreate1
IPsecSaContextCreateEnumHandle0
IPsecSaContextDeleteById0
IPsecSaContextDestroyEnumHandle0
IPsecSaContextEnum0
IPsecSaContextEnum1
IPsecSaContextExpire0
IPsecSaContextGetById0
IPsecSaContextGetById1
IPsecSaContextGetSpi0
IPsecSaContextGetSpi1
IPsecSaContextSetSpi0
IPsecSaContextSubscribe0
IPsecSaContextSubscriptionsGet0
IPsecSaContextUnsubscribe0
IPsecSaContextUpdate0
IPsecSaCreateEnumHandle0
IPsecSaDbGetSecurityInfo0
IPsecSaDbSetSecurityInfo0
IPsecSaDestroyEnumHandle0
IPsecSaEnum0
IPsecSaEnum1
IPsecSaInitiateAsync0
IkeextGetConfigParameters0
IkeextGetStatistics0
IkeextGetStatistics1
IkeextSaCreateEnumHandle0
IkeextSaDbGetSecurityInfo0
IkeextSaDbSetSecurityInfo0
IkeextSaDeleteById0
IkeextSaDestroyEnumHandle0
IkeextSaEnum0
IkeextSaEnum1
IkeextSaEnum2
IkeextSaGetById0
IkeextSaGetById1
IkeextSaGetById2
IkeextSaUpdateAdditionalAddressesByTunnelId0
IkeextSaUpdatePreferredAddressesByTunnelId0
IkeextSetConfigParameters0
NamespaceCallout
WFPRIODequeueCompletion
WSADeleteSocketPeerTargetName
WSAImpersonateSocketPeer
WSAQuerySocketSecurity
WSARevertImpersonation
WSASetSocketPeerTargetName
WSASetSocketSecurity
WfpCloseDPConfigureHandle
WfpConfigureDPSecurityDescriptor
WfpCreateDPConfigureHandle
WfpRIOChannelClose
WfpRIOCleanupRequestQueue
WfpRIOCloseCompletionQueue
WfpRIOCreateChannel
WfpRIOCreateCompletionQueue
WfpRIOCreateRequestQueue
WfpRIODeregisterBuffer
WfpRIOIndicateActivityThreshold
WfpRIONotify
WfpRIOReceive
WfpRIORegisterBuffer
WfpRIOResume
WfpRIOSend
WfpRIOSuspend
