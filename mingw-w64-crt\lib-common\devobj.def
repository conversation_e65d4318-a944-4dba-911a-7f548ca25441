;
; Definition file of DEVOBJ.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "DEVOBJ.dll"
EXPORTS
DevObjBuildClassInfoList
<PERSON><PERSON>bjChangeState
DevObjClassGuidsFromName
DevObjClassNameFromGuid
DevObjCreateDevRegKey
DevObjCreateDeviceInfo
DevObjCreateDeviceInfoList
DevObjCreateDeviceInterface
DevObjCreateDeviceInterfaceRegKey
DevObjDeleteAllInterfacesForDevice
DevObjDeleteDevRegKey
DevObjDeleteDevice
DevObjDeleteDeviceInfo
DevObjDeleteDeviceInterfaceData
DevObjDeleteDeviceInterfaceRegKey
DevObjDestroyDeviceInfoList
DevObjEnumDeviceInfo
DevObjEnumDeviceInterfaces
DevObjGetClassDescription
DevObjGetClassDevs
DevObjGetClassProperty
Dev<PERSON>etClassPropertyKeys
DevObjGetClassRegistryProperty
DevObjGetDeviceInfoDetail
DevObjGetDeviceInfoListClass
DevObjGetDeviceInfoListDetail
DevObjGetDeviceInstanceId
DevObjGetDeviceInterfaceAlias
DevObjGetDeviceInterfaceDetail
DevObjGetDeviceInterfaceProperty
DevObjGetDeviceInterfacePropertyKeys
DevObjGetDeviceProperty
DevObjGetDevicePropertyKeys
DevObjGetDeviceRegistryProperty
DevObjLocateDevice
DevObjOpenClassRegKey
DevObjOpenDevRegKey
DevObjOpenDeviceInfo
DevObjOpenDeviceInterface
DevObjOpenDeviceInterfaceRegKey
DevObjRegisterDeviceInfo
DevObjRemoveDeviceInterface
DevObjRestartDevices
DevObjSetClassProperty
DevObjSetClassRegistryProperty
DevObjSetDeviceInfoDetail
DevObjSetDeviceInterfaceDefault
DevObjSetDeviceInterfaceProperty
DevObjSetDeviceProperty
DevObjSetDeviceRegistryProperty
DevObjUninstallDevice
