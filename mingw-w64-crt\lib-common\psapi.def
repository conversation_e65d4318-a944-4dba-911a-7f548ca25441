;
; Definition file of PSAPI.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "PSAPI.DLL"
EXPORTS
EmptyWorkingSet
EnumDeviceDrivers
EnumPageFilesA
EnumPageFilesW
EnumProcessModules
EnumProcessModulesEx
EnumProcesses
GetDeviceDriverBaseNameA
GetDeviceDriverBaseNameW
GetDeviceDriverFileNameA
GetDeviceDriverFileNameW
GetMappedFileNameA
GetMappedFileNameW
GetModuleBaseNameA
GetModuleBaseNameW
GetModuleFileNameExA
GetModuleFileNameExW
GetModuleInformation
GetPerformanceInfo
GetProcessImageFileNameA
GetProcessImageFileNameW
GetProcessMemoryInfo
GetWsChanges
GetWsChangesEx
InitializeProcessForWsWatch
QueryWorkingSet
QueryWorkingSetEx
