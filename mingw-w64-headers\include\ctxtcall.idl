/*
 * Copyright (C) 2008 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "wtypes.idl";
import "objidl.idl";
import "unknwn.idl";

typedef struct tagComCallData
{
    DWORD dwDispid;
    DWORD dwReserved;
    void  *pUserDefined;
} ComCallData;

/***************************************************************************
 * IContextCallback
 */
[
  local,
  object,
  uuid(000001da-0000-0000-c000-000000000046),
  pointer_default(unique)
]
interface IContextCallback : IUnknown
{
    typedef HRESULT (__stdcall *PFNCONTEXTCALL)(ComCallData *pParam);

    HRESULT ContextCallback(
            [in] PFNCONTEXTCALL pCallback,
            [in] ComCallData *pParam,
            [in] REFIID riid,
            [in] int iMethod,
            [in] IUnknown *pUnk);
}
