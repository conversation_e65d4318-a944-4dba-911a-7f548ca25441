LIBRARY "webauthn.dll"
EXPORTS
CryptsvcDllCtrl
I_WebAuthNCtapDecodeGetAssertionRpcResponse
I_WebAuthNCtapDecodeMakeCredentialRpcResponse
I_WebAuthNCtapEncodeGetAssertionRpcRequest
I_WebAuthNCtapEncodeMakeCredentialRpcRequest
WebAuthNAuthenticatorGetAssertion
WebAuthNAuthenticatorMakeCredential
WebAuthNCancelCurrentOperation
WebAuthNCtapChangeClientPin
WebAuthNCtapChangeClientPinForSelectedDevice
WebAuthNCtapFreeSelectedDeviceInformation
WebAuthNCtapGetAssertion
WebAuthNCtapGetSupportedTransports
WebAuthNCtapGetWnfLocalizedString
WebAuthNCtapIsStopSendCommandError
WebAuthNCtapMakeCredential
WebAuthNCtapManageAuthenticatePin
WebAuthNCtapManageCancelEnrollFingerprint
WebAuthNCtapManageChangePin
WebAuthNCtapManageClose
WebAuthNCtapManageDeleteCredential
WebAuthNCtapManageEnrollFingerprint
WebAuthNCtapManageFreeDisplayCredentials
WebAuthNCtapManageGetDisplayCredentials
WebAuthNCtapManageRemoveFingerprints
WebAuthNCtapManageResetDevice
WebAuthNCtapManageSelect
WebAuthNCtapManageSetPin
WebAuthNCtapParseAuthenticatorData
WebAuthNCtapResetDevice
WebAuthNCtapRpcGetAssertionUserList
WebAuthNCtapRpcGetCborCommand
WebAuthNCtapRpcSelectGetAssertion
WebAuthNCtapSendCommand
WebAuthNCtapSetClientPin
WebAuthNCtapStartDeviceChangeNotify
WebAuthNCtapStopDeviceChangeNotify
WebAuthNCtapVerifyGetAssertion
WebAuthNDecodeAccountInformation
WebAuthNDeletePlatformCredential
WebAuthNEncodeAccountInformation
WebAuthNFreeAssertion
WebAuthNFreeCredentialAttestation
WebAuthNFreeDecodedAccountInformation
WebAuthNFreeEncodedAccountInformation
WebAuthNFreePlatformCredentials
WebAuthNFreeUserEntityList
WebAuthNGetApiVersionNumber
WebAuthNGetCancellationId
WebAuthNGetCoseAlgorithmIdentifier
WebAuthNGetCredentialIdFromAuthenticatorData
WebAuthNGetErrorName
WebAuthNGetPlatformCredentials
WebAuthNGetW3CExceptionDOMError
WebAuthNIsUserVerifyingPlatformAuthenticatorAvailable
