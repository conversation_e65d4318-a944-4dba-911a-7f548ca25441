/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "dxgi1_3.idl";
import "d3dcommon.idl";
import "d3d11_2.idl";

typedef enum D3D11_CONTEXT_TYPE
{
    D3D11_CONTEXT_TYPE_ALL = 0x0,
    D3D11_CONTEXT_TYPE_3D = 0x1,
    D3D11_CONTEXT_TYPE_COMPUTE = 0x2,
    D3D11_CONTEXT_TYPE_COPY = 0x3,
    D3D11_CONTEXT_TYPE_VIDEO = 0x4,
} D3D11_CONTEXT_TYPE;

typedef enum D3D11_TEXTURE_LAYOUT
{
    D3D11_TEXTURE_LAYOUT_UNDEFINED = 0x0,
    D3D11_TEXTURE_LAYOUT_ROW_MAJOR = 0x1,
    D3D11_TEXTURE_LAYOUT_64K_STANDARD_SWIZZLE = 0x2,
} D3D11_TEXTURE_LAYOUT;

typedef enum D3D11_CONSERVATIVE_RASTERIZATION_MODE
{
    D3D11_CONSERVATIVE_RASTERIZATION_MODE_OFF = 0x0,
    D3D11_CONSERVATIVE_RASTERIZATION_MODE_ON = 0x1,
} D3D11_CONSERVATIVE_RASTERIZATION_MODE;

typedef enum D3D11_FENCE_FLAG
{
    D3D11_FENCE_FLAG_NONE = 0x1,
    D3D11_FENCE_FLAG_SHARED = 0x2,
    D3D11_FENCE_FLAG_SHARED_CROSS_ADAPTER = 0x4,
    D3D11_FENCE_FLAG_NON_MONITORED = 0x8,
} D3D11_FENCE_FLAG;

typedef struct D3D11_TEXTURE2D_DESC1
{
    UINT Width;
    UINT Height;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    D3D11_TEXTURE_LAYOUT TextureLayout;
} D3D11_TEXTURE2D_DESC1;

typedef struct D3D11_TEXTURE3D_DESC1
{
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    D3D11_TEXTURE_LAYOUT TextureLayout;
} D3D11_TEXTURE3D_DESC1;

typedef struct D3D11_RASTERIZER_DESC2
{
    D3D11_FILL_MODE FillMode;
    D3D11_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    int DepthBias;
    float DepthBiasClamp;
    float SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL ScissorEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;
    D3D11_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D11_RASTERIZER_DESC2;

typedef struct D3D11_TEX2D_SRV1
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT PlaneSlice;
} D3D11_TEX2D_SRV1;

typedef struct D3D11_TEX2D_ARRAY_SRV1
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D11_TEX2D_ARRAY_SRV1;

typedef struct D3D11_SHADER_RESOURCE_VIEW_DESC1
{
    DXGI_FORMAT Format;
    D3D11_SRV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_SRV Buffer;
        D3D11_TEX1D_SRV Texture1D;
        D3D11_TEX1D_ARRAY_SRV Texture1DArray;
        D3D11_TEX2D_SRV1 Texture2D;
        D3D11_TEX2D_ARRAY_SRV1 Texture2DArray;
        D3D11_TEX2DMS_SRV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D11_TEX3D_SRV Texture3D;
        D3D11_TEXCUBE_SRV TextureCube;
        D3D11_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D11_BUFFEREX_SRV BufferEx;
    };
} D3D11_SHADER_RESOURCE_VIEW_DESC1;

typedef struct D3D11_TEX2D_RTV1
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D11_TEX2D_RTV1;

typedef struct D3D11_TEX2D_ARRAY_RTV1
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D11_TEX2D_ARRAY_RTV1;

typedef struct D3D11_RENDER_TARGET_VIEW_DESC1
{
    DXGI_FORMAT Format;
    D3D11_RTV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_RTV Buffer;
        D3D11_TEX1D_RTV Texture1D;
        D3D11_TEX1D_ARRAY_RTV Texture1DArray;
        D3D11_TEX2D_RTV1 Texture2D;
        D3D11_TEX2D_ARRAY_RTV1 Texture2DArray;
        D3D11_TEX2DMS_RTV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D11_TEX3D_RTV Texture3D;
    };
} D3D11_RENDER_TARGET_VIEW_DESC1;

typedef struct D3D11_TEX2D_UAV1
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D11_TEX2D_UAV1;

typedef struct D3D11_TEX2D_ARRAY_UAV1
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D11_TEX2D_ARRAY_UAV1;

typedef struct D3D11_UNORDERED_ACCESS_VIEW_DESC1
{
    DXGI_FORMAT Format;
    D3D11_UAV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_UAV Buffer;
        D3D11_TEX1D_UAV Texture1D;
        D3D11_TEX1D_ARRAY_UAV Texture1DArray;
        D3D11_TEX2D_UAV1 Texture2D;
        D3D11_TEX2D_ARRAY_UAV1 Texture2DArray;
        D3D11_TEX3D_UAV Texture3D;
    };
} D3D11_UNORDERED_ACCESS_VIEW_DESC1;

typedef struct D3D11_QUERY_DESC1
{
    D3D11_QUERY Query;
    UINT MiscFlags;
    D3D11_CONTEXT_TYPE ContextType;
} D3D11_QUERY_DESC1;

[
    uuid(51218251-1e33-4617-9ccb-4d3a4367e7bb),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Texture2D1 : ID3D11Texture2D
{
    void GetDesc1(D3D11_TEXTURE2D_DESC1 *desc);
}

[
    uuid(0c711683-2853-4846-9bb0-f3e60639e46a),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Texture3D1 : ID3D11Texture3D
{
    void GetDesc1(D3D11_TEXTURE3D_DESC1 *desc);
}

[
    uuid(6fbd02fb-209f-46c4-b059-2ed15586a6ac),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11RasterizerState2 : ID3D11RasterizerState1
{
    void GetDesc2(D3D11_RASTERIZER_DESC2 *desc);
}

[
    uuid(91308b87-9040-411d-8c67-c39253ce3802),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11ShaderResourceView1 : ID3D11ShaderResourceView
{
    void GetDesc1(D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc);
}

[
    uuid(ffbe2e23-f011-418a-ac56-5ceed7c5b94b),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11RenderTargetView1 : ID3D11RenderTargetView
{
    void GetDesc1(D3D11_RENDER_TARGET_VIEW_DESC1 *desc);
}

[
    uuid(7b3b6153-a886-4544-ab37-6537c8500403),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11UnorderedAccessView1 : ID3D11UnorderedAccessView
{
    void GetDesc1(D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc);
}

[
    uuid(631b4766-36dc-461d-8db6-c47e13e60916),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Query1 : ID3D11Query
{
    void GetDesc1(D3D11_QUERY_DESC1 *desc);
}

[
    uuid(b4e3c01d-e79e-4637-91b2-510e9f4c9b8f),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11DeviceContext3 : ID3D11DeviceContext2
{
    void Flush1(D3D11_CONTEXT_TYPE type, HANDLE event);
    void SetHardwareProtectionState(BOOL enable);
    void GetHardwareProtectionState(BOOL *enable);
}

[
    uuid(affde9d1-1df7-4bb7-8a34-0f46251dab80),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Fence : ID3D11DeviceChild
{
    HRESULT CreateSharedHandle(
        const SECURITY_ATTRIBUTES *attributes,
        DWORD access,
        const WCHAR *name,
        HANDLE *handle
    );
    UINT64 GetCompletedValue();
    HRESULT SetEventOnCompletion(
        UINT64 value,
        HANDLE event
    );
}

[
    uuid(917600da-f58c-4c33-98d8-3e15b390fa24),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11DeviceContext4 : ID3D11DeviceContext3
{
    HRESULT Signal(ID3D11Fence *fence, UINT64 value);
    HRESULT Wait(ID3D11Fence *fence, UINT64 value);
}

[
    uuid(a05c8c37-d2c6-4732-b3a0-9ce0b0dc9ae6),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Device3 : ID3D11Device2
{
    HRESULT CreateTexture2D1(
        const D3D11_TEXTURE2D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture2D1 **texture
    );
    HRESULT CreateTexture3D1(
        const D3D11_TEXTURE3D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture3D1 **texture
    );
    HRESULT CreateRasterizerState2(
        const D3D11_RASTERIZER_DESC2 *desc,
        ID3D11RasterizerState2 **state
    );
    HRESULT CreateShaderResourceView1(
        ID3D11Resource *resource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,
        ID3D11ShaderResourceView1 **view
    );
    HRESULT CreateUnorderedAccessView1(
        ID3D11Resource *resource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,
        ID3D11UnorderedAccessView1 **view
    );
    HRESULT CreateRenderTargetView1(
        ID3D11Resource *resource,
        const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,
        ID3D11RenderTargetView1 **view
    );
    HRESULT CreateQuery1(const D3D11_QUERY_DESC1 *desc, ID3D11Query1 **query);
    void GetImmediateContext3(ID3D11DeviceContext3 **context);
    HRESULT CreateDeferredContext3(UINT flags, ID3D11DeviceContext3 **context);
    void WriteToSubresource(
        ID3D11Resource *dst_resource,
        UINT dst_subresource,
        const D3D11_BOX *dst_box,
        const void *src_data,
        UINT src_row_pitch,
        UINT src_depth_pitch
    );
    void ReadFromSubresource(
        void *dst_data,
        UINT dst_row_pitch,
        UINT dst_depth_pitch,
        ID3D11Resource *src_resource,
        UINT src_subresource,
        const D3D11_BOX *src_box
    );
}
