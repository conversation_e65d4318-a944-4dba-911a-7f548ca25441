#include "func.def.in"

LIBRARY "ole32.dll"
EXPORTS
OleGetPackageClipboardOwner
CLIPFORMAT_UserFreeExt
CLIPFORMAT_UserMarshalExt
CLIPFORMAT_UserSizeExt
CLIPFORMAT_UserUnmarshalExt
CheckInitDde
CleanROTForApartment
ClipboardProcessUninitialize
DdeBindToObject
DestroyRunningObjectTable
FindExt
GetObjectFromRotByPath
HPALETTE_UserFreeExt
HPALETTE_UserMarshalExt
HPALETTE_UserSizeExt
HPALETTE_UserUnmarshalExt
HRGN_UserFree
HRGN_UserMarshal
HRGN_UserSize
HRGN_UserUnmarshal
HWND_UserFreeExt
HWND_UserMarshalExt
HWND_UserSizeExt
HWND_UserUnmarshalExt
MonikerLoadTypeLib
PropVariantChangeType
ReadStorageProperties
STGMEDIUM_UserFreeExt
STGMEDIUM_UserMarshalExt
STGMEDIUM_UserSizeExt
STGMEDIUM_UserUnmarshalExt
SetOleautModule
StdTypesGetClassObject
StdTypesRegisterServer
WriteStorageProperties
BindMoniker
CLIPFORMAT_UserFree
F64(CLIPFORMAT_UserFree64)
CLIPFORMAT_UserMarshal
F64(CLIPFORMAT_UserMarshal64)
CLIPFORMAT_UserSize
F64(CLIPFORMAT_UserSize64)
CLIPFORMAT_UserUnmarshal
F64(CLIPFORMAT_UserUnmarshal64)
CLSIDFromOle1Class
CLSIDFromProgID
CLSIDFromProgIDEx
CLSIDFromString
CoAddRefServerProcess
CoAicGetTokenForCOM
CoAllowSetForegroundWindow
CoAllowUnmarshalerCLSID
CoBuildVersion
CoCancelCall
CoCheckElevationEnabled
CoCopyProxy
CoCreateFreeThreadedMarshaler
CoCreateGuid
CoCreateInstance
CoCreateInstanceEx
CoCreateInstanceFromApp
CoCreateObjectInContext
CoDeactivateObject
CoDecodeProxy
CoDecrementMTAUsage
CoDisableCallCancellation
CoDisconnectContext
CoDisconnectObject
CoDosDateTimeToFileTime
CoEnableCallCancellation
CoFileTimeNow
CoFileTimeToDosDateTime
CoFreeAllLibraries
CoFreeLibrary
CoFreeUnusedLibraries
CoFreeUnusedLibrariesEx
CoGetActivationState
CoGetApartmentID
CoGetApartmentType
CoGetCallContext
CoGetCallState
CoGetCallerTID
CoGetCancelObject
CoGetClassObject
CoGetClassVersion
CoGetComCatalog
CoGetContextToken
CoGetCurrentLogicalThreadId
CoGetCurrentProcess
CoGetDefaultContext
CoGetInstanceFromFile
CoGetInstanceFromIStorage
CoGetInterceptor
CoGetInterceptorForOle32
CoGetInterceptorFromTypeInfo
CoGetInterfaceAndReleaseStream
CoGetMalloc
CoGetMarshalSizeMax
CoGetModuleType
CoGetObject
CoGetObjectContext
CoGetPSClsid
CoGetProcessIdentifier
CoGetStandardMarshal
CoGetState
CoGetStdMarshalEx
CoGetSystemSecurityPermissions
CoGetSystemWow64DirectoryW
CoGetTreatAsClass
CoHandlePriorityEventsFromMessagePump
CoImpersonateClient
CoIncrementMTAUsage
CoInitialize
CoInitializeEx
CoInitializeSecurity
CoInitializeWOW
CoInstall
CoInvalidateRemoteMachineBindings
CoIsHandlerConnected
CoIsOle1Class
CoLoadLibrary
CoLockObjectExternal
CoMarshalHresult
CoMarshalInterThreadInterfaceInStream
CoMarshalInterface
CoPopServiceDomain
CoPushServiceDomain
CoQueryAuthenticationServices
CoQueryClientBlanket
CoQueryProxyBlanket
CoQueryReleaseObject
CoReactivateObject
CoRegisterActivationFilter
CoRegisterChannelHook
CoRegisterClassObject
CoRegisterInitializeSpy
CoRegisterMallocSpy
CoRegisterMessageFilter
CoRegisterPSClsid
CoRegisterSurrogate
CoRegisterSurrogateEx
CoReleaseMarshalData
CoReleaseServerProcess
CoResumeClassObjects
CoRetireServer
CoRevertToSelf
CoRevokeClassObject
CoRevokeInitializeSpy
CoRevokeMallocSpy
CoSetCancelObject
CoSetMessageDispatcher
CoSetProxyBlanket
CoSetState
CoSuspendClassObjects
CoSwitchCallContext
CoTaskMemAlloc
CoTaskMemFree
CoTaskMemRealloc
CoTestCancel
CoTreatAsClass
CoUninitialize
CoUnloadingWOW
CoUnmarshalHresult
CoUnmarshalInterface
CoVrfCheckThreadState
CoVrfGetThreadState
CoVrfReleaseThreadState
CoWaitForMultipleHandles
CoWaitForMultipleObjects
ComPs_NdrDllCanUnloadNow
ComPs_NdrDllGetClassObject
ComPs_NdrDllRegisterProxy
ComPs_NdrDllUnregisterProxy
CreateAntiMoniker
CreateBindCtx
CreateClassMoniker
CreateDataAdviseHolder
CreateDataCache
CreateErrorInfo
CreateFileMoniker
CreateGenericComposite
CreateILockBytesOnHGlobal
CreateItemMoniker
CreateObjrefMoniker
CreateOleAdviseHolder
CreatePointerMoniker
CreateStdProgressIndicator
CreateStreamOnHGlobal
DcomChannelSetHResult
DeletePatternAndExtensionTables
DllDebugObjectRPCHook
DllGetClassObjectWOW
DoDragDrop
DragDropSetFDT
EnableHookObject
FmtIdToPropStgName
FreePropVariantArray
GetActiveObjectExt
GetClassFile
GetConvertStg
GetDocumentBitStg
GetErrorInfo
GetHGlobalFromILockBytes
GetHGlobalFromStream
GetHookInterface
GetRunningObjectTable
HACCEL_UserFree
F64(HACCEL_UserFree64)
HACCEL_UserMarshal
F64(HACCEL_UserMarshal64)
HACCEL_UserSize
F64(HACCEL_UserSize64)
HACCEL_UserUnmarshal
F64(HACCEL_UserUnmarshal64)
HBITMAP_UserFree
F64(HBITMAP_UserFree64)
HBITMAP_UserMarshal
F64(HBITMAP_UserMarshal64)
HBITMAP_UserSize
F64(HBITMAP_UserSize64)
HBITMAP_UserUnmarshal
F64(HBITMAP_UserUnmarshal64)
HBRUSH_UserFree
F64(HBRUSH_UserFree64)
HBRUSH_UserMarshal
F64(HBRUSH_UserMarshal64)
HBRUSH_UserSize
F64(HBRUSH_UserSize64)
HBRUSH_UserUnmarshal
F64(HBRUSH_UserUnmarshal64)
HDC_UserFree
F64(HDC_UserFree64)
HDC_UserMarshal
F64(HDC_UserMarshal64)
HDC_UserSize
F64(HDC_UserSize64)
HDC_UserUnmarshal
F64(HDC_UserUnmarshal64)
HENHMETAFILE_UserFree
F64(HENHMETAFILE_UserFree64)
HENHMETAFILE_UserMarshal
F64(HENHMETAFILE_UserMarshal64)
HENHMETAFILE_UserSize
F64(HENHMETAFILE_UserSize64)
HENHMETAFILE_UserUnmarshal
F64(HENHMETAFILE_UserUnmarshal64)
HGLOBAL_UserFree
F64(HGLOBAL_UserFree64)
HGLOBAL_UserMarshal
F64(HGLOBAL_UserMarshal64)
HGLOBAL_UserSize
F64(HGLOBAL_UserSize64)
HGLOBAL_UserUnmarshal
F64(HGLOBAL_UserUnmarshal64)
HICON_UserFree
F64(HICON_UserFree64)
HICON_UserMarshal
F64(HICON_UserMarshal64)
HICON_UserSize
F64(HICON_UserSize64)
HICON_UserUnmarshal
F64(HICON_UserUnmarshal64)
HMENU_UserFree
F64(HMENU_UserFree64)
HMENU_UserMarshal
F64(HMENU_UserMarshal64)
HMENU_UserSize
F64(HMENU_UserSize64)
HMENU_UserUnmarshal
F64(HMENU_UserUnmarshal64)
HMETAFILEPICT_UserFree
F64(HMETAFILEPICT_UserFree64)
HMETAFILEPICT_UserMarshal
F64(HMETAFILEPICT_UserMarshal64)
HMETAFILEPICT_UserSize
F64(HMETAFILEPICT_UserSize64)
HMETAFILEPICT_UserUnmarshal
F64(HMETAFILEPICT_UserUnmarshal64)
HMETAFILE_UserFree
F64(HMETAFILE_UserFree64)
HMETAFILE_UserMarshal
F64(HMETAFILE_UserMarshal64)
HMETAFILE_UserSize
F64(HMETAFILE_UserSize64)
HMETAFILE_UserUnmarshal
F64(HMETAFILE_UserUnmarshal64)
HMONITOR_UserFree
F64(HMONITOR_UserFree64)
HMONITOR_UserMarshal
F64(HMONITOR_UserMarshal64)
HMONITOR_UserSize
F64(HMONITOR_UserSize64)
HMONITOR_UserUnmarshal
F64(HMONITOR_UserUnmarshal64)
HPALETTE_UserFree
F64(HPALETTE_UserFree64)
HPALETTE_UserMarshal
F64(HPALETTE_UserMarshal64)
HPALETTE_UserSize
F64(HPALETTE_UserSize64)
HPALETTE_UserUnmarshal
F64(HPALETTE_UserUnmarshal64)
HWND_UserFree
F64(HWND_UserFree64)
HWND_UserMarshal
F64(HWND_UserMarshal64)
HWND_UserSize
F64(HWND_UserSize64)
HWND_UserUnmarshal
F64(HWND_UserUnmarshal64)
HkOleRegisterObject
IIDFromString
IsAccelerator
IsEqualGUID
IsRoInitializeASTAAllowedInDesktop
IsValidIid
IsValidInterface
IsValidPtrIn
IsValidPtrOut
MkParseDisplayName
MonikerCommonPrefixWith
MonikerRelativePathTo
NdrOleInitializeExtension
NdrProxyForwardingFunction10
NdrProxyForwardingFunction11
NdrProxyForwardingFunction12
NdrProxyForwardingFunction13
NdrProxyForwardingFunction14
NdrProxyForwardingFunction15
NdrProxyForwardingFunction16
NdrProxyForwardingFunction17
NdrProxyForwardingFunction18
NdrProxyForwardingFunction19
NdrProxyForwardingFunction20
NdrProxyForwardingFunction21
NdrProxyForwardingFunction22
NdrProxyForwardingFunction23
NdrProxyForwardingFunction24
NdrProxyForwardingFunction25
NdrProxyForwardingFunction26
NdrProxyForwardingFunction27
NdrProxyForwardingFunction28
NdrProxyForwardingFunction29
NdrProxyForwardingFunction3
NdrProxyForwardingFunction30
NdrProxyForwardingFunction31
NdrProxyForwardingFunction32
NdrProxyForwardingFunction4
NdrProxyForwardingFunction5
NdrProxyForwardingFunction6
NdrProxyForwardingFunction7
NdrProxyForwardingFunction8
NdrProxyForwardingFunction9
ObjectStublessClient10
ObjectStublessClient11
ObjectStublessClient12
ObjectStublessClient13
ObjectStublessClient14
ObjectStublessClient15
ObjectStublessClient16
ObjectStublessClient17
ObjectStublessClient18
ObjectStublessClient19
ObjectStublessClient20
ObjectStublessClient21
ObjectStublessClient22
ObjectStublessClient23
ObjectStublessClient24
ObjectStublessClient25
ObjectStublessClient26
ObjectStublessClient27
ObjectStublessClient28
ObjectStublessClient29
ObjectStublessClient3
ObjectStublessClient30
ObjectStublessClient31
ObjectStublessClient32
ObjectStublessClient4
ObjectStublessClient5
ObjectStublessClient6
ObjectStublessClient7
ObjectStublessClient8
ObjectStublessClient9
Ole32DllGetClassObject
OleBuildVersion
OleConvertIStorageToOLESTREAM
OleConvertIStorageToOLESTREAMEx
OleConvertOLESTREAMToIStorage
OleConvertOLESTREAMToIStorageEx
OleCreate
OleCreateDefaultHandler
OleCreateEmbeddingHelper
OleCreateEx
OleCreateFontIndirectExt
OleCreateFromData
OleCreateFromDataEx
OleCreateFromFile
OleCreateFromFileEx
OleCreateLink
OleCreateLinkEx
OleCreateLinkFromData
OleCreateLinkFromDataEx
OleCreateLinkToFile
OleCreateLinkToFileEx
OleCreateMenuDescriptor
OleCreatePictureIndirectExt
OleCreatePropertyFrameIndirectExt
OleCreateStaticFromData
OleDestroyMenuDescriptor
OleDoAutoConvert
OleDraw
OleDuplicateData
OleFlushClipboard
OleGetAutoConvert
OleGetClipboard
OleGetIconOfClass
OleGetIconOfFile
OleIconToCursorExt
OleInitialize
OleInitializeWOW
OleIsCurrentClipboard
OleIsRunning
OleLoad
OleLoadFromStream
OleLoadPictureExt
OleLoadPictureFileExt
OleLoadPicturePathExt
OleLockRunning
OleMetafilePictFromIconAndLabel
OleNoteObjectVisible
OleQueryCreateFromData
OleQueryLinkFromData
OleRegEnumFormatEtc
OleRegEnumVerbs
OleRegGetMiscStatus
OleRegGetUserType
OleReleaseEnumVerbCache
OleRun
OleSave
OleSavePictureFileExt
OleSaveToStream
OleSetAutoConvert
OleSetClipboard
OleSetContainedObject
OleSetMenuDescriptor
OleTranslateAccelerator
OleTranslateColorExt
OleUninitialize
OpenOrCreateStream
ProgIDFromCLSID
PropStgNameToFmtId
PropSysAllocString
PropSysFreeString
PropVariantClear
PropVariantCopy
ReadClassStg
ReadClassStm
ReadFmtUserTypeStg
ReadOleStg
ReadStringStream
RegisterActiveObjectExt
RegisterDragDrop
ReleaseStgMedium
RevokeActiveObjectExt
RevokeDragDrop
RoGetAgileReference
SNB_UserFree
F64(SNB_UserFree64)
SNB_UserMarshal
F64(SNB_UserMarshal64)
SNB_UserSize
F64(SNB_UserSize64)
SNB_UserUnmarshal
F64(SNB_UserUnmarshal64)
STGMEDIUM_UserFree
F64(STGMEDIUM_UserFree64)
STGMEDIUM_UserMarshal
F64(STGMEDIUM_UserMarshal64)
STGMEDIUM_UserSize
F64(STGMEDIUM_UserSize64)
STGMEDIUM_UserUnmarshal
F64(STGMEDIUM_UserUnmarshal64)
SetConvertStg
SetDocumentBitStg
SetErrorInfo
SetWOWThunkGlobalPtr
StgConvertPropertyToVariant
StgConvertVariantToProperty
StgCreateDocfile
StgCreateDocfileOnILockBytes
StgCreatePropSetStg
StgCreatePropStg
StgCreateStorageEx
StgGetIFillLockBytesOnFile
StgGetIFillLockBytesOnILockBytes
StgIsStorageFile
StgIsStorageILockBytes
StgOpenAsyncDocfileOnIFillLockBytes
StgOpenPropStg
StgOpenStorage
StgOpenStorageEx
StgOpenStorageOnHandle
StgOpenStorageOnILockBytes
StgPropertyLengthAsVariant
StgSetTimes
StringFromCLSID
StringFromGUID2
StringFromIID
UpdateDCOMSettings
UpdateProcessTracing
UtConvertDvtd16toDvtd32
UtConvertDvtd32toDvtd16
UtGetDvtd16Info
UtGetDvtd32Info
WdtpInterfacePointer_UserFree
F64(WdtpInterfacePointer_UserFree64)
WdtpInterfacePointer_UserMarshal
F64(WdtpInterfacePointer_UserMarshal64)
WdtpInterfacePointer_UserSize
F64(WdtpInterfacePointer_UserSize64)
WdtpInterfacePointer_UserUnmarshal
F64(WdtpInterfacePointer_UserUnmarshal64)
WriteClassStg
WriteClassStm
WriteFmtUserTypeStg
WriteOleStg
WriteStringStream
