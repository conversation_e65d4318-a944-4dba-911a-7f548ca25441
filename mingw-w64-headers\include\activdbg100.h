/*** Autogenerated by WIDL 8.5 from include/activdbg100.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __activdbg100_h__
#define __activdbg100_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __IDebugApplicationNode100_FWD_DEFINED__
#define __IDebugApplicationNode100_FWD_DEFINED__
typedef interface IDebugApplicationNode100 IDebugApplicationNode100;
#ifdef __cplusplus
interface IDebugApplicationNode100;
#endif /* __cplusplus */
#endif

#ifndef __IWebAppDiagnosticsSetup_FWD_DEFINED__
#define __IWebAppDiagnosticsSetup_FWD_DEFINED__
typedef interface IWebAppDiagnosticsSetup IWebAppDiagnosticsSetup;
#ifdef __cplusplus
interface IWebAppDiagnosticsSetup;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplication110_FWD_DEFINED__
#define __IRemoteDebugApplication110_FWD_DEFINED__
typedef interface IRemoteDebugApplication110 IRemoteDebugApplication110;
#ifdef __cplusplus
interface IRemoteDebugApplication110;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication11032_FWD_DEFINED__
#define __IDebugApplication11032_FWD_DEFINED__
typedef interface IDebugApplication11032 IDebugApplication11032;
#ifdef __cplusplus
interface IDebugApplication11032;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication11064_FWD_DEFINED__
#define __IDebugApplication11064_FWD_DEFINED__
typedef interface IDebugApplication11064 IDebugApplication11064;
#ifdef __cplusplus
interface IDebugApplication11064;
#endif /* __cplusplus */
#endif

#ifndef __IWebAppDiagnosticsObjectInitialization_FWD_DEFINED__
#define __IWebAppDiagnosticsObjectInitialization_FWD_DEFINED__
typedef interface IWebAppDiagnosticsObjectInitialization IWebAppDiagnosticsObjectInitialization;
#ifdef __cplusplus
interface IWebAppDiagnosticsObjectInitialization;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptWinRTErrorDebug_FWD_DEFINED__
#define __IActiveScriptWinRTErrorDebug_FWD_DEFINED__
typedef interface IActiveScriptWinRTErrorDebug IActiveScriptWinRTErrorDebug;
#ifdef __cplusplus
interface IActiveScriptWinRTErrorDebug;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptErrorDebug110_FWD_DEFINED__
#define __IActiveScriptErrorDebug110_FWD_DEFINED__
typedef interface IActiveScriptErrorDebug110 IActiveScriptErrorDebug110;
#ifdef __cplusplus
interface IActiveScriptErrorDebug110;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThreadEvents110_FWD_DEFINED__
#define __IDebugApplicationThreadEvents110_FWD_DEFINED__
typedef interface IDebugApplicationThreadEvents110 IDebugApplicationThreadEvents110;
#ifdef __cplusplus
interface IDebugApplicationThreadEvents110;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThread11032_FWD_DEFINED__
#define __IDebugApplicationThread11032_FWD_DEFINED__
typedef interface IDebugApplicationThread11032 IDebugApplicationThread11032;
#ifdef __cplusplus
interface IDebugApplicationThread11032;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThread11064_FWD_DEFINED__
#define __IDebugApplicationThread11064_FWD_DEFINED__
typedef interface IDebugApplicationThread11064 IDebugApplicationThread11064;
#ifdef __cplusplus
interface IDebugApplicationThread11064;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugCriticalErrorEvent110_FWD_DEFINED__
#define __IRemoteDebugCriticalErrorEvent110_FWD_DEFINED__
typedef interface IRemoteDebugCriticalErrorEvent110 IRemoteDebugCriticalErrorEvent110;
#ifdef __cplusplus
interface IRemoteDebugCriticalErrorEvent110;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <activdbg.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IWebAppDiagnosticsSetupEvent_FWD_DEFINED__
#define __IWebAppDiagnosticsSetupEvent_FWD_DEFINED__
typedef interface IWebAppDiagnosticsSetupEvent IWebAppDiagnosticsSetupEvent;
#ifdef __cplusplus
interface IWebAppDiagnosticsSetupEvent;
#endif /* __cplusplus */
#endif

typedef enum tagAPPLICATION_NODE_EVENT_FILTER {
    FILTER_EXCLUDE_NOTHING = 0x0,
    FILTER_EXCLUDE_ANONYMOUS_CODE = 0x1,
    FILTER_EXCLUDE_EVAL_CODE = 0x2
} APPLICATION_NODE_EVENT_FILTER;
typedef enum tagSCRIPT_ERROR_DEBUG_EXCEPTION_THROWN_KIND {
    ETK_FIRST_CHANCE = 0x0,
    ETK_USER_UNHANDLED = 0x1,
    ETK_UNHANDLED = 0x2
} SCRIPT_ERROR_DEBUG_EXCEPTION_THROWN_KIND;
enum SCRIPT_DEBUGGER_OPTIONS {
    SDO_NONE = 0x0,
    SDO_ENABLE_FIRST_CHANCE_EXCEPTIONS = 0x1,
    SDO_ENABLE_WEB_WORKER_SUPPORT = 0x2,
    SDO_ENABLE_NONUSER_CODE_SUPPORT = 0x4
};
typedef struct tagTEXT_DOCUMENT_ARRAY {
    DWORD dwCount;
    IDebugDocumentText **Members;
} TEXT_DOCUMENT_ARRAY;
DEFINE_ENUM_FLAG_OPERATORS(SCRIPT_DEBUGGER_OPTIONS)
#ifndef DISABLE_ACTIVDBG_INTERFACE_WRAPPERS
#ifdef _WIN64
#define IDebugApplication110 IDebugApplication11064
#define IID_IDebugApplication110 IID_IDebugApplication11064
#define IDebugApplicationThread110 IDebugApplicationThread11064
#define IID_IDebugApplicationThread110 IID_IDebugApplicationThread11064
#else
#define IDebugApplication110 IDebugApplication11032
#define IID_IDebugApplication110 IID_IDebugApplication11032
#define IDebugApplicationThread110 IDebugApplicationThread11032
#define IID_IDebugApplicationThread110 IID_IDebugApplicationThread11032
#endif
#endif
/*****************************************************************************
 * IDebugApplicationNode100 interface
 */
#ifndef __IDebugApplicationNode100_INTERFACE_DEFINED__
#define __IDebugApplicationNode100_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationNode100, 0x90a7734e, 0x841b, 0x4f77, 0x93,0x84, 0xa2,0x89,0x1e,0x76,0xe7,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("90a7734e-841b-4f77-9384-a2891e76e7e2")
IDebugApplicationNode100 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetFilterForEventSink(
        DWORD dwCookie,
        APPLICATION_NODE_EVENT_FILTER filter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExcludedDocuments(
        APPLICATION_NODE_EVENT_FILTER filter,
        TEXT_DOCUMENT_ARRAY *pDocuments) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryIsChildNode(
        IDebugDocument *pSearchKey) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationNode100, 0x90a7734e, 0x841b, 0x4f77, 0x93,0x84, 0xa2,0x89,0x1e,0x76,0xe7,0xe2)
#endif
#else
typedef struct IDebugApplicationNode100Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationNode100 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationNode100 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationNode100 *This);

    /*** IDebugApplicationNode100 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFilterForEventSink)(
        IDebugApplicationNode100 *This,
        DWORD dwCookie,
        APPLICATION_NODE_EVENT_FILTER filter);

    HRESULT (STDMETHODCALLTYPE *GetExcludedDocuments)(
        IDebugApplicationNode100 *This,
        APPLICATION_NODE_EVENT_FILTER filter,
        TEXT_DOCUMENT_ARRAY *pDocuments);

    HRESULT (STDMETHODCALLTYPE *QueryIsChildNode)(
        IDebugApplicationNode100 *This,
        IDebugDocument *pSearchKey);

    END_INTERFACE
} IDebugApplicationNode100Vtbl;

interface IDebugApplicationNode100 {
    CONST_VTBL IDebugApplicationNode100Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationNode100_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationNode100_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationNode100_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugApplicationNode100 methods ***/
#define IDebugApplicationNode100_SetFilterForEventSink(This,dwCookie,filter) (This)->lpVtbl->SetFilterForEventSink(This,dwCookie,filter)
#define IDebugApplicationNode100_GetExcludedDocuments(This,filter,pDocuments) (This)->lpVtbl->GetExcludedDocuments(This,filter,pDocuments)
#define IDebugApplicationNode100_QueryIsChildNode(This,pSearchKey) (This)->lpVtbl->QueryIsChildNode(This,pSearchKey)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationNode100_QueryInterface(IDebugApplicationNode100* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDebugApplicationNode100_AddRef(IDebugApplicationNode100* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDebugApplicationNode100_Release(IDebugApplicationNode100* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugApplicationNode100 methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationNode100_SetFilterForEventSink(IDebugApplicationNode100* This,DWORD dwCookie,APPLICATION_NODE_EVENT_FILTER filter) {
    return This->lpVtbl->SetFilterForEventSink(This,dwCookie,filter);
}
static __WIDL_INLINE HRESULT IDebugApplicationNode100_GetExcludedDocuments(IDebugApplicationNode100* This,APPLICATION_NODE_EVENT_FILTER filter,TEXT_DOCUMENT_ARRAY *pDocuments) {
    return This->lpVtbl->GetExcludedDocuments(This,filter,pDocuments);
}
static __WIDL_INLINE HRESULT IDebugApplicationNode100_QueryIsChildNode(IDebugApplicationNode100* This,IDebugDocument *pSearchKey) {
    return This->lpVtbl->QueryIsChildNode(This,pSearchKey);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationNode100_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWebAppDiagnosticsSetup interface
 */
#ifndef __IWebAppDiagnosticsSetup_INTERFACE_DEFINED__
#define __IWebAppDiagnosticsSetup_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWebAppDiagnosticsSetup, 0x379bfbe1, 0xc6c9, 0x432a, 0x93,0xe1, 0x6d,0x17,0x65,0x6c,0x53,0x8c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("379bfbe1-c6c9-432a-93e1-6d17656c538c")
IWebAppDiagnosticsSetup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DiagnosticsSupported(
        VARIANT_BOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateObjectWithSiteAtWebApp(
        REFCLSID rclsid,
        DWORD dwClsContext,
        REFIID riid,
        DWORD_PTR hPassToObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebAppDiagnosticsSetup, 0x379bfbe1, 0xc6c9, 0x432a, 0x93,0xe1, 0x6d,0x17,0x65,0x6c,0x53,0x8c)
#endif
#else
typedef struct IWebAppDiagnosticsSetupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebAppDiagnosticsSetup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebAppDiagnosticsSetup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebAppDiagnosticsSetup *This);

    /*** IWebAppDiagnosticsSetup methods ***/
    HRESULT (STDMETHODCALLTYPE *DiagnosticsSupported)(
        IWebAppDiagnosticsSetup *This,
        VARIANT_BOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *CreateObjectWithSiteAtWebApp)(
        IWebAppDiagnosticsSetup *This,
        REFCLSID rclsid,
        DWORD dwClsContext,
        REFIID riid,
        DWORD_PTR hPassToObject);

    END_INTERFACE
} IWebAppDiagnosticsSetupVtbl;

interface IWebAppDiagnosticsSetup {
    CONST_VTBL IWebAppDiagnosticsSetupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebAppDiagnosticsSetup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebAppDiagnosticsSetup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebAppDiagnosticsSetup_Release(This) (This)->lpVtbl->Release(This)
/*** IWebAppDiagnosticsSetup methods ***/
#define IWebAppDiagnosticsSetup_DiagnosticsSupported(This,pRetVal) (This)->lpVtbl->DiagnosticsSupported(This,pRetVal)
#define IWebAppDiagnosticsSetup_CreateObjectWithSiteAtWebApp(This,rclsid,dwClsContext,riid,hPassToObject) (This)->lpVtbl->CreateObjectWithSiteAtWebApp(This,rclsid,dwClsContext,riid,hPassToObject)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IWebAppDiagnosticsSetup_QueryInterface(IWebAppDiagnosticsSetup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IWebAppDiagnosticsSetup_AddRef(IWebAppDiagnosticsSetup* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IWebAppDiagnosticsSetup_Release(IWebAppDiagnosticsSetup* This) {
    return This->lpVtbl->Release(This);
}
/*** IWebAppDiagnosticsSetup methods ***/
static __WIDL_INLINE HRESULT IWebAppDiagnosticsSetup_DiagnosticsSupported(IWebAppDiagnosticsSetup* This,VARIANT_BOOL *pRetVal) {
    return This->lpVtbl->DiagnosticsSupported(This,pRetVal);
}
static __WIDL_INLINE HRESULT IWebAppDiagnosticsSetup_CreateObjectWithSiteAtWebApp(IWebAppDiagnosticsSetup* This,REFCLSID rclsid,DWORD dwClsContext,REFIID riid,DWORD_PTR hPassToObject) {
    return This->lpVtbl->CreateObjectWithSiteAtWebApp(This,rclsid,dwClsContext,riid,hPassToObject);
}
#endif
#endif

#endif


#endif  /* __IWebAppDiagnosticsSetup_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRemoteDebugApplication110 interface
 */
#ifndef __IRemoteDebugApplication110_INTERFACE_DEFINED__
#define __IRemoteDebugApplication110_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRemoteDebugApplication110, 0xd5fe005b, 0x2836, 0x485e, 0xb1,0xf9, 0x89,0xd9,0x1a,0xa2,0x4f,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5fe005b-2836-485e-b1f9-89d91aa24fd4")
IRemoteDebugApplication110 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDebuggerOptions(
        enum SCRIPT_DEBUGGER_OPTIONS mask,
        enum SCRIPT_DEBUGGER_OPTIONS value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentDebuggerOptions(
        enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMainThread(
        IRemoteDebugApplicationThread **ppThread) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRemoteDebugApplication110, 0xd5fe005b, 0x2836, 0x485e, 0xb1,0xf9, 0x89,0xd9,0x1a,0xa2,0x4f,0xd4)
#endif
#else
typedef struct IRemoteDebugApplication110Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRemoteDebugApplication110 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRemoteDebugApplication110 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRemoteDebugApplication110 *This);

    /*** IRemoteDebugApplication110 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDebuggerOptions)(
        IRemoteDebugApplication110 *This,
        enum SCRIPT_DEBUGGER_OPTIONS mask,
        enum SCRIPT_DEBUGGER_OPTIONS value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentDebuggerOptions)(
        IRemoteDebugApplication110 *This,
        enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions);

    HRESULT (STDMETHODCALLTYPE *GetMainThread)(
        IRemoteDebugApplication110 *This,
        IRemoteDebugApplicationThread **ppThread);

    END_INTERFACE
} IRemoteDebugApplication110Vtbl;

interface IRemoteDebugApplication110 {
    CONST_VTBL IRemoteDebugApplication110Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRemoteDebugApplication110_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRemoteDebugApplication110_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRemoteDebugApplication110_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplication110 methods ***/
#define IRemoteDebugApplication110_SetDebuggerOptions(This,mask,value) (This)->lpVtbl->SetDebuggerOptions(This,mask,value)
#define IRemoteDebugApplication110_GetCurrentDebuggerOptions(This,pCurrentOptions) (This)->lpVtbl->GetCurrentDebuggerOptions(This,pCurrentOptions)
#define IRemoteDebugApplication110_GetMainThread(This,ppThread) (This)->lpVtbl->GetMainThread(This,ppThread)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IRemoteDebugApplication110_QueryInterface(IRemoteDebugApplication110* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IRemoteDebugApplication110_AddRef(IRemoteDebugApplication110* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IRemoteDebugApplication110_Release(IRemoteDebugApplication110* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplication110 methods ***/
static __WIDL_INLINE HRESULT IRemoteDebugApplication110_SetDebuggerOptions(IRemoteDebugApplication110* This,enum SCRIPT_DEBUGGER_OPTIONS mask,enum SCRIPT_DEBUGGER_OPTIONS value) {
    return This->lpVtbl->SetDebuggerOptions(This,mask,value);
}
static __WIDL_INLINE HRESULT IRemoteDebugApplication110_GetCurrentDebuggerOptions(IRemoteDebugApplication110* This,enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions) {
    return This->lpVtbl->GetCurrentDebuggerOptions(This,pCurrentOptions);
}
static __WIDL_INLINE HRESULT IRemoteDebugApplication110_GetMainThread(IRemoteDebugApplication110* This,IRemoteDebugApplicationThread **ppThread) {
    return This->lpVtbl->GetMainThread(This,ppThread);
}
#endif
#endif

#endif


#endif  /* __IRemoteDebugApplication110_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplication11032 interface
 */
#ifndef __IDebugApplication11032_INTERFACE_DEFINED__
#define __IDebugApplication11032_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplication11032, 0xbdb3b5de, 0x89f2, 0x4e11, 0x84,0xa5, 0x97,0x44,0x5f,0x94,0x1c,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bdb3b5de-89f2-4e11-84a5-97445f941c7d")
IDebugApplication11032 : public IRemoteDebugApplication110
{
    virtual HRESULT STDMETHODCALLTYPE SynchronousCallInMainThread(
        IDebugThreadCall32 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE AsynchronousCallInMainThread(
        IDebugThreadCall32 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE CallableWaitForHandles(
        DWORD handleCount,
        const HANDLE *pHandles,
        DWORD *pIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplication11032, 0xbdb3b5de, 0x89f2, 0x4e11, 0x84,0xa5, 0x97,0x44,0x5f,0x94,0x1c,0x7d)
#endif
#else
typedef struct IDebugApplication11032Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplication11032 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplication11032 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplication11032 *This);

    /*** IRemoteDebugApplication110 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDebuggerOptions)(
        IDebugApplication11032 *This,
        enum SCRIPT_DEBUGGER_OPTIONS mask,
        enum SCRIPT_DEBUGGER_OPTIONS value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentDebuggerOptions)(
        IDebugApplication11032 *This,
        enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions);

    HRESULT (STDMETHODCALLTYPE *GetMainThread)(
        IDebugApplication11032 *This,
        IRemoteDebugApplicationThread **ppThread);

    /*** IDebugApplication11032 methods ***/
    HRESULT (STDMETHODCALLTYPE *SynchronousCallInMainThread)(
        IDebugApplication11032 *This,
        IDebugThreadCall32 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3);

    HRESULT (STDMETHODCALLTYPE *AsynchronousCallInMainThread)(
        IDebugApplication11032 *This,
        IDebugThreadCall32 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3);

    HRESULT (STDMETHODCALLTYPE *CallableWaitForHandles)(
        IDebugApplication11032 *This,
        DWORD handleCount,
        const HANDLE *pHandles,
        DWORD *pIndex);

    END_INTERFACE
} IDebugApplication11032Vtbl;

interface IDebugApplication11032 {
    CONST_VTBL IDebugApplication11032Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplication11032_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplication11032_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplication11032_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplication110 methods ***/
#define IDebugApplication11032_SetDebuggerOptions(This,mask,value) (This)->lpVtbl->SetDebuggerOptions(This,mask,value)
#define IDebugApplication11032_GetCurrentDebuggerOptions(This,pCurrentOptions) (This)->lpVtbl->GetCurrentDebuggerOptions(This,pCurrentOptions)
#define IDebugApplication11032_GetMainThread(This,ppThread) (This)->lpVtbl->GetMainThread(This,ppThread)
/*** IDebugApplication11032 methods ***/
#define IDebugApplication11032_SynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3)
#define IDebugApplication11032_AsynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->AsynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3)
#define IDebugApplication11032_CallableWaitForHandles(This,handleCount,pHandles,pIndex) (This)->lpVtbl->CallableWaitForHandles(This,handleCount,pHandles,pIndex)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDebugApplication11032_QueryInterface(IDebugApplication11032* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDebugApplication11032_AddRef(IDebugApplication11032* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDebugApplication11032_Release(IDebugApplication11032* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplication110 methods ***/
static __WIDL_INLINE HRESULT IDebugApplication11032_SetDebuggerOptions(IDebugApplication11032* This,enum SCRIPT_DEBUGGER_OPTIONS mask,enum SCRIPT_DEBUGGER_OPTIONS value) {
    return This->lpVtbl->SetDebuggerOptions(This,mask,value);
}
static __WIDL_INLINE HRESULT IDebugApplication11032_GetCurrentDebuggerOptions(IDebugApplication11032* This,enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions) {
    return This->lpVtbl->GetCurrentDebuggerOptions(This,pCurrentOptions);
}
static __WIDL_INLINE HRESULT IDebugApplication11032_GetMainThread(IDebugApplication11032* This,IRemoteDebugApplicationThread **ppThread) {
    return This->lpVtbl->GetMainThread(This,ppThread);
}
/*** IDebugApplication11032 methods ***/
static __WIDL_INLINE HRESULT IDebugApplication11032_SynchronousCallInMainThread(IDebugApplication11032* This,IDebugThreadCall32 *pptc,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3) {
    return This->lpVtbl->SynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
static __WIDL_INLINE HRESULT IDebugApplication11032_AsynchronousCallInMainThread(IDebugApplication11032* This,IDebugThreadCall32 *pptc,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3) {
    return This->lpVtbl->AsynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
static __WIDL_INLINE HRESULT IDebugApplication11032_CallableWaitForHandles(IDebugApplication11032* This,DWORD handleCount,const HANDLE *pHandles,DWORD *pIndex) {
    return This->lpVtbl->CallableWaitForHandles(This,handleCount,pHandles,pIndex);
}
#endif
#endif

#endif


#endif  /* __IDebugApplication11032_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplication11064 interface
 */
#ifndef __IDebugApplication11064_INTERFACE_DEFINED__
#define __IDebugApplication11064_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplication11064, 0x2039d958, 0x4eeb, 0x496a, 0x87,0xbb, 0x2e,0x52,0x01,0xea,0xde,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2039d958-4eeb-496a-87bb-2e5201eadeef")
IDebugApplication11064 : public IRemoteDebugApplication110
{
    virtual HRESULT STDMETHODCALLTYPE SynchronousCallInMainThread(
        IDebugThreadCall64 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE AsynchronousCallInMainThread(
        IDebugThreadCall64 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE CallableWaitForHandles(
        DWORD handleCount,
        const HANDLE *pHandles,
        DWORD *pIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplication11064, 0x2039d958, 0x4eeb, 0x496a, 0x87,0xbb, 0x2e,0x52,0x01,0xea,0xde,0xef)
#endif
#else
typedef struct IDebugApplication11064Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplication11064 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplication11064 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplication11064 *This);

    /*** IRemoteDebugApplication110 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDebuggerOptions)(
        IDebugApplication11064 *This,
        enum SCRIPT_DEBUGGER_OPTIONS mask,
        enum SCRIPT_DEBUGGER_OPTIONS value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentDebuggerOptions)(
        IDebugApplication11064 *This,
        enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions);

    HRESULT (STDMETHODCALLTYPE *GetMainThread)(
        IDebugApplication11064 *This,
        IRemoteDebugApplicationThread **ppThread);

    /*** IDebugApplication11064 methods ***/
    HRESULT (STDMETHODCALLTYPE *SynchronousCallInMainThread)(
        IDebugApplication11064 *This,
        IDebugThreadCall64 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3);

    HRESULT (STDMETHODCALLTYPE *AsynchronousCallInMainThread)(
        IDebugApplication11064 *This,
        IDebugThreadCall64 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3);

    HRESULT (STDMETHODCALLTYPE *CallableWaitForHandles)(
        IDebugApplication11064 *This,
        DWORD handleCount,
        const HANDLE *pHandles,
        DWORD *pIndex);

    END_INTERFACE
} IDebugApplication11064Vtbl;

interface IDebugApplication11064 {
    CONST_VTBL IDebugApplication11064Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplication11064_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplication11064_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplication11064_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplication110 methods ***/
#define IDebugApplication11064_SetDebuggerOptions(This,mask,value) (This)->lpVtbl->SetDebuggerOptions(This,mask,value)
#define IDebugApplication11064_GetCurrentDebuggerOptions(This,pCurrentOptions) (This)->lpVtbl->GetCurrentDebuggerOptions(This,pCurrentOptions)
#define IDebugApplication11064_GetMainThread(This,ppThread) (This)->lpVtbl->GetMainThread(This,ppThread)
/*** IDebugApplication11064 methods ***/
#define IDebugApplication11064_SynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3)
#define IDebugApplication11064_AsynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->AsynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3)
#define IDebugApplication11064_CallableWaitForHandles(This,handleCount,pHandles,pIndex) (This)->lpVtbl->CallableWaitForHandles(This,handleCount,pHandles,pIndex)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDebugApplication11064_QueryInterface(IDebugApplication11064* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDebugApplication11064_AddRef(IDebugApplication11064* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDebugApplication11064_Release(IDebugApplication11064* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplication110 methods ***/
static __WIDL_INLINE HRESULT IDebugApplication11064_SetDebuggerOptions(IDebugApplication11064* This,enum SCRIPT_DEBUGGER_OPTIONS mask,enum SCRIPT_DEBUGGER_OPTIONS value) {
    return This->lpVtbl->SetDebuggerOptions(This,mask,value);
}
static __WIDL_INLINE HRESULT IDebugApplication11064_GetCurrentDebuggerOptions(IDebugApplication11064* This,enum SCRIPT_DEBUGGER_OPTIONS *pCurrentOptions) {
    return This->lpVtbl->GetCurrentDebuggerOptions(This,pCurrentOptions);
}
static __WIDL_INLINE HRESULT IDebugApplication11064_GetMainThread(IDebugApplication11064* This,IRemoteDebugApplicationThread **ppThread) {
    return This->lpVtbl->GetMainThread(This,ppThread);
}
/*** IDebugApplication11064 methods ***/
static __WIDL_INLINE HRESULT IDebugApplication11064_SynchronousCallInMainThread(IDebugApplication11064* This,IDebugThreadCall64 *pptc,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3) {
    return This->lpVtbl->SynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
static __WIDL_INLINE HRESULT IDebugApplication11064_AsynchronousCallInMainThread(IDebugApplication11064* This,IDebugThreadCall64 *pptc,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3) {
    return This->lpVtbl->AsynchronousCallInMainThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
static __WIDL_INLINE HRESULT IDebugApplication11064_CallableWaitForHandles(IDebugApplication11064* This,DWORD handleCount,const HANDLE *pHandles,DWORD *pIndex) {
    return This->lpVtbl->CallableWaitForHandles(This,handleCount,pHandles,pIndex);
}
#endif
#endif

#endif


#endif  /* __IDebugApplication11064_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWebAppDiagnosticsObjectInitialization interface
 */
#ifndef __IWebAppDiagnosticsObjectInitialization_INTERFACE_DEFINED__
#define __IWebAppDiagnosticsObjectInitialization_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWebAppDiagnosticsObjectInitialization, 0x16ff3a42, 0xa5f5, 0x432b, 0xb6,0x25, 0x8e,0x8e,0x16,0xf5,0x7e,0x15);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("16ff3a42-a5f5-432b-b625-8e8e16f57e15")
IWebAppDiagnosticsObjectInitialization : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        HANDLE_PTR hPassedHandle,
        IUnknown *pDebugApplication) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebAppDiagnosticsObjectInitialization, 0x16ff3a42, 0xa5f5, 0x432b, 0xb6,0x25, 0x8e,0x8e,0x16,0xf5,0x7e,0x15)
#endif
#else
typedef struct IWebAppDiagnosticsObjectInitializationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebAppDiagnosticsObjectInitialization *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebAppDiagnosticsObjectInitialization *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebAppDiagnosticsObjectInitialization *This);

    /*** IWebAppDiagnosticsObjectInitialization methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWebAppDiagnosticsObjectInitialization *This,
        HANDLE_PTR hPassedHandle,
        IUnknown *pDebugApplication);

    END_INTERFACE
} IWebAppDiagnosticsObjectInitializationVtbl;

interface IWebAppDiagnosticsObjectInitialization {
    CONST_VTBL IWebAppDiagnosticsObjectInitializationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebAppDiagnosticsObjectInitialization_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebAppDiagnosticsObjectInitialization_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebAppDiagnosticsObjectInitialization_Release(This) (This)->lpVtbl->Release(This)
/*** IWebAppDiagnosticsObjectInitialization methods ***/
#define IWebAppDiagnosticsObjectInitialization_Initialize(This,hPassedHandle,pDebugApplication) (This)->lpVtbl->Initialize(This,hPassedHandle,pDebugApplication)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IWebAppDiagnosticsObjectInitialization_QueryInterface(IWebAppDiagnosticsObjectInitialization* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IWebAppDiagnosticsObjectInitialization_AddRef(IWebAppDiagnosticsObjectInitialization* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IWebAppDiagnosticsObjectInitialization_Release(IWebAppDiagnosticsObjectInitialization* This) {
    return This->lpVtbl->Release(This);
}
/*** IWebAppDiagnosticsObjectInitialization methods ***/
static __WIDL_INLINE HRESULT IWebAppDiagnosticsObjectInitialization_Initialize(IWebAppDiagnosticsObjectInitialization* This,HANDLE_PTR hPassedHandle,IUnknown *pDebugApplication) {
    return This->lpVtbl->Initialize(This,hPassedHandle,pDebugApplication);
}
#endif
#endif

#endif


#endif  /* __IWebAppDiagnosticsObjectInitialization_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptWinRTErrorDebug interface
 */
#ifndef __IActiveScriptWinRTErrorDebug_INTERFACE_DEFINED__
#define __IActiveScriptWinRTErrorDebug_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptWinRTErrorDebug, 0x73a3f82a, 0x0fe9, 0x4b33, 0xba,0x3b, 0xfe,0x09,0x5f,0x69,0x7e,0x0a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("73a3f82a-0fe9-4b33-ba3b-fe095f697e0a")
IActiveScriptWinRTErrorDebug : public IActiveScriptError
{
    virtual HRESULT STDMETHODCALLTYPE GetRestrictedErrorString(
        BSTR *errorString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestrictedErrorReference(
        BSTR *referenceString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCapabilitySid(
        BSTR *capabilitySid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptWinRTErrorDebug, 0x73a3f82a, 0x0fe9, 0x4b33, 0xba,0x3b, 0xfe,0x09,0x5f,0x69,0x7e,0x0a)
#endif
#else
typedef struct IActiveScriptWinRTErrorDebugVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptWinRTErrorDebug *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptWinRTErrorDebug *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptWinRTErrorDebug *This);

    /*** IActiveScriptError methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExceptionInfo)(
        IActiveScriptWinRTErrorDebug *This,
        EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *GetSourcePosition)(
        IActiveScriptWinRTErrorDebug *This,
        DWORD *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition);

    HRESULT (STDMETHODCALLTYPE *GetSourceLineText)(
        IActiveScriptWinRTErrorDebug *This,
        BSTR *pbstrSourceLine);

    /*** IActiveScriptWinRTErrorDebug methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRestrictedErrorString)(
        IActiveScriptWinRTErrorDebug *This,
        BSTR *errorString);

    HRESULT (STDMETHODCALLTYPE *GetRestrictedErrorReference)(
        IActiveScriptWinRTErrorDebug *This,
        BSTR *referenceString);

    HRESULT (STDMETHODCALLTYPE *GetCapabilitySid)(
        IActiveScriptWinRTErrorDebug *This,
        BSTR *capabilitySid);

    END_INTERFACE
} IActiveScriptWinRTErrorDebugVtbl;

interface IActiveScriptWinRTErrorDebug {
    CONST_VTBL IActiveScriptWinRTErrorDebugVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptWinRTErrorDebug_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptWinRTErrorDebug_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptWinRTErrorDebug_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptError methods ***/
#define IActiveScriptWinRTErrorDebug_GetExceptionInfo(This,pexcepinfo) (This)->lpVtbl->GetExceptionInfo(This,pexcepinfo)
#define IActiveScriptWinRTErrorDebug_GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition) (This)->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition)
#define IActiveScriptWinRTErrorDebug_GetSourceLineText(This,pbstrSourceLine) (This)->lpVtbl->GetSourceLineText(This,pbstrSourceLine)
/*** IActiveScriptWinRTErrorDebug methods ***/
#define IActiveScriptWinRTErrorDebug_GetRestrictedErrorString(This,errorString) (This)->lpVtbl->GetRestrictedErrorString(This,errorString)
#define IActiveScriptWinRTErrorDebug_GetRestrictedErrorReference(This,referenceString) (This)->lpVtbl->GetRestrictedErrorReference(This,referenceString)
#define IActiveScriptWinRTErrorDebug_GetCapabilitySid(This,capabilitySid) (This)->lpVtbl->GetCapabilitySid(This,capabilitySid)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_QueryInterface(IActiveScriptWinRTErrorDebug* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IActiveScriptWinRTErrorDebug_AddRef(IActiveScriptWinRTErrorDebug* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IActiveScriptWinRTErrorDebug_Release(IActiveScriptWinRTErrorDebug* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptError methods ***/
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_GetExceptionInfo(IActiveScriptWinRTErrorDebug* This,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->GetExceptionInfo(This,pexcepinfo);
}
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_GetSourcePosition(IActiveScriptWinRTErrorDebug* This,DWORD *pdwSourceContext,ULONG *pulLineNumber,LONG *plCharacterPosition) {
    return This->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition);
}
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_GetSourceLineText(IActiveScriptWinRTErrorDebug* This,BSTR *pbstrSourceLine) {
    return This->lpVtbl->GetSourceLineText(This,pbstrSourceLine);
}
/*** IActiveScriptWinRTErrorDebug methods ***/
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_GetRestrictedErrorString(IActiveScriptWinRTErrorDebug* This,BSTR *errorString) {
    return This->lpVtbl->GetRestrictedErrorString(This,errorString);
}
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_GetRestrictedErrorReference(IActiveScriptWinRTErrorDebug* This,BSTR *referenceString) {
    return This->lpVtbl->GetRestrictedErrorReference(This,referenceString);
}
static __WIDL_INLINE HRESULT IActiveScriptWinRTErrorDebug_GetCapabilitySid(IActiveScriptWinRTErrorDebug* This,BSTR *capabilitySid) {
    return This->lpVtbl->GetCapabilitySid(This,capabilitySid);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptWinRTErrorDebug_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptErrorDebug110 interface
 */
#ifndef __IActiveScriptErrorDebug110_INTERFACE_DEFINED__
#define __IActiveScriptErrorDebug110_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptErrorDebug110, 0x516e42b6, 0x89a8, 0x4530, 0x93,0x7b, 0x5f,0x07,0x08,0x43,0x14,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("516e42b6-89a8-4530-937b-5f0708431442")
IActiveScriptErrorDebug110 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetExceptionThrownKind(
        SCRIPT_ERROR_DEBUG_EXCEPTION_THROWN_KIND *pExceptionKind) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptErrorDebug110, 0x516e42b6, 0x89a8, 0x4530, 0x93,0x7b, 0x5f,0x07,0x08,0x43,0x14,0x42)
#endif
#else
typedef struct IActiveScriptErrorDebug110Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptErrorDebug110 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptErrorDebug110 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptErrorDebug110 *This);

    /*** IActiveScriptErrorDebug110 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExceptionThrownKind)(
        IActiveScriptErrorDebug110 *This,
        SCRIPT_ERROR_DEBUG_EXCEPTION_THROWN_KIND *pExceptionKind);

    END_INTERFACE
} IActiveScriptErrorDebug110Vtbl;

interface IActiveScriptErrorDebug110 {
    CONST_VTBL IActiveScriptErrorDebug110Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptErrorDebug110_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptErrorDebug110_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptErrorDebug110_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptErrorDebug110 methods ***/
#define IActiveScriptErrorDebug110_GetExceptionThrownKind(This,pExceptionKind) (This)->lpVtbl->GetExceptionThrownKind(This,pExceptionKind)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IActiveScriptErrorDebug110_QueryInterface(IActiveScriptErrorDebug110* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IActiveScriptErrorDebug110_AddRef(IActiveScriptErrorDebug110* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IActiveScriptErrorDebug110_Release(IActiveScriptErrorDebug110* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptErrorDebug110 methods ***/
static __WIDL_INLINE HRESULT IActiveScriptErrorDebug110_GetExceptionThrownKind(IActiveScriptErrorDebug110* This,SCRIPT_ERROR_DEBUG_EXCEPTION_THROWN_KIND *pExceptionKind) {
    return This->lpVtbl->GetExceptionThrownKind(This,pExceptionKind);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptErrorDebug110_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationThreadEvents110 interface
 */
#ifndef __IDebugApplicationThreadEvents110_INTERFACE_DEFINED__
#define __IDebugApplicationThreadEvents110_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationThreadEvents110, 0x84e5e468, 0xd5da, 0x48a8, 0x83,0xf4, 0x40,0x36,0x64,0x29,0x00,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("84e5e468-d5da-48a8-83f4-40366429007b")
IDebugApplicationThreadEvents110 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnSuspendForBreakPoint(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnResumeFromBreakPoint(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnThreadRequestComplete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnBeginThreadRequest(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationThreadEvents110, 0x84e5e468, 0xd5da, 0x48a8, 0x83,0xf4, 0x40,0x36,0x64,0x29,0x00,0x7b)
#endif
#else
typedef struct IDebugApplicationThreadEvents110Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationThreadEvents110 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationThreadEvents110 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationThreadEvents110 *This);

    /*** IDebugApplicationThreadEvents110 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSuspendForBreakPoint)(
        IDebugApplicationThreadEvents110 *This);

    HRESULT (STDMETHODCALLTYPE *OnResumeFromBreakPoint)(
        IDebugApplicationThreadEvents110 *This);

    HRESULT (STDMETHODCALLTYPE *OnThreadRequestComplete)(
        IDebugApplicationThreadEvents110 *This);

    HRESULT (STDMETHODCALLTYPE *OnBeginThreadRequest)(
        IDebugApplicationThreadEvents110 *This);

    END_INTERFACE
} IDebugApplicationThreadEvents110Vtbl;

interface IDebugApplicationThreadEvents110 {
    CONST_VTBL IDebugApplicationThreadEvents110Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationThreadEvents110_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationThreadEvents110_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationThreadEvents110_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugApplicationThreadEvents110 methods ***/
#define IDebugApplicationThreadEvents110_OnSuspendForBreakPoint(This) (This)->lpVtbl->OnSuspendForBreakPoint(This)
#define IDebugApplicationThreadEvents110_OnResumeFromBreakPoint(This) (This)->lpVtbl->OnResumeFromBreakPoint(This)
#define IDebugApplicationThreadEvents110_OnThreadRequestComplete(This) (This)->lpVtbl->OnThreadRequestComplete(This)
#define IDebugApplicationThreadEvents110_OnBeginThreadRequest(This) (This)->lpVtbl->OnBeginThreadRequest(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationThreadEvents110_QueryInterface(IDebugApplicationThreadEvents110* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDebugApplicationThreadEvents110_AddRef(IDebugApplicationThreadEvents110* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDebugApplicationThreadEvents110_Release(IDebugApplicationThreadEvents110* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugApplicationThreadEvents110 methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationThreadEvents110_OnSuspendForBreakPoint(IDebugApplicationThreadEvents110* This) {
    return This->lpVtbl->OnSuspendForBreakPoint(This);
}
static __WIDL_INLINE HRESULT IDebugApplicationThreadEvents110_OnResumeFromBreakPoint(IDebugApplicationThreadEvents110* This) {
    return This->lpVtbl->OnResumeFromBreakPoint(This);
}
static __WIDL_INLINE HRESULT IDebugApplicationThreadEvents110_OnThreadRequestComplete(IDebugApplicationThreadEvents110* This) {
    return This->lpVtbl->OnThreadRequestComplete(This);
}
static __WIDL_INLINE HRESULT IDebugApplicationThreadEvents110_OnBeginThreadRequest(IDebugApplicationThreadEvents110* This) {
    return This->lpVtbl->OnBeginThreadRequest(This);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationThreadEvents110_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationThread11032 interface
 */
#ifndef __IDebugApplicationThread11032_INTERFACE_DEFINED__
#define __IDebugApplicationThread11032_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationThread11032, 0x2194ac5c, 0x6561, 0x404a, 0xa2,0xe9, 0xf5,0x7d,0x72,0xde,0x37,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2194ac5c-6561-404a-a2e9-f57d72de3702")
IDebugApplicationThread11032 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetActiveThreadRequestCount(
        UINT *puiThreadRequests) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSuspendedForBreakPoint(
        WINBOOL *pfIsSuspended) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsThreadCallable(
        WINBOOL *pfIsCallable) = 0;

    virtual HRESULT STDMETHODCALLTYPE AsynchronousCallIntoThread(
        IDebugThreadCall32 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationThread11032, 0x2194ac5c, 0x6561, 0x404a, 0xa2,0xe9, 0xf5,0x7d,0x72,0xde,0x37,0x02)
#endif
#else
typedef struct IDebugApplicationThread11032Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationThread11032 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationThread11032 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationThread11032 *This);

    /*** IDebugApplicationThread11032 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetActiveThreadRequestCount)(
        IDebugApplicationThread11032 *This,
        UINT *puiThreadRequests);

    HRESULT (STDMETHODCALLTYPE *IsSuspendedForBreakPoint)(
        IDebugApplicationThread11032 *This,
        WINBOOL *pfIsSuspended);

    HRESULT (STDMETHODCALLTYPE *IsThreadCallable)(
        IDebugApplicationThread11032 *This,
        WINBOOL *pfIsCallable);

    HRESULT (STDMETHODCALLTYPE *AsynchronousCallIntoThread)(
        IDebugApplicationThread11032 *This,
        IDebugThreadCall32 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3);

    END_INTERFACE
} IDebugApplicationThread11032Vtbl;

interface IDebugApplicationThread11032 {
    CONST_VTBL IDebugApplicationThread11032Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationThread11032_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationThread11032_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationThread11032_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugApplicationThread11032 methods ***/
#define IDebugApplicationThread11032_GetActiveThreadRequestCount(This,puiThreadRequests) (This)->lpVtbl->GetActiveThreadRequestCount(This,puiThreadRequests)
#define IDebugApplicationThread11032_IsSuspendedForBreakPoint(This,pfIsSuspended) (This)->lpVtbl->IsSuspendedForBreakPoint(This,pfIsSuspended)
#define IDebugApplicationThread11032_IsThreadCallable(This,pfIsCallable) (This)->lpVtbl->IsThreadCallable(This,pfIsCallable)
#define IDebugApplicationThread11032_AsynchronousCallIntoThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->AsynchronousCallIntoThread(This,pptc,dwParam1,dwParam2,dwParam3)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationThread11032_QueryInterface(IDebugApplicationThread11032* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDebugApplicationThread11032_AddRef(IDebugApplicationThread11032* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDebugApplicationThread11032_Release(IDebugApplicationThread11032* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugApplicationThread11032 methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationThread11032_GetActiveThreadRequestCount(IDebugApplicationThread11032* This,UINT *puiThreadRequests) {
    return This->lpVtbl->GetActiveThreadRequestCount(This,puiThreadRequests);
}
static __WIDL_INLINE HRESULT IDebugApplicationThread11032_IsSuspendedForBreakPoint(IDebugApplicationThread11032* This,WINBOOL *pfIsSuspended) {
    return This->lpVtbl->IsSuspendedForBreakPoint(This,pfIsSuspended);
}
static __WIDL_INLINE HRESULT IDebugApplicationThread11032_IsThreadCallable(IDebugApplicationThread11032* This,WINBOOL *pfIsCallable) {
    return This->lpVtbl->IsThreadCallable(This,pfIsCallable);
}
static __WIDL_INLINE HRESULT IDebugApplicationThread11032_AsynchronousCallIntoThread(IDebugApplicationThread11032* This,IDebugThreadCall32 *pptc,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3) {
    return This->lpVtbl->AsynchronousCallIntoThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationThread11032_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationThread11064 interface
 */
#ifndef __IDebugApplicationThread11064_INTERFACE_DEFINED__
#define __IDebugApplicationThread11064_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationThread11064, 0x420aa4cc, 0xefd8, 0x4dac, 0x98,0x3b, 0x47,0x12,0x78,0x26,0x91,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("420aa4cc-efd8-4dac-983b-47127826917d")
IDebugApplicationThread11064 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetActiveThreadRequestCount(
        UINT *puiThreadRequests) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSuspendedForBreakPoint(
        WINBOOL *pfIsSuspended) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsThreadCallable(
        WINBOOL *pfIsCallable) = 0;

    virtual HRESULT STDMETHODCALLTYPE AsynchronousCallIntoThread(
        IDebugThreadCall64 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationThread11064, 0x420aa4cc, 0xefd8, 0x4dac, 0x98,0x3b, 0x47,0x12,0x78,0x26,0x91,0x7d)
#endif
#else
typedef struct IDebugApplicationThread11064Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationThread11064 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationThread11064 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationThread11064 *This);

    /*** IDebugApplicationThread11064 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetActiveThreadRequestCount)(
        IDebugApplicationThread11064 *This,
        UINT *puiThreadRequests);

    HRESULT (STDMETHODCALLTYPE *IsSuspendedForBreakPoint)(
        IDebugApplicationThread11064 *This,
        WINBOOL *pfIsSuspended);

    HRESULT (STDMETHODCALLTYPE *IsThreadCallable)(
        IDebugApplicationThread11064 *This,
        WINBOOL *pfIsCallable);

    HRESULT (STDMETHODCALLTYPE *AsynchronousCallIntoThread)(
        IDebugApplicationThread11064 *This,
        IDebugThreadCall64 *pptc,
        DWORD_PTR dwParam1,
        DWORD_PTR dwParam2,
        DWORD_PTR dwParam3);

    END_INTERFACE
} IDebugApplicationThread11064Vtbl;

interface IDebugApplicationThread11064 {
    CONST_VTBL IDebugApplicationThread11064Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationThread11064_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationThread11064_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationThread11064_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugApplicationThread11064 methods ***/
#define IDebugApplicationThread11064_GetActiveThreadRequestCount(This,puiThreadRequests) (This)->lpVtbl->GetActiveThreadRequestCount(This,puiThreadRequests)
#define IDebugApplicationThread11064_IsSuspendedForBreakPoint(This,pfIsSuspended) (This)->lpVtbl->IsSuspendedForBreakPoint(This,pfIsSuspended)
#define IDebugApplicationThread11064_IsThreadCallable(This,pfIsCallable) (This)->lpVtbl->IsThreadCallable(This,pfIsCallable)
#define IDebugApplicationThread11064_AsynchronousCallIntoThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->AsynchronousCallIntoThread(This,pptc,dwParam1,dwParam2,dwParam3)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationThread11064_QueryInterface(IDebugApplicationThread11064* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDebugApplicationThread11064_AddRef(IDebugApplicationThread11064* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDebugApplicationThread11064_Release(IDebugApplicationThread11064* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugApplicationThread11064 methods ***/
static __WIDL_INLINE HRESULT IDebugApplicationThread11064_GetActiveThreadRequestCount(IDebugApplicationThread11064* This,UINT *puiThreadRequests) {
    return This->lpVtbl->GetActiveThreadRequestCount(This,puiThreadRequests);
}
static __WIDL_INLINE HRESULT IDebugApplicationThread11064_IsSuspendedForBreakPoint(IDebugApplicationThread11064* This,WINBOOL *pfIsSuspended) {
    return This->lpVtbl->IsSuspendedForBreakPoint(This,pfIsSuspended);
}
static __WIDL_INLINE HRESULT IDebugApplicationThread11064_IsThreadCallable(IDebugApplicationThread11064* This,WINBOOL *pfIsCallable) {
    return This->lpVtbl->IsThreadCallable(This,pfIsCallable);
}
static __WIDL_INLINE HRESULT IDebugApplicationThread11064_AsynchronousCallIntoThread(IDebugApplicationThread11064* This,IDebugThreadCall64 *pptc,DWORD_PTR dwParam1,DWORD_PTR dwParam2,DWORD_PTR dwParam3) {
    return This->lpVtbl->AsynchronousCallIntoThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationThread11064_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRemoteDebugCriticalErrorEvent110 interface
 */
#ifndef __IRemoteDebugCriticalErrorEvent110_INTERFACE_DEFINED__
#define __IRemoteDebugCriticalErrorEvent110_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRemoteDebugCriticalErrorEvent110, 0x2f69c611, 0x6b14, 0x47e8, 0x92,0x60, 0x4b,0xb7,0xc5,0x2f,0x50,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2f69c611-6b14-47e8-9260-4bb7c52f504b")
IRemoteDebugCriticalErrorEvent110 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetErrorInfo(
        BSTR *pbstrSource,
        int *pMessageId,
        BSTR *pbstrMessage,
        IDebugDocumentContext **ppLocation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRemoteDebugCriticalErrorEvent110, 0x2f69c611, 0x6b14, 0x47e8, 0x92,0x60, 0x4b,0xb7,0xc5,0x2f,0x50,0x4b)
#endif
#else
typedef struct IRemoteDebugCriticalErrorEvent110Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRemoteDebugCriticalErrorEvent110 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRemoteDebugCriticalErrorEvent110 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRemoteDebugCriticalErrorEvent110 *This);

    /*** IRemoteDebugCriticalErrorEvent110 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetErrorInfo)(
        IRemoteDebugCriticalErrorEvent110 *This,
        BSTR *pbstrSource,
        int *pMessageId,
        BSTR *pbstrMessage,
        IDebugDocumentContext **ppLocation);

    END_INTERFACE
} IRemoteDebugCriticalErrorEvent110Vtbl;

interface IRemoteDebugCriticalErrorEvent110 {
    CONST_VTBL IRemoteDebugCriticalErrorEvent110Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRemoteDebugCriticalErrorEvent110_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRemoteDebugCriticalErrorEvent110_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRemoteDebugCriticalErrorEvent110_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugCriticalErrorEvent110 methods ***/
#define IRemoteDebugCriticalErrorEvent110_GetErrorInfo(This,pbstrSource,pMessageId,pbstrMessage,ppLocation) (This)->lpVtbl->GetErrorInfo(This,pbstrSource,pMessageId,pbstrMessage,ppLocation)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IRemoteDebugCriticalErrorEvent110_QueryInterface(IRemoteDebugCriticalErrorEvent110* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IRemoteDebugCriticalErrorEvent110_AddRef(IRemoteDebugCriticalErrorEvent110* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IRemoteDebugCriticalErrorEvent110_Release(IRemoteDebugCriticalErrorEvent110* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugCriticalErrorEvent110 methods ***/
static __WIDL_INLINE HRESULT IRemoteDebugCriticalErrorEvent110_GetErrorInfo(IRemoteDebugCriticalErrorEvent110* This,BSTR *pbstrSource,int *pMessageId,BSTR *pbstrMessage,IDebugDocumentContext **ppLocation) {
    return This->lpVtbl->GetErrorInfo(This,pbstrSource,pMessageId,pbstrMessage,ppLocation);
}
#endif
#endif

#endif


#endif  /* __IRemoteDebugCriticalErrorEvent110_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __activdbg100_h__ */
