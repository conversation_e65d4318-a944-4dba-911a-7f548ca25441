/*** Autogenerated by WIDL 8.5 from include/amstream.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __amstream_h__
#define __amstream_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __IDirectShowStream_FWD_DEFINED__
#define __IDirectShowStream_FWD_DEFINED__
typedef interface IDirectShowStream IDirectShowStream;
#ifdef __cplusplus
interface IDirectShowStream;
#endif /* __cplusplus */
#endif

#ifndef __IAMMultiMediaStream_FWD_DEFINED__
#define __IAMMultiMediaStream_FWD_DEFINED__
typedef interface IAMMultiMediaStream IAMMultiMediaStream;
#ifdef __cplusplus
interface IAMMultiMediaStream;
#endif /* __cplusplus */
#endif

#ifndef __IAMMediaStream_FWD_DEFINED__
#define __IAMMediaStream_FWD_DEFINED__
typedef interface IAMMediaStream IAMMediaStream;
#ifdef __cplusplus
interface IAMMediaStream;
#endif /* __cplusplus */
#endif

#ifndef __IMediaStreamFilter_FWD_DEFINED__
#define __IMediaStreamFilter_FWD_DEFINED__
typedef interface IMediaStreamFilter IMediaStreamFilter;
#ifdef __cplusplus
interface IMediaStreamFilter;
#endif /* __cplusplus */
#endif

#ifndef __IDirectDrawMediaSampleAllocator_FWD_DEFINED__
#define __IDirectDrawMediaSampleAllocator_FWD_DEFINED__
typedef interface IDirectDrawMediaSampleAllocator IDirectDrawMediaSampleAllocator;
#ifdef __cplusplus
interface IDirectDrawMediaSampleAllocator;
#endif /* __cplusplus */
#endif

#ifndef __IDirectDrawMediaSample_FWD_DEFINED__
#define __IDirectDrawMediaSample_FWD_DEFINED__
typedef interface IDirectDrawMediaSample IDirectDrawMediaSample;
#ifdef __cplusplus
interface IDirectDrawMediaSample;
#endif /* __cplusplus */
#endif

#ifndef __IAMMediaTypeStream_FWD_DEFINED__
#define __IAMMediaTypeStream_FWD_DEFINED__
typedef interface IAMMediaTypeStream IAMMediaTypeStream;
#ifdef __cplusplus
interface IAMMediaTypeStream;
#endif /* __cplusplus */
#endif

#ifndef __IAMMediaTypeSample_FWD_DEFINED__
#define __IAMMediaTypeSample_FWD_DEFINED__
typedef interface IAMMediaTypeSample IAMMediaTypeSample;
#ifdef __cplusplus
interface IAMMediaTypeSample;
#endif /* __cplusplus */
#endif

#ifndef __AMMultiMediaStream_FWD_DEFINED__
#define __AMMultiMediaStream_FWD_DEFINED__
#ifdef __cplusplus
typedef class AMMultiMediaStream AMMultiMediaStream;
#else
typedef struct AMMultiMediaStream AMMultiMediaStream;
#endif /* defined __cplusplus */
#endif /* defined __AMMultiMediaStream_FWD_DEFINED__ */

/* Headers for imported files */

#include <unknwn.h>
#include <mmstream.h>
#include <strmif.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <ddraw.h>
#include <mmsystem.h>
#include <mmstream.h>
#include <ddstream.h>
#include <austream.h>
#if 0
#ifndef __IDirectDraw_FWD_DEFINED__
#define __IDirectDraw_FWD_DEFINED__
typedef interface IDirectDraw IDirectDraw;
#ifdef __cplusplus
interface IDirectDraw;
#endif /* __cplusplus */
#endif

#ifndef __IDirectDrawSurface_FWD_DEFINED__
#define __IDirectDrawSurface_FWD_DEFINED__
typedef interface IDirectDrawSurface IDirectDrawSurface;
#ifdef __cplusplus
interface IDirectDrawSurface;
#endif /* __cplusplus */
#endif

#endif
#ifndef __IAMMultiMediaStream_FWD_DEFINED__
#define __IAMMultiMediaStream_FWD_DEFINED__
typedef interface IAMMultiMediaStream IAMMultiMediaStream;
#ifdef __cplusplus
interface IAMMultiMediaStream;
#endif /* __cplusplus */
#endif

#ifndef __IAMMediaStream_FWD_DEFINED__
#define __IAMMediaStream_FWD_DEFINED__
typedef interface IAMMediaStream IAMMediaStream;
#ifdef __cplusplus
interface IAMMediaStream;
#endif /* __cplusplus */
#endif

#ifndef __IMediaStreamFilter_FWD_DEFINED__
#define __IMediaStreamFilter_FWD_DEFINED__
typedef interface IMediaStreamFilter IMediaStreamFilter;
#ifdef __cplusplus
interface IMediaStreamFilter;
#endif /* __cplusplus */
#endif

#ifndef __IAMMediaTypeStream_FWD_DEFINED__
#define __IAMMediaTypeStream_FWD_DEFINED__
typedef interface IAMMediaTypeStream IAMMediaTypeStream;
#ifdef __cplusplus
interface IAMMediaTypeStream;
#endif /* __cplusplus */
#endif

#ifndef __IAMMediaTypeSample_FWD_DEFINED__
#define __IAMMediaTypeSample_FWD_DEFINED__
typedef interface IAMMediaTypeSample IAMMediaTypeSample;
#ifdef __cplusplus
interface IAMMediaTypeSample;
#endif /* __cplusplus */
#endif

enum {
    AMMSF_NOGRAPHTHREAD = 0x1
};
enum {
    AMMSF_ADDDEFAULTRENDERER = 0x1,
    AMMSF_CREATEPEER = 0x2,
    AMMSF_STOPIFNOSAMPLES = 0x4,
    AMMSF_NOSTALL = 0x8
};
enum {
    AMMSF_RENDERTYPEMASK = 0x3,
    AMMSF_RENDERTOEXISTING = 0x0,
    AMMSF_RENDERALLSTREAMS = 0x1,
    AMMSF_NORENDER = 0x2,
    AMMSF_NOCLOCK = 0x4,
    AMMSF_RUN = 0x8
};
typedef enum __WIDL_amstream_generated_name_00000019 {
    Disabled = 0,
    ReadData = 1,
    RenderData = 2
} OUTPUT_STATE;
/*****************************************************************************
 * IDirectShowStream interface
 */
#ifndef __IDirectShowStream_INTERFACE_DEFINED__
#define __IDirectShowStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectShowStream, 0x7db01c96, 0xc0c3, 0x11d0, 0x8f,0xf1, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7db01c96-c0c3-11d0-8ff1-00c04fd9189d")
IDirectShowStream : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_FileName(
        BSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FileName(
        BSTR newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Video(
        OUTPUT_STATE *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Video(
        OUTPUT_STATE newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Audio(
        OUTPUT_STATE *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Audio(
        OUTPUT_STATE newVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectShowStream, 0x7db01c96, 0xc0c3, 0x11d0, 0x8f,0xf1, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IDirectShowStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectShowStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectShowStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectShowStream *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IDirectShowStream *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDirectShowStream *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IDirectShowStream *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IDirectShowStream *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IDirectShowStream methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FileName)(
        IDirectShowStream *This,
        BSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *put_FileName)(
        IDirectShowStream *This,
        BSTR newVal);

    HRESULT (STDMETHODCALLTYPE *get_Video)(
        IDirectShowStream *This,
        OUTPUT_STATE *pVal);

    HRESULT (STDMETHODCALLTYPE *put_Video)(
        IDirectShowStream *This,
        OUTPUT_STATE newVal);

    HRESULT (STDMETHODCALLTYPE *get_Audio)(
        IDirectShowStream *This,
        OUTPUT_STATE *pVal);

    HRESULT (STDMETHODCALLTYPE *put_Audio)(
        IDirectShowStream *This,
        OUTPUT_STATE newVal);

    END_INTERFACE
} IDirectShowStreamVtbl;

interface IDirectShowStream {
    CONST_VTBL IDirectShowStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectShowStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectShowStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectShowStream_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IDirectShowStream_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IDirectShowStream_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IDirectShowStream_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IDirectShowStream_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IDirectShowStream methods ***/
#define IDirectShowStream_get_FileName(This,pVal) (This)->lpVtbl->get_FileName(This,pVal)
#define IDirectShowStream_put_FileName(This,newVal) (This)->lpVtbl->put_FileName(This,newVal)
#define IDirectShowStream_get_Video(This,pVal) (This)->lpVtbl->get_Video(This,pVal)
#define IDirectShowStream_put_Video(This,newVal) (This)->lpVtbl->put_Video(This,newVal)
#define IDirectShowStream_get_Audio(This,pVal) (This)->lpVtbl->get_Audio(This,pVal)
#define IDirectShowStream_put_Audio(This,newVal) (This)->lpVtbl->put_Audio(This,newVal)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDirectShowStream_QueryInterface(IDirectShowStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDirectShowStream_AddRef(IDirectShowStream* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDirectShowStream_Release(IDirectShowStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static __WIDL_INLINE HRESULT IDirectShowStream_GetTypeInfoCount(IDirectShowStream* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static __WIDL_INLINE HRESULT IDirectShowStream_GetTypeInfo(IDirectShowStream* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static __WIDL_INLINE HRESULT IDirectShowStream_GetIDsOfNames(IDirectShowStream* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static __WIDL_INLINE HRESULT IDirectShowStream_Invoke(IDirectShowStream* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IDirectShowStream methods ***/
static __WIDL_INLINE HRESULT IDirectShowStream_get_FileName(IDirectShowStream* This,BSTR *pVal) {
    return This->lpVtbl->get_FileName(This,pVal);
}
static __WIDL_INLINE HRESULT IDirectShowStream_put_FileName(IDirectShowStream* This,BSTR newVal) {
    return This->lpVtbl->put_FileName(This,newVal);
}
static __WIDL_INLINE HRESULT IDirectShowStream_get_Video(IDirectShowStream* This,OUTPUT_STATE *pVal) {
    return This->lpVtbl->get_Video(This,pVal);
}
static __WIDL_INLINE HRESULT IDirectShowStream_put_Video(IDirectShowStream* This,OUTPUT_STATE newVal) {
    return This->lpVtbl->put_Video(This,newVal);
}
static __WIDL_INLINE HRESULT IDirectShowStream_get_Audio(IDirectShowStream* This,OUTPUT_STATE *pVal) {
    return This->lpVtbl->get_Audio(This,pVal);
}
static __WIDL_INLINE HRESULT IDirectShowStream_put_Audio(IDirectShowStream* This,OUTPUT_STATE newVal) {
    return This->lpVtbl->put_Audio(This,newVal);
}
#endif
#endif

#endif


#endif  /* __IDirectShowStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMMultiMediaStream interface
 */
#ifndef __IAMMultiMediaStream_INTERFACE_DEFINED__
#define __IAMMultiMediaStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMMultiMediaStream, 0xbebe595c, 0x9a6f, 0x11d0, 0x8f,0xde, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bebe595c-9a6f-11d0-8fde-00c04fd9189d")
IAMMultiMediaStream : public IMultiMediaStream
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        STREAM_TYPE StreamType,
        DWORD dwFlags,
        IGraphBuilder *pFilterGraph) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFilterGraph(
        IGraphBuilder **ppGraphBuilder) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFilter(
        IMediaStreamFilter **ppFilter) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddMediaStream(
        IUnknown *pStreamObject,
        const MSPID *PurposeId,
        DWORD dwFlags,
        IMediaStream **ppNewStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenFile(
        LPCWSTR pszFileName,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenMoniker(
        IBindCtx *pCtx,
        IMoniker *pMoniker,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Render(
        DWORD dwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMMultiMediaStream, 0xbebe595c, 0x9a6f, 0x11d0, 0x8f,0xde, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IAMMultiMediaStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMMultiMediaStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMMultiMediaStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMMultiMediaStream *This);

    /*** IMultiMediaStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetInformation)(
        IAMMultiMediaStream *This,
        DWORD *pdwFlags,
        STREAM_TYPE *pStreamType);

    HRESULT (STDMETHODCALLTYPE *GetMediaStream)(
        IAMMultiMediaStream *This,
        REFMSPID idPurpose,
        IMediaStream **ppMediaStream);

    HRESULT (STDMETHODCALLTYPE *EnumMediaStreams)(
        IAMMultiMediaStream *This,
        LONG Index,
        IMediaStream **ppMediaStream);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IAMMultiMediaStream *This,
        STREAM_STATE *pCurrentState);

    HRESULT (STDMETHODCALLTYPE *SetState)(
        IAMMultiMediaStream *This,
        STREAM_STATE NewState);

    HRESULT (STDMETHODCALLTYPE *GetTime)(
        IAMMultiMediaStream *This,
        STREAM_TIME *pCurrentTime);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IAMMultiMediaStream *This,
        STREAM_TIME *pDuration);

    HRESULT (STDMETHODCALLTYPE *Seek)(
        IAMMultiMediaStream *This,
        STREAM_TIME SeekTime);

    HRESULT (STDMETHODCALLTYPE *GetEndOfStreamEventHandle)(
        IAMMultiMediaStream *This,
        HANDLE *phEOS);

    /*** IAMMultiMediaStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IAMMultiMediaStream *This,
        STREAM_TYPE StreamType,
        DWORD dwFlags,
        IGraphBuilder *pFilterGraph);

    HRESULT (STDMETHODCALLTYPE *GetFilterGraph)(
        IAMMultiMediaStream *This,
        IGraphBuilder **ppGraphBuilder);

    HRESULT (STDMETHODCALLTYPE *GetFilter)(
        IAMMultiMediaStream *This,
        IMediaStreamFilter **ppFilter);

    HRESULT (STDMETHODCALLTYPE *AddMediaStream)(
        IAMMultiMediaStream *This,
        IUnknown *pStreamObject,
        const MSPID *PurposeId,
        DWORD dwFlags,
        IMediaStream **ppNewStream);

    HRESULT (STDMETHODCALLTYPE *OpenFile)(
        IAMMultiMediaStream *This,
        LPCWSTR pszFileName,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *OpenMoniker)(
        IAMMultiMediaStream *This,
        IBindCtx *pCtx,
        IMoniker *pMoniker,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *Render)(
        IAMMultiMediaStream *This,
        DWORD dwFlags);

    END_INTERFACE
} IAMMultiMediaStreamVtbl;

interface IAMMultiMediaStream {
    CONST_VTBL IAMMultiMediaStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMMultiMediaStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMMultiMediaStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMMultiMediaStream_Release(This) (This)->lpVtbl->Release(This)
/*** IMultiMediaStream methods ***/
#define IAMMultiMediaStream_GetInformation(This,pdwFlags,pStreamType) (This)->lpVtbl->GetInformation(This,pdwFlags,pStreamType)
#define IAMMultiMediaStream_GetMediaStream(This,idPurpose,ppMediaStream) (This)->lpVtbl->GetMediaStream(This,idPurpose,ppMediaStream)
#define IAMMultiMediaStream_EnumMediaStreams(This,Index,ppMediaStream) (This)->lpVtbl->EnumMediaStreams(This,Index,ppMediaStream)
#define IAMMultiMediaStream_GetState(This,pCurrentState) (This)->lpVtbl->GetState(This,pCurrentState)
#define IAMMultiMediaStream_SetState(This,NewState) (This)->lpVtbl->SetState(This,NewState)
#define IAMMultiMediaStream_GetTime(This,pCurrentTime) (This)->lpVtbl->GetTime(This,pCurrentTime)
#define IAMMultiMediaStream_GetDuration(This,pDuration) (This)->lpVtbl->GetDuration(This,pDuration)
#define IAMMultiMediaStream_Seek(This,SeekTime) (This)->lpVtbl->Seek(This,SeekTime)
#define IAMMultiMediaStream_GetEndOfStreamEventHandle(This,phEOS) (This)->lpVtbl->GetEndOfStreamEventHandle(This,phEOS)
/*** IAMMultiMediaStream methods ***/
#define IAMMultiMediaStream_Initialize(This,StreamType,dwFlags,pFilterGraph) (This)->lpVtbl->Initialize(This,StreamType,dwFlags,pFilterGraph)
#define IAMMultiMediaStream_GetFilterGraph(This,ppGraphBuilder) (This)->lpVtbl->GetFilterGraph(This,ppGraphBuilder)
#define IAMMultiMediaStream_GetFilter(This,ppFilter) (This)->lpVtbl->GetFilter(This,ppFilter)
#define IAMMultiMediaStream_AddMediaStream(This,pStreamObject,PurposeId,dwFlags,ppNewStream) (This)->lpVtbl->AddMediaStream(This,pStreamObject,PurposeId,dwFlags,ppNewStream)
#define IAMMultiMediaStream_OpenFile(This,pszFileName,dwFlags) (This)->lpVtbl->OpenFile(This,pszFileName,dwFlags)
#define IAMMultiMediaStream_OpenMoniker(This,pCtx,pMoniker,dwFlags) (This)->lpVtbl->OpenMoniker(This,pCtx,pMoniker,dwFlags)
#define IAMMultiMediaStream_Render(This,dwFlags) (This)->lpVtbl->Render(This,dwFlags)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAMMultiMediaStream_QueryInterface(IAMMultiMediaStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAMMultiMediaStream_AddRef(IAMMultiMediaStream* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAMMultiMediaStream_Release(IAMMultiMediaStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IMultiMediaStream methods ***/
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetInformation(IAMMultiMediaStream* This,DWORD *pdwFlags,STREAM_TYPE *pStreamType) {
    return This->lpVtbl->GetInformation(This,pdwFlags,pStreamType);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetMediaStream(IAMMultiMediaStream* This,REFMSPID idPurpose,IMediaStream **ppMediaStream) {
    return This->lpVtbl->GetMediaStream(This,idPurpose,ppMediaStream);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_EnumMediaStreams(IAMMultiMediaStream* This,LONG Index,IMediaStream **ppMediaStream) {
    return This->lpVtbl->EnumMediaStreams(This,Index,ppMediaStream);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetState(IAMMultiMediaStream* This,STREAM_STATE *pCurrentState) {
    return This->lpVtbl->GetState(This,pCurrentState);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_SetState(IAMMultiMediaStream* This,STREAM_STATE NewState) {
    return This->lpVtbl->SetState(This,NewState);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetTime(IAMMultiMediaStream* This,STREAM_TIME *pCurrentTime) {
    return This->lpVtbl->GetTime(This,pCurrentTime);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetDuration(IAMMultiMediaStream* This,STREAM_TIME *pDuration) {
    return This->lpVtbl->GetDuration(This,pDuration);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_Seek(IAMMultiMediaStream* This,STREAM_TIME SeekTime) {
    return This->lpVtbl->Seek(This,SeekTime);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetEndOfStreamEventHandle(IAMMultiMediaStream* This,HANDLE *phEOS) {
    return This->lpVtbl->GetEndOfStreamEventHandle(This,phEOS);
}
/*** IAMMultiMediaStream methods ***/
static __WIDL_INLINE HRESULT IAMMultiMediaStream_Initialize(IAMMultiMediaStream* This,STREAM_TYPE StreamType,DWORD dwFlags,IGraphBuilder *pFilterGraph) {
    return This->lpVtbl->Initialize(This,StreamType,dwFlags,pFilterGraph);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetFilterGraph(IAMMultiMediaStream* This,IGraphBuilder **ppGraphBuilder) {
    return This->lpVtbl->GetFilterGraph(This,ppGraphBuilder);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_GetFilter(IAMMultiMediaStream* This,IMediaStreamFilter **ppFilter) {
    return This->lpVtbl->GetFilter(This,ppFilter);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_AddMediaStream(IAMMultiMediaStream* This,IUnknown *pStreamObject,const MSPID *PurposeId,DWORD dwFlags,IMediaStream **ppNewStream) {
    return This->lpVtbl->AddMediaStream(This,pStreamObject,PurposeId,dwFlags,ppNewStream);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_OpenFile(IAMMultiMediaStream* This,LPCWSTR pszFileName,DWORD dwFlags) {
    return This->lpVtbl->OpenFile(This,pszFileName,dwFlags);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_OpenMoniker(IAMMultiMediaStream* This,IBindCtx *pCtx,IMoniker *pMoniker,DWORD dwFlags) {
    return This->lpVtbl->OpenMoniker(This,pCtx,pMoniker,dwFlags);
}
static __WIDL_INLINE HRESULT IAMMultiMediaStream_Render(IAMMultiMediaStream* This,DWORD dwFlags) {
    return This->lpVtbl->Render(This,dwFlags);
}
#endif
#endif

#endif


#endif  /* __IAMMultiMediaStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMMediaStream interface
 */
#ifndef __IAMMediaStream_INTERFACE_DEFINED__
#define __IAMMediaStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMMediaStream, 0xbebe595d, 0x9a6f, 0x11d0, 0x8f,0xde, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bebe595d-9a6f-11d0-8fde-00c04fd9189d")
IAMMediaStream : public IMediaStream
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IUnknown *pSourceObject,
        DWORD dwFlags,
        REFMSPID PurposeId,
        const STREAM_TYPE StreamType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetState(
        FILTER_STATE State) = 0;

    virtual HRESULT STDMETHODCALLTYPE JoinAMMultiMediaStream(
        IAMMultiMediaStream *pAMMultiMediaStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE JoinFilter(
        IMediaStreamFilter *pMediaStreamFilter) = 0;

    virtual HRESULT STDMETHODCALLTYPE JoinFilterGraph(
        IFilterGraph *pFilterGraph) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMMediaStream, 0xbebe595d, 0x9a6f, 0x11d0, 0x8f,0xde, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IAMMediaStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMMediaStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMMediaStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMMediaStream *This);

    /*** IMediaStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMultiMediaStream)(
        IAMMediaStream *This,
        IMultiMediaStream **ppMultiMediaStream);

    HRESULT (STDMETHODCALLTYPE *GetInformation)(
        IAMMediaStream *This,
        MSPID *pPurposeId,
        STREAM_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *SetSameFormat)(
        IAMMediaStream *This,
        IMediaStream *pStreamThatHasDesiredFormat,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *AllocateSample)(
        IAMMediaStream *This,
        DWORD dwFlags,
        IStreamSample **ppSample);

    HRESULT (STDMETHODCALLTYPE *CreateSharedSample)(
        IAMMediaStream *This,
        IStreamSample *pExistingSample,
        DWORD dwFlags,
        IStreamSample **ppNewSample);

    HRESULT (STDMETHODCALLTYPE *SendEndOfStream)(
        IAMMediaStream *This,
        DWORD dwFlags);

    /*** IAMMediaStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IAMMediaStream *This,
        IUnknown *pSourceObject,
        DWORD dwFlags,
        REFMSPID PurposeId,
        const STREAM_TYPE StreamType);

    HRESULT (STDMETHODCALLTYPE *SetState)(
        IAMMediaStream *This,
        FILTER_STATE State);

    HRESULT (STDMETHODCALLTYPE *JoinAMMultiMediaStream)(
        IAMMediaStream *This,
        IAMMultiMediaStream *pAMMultiMediaStream);

    HRESULT (STDMETHODCALLTYPE *JoinFilter)(
        IAMMediaStream *This,
        IMediaStreamFilter *pMediaStreamFilter);

    HRESULT (STDMETHODCALLTYPE *JoinFilterGraph)(
        IAMMediaStream *This,
        IFilterGraph *pFilterGraph);

    END_INTERFACE
} IAMMediaStreamVtbl;

interface IAMMediaStream {
    CONST_VTBL IAMMediaStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMMediaStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMMediaStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMMediaStream_Release(This) (This)->lpVtbl->Release(This)
/*** IMediaStream methods ***/
#define IAMMediaStream_GetMultiMediaStream(This,ppMultiMediaStream) (This)->lpVtbl->GetMultiMediaStream(This,ppMultiMediaStream)
#define IAMMediaStream_GetInformation(This,pPurposeId,pType) (This)->lpVtbl->GetInformation(This,pPurposeId,pType)
#define IAMMediaStream_SetSameFormat(This,pStreamThatHasDesiredFormat,dwFlags) (This)->lpVtbl->SetSameFormat(This,pStreamThatHasDesiredFormat,dwFlags)
#define IAMMediaStream_AllocateSample(This,dwFlags,ppSample) (This)->lpVtbl->AllocateSample(This,dwFlags,ppSample)
#define IAMMediaStream_CreateSharedSample(This,pExistingSample,dwFlags,ppNewSample) (This)->lpVtbl->CreateSharedSample(This,pExistingSample,dwFlags,ppNewSample)
#define IAMMediaStream_SendEndOfStream(This,dwFlags) (This)->lpVtbl->SendEndOfStream(This,dwFlags)
/*** IAMMediaStream methods ***/
#define IAMMediaStream_Initialize(This,pSourceObject,dwFlags,PurposeId,StreamType) (This)->lpVtbl->Initialize(This,pSourceObject,dwFlags,PurposeId,StreamType)
#define IAMMediaStream_SetState(This,State) (This)->lpVtbl->SetState(This,State)
#define IAMMediaStream_JoinAMMultiMediaStream(This,pAMMultiMediaStream) (This)->lpVtbl->JoinAMMultiMediaStream(This,pAMMultiMediaStream)
#define IAMMediaStream_JoinFilter(This,pMediaStreamFilter) (This)->lpVtbl->JoinFilter(This,pMediaStreamFilter)
#define IAMMediaStream_JoinFilterGraph(This,pFilterGraph) (This)->lpVtbl->JoinFilterGraph(This,pFilterGraph)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAMMediaStream_QueryInterface(IAMMediaStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAMMediaStream_AddRef(IAMMediaStream* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAMMediaStream_Release(IAMMediaStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IMediaStream methods ***/
static __WIDL_INLINE HRESULT IAMMediaStream_GetMultiMediaStream(IAMMediaStream* This,IMultiMediaStream **ppMultiMediaStream) {
    return This->lpVtbl->GetMultiMediaStream(This,ppMultiMediaStream);
}
static __WIDL_INLINE HRESULT IAMMediaStream_GetInformation(IAMMediaStream* This,MSPID *pPurposeId,STREAM_TYPE *pType) {
    return This->lpVtbl->GetInformation(This,pPurposeId,pType);
}
static __WIDL_INLINE HRESULT IAMMediaStream_SetSameFormat(IAMMediaStream* This,IMediaStream *pStreamThatHasDesiredFormat,DWORD dwFlags) {
    return This->lpVtbl->SetSameFormat(This,pStreamThatHasDesiredFormat,dwFlags);
}
static __WIDL_INLINE HRESULT IAMMediaStream_AllocateSample(IAMMediaStream* This,DWORD dwFlags,IStreamSample **ppSample) {
    return This->lpVtbl->AllocateSample(This,dwFlags,ppSample);
}
static __WIDL_INLINE HRESULT IAMMediaStream_CreateSharedSample(IAMMediaStream* This,IStreamSample *pExistingSample,DWORD dwFlags,IStreamSample **ppNewSample) {
    return This->lpVtbl->CreateSharedSample(This,pExistingSample,dwFlags,ppNewSample);
}
static __WIDL_INLINE HRESULT IAMMediaStream_SendEndOfStream(IAMMediaStream* This,DWORD dwFlags) {
    return This->lpVtbl->SendEndOfStream(This,dwFlags);
}
/*** IAMMediaStream methods ***/
static __WIDL_INLINE HRESULT IAMMediaStream_Initialize(IAMMediaStream* This,IUnknown *pSourceObject,DWORD dwFlags,REFMSPID PurposeId,const STREAM_TYPE StreamType) {
    return This->lpVtbl->Initialize(This,pSourceObject,dwFlags,PurposeId,StreamType);
}
static __WIDL_INLINE HRESULT IAMMediaStream_SetState(IAMMediaStream* This,FILTER_STATE State) {
    return This->lpVtbl->SetState(This,State);
}
static __WIDL_INLINE HRESULT IAMMediaStream_JoinAMMultiMediaStream(IAMMediaStream* This,IAMMultiMediaStream *pAMMultiMediaStream) {
    return This->lpVtbl->JoinAMMultiMediaStream(This,pAMMultiMediaStream);
}
static __WIDL_INLINE HRESULT IAMMediaStream_JoinFilter(IAMMediaStream* This,IMediaStreamFilter *pMediaStreamFilter) {
    return This->lpVtbl->JoinFilter(This,pMediaStreamFilter);
}
static __WIDL_INLINE HRESULT IAMMediaStream_JoinFilterGraph(IAMMediaStream* This,IFilterGraph *pFilterGraph) {
    return This->lpVtbl->JoinFilterGraph(This,pFilterGraph);
}
#endif
#endif

#endif


#endif  /* __IAMMediaStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMediaStreamFilter interface
 */
#ifndef __IMediaStreamFilter_INTERFACE_DEFINED__
#define __IMediaStreamFilter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMediaStreamFilter, 0xbebe595e, 0x9a6f, 0x11d0, 0x8f,0xde, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bebe595e-9a6f-11d0-8fde-00c04fd9189d")
IMediaStreamFilter : public IBaseFilter
{
    virtual HRESULT STDMETHODCALLTYPE AddMediaStream(
        IAMMediaStream *pAMMediaStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaStream(
        REFMSPID idPurpose,
        IMediaStream **ppMediaStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumMediaStreams(
        LONG Index,
        IMediaStream **ppMediaStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SupportSeeking(
        WINBOOL bRenderer) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReferenceTimeToStreamTime(
        REFERENCE_TIME *pTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentStreamTime(
        REFERENCE_TIME *pCurrentStreamTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitUntil(
        REFERENCE_TIME WaitStreamTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        WINBOOL bCancelEOS) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndOfStream(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMediaStreamFilter, 0xbebe595e, 0x9a6f, 0x11d0, 0x8f,0xde, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IMediaStreamFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMediaStreamFilter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMediaStreamFilter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMediaStreamFilter *This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IMediaStreamFilter *This,
        CLSID *pClassID);

    /*** IMediaFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMediaStreamFilter *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMediaStreamFilter *This);

    HRESULT (STDMETHODCALLTYPE *Run)(
        IMediaStreamFilter *This,
        REFERENCE_TIME tStart);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMediaStreamFilter *This,
        DWORD dwMilliSecsTimeout,
        FILTER_STATE *State);

    HRESULT (STDMETHODCALLTYPE *SetSyncSource)(
        IMediaStreamFilter *This,
        IReferenceClock *pClock);

    HRESULT (STDMETHODCALLTYPE *GetSyncSource)(
        IMediaStreamFilter *This,
        IReferenceClock **pClock);

    /*** IBaseFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumPins)(
        IMediaStreamFilter *This,
        IEnumPins **ppEnum);

    HRESULT (STDMETHODCALLTYPE *FindPin)(
        IMediaStreamFilter *This,
        LPCWSTR Id,
        IPin **ppPin);

    HRESULT (STDMETHODCALLTYPE *QueryFilterInfo)(
        IMediaStreamFilter *This,
        FILTER_INFO *pInfo);

    HRESULT (STDMETHODCALLTYPE *JoinFilterGraph)(
        IMediaStreamFilter *This,
        IFilterGraph *pGraph,
        LPCWSTR pName);

    HRESULT (STDMETHODCALLTYPE *QueryVendorInfo)(
        IMediaStreamFilter *This,
        LPWSTR *pVendorInfo);

    /*** IMediaStreamFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *AddMediaStream)(
        IMediaStreamFilter *This,
        IAMMediaStream *pAMMediaStream);

    HRESULT (STDMETHODCALLTYPE *GetMediaStream)(
        IMediaStreamFilter *This,
        REFMSPID idPurpose,
        IMediaStream **ppMediaStream);

    HRESULT (STDMETHODCALLTYPE *EnumMediaStreams)(
        IMediaStreamFilter *This,
        LONG Index,
        IMediaStream **ppMediaStream);

    HRESULT (STDMETHODCALLTYPE *SupportSeeking)(
        IMediaStreamFilter *This,
        WINBOOL bRenderer);

    HRESULT (STDMETHODCALLTYPE *ReferenceTimeToStreamTime)(
        IMediaStreamFilter *This,
        REFERENCE_TIME *pTime);

    HRESULT (STDMETHODCALLTYPE *GetCurrentStreamTime)(
        IMediaStreamFilter *This,
        REFERENCE_TIME *pCurrentStreamTime);

    HRESULT (STDMETHODCALLTYPE *WaitUntil)(
        IMediaStreamFilter *This,
        REFERENCE_TIME WaitStreamTime);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMediaStreamFilter *This,
        WINBOOL bCancelEOS);

    HRESULT (STDMETHODCALLTYPE *EndOfStream)(
        IMediaStreamFilter *This);

    END_INTERFACE
} IMediaStreamFilterVtbl;

interface IMediaStreamFilter {
    CONST_VTBL IMediaStreamFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMediaStreamFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMediaStreamFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMediaStreamFilter_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IMediaStreamFilter_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IMediaFilter methods ***/
#define IMediaStreamFilter_Stop(This) (This)->lpVtbl->Stop(This)
#define IMediaStreamFilter_Pause(This) (This)->lpVtbl->Pause(This)
#define IMediaStreamFilter_Run(This,tStart) (This)->lpVtbl->Run(This,tStart)
#define IMediaStreamFilter_GetState(This,dwMilliSecsTimeout,State) (This)->lpVtbl->GetState(This,dwMilliSecsTimeout,State)
#define IMediaStreamFilter_SetSyncSource(This,pClock) (This)->lpVtbl->SetSyncSource(This,pClock)
#define IMediaStreamFilter_GetSyncSource(This,pClock) (This)->lpVtbl->GetSyncSource(This,pClock)
/*** IBaseFilter methods ***/
#define IMediaStreamFilter_EnumPins(This,ppEnum) (This)->lpVtbl->EnumPins(This,ppEnum)
#define IMediaStreamFilter_FindPin(This,Id,ppPin) (This)->lpVtbl->FindPin(This,Id,ppPin)
#define IMediaStreamFilter_QueryFilterInfo(This,pInfo) (This)->lpVtbl->QueryFilterInfo(This,pInfo)
#define IMediaStreamFilter_JoinFilterGraph(This,pGraph,pName) (This)->lpVtbl->JoinFilterGraph(This,pGraph,pName)
#define IMediaStreamFilter_QueryVendorInfo(This,pVendorInfo) (This)->lpVtbl->QueryVendorInfo(This,pVendorInfo)
/*** IMediaStreamFilter methods ***/
#define IMediaStreamFilter_AddMediaStream(This,pAMMediaStream) (This)->lpVtbl->AddMediaStream(This,pAMMediaStream)
#define IMediaStreamFilter_GetMediaStream(This,idPurpose,ppMediaStream) (This)->lpVtbl->GetMediaStream(This,idPurpose,ppMediaStream)
#define IMediaStreamFilter_EnumMediaStreams(This,Index,ppMediaStream) (This)->lpVtbl->EnumMediaStreams(This,Index,ppMediaStream)
#define IMediaStreamFilter_SupportSeeking(This,bRenderer) (This)->lpVtbl->SupportSeeking(This,bRenderer)
#define IMediaStreamFilter_ReferenceTimeToStreamTime(This,pTime) (This)->lpVtbl->ReferenceTimeToStreamTime(This,pTime)
#define IMediaStreamFilter_GetCurrentStreamTime(This,pCurrentStreamTime) (This)->lpVtbl->GetCurrentStreamTime(This,pCurrentStreamTime)
#define IMediaStreamFilter_WaitUntil(This,WaitStreamTime) (This)->lpVtbl->WaitUntil(This,WaitStreamTime)
#define IMediaStreamFilter_Flush(This,bCancelEOS) (This)->lpVtbl->Flush(This,bCancelEOS)
#define IMediaStreamFilter_EndOfStream(This) (This)->lpVtbl->EndOfStream(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IMediaStreamFilter_QueryInterface(IMediaStreamFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IMediaStreamFilter_AddRef(IMediaStreamFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IMediaStreamFilter_Release(IMediaStreamFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static __WIDL_INLINE HRESULT IMediaStreamFilter_GetClassID(IMediaStreamFilter* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IMediaFilter methods ***/
static __WIDL_INLINE HRESULT IMediaStreamFilter_Stop(IMediaStreamFilter* This) {
    return This->lpVtbl->Stop(This);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_Pause(IMediaStreamFilter* This) {
    return This->lpVtbl->Pause(This);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_Run(IMediaStreamFilter* This,REFERENCE_TIME tStart) {
    return This->lpVtbl->Run(This,tStart);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_GetState(IMediaStreamFilter* This,DWORD dwMilliSecsTimeout,FILTER_STATE *State) {
    return This->lpVtbl->GetState(This,dwMilliSecsTimeout,State);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_SetSyncSource(IMediaStreamFilter* This,IReferenceClock *pClock) {
    return This->lpVtbl->SetSyncSource(This,pClock);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_GetSyncSource(IMediaStreamFilter* This,IReferenceClock **pClock) {
    return This->lpVtbl->GetSyncSource(This,pClock);
}
/*** IBaseFilter methods ***/
static __WIDL_INLINE HRESULT IMediaStreamFilter_EnumPins(IMediaStreamFilter* This,IEnumPins **ppEnum) {
    return This->lpVtbl->EnumPins(This,ppEnum);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_FindPin(IMediaStreamFilter* This,LPCWSTR Id,IPin **ppPin) {
    return This->lpVtbl->FindPin(This,Id,ppPin);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_QueryFilterInfo(IMediaStreamFilter* This,FILTER_INFO *pInfo) {
    return This->lpVtbl->QueryFilterInfo(This,pInfo);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_JoinFilterGraph(IMediaStreamFilter* This,IFilterGraph *pGraph,LPCWSTR pName) {
    return This->lpVtbl->JoinFilterGraph(This,pGraph,pName);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_QueryVendorInfo(IMediaStreamFilter* This,LPWSTR *pVendorInfo) {
    return This->lpVtbl->QueryVendorInfo(This,pVendorInfo);
}
/*** IMediaStreamFilter methods ***/
static __WIDL_INLINE HRESULT IMediaStreamFilter_AddMediaStream(IMediaStreamFilter* This,IAMMediaStream *pAMMediaStream) {
    return This->lpVtbl->AddMediaStream(This,pAMMediaStream);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_GetMediaStream(IMediaStreamFilter* This,REFMSPID idPurpose,IMediaStream **ppMediaStream) {
    return This->lpVtbl->GetMediaStream(This,idPurpose,ppMediaStream);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_EnumMediaStreams(IMediaStreamFilter* This,LONG Index,IMediaStream **ppMediaStream) {
    return This->lpVtbl->EnumMediaStreams(This,Index,ppMediaStream);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_SupportSeeking(IMediaStreamFilter* This,WINBOOL bRenderer) {
    return This->lpVtbl->SupportSeeking(This,bRenderer);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_ReferenceTimeToStreamTime(IMediaStreamFilter* This,REFERENCE_TIME *pTime) {
    return This->lpVtbl->ReferenceTimeToStreamTime(This,pTime);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_GetCurrentStreamTime(IMediaStreamFilter* This,REFERENCE_TIME *pCurrentStreamTime) {
    return This->lpVtbl->GetCurrentStreamTime(This,pCurrentStreamTime);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_WaitUntil(IMediaStreamFilter* This,REFERENCE_TIME WaitStreamTime) {
    return This->lpVtbl->WaitUntil(This,WaitStreamTime);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_Flush(IMediaStreamFilter* This,WINBOOL bCancelEOS) {
    return This->lpVtbl->Flush(This,bCancelEOS);
}
static __WIDL_INLINE HRESULT IMediaStreamFilter_EndOfStream(IMediaStreamFilter* This) {
    return This->lpVtbl->EndOfStream(This);
}
#endif
#endif

#endif


#endif  /* __IMediaStreamFilter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectDrawMediaSampleAllocator interface
 */
#ifndef __IDirectDrawMediaSampleAllocator_INTERFACE_DEFINED__
#define __IDirectDrawMediaSampleAllocator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectDrawMediaSampleAllocator, 0xab6b4afc, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab6b4afc-f6e4-11d0-900d-00c04fd9189d")
IDirectDrawMediaSampleAllocator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDirectDraw(
        IDirectDraw **ppDirectDraw) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectDrawMediaSampleAllocator, 0xab6b4afc, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IDirectDrawMediaSampleAllocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectDrawMediaSampleAllocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectDrawMediaSampleAllocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectDrawMediaSampleAllocator *This);

    /*** IDirectDrawMediaSampleAllocator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDirectDraw)(
        IDirectDrawMediaSampleAllocator *This,
        IDirectDraw **ppDirectDraw);

    END_INTERFACE
} IDirectDrawMediaSampleAllocatorVtbl;

interface IDirectDrawMediaSampleAllocator {
    CONST_VTBL IDirectDrawMediaSampleAllocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectDrawMediaSampleAllocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectDrawMediaSampleAllocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectDrawMediaSampleAllocator_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectDrawMediaSampleAllocator methods ***/
#define IDirectDrawMediaSampleAllocator_GetDirectDraw(This,ppDirectDraw) (This)->lpVtbl->GetDirectDraw(This,ppDirectDraw)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDirectDrawMediaSampleAllocator_QueryInterface(IDirectDrawMediaSampleAllocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDirectDrawMediaSampleAllocator_AddRef(IDirectDrawMediaSampleAllocator* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDirectDrawMediaSampleAllocator_Release(IDirectDrawMediaSampleAllocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectDrawMediaSampleAllocator methods ***/
static __WIDL_INLINE HRESULT IDirectDrawMediaSampleAllocator_GetDirectDraw(IDirectDrawMediaSampleAllocator* This,IDirectDraw **ppDirectDraw) {
    return This->lpVtbl->GetDirectDraw(This,ppDirectDraw);
}
#endif
#endif

#endif


#endif  /* __IDirectDrawMediaSampleAllocator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectDrawMediaSample interface
 */
#ifndef __IDirectDrawMediaSample_INTERFACE_DEFINED__
#define __IDirectDrawMediaSample_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectDrawMediaSample, 0xab6b4afe, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab6b4afe-f6e4-11d0-900d-00c04fd9189d")
IDirectDrawMediaSample : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSurfaceAndReleaseLock(
        IDirectDrawSurface **ppDirectDrawSurface,
        RECT *pRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockMediaSamplePointer(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectDrawMediaSample, 0xab6b4afe, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IDirectDrawMediaSampleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectDrawMediaSample *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectDrawMediaSample *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectDrawMediaSample *This);

    /*** IDirectDrawMediaSample methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSurfaceAndReleaseLock)(
        IDirectDrawMediaSample *This,
        IDirectDrawSurface **ppDirectDrawSurface,
        RECT *pRect);

    HRESULT (STDMETHODCALLTYPE *LockMediaSamplePointer)(
        IDirectDrawMediaSample *This);

    END_INTERFACE
} IDirectDrawMediaSampleVtbl;

interface IDirectDrawMediaSample {
    CONST_VTBL IDirectDrawMediaSampleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectDrawMediaSample_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectDrawMediaSample_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectDrawMediaSample_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectDrawMediaSample methods ***/
#define IDirectDrawMediaSample_GetSurfaceAndReleaseLock(This,ppDirectDrawSurface,pRect) (This)->lpVtbl->GetSurfaceAndReleaseLock(This,ppDirectDrawSurface,pRect)
#define IDirectDrawMediaSample_LockMediaSamplePointer(This) (This)->lpVtbl->LockMediaSamplePointer(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDirectDrawMediaSample_QueryInterface(IDirectDrawMediaSample* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDirectDrawMediaSample_AddRef(IDirectDrawMediaSample* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDirectDrawMediaSample_Release(IDirectDrawMediaSample* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectDrawMediaSample methods ***/
static __WIDL_INLINE HRESULT IDirectDrawMediaSample_GetSurfaceAndReleaseLock(IDirectDrawMediaSample* This,IDirectDrawSurface **ppDirectDrawSurface,RECT *pRect) {
    return This->lpVtbl->GetSurfaceAndReleaseLock(This,ppDirectDrawSurface,pRect);
}
static __WIDL_INLINE HRESULT IDirectDrawMediaSample_LockMediaSamplePointer(IDirectDrawMediaSample* This) {
    return This->lpVtbl->LockMediaSamplePointer(This);
}
#endif
#endif

#endif


#endif  /* __IDirectDrawMediaSample_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMMediaTypeStream interface
 */
#ifndef __IAMMediaTypeStream_INTERFACE_DEFINED__
#define __IAMMediaTypeStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMMediaTypeStream, 0xab6b4afa, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab6b4afa-f6e4-11d0-900d-00c04fd9189d")
IAMMediaTypeStream : public IMediaStream
{
    virtual HRESULT STDMETHODCALLTYPE GetFormat(
        AM_MEDIA_TYPE *pMediaType,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFormat(
        AM_MEDIA_TYPE *pMediaType,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSample(
        LONG lSampleSize,
        BYTE *pbBuffer,
        DWORD dwFlags,
        IUnknown *pUnkOuter,
        IAMMediaTypeSample **ppAMMediaTypeSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamAllocatorRequirements(
        ALLOCATOR_PROPERTIES *pProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamAllocatorRequirements(
        ALLOCATOR_PROPERTIES *pProps) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMMediaTypeStream, 0xab6b4afa, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IAMMediaTypeStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMMediaTypeStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMMediaTypeStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMMediaTypeStream *This);

    /*** IMediaStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMultiMediaStream)(
        IAMMediaTypeStream *This,
        IMultiMediaStream **ppMultiMediaStream);

    HRESULT (STDMETHODCALLTYPE *GetInformation)(
        IAMMediaTypeStream *This,
        MSPID *pPurposeId,
        STREAM_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *SetSameFormat)(
        IAMMediaTypeStream *This,
        IMediaStream *pStreamThatHasDesiredFormat,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *AllocateSample)(
        IAMMediaTypeStream *This,
        DWORD dwFlags,
        IStreamSample **ppSample);

    HRESULT (STDMETHODCALLTYPE *CreateSharedSample)(
        IAMMediaTypeStream *This,
        IStreamSample *pExistingSample,
        DWORD dwFlags,
        IStreamSample **ppNewSample);

    HRESULT (STDMETHODCALLTYPE *SendEndOfStream)(
        IAMMediaTypeStream *This,
        DWORD dwFlags);

    /*** IAMMediaTypeStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFormat)(
        IAMMediaTypeStream *This,
        AM_MEDIA_TYPE *pMediaType,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *SetFormat)(
        IAMMediaTypeStream *This,
        AM_MEDIA_TYPE *pMediaType,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *CreateSample)(
        IAMMediaTypeStream *This,
        LONG lSampleSize,
        BYTE *pbBuffer,
        DWORD dwFlags,
        IUnknown *pUnkOuter,
        IAMMediaTypeSample **ppAMMediaTypeSample);

    HRESULT (STDMETHODCALLTYPE *GetStreamAllocatorRequirements)(
        IAMMediaTypeStream *This,
        ALLOCATOR_PROPERTIES *pProps);

    HRESULT (STDMETHODCALLTYPE *SetStreamAllocatorRequirements)(
        IAMMediaTypeStream *This,
        ALLOCATOR_PROPERTIES *pProps);

    END_INTERFACE
} IAMMediaTypeStreamVtbl;

interface IAMMediaTypeStream {
    CONST_VTBL IAMMediaTypeStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMMediaTypeStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMMediaTypeStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMMediaTypeStream_Release(This) (This)->lpVtbl->Release(This)
/*** IMediaStream methods ***/
#define IAMMediaTypeStream_GetMultiMediaStream(This,ppMultiMediaStream) (This)->lpVtbl->GetMultiMediaStream(This,ppMultiMediaStream)
#define IAMMediaTypeStream_GetInformation(This,pPurposeId,pType) (This)->lpVtbl->GetInformation(This,pPurposeId,pType)
#define IAMMediaTypeStream_SetSameFormat(This,pStreamThatHasDesiredFormat,dwFlags) (This)->lpVtbl->SetSameFormat(This,pStreamThatHasDesiredFormat,dwFlags)
#define IAMMediaTypeStream_AllocateSample(This,dwFlags,ppSample) (This)->lpVtbl->AllocateSample(This,dwFlags,ppSample)
#define IAMMediaTypeStream_CreateSharedSample(This,pExistingSample,dwFlags,ppNewSample) (This)->lpVtbl->CreateSharedSample(This,pExistingSample,dwFlags,ppNewSample)
#define IAMMediaTypeStream_SendEndOfStream(This,dwFlags) (This)->lpVtbl->SendEndOfStream(This,dwFlags)
/*** IAMMediaTypeStream methods ***/
#define IAMMediaTypeStream_GetFormat(This,pMediaType,dwFlags) (This)->lpVtbl->GetFormat(This,pMediaType,dwFlags)
#define IAMMediaTypeStream_SetFormat(This,pMediaType,dwFlags) (This)->lpVtbl->SetFormat(This,pMediaType,dwFlags)
#define IAMMediaTypeStream_CreateSample(This,lSampleSize,pbBuffer,dwFlags,pUnkOuter,ppAMMediaTypeSample) (This)->lpVtbl->CreateSample(This,lSampleSize,pbBuffer,dwFlags,pUnkOuter,ppAMMediaTypeSample)
#define IAMMediaTypeStream_GetStreamAllocatorRequirements(This,pProps) (This)->lpVtbl->GetStreamAllocatorRequirements(This,pProps)
#define IAMMediaTypeStream_SetStreamAllocatorRequirements(This,pProps) (This)->lpVtbl->SetStreamAllocatorRequirements(This,pProps)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAMMediaTypeStream_QueryInterface(IAMMediaTypeStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAMMediaTypeStream_AddRef(IAMMediaTypeStream* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAMMediaTypeStream_Release(IAMMediaTypeStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IMediaStream methods ***/
static __WIDL_INLINE HRESULT IAMMediaTypeStream_GetMultiMediaStream(IAMMediaTypeStream* This,IMultiMediaStream **ppMultiMediaStream) {
    return This->lpVtbl->GetMultiMediaStream(This,ppMultiMediaStream);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_GetInformation(IAMMediaTypeStream* This,MSPID *pPurposeId,STREAM_TYPE *pType) {
    return This->lpVtbl->GetInformation(This,pPurposeId,pType);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_SetSameFormat(IAMMediaTypeStream* This,IMediaStream *pStreamThatHasDesiredFormat,DWORD dwFlags) {
    return This->lpVtbl->SetSameFormat(This,pStreamThatHasDesiredFormat,dwFlags);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_AllocateSample(IAMMediaTypeStream* This,DWORD dwFlags,IStreamSample **ppSample) {
    return This->lpVtbl->AllocateSample(This,dwFlags,ppSample);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_CreateSharedSample(IAMMediaTypeStream* This,IStreamSample *pExistingSample,DWORD dwFlags,IStreamSample **ppNewSample) {
    return This->lpVtbl->CreateSharedSample(This,pExistingSample,dwFlags,ppNewSample);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_SendEndOfStream(IAMMediaTypeStream* This,DWORD dwFlags) {
    return This->lpVtbl->SendEndOfStream(This,dwFlags);
}
/*** IAMMediaTypeStream methods ***/
static __WIDL_INLINE HRESULT IAMMediaTypeStream_GetFormat(IAMMediaTypeStream* This,AM_MEDIA_TYPE *pMediaType,DWORD dwFlags) {
    return This->lpVtbl->GetFormat(This,pMediaType,dwFlags);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_SetFormat(IAMMediaTypeStream* This,AM_MEDIA_TYPE *pMediaType,DWORD dwFlags) {
    return This->lpVtbl->SetFormat(This,pMediaType,dwFlags);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_CreateSample(IAMMediaTypeStream* This,LONG lSampleSize,BYTE *pbBuffer,DWORD dwFlags,IUnknown *pUnkOuter,IAMMediaTypeSample **ppAMMediaTypeSample) {
    return This->lpVtbl->CreateSample(This,lSampleSize,pbBuffer,dwFlags,pUnkOuter,ppAMMediaTypeSample);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_GetStreamAllocatorRequirements(IAMMediaTypeStream* This,ALLOCATOR_PROPERTIES *pProps) {
    return This->lpVtbl->GetStreamAllocatorRequirements(This,pProps);
}
static __WIDL_INLINE HRESULT IAMMediaTypeStream_SetStreamAllocatorRequirements(IAMMediaTypeStream* This,ALLOCATOR_PROPERTIES *pProps) {
    return This->lpVtbl->SetStreamAllocatorRequirements(This,pProps);
}
#endif
#endif

#endif


#endif  /* __IAMMediaTypeStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMMediaTypeSample interface
 */
#ifndef __IAMMediaTypeSample_INTERFACE_DEFINED__
#define __IAMMediaTypeSample_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMMediaTypeSample, 0xab6b4afb, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab6b4afb-f6e4-11d0-900d-00c04fd9189d")
IAMMediaTypeSample : public IStreamSample
{
    virtual HRESULT STDMETHODCALLTYPE SetPointer(
        BYTE *pBuffer,
        LONG lSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPointer(
        BYTE **ppBuffer) = 0;

    virtual LONG STDMETHODCALLTYPE GetSize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTime(
        REFERENCE_TIME *pTimeStart,
        REFERENCE_TIME *pTimeEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTime(
        REFERENCE_TIME *pTimeStart,
        REFERENCE_TIME *pTimeEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSyncPoint(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSyncPoint(
        WINBOOL bIsSyncPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPreroll(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreroll(
        WINBOOL bIsPreroll) = 0;

    virtual LONG STDMETHODCALLTYPE GetActualDataLength(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetActualDataLength(
        LONG Len) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaType(
        AM_MEDIA_TYPE **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaType(
        AM_MEDIA_TYPE *pMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDiscontinuity(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDiscontinuity(
        WINBOOL bDiscontinuity) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTime(
        LONGLONG *pTimeStart,
        LONGLONG *pTimeEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaTime(
        LONGLONG *pTimeStart,
        LONGLONG *pTimeEnd) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMMediaTypeSample, 0xab6b4afb, 0xf6e4, 0x11d0, 0x90,0x0d, 0x00,0xc0,0x4f,0xd9,0x18,0x9d)
#endif
#else
typedef struct IAMMediaTypeSampleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMMediaTypeSample *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMMediaTypeSample *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMMediaTypeSample *This);

    /*** IStreamSample methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMediaStream)(
        IAMMediaTypeSample *This,
        IMediaStream **ppMediaStream);

    HRESULT (STDMETHODCALLTYPE *GetSampleTimes)(
        IAMMediaTypeSample *This,
        STREAM_TIME *pStartTime,
        STREAM_TIME *pEndTime,
        STREAM_TIME *pCurrentTime);

    HRESULT (STDMETHODCALLTYPE *SetSampleTimes)(
        IAMMediaTypeSample *This,
        const STREAM_TIME *pStartTime,
        const STREAM_TIME *pEndTime);

    HRESULT (STDMETHODCALLTYPE *Update)(
        IAMMediaTypeSample *This,
        DWORD dwFlags,
        HANDLE hEvent,
        PAPCFUNC pfnAPC,
        DWORD dwAPCData);

    HRESULT (STDMETHODCALLTYPE *CompletionStatus)(
        IAMMediaTypeSample *This,
        DWORD dwFlags,
        DWORD dwMilliseconds);

    /*** IAMMediaTypeSample methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPointer)(
        IAMMediaTypeSample *This,
        BYTE *pBuffer,
        LONG lSize);

    HRESULT (STDMETHODCALLTYPE *GetPointer)(
        IAMMediaTypeSample *This,
        BYTE **ppBuffer);

    LONG (STDMETHODCALLTYPE *GetSize)(
        IAMMediaTypeSample *This);

    HRESULT (STDMETHODCALLTYPE *GetTime)(
        IAMMediaTypeSample *This,
        REFERENCE_TIME *pTimeStart,
        REFERENCE_TIME *pTimeEnd);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IAMMediaTypeSample *This,
        REFERENCE_TIME *pTimeStart,
        REFERENCE_TIME *pTimeEnd);

    HRESULT (STDMETHODCALLTYPE *IsSyncPoint)(
        IAMMediaTypeSample *This);

    HRESULT (STDMETHODCALLTYPE *SetSyncPoint)(
        IAMMediaTypeSample *This,
        WINBOOL bIsSyncPoint);

    HRESULT (STDMETHODCALLTYPE *IsPreroll)(
        IAMMediaTypeSample *This);

    HRESULT (STDMETHODCALLTYPE *SetPreroll)(
        IAMMediaTypeSample *This,
        WINBOOL bIsPreroll);

    LONG (STDMETHODCALLTYPE *GetActualDataLength)(
        IAMMediaTypeSample *This);

    HRESULT (STDMETHODCALLTYPE *SetActualDataLength)(
        IAMMediaTypeSample *This,
        LONG Len);

    HRESULT (STDMETHODCALLTYPE *GetMediaType)(
        IAMMediaTypeSample *This,
        AM_MEDIA_TYPE **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        IAMMediaTypeSample *This,
        AM_MEDIA_TYPE *pMediaType);

    HRESULT (STDMETHODCALLTYPE *IsDiscontinuity)(
        IAMMediaTypeSample *This);

    HRESULT (STDMETHODCALLTYPE *SetDiscontinuity)(
        IAMMediaTypeSample *This,
        WINBOOL bDiscontinuity);

    HRESULT (STDMETHODCALLTYPE *GetMediaTime)(
        IAMMediaTypeSample *This,
        LONGLONG *pTimeStart,
        LONGLONG *pTimeEnd);

    HRESULT (STDMETHODCALLTYPE *SetMediaTime)(
        IAMMediaTypeSample *This,
        LONGLONG *pTimeStart,
        LONGLONG *pTimeEnd);

    END_INTERFACE
} IAMMediaTypeSampleVtbl;

interface IAMMediaTypeSample {
    CONST_VTBL IAMMediaTypeSampleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMMediaTypeSample_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMMediaTypeSample_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMMediaTypeSample_Release(This) (This)->lpVtbl->Release(This)
/*** IStreamSample methods ***/
#define IAMMediaTypeSample_GetMediaStream(This,ppMediaStream) (This)->lpVtbl->GetMediaStream(This,ppMediaStream)
#define IAMMediaTypeSample_GetSampleTimes(This,pStartTime,pEndTime,pCurrentTime) (This)->lpVtbl->GetSampleTimes(This,pStartTime,pEndTime,pCurrentTime)
#define IAMMediaTypeSample_SetSampleTimes(This,pStartTime,pEndTime) (This)->lpVtbl->SetSampleTimes(This,pStartTime,pEndTime)
#define IAMMediaTypeSample_Update(This,dwFlags,hEvent,pfnAPC,dwAPCData) (This)->lpVtbl->Update(This,dwFlags,hEvent,pfnAPC,dwAPCData)
#define IAMMediaTypeSample_CompletionStatus(This,dwFlags,dwMilliseconds) (This)->lpVtbl->CompletionStatus(This,dwFlags,dwMilliseconds)
/*** IAMMediaTypeSample methods ***/
#define IAMMediaTypeSample_SetPointer(This,pBuffer,lSize) (This)->lpVtbl->SetPointer(This,pBuffer,lSize)
#define IAMMediaTypeSample_GetPointer(This,ppBuffer) (This)->lpVtbl->GetPointer(This,ppBuffer)
#define IAMMediaTypeSample_GetSize(This) (This)->lpVtbl->GetSize(This)
#define IAMMediaTypeSample_GetTime(This,pTimeStart,pTimeEnd) (This)->lpVtbl->GetTime(This,pTimeStart,pTimeEnd)
#define IAMMediaTypeSample_SetTime(This,pTimeStart,pTimeEnd) (This)->lpVtbl->SetTime(This,pTimeStart,pTimeEnd)
#define IAMMediaTypeSample_IsSyncPoint(This) (This)->lpVtbl->IsSyncPoint(This)
#define IAMMediaTypeSample_SetSyncPoint(This,bIsSyncPoint) (This)->lpVtbl->SetSyncPoint(This,bIsSyncPoint)
#define IAMMediaTypeSample_IsPreroll(This) (This)->lpVtbl->IsPreroll(This)
#define IAMMediaTypeSample_SetPreroll(This,bIsPreroll) (This)->lpVtbl->SetPreroll(This,bIsPreroll)
#define IAMMediaTypeSample_GetActualDataLength(This) (This)->lpVtbl->GetActualDataLength(This)
#define IAMMediaTypeSample_SetActualDataLength(This,Len) (This)->lpVtbl->SetActualDataLength(This,Len)
#define IAMMediaTypeSample_GetMediaType(This,ppMediaType) (This)->lpVtbl->GetMediaType(This,ppMediaType)
#define IAMMediaTypeSample_SetMediaType(This,pMediaType) (This)->lpVtbl->SetMediaType(This,pMediaType)
#define IAMMediaTypeSample_IsDiscontinuity(This) (This)->lpVtbl->IsDiscontinuity(This)
#define IAMMediaTypeSample_SetDiscontinuity(This,bDiscontinuity) (This)->lpVtbl->SetDiscontinuity(This,bDiscontinuity)
#define IAMMediaTypeSample_GetMediaTime(This,pTimeStart,pTimeEnd) (This)->lpVtbl->GetMediaTime(This,pTimeStart,pTimeEnd)
#define IAMMediaTypeSample_SetMediaTime(This,pTimeStart,pTimeEnd) (This)->lpVtbl->SetMediaTime(This,pTimeStart,pTimeEnd)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAMMediaTypeSample_QueryInterface(IAMMediaTypeSample* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAMMediaTypeSample_AddRef(IAMMediaTypeSample* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAMMediaTypeSample_Release(IAMMediaTypeSample* This) {
    return This->lpVtbl->Release(This);
}
/*** IStreamSample methods ***/
static __WIDL_INLINE HRESULT IAMMediaTypeSample_GetMediaStream(IAMMediaTypeSample* This,IMediaStream **ppMediaStream) {
    return This->lpVtbl->GetMediaStream(This,ppMediaStream);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_GetSampleTimes(IAMMediaTypeSample* This,STREAM_TIME *pStartTime,STREAM_TIME *pEndTime,STREAM_TIME *pCurrentTime) {
    return This->lpVtbl->GetSampleTimes(This,pStartTime,pEndTime,pCurrentTime);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetSampleTimes(IAMMediaTypeSample* This,const STREAM_TIME *pStartTime,const STREAM_TIME *pEndTime) {
    return This->lpVtbl->SetSampleTimes(This,pStartTime,pEndTime);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_Update(IAMMediaTypeSample* This,DWORD dwFlags,HANDLE hEvent,PAPCFUNC pfnAPC,DWORD dwAPCData) {
    return This->lpVtbl->Update(This,dwFlags,hEvent,pfnAPC,dwAPCData);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_CompletionStatus(IAMMediaTypeSample* This,DWORD dwFlags,DWORD dwMilliseconds) {
    return This->lpVtbl->CompletionStatus(This,dwFlags,dwMilliseconds);
}
/*** IAMMediaTypeSample methods ***/
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetPointer(IAMMediaTypeSample* This,BYTE *pBuffer,LONG lSize) {
    return This->lpVtbl->SetPointer(This,pBuffer,lSize);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_GetPointer(IAMMediaTypeSample* This,BYTE **ppBuffer) {
    return This->lpVtbl->GetPointer(This,ppBuffer);
}
static __WIDL_INLINE LONG IAMMediaTypeSample_GetSize(IAMMediaTypeSample* This) {
    return This->lpVtbl->GetSize(This);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_GetTime(IAMMediaTypeSample* This,REFERENCE_TIME *pTimeStart,REFERENCE_TIME *pTimeEnd) {
    return This->lpVtbl->GetTime(This,pTimeStart,pTimeEnd);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetTime(IAMMediaTypeSample* This,REFERENCE_TIME *pTimeStart,REFERENCE_TIME *pTimeEnd) {
    return This->lpVtbl->SetTime(This,pTimeStart,pTimeEnd);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_IsSyncPoint(IAMMediaTypeSample* This) {
    return This->lpVtbl->IsSyncPoint(This);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetSyncPoint(IAMMediaTypeSample* This,WINBOOL bIsSyncPoint) {
    return This->lpVtbl->SetSyncPoint(This,bIsSyncPoint);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_IsPreroll(IAMMediaTypeSample* This) {
    return This->lpVtbl->IsPreroll(This);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetPreroll(IAMMediaTypeSample* This,WINBOOL bIsPreroll) {
    return This->lpVtbl->SetPreroll(This,bIsPreroll);
}
static __WIDL_INLINE LONG IAMMediaTypeSample_GetActualDataLength(IAMMediaTypeSample* This) {
    return This->lpVtbl->GetActualDataLength(This);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetActualDataLength(IAMMediaTypeSample* This,LONG Len) {
    return This->lpVtbl->SetActualDataLength(This,Len);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_GetMediaType(IAMMediaTypeSample* This,AM_MEDIA_TYPE **ppMediaType) {
    return This->lpVtbl->GetMediaType(This,ppMediaType);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetMediaType(IAMMediaTypeSample* This,AM_MEDIA_TYPE *pMediaType) {
    return This->lpVtbl->SetMediaType(This,pMediaType);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_IsDiscontinuity(IAMMediaTypeSample* This) {
    return This->lpVtbl->IsDiscontinuity(This);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetDiscontinuity(IAMMediaTypeSample* This,WINBOOL bDiscontinuity) {
    return This->lpVtbl->SetDiscontinuity(This,bDiscontinuity);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_GetMediaTime(IAMMediaTypeSample* This,LONGLONG *pTimeStart,LONGLONG *pTimeEnd) {
    return This->lpVtbl->GetMediaTime(This,pTimeStart,pTimeEnd);
}
static __WIDL_INLINE HRESULT IAMMediaTypeSample_SetMediaTime(IAMMediaTypeSample* This,LONGLONG *pTimeStart,LONGLONG *pTimeEnd) {
    return This->lpVtbl->SetMediaTime(This,pTimeStart,pTimeEnd);
}
#endif
#endif

#endif


#endif  /* __IAMMediaTypeSample_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AMMultiMediaStream coclass
 */

DEFINE_GUID(CLSID_AMMultiMediaStream, 0x49c47ce5, 0x9ba4, 0x11d0, 0x82,0x12, 0x00,0xc0,0x4f,0xc3,0x2c,0x45);

#ifdef __cplusplus
class DECLSPEC_UUID("49c47ce5-9ba4-11d0-8212-00c04fc32c45") AMMultiMediaStream;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AMMultiMediaStream, 0x49c47ce5, 0x9ba4, 0x11d0, 0x82,0x12, 0x00,0xc0,0x4f,0xc3,0x2c,0x45)
#endif
#endif

DEFINE_GUID(CLSID_AMDirectDrawStream,  0x49c47ce4, 0x9ba4, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);
DEFINE_GUID(CLSID_AMAudioStream,       0x8496e040, 0xaf4c, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);
DEFINE_GUID(CLSID_AMAudioData,         0xf2468580, 0xaf8a, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);
DEFINE_GUID(CLSID_AMMediaTypeStream,   0xcf0f2f7c, 0xf7bf, 0x11d0, 0x90, 0x0d, 0x00, 0xc0, 0x4f, 0xd9, 0x18, 0x9d);
DEFINE_GUID(CLSID_MediaStreamFilter,   0x49c47ce0, 0x9ba4, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __amstream_h__ */
