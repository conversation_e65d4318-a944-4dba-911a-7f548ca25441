;
; Definition file of USERENV.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "USERENV.dll"
EXPORTS
RsopLoggingEnabled@0
CreateEnvironmentBlock@12
CreateProfile@16
DeleteProfileA@12
DeletePro<PERSON>le<PERSON>@12
DestroyEnvironmentBlock@4
;DllCanUnloadNow@0
;DllGetClassObject@12
;DllGetContractDescription@8
;DllRegisterServer@0
;DllUnregisterServer@0
EnterCriticalPolicySection@4
ExpandEnvironmentStringsForUserA@16
ExpandEnvironmentStringsForUserW@16
ForceSyncFgPolicy@4
FreeGPOListA@4
FreeGPOListW@4
GetAllUsersProfileDirectoryA@8
GetAllUsersProfileDirectoryW@8
GetAppliedGPOListA@20
GetAppliedGPOListW@20
GetDefaultUserProfileDirectoryA@8
GetDefaultUserProfile<PERSON><PERSON><PERSON>W@8
GetGPOListA@24
GetGPOList<PERSON>@24
GetNextFgPolicyRefreshInfo@8
GetPreviousFgPolicyRefreshInfo@8
GetProfileType@4
GetProfilesDirectoryA@8
GetProfilesDirectoryW@8
GetUserProfileDirectoryA@12
GetUserProfileDirectoryW@12
LeaveCriticalPolicySection@4
LoadUserProfileA@8
LoadUserProfileW@8
ProcessGroupPolicyCompleted@12
ProcessGroupPolicyCompletedEx@16
RefreshPolicy@4
RefreshPolicyEx@8
RegisterGPNotification@8
RsopAccessCheckByType@44
RsopFileAccessCheck@20
RsopResetPolicySettingStatus@12
RsopSetPolicySettingStatus@20
UnloadUserProfile@8
UnregisterGPNotification@4
WaitForMachinePolicyForegroundProcessing@0
WaitForUserPolicyForegroundProcessing@0
