LIBRARY "webauthn.dll"
EXPORTS
CryptsvcDllCtrl@16
I_WebAuthNCtapDecodeGetAssertionRpcResponse@32
I_WebAuthNCtapDecodeMakeCredentialRpcResponse@24
I_WebAuthNCtapEncodeGetAssertionRpcRequest@56
I_WebAuthNCtapEncodeMakeCredentialRpcRequest@56
WebAuthNAuthenticatorGetAssertion@20
WebAuthNAuthenticatorMakeCredential@28
WebAuthNCancelCurrentOperation@4
WebAuthNCtapChangeClientPin@28
WebAuthNCtapChangeClientPinForSelectedDevice@24
WebAuthNCtapFreeSelectedDeviceInformation@4
WebAuthNCtapGetAssertion@52
WebAuthNCtapGetSupportedTransports@8
WebAuthNCtapGetWnfLocalizedString@24
WebAuthNCtapIsStopSendCommandError@4
WebAuthNCtapMakeCredential@52
WebAuthNCtapManageAuthenticatePin@20
WebAuthNCtapManageCancelEnrollFingerprint@8
WebAuthNCtapManageChangePin@24
WebAuthNCtapManageClose@4
WebAuthNCtapManageDeleteCredential@16
WebAuthNCtapManageEnrollFingerprint@24
WebAuthNCtapManageFreeDisplayCredentials@4
WebAuthNCtapManageGetDisplayCredentials@12
WebAuthNCtapManageRemoveFingerprints@8
WebAuthNCtapManageResetDevice@8
WebAuthNCtapManageSelect@16
WebAuthNCtapManageSetPin@16
WebAuthNCtapParseAuthenticatorData@16
WebAuthNCtapResetDevice@12
WebAuthNCtapRpcGetAssertionUserList@24
WebAuthNCtapRpcGetCborCommand@12
WebAuthNCtapRpcSelectGetAssertion@20
WebAuthNCtapSendCommand@28
WebAuthNCtapSetClientPin@20
WebAuthNCtapStartDeviceChangeNotify@0
WebAuthNCtapStopDeviceChangeNotify@0
WebAuthNCtapVerifyGetAssertion@20
WebAuthNDecodeAccountInformation@12
WebAuthNDeletePlatformCredential@8
WebAuthNEncodeAccountInformation@12
WebAuthNFreeAssertion@4
WebAuthNFreeCredentialAttestation@4
WebAuthNFreeDecodedAccountInformation@4
WebAuthNFreeEncodedAccountInformation@4
WebAuthNFreePlatformCredentials@4
WebAuthNFreeUserEntityList@4
WebAuthNGetApiVersionNumber@0
WebAuthNGetCancellationId@4
WebAuthNGetCoseAlgorithmIdentifier@8
WebAuthNGetCredentialIdFromAuthenticatorData@16
WebAuthNGetErrorName@4
WebAuthNGetPlatformCredentials@12
WebAuthNGetW3CExceptionDOMError@4
WebAuthNIsUserVerifyingPlatformAuthenticatorAvailable@4
