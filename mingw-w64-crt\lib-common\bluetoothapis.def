;
; Definition file of BluetoothApis.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "BluetoothApis.dll"
EXPORTS
BluetoothAddressToString
BluetoothDisconnectDevice
BluetoothEnableDiscovery
BluetoothEnableIncomingConnections
BluetoothEnumerateInstalledServices
BluetoothEnumerateInstalledServicesEx
BluetoothEnumerateLocalServices
BluetoothFindBrowseGroupClose
BluetoothFindClassIdClose
BluetoothFindDeviceClose
BluetoothFindFirstBrowseGroup
BluetoothFindFirstClassId
BluetoothFindFirstDevice
BluetoothFindFirstProfileDescriptor
BluetoothFindFirstProtocolDescriptorStack
BluetoothFindFirstProtocolEntry
BluetoothFindFirstRadio
BluetoothFindFirstService
BluetoothFindFirstServiceEx
BluetoothFindNextBrowseGroup
BluetoothFindNextClassId
BluetoothFindNextDevice
BluetoothFindNextProfileDescriptor
<PERSON>tooth<PERSON>N<PERSON>ProtocolDescriptorStack
BluetoothFindNextProtocolEntry
BluetoothFindNextRadio
BluetoothFindNextService
BluetoothFindProfileDescriptorClose
BluetoothFindProtocolDescriptorStackClose
BluetoothFindProtocolEntryClose
BluetoothFindRadioClose
BluetoothFindServiceClose
BluetoothGATTAbortReliableWrite
BluetoothGATTBeginReliableWrite
BluetoothGATTEndReliableWrite
BluetoothGATTGetCharacteristicValue
BluetoothGATTGetCharacteristics
BluetoothGATTGetDescriptorValue
BluetoothGATTGetDescriptors
BluetoothGATTGetIncludedServices
BluetoothGATTGetServices
BluetoothGATTRegisterEvent
BluetoothGATTSetCharacteristicValue
BluetoothGATTSetDescriptorValue
BluetoothGATTUnregisterEvent
BluetoothGetDeviceInfo
BluetoothGetLocalServiceInfo
BluetoothGetRadioInfo
BluetoothGetServicePnpInstance
BluetoothIsConnectable
BluetoothIsDiscoverable
BluetoothIsVersionAvailable
BluetoothRegisterForAuthentication
BluetoothRegisterForAuthenticationEx
BluetoothRemoveDevice
BluetoothSdpEnumAttributes
BluetoothSdpGetAttributeValue
BluetoothSdpGetContainerElementData
BluetoothSdpGetElementData
BluetoothSdpGetString
BluetoothSendAuthenticationResponse
BluetoothSendAuthenticationResponseEx
BluetoothSetLocalServiceInfo
BluetoothSetServiceState
BluetoothSetServiceStateEx
BluetoothUnregisterAuthentication
BluetoothUpdateDeviceRecord
BthpCheckForUnsupportedGuid
BthpCleanupBRDeviceNode
BthpCleanupDeviceLocalServices
BthpCleanupDeviceRemoteServices
BthpCleanupLEDeviceNodes
BthpEnableA2DPIfPresent
BthpEnableAllServices
BthpEnableConnectableAndDiscoverable
BthpEnableRadioSoftware
BthpFindPnpInfo
BthpGATTCloseSession
BthpInnerRecord
BthpIsBluetoothServiceRunning
BthpIsConnectableByDefault
BthpIsDiscoverable
BthpIsDiscoverableByDefault
BthpIsRadioSoftwareEnabled
BthpIsTopOfServiceGroup
BthpMapStatusToErr
BthpNextRecord
BthpRegisterForAuthentication
BthpSetServiceState
BthpSetServiceStateEx
BthpTranspose16Bits
BthpTranspose32Bits
BthpTransposeAndExtendBytes
FindNextOpenVCOMPort
InstallIncomingComPort
ShouldForceAuthentication
