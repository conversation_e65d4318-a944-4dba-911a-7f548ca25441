;
; Definition file of ks.sys
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "ks.sys"
EXPORTS
; public: __cdecl CBaseUnknown::CBaseUnknown(struct _GUID const &__ptr64 ,struct IUnknown *__ptr64)__ptr64 
??0CBaseUnknown@@QEAA@AEBU_GUID@@PEAUIUnknown@@@Z
; public: __cdecl CBaseUnknown::CBaseUnknown(struct IUnknown *__ptr64)__ptr64 
??0CBaseUnknown@@QEAA@PEAUIUnknown@@@Z
; public: virtual __cdecl CBaseUnknown::~CBaseUnknown(void)__ptr64 
??1CBaseUnknown@@UEAA@XZ
; public: void __cdecl CBaseUnknown::__dflt_ctor_closure(void)__ptr64 
??_FCBaseUnknown@@QEAAXXZ
; public: virtual unsigned long __c<PERSON>cl CBaseUnknown::IndirectedAddRef(void)__ptr64 
?IndirectedAddRef@CBaseUnknown@@UEAAKXZ
; public: virtual long __cdecl CBaseUnknown::IndirectedQueryInterface(struct _GUID const &__ptr64 ,void *__ptr64 *__ptr64)__ptr64 
?IndirectedQueryInterface@CBaseUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z
; public: virtual unsigned long __cdecl CBaseUnknown::IndirectedRelease(void)__ptr64 
?IndirectedRelease@CBaseUnknown@@UEAAKXZ
; public: virtual unsigned long __cdecl CBaseUnknown::NonDelegatedAddRef(void)__ptr64 
?NonDelegatedAddRef@CBaseUnknown@@UEAAKXZ
; public: virtual long __cdecl CBaseUnknown::NonDelegatedQueryInterface(struct _GUID const &__ptr64 ,void *__ptr64 *__ptr64)__ptr64 
?NonDelegatedQueryInterface@CBaseUnknown@@UEAAJAEBU_GUID@@PEAPEAX@Z
; public: virtual unsigned long __cdecl CBaseUnknown::NonDelegatedRelease(void)__ptr64 
?NonDelegatedRelease@CBaseUnknown@@UEAAKXZ
DllInitialize
KoCreateInstance
KoDeviceInitialize
KoDriverInitialize
KoRelease
KsAcquireCachedMdl
KsAcquireControl
KsAcquireDevice
KsAcquireDeviceSecurityLock
KsAcquireResetValue
KsAddDevice
KsAddEvent
KsAddIrpToCancelableQueue
KsAddItemToObjectBag
KsAddObjectCreateItemToDeviceHeader
KsAddObjectCreateItemToObjectHeader
KsAllocateDefaultClock
KsAllocateDefaultClockEx
KsAllocateDeviceHeader
KsAllocateExtraData
KsAllocateObjectBag
KsAllocateObjectCreateItem
KsAllocateObjectHeader
KsCacheMedium
KsCancelIo
KsCancelRoutine
KsCompletePendingRequest
KsCopyObjectBagItems
KsCreateAllocator
KsCreateBusEnumObject
KsCreateClock
KsCreateDefaultAllocator
KsCreateDefaultAllocatorEx
KsCreateDefaultClock
KsCreateDefaultSecurity
KsCreateDevice
KsCreateFilterFactory
KsCreatePin
KsCreateTopologyNode
KsDecrementCountedWorker
KsDefaultAddEventHandler
KsDefaultDeviceIoCompletion
KsDefaultDispatchPnp
KsDefaultDispatchPower
KsDefaultForwardIrp
KsDereferenceBusObject
KsDereferenceSoftwareBusObject
KsDeviceGetBusData
KsDeviceRegisterAdapterObject
KsDeviceRegisterThermalDispatch
KsDeviceSetBusData
KsDisableEvent
KsDiscardEvent
KsDispatchFastIoDeviceControlFailure
KsDispatchFastReadFailure
KsDispatchInvalidDeviceRequest
KsDispatchIrp
KsDispatchQuerySecurity
KsDispatchSetSecurity
KsDispatchSpecificMethod
KsDispatchSpecificProperty
KsEnableEvent
KsEnableEventWithAllocator
KsFastMethodHandler
KsFastPropertyHandler
KsFilterAcquireProcessingMutex
KsFilterAddTopologyConnections
KsFilterAttemptProcessing
KsFilterCreateNode
KsFilterCreatePinFactory
KsFilterFactoryAddCreateItem
KsFilterFactoryGetSymbolicLink
KsFilterFactorySetDeviceClassesState
KsFilterFactoryUpdateCacheData
KsFilterGetAndGate
KsFilterGetChildPinCount
KsFilterGetFirstChildPin
KsFilterRegisterPowerCallbacks
KsFilterReleaseProcessingMutex
KsForwardAndCatchIrp
KsForwardIrp
KsFreeDefaultClock
KsFreeDeviceHeader
KsFreeEventList
KsFreeObjectBag
KsFreeObjectCreateItem
KsFreeObjectCreateItemsByContext
KsFreeObjectHeader
KsGenerateDataEvent
KsGenerateEvent
KsGenerateEventList
KsGenerateEvents
KsGenerateThermalEvent
KsGetBusEnumIdentifier
KsGetBusEnumParentFDOFromChildPDO
KsGetBusEnumPnpDeviceObject
KsGetDefaultClockState
KsGetDefaultClockTime
KsGetDevice
KsGetDeviceForDeviceObject
KsGetFilterFromIrp
KsGetFirstChild
KsGetImageNameAndResourceId
KsGetNextSibling
KsGetNodeIdFromIrp
KsGetObjectFromFileObject
KsGetObjectTypeFromFileObject
KsGetObjectTypeFromIrp
KsGetOuterUnknown
KsGetParent
KsGetPinFromIrp
KsHandleSizedListQuery
KsIncrementCountedWorker
KsInitializeDevice
KsInitializeDeviceProfile
KsInitializeDriver
KsInstallBusEnumInterface
KsIsBusEnumChildDevice
KsIsCurrentProcessFrameServer
KsLoadResource
KsMapModuleName
KsMergeAutomationTables
KsMethodHandler
KsMethodHandlerWithAllocator
KsMoveIrpsOnCancelableQueue
KsNullDriverUnload
KsPersistDeviceProfile
KsPinAcquireProcessingMutex
KsPinAttachAndGate
KsPinAttachOrGate
KsPinAttemptProcessing
KsPinDataIntersection
KsPinGetAndGate
KsPinGetAvailableByteCount
KsPinGetConnectedFilterInterface
KsPinGetConnectedPinDeviceObject
KsPinGetConnectedPinFileObject
KsPinGetConnectedPinInterface
KsPinGetCopyRelationships
KsPinGetFirstCloneStreamPointer
KsPinGetLeadingEdgeStreamPointer
KsPinGetNextSiblingPin
KsPinGetParentFilter
KsPinGetReferenceClockInterface
KsPinGetTrailingEdgeStreamPointer
KsPinPropertyHandler
KsPinRegisterFrameReturnCallback
KsPinRegisterHandshakeCallback
KsPinRegisterIrpCompletionCallback
KsPinRegisterPowerCallbacks
KsPinReleaseProcessingMutex
KsPinSetPinClockTime
KsPinSubmitFrame
KsPinSubmitFrameMdl
KsProbeStreamIrp
KsProcessPinUpdate
KsPropertyHandler
KsPropertyHandlerWithAllocator
KsPublishDeviceProfile
KsQueryDevicePnpObject
KsQueryInformationFile
KsQueryObjectAccessMask
KsQueryObjectCreateItem
KsQueueWorkItem
KsReadFile
KsRecalculateStackDepth
KsReferenceBusObject
KsReferenceSoftwareBusObject
KsRegisterAggregatedClientUnknown
KsRegisterCountedWorker
KsRegisterFilterWithNoKSPins
KsRegisterWorker
KsReleaseCachedMdl
KsReleaseControl
KsReleaseDevice
KsReleaseDeviceSecurityLock
KsReleaseIrpOnCancelableQueue
KsRemoveBusEnumInterface
KsRemoveIrpFromCancelableQueue
KsRemoveItemFromObjectBag
KsRemoveSpecificIrpFromCancelableQueue
KsServiceBusEnumCreateRequest
KsServiceBusEnumPnpRequest
KsSetDefaultClockState
KsSetDefaultClockTime
KsSetDevicePnpAndBaseObject
KsSetInformationFile
KsSetMajorFunctionHandler
KsSetPowerDispatch
KsSetTargetDeviceObject
KsSetTargetState
KsStreamIo
KsStreamPointerAdvance
KsStreamPointerAdvanceOffsets
KsStreamPointerAdvanceOffsetsAndUnlock
KsStreamPointerCancelTimeout
KsStreamPointerClone
KsStreamPointerDelete
KsStreamPointerGetIrp
KsStreamPointerGetMdl
KsStreamPointerGetNextClone
KsStreamPointerLock
KsStreamPointerScheduleTimeout
KsStreamPointerSetStatusCode
KsStreamPointerUnlock
KsSynchronousIoControlDevice
KsTerminateDevice
KsTopologyPropertyHandler
KsUnregisterWorker
KsUnserializeObjectPropertiesFromRegistry
KsUpdateCameraStreamingConsent
KsValidateAllocatorCreateRequest
KsValidateAllocatorFramingEx
KsValidateClockCreateRequest
KsValidateConnectRequest
KsValidateTopologyNodeCreateRequest
KsWriteFile
KsiDefaultClockAddMarkEvent
KsiPropertyDefaultClockGetCorrelatedPhysicalTime
KsiPropertyDefaultClockGetCorrelatedTime
KsiPropertyDefaultClockGetFunctionTable
KsiPropertyDefaultClockGetPhysicalTime
KsiPropertyDefaultClockGetResolution
KsiPropertyDefaultClockGetState
KsiPropertyDefaultClockGetTime
KsiQueryObjectCreateItemsPresent
_KsEdit
