;
; Definition file of msvcr120_app.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "msvcr120_app.dll"
EXPORTS

#include "func.def.in"
#define NO_GETPID_ALIAS
#include "msvcrt-common.def.in"

#ifdef DEF_X64
$I10_OUTPUT
??0?$_SpinWait@$00@details@Concurrency@@QEAA@P6AXXZ@Z
??0?$_SpinWait@$0A@@details@Concurrency@@QEAA@P6AXXZ@Z
??0SchedulerPolicy@Concurrency@@QEAA@AEBV01@@Z
??0SchedulerPolicy@Concurrency@@QEAA@XZ
??0SchedulerPolicy@Concurrency@@QEAA@_KZZ
??0_Cancellation_beacon@details@Concurrency@@QEAA@XZ
??0_Condition_variable@details@Concurrency@@QEAA@XZ
??0_Context@details@Concurrency@@QEAA@PEAVContext@2@@Z
??0_Interruption_exception@details@Concurrency@@QEAA@PEBD@Z
??0_Interruption_exception@details@Concurrency@@QEAA@XZ
??0_NonReentrantBlockingLock@details@Concurrency@@QEAA@XZ
??0_NonReentrantPPLLock@details@Concurrency@@QEAA@XZ
??0_ReaderWriterLock@details@Concurrency@@QEAA@XZ
??0_ReentrantBlockingLock@details@Concurrency@@QEAA@XZ
??0_ReentrantLock@details@Concurrency@@QEAA@XZ
??0_ReentrantPPLLock@details@Concurrency@@QEAA@XZ
??0_Scheduler@details@Concurrency@@QEAA@PEAVScheduler@2@@Z
??0_Scoped_lock@_NonReentrantPPLLock@details@Concurrency@@QEAA@AEAV123@@Z
??0_Scoped_lock@_ReentrantPPLLock@details@Concurrency@@QEAA@AEAV123@@Z
??0_SpinLock@details@Concurrency@@QEAA@AECJ@Z
??0_StructuredTaskCollection@details@Concurrency@@QEAA@PEAV_CancellationTokenState@12@@Z
??0_TaskCollection@details@Concurrency@@QEAA@PEAV_CancellationTokenState@12@@Z
??0_TaskCollection@details@Concurrency@@QEAA@XZ
??0_Timer@details@Concurrency@@IEAA@I_N@Z
??0__non_rtti_object@std@@QEAA@AEBV01@@Z
??0__non_rtti_object@std@@QEAA@PEBD@Z
??0bad_cast@std@@AEAA@PEBQEBD@Z
??0bad_cast@std@@QEAA@AEBV01@@Z
??0bad_cast@std@@QEAA@PEBD@Z
??0bad_target@Concurrency@@QEAA@PEBD@Z
??0bad_target@Concurrency@@QEAA@XZ
??0bad_typeid@std@@QEAA@AEBV01@@Z
??0bad_typeid@std@@QEAA@PEBD@Z
??0context_self_unblock@Concurrency@@QEAA@PEBD@Z
??0context_self_unblock@Concurrency@@QEAA@XZ
??0context_unblock_unbalanced@Concurrency@@QEAA@PEBD@Z
??0context_unblock_unbalanced@Concurrency@@QEAA@XZ
??0critical_section@Concurrency@@QEAA@XZ
??0default_scheduler_exists@Concurrency@@QEAA@PEBD@Z
??0default_scheduler_exists@Concurrency@@QEAA@XZ
??0event@Concurrency@@QEAA@XZ
??0exception@std@@QEAA@AEBQEBD@Z
??0exception@std@@QEAA@AEBQEBDH@Z
??0exception@std@@QEAA@AEBV01@@Z
??0exception@std@@QEAA@XZ
??0improper_lock@Concurrency@@QEAA@PEBD@Z
??0improper_lock@Concurrency@@QEAA@XZ
??0improper_scheduler_attach@Concurrency@@QEAA@PEBD@Z
??0improper_scheduler_attach@Concurrency@@QEAA@XZ
??0improper_scheduler_detach@Concurrency@@QEAA@PEBD@Z
??0improper_scheduler_detach@Concurrency@@QEAA@XZ
??0improper_scheduler_reference@Concurrency@@QEAA@PEBD@Z
??0improper_scheduler_reference@Concurrency@@QEAA@XZ
??0invalid_link_target@Concurrency@@QEAA@PEBD@Z
??0invalid_link_target@Concurrency@@QEAA@XZ
??0invalid_multiple_scheduling@Concurrency@@QEAA@PEBD@Z
??0invalid_multiple_scheduling@Concurrency@@QEAA@XZ
??0invalid_operation@Concurrency@@QEAA@PEBD@Z
??0invalid_operation@Concurrency@@QEAA@XZ
??0invalid_oversubscribe_operation@Concurrency@@QEAA@PEBD@Z
??0invalid_oversubscribe_operation@Concurrency@@QEAA@XZ
??0invalid_scheduler_policy_key@Concurrency@@QEAA@PEBD@Z
??0invalid_scheduler_policy_key@Concurrency@@QEAA@XZ
??0invalid_scheduler_policy_thread_specification@Concurrency@@QEAA@PEBD@Z
??0invalid_scheduler_policy_thread_specification@Concurrency@@QEAA@XZ
??0invalid_scheduler_policy_value@Concurrency@@QEAA@PEBD@Z
??0invalid_scheduler_policy_value@Concurrency@@QEAA@XZ
??0message_not_found@Concurrency@@QEAA@PEBD@Z
??0message_not_found@Concurrency@@QEAA@XZ
??0missing_wait@Concurrency@@QEAA@PEBD@Z
??0missing_wait@Concurrency@@QEAA@XZ
??0nested_scheduler_missing_detach@Concurrency@@QEAA@PEBD@Z
??0nested_scheduler_missing_detach@Concurrency@@QEAA@XZ
??0operation_timed_out@Concurrency@@QEAA@PEBD@Z
??0operation_timed_out@Concurrency@@QEAA@XZ
??0reader_writer_lock@Concurrency@@QEAA@XZ
??0scheduler_not_attached@Concurrency@@QEAA@PEBD@Z
??0scheduler_not_attached@Concurrency@@QEAA@XZ
??0scheduler_resource_allocation_error@Concurrency@@QEAA@J@Z
??0scheduler_resource_allocation_error@Concurrency@@QEAA@PEBDJ@Z
??0scheduler_worker_creation_error@Concurrency@@QEAA@J@Z
??0scheduler_worker_creation_error@Concurrency@@QEAA@PEBDJ@Z
??0scoped_lock@critical_section@Concurrency@@QEAA@AEAV12@@Z
??0scoped_lock@reader_writer_lock@Concurrency@@QEAA@AEAV12@@Z
??0scoped_lock_read@reader_writer_lock@Concurrency@@QEAA@AEAV12@@Z
??0task_canceled@Concurrency@@QEAA@PEBD@Z
??0task_canceled@Concurrency@@QEAA@XZ
??0unsupported_os@Concurrency@@QEAA@PEBD@Z
??0unsupported_os@Concurrency@@QEAA@XZ
??1SchedulerPolicy@Concurrency@@QEAA@XZ
??1_Cancellation_beacon@details@Concurrency@@QEAA@XZ
??1_Condition_variable@details@Concurrency@@QEAA@XZ
??1_NonReentrantBlockingLock@details@Concurrency@@QEAA@XZ
??1_ReentrantBlockingLock@details@Concurrency@@QEAA@XZ
??1_Scoped_lock@_NonReentrantPPLLock@details@Concurrency@@QEAA@XZ
??1_Scoped_lock@_ReentrantPPLLock@details@Concurrency@@QEAA@XZ
??1_SpinLock@details@Concurrency@@QEAA@XZ
??1_StructuredTaskCollection@details@Concurrency@@QEAA@XZ
??1_TaskCollection@details@Concurrency@@QEAA@XZ
??1_Timer@details@Concurrency@@MEAA@XZ
??1__non_rtti_object@std@@UEAA@XZ
??1bad_cast@std@@UEAA@XZ
??1bad_typeid@std@@UEAA@XZ
??1critical_section@Concurrency@@QEAA@XZ
??1event@Concurrency@@QEAA@XZ
??1exception@std@@UEAA@XZ
??1reader_writer_lock@Concurrency@@QEAA@XZ
??1scoped_lock@critical_section@Concurrency@@QEAA@XZ
??1scoped_lock@reader_writer_lock@Concurrency@@QEAA@XZ
??1scoped_lock_read@reader_writer_lock@Concurrency@@QEAA@XZ
??1type_info@@UEAA@XZ
??2@YAPEAX_K@Z
??2@YAPEAX_KHPEBDH@Z
??3@YAXPEAX@Z
??3@YAXPEAXHPEBDH@Z
??4?$_SpinWait@$00@details@Concurrency@@QEAAAEAV012@AEBV012@@Z
??4?$_SpinWait@$0A@@details@Concurrency@@QEAAAEAV012@AEBV012@@Z
??4SchedulerPolicy@Concurrency@@QEAAAEAV01@AEBV01@@Z
??4__non_rtti_object@std@@QEAAAEAV01@AEBV01@@Z
??4bad_cast@std@@QEAAAEAV01@AEBV01@@Z
??4bad_typeid@std@@QEAAAEAV01@AEBV01@@Z
??4exception@std@@QEAAAEAV01@AEBV01@@Z
??8type_info@@QEBA_NAEBV0@@Z
??9type_info@@QEBA_NAEBV0@@Z
??_7__non_rtti_object@std@@6B@ DATA
??_7bad_cast@std@@6B@ DATA
??_7bad_typeid@std@@6B@ DATA
??_7exception@std@@6B@ DATA
??_F?$_SpinWait@$00@details@Concurrency@@QEAAXXZ
??_F?$_SpinWait@$0A@@details@Concurrency@@QEAAXXZ
??_F_Context@details@Concurrency@@QEAAXXZ
??_F_Scheduler@details@Concurrency@@QEAAXXZ
??_Fbad_cast@std@@QEAAXXZ
??_Fbad_typeid@std@@QEAAXXZ
??_U@YAPEAX_K@Z
??_U@YAPEAX_KHPEBDH@Z
??_V@YAXPEAX@Z
??_V@YAXPEAXHPEBDH@Z
?Alloc@Concurrency@@YAPEAX_K@Z
?Block@Context@Concurrency@@SAXXZ
?CaptureCallstack@platform@details@Concurrency@@YA_KPEAPEAX_K1@Z
?Create@CurrentScheduler@Concurrency@@SAXAEBVSchedulerPolicy@2@@Z
?Create@Scheduler@Concurrency@@SAPEAV12@AEBVSchedulerPolicy@2@@Z
?CreateResourceManager@Concurrency@@YAPEAUIResourceManager@1@XZ
?CreateScheduleGroup@CurrentScheduler@Concurrency@@SAPEAVScheduleGroup@2@AEAVlocation@2@@Z
?CreateScheduleGroup@CurrentScheduler@Concurrency@@SAPEAVScheduleGroup@2@XZ
?CurrentContext@Context@Concurrency@@SAPEAV12@XZ
?Detach@CurrentScheduler@Concurrency@@SAXXZ
?DisableTracing@Concurrency@@YAJXZ
?EnableTracing@Concurrency@@YAJXZ
?Free@Concurrency@@YAXPEAX@Z
?Get@CurrentScheduler@Concurrency@@SAPEAVScheduler@2@XZ
?GetCurrentThreadId@platform@details@Concurrency@@YAJXZ
?GetExecutionContextId@Concurrency@@YAIXZ
?GetNumberOfVirtualProcessors@CurrentScheduler@Concurrency@@SAIXZ
?GetOSVersion@Concurrency@@YA?AW4OSVersion@IResourceManager@1@XZ
?GetPolicy@CurrentScheduler@Concurrency@@SA?AVSchedulerPolicy@2@XZ
?GetPolicyValue@SchedulerPolicy@Concurrency@@QEBAIW4PolicyElementKey@2@@Z
?GetProcessorCount@Concurrency@@YAIXZ
?GetProcessorNodeCount@Concurrency@@YAIXZ
?GetSchedulerId@Concurrency@@YAIXZ
?GetSharedTimerQueue@details@Concurrency@@YAPEAXXZ
?Id@Context@Concurrency@@SAIXZ
?Id@CurrentScheduler@Concurrency@@SAIXZ
?IsAvailableLocation@CurrentScheduler@Concurrency@@SA_NAEBVlocation@2@@Z
?IsCurrentTaskCollectionCanceling@Context@Concurrency@@SA_NXZ
?Log2@details@Concurrency@@YAK_K@Z
?Oversubscribe@Context@Concurrency@@SAX_N@Z
?RegisterShutdownEvent@CurrentScheduler@Concurrency@@SAXPEAX@Z
?ResetDefaultSchedulerPolicy@Scheduler@Concurrency@@SAXXZ
?ScheduleGroupId@Context@Concurrency@@SAIXZ
?ScheduleTask@CurrentScheduler@Concurrency@@SAXP6AXPEAX@Z0@Z
?ScheduleTask@CurrentScheduler@Concurrency@@SAXP6AXPEAX@Z0AEAVlocation@2@@Z
?SetConcurrencyLimits@SchedulerPolicy@Concurrency@@QEAAXII@Z
?SetDefaultSchedulerPolicy@Scheduler@Concurrency@@SAXAEBVSchedulerPolicy@2@@Z
?SetPolicyValue@SchedulerPolicy@Concurrency@@QEAAIW4PolicyElementKey@2@I@Z
?VirtualProcessorId@Context@Concurrency@@SAIXZ
?Yield@Context@Concurrency@@SAXXZ
?_Abort@_StructuredTaskCollection@details@Concurrency@@AEAAXXZ
?_Acquire@_NonReentrantBlockingLock@details@Concurrency@@QEAAXXZ
?_Acquire@_NonReentrantPPLLock@details@Concurrency@@QEAAXPEAX@Z
?_Acquire@_ReentrantBlockingLock@details@Concurrency@@QEAAXXZ
?_Acquire@_ReentrantLock@details@Concurrency@@QEAAXXZ
?_Acquire@_ReentrantPPLLock@details@Concurrency@@QEAAXPEAX@Z
?_AcquireRead@_ReaderWriterLock@details@Concurrency@@QEAAXXZ
?_AcquireWrite@_ReaderWriterLock@details@Concurrency@@QEAAXXZ
?_Cancel@_StructuredTaskCollection@details@Concurrency@@QEAAXXZ
?_Cancel@_TaskCollection@details@Concurrency@@QEAAXXZ
?_CheckTaskCollection@_UnrealizedChore@details@Concurrency@@IEAAXXZ
?_CleanupToken@_StructuredTaskCollection@details@Concurrency@@AEAAXXZ
?_ConcRT_CoreAssert@details@Concurrency@@YAXPEBD0H@Z
?_ConcRT_Trace@details@Concurrency@@YAXHPEB_WZZ
?_Confirm_cancel@_Cancellation_beacon@details@Concurrency@@QEAA_NXZ
?_Copy_str@exception@std@@AEAAXPEBD@Z
?_CurrentContext@_Context@details@Concurrency@@SA?AV123@XZ
?_Current_node@location@Concurrency@@SA?AV12@XZ
?_Destroy@_AsyncTaskCollection@details@Concurrency@@EEAAXXZ
?_DoYield@?$_SpinWait@$00@details@Concurrency@@IEAAXXZ
?_DoYield@?$_SpinWait@$0A@@details@Concurrency@@IEAAXXZ
?_Get@_CurrentScheduler@details@Concurrency@@SA?AV_Scheduler@23@XZ
?_GetConcRTTraceInfo@Concurrency@@YAPEBU_CONCRT_TRACE_INFO@details@1@XZ
?_GetConcurrency@details@Concurrency@@YAIXZ
?_GetCurrentInlineDepth@_StackGuard@details@Concurrency@@CAAEA_KXZ
?_GetNumberOfVirtualProcessors@_CurrentScheduler@details@Concurrency@@SAIXZ
?_GetScheduler@_Scheduler@details@Concurrency@@QEAAPEAVScheduler@3@XZ
?_Id@_CurrentScheduler@details@Concurrency@@SAIXZ
?_IsCanceling@_StructuredTaskCollection@details@Concurrency@@QEAA_NXZ
?_IsCanceling@_TaskCollection@details@Concurrency@@QEAA_NXZ
?_IsSynchronouslyBlocked@_Context@details@Concurrency@@QEBA_NXZ
?_Name_base@type_info@@CAPEBDPEBV1@PEAU__type_info_node@@@Z
?_Name_base_internal@type_info@@CAPEBDPEBV1@PEAU__type_info_node@@@Z
?_NewCollection@_AsyncTaskCollection@details@Concurrency@@SAPEAV123@PEAV_CancellationTokenState@23@@Z
?_NumberOfSpins@?$_SpinWait@$00@details@Concurrency@@IEAAKXZ
?_NumberOfSpins@?$_SpinWait@$0A@@details@Concurrency@@IEAAKXZ
?_Oversubscribe@_Context@details@Concurrency@@SAX_N@Z
?_Reference@_Scheduler@details@Concurrency@@QEAAIXZ
?_Release@_NonReentrantBlockingLock@details@Concurrency@@QEAAXXZ
?_Release@_NonReentrantPPLLock@details@Concurrency@@QEAAXXZ
?_Release@_ReentrantBlockingLock@details@Concurrency@@QEAAXXZ
?_Release@_ReentrantLock@details@Concurrency@@QEAAXXZ
?_Release@_ReentrantPPLLock@details@Concurrency@@QEAAXXZ
?_Release@_Scheduler@details@Concurrency@@QEAAIXZ
?_ReleaseRead@_ReaderWriterLock@details@Concurrency@@QEAAXXZ
?_ReleaseWrite@_ReaderWriterLock@details@Concurrency@@QEAAXXZ
?_ReportUnobservedException@details@Concurrency@@YAXXZ
?_Reset@?$_SpinWait@$00@details@Concurrency@@IEAAXXZ
?_Reset@?$_SpinWait@$0A@@details@Concurrency@@IEAAXXZ
?_RunAndWait@_StructuredTaskCollection@details@Concurrency@@QEAA?AW4_TaskCollectionStatus@23@PEAV_UnrealizedChore@23@@Z
?_RunAndWait@_TaskCollection@details@Concurrency@@QEAA?AW4_TaskCollectionStatus@23@PEAV_UnrealizedChore@23@@Z
?_Schedule@_StructuredTaskCollection@details@Concurrency@@QEAAXPEAV_UnrealizedChore@23@@Z
?_Schedule@_StructuredTaskCollection@details@Concurrency@@QEAAXPEAV_UnrealizedChore@23@PEAVlocation@3@@Z
?_Schedule@_TaskCollection@details@Concurrency@@QEAAXPEAV_UnrealizedChore@23@@Z
?_Schedule@_TaskCollection@details@Concurrency@@QEAAXPEAV_UnrealizedChore@23@PEAVlocation@3@@Z
?_ScheduleTask@_CurrentScheduler@details@Concurrency@@SAXP6AXPEAX@Z0@Z
?_SetSpinCount@?$_SpinWait@$00@details@Concurrency@@QEAAXI@Z
?_SetSpinCount@?$_SpinWait@$0A@@details@Concurrency@@QEAAXI@Z
?_SetUnobservedExceptionHandler@details@Concurrency@@YAXP6AXXZ@Z
?_ShouldSpinAgain@?$_SpinWait@$00@details@Concurrency@@IEAA_NXZ
?_ShouldSpinAgain@?$_SpinWait@$0A@@details@Concurrency@@IEAA_NXZ
?_SpinOnce@?$_SpinWait@$00@details@Concurrency@@QEAA_NXZ
?_SpinOnce@?$_SpinWait@$0A@@details@Concurrency@@QEAA_NXZ
?_SpinYield@Context@Concurrency@@SAXXZ
?_Start@_Timer@details@Concurrency@@IEAAXXZ
?_Stop@_Timer@details@Concurrency@@IEAAXXZ
?_Tidy@exception@std@@AEAAXXZ
?_Trace_agents@Concurrency@@YAXW4Agents_EventType@1@_JZZ
?_Trace_ppl_function@Concurrency@@YAXAEBU_GUID@@EW4ConcRT_EventType@1@@Z
?_TryAcquire@_NonReentrantBlockingLock@details@Concurrency@@QEAA_NXZ
?_TryAcquire@_ReentrantBlockingLock@details@Concurrency@@QEAA_NXZ
?_TryAcquire@_ReentrantLock@details@Concurrency@@QEAA_NXZ
?_TryAcquireWrite@_ReaderWriterLock@details@Concurrency@@QEAA_NXZ
?_Type_info_dtor@type_info@@CAXPEAV1@@Z
?_Type_info_dtor_internal@type_info@@CAXPEAV1@@Z
?_UnderlyingYield@details@Concurrency@@YAXXZ
?_ValidateExecute@@YAHP6A_JXZ@Z
?_ValidateRead@@YAHPEBXI@Z
?_ValidateWrite@@YAHPEAXI@Z
?_Value@_SpinCount@details@Concurrency@@SAIXZ
?_Yield@_Context@details@Concurrency@@SAXXZ
?__ExceptionPtrAssign@@YAXPEAXPEBX@Z
?__ExceptionPtrCompare@@YA_NPEBX0@Z
?__ExceptionPtrCopy@@YAXPEAXPEBX@Z
?__ExceptionPtrCopyException@@YAXPEAXPEBX1@Z
?__ExceptionPtrCreate@@YAXPEAX@Z
?__ExceptionPtrCurrentException@@YAXPEAX@Z
?__ExceptionPtrDestroy@@YAXPEAX@Z
?__ExceptionPtrRethrow@@YAXPEBX@Z
?__ExceptionPtrSwap@@YAXPEAX0@Z
?__ExceptionPtrToBool@@YA_NPEBX@Z
__uncaught_exception
?_inconsistency@@YAXXZ
?_invalid_parameter@@YAXPEBG00I_K@Z
?_is_exception_typeof@@YAHAEBVtype_info@@PEAU_EXCEPTION_POINTERS@@@Z
?_name_internal_method@type_info@@QEBAPEBDPEAU__type_info_node@@@Z
?_open@@YAHPEBDHH@Z
?_query_new_handler@@YAP6AH_K@ZXZ
?_query_new_mode@@YAHXZ
?_set_new_handler@@YAP6AH_K@ZH@Z
?_set_new_handler@@YAP6AH_K@ZP6AH0@Z@Z
?_set_new_mode@@YAHH@Z
?_set_se_translator@@YAP6AXIPEAU_EXCEPTION_POINTERS@@@ZH@Z
?_set_se_translator@@YAP6AXIPEAU_EXCEPTION_POINTERS@@@ZP6AXI0@Z@Z
?_sopen@@YAHPEBDHHH@Z
?_type_info_dtor_internal_method@type_info@@QEAAXXZ
?_wopen@@YAHPEB_WHH@Z
?_wsopen@@YAHPEB_WHHH@Z
?before@type_info@@QEBA_NAEBV1@@Z
?current@location@Concurrency@@SA?AV12@XZ
?from_numa_node@location@Concurrency@@SA?AV12@G@Z
?get_error_code@scheduler_resource_allocation_error@Concurrency@@QEBAJXZ
?lock@critical_section@Concurrency@@QEAAXXZ
?lock@reader_writer_lock@Concurrency@@QEAAXXZ
?lock_read@reader_writer_lock@Concurrency@@QEAAXXZ
?name@type_info@@QEBAPEBDPEAU__type_info_node@@@Z
?native_handle@critical_section@Concurrency@@QEAAAEAV12@XZ
?notify_all@_Condition_variable@details@Concurrency@@QEAAXXZ
?notify_one@_Condition_variable@details@Concurrency@@QEAAXXZ
?raw_name@type_info@@QEBAPEBDXZ
?reset@event@Concurrency@@QEAAXXZ
?set@event@Concurrency@@QEAAXXZ
?set_new_handler@@YAP6AXXZP6AXXZ@Z
?set_task_execution_resources@Concurrency@@YAXGPEAU_GROUP_AFFINITY@@@Z
?set_task_execution_resources@Concurrency@@YAX_K@Z
?set_terminate@@YAP6AXXZH@Z
?set_terminate@@YAP6AXXZP6AXXZ@Z
?set_unexpected@@YAP6AXXZH@Z
?set_unexpected@@YAP6AXXZP6AXXZ@Z
?terminate@@YAXXZ
?try_lock@critical_section@Concurrency@@QEAA_NXZ
?try_lock@reader_writer_lock@Concurrency@@QEAA_NXZ
?try_lock_for@critical_section@Concurrency@@QEAA_NI@Z
?try_lock_read@reader_writer_lock@Concurrency@@QEAA_NXZ
?unexpected@@YAXXZ
?unlock@critical_section@Concurrency@@QEAAXXZ
?unlock@reader_writer_lock@Concurrency@@QEAAXXZ
?wait@Concurrency@@YAXI@Z
?wait@_Condition_variable@details@Concurrency@@QEAAXAEAVcritical_section@3@@Z
?wait@event@Concurrency@@QEAA_KI@Z
?wait_for@_Condition_variable@details@Concurrency@@QEAA_NAEAVcritical_section@3@I@Z
?wait_for_multiple@event@Concurrency@@SA_KPEAPEAV12@_K_NI@Z
?what@exception@std@@UEBAPEBDXZ
#endif

#ifdef DEF_I386
??0?$_SpinWait@$00@details@Concurrency@@QAE@P6AXXZ@Z ; has WINAPI (@4)
??0?$_SpinWait@$0A@@details@Concurrency@@QAE@P6AXXZ@Z ; has WINAPI (@4)
??0SchedulerPolicy@Concurrency@@QAA@IZZ
??0SchedulerPolicy@Concurrency@@QAE@ABV01@@Z ; has WINAPI (@4)
??0SchedulerPolicy@Concurrency@@QAE@XZ
??0_Cancellation_beacon@details@Concurrency@@QAE@XZ
??0_Condition_variable@details@Concurrency@@QAE@XZ
??0_Context@details@Concurrency@@QAE@PAVContext@2@@Z ; has WINAPI (@4)
??0_Interruption_exception@details@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0_Interruption_exception@details@Concurrency@@QAE@XZ
??0_NonReentrantBlockingLock@details@Concurrency@@QAE@XZ
??0_NonReentrantPPLLock@details@Concurrency@@QAE@XZ
??0_ReaderWriterLock@details@Concurrency@@QAE@XZ
??0_ReentrantBlockingLock@details@Concurrency@@QAE@XZ
??0_ReentrantLock@details@Concurrency@@QAE@XZ
??0_ReentrantPPLLock@details@Concurrency@@QAE@XZ
??0_Scheduler@details@Concurrency@@QAE@PAVScheduler@2@@Z ; has WINAPI (@4)
??0_Scoped_lock@_NonReentrantPPLLock@details@Concurrency@@QAE@AAV123@@Z ; has WINAPI (@4)
??0_Scoped_lock@_ReentrantPPLLock@details@Concurrency@@QAE@AAV123@@Z ; has WINAPI (@4)
??0_SpinLock@details@Concurrency@@QAE@ACJ@Z ; has WINAPI (@4)
??0_StructuredTaskCollection@details@Concurrency@@QAE@PAV_CancellationTokenState@12@@Z ; has WINAPI (@4)
??0_TaskCollection@details@Concurrency@@QAE@PAV_CancellationTokenState@12@@Z ; has WINAPI (@4)
??0_TaskCollection@details@Concurrency@@QAE@XZ
??0_Timer@details@Concurrency@@IAE@I_N@Z ; has WINAPI (@8)
??0__non_rtti_object@std@@QAE@ABV01@@Z ; has WINAPI (@4)
??0__non_rtti_object@std@@QAE@PBD@Z ; has WINAPI (@4)
??0bad_cast@std@@AAE@PBQBD@Z ; has WINAPI (@4)
??0bad_cast@std@@QAE@ABV01@@Z ; has WINAPI (@4)
??0bad_cast@std@@QAE@PBD@Z ; has WINAPI (@4)
??0bad_target@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0bad_target@Concurrency@@QAE@XZ
??0bad_typeid@std@@QAE@ABV01@@Z ; has WINAPI (@4)
??0bad_typeid@std@@QAE@PBD@Z ; has WINAPI (@4)
??0context_self_unblock@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0context_self_unblock@Concurrency@@QAE@XZ
??0context_unblock_unbalanced@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0context_unblock_unbalanced@Concurrency@@QAE@XZ
??0critical_section@Concurrency@@QAE@XZ
??0default_scheduler_exists@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0default_scheduler_exists@Concurrency@@QAE@XZ
??0event@Concurrency@@QAE@XZ
??0exception@std@@QAE@ABQBD@Z ; has WINAPI (@4)
??0exception@std@@QAE@ABQBDH@Z ; has WINAPI (@8)
??0exception@std@@QAE@ABV01@@Z ; has WINAPI (@4)
??0exception@std@@QAE@XZ
??0improper_lock@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0improper_lock@Concurrency@@QAE@XZ
??0improper_scheduler_attach@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0improper_scheduler_attach@Concurrency@@QAE@XZ
??0improper_scheduler_detach@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0improper_scheduler_detach@Concurrency@@QAE@XZ
??0improper_scheduler_reference@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0improper_scheduler_reference@Concurrency@@QAE@XZ
??0invalid_link_target@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_link_target@Concurrency@@QAE@XZ
??0invalid_multiple_scheduling@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_multiple_scheduling@Concurrency@@QAE@XZ
??0invalid_operation@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_operation@Concurrency@@QAE@XZ
??0invalid_oversubscribe_operation@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_oversubscribe_operation@Concurrency@@QAE@XZ
??0invalid_scheduler_policy_key@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_scheduler_policy_key@Concurrency@@QAE@XZ
??0invalid_scheduler_policy_thread_specification@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_scheduler_policy_thread_specification@Concurrency@@QAE@XZ
??0invalid_scheduler_policy_value@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0invalid_scheduler_policy_value@Concurrency@@QAE@XZ
??0message_not_found@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0message_not_found@Concurrency@@QAE@XZ
??0missing_wait@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0missing_wait@Concurrency@@QAE@XZ
??0nested_scheduler_missing_detach@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0nested_scheduler_missing_detach@Concurrency@@QAE@XZ
??0operation_timed_out@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0operation_timed_out@Concurrency@@QAE@XZ
??0reader_writer_lock@Concurrency@@QAE@XZ
??0scheduler_not_attached@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0scheduler_not_attached@Concurrency@@QAE@XZ
??0scheduler_resource_allocation_error@Concurrency@@QAE@J@Z ; has WINAPI (@4)
??0scheduler_resource_allocation_error@Concurrency@@QAE@PBDJ@Z ; has WINAPI (@8)
??0scheduler_worker_creation_error@Concurrency@@QAE@J@Z ; has WINAPI (@4)
??0scheduler_worker_creation_error@Concurrency@@QAE@PBDJ@Z ; has WINAPI (@8)
??0scoped_lock@critical_section@Concurrency@@QAE@AAV12@@Z ; has WINAPI (@4)
??0scoped_lock@reader_writer_lock@Concurrency@@QAE@AAV12@@Z ; has WINAPI (@4)
??0scoped_lock_read@reader_writer_lock@Concurrency@@QAE@AAV12@@Z ; has WINAPI (@4)
??0task_canceled@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0task_canceled@Concurrency@@QAE@XZ
??0unsupported_os@Concurrency@@QAE@PBD@Z ; has WINAPI (@4)
??0unsupported_os@Concurrency@@QAE@XZ
??1SchedulerPolicy@Concurrency@@QAE@XZ
??1_Cancellation_beacon@details@Concurrency@@QAE@XZ
??1_Condition_variable@details@Concurrency@@QAE@XZ
??1_NonReentrantBlockingLock@details@Concurrency@@QAE@XZ
??1_ReentrantBlockingLock@details@Concurrency@@QAE@XZ
??1_Scoped_lock@_NonReentrantPPLLock@details@Concurrency@@QAE@XZ
??1_Scoped_lock@_ReentrantPPLLock@details@Concurrency@@QAE@XZ
??1_SpinLock@details@Concurrency@@QAE@XZ
??1_StructuredTaskCollection@details@Concurrency@@QAE@XZ
??1_TaskCollection@details@Concurrency@@QAE@XZ
??1_Timer@details@Concurrency@@MAE@XZ
??1__non_rtti_object@std@@UAE@XZ
??1bad_cast@std@@UAE@XZ
??1bad_typeid@std@@UAE@XZ
??1critical_section@Concurrency@@QAE@XZ
??1event@Concurrency@@QAE@XZ
??1exception@std@@UAE@XZ
??1reader_writer_lock@Concurrency@@QAE@XZ
??1scoped_lock@critical_section@Concurrency@@QAE@XZ
??1scoped_lock@reader_writer_lock@Concurrency@@QAE@XZ
??1scoped_lock_read@reader_writer_lock@Concurrency@@QAE@XZ
??1type_info@@UAE@XZ
??2@YAPAXI@Z
??2@YAPAXIHPBDH@Z
??3@YAXPAX@Z
??3@YAXPAXHPBDH@Z
??4?$_SpinWait@$00@details@Concurrency@@QAEAAV012@ABV012@@Z ; has WINAPI (@4)
??4?$_SpinWait@$0A@@details@Concurrency@@QAEAAV012@ABV012@@Z ; has WINAPI (@4)
??4SchedulerPolicy@Concurrency@@QAEAAV01@ABV01@@Z ; has WINAPI (@4)
??4__non_rtti_object@std@@QAEAAV01@ABV01@@Z ; has WINAPI (@4)
??4bad_cast@std@@QAEAAV01@ABV01@@Z ; has WINAPI (@4)
??4bad_typeid@std@@QAEAAV01@ABV01@@Z ; has WINAPI (@4)
??4exception@std@@QAEAAV01@ABV01@@Z ; has WINAPI (@4)
??8type_info@@QBE_NABV0@@Z ; has WINAPI (@4)
??9type_info@@QBE_NABV0@@Z ; has WINAPI (@4)
??_7__non_rtti_object@std@@6B@ DATA
??_7bad_cast@std@@6B@ DATA
??_7bad_typeid@std@@6B@ DATA
??_7exception@std@@6B@ DATA
??_F?$_SpinWait@$00@details@Concurrency@@QAEXXZ
??_F?$_SpinWait@$0A@@details@Concurrency@@QAEXXZ
??_F_Context@details@Concurrency@@QAEXXZ
??_F_Scheduler@details@Concurrency@@QAEXXZ
??_Fbad_cast@std@@QAEXXZ
??_Fbad_typeid@std@@QAEXXZ
??_U@YAPAXI@Z
??_U@YAPAXIHPBDH@Z
??_V@YAXPAX@Z
??_V@YAXPAXHPBDH@Z
?Alloc@Concurrency@@YAPAXI@Z
?Block@Context@Concurrency@@SAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?CaptureCallstack@platform@details@Concurrency@@YAIPAPAXII@Z
?Create@CurrentScheduler@Concurrency@@SAXABVSchedulerPolicy@2@@Z ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?Create@Scheduler@Concurrency@@SAPAV12@ABVSchedulerPolicy@2@@Z
?CreateResourceManager@Concurrency@@YAPAUIResourceManager@1@XZ
?CreateScheduleGroup@CurrentScheduler@Concurrency@@SAPAVScheduleGroup@2@AAVlocation@2@@Z
?CreateScheduleGroup@CurrentScheduler@Concurrency@@SAPAVScheduleGroup@2@XZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?CurrentContext@Context@Concurrency@@SAPAV12@XZ
?Detach@CurrentScheduler@Concurrency@@SAXXZ
?DisableTracing@Concurrency@@YAJXZ
?EnableTracing@Concurrency@@YAJXZ
?Free@Concurrency@@YAXPAX@Z
?Get@CurrentScheduler@Concurrency@@SAPAVScheduler@2@XZ
?GetCurrentThreadId@platform@details@Concurrency@@YAJXZ ; Check!!! forwards to GetCurrentThreadId in api-ms-win-core-processthreads-l1-1-1.dll (ordinal 16)
?GetExecutionContextId@Concurrency@@YAIXZ
?GetNumberOfVirtualProcessors@CurrentScheduler@Concurrency@@SAIXZ
?GetOSVersion@Concurrency@@YA?AW4OSVersion@IResourceManager@1@XZ
?GetPolicy@CurrentScheduler@Concurrency@@SA?AVSchedulerPolicy@2@XZ
?GetPolicyValue@SchedulerPolicy@Concurrency@@QBEIW4PolicyElementKey@2@@Z ; has WINAPI (@4)
?GetProcessorCount@Concurrency@@YAIXZ
?GetProcessorNodeCount@Concurrency@@YAIXZ
?GetSchedulerId@Concurrency@@YAIXZ
?GetSharedTimerQueue@details@Concurrency@@YAPAXXZ
?Id@Context@Concurrency@@SAIXZ
?Id@CurrentScheduler@Concurrency@@SAIXZ
?IsAvailableLocation@CurrentScheduler@Concurrency@@SA_NABVlocation@2@@Z
?IsCurrentTaskCollectionCanceling@Context@Concurrency@@SA_NXZ
?Log2@details@Concurrency@@YAKI@Z
?Oversubscribe@Context@Concurrency@@SAX_N@Z
?RegisterShutdownEvent@CurrentScheduler@Concurrency@@SAXPAX@Z
?ResetDefaultSchedulerPolicy@Scheduler@Concurrency@@SAXXZ
?ScheduleGroupId@Context@Concurrency@@SAIXZ
?ScheduleTask@CurrentScheduler@Concurrency@@SAXP6AXPAX@Z0@Z
?ScheduleTask@CurrentScheduler@Concurrency@@SAXP6AXPAX@Z0AAVlocation@2@@Z
?SetConcurrencyLimits@SchedulerPolicy@Concurrency@@QAEXII@Z ; has WINAPI (@8)
?SetDefaultSchedulerPolicy@Scheduler@Concurrency@@SAXABVSchedulerPolicy@2@@Z
?SetPolicyValue@SchedulerPolicy@Concurrency@@QAEIW4PolicyElementKey@2@I@Z ; has WINAPI (@8)
?VirtualProcessorId@Context@Concurrency@@SAIXZ
?Yield@Context@Concurrency@@SAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_Abort@_StructuredTaskCollection@details@Concurrency@@AAEXXZ
?_Acquire@_NonReentrantBlockingLock@details@Concurrency@@QAEXXZ
?_Acquire@_NonReentrantPPLLock@details@Concurrency@@QAEXPAX@Z ; has WINAPI (@4)
?_Acquire@_ReentrantBlockingLock@details@Concurrency@@QAEXXZ
?_Acquire@_ReentrantLock@details@Concurrency@@QAEXXZ
?_Acquire@_ReentrantPPLLock@details@Concurrency@@QAEXPAX@Z ; has WINAPI (@4)
?_AcquireRead@_ReaderWriterLock@details@Concurrency@@QAEXXZ
?_AcquireWrite@_ReaderWriterLock@details@Concurrency@@QAEXXZ
?_Cancel@_StructuredTaskCollection@details@Concurrency@@QAEXXZ
?_Cancel@_TaskCollection@details@Concurrency@@QAEXXZ
?_CheckTaskCollection@_UnrealizedChore@details@Concurrency@@IAEXXZ
?_CleanupToken@_StructuredTaskCollection@details@Concurrency@@AAEXXZ
?_ConcRT_CoreAssert@details@Concurrency@@YAXPBD0H@Z ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_ConcRT_Trace@details@Concurrency@@YAXHPB_WZZ
?_Confirm_cancel@_Cancellation_beacon@details@Concurrency@@QAE_NXZ
?_Copy_str@exception@std@@AAEXPBD@Z ; has WINAPI (@4)
?_CurrentContext@_Context@details@Concurrency@@SA?AV123@XZ
?_Current_node@location@Concurrency@@SA?AV12@XZ
?_Destroy@_AsyncTaskCollection@details@Concurrency@@EAEXXZ
?_DoYield@?$_SpinWait@$00@details@Concurrency@@IAEXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_DoYield@?$_SpinWait@$0A@@details@Concurrency@@IAEXXZ
?_Get@_CurrentScheduler@details@Concurrency@@SA?AV_Scheduler@23@XZ
?_GetConcRTTraceInfo@Concurrency@@YAPBU_CONCRT_TRACE_INFO@details@1@XZ
?_GetConcurrency@details@Concurrency@@YAIXZ
?_GetCurrentInlineDepth@_StackGuard@details@Concurrency@@CAAAIXZ
?_GetNumberOfVirtualProcessors@_CurrentScheduler@details@Concurrency@@SAIXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_GetScheduler@_Scheduler@details@Concurrency@@QAEPAVScheduler@3@XZ
?_Id@_CurrentScheduler@details@Concurrency@@SAIXZ
?_IsCanceling@_StructuredTaskCollection@details@Concurrency@@QAE_NXZ
?_IsCanceling@_TaskCollection@details@Concurrency@@QAE_NXZ
?_IsSynchronouslyBlocked@_Context@details@Concurrency@@QBE_NXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_Name_base@type_info@@CAPBDPBV1@PAU__type_info_node@@@Z
?_Name_base_internal@type_info@@CAPBDPBV1@PAU__type_info_node@@@Z
?_NewCollection@_AsyncTaskCollection@details@Concurrency@@SAPAV123@PAV_CancellationTokenState@23@@Z
?_NumberOfSpins@?$_SpinWait@$00@details@Concurrency@@IAEKXZ
?_NumberOfSpins@?$_SpinWait@$0A@@details@Concurrency@@IAEKXZ
?_Oversubscribe@_Context@details@Concurrency@@SAX_N@Z
?_Reference@_Scheduler@details@Concurrency@@QAEIXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_Release@_NonReentrantBlockingLock@details@Concurrency@@QAEXXZ
?_Release@_NonReentrantPPLLock@details@Concurrency@@QAEXXZ
?_Release@_ReentrantBlockingLock@details@Concurrency@@QAEXXZ
?_Release@_ReentrantLock@details@Concurrency@@QAEXXZ
?_Release@_ReentrantPPLLock@details@Concurrency@@QAEXXZ
?_Release@_Scheduler@details@Concurrency@@QAEIXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_ReleaseRead@_ReaderWriterLock@details@Concurrency@@QAEXXZ
?_ReleaseWrite@_ReaderWriterLock@details@Concurrency@@QAEXXZ
?_ReportUnobservedException@details@Concurrency@@YAXXZ
?_Reset@?$_SpinWait@$00@details@Concurrency@@IAEXXZ
?_Reset@?$_SpinWait@$0A@@details@Concurrency@@IAEXXZ
?_RunAndWait@_StructuredTaskCollection@details@Concurrency@@QAG?AW4_TaskCollectionStatus@23@PAV_UnrealizedChore@23@@Z ; has WINAPI (@8)
?_RunAndWait@_TaskCollection@details@Concurrency@@QAG?AW4_TaskCollectionStatus@23@PAV_UnrealizedChore@23@@Z ; has WINAPI (@8)
?_Schedule@_StructuredTaskCollection@details@Concurrency@@QAEXPAV_UnrealizedChore@23@@Z ; has WINAPI (@4)
?_Schedule@_StructuredTaskCollection@details@Concurrency@@QAEXPAV_UnrealizedChore@23@PAVlocation@3@@Z ; has WINAPI (@8)
?_Schedule@_TaskCollection@details@Concurrency@@QAEXPAV_UnrealizedChore@23@@Z ; has WINAPI (@4)
?_Schedule@_TaskCollection@details@Concurrency@@QAEXPAV_UnrealizedChore@23@PAVlocation@3@@Z ; has WINAPI (@8)
?_ScheduleTask@_CurrentScheduler@details@Concurrency@@SAXP6AXPAX@Z0@Z
?_SetSpinCount@?$_SpinWait@$00@details@Concurrency@@QAEXI@Z ; has WINAPI (@4)
?_SetSpinCount@?$_SpinWait@$0A@@details@Concurrency@@QAEXI@Z ; has WINAPI (@4)
?_SetUnobservedExceptionHandler@details@Concurrency@@YAXP6AXXZ@Z
?_ShouldSpinAgain@?$_SpinWait@$00@details@Concurrency@@IAE_NXZ
?_ShouldSpinAgain@?$_SpinWait@$0A@@details@Concurrency@@IAE_NXZ
?_SpinOnce@?$_SpinWait@$00@details@Concurrency@@QAE_NXZ
?_SpinOnce@?$_SpinWait@$0A@@details@Concurrency@@QAE_NXZ
?_SpinYield@Context@Concurrency@@SAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_Start@_Timer@details@Concurrency@@IAEXXZ
?_Stop@_Timer@details@Concurrency@@IAEXXZ
?_Tidy@exception@std@@AAEXXZ
?_Trace_agents@Concurrency@@YAXW4Agents_EventType@1@_JZZ
?_Trace_ppl_function@Concurrency@@YAXABU_GUID@@EW4ConcRT_EventType@1@@Z
?_TryAcquire@_NonReentrantBlockingLock@details@Concurrency@@QAE_NXZ
?_TryAcquire@_ReentrantBlockingLock@details@Concurrency@@QAE_NXZ
?_TryAcquire@_ReentrantLock@details@Concurrency@@QAE_NXZ
?_TryAcquireWrite@_ReaderWriterLock@details@Concurrency@@QAE_NXZ
?_Type_info_dtor@type_info@@CAXPAV1@@Z
?_Type_info_dtor_internal@type_info@@CAXPAV1@@Z
?_UnderlyingYield@details@Concurrency@@YAXXZ
?_ValidateExecute@@YAHP6GHXZ@Z
?_ValidateRead@@YAHPBXI@Z
?_ValidateWrite@@YAHPAXI@Z
?_Value@_SpinCount@details@Concurrency@@SAIXZ
?_Yield@_Context@details@Concurrency@@SAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?__ExceptionPtrAssign@@YAXPAXPBX@Z
?__ExceptionPtrCompare@@YA_NPBX0@Z
?__ExceptionPtrCopy@@YAXPAXPBX@Z
?__ExceptionPtrCopyException@@YAXPAXPBX1@Z
?__ExceptionPtrCreate@@YAXPAX@Z
?__ExceptionPtrCurrentException@@YAXPAX@Z
?__ExceptionPtrDestroy@@YAXPAX@Z
?__ExceptionPtrRethrow@@YAXPBX@Z
?__ExceptionPtrSwap@@YAXPAX0@Z
?__ExceptionPtrToBool@@YA_NPBX@Z
__uncaught_exception
?_inconsistency@@YAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?_invalid_parameter@@YAXPBG00II@Z
?_is_exception_typeof@@YAHABVtype_info@@PAU_EXCEPTION_POINTERS@@@Z
?_name_internal_method@type_info@@QBEPBDPAU__type_info_node@@@Z ; has WINAPI (@4)
?_open@@YAHPBDHH@Z
?_query_new_handler@@YAP6AHI@ZXZ
?_query_new_mode@@YAHXZ
?_set_new_handler@@YAP6AHI@ZH@Z
?_set_new_handler@@YAP6AHI@ZP6AHI@Z@Z
?_set_new_mode@@YAHH@Z
?_set_se_translator@@YAP6AXIPAU_EXCEPTION_POINTERS@@@ZH@Z
?_set_se_translator@@YAP6AXIPAU_EXCEPTION_POINTERS@@@ZP6AXI0@Z@Z
?_sopen@@YAHPBDHHH@Z
?_type_info_dtor_internal_method@type_info@@QAEXXZ
?_wopen@@YAHPB_WHH@Z
?_wsopen@@YAHPB_WHHH@Z
?before@type_info@@QBE_NABV1@@Z ; has WINAPI (@4)
?current@location@Concurrency@@SA?AV12@XZ
?from_numa_node@location@Concurrency@@SA?AV12@G@Z
?get_error_code@scheduler_resource_allocation_error@Concurrency@@QBEJXZ
?lock@critical_section@Concurrency@@QAEXXZ
?lock@reader_writer_lock@Concurrency@@QAEXXZ
?lock_read@reader_writer_lock@Concurrency@@QAEXXZ
?name@type_info@@QBEPBDPAU__type_info_node@@@Z ; has WINAPI (@4)
?native_handle@critical_section@Concurrency@@QAEAAV12@XZ
?notify_all@_Condition_variable@details@Concurrency@@QAEXXZ
?notify_one@_Condition_variable@details@Concurrency@@QAEXXZ
?raw_name@type_info@@QBEPBDXZ
?reset@event@Concurrency@@QAEXXZ
?set@event@Concurrency@@QAEXXZ
?set_new_handler@@YAP6AXXZP6AXXZ@Z
?set_task_execution_resources@Concurrency@@YAXGPAU_GROUP_AFFINITY@@@Z ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?set_task_execution_resources@Concurrency@@YAXK@Z ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?set_terminate@@YAP6AXXZH@Z
?set_terminate@@YAP6AXXZP6AXXZ@Z
?set_unexpected@@YAP6AXXZH@Z
?set_unexpected@@YAP6AXXZP6AXXZ@Z
?swprintf@@YAHPAGIPBGZZ
?swprintf@@YAHPA_WIPB_WZZ
?terminate@@YAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?try_lock@critical_section@Concurrency@@QAE_NXZ
?try_lock@reader_writer_lock@Concurrency@@QAE_NXZ
?try_lock_for@critical_section@Concurrency@@QAE_NI@Z ; has WINAPI (@4)
?try_lock_read@reader_writer_lock@Concurrency@@QAE_NXZ
?unexpected@@YAXXZ ; Check!!! Couldn't determine function argument count. Function doesn't return. 
?unlock@critical_section@Concurrency@@QAEXXZ
?unlock@reader_writer_lock@Concurrency@@QAEXXZ
?vswprintf@@YAHPA_WIPB_WPAD@Z
?wait@Concurrency@@YAXI@Z
?wait@_Condition_variable@details@Concurrency@@QAEXAAVcritical_section@3@@Z ; has WINAPI (@4)
?wait@event@Concurrency@@QAEII@Z ; has WINAPI (@4)
?wait_for@_Condition_variable@details@Concurrency@@QAE_NAAVcritical_section@3@I@Z ; has WINAPI (@8)
?wait_for_multiple@event@Concurrency@@SAIPAPAV12@I_NI@Z
?what@exception@std@@UBEPBDXZ
$I10_OUTPUT
#endif

#ifdef DEF_ARM32
??0__non_rtti_object@@QAA@ABV0@@Z
??0__non_rtti_object@@QAA@PBD@Z
??0bad_cast@@AAA@PBQBD@Z
??0bad_cast@@QAA@ABV0@@Z
??0bad_cast@@QAA@PBD@Z
??0bad_typeid@@QAA@ABV0@@Z
??0bad_typeid@@QAA@PBD@Z
??0exception@@QAA@ABQBD@Z
??0exception@@QAA@ABQBDH@Z
??0exception@@QAA@ABV0@@Z
??0exception@@QAA@XZ
??1__non_rtti_object@@UAA@XZ
??1bad_cast@@UAA@XZ
??1bad_typeid@@UAA@XZ
??1exception@@UAA@XZ
??1type_info@@UAA@XZ
??2@YAPAXI@Z
??2@YAPAXIHPBDH@Z
??3@YAXPAX@Z
??4__non_rtti_object@@QAAAAV0@ABV0@@Z
??4bad_cast@@QAAAAV0@ABV0@@Z
??4bad_typeid@@QAAAAV0@ABV0@@Z
??4exception@@QAAAAV0@ABV0@@Z
??8type_info@@QBAHABV0@@Z
??9type_info@@QBAHABV0@@Z
??_7__non_rtti_object@@6B@ DATA
??_7bad_cast@@6B@ DATA
??_7bad_typeid@@6B@ DATA
??_7exception@@6B@ DATA
??_Fbad_cast@@QAAXXZ
??_Fbad_typeid@@QAAXXZ
??_U@YAPAXI@Z
??_U@YAPAXIHPBDH@Z
??_V@YAXPAX@Z
_CallMemberFunction0
_CallMemberFunction1
_CallMemberFunction2
__ExceptionPtrAssign
__ExceptionPtrCompare
__ExceptionPtrCopy
__ExceptionPtrCopyException
__ExceptionPtrCreate
__ExceptionPtrCurrentException
__ExceptionPtrDestroy
__ExceptionPtrRethrow
__ExceptionPtrSwap
__ExceptionPtrToBool
__uncaught_exception
?_query_new_handler@@YAP6AHI@ZXZ
?_set_new_handler@@YAP6AHI@ZP6AHI@Z@Z
?_set_new_mode@@YAHH@Z
?_set_se_translator@@YAP6AXIPAU_EXCEPTION_POINTERS@@@ZP6AXI0@Z@Z
?before@type_info@@QBAHABV1@@Z
?name@type_info@@QBAPBDXZ
?raw_name@type_info@@QBAPBDXZ
?set_terminate@@YAP6AXXZP6AXXZ@Z
?set_unexpected@@YAP6AXXZP6AXXZ@Z
?terminate@@YAXXZ
?unexpected@@YAXXZ
?what@exception@@UBAPBDXZ
#endif

#ifdef DEF_ARM32
_CrtCheckMemory
_CrtDbgBreak
_CrtDbgReport
_CrtDbgReportV
_CrtDbgReportW
_CrtDbgReportWV
_CrtDoForAllClientObjects
_CrtDumpMemoryLeaks
_CrtIsMemoryBlock
_CrtIsValidHeapPointer
_CrtIsValidPointer
_CrtMemCheckpoint
_CrtMemDifference
_CrtMemDumpAllObjectsSince
_CrtMemDumpStatistics
_CrtReportBlockType
_CrtSetAllocHook
_CrtSetBreakAlloc
_CrtSetDbgBlockType
_CrtSetDbgFlag
_CrtSetDumpClient
_CrtSetReportFile
_CrtSetReportHook
_CrtSetReportHook2
_CrtSetReportMode
#endif

#ifdef DEF_I386
_CIacos
_CIasin
_CIatan
_CIatan2
_CIcos
_CIcosh
_CIexp
_CIfmod
_CIlog
_CIlog10
_CIpow
_CIsin
_CIsinh
_CIsqrt
_CItan
_CItanh
#endif

F_X86_ANY(_Cbuild)
F_X86_ANY(_CreateFrameInfo)
F_I386(_CxxThrowException@8)
F_NON_I386(_CxxThrowException)
F_I386(_EH_prolog)
F_X86_ANY(_FCbuild)
F_X86_ANY(_FindAndUnlinkFrame)
F_X64(_GetImageBase)
F_X64(_GetThrowImageBase)
_Getdays
_Getmonths
_Gettnames
_HUGE DATA
F_X86_ANY(_IsExceptionObjectToBeDestroyed)
F_X86_ANY(_LCbuild)
F_X64(_SetImageBase)
F_X64(_SetThrowImageBase)
F_X86_ANY(_SetWinRTOutOfMemoryExceptionCallback)
_Strftime
_W_Getdays
_W_Getmonths
_W_Gettnames
_Wcsftime
_XcptFilter
__AdjustPointer
F_X86_ANY(__BuildCatchObject)
F_X86_ANY(__BuildCatchObjectHelper)
F_NON_I386(__C_specific_handler)
__CppXcptFilter
F_X86_ANY(__CxxDetectRethrow)
F_X86_ANY(__CxxExceptionFilter)
F_X86_ANY(__CxxFrameHandler)
F_X86_ANY(__CxxFrameHandler2)
__CxxFrameHandler3
F_I386(__CxxLongjmpUnwind@4)
F_X86_ANY(__CxxQueryExceptionSize)
F_X86_ANY(__CxxRegisterExceptionObject)
F_X86_ANY(__CxxUnregisterExceptionObject)
__DestructExceptionObject
F_X86_ANY(__FrameUnwindFilter)
F_X86_ANY(__GetPlatformExceptionInfo)
F_I386(_NLG_Dispatch2@4)
F_I386(_NLG_Return@12)
F_X64(__NLG_Dispatch2)
F_X86_ANY(__NLG_Return2)
__RTCastToVoid
__RTDynamicCast
__RTtypeid
__STRINGTOLD
F_X86_ANY(__STRINGTOLD_L)
F_X86_ANY(__TypeMatch)
___lc_codepage_func
___lc_collate_cp_func
F_X86_ANY(___lc_locale_name_func)
F_ARM32(___lc_handle_func)
___mb_cur_max_func
F_X86_ANY(___mb_cur_max_l_func)
F_X86_ANY(___setlc_active_func)
F_X86_ANY(___unguarded_readlc_active_add_func)
__argc DATA
__argv DATA
__badioinfo DATA
F_X86_ANY(__clean_type_info_names_internal)
F_I386(__control87_2)
F_X86_ANY(__create_locale)
F_X86_ANY(__crtCloseWinRTThreadHandle)
__crtCompareStringA
__crtCompareStringW
F_X86_ANY(__crtCreateWinRTThread)
F_X86_ANY(__crtGetCurrentWinRTThread)
F_X86_ANY(__crtGetCurrentWinRTThreadId)
F_X86_ANY(__crtGetExitCodeWinRTThread)
F_X86_ANY(__crtIsPackagedApp)
F_ARM32(__crtGetLocaleInfoW)
F_ARM32(__crtGetStringTypeW)
__crtLCMapStringA
__crtLCMapStringW
F_X86_ANY(__crtSleep)
F_X86_ANY(__crtWaitForWinRTThreadExit)
F_X64(__crt_debugger_hook)
__daylight
__dllonexit
__doserrno
__dstbias
__fpecode
F_X86_ANY(__free_locale)
F_X86_ANY(__get_current_locale)
F_X86_ANY(__get_flsindex)
F_X86_ANY(__get_tlsindex)
__getmainargs
F_X86_ANY(__initenv DATA)
__iob_func
__isascii
__iscsym
__iscsymf
F_X86_ANY(__iswcsym)
F_X86_ANY(__iswcsymf)
F_X86_ANY(__lconv DATA)
F_ARM32(__jump_unwind)
F_ARM32(__lc_handle DATA)
__lconv_init
#ifdef DEF_I386
__libm_sse2_acos
__libm_sse2_acosf
__libm_sse2_asin
__libm_sse2_asinf
__libm_sse2_atan
__libm_sse2_atan2
__libm_sse2_atanf
__libm_sse2_cos
__libm_sse2_cosf
__libm_sse2_exp
__libm_sse2_expf
__libm_sse2_log
__libm_sse2_log10
__libm_sse2_log10f
__libm_sse2_logf
__libm_sse2_pow
__libm_sse2_powf
__libm_sse2_sin
__libm_sse2_sinf
__libm_sse2_tan
__libm_sse2_tanf
#endif
__mb_cur_max DATA
#if defined(DEF_I386) || defined(DEF_X64)
__p___argc
__p___argv
__p___mb_cur_max
__p___wargv
__p__acmdln
__p__commode
__p__daylight
__p__dstbias
__p__fmode
__p__iob
__p__mbcasemap
__p__mbctype
__p__pctype
__p__pgmptr
__p__pwctype
__p__timezone
__p__tzname
__p__wcmdln
__p__wpgmptr
#endif
__pctype_func
__pioinfo DATA
__pwctype_func
__pxcptinfoptrs
F_X86_ANY(__report_gsfailure)
F_X86_ANY(__setlc_active DATA)
__setusermatherr
__strncnt
F_X86_ANY(__swprintf_l)
F_X86_ANY(__sys_errlist)
F_X86_ANY(__sys_nerr)
F_X86_ANY(__threadhandle)
F_X86_ANY(__threadid)
F_X86_ANY(__timezone)
__toascii
F_X86_ANY(__tzname)
__unDName
__unDNameEx
F_X86_ANY(__unDNameHelper)
F_X86_ANY(__unguarded_readlc_active DATA)
F_X86_ANY(__vswprintf_l)
__wargv DATA
__wcserror
__wcserror_s
__wcsncnt
__wgetmainargs
F_X86_ANY(__winitenv DATA)
F_I386(_abnormal_termination)
_abs64
_access
_access_s
_acmdln DATA
F_ARM32(_aexit_rtn DATA)
_aligned_free
F_ARM32(_aligned_free_dbg)
_aligned_malloc
F_ARM32(_aligned_malloc_dbg)
F_X86_ANY(_aligned_msize)
_aligned_offset_malloc
F_ARM32(_aligned_offset_malloc_dbg)
_aligned_offset_realloc
F_ARM32(_aligned_offset_realloc_dbg)
F_X86_ANY(_aligned_offset_recalloc)
_aligned_realloc
F_ARM32(_aligned_realloc_dbg)
F_X86_ANY(_aligned_recalloc)
_amsg_exit
_assert
_atodbl
_atodbl_l
_atof_l
F_X86_ANY(_atoflt)
_atoflt_l
_atoi64
_atoi64_l
_atoi_l
_atol_l
_atoldbl
_atoldbl_l
F_X86_ANY(_atoll_l)
F_X86_ANY(_byteswap_uint64)
F_X86_ANY(_byteswap_ulong)
F_X86_ANY(_byteswap_ushort)
F_ARM32(_beginthread)
F_ARM32(_beginthreadex)
_c_exit
_cabs
_callnewh
F_X86_ANY(_calloc_crt)
F_ARM32(_calloc_dbg)
_cexit
F_ARM32(_cgets)
F_ARM32(_cgets_s)
F_ARM32(_cgetws)
F_ARM32(_cgetws_s)
F_ARM32(_chdir)
F_ARM32(_chdrive)
_chgsign
_chgsignf
F_I386(_chkesp)
_chmod
_chsize
_chsize_s
F_ARM32(_chvalidator)
F_ARM32(_chvalidator_l)
_clearfp
_close
_commit
_commode DATA
F_X86_ANY(_configthreadlocale)
_control87
_controlfp
_controlfp_s
_copysign
_copysignf
F_ARM32(_cprintf)
F_ARM32(_cprintf_l)
F_ARM32(_cprintf_p)
F_ARM32(_cprintf_p_l)
F_ARM32(_cprintf_s)
F_ARM32(_cprintf_s_l)
F_ARM32(_cputs)
F_ARM32(_cputws)
_creat
_create_locale
F_I386(_crt_debugger_hook)
F_ARM32(_crtAssertBusy)
F_ARM32(_crtBreakAlloc)
F_ARM32(_crtDbgFlag)
F_ARM32(_cscanf)
F_ARM32(_cscanf_l)
F_ARM32(_cscanf_s)
F_ARM32(_cscanf_s_l)
_ctime32
F32(ctime == _ctime32)
_ctime32_s
_ctime64
F64(ctime == _ctime64)
_ctime64_s
F_ARM32(_ctype)
F_ARM32(_cwait)
F_ARM32(_cwprintf)
F_ARM32(_cwprintf_l)
F_ARM32(_cwprintf_p)
F_ARM32(_cwprintf_p_l)
F_ARM32(_cwprintf_s)
F_ARM32(_cwprintf_s_l)
F_ARM32(_cwscanf)
F_ARM32(_cwscanf_l)
F_ARM32(_cwscanf_s)
F_ARM32(_cwscanf_s_l)
_daylight DATA
F_X86_ANY(_dclass)
_difftime32
_difftime64
F_X86_ANY(_dosmaperr)
F_X86_ANY(_dpcomp)
F_X86_ANY(_dsign)
F_X86_ANY(_dstbias DATA)
F_X86_ANY(_dtest)
_dup
_dup2
_ecvt
_ecvt_s
F_X86_ANY(_environ DATA)
F_ARM32(_endthread)
F_ARM32(_endthreadex)
_eof
_errno
F_I386(_except1)
F_I386(_except_handler2)
F_I386(_except_handler3)
F_I386(_except_handler4_common)
F_ARM32(_execl)
F_ARM32(_execle)
F_ARM32(_execlp)
F_ARM32(_execlpe)
F_ARM32(_execv)
F_ARM32(_execve)
F_ARM32(_execvp)
F_ARM32(_execvpe)
_exit
F_X86_ANY(_exit_app)
_expand
F_X86_ANY(_fclose_nolock)
F_ARM32(_expand_dbg)
_fcloseall
_fcvt
_fcvt_s
F_X86_ANY(_fdclass)
_fdopen
F_X86_ANY(_fdpcomp)
F_X86_ANY(_fdsign)
F_X86_ANY(_fdtest)
F_X86_ANY(_fflush_nolock)
_fgetchar
F_X86_ANY(_fgetwc_nolock)
_fgetwchar
_filbuf
_filelength
_filelengthi64
_fileno
_findclose
F_ARM32(_findfirst)
F_X86_ANY(_findfirst32)
F_I386(_findfirst == _findfirst32)
F_X86_ANY(_findfirst32i64)
_findfirst64
F_X86_ANY(_findfirst64i32)
F_X64(_findfirst == _findfirst64)
F_ARM32(_findfirsti64)
F_ARM32(_findnext)
F_X86_ANY(_findnext32)
F_I386(_findnext == _findnext32)
F_X86_ANY(_findnext32i64)
_findnext64
F_X86_ANY(_findnext64i32)
F_X64(_findnext == _findnext64)
F_ARM32(_findnexti64)
_finite
F_NON_I386(_finitef)
_flsbuf
_flushall
_fmode DATA
_fpclass
F_X64(_fpclassf)
_fpieee_flt
_fpreset
_fprintf_l
_fprintf_p
_fprintf_p_l
_fprintf_s_l
_fputchar
F_X86_ANY(_fputwc_nolock)
_fputwchar
F_X86_ANY(_fread_nolock)
F_X86_ANY(_fread_nolock_s)
F_ARM32(_free_dbg)
_free_locale
_freea
F_X86_ANY(_freea_s)
F_X64(_freefls)
F_I386(_freefls@4)
_fscanf_l
_fscanf_s_l
F_X86_ANY(_fseek_nolock)
_fseeki64
F_X86_ANY(_fseeki64_nolock)
_fsopen
F_ARM32(_fstat)
F_X86_ANY(_fstat32)
F_X86_ANY(_fstat32i64)
_fstat64
F_X86_ANY(_fstat64i32)
F_I386(_fstat == _fstat32)
F_I386(_fstati64 == _fstat32i64)
F_X64(_fstat == _fstat64i32)
F_X64(_fstati64 == _fstat64)
F_ARM32(_fstati64)
F_X86_ANY(_ftell_nolock)
F_X86_ANY(_ftelli64)
F_X86_ANY(_ftelli64_nolock)
F_ARM32(_ftime)
_ftime32
_ftime32_s
_ftime64
_ftime64_s
F_I386(_ftime == _ftime32)
F_X64(_ftime == _ftime64)
F_I386(_ftol)
_fullpath
F_ARM32(_fullpath_dbg)
F_ARM32(_futime)
_futime32
_futime64
_fwprintf_l
_fwprintf_p
_fwprintf_p_l
_fwprintf_s_l
F_X86_ANY(_fwrite_nolock)
_fwscanf_l
_fwscanf_s_l
_gcvt
_gcvt_s
_get_current_locale
F_X86_ANY(_get_daylight)
_get_doserrno
F_X86_ANY(_get_dstbias)
F_ARM32(_get_environ)
_get_errno
F_ARM32(_get_fileinfo)
_get_fmode
F_X86_ANY(_get_heap_handle)
F_X86_ANY(_get_invalid_parameter_handler)
_get_osfhandle
_get_output_format
F_X86_ANY(_get_pgmptr)
F_X86_ANY(_get_printf_count_output)
F_X86_ANY(_get_purecall_handler)
F_X86_ANY(_get_terminate)
F_X86_ANY(_get_timezone)
F_X86_ANY(_get_tzname)
F_X86_ANY(_get_unexpected)
F_X86_ANY(_get_wpgmptr)
F_X86_ANY(_getc_nolock)
F_ARM32(_get_wenviron)
F_ARM32(_getch)
F_ARM32(_getche)
F_ARM32(_getcwd)
F_ARM32(_getdcwd)
F_ARM32(_getdiskfree)
F_ARM32(_getdrive)
_getmaxstdio
_getmbcp
F_X86_ANY(_getptd)
_getw
F_X86_ANY(_getws)
F_X86_ANY(_getws_s)
F_I386(_global_unwind2)
F_ARM32(_getwch)
F_ARM32(_getwche)
_gmtime32
_gmtime32_s
F32(gmtime == _gmtime32)
_gmtime64
_gmtime64_s
F_X64(gmtime == _gmtime64)
F_ARM32(_heapchk)
F_ARM32(_heapmin)
F_ARM32(_heapwalk)
_hypot
_hypotf
_i64toa
_i64toa_s
_i64tow
_i64tow_s
F_X86_ANY(_initptd)
_initterm
_initterm_e
_invalid_parameter
F_X86_ANY(_invalid_parameter_noinfo)
F_X86_ANY(_invalid_parameter_noinfo_noreturn)
F_X86_ANY(_invoke_watson)
_iob DATA
_isalnum_l
_isalpha_l
_isatty
F_X86_ANY(_isblank_l)
_iscntrl_l
_isctype
_isctype_l
_isdigit_l
_isgraph_l
_isleadbyte_l
_islower_l
_ismbbalnum
_ismbbalnum_l
_ismbbalpha
_ismbbalpha_l
F_X86_ANY(_ismbbblank)
F_X86_ANY(_ismbbblank_l)
_ismbbgraph
_ismbbgraph_l
_ismbbkalnum
_ismbbkalnum_l
_ismbbkana
_ismbbkana_l
_ismbbkprint
_ismbbkprint_l
_ismbbkpunct
_ismbbkpunct_l
_ismbblead
_ismbblead_l
_ismbbprint
_ismbbprint_l
_ismbbpunct
_ismbbpunct_l
_ismbbtrail
_ismbbtrail_l
#ifdef DEF_ARM32
_ismbcalnum
_ismbcalnum_l
_ismbcalpha
_ismbcalpha_l
_ismbcdigit
_ismbcdigit_l
_ismbcgraph
_ismbcgraph_l
_ismbchira
_ismbchira_l
_ismbckata
_ismbckata_l
_ismbcl0
_ismbcl0_l
_ismbcl1
_ismbcl1_l
_ismbcl2
_ismbcl2_l
_ismbclegal
_ismbclegal_l
_ismbclower
_ismbclower_l
_ismbcprint
_ismbcprint_l
_ismbcpunct
_ismbcpunct_l
_ismbcspace
_ismbcspace_l
_ismbcsymbol
_ismbcsymbol_l
_ismbcupper
_ismbcupper_l
_ismbslead
_ismbslead_l
_ismbstrail
_ismbstrail_l
#endif
_isnan
F_X64(_isnanf)
_isprint_l
F_X86_ANY(_ispunct_l)
_isspace_l
_isupper_l
_iswalnum_l
_iswalpha_l
F_X86_ANY(_iswblank_l)
_iswcntrl_l
F_X86_ANY(_iswcsym_l)
F_X86_ANY(_iswcsymf_l)
_iswctype_l
_iswdigit_l
_iswgraph_l
_iswlower_l
_iswprint_l
_iswpunct_l
_iswspace_l
_iswupper_l
_iswxdigit_l
_isxdigit_l
_itoa
_itoa_s
_itow
_itow_s
_j0
_j1
_jn
F_X86_ANY(_ldclass)
F_X86_ANY(_ldpcomp)
F_X86_ANY(_ldsign)
F_X86_ANY(_ldtest)
F_ARM32(_kbhit)
_lfind
_lfind_s
#ifdef DEF_I386
_libm_sse2_acos_precise
_libm_sse2_asin_precise
_libm_sse2_atan_precise
_libm_sse2_cos_precise
_libm_sse2_exp_precise
_libm_sse2_log10_precise
_libm_sse2_log_precise
_libm_sse2_pow_precise
_libm_sse2_sin_precise
_libm_sse2_sqrt_precise
_libm_sse2_tan_precise
#endif
F_X64(_local_unwind)
F_I386(_local_unwind2)
F_I386(_local_unwind4)
_localtime32
F32(localtime == _localtime32)
_localtime32_s
_localtime64
_localtime64_s
F64(localtime == _localtime64)
_lock
F_X86_ANY(_lock_file)
_locking
_logb
F_NON_I386(_logbf)
F_I386(_longjmpex)
_lrotl
_lrotr
_lsearch
_lsearch_s
_lseek
_lseeki64
_ltoa
_ltoa_s
_ltow
_ltow_s
_makepath
_makepath_s
F_X86_ANY(_malloc_crt)
F_ARM32(_malloc_dbg)
F_ARM32(_mbbtombc)
F_ARM32(_mbbtombc_l)
F_ARM32(_mbbtype)
_mbcasemap DATA
#ifdef DEF_ARM32
_mbccpy
_mbccpy_l
_mbccpy_s
_mbccpy_s_l
_mbcjistojms
_mbcjistojms_l
_mbcjmstojis
_mbcjmstojis_l
_mbclen
_mbclen_l
_mbctohira
_mbctohira_l
_mbctokata
_mbctokata_l
_mbctolower
_mbctolower_l
_mbctombb
_mbctombb_l
_mbctoupper
_mbctoupper_l
#endif
_mbctype DATA
_mblen_l
#ifdef DEF_ARM32
_mbsbtype
_mbsbtype_l
_mbscat
_mbscat_s
_mbscat_s_l
_mbschr
_mbschr_l
_mbscmp
_mbscmp_l
_mbscoll
_mbscoll_l
_mbscpy
_mbscpy_s
_mbscpy_s_l
_mbscspn
_mbscspn_l
_mbsdec
_mbsdec_l
_mbsdup
_mbsicmp
_mbsicmp_l
_mbsicoll
_mbsicoll_l
_mbsinc
_mbsinc_l
_mbslen
_mbslen_l
_mbslwr
_mbslwr_l
_mbslwr_s
_mbslwr_s_l
_mbsnbcat
_mbsnbcat_l
_mbsnbcat_s
_mbsnbcat_s_l
_mbsnbcmp
_mbsnbcmp_l
_mbsnbcnt
_mbsnbcnt_l
_mbsnbcoll
_mbsnbcoll_l
_mbsnbcpy
_mbsnbcpy_l
_mbsnbcpy_s
_mbsnbcpy_s_l
_mbsnbicmp
_mbsnbicmp_l
_mbsnbicoll
_mbsnbicoll_l
_mbsnbset
_mbsnbset_l
_mbsnbset_s
_mbsnbset_s_l
_mbsncat
_mbsncat_l
_mbsncat_s
_mbsncat_s_l
_mbsnccnt
_mbsnccnt_l
_mbsncmp
_mbsncmp_l
_mbsncoll
_mbsncoll_l
_mbsncpy
_mbsncpy_l
_mbsncpy_s
_mbsncpy_s_l
_mbsnextc
_mbsnextc_l
_mbsnicmp
_mbsnicmp_l
_mbsnicoll
_mbsnicoll_l
_mbsninc
_mbsninc_l
_mbsnlen
_mbsnlen_l
_mbsnset
_mbsnset_l
_mbsnset_s
_mbsnset_s_l
_mbspbrk
_mbspbrk_l
_mbsrchr
_mbsrchr_l
_mbsrev
_mbsrev_l
_mbsset
_mbsset_l
_mbsset_s
_mbsset_s_l
_mbsspn
_mbsspn_l
_mbsspnp
_mbsspnp_l
_mbsstr
_mbsstr_l
_mbstok
_mbstok_l
_mbstok_s
_mbstok_s_l
#endif
_mbstowcs_l
_mbstowcs_s_l
_mbstrlen
_mbstrlen_l
_mbstrnlen
_mbstrnlen_l
#ifdef DEF_ARM32
_mbsupr
_mbsupr_l
_mbsupr_s
_mbsupr_s_l
#endif
_mbtowc_l
_memccpy
F_ARM32(_memcpy_strict_align)
_memicmp
_memicmp_l
_mkdir
F_ARM32(_mkgmtime)
_mkgmtime32
_mkgmtime64
_mktemp
_mktemp_s
_mktime32
_mktime64
_msize
F_ARM32(_msize_dbg)
_nextafter
F_X64(_nextafterf)
_onexit
_open
_open_osfhandle
F_ARM32(_osver DATA)
F_ARM32(_pclose)
_pctype DATA
_pgmptr DATA
F_ARM32(_pipe)
F_ARM32(_popen)
_printf_l
_printf_p
_printf_p_l
_printf_s_l
_purecall
F_ARM32(_putch)
F_ARM32(_putenv)
F_ARM32(_putenv_s)
_putw
F_ARM32(_putwch)
_putws
_pwctype DATA
_read
F_X86_ANY(_realloc_crt)
F_X86_ANY(_recalloc)
F_X86_ANY(_recalloc_crt)
F_ARM32(_realloc_dbg)
F_ARM32(_resetstkoflw)
_rmdir
_rmtmp
_rotl
_rotl64
_rotr
_rotr64
_scalb
F_X64(_scalbf)
_scanf_l
_scanf_s_l
_scprintf
_scprintf_l
F_X86_ANY(_scprintf_p)
_scprintf_p_l
_scwprintf
_scwprintf_l
F_X86_ANY(_scwprintf_p)
_scwprintf_p_l
F_X64(_set_FMA3_enable)
F_I386(_seh_longjmp_unwind4@4)
F_I386(_seh_longjmp_unwind@4)
F_I386(_set_SSE2_enable)
F_X86_ANY(_set_abort_behavior)
F_ARM32(_searchenv)
F_ARM32(_searchenv_s)
_set_controlfp
_set_doserrno
_set_errno
_set_error_mode
F_ARM32(_set_fileinfo)
_set_fmode
F_X86_ANY(_set_invalid_parameter_handler)
F_X86_ANY(_set_malloc_crt_max_wait)
_set_output_format
F_X86_ANY(_set_printf_count_output)
F_X86_ANY(_set_purecall_handler)
_setjmp
F_I386(_setjmp3)
F_NON_I386(_setjmpex)
F_X86_ANY(_setmaxstdio)
_setmbcp
_setmode
_snprintf
_snprintf_c
_snprintf_c_l
_snprintf_l
_snprintf_s
_snprintf_s_l
_snscanf
_snscanf_l
_snscanf_s
_snscanf_s_l
_snwprintf
_snwprintf_l
_snwprintf_s
_snwprintf_s_l
_snwscanf
_snwscanf_l
_snwscanf_s
_snwscanf_s_l
_sopen
_sopen_s
F_ARM32(_spawnl)
F_ARM32(_spawnle)
F_ARM32(_spawnlp)
F_ARM32(_spawnlpe)
F_ARM32(_spawnv)
F_ARM32(_spawnve)
F_ARM32(_spawnvp)
F_ARM32(_spawnvpe)
_splitpath
_splitpath_s
_sprintf_l
F_X86_ANY(_sprintf_p)
_sprintf_p_l
_sprintf_s_l
_sscanf_l
_sscanf_s_l
F_ARM32(_stat)
F_X86_ANY(_stat32)
F_X86_ANY(_stat32i64)
_stat64
F_X86_ANY(_stat64i32)
F_I386(_stat == _stat32)
F_I386(_stati64 == _stat32i64)
F_X64(_stat = _stat64i32)
F_X64(_stati64 = _stat64)
F_ARM32(_stati64)
_statusfp
F_I386(_statusfp2)
F_ARM32(_strcmpi)
_strcoll_l
_strdate
_strdate_s
_strdup
F_ARM32(_strdup_dbg)
_strerror
_strerror_s
F_X86_ANY(_strftime_l)
_stricmp
_stricmp_l
_stricoll
_stricoll_l
_strlwr
strlwr == _strlwr
_strlwr_l
_strlwr_s
_strlwr_s_l
_strncoll
_strncoll_l
_strnicmp
_strnicmp_l
_strnicoll
_strnicoll_l
_strnset
_strnset_s
_strrev
_strset
_strset_s
_strtime
_strtime_s
_strtod_l
F_X86_ANY(_strtof_l)
_strtoi64
_strtoi64_l
F_X86_ANY(_strtoimax_l)
_strtol_l
F_X86_ANY(_strtold_l)
F_X86_ANY(_strtoll_l)
_strtoui64
_strtoui64_l
_strtoul_l
F_X86_ANY(_strtoull_l)
F_X86_ANY(_strtoumax_l)
_strupr
_strupr_l
_strupr_s
_strupr_s_l
_strxfrm_l
_swab
F_I386(swab == _swab)
_swprintf
_swprintf_c
_swprintf_c_l
F_X86_ANY(_swprintf_p)
_swprintf_p_l
_swprintf_s_l
_swscanf_l
_swscanf_s_l
_sys_errlist DATA
_sys_nerr DATA
_tell
_telli64
_tempnam
F_ARM32(_tempnam_dbg)
_time32
_time64
F32(time == _time32)
F64(time == _time64)
_timezone DATA
_tolower
_tolower_l
_toupper
_toupper_l
_towlower_l
_towupper_l
_tzname DATA
_tzset
_ui64toa
_ui64toa_s
_ui64tow
_ui64tow_s
_ultoa
_ultoa_s
_ultow
_ultow_s
_umask
_umask_s
F_X86_ANY(_ungetc_nolock)
F_X86_ANY(_ungetwc_nolock)
F_ARM32(_ungetch)
F_ARM32(_ungetwch)
_unlink
_unlock
F_X86_ANY(_unlock_file)
F_ARM32(_utime)
_utime32
_utime64
F_X86_ANY(_vacopy)
F_ARM32(_vcprintf)
F_ARM32(_vcprintf_l)
F_ARM32(_vcprintf_p)
F_ARM32(_vcprintf_p_l)
F_ARM32(_vcprintf_s)
F_ARM32(_vcprintf_s_l)
F_ARM32(_vcwprintf)
F_ARM32(_vcwprintf_l)
F_ARM32(_vcwprintf_p)
F_ARM32(_vcwprintf_p_l)
F_ARM32(_vcwprintf_s)
F_ARM32(_vcwprintf_s_l)
_vfprintf_l
_vfprintf_p
_vfprintf_p_l
_vfprintf_s_l
_vfwprintf_l
_vfwprintf_p
_vfwprintf_p_l
_vfwprintf_s_l
_vprintf_l
_vprintf_p
_vprintf_p_l
_vprintf_s_l
_vscprintf
_vscprintf_l
F_X86_ANY(_vscprintf_p)
_vscprintf_p_l
_vscwprintf
_vscwprintf_l
F_X86_ANY(_vscwprintf_p)
_vscwprintf_p_l
_vsnprintf
_vsnprintf_c
_vsnprintf_c_l
_vsnprintf_l
_vsnprintf_s
_vsnprintf_s_l
_vsnwprintf
_vsnwprintf_l
_vsnwprintf_s
_vsnwprintf_s_l
_vsprintf_l
_vsprintf_p
_vsprintf_p_l
_vsprintf_s_l
_vswprintf
_vswprintf_c
_vswprintf_c_l
_vswprintf_l
F_X86_ANY(_vswprintf_p)
_vswprintf_p_l
_vswprintf_s_l
_vwprintf_l
_vwprintf_p
_vwprintf_p_l
_vwprintf_s_l
_waccess
_waccess_s
_wasctime
_wasctime_s
_wassert
F_ARM32(_wchdir)
_wchmod
_wcmdln DATA
_wcreat
F_X86_ANY(_wcreate_locale)
_wcscoll_l
_wcsdup
F_ARM32(_wcsdup_dbg)
_wcserror
_wcserror_s
_wcsftime_l
_wcsicmp
_wcsicmp_l
_wcsicoll
_wcsicoll_l
_wcslwr
wcslwr == _wcslwr
_wcslwr_l
_wcslwr_s
_wcslwr_s_l
_wcsncoll
_wcsncoll_l
_wcsnicmp
_wcsnicmp_l
_wcsnicoll
_wcsnicoll_l
_wcsnset
_wcsnset_s
_wcsrev
_wcsset
_wcsset_s
_wcstod_l
F_X86_ANY(_wcstof_l)
_wcstoi64
_wcstoi64_l
F_X86_ANY(_wcstoimax_l)
_wcstol_l
F_X86_ANY(_wcstold_l)
F_X86_ANY(_wcstoll_l)
_wcstombs_l
_wcstombs_s_l
_wcstoui64
_wcstoui64_l
_wcstoul_l
F_X86_ANY(_wcstoull_l)
F_X86_ANY(_wcstoumax_l)
_wcsupr
_wcsupr_l
_wcsupr_s
_wcsupr_s_l
_wcsxfrm_l
F_ARM32(_wctime)
_wctime32
F_I386(_wctime == _wctime32)
_wctime32_s
_wctime64
_wctime64_s
F_X64(_wctime == _wctime64)
_wctomb_l
_wctomb_s_l
F_X64(_wctype DATA)
F_I386(_wctype@50371)
F_ARM32(_wctype)
F_X86_ANY(_wenviron DATA)
F_ARM32(_wexecl)
F_ARM32(_wexecle)
F_ARM32(_wexeclp)
F_ARM32(_wexeclpe)
F_ARM32(_wexecv)
F_ARM32(_wexecve)
F_ARM32(_wexecvp)
F_ARM32(_wexecvpe)
_wfdopen
F_ARM32(_wfindfirst)
F_X86_ANY(_wfindfirst32)
F_I386(_wfindfirst == _wfindfirst32)
F_X86_ANY(_wfindfirst32i64)
_wfindfirst64
F_X86_ANY(_wfindfirst64i32)
F_X64(_wfindfirst == _wfindfirst64)
F_ARM32(_wfindfirsti64)
F_ARM32(_wfindnext)
F_X86_ANY(_wfindnext32)
F_I386(_wfindnext == _wfindnext32)
F_X86_ANY(_wfindnext32i64)
_wfindnext64
F_X86_ANY(_wfindnext64i32)
F_X64(_wfindnext == _wfindnext64)
F_ARM32(_wfindnexti64)
_wfopen
_wfopen_s
_wfreopen
_wfreopen_s
_wfsopen
_wfullpath
F_ARM32(_wfullpath_dbg)
F_ARM32(_wgetcwd)
F_ARM32(_wgetdcwd)
F_ARM32(_wgetenv)
F_ARM32(_wgetenv_s)
F_ARM32(_winmajor DATA)
F_ARM32(_winminor DATA)
F_ARM32(_winput_s)
_wmakepath
_wmakepath_s
_wmkdir
_wmktemp
_wmktemp_s
_wopen
F_ARM32(_woutput_s)
_wperror
_wpgmptr DATA
F_ARM32(_wpopen)
_wprintf_l
_wprintf_p
_wprintf_p_l
_wprintf_s_l
F_ARM32(_wputenv)
F_ARM32(_wputenv_s)
_wremove
_wrename
_write
_wrmdir
_wscanf_l
_wscanf_s_l
F_ARM32(_wsearchenv)
F_ARM32(_wsearchenv_s)
_wsetlocale
_wsopen
_wsopen_s
F_ARM32(_wspawnl)
F_ARM32(_wspawnle)
F_ARM32(_wspawnlp)
F_ARM32(_wspawnlpe)
F_ARM32(_wspawnv)
F_ARM32(_wspawnve)
F_ARM32(_wspawnvp)
F_ARM32(_wspawnvpe)
_wsplitpath
_wsplitpath_s
F_ARM32(_wstat)
F_X86_ANY(_wstat32)
F_X86_ANY(_wstat32i64)
F_I386(_wstat == _wstat32)
F_I386(_wstati64 == _wstat32i64)
_wstat64
F_X86_ANY(_wstat64i32)
F_X64(_wstat == _wstat64i32)
F_X64(_wstati64 == _wstat64)
F_ARM32(_wstati64)
_wstrdate
_wstrdate_s
_wstrtime
_wstrtime_s
F_ARM32(_wsystem)
_wtempnam
F_ARM32(_wtempnam_dbg)
_wtmpnam
_wtmpnam_s
_wtof
_wtof_l
_wtoi
_wtoi64
_wtoi64_l
_wtoi_l
_wtol
_wtol_l
F_X86_ANY(_wtoll)
F_X86_ANY(_wtoll_l)
_wunlink
F_ARM32(_wutime)
_wutime32
_wutime64
_y0
_y1
_yn
abort
abs
acos
F_NON_I386(acosf)
F_X86_ANY(acosh)
F_X86_ANY(acoshf)
F_X86_ANY(acoshl)
asctime
asctime_s
asin
F_NON_I386(asinf)
F_X86_ANY(asinh)
F_X86_ANY(asinhf)
F_X86_ANY(asinhl)
atan
atan2
F_NON_I386(atan2f)
F_NON_I386(atanf)
F_X86_ANY(atanh)
F_X86_ANY(atanhf)
F_X86_ANY(atanhl)
atexit
atof
atoi
atol
F_X86_ANY(atoll)
bsearch
bsearch_s
btowc
#if defined(DEF_I386) || defined(DEF_X64)
cabs
cabsf
cabsl
cacos
cacosf
cacosh
cacoshf
cacoshl
cacosl
#endif
calloc
#if defined(DEF_I386) || defined(DEF_X64)
carg
cargf
cargl
casin
casinf
casinh
casinhf
casinhl
casinl
catan
catanf
catanh
catanhf
catanhl
catanl
cbrt
cbrtf
cbrtl
ccos
ccosf
ccosh
ccoshf
ccoshl
ccosl
#endif
ceil
F_NON_I386(ceilf)
#if defined(DEF_I386) || defined(DEF_X64)
cexp
cexpf
cexpl
cimag
cimagf
cimagl
#endif
clearerr
clearerr_s
clock
#if defined(DEF_I386) || defined(DEF_X64)
clog
clog10
clog10f
clog10l
clogf
clogl
conj
conjf
conjl
copysign
copysignf
copysignl
#endif
cos
F_NON_I386(cosf)
cosh
F_NON_I386(coshf)
#if defined(DEF_I386) || defined(DEF_X64)
cpow
cpowf
cpowl
cproj
cprojf
cprojl
creal
crealf
creall
csin
csinf
csinh
csinhf
csinhl
csinl
csqrt
csqrtf
csqrtl
ctan
ctanf
ctanh
ctanhf
ctanhl
ctanl
#endif
F_ARM32(ctime)
F_ARM32(difftime)
div
#if defined(DEF_I386) || defined(DEF_X64)
erf
erfc
erfcf
erfcl
erff
erfl
#endif
exit
exp
exp2
exp2f
F_X86_ANY(exp2l)
F_NON_I386(expf)
F_X86_ANY(expm1)
F_X86_ANY(expm1f)
F_X86_ANY(expm1l)
fabs
F_ARM32(fabsf)
fclose
#if defined(DEF_I386) || defined(DEF_X64)
fdim
fdimf
fdiml
feclearexcept
fegetenv
fegetexceptflag
fegetround
feholdexcept
#endif
feof
F_X86_ANY(feraiseexcept)
ferror
#if defined(DEF_I386) || defined(DEF_X64)
fesetenv
fesetexceptflag
fesetround
fetestexcept
feupdateenv
#endif
fflush
fgetc
fgetpos
fgets
fgetwc
fgetws
floor
F_NON_I386(floorf)
#if defined(DEF_I386) || defined(DEF_X64)
fma
fmaf
fmal
fmax
fmaxf
fmaxl
fmin
fminf
fminl
#endif
fmod
F_NON_I386(fmodf)
fopen
fopen_s
fprintf
__ms_fprintf == fprintf
fprintf_s
fputc
fputs
fputwc
fputws
fread
F_X86_ANY(fread_s)
free
freopen
freopen_s
frexp DATA
fscanf
__ms_fscanf == fscanf
fscanf_s
fseek
fsetpos
ftell
fwprintf
__ms_fwprintf == fwprintf
fwprintf_s
fwrite
fwscanf
__ms_fwscanf == fwscanf
fwscanf_s
getc
getchar
F_X86_ANY(gets)
F_X86_ANY(gets_s)
F_ARM32(getenv)
F_ARM32(getenv_s)
getwc
getwchar
#if defined(DEF_I386) || defined(DEF_X64)
ilogb
ilogbf
ilogbl
imaxabs
imaxdiv
#endif
F_ARM32(gmtime)
F_ARM32(is_wctype)
isalnum
isalpha
F_X86_ANY(isblank)
iscntrl
isdigit
isgraph
isleadbyte
islower
isprint
ispunct
isspace
isupper
iswalnum
iswalpha
iswascii
F_X86_ANY(iswblank)
iswcntrl
iswctype
iswdigit
iswgraph
iswlower
iswprint
iswpunct
iswspace
iswupper
iswxdigit
isxdigit
labs
ldexp
ldiv
#if defined(DEF_I386) || defined(DEF_X64)
lgamma
lgammaf
lgammal
llabs
lldiv
llrint
llrintf
llrintl
llround
llroundf
llroundl
#endif
localeconv
F_ARM32(localtime)
log
log10
F_NON_I386(log10f)
F_X86_ANY(log1p)
F_X86_ANY(log1pf)
F_X86_ANY(log1pl)
log2
log2f
F_X86_ANY(log2l)
F_X86_ANY(logb)
F_X86_ANY(logbf)
F_X86_ANY(logbl)
F_NON_I386(logf)
longjmp
#if defined(DEF_I386) || defined(DEF_X64)
lrint
lrintf
lrintl
lround
lroundf
lroundl
#endif
malloc
mblen
mbrlen
mbrtowc
F_ARM32(mbsdup_dbg)
mbsrtowcs
mbsrtowcs_s
mbstowcs
mbstowcs_s
mbtowc
memchr
memcmp
memcpy
memcpy_s
memmove
memmove_s
memset
F_ARM32(mktime)
modf
F_NON_I386(modff)
#if defined(DEF_I386) || defined(DEF_X64)
nan
nanf
nanl
nearbyint
nearbyintf
nearbyintl
nextafter
nextafterf
nextafterl
nexttoward
nexttowardf
nexttowardl
norm
normf
norml
#endif
perror
pow
F_NON_I386(powf)
printf
__ms_printf == printf
printf_s
putc
putchar
puts
putwc
putwchar
qsort
qsort_s
raise
rand
rand_s
realloc
F_X86_ANY(remainder)
F_X86_ANY(remainderf)
F_X86_ANY(remainderl)
remove
F_X86_ANY(remquo)
F_X86_ANY(remquof)
F_X86_ANY(remquol)
rename
rewind
#if defined(DEF_I386) || defined(DEF_X64)
rint
rintf
rintl
round
roundf
roundl
scalbln
scalblnf
scalblnl
scalbn
scalbnf
scalbnl
#endif
scanf
__ms_scanf == scanf
scanf_s
setbuf
F_NON_I386(setjmp)
setlocale
setvbuf
signal
sin
F_NON_I386(sinf)
sinh
F_NON_I386(sinhf)
sprintf
__ms_sprintf == sprintf
sprintf_s
sqrt
F_NON_I386(sqrtf)
srand
sscanf
__ms_sscanf == sscanf
sscanf_s
strcat
strcat_s
strchr
strcmp
strcoll
strcpy
strcpy_s
strcspn
strerror
strerror_s
strftime
strlen
strncat
strncat_s
strncmp
strncpy
strncpy_s
strnlen
strpbrk
strrchr
strspn
strstr
strtod
F_X86_ANY(strtof)
F_X86_ANY(strtoimax)
strtok
strtok_s
strtol
F_X86_ANY(strtold)
F_X86_ANY(strtoll)
strtoul
F_X86_ANY(strtoull)
F_X86_ANY(strtoumax)
strxfrm
F_ARM32(swprintf)
swprintf_s
swscanf
__ms_swscanf == swscanf
swscanf_s
F_ARM32(system)
tan
F_NON_I386(tanf)
tanh
F_NON_I386(tanhf)
F_X86_ANY(tgamma)
F_X86_ANY(tgammaf)
F_X86_ANY(tgammal)
F_ARM32(time)
tmpfile
tmpfile_s
tmpnam
tmpnam_s
tolower
toupper
F_X86_ANY(towctrans)
towlower
towupper
F_X86_ANY(trunc)
F_X86_ANY(truncf)
F_X86_ANY(truncl)
ungetc
ungetwc
F_ARM32(utime)
vfprintf
__ms_vfprintf == vfprintf
vfprintf_s
F_X86_ANY(vfscanf)
F_X86_ANY(vfscanf_s)
vfwprintf
__ms_vfwprintf == vfwprintf
vfwprintf_s
F_X86_ANY(vfwscanf)
F_X86_ANY(vfwscanf_s)
vprintf
__ms_vprintf == vprintf
vprintf_s
F_X86_ANY(vscanf)
F_X86_ANY(vscanf_s)
vsprintf
__ms_vsprintf == vsprintf
vsprintf_s
F_X86_ANY(vsscanf)
F_X86_ANY(vsscanf_s)
F_ARM32(vswprintf)
vswprintf_s
F_X86_ANY(vswscanf)
F_X86_ANY(vswscanf_s)
vwprintf
__ms_vwprintf == vwprintf
vwprintf_s
F_X86_ANY(vwscanf)
F_X86_ANY(vwscanf_s)
wcrtomb
wcrtomb_s
wcscat
wcscat_s
wcschr
wcscmp
wcscoll
wcscpy
wcscpy_s
wcscspn
wcsftime
wcslen
wcsncat
wcsncat_s
wcsncmp
wcsncpy
wcsncpy_s
wcsnlen
wcspbrk
wcsrchr
wcsrtombs
wcsrtombs_s
wcsspn
wcsstr
wcstod
F_X86_ANY(wcstof)
F_X86_ANY(wcstoimax)
wcstok
wcstok_s
wcstol
F_X86_ANY(wcstold)
F_X86_ANY(wcstoll)
wcstombs
wcstombs_s
wcstoul
F_X86_ANY(wcstoull)
F_X86_ANY(wcstoumax)
wcsxfrm
wctob
wctomb
wctomb_s
F_X86_ANY(wctrans)
F_X86_ANY(wctype)
F_X86_ANY(wmemcpy_s)
F_X86_ANY(wmemmove_s)
wprintf
__ms_wprintf == wprintf
wprintf_s
wscanf
__ms_wscanf == wscanf
wscanf_s
