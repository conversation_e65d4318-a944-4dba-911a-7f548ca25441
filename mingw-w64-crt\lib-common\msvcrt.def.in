LIBRARY "msvcrt.dll"
EXPORTS

#include "func.def.in"
#include "msvcrt-common.def.in"

#ifdef DEF_I386
_CIacos
_CIasin
_CIatan
_CIatan2
_CIcos
_CIcosh
_CIexp
_CIfmod
_CIlog
_CIlog10
_CIpow
_CIsin
_CIsinh
_CIsqrt
_CItan
_CItanh
#endif

#ifdef DEF_X64
$I10_OUTPUT
; public: __cdecl __non_rtti_object::__non_rtti_object(class __non_rtti_object const & __ptr64) __ptr64
; GCC = __ZN17__non_rtti_objectC2ERKS_
??0__non_rtti_object@@QEAA@AEBV0@@Z
; public: __cdecl __non_rtti_object::__non_rtti_object(char const * __ptr64) __ptr64
; GCC = __ZN17__non_rtti_objectC1ERKS_
??0__non_rtti_object@@QEAA@PEBD@Z
; private: __cdecl bad_cast::bad_cast(char const * __ptr64 const * __ptr64) __ptr64
??0bad_cast@@AEAA@PEBQEBD@Z
; public: __cdecl bad_cast::bad_cast(char const * __ptr64 const & __ptr64) __ptr64
??0bad_cast@@QEAA@AEBQEBD@Z
; public: __cdecl bad_cast::bad_cast(class bad_cast const & __ptr64) __ptr64
??0bad_cast@@QEAA@AEBV0@@Z
; public: __cdecl bad_cast::bad_cast(char const * __ptr64) __ptr64
??0bad_cast@@QEAA@PEBD@Z
; public: __cdecl bad_typeid::bad_typeid(class bad_typeid const & __ptr64) __ptr64
??0bad_typeid@@QEAA@AEBV0@@Z
; public: __cdecl bad_typeid::bad_typeid(char const * __ptr64) __ptr64
??0bad_typeid@@QEAA@PEBD@Z
; public: __cdecl exception::exception(char const * __ptr64 const & __ptr64) __ptr64
??0exception@@QEAA@AEBQEBD@Z
; public: __cdecl exception::exception(char const * __ptr64 const & __ptr64,int) __ptr64
??0exception@@QEAA@AEBQEBDH@Z
; public: __cdecl exception::exception(class exception const & __ptr64) __ptr64
??0exception@@QEAA@AEBV0@@Z
; public: __cdecl exception::exception(void) __ptr64
??0exception@@QEAA@XZ
; public: virtual __cdecl __non_rtti_object::~__non_rtti_object(void) __ptr64
??1__non_rtti_object@@UEAA@XZ
; public: virtual __cdecl bad_cast::~bad_cast(void) __ptr64
??1bad_cast@@UEAA@XZ
; public: virtual __cdecl bad_typeid::~bad_typeid(void) __ptr64
??1bad_typeid@@UEAA@XZ
; public: virtual __cdecl exception::~exception(void) __ptr64
??1exception@@UEAA@XZ
; public: virtual __cdecl type_info::~type_info(void) __ptr64
??1type_info@@UEAA@XZ
; void * __ptr64 __cdecl operator new(unsigned __int64)
; GCC = __Znwy
??2@YAPEAX_K@Z
; void __cdecl operator delete(void * __ptr64)
; GCC = __ZdlPv
??3@YAXPEAX@Z
; public: class __non_rtti_object & __ptr64 __cdecl __non_rtti_object::operator=(class __non_rtti_object const & __ptr64) __ptr64
??4__non_rtti_object@@QEAAAEAV0@AEBV0@@Z
; public: class bad_cast & __ptr64 __cdecl bad_cast::operator=(class bad_cast const & __ptr64) __ptr64
??4bad_cast@@QEAAAEAV0@AEBV0@@Z
; public: class bad_typeid & __ptr64 __cdecl bad_typeid::operator=(class bad_typeid const & __ptr64) __ptr64
??4bad_typeid@@QEAAAEAV0@AEBV0@@Z
; public: class exception & __ptr64 __cdecl exception::operator=(class exception const & __ptr64) __ptr64
??4exception@@QEAAAEAV0@AEBV0@@Z
; public: int __cdecl type_info::operator==(class type_info const & __ptr64)const  __ptr64
??8type_info@@QEBAHAEBV0@@Z
; public: int __cdecl type_info::operator!=(class type_info const & __ptr64)const  __ptr64
??9type_info@@QEBAHAEBV0@@Z
; const  __non_rtti_object::`vftable'
??_7__non_rtti_object@@6B@
; const  bad_cast::`vftable'
??_7bad_cast@@6B@
; const  bad_typeid::`vftable'
??_7bad_typeid@@6B@
; const  exception::`vftable'
??_7exception@@6B@
; public: void __cdecl bad_cast::`default constructor closure'(void) __ptr64
??_Fbad_cast@@QEAAXXZ
; public: void __cdecl bad_typeid::`default constructor closure'(void) __ptr64
??_Fbad_typeid@@QEAAXXZ
; void * __ptr64 __cdecl operator new[](unsigned __int64)
; GNU = __Znay
??_U@YAPEAX_K@Z
; void __cdecl operator delete[](void * __ptr64)
; GNU = __ZdaPv
??_V@YAXPEAX@Z
; int (__cdecl*__cdecl _query_new_handler(void))(unsigned __int64)
; GNU = __Z18_query_new_handlerv
?_query_new_handler@@YAP6AH_K@ZXZ
; int __cdecl _query_new_mode(void)
; GNU = __Z15_query_new_modev
?_query_new_mode@@YAHXZ
; int (__cdecl*__cdecl _set_new_handler(int (__cdecl*)(unsigned __int64)))(unsigned __int64)
; GNU = __Z16_set_new_handlerPFiyE
?_set_new_handler@@YAP6AH_K@ZP6AH0@Z@Z
; int __cdecl _set_new_mode(int)
; GNU = __Z13_set_new_modei
?_set_new_mode@@YAHH@Z
; void (__cdecl*__cdecl _set_se_translator(void (__cdecl*)(unsigned int,struct _EXCEPTION_POINTERS * __ptr64)))(unsigned int,struct _EXCEPTION_POINTERS * __ptr64)
; GNU = __Z18_set_se_translatorPFvjP19_EXCEPTION_POINTERSE
?_set_se_translator@@YAP6AXIPEAU_EXCEPTION_POINTERS@@@ZP6AXI0@Z@Z
; public: int __cdecl type_info::before(class type_info const & __ptr64)const  __ptr64
?before@type_info@@QEBAHAEBV1@@Z
; public: char const * __ptr64 __cdecl type_info::name(void)const  __ptr64
?name@type_info@@QEBAPEBDXZ
; public: char const * __ptr64 __cdecl type_info::raw_name(void)const  __ptr64
?raw_name@type_info@@QEBAPEBDXZ
; void (__cdecl*__cdecl set_new_handler(void (__cdecl*)(void)))(void)
; GNU = __Z15set_new_handlerPFvvE
?set_new_handler@@YAP6AXXZP6AXXZ@Z
; void (__cdecl*__cdecl set_terminate(void (__cdecl*)(void)))(void)
; GNU = __Z13set_terminatePFvvE
?set_terminate@@YAP6AXXZP6AXXZ@Z
; void (__cdecl*__cdecl set_unexpected(void (__cdecl*)(void)))(void)
; GNU = __Z14set_unexpectedPFvvE
?set_unexpected@@YAP6AXXZP6AXXZ@Z
; void __cdecl terminate(void)
; GNU = __Z9terminatev
?terminate@@YAXXZ
; void __cdecl unexpected(void)
; GNU = __Z10unexpectedv
?unexpected@@YAXXZ
; public: virtual char const * __ptr64 __cdecl exception::what(void)const  __ptr64
?what@exception@@UEBAPEBDXZ
#endif

#ifdef DEF_ARM32
??0__non_rtti_object@@QAA@ABV0@@Z
??0__non_rtti_object@@QAA@PBD@Z
??0bad_cast@@AAA@PBQBD@Z
??0bad_cast@@QAA@ABV0@@Z
??0bad_cast@@QAA@PBD@Z
??0bad_typeid@@QAA@ABV0@@Z
??0bad_typeid@@QAA@PBD@Z
??0exception@@QAA@ABQBD@Z
??0exception@@QAA@ABQBDH@Z
??0exception@@QAA@ABV0@@Z
??0exception@@QAA@XZ
??1__non_rtti_object@@UAA@XZ
??1bad_cast@@UAA@XZ
??1bad_typeid@@UAA@XZ
??1exception@@UAA@XZ
??1type_info@@UAA@XZ
??2@YAPAXI@Z
??2@YAPAXIHPBDH@Z
??3@YAXPAX@Z
??4__non_rtti_object@@QAAAAV0@ABV0@@Z
??4bad_cast@@QAAAAV0@ABV0@@Z
??4bad_typeid@@QAAAAV0@ABV0@@Z
??4exception@@QAAAAV0@ABV0@@Z
??8type_info@@QBAHABV0@@Z
??9type_info@@QBAHABV0@@Z
??_7__non_rtti_object@@6B@ DATA
??_7bad_cast@@6B@ DATA
??_7bad_typeid@@6B@ DATA
??_7exception@@6B@ DATA
??_Fbad_cast@@QAAXXZ
??_Fbad_typeid@@QAAXXZ
??_U@YAPAXI@Z
??_U@YAPAXIHPBDH@Z
??_V@YAXPAX@Z
_CallMemberFunction0
_CallMemberFunction1
_CallMemberFunction2
__ExceptionPtrAssign
__ExceptionPtrCompare
__ExceptionPtrCopy
__ExceptionPtrCopyException
__ExceptionPtrCreate
__ExceptionPtrCurrentException
__ExceptionPtrDestroy
__ExceptionPtrRethrow
__ExceptionPtrSwap
__ExceptionPtrToBool
?_query_new_handler@@YAP6AHI@ZXZ
?_set_new_handler@@YAP6AHI@ZP6AHI@Z@Z
?_set_new_mode@@YAHH@Z
?_set_se_translator@@YAP6AXIPAU_EXCEPTION_POINTERS@@@ZP6AXI0@Z@Z
?before@type_info@@QBAHABV1@@Z
?name@type_info@@QBAPBDXZ
?raw_name@type_info@@QBAPBDXZ
?set_terminate@@YAP6AXXZP6AXXZ@Z
?set_unexpected@@YAP6AXXZP6AXXZ@Z
?terminate@@YAXXZ
?unexpected@@YAXXZ
?what@exception@@UBAPBDXZ
#endif

#ifdef DEF_ARM32
_CrtCheckMemory
_CrtDbgBreak
_CrtDbgReport
_CrtDbgReportV
_CrtDbgReportW
_CrtDbgReportWV
_CrtDoForAllClientObjects
_CrtDumpMemoryLeaks
_CrtIsMemoryBlock
_CrtIsValidHeapPointer
_CrtIsValidPointer
_CrtMemCheckpoint
_CrtMemDifference
_CrtMemDumpAllObjectsSince
_CrtMemDumpStatistics
_CrtReportBlockType
_CrtSetAllocHook
_CrtSetBreakAlloc
_CrtSetDbgBlockType
_CrtSetDbgFlag
_CrtSetDumpClient
_CrtSetReportFile
_CrtSetReportHook
_CrtSetReportHook2
_CrtSetReportMode
#endif
F_I386(_CxxThrowException@8)
F_NON_I386(_CxxThrowException)
F_I386(_EH_prolog)
_Getdays
_Getmonths
_Gettnames
_HUGE DATA
_Strftime
F_ARM_ANY(_W_Getdays)
F_ARM_ANY(_W_Getmonths)
F_ARM_ANY(_W_Gettnames)
F_ARM_ANY(_Wcsftime)
_XcptFilter
F_ARM_ANY(__AdjustPointer)
F_NON_I386(__C_specific_handler)
__CppXcptFilter
F_I386(__CxxCallUnwindDtor)
F_I386(__CxxCallUnwindVecDtor)
F_I386(__CxxDetectRethrow)
F_I386(__CxxExceptionFilter)
F_X86_ANY(__CxxFrameHandler)
F_ARM_ANY(__CxxFrameHandler3)
F_I386(__CxxLongjmpUnwind)
F_I386(__CxxQueryExceptionSize)
F_I386(__CxxRegisterExceptionObject)
F_I386(__CxxUnregisterExceptionObject)
__DestructExceptionObject
__RTCastToVoid
__RTDynamicCast
__RTtypeid
__STRINGTOLD
F_NON_I386(___lc_codepage_func)
___lc_collate_cp_func
___lc_handle_func
; ___mb_cur_max_func exists (on all archs) since XP. Earlier, this function
; was never used, but the __mb_cur_max data symbol was accessed instead.
; For i386 we provide this function as a statically linked helper, that uses
; __mb_cur_max, to avoid forcing a dependency on XP here.
F_NON_I386(___mb_cur_max_func)
F_X86_ANY(___setlc_active_func)
F_X86_ANY(___unguarded_readlc_active_add_func)
__argc DATA
__argv DATA
__badioinfo DATA
F_I386(__buffer_overrun)
__crtCompareStringA
__crtCompareStringW
__crtGetLocaleInfoW
__crtGetStringTypeW
__crtLCMapStringA
__crtLCMapStringW
F_ARM_ANY(__daylight)
__dllonexit
__doserrno
F_ARM_ANY(__dstbias)
__fpecode
__getmainargs
F_X86_ANY(__initenv DATA)
__iob_func
__isascii
__iscsym
__iscsymf
F_I386(__lc_clike)
F_X86_ANY(__lc_codepage DATA)
F_X86_ANY(__lc_collate_cp DATA)
__lc_handle DATA
__lconv_init
__mb_cur_max DATA
#ifdef DEF_I386
__p___argc
__p___argv
__p___initenv
__p___mb_cur_max
__p___wargv
__p___winitenv
__p__acmdln
__p__amblksiz
__p__commode
__p__daylight
__p__dstbias
__p__environ
__p__fileinfo
__p__fmode
__p__iob
__p__mbcasemap
__p__mbctype
__p__osver
__p__pctype
__p__pgmptr
__p__pwctype
__p__timezone
__p__tzname
__p__wcmdln
__p__wenviron
__p__winmajor
__p__winminor
__p__winver
__p__wpgmptr
#endif
__pctype_func
__pioinfo DATA
__pwctype_func
__pxcptinfoptrs
F_I386(__security_error_handler)
__set_app_type
F_I386(__set_buffer_overrun_handler)
F_X86_ANY(__setlc_active DATA)
__setusermatherr
F_ARM_ANY(__strncnt)
F_X86_ANY(__threadhandle)
F_X86_ANY(__threadid)
__toascii
__uncaught_exception
__unDName
__unDNameEx
F_X86_ANY(__unguarded_readlc_active DATA)
__wargv DATA
__wcserror
F_NON_I386(__wcserror_s)
F_ARM_ANY(__wcsncnt)
__wgetmainargs
F_X86_ANY(__winitenv DATA)
F_I386(_abnormal_termination)
F_NON_I386(_abs64)
F_NON_I386(llabs == _abs64)
F_NON_I386(imaxabs == _abs64)
_access
; _access_s Replaced by emu
_acmdln DATA
#ifdef DEF_I386
_adj_fdiv_m16i
_adj_fdiv_m32
_adj_fdiv_m32i
_adj_fdiv_m64
_adj_fdiv_r
_adj_fdivr_m16i
_adj_fdivr_m32
_adj_fdivr_m32i
_adj_fdivr_m64
_adj_fpatan
_adj_fprem
_adj_fprem1
_adj_fptan
_adjust_fdiv DATA
#endif
_aexit_rtn DATA
_aligned_free
F_ARM_ANY(_aligned_free_dbg)
_aligned_malloc
F_ARM_ANY(_aligned_malloc_dbg)
_aligned_offset_malloc
F_ARM_ANY(_aligned_offset_malloc_dbg)
_aligned_offset_realloc
F_ARM_ANY(_aligned_offset_realloc_dbg)
_aligned_realloc
F_ARM_ANY(_aligned_realloc_dbg)
_amsg_exit
_assert
_atodbl
_atodbl_l
_atof_l
_atoflt_l
_atoi64
atoll == _atoi64
_atoi64_l
_atoll_l == _atoi64_l
_atoi_l
_atol_l
_atoldbl
F_NON_I386(_atoldbl_l)
F_X86_ANY(_beep)
_beginthread
_beginthreadex
_c_exit
_cabs DATA
_callnewh
F_ARM_ANY(_calloc_dbg)
_cexit
_cgets
; _cgets_s replaced by emu
_cgetws
; _cgetws_s replaced by emu
_chdir
_chdrive
_chgsign
F_NON_I386(_chgsignf)
_chmod
F_I386(_chkesp)
_chsize
; _chsize_s replaced by emu
F_ARM_ANY(_chvalidator)
F_ARM_ANY(_chvalidator_l)
_clearfp
_close
_commit
_commode DATA
_control87
_controlfp
; _controlfp_s replaced by emu
_copysign
F_NON_I386(_copysignf)
_cprintf
_cprintf_l
_cprintf_p
_cprintf_p_l
; _cprintf_s Replaced by emu
; _cprintf_s_l likewise.
_cputs
_cputws
F_I386(_CRT_RTC_INIT)
_creat
F_ARM_ANY(_create_locale)
F_ARM32(_crtAssertBusy)
F_ARM32(_crtBreakAlloc)
F_ARM32(_crtDbgFlag)
_cscanf
_cscanf_l
_cscanf_s
_cscanf_s_l
F_ARM_ANY(_ctime32)
F_I386(_ctime32 == ctime)
; _ctime32_s replaced by emu
_ctime64
; _ctime64_s replaced by emu
_ctype F_I386(DATA)
_cwait
_cwprintf
_cwprintf_l
_cwprintf_p
_cwprintf_p_l
; _cwprintf_s Replaced by emu
; _cwprintf_s_l Likewise.
_cwscanf
_cwscanf_l
_cwscanf_s
_cwscanf_s_l
F_X86_ANY(_dstbias DATA)
_daylight DATA
_difftime32 F_I386(== difftime)
_difftime64
_dup
_dup2
_ecvt
_ecvt_s
_endthread
_endthreadex
F_X86_ANY(_environ DATA)
_eof
_errno
F_I386(_except_handler2)
F_I386(_except_handler3)
_execl
_execle
_execlp
_execlpe
_execv
_execve
_execvp
_execvpe
_exit
_expand
F_ARM_ANY(_expand_dbg)
_fcloseall
_fcvt
_fcvt_s
_fdopen
_fgetchar
_fgetwchar
_filbuf
F_X86_ANY(_fileinfo DATA)
_filelength
_filelengthi64
_fileno
_findclose
_findfirst
F32(_findfirst32 == _findfirst)
_findfirst64
_findfirsti64
F32(_findfirst32i64 == _findfirsti64)
F64(_findfirst64i32 == _findfirst)
_findnext
F32(_findnext32 == _findnext)
_findnext64
_findnexti64
F32(_findnext32i64 == _findnexti64)
F64(_findnext64i32 == _findnext)
_finite
F_NON_I386(_finitef)
_flsbuf
_flushall
_fmode DATA
_fpclass
F_X64(_fpclassf)
F_I386(_fpieee_flt)
F_ARM_ANY(_fpieee_flt)
_fpreset DATA
_fprintf_l
_fprintf_p
_fprintf_p_l
_fprintf_s_l
_fputchar
_fputwchar
F_ARM_ANY(_free_dbg)
F_ARM_ANY(_free_locale)
F_ARM_ANY(_freea)
F_NON_I386(_fscanf_l)
F_NON_I386(_fscanf_s_l)
F_ARM_ANY(_fseeki64)
_fsopen
_fstat
F32(_fstat32 == _fstat)
_fstat64
_fstati64
F64(_fstat64i32 == _fstat)
_ftime
F_I386(_ftime32 == _ftime)
F_NON_I386(_ftime32)
_ftime32_s
_ftime64
_ftime64_s
F32(_ftime_s == _ftime32_s)
F64(_ftime_s == _ftime64_s)
F_I386(_ftol)
_fullpath
F_ARM_ANY(_fullpath_dbg)
_futime
F_I386(_futime32 == _futime)
F_NON_I386(_futime32)
_futime64
_fwprintf_l
_fwprintf_p
_fwprintf_p_l
_fwprintf_s_l
_fwscanf_l
_fwscanf_s_l
_gcvt
_gcvt_s
F_ARM_ANY(_get_current_locale)
_get_doserrno
F_ARM_ANY(_get_environ)
F_ARM_ANY(_get_errno)
F_ARM_ANY(_get_fileinfo)
F_ARM_ANY(_get_fmode)
F_X86_ANY(_get_heap_handle)
_get_osfhandle
;_get_output_format provided by emu
F_X86_ANY(_get_sbh_threshold)
F_ARM_ANY(_get_wenviron)
_getch
_getche
_getcwd
_getdcwd
_getdiskfree
_getdllprocaddr
_getdrive
F_X86_ANY(_getdrives)
_getmaxstdio
_getmbcp
F_X86_ANY(_getpid)
F_X86_ANY(_getsystime)
_getw
_getwch
_getwche
F_X86_ANY(_getws)
F_I386(_global_unwind2)
_gmtime32 F_I386(== gmtime)
; _gmtime32_s replaced by emu
_gmtime64
; _gmtime64_s replaced by emu
F_X86_ANY(_heapadd)
_heapchk
_heapmin
F_X86_ANY(_heapset)
F_X86_ANY(_heapused)
_heapwalk
_hypot
F_NON_I386(_hypotf)
_i64toa
_i64toa_s
_i64tow
_i64tow_s
_initterm
F_ARM_ANY(_initterm_e)
F_I386(_inp)
F_I386(_inpd)
F_I386(_inpw)
F_ARM_ANY(_invalid_parameter)
_iob DATA
_isalnum_l
_isalpha_l
_isatty
_iscntrl_l
_isctype
_isctype_l
_isdigit_l
_isgraph_l
_isleadbyte_l
_islower_l
_ismbbalnum
_ismbbalnum_l
_ismbbalpha
_ismbbalpha_l
_ismbbgraph
_ismbbgraph_l
_ismbbkalnum
_ismbbkalnum_l
_ismbbkana
_ismbbkana_l
_ismbbkprint
_ismbbkprint_l
_ismbbkpunct
_ismbbkpunct_l
_ismbblead
_ismbblead_l
_ismbbprint
_ismbbprint_l
_ismbbpunct
_ismbbpunct_l
_ismbbtrail
_ismbbtrail_l
_ismbcalnum
_ismbcalnum_l
_ismbcalpha
_ismbcalpha_l
_ismbcdigit
_ismbcdigit_l
_ismbcgraph
_ismbcgraph_l
_ismbchira
_ismbchira_l
_ismbckata
_ismbckata_l
_ismbcl0
_ismbcl0_l
_ismbcl1
_ismbcl1_l
_ismbcl2
_ismbcl2_l
_ismbclegal
_ismbclegal_l
_ismbclower
_ismbclower_l
_ismbcprint
_ismbcprint_l
_ismbcpunct
_ismbcpunct_l
_ismbcspace
_ismbcspace_l
_ismbcsymbol
_ismbcsymbol_l
_ismbcupper
_ismbcupper_l
_ismbslead
_ismbslead_l
_ismbstrail
_ismbstrail_l
_isnan
F_X64(_isnanf)
_isprint_l
_isspace_l
_isupper_l
_iswalnum_l
_iswalpha_l
_iswcntrl_l
_iswctype_l
_iswdigit_l
_iswgraph_l
_iswlower_l
_iswprint_l
_iswpunct_l
_iswspace_l
_iswupper_l
_iswxdigit_l
_isxdigit_l
_itoa
_itoa_s
_itow
_itow_s
_j0
_j1
_jn
_kbhit
_lfind
F_X86_ANY(_loaddll)
F_NON_I386(_lfind_s)
F_X64(_local_unwind)
F_I386(_local_unwind2)
_localtime32 F_I386(== localtime)
; _localtime32_s replaced by emu
_localtime64
; _localtime64_s replaced by emu
_lock
_locking
_logb
F_NON_I386(_logbf)
F_I386(_longjmpex)
_lrotl
_lrotr
_lsearch
F_NON_I386(_lsearch_s)
_lseek
_lseeki64
_ltoa
F_NON_I386(_ltoa_s)
_ltow
F_NON_I386(_ltow_s)
_makepath
_makepath_s
F_ARM_ANY(_malloc_dbg)
_mbbtombc
_mbbtombc_l
_mbbtype
_mbcasemap F_NON_I386(DATA)
_mbccpy
_mbccpy_l
_mbccpy_s
_mbccpy_s_l
_mbcjistojms
_mbcjistojms_l
_mbcjmstojis
_mbcjmstojis_l
_mbclen
_mbclen_l
_mbctohira
_mbctohira_l
_mbctokata
_mbctokata_l
_mbctolower
_mbctolower_l
_mbctombb
_mbctombb_l
_mbctoupper
_mbctoupper_l
_mbctype DATA
_mblen_l
_mbsbtype
_mbsbtype_l
_mbscat
_mbscat_s
_mbscat_s_l
_mbschr
_mbschr_l
_mbscmp
_mbscmp_l
_mbscoll
_mbscoll_l
_mbscpy
_mbscpy_s
_mbscpy_s_l
_mbscspn
_mbscspn_l
_mbsdec
_mbsdec_l
_mbsdup
_mbsicmp
_mbsicmp_l
_mbsicoll
_mbsicoll_l
_mbsinc
_mbsinc_l
_mbslen
_mbslen_l
_mbslwr
_mbslwr_l
_mbslwr_s
_mbslwr_s_l
_mbsnbcat
_mbsnbcat_l
_mbsnbcat_s
_mbsnbcat_s_l
_mbsnbcmp
_mbsnbcmp_l
_mbsnbcnt
_mbsnbcnt_l
_mbsnbcoll
_mbsnbcoll_l
_mbsnbcpy
_mbsnbcpy_l
_mbsnbcpy_s
_mbsnbcpy_s_l
_mbsnbicmp
_mbsnbicmp_l
_mbsnbicoll
_mbsnbicoll_l
_mbsnbset
_mbsnbset_l
_mbsnbset_s
_mbsnbset_s_l
_mbsncat
_mbsncat_l
_mbsncat_s
_mbsncat_s_l
_mbsnccnt
_mbsnccnt_l
_mbsncmp
_mbsncmp_l
_mbsncoll
_mbsncoll_l
_mbsncpy
_mbsncpy_l
_mbsncpy_s
_mbsncpy_s_l
_mbsnextc
_mbsnextc_l
_mbsnicmp
_mbsnicmp_l
_mbsnicoll
_mbsnicoll_l
_mbsninc
_mbsninc_l
_mbsnlen
_mbsnlen_l
_mbsnset
_mbsnset_l
_mbsnset_s
_mbsnset_s_l
_mbspbrk
_mbspbrk_l
_mbsrchr
_mbsrchr_l
_mbsrev
_mbsrev_l
_mbsset
_mbsset_l
_mbsset_s
_mbsset_s_l
_mbsspn
_mbsspn_l
_mbsspnp
_mbsspnp_l
_mbsstr
_mbsstr_l
_mbstok
_mbstok_l
_mbstok_s
_mbstok_s_l
_mbstowcs_l
_mbstowcs_s_l
_mbstrlen
_mbstrlen_l
_mbstrnlen
_mbstrnlen_l
_mbsupr
_mbsupr_l
_mbsupr_s
_mbsupr_s_l
_mbtowc_l
_memccpy
F_ARM_ANY(_memcpy_strict_align)
_memicmp
_memicmp_l
_mkdir
_mkgmtime
_mkgmtime32
_mkgmtime64
_mktemp
; _mktemp_s replaced by emu
F_I386(_mktime32 == mktime)
F_ARM_ANY(_mktime32)
_mktime64
_msize
F_ARM_ANY(_msize_dbg)
_nextafter
F_X64(_nextafterf)
_onexit
_open
_open_osfhandle
F_X86_ANY(_osplatform DATA)
_osver DATA
F_I386(_outp)
F_I386(_outpd)
F_I386(_outpw)
_pclose
_pctype DATA
_pgmptr DATA
_pipe
_popen
_printf_l
_printf_p
_printf_p_l
_printf_s_l
_purecall
_putch
_putenv
_putenv_s
_putw
_putwch
_putws
_pwctype DATA
_read
F_ARM_ANY(_realloc_dbg)
_resetstkoflw
_rmdir
_rmtmp
_rotl
F_NON_I386(_rotl64)
_rotr
F_NON_I386(_rotr64)
#ifdef DEF_I386
_safe_fdiv
_safe_fdivr
_safe_fprem
_safe_fprem1
#endif
_scalb
F_X64(_scalbf)
_scanf_l
_scanf_s_l
F_NON_I386(_scprintf) ; i386 _scprintf replaced by emu
_scprintf_l
_scprintf_p_l
_scwprintf
_scwprintf_l
_scwprintf_p_l
_searchenv
_searchenv_s
F_I386(_seh_longjmp_unwind)
_set_controlfp
_set_doserrno
F_ARM_ANY(_set_errno)
_set_error_mode
F_ARM_ANY(_set_fileinfo)
F_ARM_ANY(_set_fmode)
; Does not seem to present even on Win7 msvcrt
;_set_purecall_handler
F_X86_ANY(_set_sbh_threshold)
; _set_output_format provided by emu
F_I386(_set_SSE2_enable)
F_I386(_set_security_error_handler)
F_X86_ANY(_seterrormode)
_setjmp
F_I386(_setjmp3)
F_NON_I386(_setjmpex)
F_X86_ANY(_setmaxstdio)
_setmbcp
_setmode
F_X86_ANY(_setsystime)
F_X86_ANY(_sleep)
_snprintf
_snprintf_c
_snprintf_c_l
_snprintf_l
_snprintf_s
_snprintf_s_l
_snscanf
_snscanf_l
_snscanf_s
_snscanf_s_l
_snwprintf
snwprintf == _snwprintf
_snwprintf_l
_snwprintf_s
_snwprintf_s_l
_snwscanf
_snwscanf_l
_snwscanf_s
_snwscanf_s_l
_sopen
; _sopen_s replaced by emu
_spawnl
_spawnle
_spawnlp
_spawnlpe
_spawnv
_spawnve
_spawnvp
_spawnvpe
_splitpath
_splitpath_s
_sprintf_l
_sprintf_p_l
_sprintf_s_l
_sscanf_l
_sscanf_s_l
_stat
_stat64
_stati64
F32(_stat32 == _stat)
F64(_stat64i32 == _stat)
_statusfp
_strcmpi
_strcoll_l
_strdate
; _strdate_s replaced by emu
_strdup
F_ARM_ANY(_strdup_dbg)
_strerror
_strerror_s
_stricmp
_stricmp_l
_stricoll
_stricoll_l
_strlwr
strlwr == _strlwr
_strlwr_l
_strlwr_s
_strlwr_s_l
_strncoll
_strncoll_l
_strnicmp
_strnicmp_l
_strnicoll
_strnicoll_l
_strnset
_strnset_s
_strrev
_strset
_strset_s
_strtime
; _strtime_s replaced by emu
_strtod_l
_strtoi64
strtoll == _strtoi64
strtoimax == _strtoi64
_strtoi64_l
_strtoll_l == _strtoi64_l
_strtoimax_l == _strtoi64_l
_strtol_l
_strtoui64
strtoull == _strtoui64
strtoumax == _strtoui64
_strtoui64_l
_strtoull_l == _strtoui64_l
_strtoumax_l == _strtoui64_l
_strtoul_l
_strupr
_strupr_l
_strupr_s
_strupr_s_l
_strxfrm_l
_swab
_swprintf == swprintf
F_NON_I386(_swprintf_c)
_swprintf_c_l
_swprintf_p_l
_swprintf_s_l
_swscanf_l
_swscanf_s_l
_sys_errlist DATA
_sys_nerr DATA
_tell
_telli64
_tempnam
F_ARM_ANY(_tempnam_dbg)
F_I386(_time32 == time)
F_ARM_ANY(_time32)
_time64
_timezone DATA
_tolower
_tolower_l
_toupper
_toupper_l
_towlower_l
_towupper_l
_tzname DATA
_tzset
_ui64toa
_ui64toa_s
_ui64tow
_ui64tow_s
_ultoa
_ultoa_s
_ultow
_ultow_s
_umask
; _umask_s replaced by emu
_ungetch
_ungetwch
_unlink
F_X86_ANY(_unloaddll)
_unlock
_utime
F_I386(_utime32 == _utime)
F_NON_I386(_utime32)
_utime64
_vcprintf
_vcprintf_l
_vcprintf_p
_vcprintf_p_l
; _vcprintf_s Replaced by emu
; _vcprintf_s_l Likewise.
_vcwprintf
_vcwprintf_l
_vcwprintf_p
_vcwprintf_p_l
; _vcwprintf_s Replaced by emu
; _vcwprintf_s_l Likewise.
_vfprintf_l
_vfprintf_p
_vfprintf_p_l
_vfprintf_s_l
_vfwprintf_l
_vfwprintf_p
_vfwprintf_p_l
_vfwprintf_s_l
_vprintf_l
_vprintf_p
_vprintf_p_l
_vprintf_s_l
F_NON_I386(_vscprintf) ; i386 _vscprintf replaced by emu
_vscprintf_l
_vscprintf_p_l
_vscwprintf
_vscwprintf_l
_vscwprintf_p_l
_vsnprintf
_vsnprintf_c
_vsnprintf_c_l
_vsnprintf_l
_vsnprintf_s
_vsnprintf_s_l
_vsnwprintf
vsnwprintf == _vsnwprintf
_vsnwprintf_l
_vsnwprintf_s
_vsnwprintf_s_l
_vsprintf_l
_vsprintf_p
_vsprintf_p_l
_vsprintf_s_l
_vswprintf F_I386(== vswprintf)
_vswprintf_c
_vswprintf_c_l
_vswprintf_l
_vswprintf_p_l
_vswprintf_s_l
_vwprintf_l
_vwprintf_p
_vwprintf_p_l
_vwprintf_s_l
_waccess
; _waccess_s Replaced by emu
_wasctime
; _wasctime_s Replaced by emu
F_NON_I386(_wassert)
_wchdir
_wchmod
_wcmdln DATA
_wcreat
_wcscoll_l
_wcsdup
F_ARM_ANY(_wcsdup_dbg)
_wcserror
_wcserror_s
_wcsftime_l
_wcsicmp
_wcsicmp_l
_wcsicoll
_wcsicoll_l
_wcslwr
wcslwr == _wcslwr
_wcslwr_l
_wcslwr_s
_wcslwr_s_l
_wcsncoll
_wcsncoll_l
_wcsnicmp
_wcsnicmp_l
_wcsnicoll
_wcsnicoll_l
_wcsnset
_wcsnset_s
_wcsrev
_wcsset
_wcsset_s
F_ARM_ANY(_wcstod_l)
_wcstoi64
_wcstoi64_l
_wcstol_l
_wcstombs_l
_wcstombs_s_l
_wcstoui64
_wcstoui64_l
_wcstoul_l
_wcsupr
_wcsupr_l
_wcsupr_s
_wcsupr_s_l
_wcsxfrm_l
_wctime
F_I386(_wctime32 == _wctime)
F_ARM_ANY(_wctime32)
; _wctime32_s replaced by emu
_wctime64
; _wctime64_s replaced by emu
_wctomb_l
_wctomb_s_l
_wctype
F_X86_ANY(_wenviron DATA)
_wexecl
_wexecle
_wexeclp
_wexeclpe
_wexecv
_wexecve
_wexecvp
_wexecvpe
_wfdopen
_wfindfirst
F32(_wfindfirst32 == _wfindfirst)
_wfindfirst64
_wfindfirsti64
F32(_wfindfirst32i64 == _wfindfirsti64)
F64(_wfindfirst64i32 == _wfindfirst)
_wfindnext
F32(_wfindnext32 == _wfindnext)
_wfindnext64
_wfindnexti64
F32(_wfindnext32i64 == _wfindnexti64)
F64(_wfindnext64i32 == _wfindnext)
_wfopen
_wfopen_s
_wfreopen
_wfreopen_s
_wfsopen
_wfullpath
F_ARM_ANY(_wfullpath_dbg)
_wgetcwd
_wgetdcwd
_wgetenv
_wgetenv_s
_winmajor DATA
_winminor DATA
_winput_s
F_X86_ANY(_winver DATA)
_wmakepath
_wmakepath_s
_wmkdir
_wmktemp
; _wmktemp_s replaced by emu
_wopen
_woutput_s
_wperror
_wpgmptr DATA
_wpopen
_wprintf_l
_wprintf_p
_wprintf_p_l
_wprintf_s_l
_wputenv
_wputenv_s
_wremove
_wrename
_write
_wrmdir
_wscanf_l
_wscanf_s_l
_wsearchenv
_wsearchenv_s
_wsetlocale
_wsopen
_wsopen_s
_wspawnl
_wspawnle
_wspawnlp
_wspawnlpe
_wspawnv
_wspawnve
_wspawnvp
_wspawnvpe
_wsplitpath
_wsplitpath_s
_wstat
_wstat64
_wstati64
F32(_wstat32 == _wstat)
F64(_wstat64i32 == _wstat)
_wstrdate
; _wstrdate_s replaced by emu
_wstrtime
; _wstrtime_s replaced by emu
_wsystem
_wtempnam
F_ARM_ANY(_wtempnam_dbg)
_wtmpnam
_wtmpnam_s
_wtof
_wtof_l
_wtoi
_wtoi64
_wtoi64_l
_wtoi_l
_wtol
_wtol_l
_wunlink
_wutime
F_I386(_wutime32 == _wutime)
F_NON_I386(_wutime32)
_wutime64
_y0
_y1
_yn
abort
abs
acos
F_NON_I386(acosf F_X86_ANY(DATA))
F_ARM_ANY(acosl == acos)
asctime
; asctime_s replaced by emu
asin
F_NON_I386(asinf F_X86_ANY(DATA))
F_ARM_ANY(asinl == asin)
atan
atan2 F_X86_ANY(DATA)
F_NON_I386(atan2f F_X86_ANY(DATA))
F_ARM_ANY(atan2l == atan2)
F_NON_I386(atanf F_X86_ANY(DATA))
F_ARM_ANY(atanl == atan)
atexit DATA
atof
atoi
atol
bsearch
bsearch_s
F_ARM_ANY(btowc)
calloc
ceil F_X86_ANY(DATA)
F_NON_I386(ceilf F_X86_ANY(DATA))
F_ARM_ANY(ceill == ceil)
clearerr
clearerr_s
clock
cos F_X86_ANY(DATA)
F_NON_I386(cosf F_X86_ANY(DATA))
F_ARM_ANY(cosl == cos)
cosh
F_NON_I386(coshf DATA)
ctime
difftime
div
exit
exp F_X86_ANY(DATA)
F_NON_I386(expf F_X86_ANY(DATA))
F_ARM_ANY(expl == exp)
fabs DATA
F_ARM_ANY(fabsf)
fclose
feof
ferror
fflush
fgetc
fgetpos
fgets
fgetwc
fgetws
floor F_X86_ANY(DATA)
F_NON_I386(floorf F_X86_ANY(DATA))
F_ARM_ANY(floorl == floor)
fmod F_X86_ANY(DATA)
F_NON_I386(fmodf F_X86_ANY(DATA))
F_ARM_ANY(fmodl == fmod)
fopen
fopen_s
fprintf
__ms_fprintf == fprintf
fprintf_s
fputc
fputs
fputwc
fputws
fread
free
freopen
freopen_s
frexp DATA
fscanf
__ms_fscanf == fscanf
fscanf_s
fseek
fsetpos
ftell
fwprintf
__ms_fwprintf == fwprintf
fwprintf_s
fwrite
fwscanf
__ms_fwscanf == fwscanf
fwscanf_s
getc
getchar
getenv
getenv_s
F_X86_ANY(gets)
getwc
getwchar
gmtime
is_wctype
isalnum
isalpha
iscntrl
isdigit
isgraph
isleadbyte
islower
isprint
ispunct
isspace
isupper
iswalnum
iswalpha
iswascii
iswcntrl
iswctype
iswdigit
iswgraph
iswlower
iswprint
iswpunct
iswspace
iswupper
iswxdigit
isxdigit
labs
ldexp F_X86_ANY(DATA)
ldiv
localeconv
localtime
log F_X86_ANY(DATA)
log10
F_NON_I386(log10f F_X86_ANY(DATA))
F_ARM_ANY(log10l == log10)
F_NON_I386(logf F_X86_ANY(DATA))
F_ARM_ANY(logl == log)
longjmp
malloc
mblen
F_ARM_ANY(mbrlen)
F_ARM_ANY(mbrtowc)
F_ARM_ANY(mbsdup_dbg)
F_ARM_ANY(mbsrtowcs)
mbsrtowcs_s
mbstowcs
mbstowcs_s
mbtowc
memchr
memcmp
memcpy
; memcpy_s replaced by emu
memmove
; memmove_s replaced by emu
memset
mktime
modf DATA
F_NON_I386(modff DATA)
perror
pow DATA
F_NON_I386(powf DATA)
printf
__ms_printf == printf
printf_s
putc
putchar
puts
putwc
putwchar
qsort
qsort_s
raise
rand
; rand_s replaced by emu
realloc
remove
rename
rewind
scanf
__ms_scanf == scanf
scanf_s
setbuf
F_NON_I386(setjmp)
setlocale
setvbuf
signal
sin F_X86_ANY(DATA)
F_NON_I386(sinf F_X86_ANY(DATA))
F_ARM_ANY(sinl == sin)
; if we implement sinh, we can set it DATA only.
sinh
F_NON_I386(sinhf DATA)
sprintf
__ms_sprintf == sprintf
; sprintf_s replaced by emu
sqrt DATA
F_NON_I386(sqrtf DATA)
srand
sscanf
__ms_sscanf == sscanf
sscanf_s
strcat
strcat_s
strchr
strcmp
strcoll
strcpy
strcpy_s
strcspn
strerror
; strerror_s replaced by emu
strftime
strlen
strncat
strncat_s
strncmp
strncpy
strncpy_s
; strnlen replaced by emu
strpbrk
strrchr
strspn
strstr
strtod
strtok
strtok_s
strtol
strtoul
strxfrm
swprintf
__ms_swprintf == swprintf
swprintf_s
swscanf
__ms_swscanf == swscanf
swscanf_s
system
tan
F_NON_I386(tanf F_X86_ANY(DATA))
F_ARM_ANY(tanl == tan)
; if we implement tanh, we can set it to DATA only.
tanh
F_ARM_ANY(tanhf)
time F_NON_I386(== _time64)
tmpfile
tmpfile_s
tmpnam
tmpnam_s
tolower
toupper
towlower
towupper
ungetc
ungetwc
F_ARM_ANY(utime)
vfprintf
__ms_vfprintf == vfprintf
vfprintf_s
vfwprintf
__ms_vfwprintf == vfwprintf
vfwprintf_s
vprintf
__ms_vprintf == vprintf
vprintf_s
vsprintf
__ms_vsprintf == vsprintf
; vsprintf_s replaced by emu
vswprintf
__ms_vswprintf == vswprintf
vswprintf_s
vwprintf
__ms_vwprintf == vwprintf
vwprintf_s
F_ARM_ANY(wcrtomb)
wcrtomb_s
wcscat
wcscat_s
wcschr
wcscmp
wcscoll
wcscpy
wcscpy_s
wcscspn
wcsftime
wcslen
wcsncat
wcsncat_s
wcsncmp
wcsncpy
wcsncpy_s
; We provide replacement implementation in libmingwex
wcsnlen DATA
wcspbrk
wcsrchr
F_ARM_ANY(wcsrtombs)
wcsrtombs_s
wcsspn
wcsstr
wcstod
wcstok
wcstok_s
wcstol
wcstombs
wcstombs_s
wcstoul
wcsxfrm
F_ARM_ANY(wctob)
wctomb
wctomb_s
wprintf
__ms_wprintf == wprintf
wprintf_s
wscanf
__ms_wscanf == wscanf
wscanf_s
