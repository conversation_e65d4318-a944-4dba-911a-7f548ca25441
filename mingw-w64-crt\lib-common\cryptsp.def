;
; Definition file of CRYPTSP.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "CRYPTSP.dll"
EXPORTS
CheckSignatureInFile
CryptAcquireContextA
CryptAcquireContextW
CryptContextAddRef
CryptCreateHash
CryptDecrypt
CryptDeriveKey
CryptDestroyHash
CryptDestroyKey
CryptDuplicateHash
CryptDuplicateKey
CryptEncrypt
CryptEnumProviderTypesA
CryptEnumProviderTypesW
CryptEnumProvidersA
CryptEnumProvidersW
CryptExportKey
CryptGenKey
CryptGenRandom
CryptGetDefaultProviderA
CryptGetDefaultProviderW
CryptGetHashParam
CryptGetKeyParam
CryptGetProvParam
CryptGetUserKey
CryptHashData
CryptHashSessionKey
CryptImportKey
CryptReleaseContext
CryptSetHashParam
CryptSetKeyParam
CryptSetProvParam
CryptSetP<PERSON><PERSON>etProviderExA
CryptSetProviderExW
CryptSetProviderW
CryptSignHashA
CryptSignHashW
CryptVerifySignatureA
CryptVerifySignatureW
SystemFunction006
SystemFunction007
SystemFunction008
SystemFunction009
SystemFunction010
SystemFunction011
SystemFunction012
SystemFunction013
SystemFunction014
SystemFunction015
SystemFunction016
SystemFunction018
SystemFunction020
SystemFunction021
SystemFunction022
SystemFunction023
SystemFunction024
SystemFunction025
SystemFunction026
SystemFunction027
SystemFunction030
SystemFunction031
SystemFunction032
SystemFunction033
SystemFunction035
