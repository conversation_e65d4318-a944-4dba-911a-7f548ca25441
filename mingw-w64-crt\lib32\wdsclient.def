;
; Definition file of WdsClient.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WdsClient.dll"
EXPORTS
CallBack_WdsClient_ConnectToImageStore@8
CallBack_WdsClient_DetectWdsMode@8
CallBack_WdsClient_GetImageList@8
CallBack_WdsClient_ImageSelectionDone@8
CallBack_WdsClient_ProcessCmdLine@8
GetServerParamsFromBootPacket@8
Module_Init_WdsClient@16
g_Kernel32 DATA
g_Mpr DATA
g_Wdscore DATA
g_Wdslib DATA
g_hSession DATA
