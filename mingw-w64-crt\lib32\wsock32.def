LIBRARY WSOCK32.DLL
EXPORTS
AcceptEx@32
EnumProtocolsA@12
EnumProtocolsW@12
GetAcceptExSockaddrs@32
GetAddressByNameA@40
GetAddressByNameW@40
GetNameByTypeA@12
GetNameByTypeW@12
GetServiceA@28
GetServiceW@28
GetTypeByNameA@8
GetTypeByNameW@8
NPLoadNameSpaces@12
SetServiceA@24
SetServiceW@24
TransmitFile@28
WSAAsyncGetHostByAddr@28
WSAAsyncGetHostByName@20
WSAAsyncGetProtoByName@20
WSAAsyncGetProtoByNumber@20
WSAAsyncGetServByName@24
WSAAsyncGetServByPort@24
WSAAsyncSelect@16
WSACancelAsyncRequest@4
WSACancelBlockingCall@0
WSACleanup@0
WSAGetLastError@0
WSAIsBlocking@0
WSARecvEx@16
WSASetBlockingH<PERSON>@4
WSASetLastError@4
WSAStartup@8
WSAUnhook<PERSON><PERSON>ingHook@0
WSApSetPostRoutine@4
__WSAFDIsSet@8
accept@12
bind@12
closesocket@4
connect@12
dn_expand@20
gethostbyaddr@12
gethostbyname@4
gethostname@8
getnetbyname@4
getpeername@12
getprotobyname@4
getprotobynumber@4
getservbyname@8
getservbyport@8
getsockname@12
getsockopt@20
htonl@4
htons@4
inet_addr@4
inet_network@4
inet_ntoa@4
ioctlsocket@12
listen@8
ntohl@4
ntohs@4
rcmd@24
recv@16
recvfrom@24
rexec@24
rresvport@4
s_perror@8
select@20
send@16
sendto@24
sethostname@8
setsockopt@20
shutdown@8
socket@12
