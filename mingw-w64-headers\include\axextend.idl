/*
 * Copyright (C) 2002 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

interface IAMAnalogVideoDecoder;
interface IAMAnalogVideoEncoder;
interface IAMAudioInputMixer;
interface IAMAudioRendererStats;
interface IAMBufferNegotiation;
interface IAMCameraControl;
interface IAMCertifiedOutputProtection;
interface IAMClockSlave;
interface IAMCopyCaptureFileProgress;
interface IAMCrossbar;
interface IAMDevMemoryAllocator;
interface IAMDevMemoryControl;
interface IAMDroppedFrames;
interface IAMExtDevice;
interface IAMExtTransport;
interface IAMGraphStreams;
interface IAMLatency;
interface IAMOpenProgress;
interface IAMOverlayFX;
interface IAMPhysicalPinInfo;
interface IAMPushSource;
interface IAMStreamConfig;
interface IAMTimecodeDisplay;
interface IAMTimecodeGenerator;
interface IAMTimecodeReader;
interface IAMTVTuner;
interface IAMVfwCaptureDialogs;
interface IAMVfwCompressDialogs;
interface IAMVideoCompression;
interface IAMVideoDecimationProperties;
interface IAMVideoProcAmp;
interface IAMGraphBuilderCallback;
interface ICaptureGraphBuilder;
interface ICaptureGraphBuilder2;
interface IConfigAviMux;
interface IConfigInterleaving;
interface IDecimateVideoImage;
interface IDrawVideoImage;
interface IEnumRegFilters;
interface IEnumStreamIdMap;
interface IFileSourceFilter;
interface IFileSinkFilter;
interface IFileSinkFilter2;
interface IFilterMapper;
interface IFilterMapper2;
interface IGraphBuilder;
interface IKsPropertySet;
interface IMediaEventSink;
interface IMpeg2Demultiplexer;
interface IMPEG2StreamIdMap;
interface IOverlay;
interface IOverlayNotify;
interface IOverlayNotify2;
interface IQualityControl;
interface ISeekingPassThru;

#include <axextendenums.h>

typedef struct
{
    CLSID Clsid;
    LPWSTR Name;
} REGFILTER;

typedef enum tagTVAudioMode {
    AMTVAUDIO_MODE_MONO         = 0x0001,
    AMTVAUDIO_MODE_STEREO       = 0x0002,
    AMTVAUDIO_MODE_LANG_A       = 0x0010,
    AMTVAUDIO_MODE_LANG_B       = 0x0020,
    AMTVAUDIO_MODE_LANG_C       = 0x0040,
    AMTVAUDIO_PRESET_STEREO     = 0x0200,
    AMTVAUDIO_PRESET_LANG_A     = 0x1000,
    AMTVAUDIO_PRESET_LANG_B     = 0x2000,
    AMTVAUDIO_PRESET_LANG_C     = 0x4000
} TVAudioMode;

[
object,
uuid(56a868a4-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IEnumRegFilters : IUnknown
{
    HRESULT Next
        ( [in]  ULONG cFilters,
          [out] REGFILTER ** apRegFilter,
          [out] ULONG * pcFetched
        );

    HRESULT Skip(
        [in] ULONG cFilters
    );

    HRESULT Reset(void);

    HRESULT Clone(
        [out] IEnumRegFilters **ppEnum
    );
}


typedef IEnumRegFilters *PENUMREGFILTERS;

[
object,
uuid(56a868a3-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IFilterMapper : IUnknown
{
    enum { MERIT_PREFERRED     = 0x800000,
           MERIT_NORMAL        = 0x600000,
           MERIT_UNLIKELY      = 0x400000,
           MERIT_DO_NOT_USE    = 0x200000,
           MERIT_SW_COMPRESSOR = 0x100000,
           MERIT_HW_COMPRESSOR = 0x100050
         };

    HRESULT RegisterFilter
        ( [in] CLSID   clsid,
          [in] LPCWSTR Name,
          [in] DWORD   dwMerit
        );

    HRESULT RegisterFilterInstance
        ( [in]  CLSID  clsid,
          [in]  LPCWSTR Name,
          [out] CLSID *MRId
        );


    HRESULT RegisterPin
        ( [in] CLSID   Filter,
          [in] LPCWSTR Name,
          [in] BOOL    bRendered,
          [in] BOOL    bOutput,
          [in] BOOL    bZero,
          [in] BOOL    bMany,
          [in] CLSID   ConnectsToFilter,
          [in] LPCWSTR ConnectsToPin
        );

    HRESULT RegisterPinType
        ( [in] CLSID   clsFilter,
          [in] LPCWSTR strName,
          [in] CLSID   clsMajorType,
          [in] CLSID   clsSubType
        );


    HRESULT UnregisterFilter
        ( [in] CLSID  Filter
        );


    HRESULT UnregisterFilterInstance
        ( [in] CLSID  MRId
        );


    HRESULT UnregisterPin
        ( [in] CLSID   Filter,
          [in] LPCWSTR Name
        );

    HRESULT EnumMatchingFilters
       ( [out] IEnumRegFilters **ppEnum,
         [in]  DWORD dwMerit,
         [in]  BOOL  bInputNeeded,
         [in]  CLSID clsInMaj,
         [in]  CLSID clsInSub,
         [in]  BOOL  bRender,
         [in]  BOOL  bOutputNeeded,
         [in]  CLSID clsOutMaj,
         [in]  CLSID clsOutSub
       );

}

typedef struct
{
    const CLSID * clsMajorType;
    const CLSID * clsMinorType;
} REGPINTYPES;

typedef struct
{
    LPWSTR           strName;
    BOOL             bRendered;
    BOOL             bOutput;
    BOOL             bZero;
    BOOL             bMany;
    const CLSID *    clsConnectsToFilter;
    const WCHAR *    strConnectsToPin;
    UINT             nMediaTypes;
    const REGPINTYPES * lpMediaType;
} REGFILTERPINS;

typedef struct
{
    CLSID clsMedium;
    DWORD dw1;
    DWORD dw2;
} REGPINMEDIUM;

enum
{
    REG_PINFLAG_B_ZERO = 0x1,
    REG_PINFLAG_B_RENDERER = 0x2,
    REG_PINFLAG_B_MANY = 0x4,
    REG_PINFLAG_B_OUTPUT = 0x8
};


typedef struct
{
    DWORD dwFlags;
    UINT             cInstances;
    UINT             nMediaTypes;
    [size_is(nMediaTypes)] const REGPINTYPES * lpMediaType;
    UINT             nMediums;
    [size_is(nMediums)] const REGPINMEDIUM *lpMedium;
    const CLSID      *clsPinCategory;
} REGFILTERPINS2;

typedef struct
{
    DWORD dwVersion;
    DWORD dwMerit;
    [switch_is(dwVersion)] [switch_type(DWORD)] union
    {
        [case(1)]
            struct
            {
                ULONG cPins;
                [size_is(cPins)] const REGFILTERPINS *rgPins;
            };

        [case(2)]
            struct
            {
                ULONG cPins2;
                [size_is(cPins2)] const REGFILTERPINS2 *rgPins2;
            };

        [default]
            ;
    };

} REGFILTER2;


[
object,
uuid(b79bb0b0-33c1-11d1-abe1-00a0c905f375),
pointer_default(unique)
]
interface IFilterMapper2 : IUnknown
{
    HRESULT CreateCategory
        ( [in] REFCLSID clsidCategory,
          [in] DWORD dwCategoryMerit,
          [in] LPCWSTR Description
          );

    HRESULT UnregisterFilter
        ( [in] const CLSID *pclsidCategory,
          [in] LPCOLESTR szInstance,
          [in] REFCLSID Filter
        );

    HRESULT RegisterFilter
        ( [in] REFCLSID clsidFilter,
          [in] LPCWSTR Name,
          [in, out] IMoniker **ppMoniker,
          [in] const CLSID *pclsidCategory,
          [in] LPCOLESTR szInstance,
          [in] const REGFILTER2 *prf2
        );

    HRESULT EnumMatchingFilters
       ( [out] IEnumMoniker **ppEnum,
         [in]  DWORD dwFlags,
         [in]  BOOL bExactMatch,
         [in]  DWORD dwMerit,
         [in]  BOOL  bInputNeeded,
         [in]  DWORD cInputTypes,
         [size_is(cInputTypes*2)]  const GUID *pInputTypes,
         [in]  const REGPINMEDIUM *pMedIn,
         [in]  const CLSID *pPinCategoryIn,
         [in]  BOOL  bRender,
         [in]  BOOL  bOutputNeeded,
         [in]  DWORD cOutputTypes,
         [size_is(cOutputTypes*2)]  const GUID *pOutputTypes,
         [in]  const REGPINMEDIUM *pMedOut,
         [in]  const CLSID *pPinCategoryOut
       );
}

[
object,
uuid(b79bb0b1-33c1-11d1-abe1-00a0c905f375),
pointer_default(unique)
]
interface IFilterMapper3 : IFilterMapper2
{
    HRESULT GetICreateDevEnum( [out] ICreateDevEnum **ppEnum );
}

typedef enum tagQualityMessageType
{
    Famine,
    Flood
} QualityMessageType;

typedef struct tagQuality
{
    QualityMessageType Type;
    LONG               Proportion;
    REFERENCE_TIME       Late;
    REFERENCE_TIME       TimeStamp;
} Quality;

typedef IQualityControl *PQUALITYCONTROL;

[
object,
uuid(56a868a5-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IQualityControl : IUnknown
{
    HRESULT Notify
        ( [in] IBaseFilter * pSelf,
          [in] Quality q
        );

    HRESULT SetSink
        ( [in] IQualityControl * piqc
        );
}

enum
{
    CK_NOCOLORKEY  = 0x0,
    CK_INDEX       = 0x1,
    CK_RGB         = 0x2
};

typedef struct tagCOLORKEY
{
    DWORD    KeyType;
    DWORD    PaletteIndex;
    COLORREF LowColorValue;
    COLORREF HighColorValue;
} COLORKEY;

enum
{
    ADVISE_NONE = 0x0,
    ADVISE_CLIPPING = 0x1,
    ADVISE_PALETTE = 0x2,
    ADVISE_COLORKEY = 0x4,
    ADVISE_POSITION = 0x8,
    ADVISE_DISPLAY_CHANGE = 0x10
};

const DWORD ADVISE_ALL = ADVISE_CLIPPING |
                         ADVISE_PALETTE  |
                         ADVISE_COLORKEY |
                         ADVISE_POSITION;

const DWORD ADVISE_ALL2 = ADVISE_ALL |
                          ADVISE_DISPLAY_CHANGE;

cpp_quote("#ifndef _WINGDI_")

/* already defined in wingdi.h, but needed for WIDL */

typedef struct _RGNDATAHEADER
{
    DWORD dwSize;
    DWORD iType;
    DWORD nCount;
    DWORD nRgnSize;
    RECT  rcBound;
} RGNDATAHEADER;

typedef struct _RGNDATA
{
    RGNDATAHEADER rdh;
    char Buffer[1];
} RGNDATA;

cpp_quote("#endif /* _WINGDI_ */")

[
object,
local,
uuid(56a868a0-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IOverlayNotify : IUnknown
{
    HRESULT OnPaletteChange(
        [in] DWORD dwColors,
        [in] const PALETTEENTRY *pPalette);

    HRESULT OnClipChange(
        [in] const RECT *pSourceRect,
        [in] const RECT *pDestinationRect,
        [in] const RGNDATA *pRgnData);

    HRESULT OnColorKeyChange([in] const COLORKEY *pColorKey);

    HRESULT OnPositionChange([in] const RECT *pSourceRect,
                             [in] const RECT *pDestinationRect);
}

typedef IOverlayNotify *POVERLAYNOTIFY;

cpp_quote("#if 0")
typedef HANDLE HMONITOR;
cpp_quote("#endif /* 0 */")

[
object,
local,
uuid(680EFA10-D535-11D1-87C8-00A0C9223196),
pointer_default(unique)
]
interface IOverlayNotify2 : IOverlayNotify
{
    HRESULT OnDisplayChange(
        HMONITOR hMonitor);
}

typedef IOverlayNotify2 *POVERLAYNOTIFY2;

[
object,
local,
uuid(56a868a1-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IOverlay : IUnknown
{
    HRESULT GetPalette(
        [out] DWORD *pdwColors,
        [out] PALETTEENTRY **ppPalette);

    HRESULT SetPalette(
        [in] DWORD dwColors,
        [in] PALETTEENTRY *pPalette);

    HRESULT GetDefaultColorKey([out] COLORKEY *pColorKey);

    HRESULT GetColorKey([out] COLORKEY *pColorKey);

    HRESULT SetColorKey([in,out] COLORKEY *pColorKey);

    HRESULT GetWindowHandle([out] HWND *pHwnd);

    HRESULT GetClipList([out] RECT *pSourceRect,
                        [out] RECT *pDestinationRect,
                        [out] RGNDATA **ppRgnData);

    HRESULT GetVideoPosition([out] RECT *pSourceRect,
                             [out] RECT *pDestinationRect);

    HRESULT Advise(
        [in] IOverlayNotify *pOverlayNotify,
        [in] DWORD dwInterests);

    HRESULT Unadvise();
}

typedef IOverlay *POVERLAY;

[
object,
uuid(56a868a2-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IMediaEventSink : IUnknown
{
    HRESULT Notify(
        [in] LONG EventCode,
        [in] LONG_PTR EventParam1,
        [in] LONG_PTR EventParam2
    );
}

typedef IMediaEventSink *PMEDIAEVENTSINK;

[
object,
uuid(56a868a6-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IFileSourceFilter : IUnknown
{
    HRESULT Load(
        [in] LPCOLESTR pszFileName,
        [in, unique] const AM_MEDIA_TYPE *pmt
    );

    HRESULT GetCurFile(
        [out] LPOLESTR *ppszFileName,
        [out] AM_MEDIA_TYPE *pmt
    );
}

typedef IFileSourceFilter *PFILTERFILESOURCE;

[
object,
uuid(a2104830-7c70-11cf-8bce-00aa00a3f1a6),
pointer_default(unique)
]
interface IFileSinkFilter : IUnknown
{
    HRESULT SetFileName(
        [in] LPCOLESTR pszFileName,
        [in, unique] const AM_MEDIA_TYPE *pmt
    );

    HRESULT GetCurFile(
        [out] LPOLESTR *ppszFileName,
        [out] AM_MEDIA_TYPE *pmt
    );
}

typedef IFileSinkFilter *PFILTERFILESINK;

[
        object,
        uuid(00855B90-CE1B-11d0-BD4F-00A0C911CE86),
        pointer_default(unique)
]
interface IFileSinkFilter2 : IFileSinkFilter
{
    HRESULT SetMode(
        [in] DWORD dwFlags
    );

    HRESULT GetMode(
        [out] DWORD *pdwFlags
    );
}

typedef IFileSinkFilter2 *PFILESINKFILTER2;

typedef enum
{
    AM_FILE_OVERWRITE = 0x00000001,
} AM_FILESINK_FLAGS;

[
object,
uuid(56a868a9-0ad4-11ce-b03a-0020af0ba770),
pointer_default(unique)
]
interface IGraphBuilder : IFilterGraph
{
    HRESULT Connect
        ( [in] IPin * ppinOut,
          [in] IPin * ppinIn
        );

    HRESULT Render
        ( [in] IPin * ppinOut
        );

    HRESULT RenderFile
        ( [in] LPCWSTR lpcwstrFile,
          [in, unique] LPCWSTR lpcwstrPlayList
        );

    HRESULT AddSourceFilter
        ( [in]      LPCWSTR lpcwstrFileName,
          [in, unique]      LPCWSTR lpcwstrFilterName,
          [out]     IBaseFilter* *ppFilter
        );

    HRESULT SetLogFile
        ( [in]      DWORD_PTR hFile
        );

    HRESULT Abort();

    HRESULT ShouldOperationContinue();
}


[
    object,
    uuid(bf87b6e0-8c27-11d0-b3f0-00aa003761c5),
    pointer_default(unique)
]
interface ICaptureGraphBuilder : IUnknown {

    HRESULT SetFiltergraph(
        [in] IGraphBuilder *pfg);

    HRESULT GetFiltergraph(
        [out] IGraphBuilder **ppfg);

    HRESULT SetOutputFileName(
        [in] const GUID *pType,
        [in] LPCOLESTR lpstrFile,
        [out] IBaseFilter **ppf,
        [out] IFileSinkFilter **ppSink);

    [local] HRESULT FindInterface(
        [in, unique] const GUID *pCategory,
        [in] IBaseFilter *pf,
        [in] REFIID riid,
        [out] void **ppint);

    [call_as(FindInterface)] HRESULT RemoteFindInterface(
        [in, unique] const GUID *pCategory,
        [in] IBaseFilter *pf,
        [in] REFIID riid,
        [out] IUnknown **ppint);

    HRESULT RenderStream(
        [in] const GUID *pCategory,
        [in] IUnknown *pSource,
        [in] IBaseFilter *pfCompressor,
        [in] IBaseFilter *pfRenderer);

    HRESULT ControlStream(
        [in] const GUID *pCategory,
        [in] IBaseFilter *pFilter,
        [in] REFERENCE_TIME *pstart,
        [in] REFERENCE_TIME *pstop,
        [in] WORD wStartCookie,
        [in] WORD wStopCookie);

    HRESULT AllocCapFile(
        [in] LPCOLESTR lpstr,
        [in] DWORDLONG dwlSize);

    HRESULT CopyCaptureFile(
        [in] LPOLESTR lpwstrOld,
        [in] LPOLESTR lpwstrNew,
        [in] int fAllowEscAbort,
        [in] IAMCopyCaptureFileProgress *pCallback);
}

[
object,
uuid(670d1d20-a068-11d0-b3f0-00aa003761c5),
pointer_default(unique)
]
interface IAMCopyCaptureFileProgress : IUnknown
{
    HRESULT Progress(
        [in] int iProgress);
}

[
    object,
    uuid(93E5A4E0-2D50-11d2-ABFA-00A0C9C6E38D),
    pointer_default(unique)
]
interface ICaptureGraphBuilder2 : IUnknown
{
    HRESULT SetFiltergraph(
        [in] IGraphBuilder *pfg);

    HRESULT GetFiltergraph(
        [out] IGraphBuilder **ppfg);

    HRESULT SetOutputFileName(
        [in] const GUID *pType,
        [in] LPCOLESTR lpstrFile,
        [out] IBaseFilter **ppf,
        [out] IFileSinkFilter **ppSink);

    [local] HRESULT FindInterface(
        [in] const GUID *pCategory,
        [in] const GUID *pType,
        [in] IBaseFilter *pf,
        [in] REFIID riid,
        [out] void **ppint);

    [call_as(FindInterface)] HRESULT RemoteFindInterface(
        [in] const GUID *pCategory,
        [in] const GUID *pType,
        [in] IBaseFilter *pf,
        [in] REFIID riid,
        [out] IUnknown **ppint);

    HRESULT RenderStream(
        [in] const GUID *pCategory,
        [in] const GUID *pType,
        [in] IUnknown *pSource,
        [in] IBaseFilter *pfCompressor,
        [in] IBaseFilter *pfRenderer);

    HRESULT ControlStream(
        [in] const GUID *pCategory,
        [in] const GUID *pType,
        [in] IBaseFilter *pFilter,
        [in] REFERENCE_TIME *pstart,
        [in] REFERENCE_TIME *pstop,
        [in] WORD wStartCookie,
        [in] WORD wStopCookie);

    HRESULT AllocCapFile(
        [in] LPCOLESTR lpstr,
        [in] DWORDLONG dwlSize);

    HRESULT CopyCaptureFile(
        [in] LPOLESTR lpwstrOld,
        [in] LPOLESTR lpwstrNew,
        [in] int fAllowEscAbort,
        [in] IAMCopyCaptureFileProgress *pCallback);

    HRESULT FindPin(
        [in] IUnknown *pSource,
        [in] PIN_DIRECTION pindir,
        [in] const GUID *pCategory,
        [in] const GUID *pType,
        [in] BOOL fUnconnected,
        [in] int num,
        [out] IPin **ppPin);
}

[
    local,
    object,
    uuid(5acd6aa0-f482-11ce-8b67-00aa00a3f1a6),
    pointer_default(unique)
]
interface IConfigAviMux : IUnknown
{
    HRESULT SetMasterStream(
        [in] LONG iStream);

    HRESULT GetMasterStream(
        [out] LONG *pStream);

    HRESULT SetOutputCompatibilityIndex(
        [in] BOOL fOldIndex);

    HRESULT GetOutputCompatibilityIndex(
        [out] BOOL *pfOldIndex);
}

[
    local,
    object,
    uuid(bee3d220-157b-11d0-bd23-00a0c911ce86),
    pointer_default(unique)
]
interface IConfigInterleaving : IUnknown
{
    typedef enum InterleavingMode {
        INTERLEAVE_NONE,
        INTERLEAVE_CAPTURE,
        INTERLEAVE_FULL,
        INTERLEAVE_NONE_BUFFERED
    } InterleavingMode;

    HRESULT put_Mode(
        [in] InterleavingMode mode);

    HRESULT get_Mode(
        [out] InterleavingMode *pMode);

    HRESULT put_Interleaving(
        [in] const REFERENCE_TIME *prtInterleave,
        [in] const REFERENCE_TIME *prtPreroll);

    HRESULT get_Interleaving(
        [out] REFERENCE_TIME *prtInterleave,
        [out] REFERENCE_TIME *prtPreroll);
}

[
    local,
    object,
    uuid(9fd52741-176d-4b36-8f51-ca8f933223be),
    pointer_default(unique)
]
interface IAMClockSlave : IUnknown
{
    HRESULT SetErrorTolerance(
        [in] DWORD dwTolerance);

    HRESULT GetErrorTolerance(
        [out] DWORD *pdwTolerance);
}

typedef struct _AMCOPPSignature {
    BYTE Signature[256];
} AMCOPPSignature;

typedef struct _AMCOPPCommand {
    GUID macKDI;
    GUID guidCommandID;
    DWORD dwSequence;
    DWORD bSizeData;
    BYTE CommandData[4056];
} AMCOPPCommand, *LPAMCOPPCommand;

typedef struct _AMCOPPStatusInput {
    GUID rApp;
    GUID guidStatusRequestID;
    DWORD dwSequence;
    DWORD cbSizeData;
    BYTE StatusData[4056];
} AMCOPPStatusInput, *LPAMCOPPStatusInput;

typedef struct _AMCOPPStatusOutput {
    GUID macKDI;
    DWORD cbSizeData;
    BYTE COPPStatus[4076];
} AMCOPPStatusOutput, *LPAMCOPPStatusOutput;

[
    local,
    object,
    uuid(6feded3e-0ff1-4901-a2f1-43f7012c8515),
    pointer_default(unique)
]
interface IAMCertifiedOutputProtection : IUnknown
{
    HRESULT KeyExchange (
        [out] GUID* pRandom,
        [out] BYTE** VarLenCertGH,
        [out] DWORD* pdwLengthCertGH);

    HRESULT SessionSequenceStart(
        [in] AMCOPPSignature* pSig);

    HRESULT ProtectionCommand(
        [in] const AMCOPPCommand* cmd);

    HRESULT ProtectionStatus(
        [in] const AMCOPPStatusInput* pStatusInput,
        [out] AMCOPPStatusOutput* pStatusOutput);
};

enum _AM_RENSDEREXFLAGS
{
    AM_RENDEREX_RENDERTOEXISTINGRENDERERS = 0x01
};

[
    object,
    uuid(36b73882-c2c8-11cf-8b46-00805f6cef60),
    pointer_default(unique)
]
interface IFilterGraph2: IGraphBuilder
{
    HRESULT AddSourceFilterForMoniker(
          [in] IMoniker *pMoniker,
          [in] IBindCtx *pCtx,
          [in, unique] LPCWSTR lpcwstrFilterName,
          [out] IBaseFilter **ppFilter
    );

    HRESULT ReconnectEx
        ( [in] IPin * ppin,
          [in, unique] const AM_MEDIA_TYPE *pmt
        );

    HRESULT RenderEx( [in] IPin *pPinOut,
                      [in] DWORD dwFlags,
                      [in, out] DWORD *pvContext
                    );
}

[
    object,
    local,
    uuid(56a868bf-0ad4-11ce-b03a-0020af0ba770),
    pointer_default(unique)
]
interface IStreamBuilder : IUnknown
{
    HRESULT Render
        ( [in] IPin * ppinOut,
          [in] IGraphBuilder * pGraph
        );

    HRESULT Backout
        ( [in] IPin * ppinOut,
          [in] IGraphBuilder * pGraph
        );
}


/*****************************************************************************
 * IAMStreamConfig interface
 */
[
    object,
    uuid(c6e13340-30ac-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMStreamConfig : IUnknown
{
    typedef struct _VIDEO_STREAM_CONFIG_CAPS {
        GUID  guid;
        ULONG VideoStandard;
        SIZE  InputSize;
        SIZE  MinCroppingSize;
        SIZE  MaxCroppingSize;
        int   CropGranularityX;
        int   CropGranularityY;
        int   CropAlignX;
        int   CropAlignY;
        SIZE  MinOutputSize;
        SIZE  MaxOutputSize;
        int   OutputGranularityX;
        int   OutputGranularityY;
        int   StretchTapsX;
        int   StretchTapsY;
        int   ShrinkTapsX;
        int   ShrinkTapsY;
        LONGLONG MinFrameInterval;
        LONGLONG MaxFrameInterval;
        LONG  MinBitsPerSecond;
        LONG  MaxBitsPerSecond;
    } VIDEO_STREAM_CONFIG_CAPS;

    typedef struct _AUDIO_STREAM_CONFIG_CAPS {
        GUID guid;
        ULONG MinimumChannels;
        ULONG MaximumChannels;
        ULONG ChannelsGranularity;
        ULONG MinimumBitsPerSample;
        ULONG MaximumBitsPerSample;
        ULONG BitsPerSampleGranularity;
        ULONG MinimumSampleFrequency;
        ULONG MaximumSampleFrequency;
        ULONG SampleFrequencyGranularity;
    } AUDIO_STREAM_CONFIG_CAPS;

    HRESULT SetFormat( [in] AM_MEDIA_TYPE *pmt);
    HRESULT GetFormat( [in] AM_MEDIA_TYPE **pmt);
    HRESULT GetNumberOfCapabilities( [out] int *piCount, [out] int *piSize);
    HRESULT GetStreamCaps( [in] int iIndex, [out] AM_MEDIA_TYPE **pmt,
                          [out] BYTE *pSCC);
}


typedef enum tagVideoProcAmpProperty
{
    VideoProcAmp_Brightness,
    VideoProcAmp_Contrast,
    VideoProcAmp_Hue,
    VideoProcAmp_Saturation,
    VideoProcAmp_Sharpness,
    VideoProcAmp_Gamma,
    VideoProcAmp_ColorEnable,
    VideoProcAmp_WhiteBalance,
    VideoProcAmp_BacklightCompensation,
    VideoProcAmp_Gain
} VideoProcAmpProperty;

typedef enum tagVideoProcAmpFlags
{
    VideoProcAmp_Flags_Auto   = 0x0001,
    VideoProcAmp_Flags_Manual = 0x0002,
} VideoProcAmpFlags;

/*****************************************************************************
 * IAMVideoProcAmp interface
 */
[
    object,
    uuid(c6e13360-30ac-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMVideoProcAmp : IUnknown
{
    HRESULT GetRange( [in] LONG Property, [out] LONG *pMin, [out] LONG *pMax,
                     [out] LONG *pSteppingDelta, [out] LONG *pDefault,
                     [out] LONG *pCapsFlags);
    HRESULT Set( [in] LONG Property, [in] LONG lValue, [in] LONG Flags);
    HRESULT Get( [in] LONG Property, [out] LONG *lValue, [out] LONG *Flags);
}


[
        object,
        uuid(56a868aa-0ad4-11ce-b03a-0020af0ba770),
        pointer_default(unique)
]
interface IAsyncReader : IUnknown
{
    HRESULT RequestAllocator(
                [in]  IMemAllocator* pPreferred,
                [in]  ALLOCATOR_PROPERTIES* pProps,
                [out] IMemAllocator ** ppActual);

    HRESULT Request(
                [in] IMediaSample* pSample,
                [in] DWORD_PTR dwUser);

    HRESULT WaitForNext(
                [in]  DWORD dwTimeout,
                [out] IMediaSample** ppSample,
                [out] DWORD_PTR * pdwUser);

    HRESULT SyncReadAligned(
                [in] IMediaSample* pSample);

    HRESULT SyncRead(
                [in]  LONGLONG llPosition,
                [in]  LONG lLength,
                [out, size_is(lLength)]
                      BYTE* pBuffer);

    HRESULT Length(
                [out] LONGLONG* pTotal,
                [out] LONGLONG* pAvailable);

    HRESULT BeginFlush(void);

    HRESULT EndFlush(void);
}


[
    object,
    uuid(56a868ab-0ad4-11ce-b03a-0020af0ba770),
    pointer_default(unique)
]
interface IGraphVersion : IUnknown
{
    HRESULT QueryVersion(LONG* pVersion);
}

[
    object,
    uuid(56a868ad-0ad4-11ce-b03a-0020af0ba770),
    pointer_default(unique)
]
interface IResourceConsumer : IUnknown
{
    HRESULT AcquireResource(
        [in] LONG idResource);

    HRESULT ReleaseResource(
        [in] LONG idResource);
}


[
    object,
    uuid(56a868ac-0ad4-11ce-b03a-0020af0ba770),
    pointer_default(unique)
]
interface IResourceManager : IUnknown
{
    HRESULT Register(
        [in] LPCWSTR pName,
        [in] LONG   cResource,
        [out] LONG* plToken
        );

    HRESULT RegisterGroup(
        [in] LPCWSTR pName,
        [in] LONG cResource,
        [in, size_is(cResource)]
             LONG* palTokens,
        [out] LONG* plToken
        );

    HRESULT RequestResource(
        [in] LONG idResource,
        [in] IUnknown* pFocusObject,
        [in] IResourceConsumer* pConsumer
        );

    HRESULT NotifyAcquire(
        [in] LONG idResource,
        [in] IResourceConsumer* pConsumer,
        [in] HRESULT hr);

    HRESULT NotifyRelease(
        [in] LONG idResource,
        [in] IResourceConsumer* pConsumer,
        [in] BOOL bStillWant);

    HRESULT CancelRequest(
        [in] LONG idResource,
        [in] IResourceConsumer* pConsumer);

    HRESULT SetFocus(
        [in] IUnknown* pFocusObject);

    HRESULT ReleaseFocus(
        [in] IUnknown* pFocusObject);

}

typedef enum AM_STREAM_INFO_FLAGS
{
    AM_STREAM_INFO_START_DEFINED   = 0x01,
    AM_STREAM_INFO_STOP_DEFINED    = 0x02,
    AM_STREAM_INFO_DISCARDING      = 0x04,
    AM_STREAM_INFO_STOP_SEND_EXTRA = 0x10
} AM_STREAM_INFO_FLAGS;

typedef struct AM_STREAM_INFO
{
    REFERENCE_TIME tStart;
    REFERENCE_TIME tStop;
    DWORD dwStartCookie;
    DWORD dwStopCookie;
    DWORD dwFlags;
} AM_STREAM_INFO;

[
    local,
    object,
    uuid(36b73881-c2c8-11cf-8b46-00805f6cef60),
    pointer_default(unique)
]
interface IAMStreamControl : IUnknown
{
    HRESULT StartAt(
        [in] const REFERENCE_TIME *ptStart,
        [in] DWORD dwCookie);

    HRESULT StopAt(
        [in] const REFERENCE_TIME *ptStop,
        [in] BOOL bSendExtra,
        [in] DWORD dwCookie);

    HRESULT GetInfo(
        [out] AM_STREAM_INFO *pInfo);
}

cpp_quote("#ifndef _IKsPropertySet_")
cpp_quote("#define _IKsPropertySet_")
cpp_quote("#define KSPROPERTY_SUPPORT_GET  1")
cpp_quote("#define KSPROPERTY_SUPPORT_SET  2")

typedef enum AMPROPERTY_PIN
{
    AMPROPERTY_PIN_CATEGORY,
    AMPROPERTY_PIN_MEDIUM,
} AMPROPERTY_PIN;

[
    object,
    uuid(31efac30-515c-11d0-a9aa-00aa0061be93),
    pointer_default(unique),
    local
]
interface IKsPropertySet : IUnknown
{
    HRESULT Set( [in] REFGUID guidPropSet,
                 [in] DWORD dwPropID,
                 [in, size_is(cbInstanceData)] LPVOID pInstanceData,
                 [in] DWORD cbInstanceData,
                 [in, size_is(cbPropData)] LPVOID pPropData,
                 [in] DWORD cbPropData );

    HRESULT Get( [in] REFGUID guidPropSet,
                 [in] DWORD dwPropID,
                 [in, size_is(cbInstanceData)] LPVOID pInstanceData,
                 [in] DWORD cbInstanceData,
                 [out, size_is(cbPropData)] LPVOID pPropData,
                 [in] DWORD cbPropData,
                 [out] DWORD *pcbReturned );

    HRESULT QuerySupported( [in] REFGUID guidPropSet,
                            [in] DWORD dwPropID,
                            [out] DWORD *pTypeSupport);
}
cpp_quote("#endif /* _IKsPropertySet_ */")

[
    local,
    object,
    uuid(6025a880-c0d5-11d0-bd4e-00a0c911ce86),
    pointer_default(unique)
]
interface IMediaPropertyBag : IPropertyBag
{
    typedef IMediaPropertyBag *LPMEDIAPROPERTYBAG;

    HRESULT EnumProperty(
            [in]  ULONG iProperty,
            [in, out] VARIANT *pvarPropertyName,
            [in, out] VARIANT *pvarPropertyValue);
}


[
    local,
    object,
    uuid(5738e040-b67f-11d0-bd4d-00a0c911ce86),
    pointer_default(unique)
]
interface IPersistMediaPropertyBag : IPersist
{
    HRESULT InitNew(void);

    HRESULT Load(
            [in] IMediaPropertyBag *pPropBag,
            [in] IErrorLog *pErrorLog);

    HRESULT Save(
            [in] IMediaPropertyBag *pPropBag,
            [in] BOOL fClearDirty,
            [in] BOOL fSaveAllProperties);

    typedef IPersistMediaPropertyBag *LPPERSISTMEDIAPROPERTYBAG;
}

[
    object,
    uuid(36b73883-c2c8-11cf-8b46-00805f6cef60),
    pointer_default(unique)
]
interface ISeekingPassThru : IUnknown
{
    HRESULT Init( [in] BOOL bSupportRendering, [in] IPin *pPin);
}

enum _AM_FILTER_MISC_FLAGS
{
    AM_FILTER_MISC_FLAGS_IS_RENDERER = 0x1,
    AM_FILTER_MISC_FLAGS_IS_SOURCE = 0x2
};

[
    local,
    object,
    uuid(2dd74950-a890-11d1-abe8-00a0c905f375),
    pointer_default(unique)
]
interface IAMFilterMiscFlags : IUnknown
{
    ULONG GetMiscFlags();
}

[
    local,
    object,
    uuid(4995f511-9ddb-4f12-bd3b-f04611807b79),
    pointer_default(unique)
]
interface IAMGraphBuilderCallback : IUnknown
{
    HRESULT SelectedFilter(
        [in] IMoniker *pMon);

    HRESULT CreatedFilter(
        [in] IBaseFilter *pFil);
}

[
    local,
    object,
    uuid(d8d715a0-6e5e-11d0-b3f0-00aa003761c5),
    pointer_default(unique)
]
interface IAMVfwCaptureDialogs : IUnknown
{
    HRESULT HasDialog(
            [in] int dialog);
    HRESULT ShowDialog(
            [in] int dialog,
            [in] HWND hwnd);
    HRESULT SendDriverMessage(
            [in] int dialog,
            [in] int msg,
            [in] long data1,
            [in] long data2);
}

[
    local,
    object,
    uuid(c6e13370-30ac-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMCameraControl : IUnknown
{
    HRESULT GetRange([in] long property, [out] long *min, [out] long *max,
        [out] long *stepping_delta, [out] long *default_value, [out] long *flags);
    HRESULT Set([in] long property, [in] long property_value, [in] long flags);
    HRESULT Get([in] long property, [out] long *property_value, [out] long *flags);
}

[
    local,
    object,
    uuid(6a2e0670-28e4-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMVideoControl : IUnknown
{
    HRESULT GetCaps([in] IPin *pin, [out] long *flags);
    HRESULT SetMode([in] IPin *pin, long mode);
    HRESULT GetMode([in] IPin *pin, [out] long *mode);
    HRESULT GetCurrentActualFrameRate([in] IPin *pin, [out] LONGLONG *frame_rate);
    HRESULT GetMaxAvailableFrameRate([in] IPin *pin, [in] long index,
            [in] SIZE dimensions, [out] LONGLONG *frame_rate);
    HRESULT GetFrameRateList([in] IPin *pin, [in] long index, [in] SIZE dimensions,
            [out] long *list_size, [out] LONGLONG **frame_rate);
}

[
    local,
    object,
    uuid(56ed71a0-af5f-11d0-b3f0-00aa003761c5),
    pointer_default(unique)
]
interface IAMBufferNegotiation : IUnknown
{
    HRESULT SuggestAllocatorProperties([in] const ALLOCATOR_PROPERTIES *prop);
    HRESULT GetAllocatorProperties([out] ALLOCATOR_PROPERTIES *prop);
}

[
    local,
    object,
    uuid(c6e13344-30ac-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMDroppedFrames : IUnknown
{
    HRESULT GetNumDropped([out] long *number);
    HRESULT GetNumNotDropped([out] long *number);
    HRESULT GetDroppedInfo([in] long size, [out] long *array, [out] long *copied);
    HRESULT GetAverageFrameSize([out] long *size);
}

[
    local,
    object,
    uuid(62ea93ba-ec62-11d2-b770-00c04fb6bd3d),
    pointer_default(unique)
]
interface IAMLatency : IUnknown
{
    HRESULT GetLatency([in, out] REFERENCE_TIME *latency);
}

[
    local,
    object,
    uuid(f185fe76-e64e-11d2-b76e-00c04fb6bd3d),
    pointer_default(unique)
]
interface IAMPushSource : IAMLatency
{
    HRESULT GetPushSourceFlags([out] ULONG *flags);
    HRESULT SetPushSourceFlags([in] ULONG flags);
    HRESULT SetStreamOffset([in] REFERENCE_TIME offset);
    HRESULT GetStreamOffset([out] REFERENCE_TIME *offset);
    HRESULT GetMaxStreamOffset([out] REFERENCE_TIME *offset);
    HRESULT SetMaxStreamOffset([in] REFERENCE_TIME offset);
}

[
    local,
    object,
    uuid(c6e13343-30ac-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMVideoCompression : IUnknown
{
    HRESULT put_KeyFrameRate([in] long frame_rate);
    HRESULT get_KeyFrameRate([out] long *frame_rate);
    HRESULT put_PFramesPerKeyFrame([in] long frame);
    HRESULT get_PFramesPerKeyFrame([out] long *frame);
    HRESULT put_Quality([in] double quality);
    HRESULT get_Quality([out] double *quality);
    HRESULT put_WindowSize([in] DWORDLONG size);
    HRESULT get_WindowSize([out] DWORDLONG *size);
    HRESULT GetInfo([out, size_is(*version_size)] LPWSTR version, [in, out] int *version_size,
            [out, size_is(*description_size)] LPWSTR description, [in, out] int *description_size,
            [out] long *frame_rate, [out] long *key, [out] double *quality, [out] long *capabilities);
    HRESULT OverrideKeyFrame([in] long number);
    HRESULT OverrideFrameSize([in] long number, [in] long size);
}

[
    local,
    object,
    uuid(c6e13380-30ac-11d0-a18c-00a0c9118956),
    pointer_default(unique)
]
interface IAMCrossbar : IUnknown
{
    HRESULT get_PinCounts([out] long *output, [out] long *intput);
    HRESULT CanRoute([in] long output, [in] long input);
    HRESULT Route([in] long output, [in] long input);
    HRESULT get_IsRoutedTo([in] long output, [out] long *input);
    HRESULT get_CrossbarPinInfo([in] BOOL is_input, [in] long index,
            [out] long *related, [out] long *type);
}

typedef enum tagAMTunerEventType
{
    AMTUNER_EVENT_CHANGED = 0x0001,
} AMTunerEventType;

[
    local,
    object,
    uuid(211a8760-03ac-11d1-8d13-00aa00bd8339),
    pointer_default(unique)
]
interface IAMTunerNotification : IUnknown
{
    HRESULT OnEvent([in] AMTunerEventType event);
}

typedef enum tagAMTunerModeType
{
    AMTUNER_MODE_DEFAULT  = 0x0000,
    AMTUNER_MODE_TV       = 0x0001,
    AMTUNER_MODE_FM_RADIO = 0x0002,
    AMTUNER_MODE_AM_RADIO = 0x0004,
    AMTUNER_MODE_DSS      = 0x0008,
} AMTunerModeType;

[
    local,
    object,
    uuid(211a8761-03ac-11d1-8d13-00aa00bd8339),
    pointer_default(unique)
]
interface IAMTuner : IUnknown
{
    HRESULT put_Channel([in] long channel, [in] long video, [in] long audio);
    HRESULT get_Channel([out] long *channel, [out] long *video, [out] long *audio);
    HRESULT ChannelMinMax([out] long *min, [out] long *max);
    HRESULT put_CountryCode([in] long country);
    HRESULT get_CountryCode([out] long *country);
    HRESULT put_TuningSpace([in] long tuning_space);
    HRESULT get_TuningSpace([out] long *tuning_space);
    [local] HRESULT Logon([in] HANDLE user);
    HRESULT Logout();
    HRESULT SignalPresent([out] long *signal_strength);
    HRESULT put_Mode([in] AMTunerModeType mode);
    HRESULT get_Mode([out] AMTunerModeType *mode);
    HRESULT GetAvailableModes([out] long *modes);
    HRESULT RegisterNotificationCallBack([in] IAMTunerNotification *notify, [in] long events);
    HRESULT UnRegisterNotificationCallBack([in] IAMTunerNotification *notify);
}

[
    local,
    object,
    uuid(211a8766-03ac-11d1-8d13-00aa00bd8339),
    pointer_default(unique)
]
interface IAMTVTuner : IAMTuner
{
    HRESULT get_AvailableTVFormats([out] long *standard);
    HRESULT get_TVFormat([out] long *standard);
    HRESULT AutoTune([in] long channel, [out] long *signal);
    HRESULT StoreAutoTune();
    HRESULT get_NumInputConnections([out] long *number);
    HRESULT put_InputType([in] long index, [in] TunerInputType type);
    HRESULT get_InputType([in] long index, [out] TunerInputType *type);
    HRESULT put_ConnectInput([in] long index);
    HRESULT get_ConnectInput([out] long *index);
    HRESULT get_VideoFrequency([out] long *freq);
    HRESULT get_AudioFrequency([out] long *freq);
}

[
    local,
    object,
    uuid(e46a9787-2b71-444d-a4b5-1fab7b708d6a),
    pointer_default(unique),
]
interface IVideoFrameStep : IUnknown
{
    HRESULT Step(DWORD frame_count, [in] IUnknown *filter);
    HRESULT CanStep(long multiple, [in] IUnknown *filter);
    HRESULT CancelStep();
}

cpp_quote("#if (_WIN32_WINNT >= 0x0601)")
[
    local,
    object,
    uuid(cf7b26fc-9a00-485b-8147-3e789d5e8f67),
    pointer_default(unique)
]
interface IAMAsyncReaderTimestampScaling : IUnknown
{
    HRESULT GetTimestampMode(
            [out] BOOL *pfRaw);
    HRESULT SetTimestampMode(
            [in] BOOL fRaw);
}

[
    local,
    object,
    uuid(0e26a181-f40c-4635-8786-976284b52981),
    pointer_default(unique)
]
interface IAMPluginControl : IUnknown
{
    HRESULT GetPreferredClsid(
            [in] REFGUID subType,
            [out] CLSID *clsid);
    HRESULT GetPreferredClsidByIndex(
            [in] DWORD index,
            [out] GUID *subType,
            [out] CLSID *clsid);
    HRESULT SetPreferredClsid(
            [in] REFGUID subType,
            [in] const CLSID *clsid);
    HRESULT IsDisabled(
            [in] REFCLSID clsid);
    HRESULT GetDisabledByIndex(
            [in] DWORD index,
            [out] CLSID *clsid);
    HRESULT SetDisabled(
            [in] REFCLSID clsid,
            [in] BOOL disabled);
    HRESULT IsLegacyDisabled(
            [in] LPCWSTR dllName);
}
cpp_quote("#endif /*(_WIN32_WINNT >= 0x0601)*/")
