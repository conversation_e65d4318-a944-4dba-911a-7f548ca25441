;
; Definition file of WdsImage.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WdsImage.dll"
EXPORTS
FindFirstImage@8
FindNextImage@4
WDSFreeImageInformation@4
WDSGetImageInformation@8
WDSInitializeEmptyImageInformation@4
WDSParseImageInformation@12
WDSSetImageInformation@8
WdsImgAddReference@4
WdsImgApplyImage@20
WdsImgCaptureImage@40
WdsImgClose@4
WdsImgCopyImage@12
WdsImgCreateImageGroup@20
WdsImgDeleteImage@4
WdsImgDeleteImageGroup@4
WdsImgDeleteUnattendFile@4
WdsImgExportImage@36
WdsImgExtractFiles@20
WdsImgFindFirstImage@16
WdsImgFindFirstImageGroup@12
WdsImgFindNextImage@4
WdsImgFindNextImageGroup@4
WdsIm<PERSON><PERSON>rchitecture@8
WdsImgGetBootIndex@8
WdsImgGetCompressionType@8
WdsImgGetCreationTime@8
WdsImgGetDependantFiles@12
WdsImgGetDescription@8
WdsImgGetEnabled@8
WdsImgGetExFlags@8
WdsImgGetFlags@8
WdsImgGetHalName@8
WdsImgGetHandleFromFindHandle@8
WdsImgGetImageType@8
WdsImgGetIndex@8
WdsImgGetLanguage@8
WdsImgGetLanguages@12
WdsImgGetLastModifiedTime@8
WdsImgGetName@8
WdsImgGetPartitionStyle@8
WdsImgGetPath@8
WdsImgGetProductFamily@8
WdsImgGetProductName@8
WdsImgGetResourcePath@8
WdsImgGetSecurity@8
WdsImgGetServicePackLevel@8
WdsImgGetSize@8
WdsImgGetSystemRoot@8
WdsImgGetUnattendFilePresent@8
WdsImgGetVersion@8
WdsImgGetXml@8
WdsImgGroupCanImportImage@8
WdsImgGroupGetName@8
WdsImgGroupGetSecurity@8
WdsImgGroupSetName@8
WdsImgGroupSetSecurity@8
WdsImgImportImage@32
WdsImgIsAccessible@8
WdsImgIsBootImage@8
WdsImgIsFoundationImage@8
WdsImgIsValidImageFile@8
WdsImgOpenBootImageGroup@12
WdsImgOpenImage@16
WdsImgOpenImageGroup@12
WdsImgOpenImageStore@8
WdsImgRefreshData@4
WdsImgReplaceImage@32
WdsImgSetBootImage@8
WdsImgSetDescription@8
WdsImgSetEnabled@8
WdsImgSetName@8
WdsImgSetSecurity@8
WdsImgSetUnattendFile@12
WdsImgVerifyImageFile@8
