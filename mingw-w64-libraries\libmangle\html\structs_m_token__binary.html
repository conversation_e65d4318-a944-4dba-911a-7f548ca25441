<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: sMToken_binary Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>sMToken_binary Struct Reference</h1><!-- doxytag: class="sMToken_binary" -->
<p><code>#include &lt;<a class="el" href="m__token_8h_source.html">m_token.h</a>&gt;</code></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__base.html">sMToken_base</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__binary.html#adce4a3fd6700e408f79cfadc39ad58f7">base</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">union <a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__binary.html#abee09455681a857a6ac3fc6bd1877f5c">left</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">union <a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__binary.html#a6e2b3b9e012d9bee596529f517d8752a">right</a></td></tr>
</table>
<hr/><a name="_details"></a><h2>Detailed Description</h2>
<p>Binary node token. Contains pointers to any 2 generic token instances as child node elements. May act as a connector for decoded C++ names. </p>
<hr/><h2>Field Documentation</h2>
<a class="anchor" id="adce4a3fd6700e408f79cfadc39ad58f7"></a><!-- doxytag: member="sMToken_binary::base" ref="adce4a3fd6700e408f79cfadc39ad58f7" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__base.html">sMToken_base</a> <a class="el" href="structs_m_token__binary.html#adce4a3fd6700e408f79cfadc39ad58f7">sMToken_binary::base</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Base descriptor header. </p>

</div>
</div>
<a class="anchor" id="abee09455681a857a6ac3fc6bd1877f5c"></a><!-- doxytag: member="sMToken_binary::left" ref="abee09455681a857a6ac3fc6bd1877f5c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union <a class="el" href="unionu_m_token.html">uMToken</a>* <a class="el" href="structs_m_token__binary.html#abee09455681a857a6ac3fc6bd1877f5c">sMToken_binary::left</a><code> [write]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Left node element. </p>

</div>
</div>
<a class="anchor" id="a6e2b3b9e012d9bee596529f517d8752a"></a><!-- doxytag: member="sMToken_binary::right" ref="a6e2b3b9e012d9bee596529f517d8752a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union <a class="el" href="unionu_m_token.html">uMToken</a>* <a class="el" href="structs_m_token__binary.html#a6e2b3b9e012d9bee596529f517d8752a">sMToken_binary::right</a><code> [write]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Right node element. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="m__token_8h_source.html">m_token.h</a></li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
