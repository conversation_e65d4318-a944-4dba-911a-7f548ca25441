LIBRARY "CRYPT32.dll"
EXPORTS
ChainWlxLogoffEvent
CloseCertPerformanceData
CollectCertPerformanceData
OpenCertPerformanceData
CryptObjectLocatorFree
CryptObjectLocatorGet
CryptObjectLocatorGetContent
CryptObjectLocatorGetUpdated
CryptObjectLocatorInitialize
CryptObjectLocatorIsChanged
CryptObjectLocatorRelease
I_PFXImportCertStoreEx
CertAddCRLContextToStore
CertAddCRLLinkToStore
CertAddCTLContextToStore
CertAddCTLLinkToStore
CertAddCertificateContextToStore
CertAddCertificateLinkToStore
CertAddEncodedCRLToStore
CertAddEncodedCTLToStore
CertAddEncodedCertificateToStore
CertAddEncodedCertificateToSystemStoreA
CertAddEncodedCertificateToSystemStoreW
CertAddEnhancedKeyUsageIdentifier
CertAddRefServerOcspResponse
CertAddRefServerOcspResponseContext
CertAddSerializedElementToStore
CertAddStoreToCollection
CertAlgIdToOID
CertCloseServerOcspResponse
CertCloseStore
CertCompareCertificate
CertCompareCertificateName
CertCompareIntegerBlob
CertComparePublicKeyInfo
CertControlStore
CertCreateCRLContext
CertCreateCTLContext
CertCreateCTLEntryFromCertificateContextProperties
CertCreateCertificateChainEngine
CertCreateCertificateContext
CertCreateContext
CertCreateSelfSignCertificate
CertDeleteCRLFromStore
CertDeleteCTLFromStore
CertDeleteCertificateFromStore
CertDuplicateCRLContext
CertDuplicateCTLContext
CertDuplicateCertificateChain
CertDuplicateCertificateContext
CertDuplicateStore
CertEnumCRLContextProperties
CertEnumCRLsInStore
CertEnumCTLContextProperties
CertEnumCTLsInStore
CertEnumCertificateContextProperties
CertEnumCertificatesInStore
CertEnumPhysicalStore
CertEnumSubjectInSortedCTL
CertEnumSystemStore
CertEnumSystemStoreLocation
CertFindAttribute
CertFindCRLInStore
CertFindCTLInStore
CertFindCertificateInCRL
CertFindCertificateInStore
CertFindChainInStore
CertFindExtension
CertFindRDNAttr
CertFindSubjectInCTL
CertFindSubjectInSortedCTL
CertFreeCRLContext
CertFreeCTLContext
CertFreeCertificateChain
CertFreeCertificateChainEngine
CertFreeCertificateChainList
CertFreeCertificateContext
CertFreeServerOcspResponseContext
CertGetCRLContextProperty
CertGetCRLFromStore
CertGetCTLContextProperty
CertGetCertificateChain
CertGetCertificateContextProperty
CertGetEnhancedKeyUsage
CertGetIntendedKeyUsage
CertGetIssuerCertificateFromStore
CertGetNameStringA
CertGetNameStringW
CertGetPublicKeyLength
CertGetServerOcspResponseContext
CertGetStoreProperty
CertGetSubjectCertificateFromStore
CertGetValidUsages
CertIsRDNAttrsInCertificateName
CertIsStrongHashToSign
CertIsValidCRLForCertificate
CertIsWeakHash
CertNameToStrA
CertNameToStrW
CertOIDToAlgId
CertOpenServerOcspResponse
CertOpenStore
CertOpenSystemStoreA
CertOpenSystemStoreW
CertRDNValueToStrA
CertRDNValueToStrW
CertRegisterPhysicalStore
CertRegisterSystemStore
CertRemoveEnhancedKeyUsageIdentifier
CertRemoveStoreFromCollection
CertResyncCertificateChainEngine
CertRetrieveLogoOrBiometricInfo
CertSaveStore
CertSelectCertificateChains
CertSerializeCRLStoreElement
CertSerializeCTLStoreElement
CertSerializeCertificateStoreElement
CertSetCRLContextProperty
CertSetCTLContextProperty
CertSetCertificateContextPropertiesFromCTLEntry
CertSetCertificateContextProperty
CertSetEnhancedKeyUsage
CertSetStoreProperty
CertStrToNameA
CertStrToNameW
CertUnregisterPhysicalStore
CertUnregisterSystemStore
CertVerifyCRLRevocation
CertVerifyCRLTimeValidity
CertVerifyCTLUsage
CertVerifyCertificateChainPolicy
CertVerifyRevocation
CertVerifySubjectCertificateContext
CertVerifyTimeValidity
CertVerifyValidityNesting
CryptAcquireCertificatePrivateKey
CryptBinaryToStringA
CryptBinaryToStringW
CryptCloseAsyncHandle
CryptCreateAsyncHandle
CryptCreateKeyIdentifierFromCSP
CryptDecodeMessage
CryptDecodeObject
CryptDecodeObjectEx
CryptDecryptAndVerifyMessageSignature
CryptDecryptMessage
CryptEncodeObject
CryptEncodeObjectEx
CryptEncryptMessage
CryptEnumKeyIdentifierProperties
CryptEnumOIDFunction
CryptEnumOIDInfo
CryptExportPKCS8
CryptExportPublicKeyInfo
CryptExportPublicKeyInfoEx
CryptExportPublicKeyInfoFromBCryptKeyHandle
CryptFindCertificateKeyProvInfo
CryptFindLocalizedName
CryptFindOIDInfo
CryptFormatObject
CryptFreeOIDFunctionAddress
CryptGetAsyncParam
CryptGetDefaultOIDDllList
CryptGetDefaultOIDFunctionAddress
CryptGetKeyIdentifierProperty
CryptGetMessageCertificates
CryptGetMessageSignerCount
CryptGetOIDFunctionAddress
CryptGetOIDFunctionValue
CryptGetDefaultProviderA
CryptGetDefaultProviderW
CryptHashCertificate
CryptHashCertificate2
CryptHashMessage
CryptHashPublicKeyInfo
CryptHashToBeSigned
CryptImportPKCS8
CryptImportPublicKeyInfo
CryptImportPublicKeyInfoEx
CryptImportPublicKeyInfoEx2
CryptInitOIDFunctionSet
CryptInstallDefaultContext
CryptInstallOIDFunctionAddress
CryptLoadSip
CryptMemAlloc
CryptMemFree
CryptMemRealloc
CryptMsgCalculateEncodedLength
CryptMsgClose
CryptMsgControl
CryptMsgCountersign
CryptMsgCountersignEncoded
CryptMsgDuplicate
CryptMsgEncodeAndSignCTL
CryptMsgGetAndVerifySigner
CryptMsgGetParam
CryptMsgOpenToDecode
CryptMsgOpenToEncode
CryptMsgSignCTL
CryptMsgUpdate
CryptMsgVerifyCountersignatureEncoded
CryptMsgVerifyCountersignatureEncodedEx
CryptProtectData
CryptProtectMemory
CryptQueryObject
CryptRegisterDefaultOIDFunction
CryptRegisterOIDFunction
CryptRegisterOIDInfo
CryptRetrieveTimeStamp
CryptSIPAddProvider
CryptSIPCreateIndirectData
CryptSIPGetCaps
CryptSIPGetSealedDigest
CryptSIPGetSignedDataMsg
CryptSIPLoad
CryptSIPPutSignedDataMsg
CryptSIPRemoveProvider
CryptSIPRemoveSignedDataMsg
CryptSIPRetrieveSubjectGuid
CryptSIPRetrieveSubjectGuidForCatalogFile
CryptSIPVerifyIndirectData
CryptSetAsyncParam
CryptSetKeyIdentifierProperty
CryptSetOIDFunctionValue
CryptSignAndEncodeCertificate
CryptSignAndEncryptMessage
CryptSignCertificate
CryptSignMessage
CryptSignMessageWithKey
CryptStringToBinaryA
CryptStringToBinaryW
CryptUninstallDefaultContext
CryptUnprotectData
CryptUnprotectMemory
CryptUnregisterDefaultOIDFunction
CryptUnregisterOIDFunction
CryptUnregisterOIDInfo
CryptUpdateProtectedState
CryptVerifyCertificateSignature
CryptVerifyCertificateSignatureEx
CryptVerifyDetachedMessageHash
CryptVerifyDetachedMessageSignature
CryptVerifyMessageHash
CryptVerifyMessageSignature
CryptVerifyMessageSignatureWithKey
CryptVerifyTimeStampSignature
I_CertChainEngineIsDisallowedCertificate
I_CertDiagControl
I_CertFinishSslHandshake
I_CertProcessSslHandshake
I_CertProtectFunction
I_CertSrvProtectFunction
I_CertSyncStore
I_CertUpdateStore
I_CryptAddRefLruEntry
I_CryptAddSmartCardCertToStore
I_CryptAllocTls
I_CryptCreateLruCache
I_CryptCreateLruEntry
I_CryptDetachTls
I_CryptDisableLruOfEntries
I_CryptEnableLruOfEntries
I_CryptEnumMatchingLruEntries
I_CryptFindLruEntry
I_CryptFindLruEntryData
I_CryptFindSmartCardCertInStore
I_CryptFlushLruCache
I_CryptFreeLruCache
I_CryptFreeTls
I_CryptGetAsn1Decoder
I_CryptGetAsn1Encoder
I_CryptGetDefaultCryptProv
I_CryptGetDefaultCryptProvForEncrypt
I_CryptGetFileVersion
I_CryptGetLruEntryData
I_CryptGetLruEntryIdentifier
I_CryptGetOssGlobal
I_CryptGetTls
I_CryptAllocTlsEx
I_CryptInsertLruEntry
I_CryptInstallAsn1Module
I_CryptInstallOssGlobal
I_CryptReadTrustedPublisherDWORDValueFromRegistry
I_CryptRegisterSmartCardStore
I_CryptReleaseLruEntry
I_CryptRemoveLruEntry
I_CryptSetTls
I_CryptTouchLruEntry
I_CryptUninstallAsn1Module
I_CryptUninstallOssGlobal
I_CryptUnregisterSmartCardStore
I_CryptWalkAllLruCacheEntries
PFXExportCertStore
PFXExportCertStore2
PFXExportCertStoreEx
PFXImportCertStore
PFXIsPFXBlob
PFXVerifyPassword
