#include "func.def.in"

LIBRARY "USER32.dll"
EXPORTS
ActivateKeyboardLayout
AddClipboardFormatListener
AdjustWindowRect
AdjustWindowRectEx
AdjustWindowRectExForDpi
AlignRects
AllowForegroundActivation
AllowSetForegroundWindow
AnimateWindow
AnyPopup
AppendMenuA
AppendMenuW
AreDpiAwarenessContextsEqual
ArrangeIconicWindows
AttachThreadInput
BeginDeferWindowPos
BeginPaint
BlockInput
BringWindowToTop
BroadcastSystemMessage
BroadcastSystemMessageA
BroadcastSystemMessageExA
BroadcastSystemMessageExW
BroadcastSystemMessageW
BuildReasonArray
CalcMenuBar
CalculatePopupWindowPosition
CallMsgFilter
CallMsgFilterA
CallMsgFilterW
CallNextHookEx
CallWindowProcA
CallWindowProcW
CancelShutdown
CascadeChildWindows
CascadeWindows
ChangeClipboardChain
ChangeDisplaySettingsA
ChangeDisplaySettingsExA
ChangeDisplaySettingsExW
ChangeDisplaySettingsW
ChangeMenuA
ChangeMenuW
ChangeWindowMessageFilter
ChangeWindowMessageFilterEx
CharLowerA
CharLowerBuffA
CharLowerBuffW
CharLowerW
CharNextA
CharNextExA
CharNextW
CharPrevA
CharPrevExA
CharPrevW
CharToOemA
CharToOemBuffA
CharToOemBuffW
CharToOemW
CharUpperA
CharUpperBuffA
CharUpperBuffW
CharUpperW
CheckDBCSEnabledExt
CheckDlgButton
CheckMenuItem
CheckMenuRadioItem
CheckProcessForClipboardAccess
CheckProcessSession
CheckRadioButton
CheckWindowThreadDesktop
ChildWindowFromPoint
ChildWindowFromPointEx
CliImmSetHotKey
ClientThreadSetup
ClientToScreen
ClipCursor
CloseClipboard
CloseDesktop
CloseGestureInfoHandle
CloseTouchInputHandle
CloseWindow
CloseWindowStation
ConsoleControl
ControlMagnification
CopyAcceleratorTableA
CopyAcceleratorTableW
CopyIcon
CopyImage
CopyRect
CountClipboardFormats
CreateAcceleratorTableA
CreateAcceleratorTableW
CreateCaret
CreateCursor
CreateDCompositionHwndTarget
CreateDesktopA
CreateDesktopExA
CreateDesktopExW
CreateDesktopW
CreateDialogIndirectParamA
CreateDialogIndirectParamAorW
CreateDialogIndirectParamW
CreateDialogParamA
CreateDialogParamW
CreateIcon
CreateIconFromResource
CreateIconFromResourceEx
CreateIconIndirect
CreateMDIWindowA
CreateMDIWindowW
CreateMenu
CreatePopupMenu
CreateSystemThreads
CreateWindowExA
CreateWindowExW
CreateWindowInBand
CreateWindowInBandEx
CreateWindowIndirect
CreateWindowStationA
CreateWindowStationW
CsrBroadcastSystemMessageExW
CtxInitUser32
DdeAbandonTransaction
DdeAccessData
DdeAddData
DdeClientTransaction
DdeCmpStringHandles
DdeConnect
DdeConnectList
DdeCreateDataHandle
DdeCreateStringHandleA
DdeCreateStringHandleW
DdeDisconnect
DdeDisconnectList
DdeEnableCallback
DdeFreeDataHandle
DdeFreeStringHandle
DdeGetData
DdeGetLastError
DdeGetQualityOfService
DdeImpersonateClient
DdeInitializeA
DdeInitializeW
DdeKeepStringHandle
DdeNameService
DdePostAdvise
DdeQueryConvInfo
DdeQueryNextServer
DdeQueryStringA
DdeQueryStringW
DdeReconnect
DdeSetQualityOfService
DdeSetUserHandle
DdeUnaccessData
DdeUninitialize
DefDlgProcA
DefDlgProcW
DefFrameProcA
DefFrameProcW
DefMDIChildProcA
DefMDIChildProcW
DefRawInputProc
DefWindowProcA
DefWindowProcW
DeferWindowPos
DeferWindowPosAndBand
DeleteMenu
DeregisterShellHookWindow
DestroyAcceleratorTable
DestroyCaret
DestroyCursor
DestroyDCompositionHwndTarget
DestroyIcon
DestroyMenu
DestroyReasons
DestroyWindow
DeviceEventWorker
DialogBoxIndirectParamA
DialogBoxIndirectParamAorW
DialogBoxIndirectParamW
DialogBoxParamA
DialogBoxParamW
DisableProcessWindowsGhosting
DispatchMessageA
DispatchMessageW
DisplayConfigGetDeviceInfo
DisplayConfigSetDeviceInfo
DisplayExitWindowsWarnings
DlgDirListA
DlgDirListComboBoxA
DlgDirListComboBoxW
DlgDirListW
DlgDirSelectComboBoxExA
DlgDirSelectComboBoxExW
DlgDirSelectExA
DlgDirSelectExW
DoSoundConnect
DoSoundDisconnect
DragDetect
DragObject
DrawAnimatedRects
DrawCaption
DrawCaptionTempA
DrawCaptionTempW
DrawEdge
DrawFocusRect
DrawFrame
DrawFrameControl
DrawIcon
DrawIconEx
DrawMenuBar
DrawMenuBarTemp
DrawStateA
DrawStateW
DrawTextA
DrawTextExA
DrawTextExW
DrawTextW
DwmGetDxSharedSurface
DwmGetRemoteSessionOcclusionEvent
DwmGetRemoteSessionOcclusionState
DwmKernelShutdown
DwmKernelStartup
DwmLockScreenUpdates
DwmStartRedirection
DwmStopRedirection
DwmValidateWindow
EditWndProc
EmptyClipboard
EnableMenuItem
EnableMouseInPointer
EnableNonClientDpiScaling
EnableScrollBar
EnableSessionForMMCSS
EnableWindow
EndDeferWindowPos
EndDeferWindowPosEx
EndDialog
EndMenu
EndPaint
EndTask
EnterReaderModeHelper
EnumChildWindows
EnumClipboardFormats
EnumDesktopWindows
EnumDesktopsA
EnumDesktopsW
EnumDisplayDevicesA
EnumDisplayDevicesW
EnumDisplayMonitors
EnumDisplaySettingsA
EnumDisplaySettingsExA
EnumDisplaySettingsExW
EnumDisplaySettingsW
EnumPropsA
EnumPropsExA
EnumPropsExW
EnumPropsW
EnumThreadWindows
EnumWindowStationsA
EnumWindowStationsW
EnumWindows
EqualRect
EvaluateProximityToPolygon
EvaluateProximityToRect
ExcludeUpdateRgn
ExitWindowsEx
FillRect
FindWindowA
FindWindowExA
FindWindowExW
FindWindowW
FlashWindow
FlashWindowEx
FrameRect
FreeDDElParam
FrostCrashedWindow
GetActiveWindow
GetAltTabInfo
GetAltTabInfoA
GetAltTabInfoW
GetAncestor
GetAppCompatFlags
GetAppCompatFlags2
GetAsyncKeyState
GetAutoRotationState
GetAwarenessFromDpiAwarenessContext
GetCIMSSM
GetCapture
GetCaretBlinkTime
GetCaretPos
GetClassInfoA
GetClassInfoExA
GetClassInfoExW
GetClassInfoW
GetClassLongA
GetClassLongW
F64(GetClassLongPtrA)
F64(GetClassLongPtrW)
GetClassNameA
GetClassNameW
GetClassWord
GetClientRect
GetClipCursor
GetClipboardAccessToken
GetClipboardData
GetClipboardFormatNameA
GetClipboardFormatNameW
GetClipboardOwner
GetClipboardSequenceNumber
GetClipboardViewer
GetComboBoxInfo
GetCurrentInputMessageSource
GetCursor
GetCursorFrameInfo
GetCursorInfo
GetCursorPos
GetDC
GetDCEx
GetDesktopID
GetDesktopWindow
GetDialogBaseUnits
GetDisplayAutoRotationPreferences
GetDisplayConfigBufferSizes
GetDlgCtrlID
GetDlgItem
GetDlgItemInt
GetDlgItemTextA
GetDlgItemTextW
GetDoubleClickTime
GetDpiForMonitorInternal
GetDpiForSystem
GetDpiForWindow
GetFocus
GetForegroundWindow
GetGestureConfig
GetGestureExtraArgs
GetGestureInfo
GetGUIThreadInfo
GetGuiResources
GetIconInfo
GetIconInfoExA
GetIconInfoExW
GetInputDesktop
GetInputLocaleInfo
GetInputState
GetInternalWindowPos
GetKBCodePage
GetKeyNameTextA
GetKeyNameTextW
GetKeyState
GetKeyboardLayout
GetKeyboardLayoutList
GetKeyboardLayoutNameA
GetKeyboardLayoutNameW
GetKeyboardState
GetKeyboardType
GetLastActivePopup
GetLastInputInfo
GetLayeredWindowAttributes
GetListBoxInfo
GetMagnificationDesktopColorEffect
GetMagnificationDesktopMagnification
GetMagnificationLensCtxInformation
GetMenu
GetMenuBarInfo
GetMenuCheckMarkDimensions
GetMenuContextHelpId
GetMenuDefaultItem
GetMenuInfo
GetMenuItemCount
GetMenuItemID
GetMenuItemInfoA
GetMenuItemInfoW
GetMenuItemRect
GetMenuState
GetMenuStringA
GetMenuStringW
GetMessageA
GetMessageExtraInfo
GetMessagePos
GetMessageTime
GetMessageW
GetMonitorInfoA
GetMonitorInfoW
GetMouseMovePointsEx
GetNextDlgGroupItem
GetNextDlgTabItem
GetOpenClipboardWindow
GetParent
GetPhysicalCursorPos
GetPointerCursorId
GetPointerDevice
GetPointerDeviceCursors
GetPointerDeviceProperties
GetPointerDeviceRects
GetPointerDevices
GetPointerFrameArrivalTimes
GetPointerFrameInfo
GetPointerFrameInfoHistory
GetPointerFramePenInfo
GetPointerFramePenInfoHistory
GetPointerFrameTouchInfo
GetPointerFrameTouchInfoHistory
GetPointerInfo
GetPointerInfoHistory
GetPointerInputTransform
GetPointerPenInfo
GetPointerPenInfoHistory
GetPointerTouchInfo
GetPointerTouchInfoHistory
GetPointerType
GetPriorityClipboardFormat
GetProcessDefaultLayout
GetProcessDpiAwarenessInternal
GetProcessWindowStation
GetProgmanWindow
GetPropA
GetPropW
GetQueueStatus
GetRawInputBuffer
GetRawInputData
GetRawInputDeviceInfoA
GetRawInputDeviceInfoW
GetRawInputDeviceList
GetRawPointerDeviceData
GetReasonTitleFromReasonCode
GetRegisteredRawInputDevices
GetScrollBarInfo
GetScrollInfo
GetScrollPos
GetScrollRange
GetSendMessageReceiver
GetShellWindow
GetSubMenu
GetSysColor
GetSysColorBrush
GetSystemMenu
GetSystemMetrics
GetSystemMetricsForDpi
GetTabbedTextExtentA
GetTabbedTextExtentW
GetTaskmanWindow
GetThreadDesktop
GetThreadDpiAwarenessContext
GetTitleBarInfo
GetTopLevelWindow
GetTopWindow
GetTouchInputInfo
GetUnpredictedMessagePos
GetUpdateRect
GetUpdateRgn
GetUpdatedClipboardFormats
GetUserObjectInformationA
GetUserObjectInformationW
GetUserObjectSecurity
GetWinStationInfo
GetWindow
GetWindowBand
GetWindowCompositionAttribute
GetWindowCompositionInfo
GetWindowContextHelpId
GetWindowDC
GetWindowDisplayAffinity
GetWindowDpiAwarenessContext
GetWindowFeedbackSetting
GetWindowInfo
GetWindowLongA
GetWindowLongW
F64(GetWindowLongPtrA)
F64(GetWindowLongPtrW)
GetWindowMinimizeRect
GetWindowModuleFileName
GetWindowModuleFileNameA
GetWindowModuleFileNameW
GetWindowPlacement
GetWindowRect
GetWindowRgn
GetWindowRgnBox
GetWindowRgnEx
GetWindowTextA
GetWindowTextLengthA
GetWindowTextLengthW
GetWindowTextW
GetWindowThreadProcessId
GetWindowWord
GhostWindowFromHungWindow
GrayStringA
GrayStringW
HideCaret
HiliteMenuItem
HungWindowFromGhostWindow
IMPGetIMEA
IMPGetIMEW
IMPQueryIMEA
IMPQueryIMEW
IMPSetIMEA
IMPSetIMEW
ImpersonateDdeClientWindow
InSendMessage
InSendMessageEx
InflateRect
InheritWindowMonitor
InitDManipHook
InitializeInputDeviceInjection
InitializeLpkHooks
InitializePointerDeviceInjection
InitializeTouchInjection
InjectDeviceInput
InjectKeyboardInput
InjectMouseInput
InjectPointerInput
InjectTouchInput
InsertMenuA
InsertMenuItemA
InsertMenuItemW
InsertMenuW
InternalGetWindowIcon
InternalGetWindowText
IntersectRect
InvalidateRect
InvalidateRgn
InvertRect
IsCharAlphaA
IsCharAlphaNumericA
IsCharAlphaNumericW
IsCharAlphaW
IsCharLowerA
IsCharLowerW
IsCharUpperA
IsCharUpperW
IsChild
IsClipboardFormatAvailable
IsDialogMessage
IsDialogMessageA
IsDialogMessageW
IsDlgButtonChecked
IsGUIThread
IsHungAppWindow
IsIconic
IsImmersiveProcess
IsInDesktopWindowBand
IsMenu
IsProcess16Bit
IsMouseInPointerEnabled
IsProcessDPIAware
IsQueueAttached
IsRectEmpty
IsSETEnabled
IsServerSideWindow
IsThreadDesktopComposited
IsTopLevelWindow
IsTouchWindow
IsValidDpiAwarenessContext
IsWinEventHookInstalled
IsWindow
IsWindowArranged
IsWindowEnabled
IsWindowInDestroy
IsWindowRedirectedForPrint
IsWindowUnicode
IsWindowVisible
IsWow64Message
IsZoomed
KillSystemTimer
KillTimer
LoadAcceleratorsA
LoadAcceleratorsW
LoadBitmapA
LoadBitmapW
LoadCursorA
LoadCursorFromFileA
LoadCursorFromFileW
LoadCursorW
LoadIconA
LoadIconW
LoadImageA
LoadImageW
LoadKeyboardLayoutA
LoadKeyboardLayoutEx
LoadKeyboardLayoutW
LoadLocalFonts
LoadMenuA
LoadMenuIndirectA
LoadMenuIndirectW
LoadMenuW
LoadRemoteFonts
LoadStringA
LoadStringW
LockSetForegroundWindow
LockWindowStation
LockWindowUpdate
LockWorkStation
LogicalToPhysicalPoint
LogicalToPhysicalPointForPerMonitorDPI
LookupIconIdFromDirectory
LookupIconIdFromDirectoryEx
MBToWCSEx
MBToWCSExt
MB_GetString
MapDialogRect
MapVirtualKeyA
MapVirtualKeyExA
MapVirtualKeyExW
MapVirtualKeyW
MapWindowPoints
MenuItemFromPoint
MenuWindowProcA
MenuWindowProcW
MessageBeep
MessageBoxA
MessageBoxExA
MessageBoxExW
MessageBoxIndirectA
MessageBoxIndirectW
MessageBoxTimeoutA
MessageBoxTimeoutW
MessageBoxW
ModifyMenuA
ModifyMenuW
MonitorFromPoint
MonitorFromRect
MonitorFromWindow
MoveWindow
MsgWaitForMultipleObjects
MsgWaitForMultipleObjectsEx
NotifyOverlayWindow
NotifyWinEvent
OemKeyScan
OemToCharA
OemToCharBuffA
OemToCharBuffW
OemToCharW
OffsetRect
OpenClipboard
OpenDesktopA
OpenDesktopW
OpenIcon
OpenInputDesktop
OpenThreadDesktop
OpenWindowStationA
OpenWindowStationW
PackDDElParam
PackTouchHitTestingProximityEvaluation
PaintDesktop
PaintMenuBar
PaintMonitor
PeekMessageA
PeekMessageW
PhysicalToLogicalPoint
PhysicalToLogicalPointForPerMonitorDPI
PostMessageA
PostMessageW
PostQuitMessage
PostThreadMessageA
PostThreadMessageW
PrintWindow
PrivateExtractIconExA
PrivateExtractIconExW
PrivateExtractIconsA
PrivateExtractIconsW
PrivateRegisterICSProc
PtInRect
QueryBSDRWindow
QueryDisplayConfig
QuerySendMessage
RIMAddInputObserver
RIMAreSiblingDevices
RIMDeviceIoControl
RIMFreeInputBuffer
RIMGetDevicePreparsedData
RIMGetDevicePreparsedDataLockfree
RIMGetDeviceProperties
RIMGetPhysicalDeviceRect
RIMGetSourceProcessId
RIMObserveNextInput
RIMOnPnpNotification
RIMOnTimerNotification
RIMReadInput
RIMRegisterForInput
RIMRemoveInputObserver
RIMSetTestModeStatus
RIMUnregisterForInput
RIMUpdateInputObserverRegistration
RealChildWindowFromPoint
RealGetWindowClass
RealGetWindowClassA
RealGetWindowClassW
ReasonCodeNeedsBugID
ReasonCodeNeedsComment
RecordShutdownReason
RedrawWindow
RegisterBSDRWindow
RegisterClassA
RegisterClassExA
RegisterClassExW
RegisterClassW
RegisterClipboardFormatA
RegisterClipboardFormatW
RegisterDManipHook
RegisterDeviceNotificationA
RegisterDeviceNotificationW
RegisterErrorReportingDialog
RegisterFrostWindow
RegisterGhostWindow
RegisterHotKey
RegisterLogonProcess
RegisterManipulationThread
RegisterMessagePumpHook
RegisterPointerDeviceNotifications
RegisterPointerInputTarget
RegisterPointerInputTargetEx
RegisterPowerSettingNotification
RegisterRawInputDevices
RegisterServicesProcess
RegisterSessionPort
RegisterShellHookWindow
RegisterSuspendResumeNotification
RegisterSystemThread
RegisterTasklist
RegisterTouchHitTestingWindow
RegisterTouchWindow
RegisterUserApiHook
RegisterWindowMessageA
RegisterWindowMessageW
ReleaseCapture
ReleaseDC
ReleaseDwmHitTestWaiters
RemoveClipboardFormatListener
RemoveInjectionDevice
RemoveMenu
RemovePropA
RemovePropW
ReplyMessage
ResolveDesktopForWOW
ReuseDDElParam
ScreenToClient
ScrollChildren
ScrollDC
ScrollWindow
ScrollWindowEx
SendDlgItemMessageA
SendDlgItemMessageW
SendIMEMessageExA
SendIMEMessageExW
SendInput
SendMessageA
SendMessageCallbackA
SendMessageCallbackW
SendMessageTimeoutA
SendMessageTimeoutW
SendMessageW
SendNotifyMessageA
SendNotifyMessageW
SetActiveWindow
SetCapture
SetCaretBlinkTime
SetCaretPos
SetClassLongA
SetClassLongW
F64(SetClassLongPtrA)
F64(SetClassLongPtrW)
SetClassWord
SetClipboardData
SetClipboardViewer
SetConsoleReserveKeys
SetCoalescableTimer
SetCursor
SetCursorContents
SetCursorPos
SetDebugErrorLevel
SetDeskWallpaper
SetDisplayAutoRotationPreferences
SetDisplayConfig
SetDlgItemInt
SetDlgItemTextA
SetDlgItemTextW
SetDoubleClickTime
SetFeatureReportResponse
SetFocus
SetForegroundWindow
SetGestureConfig
SetImmersiveBackgroundWindow
SetInternalWindowPos
SetKeyboardState
SetLastErrorEx
SetLayeredWindowAttributes
SetLogonNotifyWindow
SetMagnificationDesktopColorEffect
SetMagnificationDesktopMagnification
SetMagnificationLensCtxInformation
SetManipulationInputTarget
SetMenu
SetMenuContextHelpId
SetMenuDefaultItem
SetMenuInfo
SetMenuItemBitmaps
SetMenuItemInfoA
SetMenuItemInfoW
SetMessageExtraInfo
SetMessageQueue
SetMirrorRendering
SetParent
SetPhysicalCursorPos
SetProcessDPIAware
SetProcessDefaultLayout
SetProcessDpiAwarenessContext
SetProcessDpiAwarenessInternal
SetProcessRestrictionExemption
SetProcessWindowStation
SetProgmanWindow
SetPropA
SetPropW
SetRect
SetRectEmpty
SetScrollInfo
SetScrollPos
SetScrollRange
SetShellWindow
SetShellWindowEx
SetSysColors
SetSysColorsTemp
SetSystemCursor
SetSystemMenu
SetSystemTimer
SetTaskmanWindow
SetThreadDesktop
SetThreadDpiAwarenessContext
SetThreadInputBlocked
SetTimer
SetUserObjectInformationA
SetUserObjectInformationW
SetUserObjectSecurity
SetWinEventHook
SetWindowBand
SetWindowCompositionAttribute
SetWindowCompositionTransition
SetWindowContextHelpId
SetWindowDisplayAffinity
SetWindowFeedbackSetting
SetWindowLongA
SetWindowLongW
F64(SetWindowLongPtrA)
F64(SetWindowLongPtrW)
SetWindowPlacement
SetWindowPos
SetWindowRgn
SetWindowRgnEx
SetWindowStationUser
SetWindowTextA
SetWindowTextW
SetWindowWord
SetWindowsHookA
SetWindowsHookExA
SetWindowsHookExW
SetWindowsHookW
ShowCaret
ShowCursor
ShowOwnedPopups
ShowScrollBar
ShowStartGlass
ShowSystemCursor
ShowWindow
ShowWindowAsync
ShutdownBlockReasonCreate
ShutdownBlockReasonDestroy
ShutdownBlockReasonQuery
SignalRedirectionStartComplete
SkipPointerFrameMessages
SoftModalMessageBox
SoundSentry
SubtractRect
SwapMouseButton
SwitchDesktop
SwitchDesktopWithFade
SwitchToThisWindow
SystemParametersInfoA
SystemParametersInfoForDpi
SystemParametersInfoW
TabbedTextOutA
TabbedTextOutW
TileChildWindows
TileWindows
ToAscii
ToAsciiEx
ToUnicode
ToUnicodeEx
TrackMouseEvent
TrackPopupMenu
TrackPopupMenuEx
TranslateAccelerator
TranslateAcceleratorA
TranslateAcceleratorW
TranslateMDISysAccel
TranslateMessage
TranslateMessageEx
UnhookWinEvent
UnhookWindowsHook
UnhookWindowsHookEx
UnionRect
UnloadKeyboardLayout
UnlockWindowStation
UnpackDDElParam
UnregisterClassA
UnregisterClassW
UnregisterDeviceNotification
UnregisterHotKey
UnregisterMessagePumpHook
UnregisterPointerInputTarget
UnregisterPointerInputTargetEx
UnregisterPowerSettingNotification
UnregisterSessionPort
UnregisterSuspendResumeNotification
UnregisterTouchWindow
UnregisterUserApiHook
UpdateDefaultDesktopThumbnail
UpdateLayeredWindow
UpdateLayeredWindowIndirect
UpdatePerUserSystemParameters
UpdateWindow
UpdateWindowInputSinkHints
UpdateWindowTransform
User32InitializeImmEntryTable
UserClientDllInitialize
UserHandleGrantAccess
UserLpkPSMTextOut
UserLpkTabbedTextOut
UserRealizePalette
UserRegisterWowHandlers
VRipOutput
VTagOutput
ValidateRect
ValidateRgn
VkKeyScanA
VkKeyScanExA
VkKeyScanExW
VkKeyScanW
WCSToMBEx
WINNLSEnableIME
WINNLSGetEnableStatus
WINNLSGetIMEHotkey
WaitForInputIdle
WaitForRedirectionStartComplete
WaitMessage
Win32PoolAllocationStats
WinHelpA
WinHelpW
WindowFromDC
WindowFromPhysicalPoint
WindowFromPoint
_UserTestTokenForInteractive
gSharedInfo DATA
gapfnScSendMessage DATA
keybd_event
mouse_event
wsprintfA
wsprintfW
wvsprintfA
wvsprintfW
IsThreadMessageQueueAttached
