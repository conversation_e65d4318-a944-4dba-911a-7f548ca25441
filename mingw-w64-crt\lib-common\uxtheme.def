;
; Definition file of UxTheme.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "UxTheme.dll"
EXPORTS
BeginPanningFeedback
EndPanningFeedback
UpdatePanningFeedback
BeginBufferedAnimation
BeginBufferedPaint
BufferedPaintClear
BufferedPaintInit
BufferedPaintRenderAnimation
BufferedPaintSetAlpha
DrawThemeBackgroundEx
BufferedPaintStopAllAnimations
BufferedPaintUnInit
CloseThemeData
DrawThemeBackground
DrawThemeEdge
DrawThemeIcon
OpenThemeDataEx
DrawThemeParentBackground
DrawThemeParentBackgroundEx
DrawThemeText
GetImmersiveColorFromColorSetEx
GetImmersiveUserColorSetPreference
DrawThemeTextEx
GetUserColorPreference
GetColorFromPreference
EnableThemeDialogTexture
EnableTheming
EndBufferedAnimation
EndBufferedPaint
GetBufferedPaintBits
GetB<PERSON>eredPaintDC
GetBufferedPaintTargetDC
GetBufferedPaintTargetRect
GetCurrentThemeName
GetThemeAnimationProperty
GetThemeAnimationTransform
GetThemeAppProperties
GetThemeBackgroundContentRect
GetThemeBackgroundExtent
GetThemeBackgroundRegion
GetThemeBitmap
GetThemeBool
GetThemeColor
GetThemeDocumentationProperty
GetThemeEnumValue
GetThemeFilename
GetThemeFont
GetThemeInt
GetThemeIntList
GetThemeMargins
GetThemeMetric
GetThemePartSize
GetThemePosition
GetThemePropertyOrigin
GetThemeRect
GetThemeStream
GetThemeString
GetThemeSysBool
GetThemeSysColor
GetThemeSysColorBrush
GetThemeSysFont
GetThemeSysInt
GetThemeSysSize
GetThemeSysString
GetThemeTextExtent
GetThemeTextMetrics
GetThemeTimingFunction
GetThemeTransitionDuration
GetWindowTheme
HitTestThemeBackground
IsAppThemed
IsCompositionActive
IsThemeActive
IsThemeBackgroundPartiallyTransparent
IsThemeDialogTextureEnabled
IsThemePartDefined
OpenThemeData
SetThemeAppProperties
SetWindowTheme
SetWindowThemeAttribute
ThemeInitApiHook
