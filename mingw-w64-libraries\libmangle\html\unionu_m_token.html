<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: uMToken Union Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>uMToken Union Reference</h1><!-- doxytag: class="uMToken" -->
<p><code>#include &lt;<a class="el" href="m__token_8h_source.html">m_token.h</a>&gt;</code></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__base.html">sMToken_base</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html#a4a4795bbd5a58f0f5d21ded3506c4a6c">base</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__value.html">sMToken_value</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html#a97f52a4d0d3ed56e9cf79045402c5202">value</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__name.html">sMToken_name</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html#a168eea3eefe059407dbafc873823ce4d">name</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__dim.html">sMToken_dim</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html#aef4d7f4b830f7133b09b6670b98d1cb0">dim</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token___unary.html">sMToken_Unary</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html#ac30a468d7a8b3e6f8eca74bce8f9bf05">unary</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__binary.html">sMToken_binary</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="unionu_m_token.html#a7a0e65155e6a9f49c3cd18c682071bb6">binary</a></td></tr>
</table>
<hr/><a name="_details"></a><h2>Detailed Description</h2>
<p>Generic token instances. Type of token determined by base descriptor in members. Base descriptor header available in all members through type punning. </p>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="m__token_8c.html#a3d90ad7945dc89f63c39837ee512fd85">gen_tok()</a> </dd></dl>
<hr/><h2>Field Documentation</h2>
<a class="anchor" id="a4a4795bbd5a58f0f5d21ded3506c4a6c"></a><!-- doxytag: member="uMToken::base" ref="a4a4795bbd5a58f0f5d21ded3506c4a6c" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__base.html">sMToken_base</a> <a class="el" href="unionu_m_token.html#a4a4795bbd5a58f0f5d21ded3506c4a6c">uMToken::base</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Base descriptor header. </p>

</div>
</div>
<a class="anchor" id="a7a0e65155e6a9f49c3cd18c682071bb6"></a><!-- doxytag: member="uMToken::binary" ref="a7a0e65155e6a9f49c3cd18c682071bb6" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__binary.html">sMToken_binary</a> <a class="el" href="unionu_m_token.html#a7a0e65155e6a9f49c3cd18c682071bb6">uMToken::binary</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Binary node token. </p>

</div>
</div>
<a class="anchor" id="aef4d7f4b830f7133b09b6670b98d1cb0"></a><!-- doxytag: member="uMToken::dim" ref="aef4d7f4b830f7133b09b6670b98d1cb0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__dim.html">sMToken_dim</a> <a class="el" href="unionu_m_token.html#aef4d7f4b830f7133b09b6670b98d1cb0">uMToken::dim</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>"dim" token </p>

</div>
</div>
<a class="anchor" id="a168eea3eefe059407dbafc873823ce4d"></a><!-- doxytag: member="uMToken::name" ref="a168eea3eefe059407dbafc873823ce4d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__name.html">sMToken_name</a> <a class="el" href="unionu_m_token.html#a168eea3eefe059407dbafc873823ce4d">uMToken::name</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>"name" token. </p>

</div>
</div>
<a class="anchor" id="ac30a468d7a8b3e6f8eca74bce8f9bf05"></a><!-- doxytag: member="uMToken::unary" ref="ac30a468d7a8b3e6f8eca74bce8f9bf05" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token___unary.html">sMToken_Unary</a> <a class="el" href="unionu_m_token.html#ac30a468d7a8b3e6f8eca74bce8f9bf05">uMToken::unary</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Unary node token. </p>

</div>
</div>
<a class="anchor" id="a97f52a4d0d3ed56e9cf79045402c5202"></a><!-- doxytag: member="uMToken::value" ref="a97f52a4d0d3ed56e9cf79045402c5202" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__value.html">sMToken_value</a> <a class="el" href="unionu_m_token.html#a97f52a4d0d3ed56e9cf79045402c5202">uMToken::value</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>"value" token. </p>

</div>
</div>
<hr/>The documentation for this union was generated from the following file:<ul>
<li>src/<a class="el" href="m__token_8h_source.html">m_token.h</a></li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
