;
; Definition file of ATL.DLL
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "ATL.DLL"
EXPORTS
AtlAdvise
AtlUnadvise
AtlFreeMarshalStream
AtlMarshalPtrInProc
AtlUnmarshalPtr
AtlModuleGetClassObject
AtlModuleInit
AtlModuleRegisterClassObjects
AtlModuleRegisterServer
AtlModuleRegisterTypeLib
AtlModuleRevokeClassObjects
AtlModuleTerm
AtlModuleUnregisterServer
AtlModuleUpdateRegistryFromResourceD
AtlWaitWithMessageLoop
AtlSetErrorInfo
AtlCreateTargetDC
AtlHiMetricToPixel
AtlPixelToHiMetric
AtlDevModeW2A
AtlComPtrAssign
AtlComQIPtrAssign
AtlInternalQueryInterface
AtlGetVersion
AtlAxDialogBoxW
AtlAxDialogBoxA
AtlAxCreateDialogW
At<PERSON>A<PERSON>Create<PERSON>ogA
AtlAxCreateControl
AtlAxCreateControlEx
AtlAxAttachControl
AtlAxWinInit
AtlModuleAddCreateWndData
AtlModuleExtractCreateWndData
AtlModuleRegisterWndClassInfoW
AtlModuleRegisterWndClassInfoA
AtlAxGetControl
AtlAxGetHost
AtlRegisterClassCategoriesHelper
AtlIPersistStreamInit_Load
AtlIPersistStreamInit_Save
AtlIPersistPropertyBag_Load
AtlIPersistPropertyBag_Save
AtlGetObjectSourceInterface
AtlModuleUnRegisterTypeLib
AtlModuleLoadTypeLib
AtlModuleUnregisterServerEx
AtlModuleAddTermFunc
AtlSetErrorInfo2
AtlIPersistStreamInit_GetSizeMax
