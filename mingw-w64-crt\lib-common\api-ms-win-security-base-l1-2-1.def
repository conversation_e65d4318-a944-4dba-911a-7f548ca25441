LIBRARY api-ms-win-security-base-l1-2-1

EXPORTS

AddAccessAllowedAce
AddAccessAllowedAceEx
AddAce
AddMandatoryAce
AdjustTokenGroups
AdjustTokenPrivileges
AllocateAndInitializeSid
AllocateLocallyUniqueId
CheckTokenMembership
CheckTokenMembershipEx
CopySid
CreateWellKnownSid
CveEventWrite
DeleteAce
DuplicateToken
DuplicateTokenEx
EqualDomainSid
EqualSid
FreeSid
GetAce
GetAclInformation
GetFileSecurityW
GetKernelObjectSecurity
GetLengthSid
GetSecurityDescriptorControl
GetSecurityDescriptorDacl
GetSecurityDescriptorGroup
GetSecurityDescriptorLength
GetSecurityDescriptorOwner
GetSecurityDescriptorRMControl
GetSecurityDescriptorSacl
GetSidIdentifierAuthority
GetSidLengthRequired
GetSidSubAuthority
GetSidSubAuthorityCount
GetTokenInformation
GetWindowsAccountDomainSid
ImpersonateLoggedOnUser
InitializeAcl
InitializeSecurityDescriptor
InitializeSid
IsValidAcl
IsValidSecurityDescriptor
IsValidSid
IsWellKnownSid
MakeAbsoluteSD
MakeSelfRelativeSD
RevertToSelf
SetFileSecurityW
SetKernelObjectSecurity
SetSecurityDescriptorControl
SetSecurityDescriptorDacl
SetSecurityDescriptorGroup
SetSecurityDescriptorOwner
SetSecurityDescriptorRMControl
SetSecurityDescriptorSacl
SetTokenInformation
