LIBRARY api-ms-win-core-synch-l1-2-1

EXPORTS

AcquireSRWLockExclusive
AcquireSRWLockShared
CancelWaitableTimer
CreateEventA
CreateEventExA
CreateEventExW
CreateEventW
CreateMutexA
CreateMutexExA
CreateMutexExW
CreateMutexW
CreateSemaphoreExW
CreateSemaphoreW
CreateWaitableTimerExW
CreateWaitableTimerW
DeleteCriticalSection
EnterCriticalSection
InitializeConditionVariable
InitializeCriticalSection
InitializeCriticalSectionAndSpinCount
InitializeCriticalSectionEx
InitializeSRWLock
InitOnceBeginInitialize
InitOnceComplete
InitOnceExecuteOnce
InitOnceInitialize
LeaveCriticalSection
OpenEventA
OpenEventW
OpenMutexW
OpenSemaphoreW
OpenWaitableTimerW
ReleaseMutex
ReleaseSemaphore
ReleaseSRWLockExclusive
ReleaseSRWLockShared
ResetEvent
SetCriticalSectionSpinCount
SetEvent
SetWaitableTimer
SetWaitableTimerEx
SignalObjectAndWait
Sleep
SleepConditionVariableCS
SleepConditionVariableSRW
SleepEx
TryAcquireSRWLockExclusive
TryAcquireSRWLockShared
TryEnterCriticalSection
WaitForMultipleObjects
WaitForMultipleObjectsEx
WaitForSingleObject
WaitForSingleObjectEx
WaitOnAddress
WakeAllConditionVariable
WakeByAddressAll
WakeByAddressSingle
WakeConditionVariable
