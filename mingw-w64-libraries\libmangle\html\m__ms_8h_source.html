<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: src/m_ms.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<h1>src/m_ms.h</h1><a href="m__ms_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment">   Copyright (c) 2009, 2010 mingw-w64 project</span>
<a name="l00003"></a>00003 <span class="comment"></span>
<a name="l00004"></a>00004 <span class="comment">   Contributing authors: Kai Tietz, Jonathan Yong</span>
<a name="l00005"></a>00005 <span class="comment"></span>
<a name="l00006"></a>00006 <span class="comment">   Permission is hereby granted, free of charge, to any person obtaining a</span>
<a name="l00007"></a>00007 <span class="comment">   copy of this software and associated documentation files (the &quot;Software&quot;),</span>
<a name="l00008"></a>00008 <span class="comment">   to deal in the Software without restriction, including without limitation</span>
<a name="l00009"></a>00009 <span class="comment">   the rights to use, copy, modify, merge, publish, distribute, sublicense,</span>
<a name="l00010"></a>00010 <span class="comment">   and/or sell copies of the Software, and to permit persons to whom the</span>
<a name="l00011"></a>00011 <span class="comment">   Software is furnished to do so, subject to the following conditions:</span>
<a name="l00012"></a>00012 <span class="comment"></span>
<a name="l00013"></a>00013 <span class="comment">   The above copyright notice and this permission notice shall be included in</span>
<a name="l00014"></a>00014 <span class="comment">   all copies or substantial portions of the Software.</span>
<a name="l00015"></a>00015 <span class="comment"></span>
<a name="l00016"></a>00016 <span class="comment">   THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR</span>
<a name="l00017"></a>00017 <span class="comment">   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,</span>
<a name="l00018"></a>00018 <span class="comment">   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE</span>
<a name="l00019"></a>00019 <span class="comment">   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER</span>
<a name="l00020"></a>00020 <span class="comment">   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING</span>
<a name="l00021"></a>00021 <span class="comment">   FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER</span>
<a name="l00022"></a>00022 <span class="comment">   DEALINGS IN THE SOFTWARE.</span>
<a name="l00023"></a>00023 <span class="comment">*/</span>
<a name="l00046"></a>00046 <span class="preprocessor">#ifndef _M_MS_HXX</span>
<a name="l00047"></a>00047 <span class="preprocessor"></span><span class="preprocessor">#define _M_MS_HXX</span>
<a name="l00048"></a>00048 <span class="preprocessor"></span>
<a name="l00049"></a>00049 <span class="preprocessor">#include &quot;<a class="code" href="m__token_8h.html">m_token.h</a>&quot;</span>
<a name="l00050"></a>00050 
<a name="l00051"></a><a class="code" href="m__ms_8h.html#aef5f6ad4353a2cf2321c074dbfaa9aac">00051</a> <span class="preprocessor">#define ENCODING_TYPE_MS        1</span>
<a name="l00052"></a>00052 <span class="preprocessor"></span>
<a name="l00059"></a><a class="code" href="structs_cached.html">00059</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_cached.html">sCached</a> {
<a name="l00060"></a><a class="code" href="structs_cached.html#aaf6b51539515c90f3258e80701871bc3">00060</a>   <span class="keywordtype">int</span> <a class="code" href="structs_cached.html#aaf6b51539515c90f3258e80701871bc3">count</a>;                       
<a name="l00061"></a><a class="code" href="structs_cached.html#af1075e0e3cedc49b7adf9f79d6c18e27">00061</a>   <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="structs_cached.html#af1075e0e3cedc49b7adf9f79d6c18e27">arr</a>[10];                
<a name="l00062"></a>00062 } <a class="code" href="structs_cached.html">sCached</a>;
<a name="l00063"></a>00063 
<a name="l00064"></a><a class="code" href="structs_m_s_ctx.html">00064</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structs_m_s_ctx.html">sMSCtx</a> {
<a name="l00065"></a><a class="code" href="structs_m_s_ctx.html#a2f5063c35143e68593acf2f1d718cfeb">00065</a>   <a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *<a class="code" href="structs_m_s_ctx.html#a2f5063c35143e68593acf2f1d718cfeb">gc</a>;
<a name="l00066"></a><a class="code" href="structs_m_s_ctx.html#ac0a073d6988c2278ef48f9d159383d84">00066</a>   <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="structs_m_s_ctx.html#ac0a073d6988c2278ef48f9d159383d84">name</a>;                
<a name="l00067"></a><a class="code" href="structs_m_s_ctx.html#a6e5fc61ecefe939aea462a75a2ba1332">00067</a>   <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="structs_m_s_ctx.html#a6e5fc61ecefe939aea462a75a2ba1332">end</a>;                 
<a name="l00068"></a><a class="code" href="structs_m_s_ctx.html#a26e2b2ad1f83c22f21581f0fd474fc21">00068</a>   <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="structs_m_s_ctx.html#a26e2b2ad1f83c22f21581f0fd474fc21">pos</a>;                 
<a name="l00069"></a><a class="code" href="structs_m_s_ctx.html#a78565b455c2442fb61bf1bcce3af88e4">00069</a>   <span class="keywordtype">int</span> <a class="code" href="structs_m_s_ctx.html#a78565b455c2442fb61bf1bcce3af88e4">err</a>;                         
<a name="l00070"></a><a class="code" href="structs_m_s_ctx.html#a6bc7b52416f139ba855c13b30621c59d">00070</a>   <span class="keywordtype">int</span> <a class="code" href="structs_m_s_ctx.html#a6bc7b52416f139ba855c13b30621c59d">fExplicitTemplateParams</a>;     
<a name="l00071"></a><a class="code" href="structs_m_s_ctx.html#af7821943d90885f933ff5ab411b339a3">00071</a>   <span class="keywordtype">int</span> <a class="code" href="structs_m_s_ctx.html#af7821943d90885f933ff5ab411b339a3">fGetTemplateArgumentList</a>;    
<a name="l00072"></a><a class="code" href="structs_m_s_ctx.html#a5fd6ba39ed9dde4dbdc7c8ac632955f2">00072</a>   <a class="code" href="structs_cached.html">sCached</a> *<a class="code" href="structs_m_s_ctx.html#a5fd6ba39ed9dde4dbdc7c8ac632955f2">pZNameList</a>;             
<a name="l00073"></a><a class="code" href="structs_m_s_ctx.html#a106ed398e4438095320072ffea744e3b">00073</a>   <a class="code" href="structs_cached.html">sCached</a> *<a class="code" href="structs_m_s_ctx.html#a106ed398e4438095320072ffea744e3b">pTemplateArgList</a>;       
<a name="l00074"></a><a class="code" href="structs_m_s_ctx.html#ac4b8422234e32c0045fa7f3f09dd0412">00074</a>   <a class="code" href="structs_cached.html">sCached</a> *<a class="code" href="structs_m_s_ctx.html#ac4b8422234e32c0045fa7f3f09dd0412">pArgList</a>;               
<a name="l00075"></a>00075 } <a class="code" href="structs_m_s_ctx.html">sMSCtx</a>;
<a name="l00076"></a>00076 
<a name="l00077"></a><a class="code" href="m__ms_8h.html#a0abbf2a725f45243f4292bf3e764973c">00077</a> <span class="preprocessor">#define GET_CHAR(CTX)   ((CTX)-&gt;pos == (CTX)-&gt;end ? 0 : (CTX)-&gt;pos[0])</span>
<a name="l00078"></a><a class="code" href="m__ms_8h.html#adfca56cc6bed709fa84cc0b26430100d">00078</a> <span class="preprocessor"></span><span class="preprocessor">#define INC_CHAR(CTX)   do { if ((CTX)-&gt;pos != (CTX)-&gt;end) (CTX)-&gt;pos++; } while (0)</span>
<a name="l00079"></a><a class="code" href="m__ms_8h.html#aac4ed973666f5f09c3e866666326fb05">00079</a> <span class="preprocessor"></span><span class="preprocessor">#define DEC_CHAR(CTX)   do { if ((CTX)-&gt;pos != (CTX)-&gt;name) (CTX)-&gt;pos--; } while (0)</span>
<a name="l00080"></a><a class="code" href="m__ms_8h.html#a1a18b928484e6e93526a252d4b7e3532">00080</a> <span class="preprocessor"></span><span class="preprocessor">#define SKIP_CHAR(CTX,LEN) do { (CTX)-&gt;pos += (LEN); if ((CTX)-&gt;pos &gt; (CTX)-&gt;end) (CTX)-&gt;pos=(CTX)-&gt;end; } while (0)</span>
<a name="l00081"></a>00081 <span class="preprocessor"></span>
<a name="l00092"></a>00092 <a class="code" href="unionu_m_token.html">uMToken</a> *<a class="code" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle_decode_ms_name</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">const</span> <span class="keywordtype">char</span> *name);
<a name="l00093"></a>00093 <span class="keywordtype">char</span> *<a class="code" href="libmangle_8h.html#ad6e58fecfca8cc312a2b09a44e3748fb">libmangle_encode_ms_name</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <a class="code" href="unionu_m_token.html">uMToken</a> *tok);
<a name="l00094"></a>00094 
<a name="l00095"></a>00095 <span class="preprocessor">#endif</span>
</pre></div></div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
