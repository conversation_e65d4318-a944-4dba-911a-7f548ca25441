;
; Definition file of WINUSB.DLL
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "WINUSB.DLL"
EXPORTS
WinUsb_AbortPipe
WinUsb_AbortPipeAsync
WinUsb_ControlTransfer
WinUsb_FlushPipe
WinUsb_Free
WinUsb_GetAdjustedFrameNumber
WinUsb_GetAssociatedInterface
WinUsb_GetCurrentAlternateSetting
WinUsb_GetCurrentFrameNumber
WinUsb_GetDescriptor
WinUsb_GetOverlappedResult
WinUsb_GetPipePolicy
WinUsb_GetPowerPolicy
WinUsb_Initialize
WinUsb_ParseConfigurationDescriptor
WinUsb_ParseDescriptors
WinUsb_QueryDeviceInformation
WinUsb_QueryInterfaceSettings
WinUsb_QueryPipe
WinUsb_QueryPipeEx
WinUsb_ReadIsochPipe
WinUsb_ReadIsochPipeAsap
WinUsb_ReadPipe
WinUsb_RegisterIsochBuffer
WinUsb_ResetPipe
WinUsb_ResetPipeAsync
WinUsb_SetCurrentAlternateSetting
WinUsb_SetCurrentAlternateSettingAsync
WinUsb_SetPipePolicy
WinUsb_SetPowerPolicy
WinUsb_UnregisterIsochBuffer
WinUsb_WriteIsochPipe
WinUsb_WriteIsochPipeAsap
WinUsb_WritePipe
