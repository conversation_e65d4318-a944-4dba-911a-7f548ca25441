;
; Definition file of Wlanapi.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "Wlanapi.dll"
EXPORTS
WlanAllocateMemory@4
WlanCloseHandle@8
WlanConnect@16
WlanDeleteProfile@16
WlanDisconnect@12
WlanEnumInterfaces@12
WlanExtractPsdIEDataList@24
Wlan<PERSON>reeMemory@4
WlanGetAvailableNetworkList@20
WlanGetFilterList@16
WlanGetInterfaceCapability@16
WlanGetNetworkBssList@28
WlanGetProfile@28
WlanGetProfileCustomUserData@24
WlanGetProfileList@16
WlanGetSecuritySettings@20
WlanIhvControl@32
Wlan<PERSON>penHandle@16
WlanQueryAutoConfigParameter@24
WlanQueryInterface@28
WlanReasonCodeToString@16
WlanRegisterNotification@28
WlanRenameProfile@20
WlanSaveTemporaryProfile@28
WlanScan@20
W<PERSON>etAutoConfigParameter@20
WlanSetFilterList@16
WlanSetInterface@24
WlanSetProfile@32
WlanSetProfileCustomUserData@24
WlanSetProfileEapUserData@44
WlanSetProfileEapXmlUserData@24
WlanSetProfileList@20
WlanSetProfilePosition@20
WlanSetPsdIEDataList@16
WlanSetSecuritySettings@12
