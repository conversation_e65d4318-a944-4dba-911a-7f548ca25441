LIBRARY WINMM.DLL
EXPORTS
CloseDriver@12
DefDriverProc@20
DriverCallback@28
DrvGetModuleHandle@4
GetDriverModuleHandle@4
NotifyCallbackData@20
OpenDriver@12
PlaySound@12
PlaySoundA@12
PlaySoundW@12
SendDriverMessage@16
WOW32DriverCallback@28
WOW32ResolveMultiMediaHandle@24
WOWAppExit@4
aux32Message@20
auxGetDevCapsA@12
auxGetDevCapsW@12
auxGetNumDevs@0
auxGetVolume@8
auxOutMessage@16
auxSetVolume@8
joy32Message@20
joyConfigChanged@4
joyGetDevCapsA@12
joyGetDevCapsW@12
joyGetNumDevs@0
joyGetPos@8
joyGetPosEx@8
joyGetThreshold@8
joyReleaseCapture@4
joySetCapture@16
joySetThreshold@8
mci32Message@20
mciDriverNotify@12
mciDriverYield@4
mciExecute@4
mciFreeCommandResource@4
mciGetCreatorTask@4
mciGetDeviceIDA@4
mciGetDeviceIDFromElementIDA@8
mciGetDeviceIDFromElementIDW@8
mciGetDeviceIDW@4
mciGetDriverData@4
mciGetErrorStringA@12
mciGetErrorStringW@12
mciGetYieldProc@8
mciLoadCommandResource@12
mciSendCommandA@16
mciSendCommandW@16
mciSendStringA@16
mciSendStringW@16
mciSetDriverData@8
mciSetYieldProc@12
mid32Message@20
midiConnect@12
midiDisconnect@12
midiInAddBuffer@12
midiInClose@4
midiInGetDevCapsA@12
midiInGetDevCapsW@12
midiInGetErrorTextA@12
midiInGetErrorTextW@12
midiInGetID@8
midiInGetNumDevs@0
midiInMessage@16
midiInOpen@20
midiInPrepareHeader@12
midiInReset@4
midiInStart@4
midiInStop@4
midiInUnprepareHeader@12
midiOutCacheDrumPatches@16
midiOutCachePatches@16
midiOutClose@4
midiOutGetDevCapsA@12
midiOutGetDevCapsW@12
midiOutGetErrorTextA@12
midiOutGetErrorTextW@12
midiOutGetID@8
midiOutGetNumDevs@0
midiOutGetVolume@8
midiOutLongMsg@12
midiOutMessage@16
midiOutOpen@20
midiOutPrepareHeader@12
midiOutReset@4
midiOutSetVolume@8
midiOutShortMsg@8
midiOutUnprepareHeader@12
midiStreamClose@4
midiStreamOpen@24
midiStreamOut@12
midiStreamPause@4
midiStreamPosition@12
midiStreamProperty@12
midiStreamRestart@4
midiStreamStop@4
mixerClose@4
mixerGetControlDetailsA@12
mixerGetControlDetailsW@12
mixerGetDevCapsA@12
mixerGetDevCapsW@12
mixerGetID@12
mixerGetLineControlsA@12
mixerGetLineControlsW@12
mixerGetLineInfoA@12
mixerGetLineInfoW@12
mixerGetNumDevs@0
mixerMessage@16
mixerOpen@20
mixerSetControlDetails@12
mmDrvInstall@12
mmGetCurrentTask@0
mmTaskBlock@4
mmTaskCreate@12
mmTaskSignal@4
mmTaskYield@0
mmioAdvance@12
mmioAscend@12
mmioClose@8
mmioCreateChunk@12
mmioDescend@16
mmioFlush@8
mmioGetInfo@12
mmioInstallIOProcA@12
mmioInstallIOProcW@12
mmioOpenA@12
mmioOpenW@12
mmioRead@12
mmioRenameA@16
mmioRenameW@16
mmioSeek@12
mmioSendMessage@16
mmioSetBuffer@16
mmioSetInfo@12
mmioStringToFOURCCA@8
mmioStringToFOURCCW@8
mmioWrite@12
mmsystemGetVersion@0
mod32Message@20
mxd32Message@20
sndPlaySoundA@8
sndPlaySoundW@8
tid32Message@20
timeBeginPeriod@4
timeEndPeriod@4
timeGetDevCaps@8
timeGetSystemTime@8
timeGetTime@0
timeKillEvent@4
timeSetEvent@20
waveInAddBuffer@12
waveInClose@4
waveInGetDevCapsA@12
waveInGetDevCapsW@12
waveInGetErrorTextA@12
waveInGetErrorTextW@12
waveInGetID@8
waveInGetNumDevs@0
waveInGetPosition@12
waveInMessage@16
waveInOpen@24
waveInPrepareHeader@12
waveInReset@4
waveInStart@4
waveInStop@4
waveInUnprepareHeader@12
waveOutBreakLoop@4
waveOutClose@4
waveOutGetDevCapsA@12
waveOutGetDevCapsW@12
waveOutGetErrorTextA@12
waveOutGetErrorTextW@12
waveOutGetID@8
waveOutGetNumDevs@0
waveOutGetPitch@8
waveOutGetPlaybackRate@8
waveOutGetPosition@12
waveOutGetVolume@8
waveOutMessage@16
waveOutOpen@24
waveOutPause@4
waveOutPrepareHeader@12
waveOutReset@4
waveOutRestart@4
waveOutSetPitch@8
waveOutSetPlaybackRate@8
waveOutSetVolume@8
waveOutUnprepareHeader@12
waveOutWrite@12
wid32Message@20
winmmDbgOut
winmmSetDebugLevel@4
wod32Message@20
