;
; Definition file of WSOCK32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "WSOCK32.dll"
EXPORTS
accept
bind
closesocket
connect
getpeername
getsockname
getsockopt
htonl
htons
inet_addr
inet_ntoa
ioctlsocket
listen
ntohl
ntohs
recv
recvfrom
select
send
sendto
setsockopt
shutdown
socket
MigrateWinsockConfiguration
gethostbyaddr
gethostbyname
getprotobyname
getprotobynumber
getservbyname
getservbyport
gethostname
WSAAsyncSelect
WSAAsyncGetHostByAddr
WSAAsyncGetHostByName
WSAAsyncGetProtoByNumber
WSAAsyncGetProtoByName
WSAAsyncGetServByPort
WSAAsyncGetServByName
WSACancelAsyncRequest
WSASetBlockingHook
WSAUnhookBlockingHook
WSAGetLastError
WSASetLastError
WSACancelBlockingCall
WSAIsBlocking
WSAStartup
WSAClean<PERSON>
__WSAFDIsSet
WEP
WSApSetPostRoutine
inet_network
getnetbyname
rcmd
rexec
rresvport
sethostname
dn_expand
WSARecvEx
s_perror
GetAddressByNameA
GetAddressByNameW
EnumProtocolsA
EnumProtocolsW
GetTypeByNameA
GetTypeByNameW
GetNameByTypeA
GetNameByTypeW
SetServiceA
SetServiceW
GetServiceA
GetServiceW
NPLoadNameSpaces
TransmitFile
AcceptEx
GetAcceptExSockaddrs
