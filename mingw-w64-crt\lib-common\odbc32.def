; 
; Exports of file ODBC32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY ODBC32.dll
EXPORTS
SQLAllocConnect
SQLAllocEnv
SQLAllocStmt
SQLBindCol
SQLCancel
SQLColAttributes
SQLConnect
SQLDescribeCol
SQLDisconnect
SQLError
SQLExecDirect
SQLExecute
SQLFetch
SQLFreeConnect
SQLFreeEnv
SQLFreeStmt
SQLGetCursorName
SQLNumResultCols
SQLPrepare
SQLRowCount
SQLSetCursorName
SQLSetParam
SQLTransact
SQLAllocHandle
SQLBindParam
SQLCloseCursor
SQLColAttribute
SQLCopyDesc
SQLEndTran
SQLFetchScroll
SQLFreeHandle
SQLGetConnectAttr
SQLGetDescField
SQLGetDescRec
SQLGetDiagField
SQLGetDiagRec
SQLGetEnvAttr
SQLGetStmtAttr
SQLSetConnectAttr
SQLColumns
SQLDriverConnect
SQLGetConnectOption
SQLGetData
SQLGetFunctions
SQLGetInfo
SQLGetStmtOption
SQLGetTypeInfo
SQLParamData
SQLPutData
SQLSetConnectOption
SQLSetStmtOption
SQLSpecialColumns
SQLStatistics
SQLTables
SQLBrowseConnect
SQLColumnPrivileges
SQLDataSources
SQLDescribeParam
SQLExtendedFetch
SQLForeignKeys
SQLMoreResults
SQLNativeSql
SQLNumParams
SQLParamOptions
SQLPrimaryKeys
SQLProcedureColumns
SQLProcedures
SQLSetPos
SQLSetScrollOptions
SQLTablePrivileges
SQLDrivers
SQLBindParameter
SQLSetDescField
SQLSetDescRec
SQLSetEnvAttr
SQLSetStmtAttr
SQLAllocHandleStd
SQLBulkOperations
CloseODBCPerfData
CollectODBCPerfData
CursorLibLockDbc
CursorLibLockDesc
CursorLibLockStmt
ODBCGetTryWaitValue
CursorLibTransact
ODBCSetTryWaitValue
DllBidEntryPoint
GetODBCSharedData
LockHandle
MpHeapAlloc
MpHeapCompact
MpHeapCreate
MpHeapDestroy
MpHeapFree
MpHeapReAlloc
MpHeapSize
MpHeapValidate
ODBCInternalConnectW
OpenODBCPerfData
PostComponentError
PostODBCComponentError
PostODBCError
SQLCancelHandle
SQLCompleteAsync
SearchStatusCode
VFreeErrors
VRetrieveDriverErrorsRowCol
SQLColAttributesW
SQLConnectW
SQLDescribeColW
ValidateErrorQueue
SQLErrorW
SQLExecDirectW
g_hHeapMalloc DATA
SQLGetCursorNameW
SQLPrepareW
SQLSetCursorNameW
SQLColAttributeW
SQLGetConnectAttrW
SQLGetDescFieldW
SQLGetDescRecW
SQLGetDiagFieldW
SQLGetDiagRecW
SQLGetStmtAttrW
SQLSetConnectAttrW
SQLColumnsW
SQLDriverConnectW
SQLGetConnectOptionW
SQLGetInfoW
SQLGetTypeInfoW
SQLSetConnectOptionW
SQLSpecialColumnsW
SQLStatisticsW
SQLTablesW
SQLBrowseConnectW
SQLColumnPrivilegesW
SQLDataSourcesW
SQLForeignKeysW
SQLNativeSqlW
SQLPrimaryKeysW
SQLProcedureColumnsW
SQLProceduresW
SQLTablePrivilegesW
SQLDriversW
SQLSetDescFieldW
SQLSetStmtAttrW
SQLColAttributesA
SQLConnectA
SQLDescribeColA
SQLErrorA
SQLExecDirectA
SQLGetCursorNameA
SQLPrepareA
SQLSetCursorNameA
SQLColAttributeA
SQLGetConnectAttrA
SQLGetDescFieldA
SQLGetDescRecA
SQLGetDiagFieldA
SQLGetDiagRecA
SQLGetStmtAttrA
SQLSetConnectAttrA
SQLColumnsA
SQLDriverConnectA
SQLGetConnectOptionA
SQLGetInfoA
SQLGetTypeInfoA
SQLSetConnectOptionA
SQLSpecialColumnsA
SQLStatisticsA
SQLTablesA
SQLBrowseConnectA
SQLColumnPrivilegesA
SQLDataSourcesA
SQLForeignKeysA
SQLNativeSqlA
SQLPrimaryKeysA
SQLProcedureColumnsA
SQLProceduresA
SQLTablePrivilegesA
SQLDriversA
SQLSetDescFieldA
SQLSetStmtAttrA
ODBCQualifyFileDSNW
