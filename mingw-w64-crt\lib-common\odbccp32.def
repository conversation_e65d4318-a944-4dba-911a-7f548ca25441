; 
; Exports of file ODBCCP32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY ODBCCP32.dll
EXPORTS
SQLInstallDriver
SQLInstallDriverManager
SQLGetInstalledDrivers
SQLGetAvailableDrivers
SQLConfigDataSource
SQLRemoveDefaultDataSource
SQLWriteDSNToIni
SQLRemoveDSNFromIni
SQLInstallODBC
SQLManageDataSources
SQLCreateDataSource
SQLGetTranslator
SQLWritePrivateProfileString
SQLGetPrivateProfileString
SQLValidDSN
SQLRemoveDriverManager
SQLInstallTranslator
SQLRemoveTranslator
SQLRemoveDriver
SQLConfigDriver
SQLInstallerError
SQLPostInstallerError
SQLReadFileDSN
SQLWriteFileDSN
SQLInstallDriverEx
SQLGetConfigMode
SQLSetConfigMode
SQLInstallTranslatorEx
SQLCreateDataSourceEx
ODBCCPlApplet
SelectTransDlg
SQLInstallDriverW
SQLInstallDriverManagerW
SQLGetInstalledDriversW
SQLGetAvailableDriversW
SQLConfigDataSourceW
SQLWriteDSNToIniW
SQLRemoveDSNFromIniW
SQLInstallODBCW
SQLCreateDataSourceW
SQLGetTranslatorW
SQLWritePrivateProfileStringW
SQLGetPrivateProfileStringW
SQLValidDSNW
SQLInstallTranslatorW
SQLRemoveTranslatorW
SQLRemoveDriverW
SQLConfigDriverW
SQLInstallerErrorW
SQLPostInstallerErrorW
SQLReadFileDSNW
SQLWriteFileDSNW
SQLInstallDriverExW
SQLInstallTranslatorExW
SQLCreateDataSourceExW
SQLLoadDriverListBox
SQLLoadDataSourcesListBox
