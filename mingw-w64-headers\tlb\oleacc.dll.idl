/*
 * Copyright (C) 2005 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";

#ifndef __WIDL__
#define threading(model)
#endif

cpp_quote("#define NAVDIR_MIN        0")
cpp_quote("#define NAVDIR_UP         1")
cpp_quote("#define NAVDIR_DOWN       2")
cpp_quote("#define NAVDIR_LEFT       3")
cpp_quote("#define NAVDIR_RIGHT      4")
cpp_quote("#define NAVDIR_NEXT       5")
cpp_quote("#define NAVDIR_PREVIOUS   6")
cpp_quote("#define NAVDIR_FIRSTCHILD 7")
cpp_quote("#define NAVDIR_LASTCHILD  8")
cpp_quote("#define NAVDIR_MAX        9")

cpp_quote("#define ROLE_SYSTEM_TITLEBAR     1")
cpp_quote("#define ROLE_SYSTEM_MENUBAR      2")
cpp_quote("#define ROLE_SYSTEM_SCROLLBAR    3")
cpp_quote("#define ROLE_SYSTEM_GRIP         4")
cpp_quote("#define ROLE_SYSTEM_SOUND        5")
cpp_quote("#define ROLE_SYSTEM_CURSOR       6")
cpp_quote("#define ROLE_SYSTEM_CARET        7")
cpp_quote("#define ROLE_SYSTEM_ALERT        8")
cpp_quote("#define ROLE_SYSTEM_WINDOW       9")
cpp_quote("#define ROLE_SYSTEM_CLIENT       10")
cpp_quote("#define ROLE_SYSTEM_MENUPOPUP    11")
cpp_quote("#define ROLE_SYSTEM_MENUITEM     12")
cpp_quote("#define ROLE_SYSTEM_TOOLTIP      13")
cpp_quote("#define ROLE_SYSTEM_APPLICATION  14")
cpp_quote("#define ROLE_SYSTEM_DOCUMENT     15")
cpp_quote("#define ROLE_SYSTEM_PANE         16")
cpp_quote("#define ROLE_SYSTEM_CHART        17")
cpp_quote("#define ROLE_SYSTEM_DIALOG       18")
cpp_quote("#define ROLE_SYSTEM_BORDER       19")
cpp_quote("#define ROLE_SYSTEM_GROUPING     20")
cpp_quote("#define ROLE_SYSTEM_SEPARATOR    21")
cpp_quote("#define ROLE_SYSTEM_TOOLBAR      22")
cpp_quote("#define ROLE_SYSTEM_STATUSBAR    23")
cpp_quote("#define ROLE_SYSTEM_TABLE        24")
cpp_quote("#define ROLE_SYSTEM_COLUMNHEADER 25")
cpp_quote("#define ROLE_SYSTEM_ROWHEADER    26")
cpp_quote("#define ROLE_SYSTEM_COLUMN       27")
cpp_quote("#define ROLE_SYSTEM_ROW          28")
cpp_quote("#define ROLE_SYSTEM_CELL         29")
cpp_quote("#define ROLE_SYSTEM_LINK         30")
cpp_quote("#define ROLE_SYSTEM_HELPBALLOON  31")
cpp_quote("#define ROLE_SYSTEM_CHARACTER    32")
cpp_quote("#define ROLE_SYSTEM_LIST         33")
cpp_quote("#define ROLE_SYSTEM_LISTITEM     34")
cpp_quote("#define ROLE_SYSTEM_OUTLINE      35")
cpp_quote("#define ROLE_SYSTEM_OUTLINEITEM  36")
cpp_quote("#define ROLE_SYSTEM_PAGETAB      37")
cpp_quote("#define ROLE_SYSTEM_PROPERTYPAGE 38")
cpp_quote("#define ROLE_SYSTEM_INDICATOR    39")
cpp_quote("#define ROLE_SYSTEM_GRAPHIC      40")
cpp_quote("#define ROLE_SYSTEM_STATICTEXT   41")
cpp_quote("#define ROLE_SYSTEM_TEXT         42")
cpp_quote("#define ROLE_SYSTEM_PUSHBUTTON   43")
cpp_quote("#define ROLE_SYSTEM_CHECKBUTTON  44")
cpp_quote("#define ROLE_SYSTEM_RADIOBUTTON  45")
cpp_quote("#define ROLE_SYSTEM_COMBOBOX     46")
cpp_quote("#define ROLE_SYSTEM_DROPLIST     47")
cpp_quote("#define ROLE_SYSTEM_PROGRESSBAR  48")
cpp_quote("#define ROLE_SYSTEM_DIAL         49")
cpp_quote("#define ROLE_SYSTEM_HOTKEYFIELD  50")
cpp_quote("#define ROLE_SYSTEM_SLIDER       51")
cpp_quote("#define ROLE_SYSTEM_SPINBUTTON   52")
cpp_quote("#define ROLE_SYSTEM_DIAGRAM      53")
cpp_quote("#define ROLE_SYSTEM_ANIMATION    54")
cpp_quote("#define ROLE_SYSTEM_EQUATION     55")
cpp_quote("#define ROLE_SYSTEM_BUTTONDROPDOWN 56")
cpp_quote("#define ROLE_SYSTEM_BUTTONMENU   57")
cpp_quote("#define ROLE_SYSTEM_BUTTONDROPDOWNGRID 58")
cpp_quote("#define ROLE_SYSTEM_WHITESPACE   59")
cpp_quote("#define ROLE_SYSTEM_PAGETABLIST  60")
cpp_quote("#define ROLE_SYSTEM_CLOCK        61")
cpp_quote("#define ROLE_SYSTEM_SPLITBUTTON  62")
cpp_quote("#define ROLE_SYSTEM_IPADDRESS    63")
cpp_quote("#define ROLE_SYSTEM_OUTLINEBUTTON 64")

cpp_quote("#define SELFLAG_NONE            0x00")
cpp_quote("#define SELFLAG_TAKEFOCUS       0x01")
cpp_quote("#define SELFLAG_TAKESELECTION   0x02")
cpp_quote("#define SELFLAG_EXTENDSELECTION 0x04")
cpp_quote("#define SELFLAG_ADDSELECTION    0x08")
cpp_quote("#define SELFLAG_REMOVESELECTION 0x10")
cpp_quote("#define SELFLAG_VALID           0x1f")

cpp_quote("#ifndef STATE_SYSTEM_UNAVAILABLE")
cpp_quote("#define STATE_SYSTEM_NORMAL          0x00000000")
cpp_quote("#define STATE_SYSTEM_UNAVAILABLE     0x00000001")
cpp_quote("#define STATE_SYSTEM_SELECTED        0x00000002")
cpp_quote("#define STATE_SYSTEM_FOCUSED         0x00000004")
cpp_quote("#define STATE_SYSTEM_PRESSED         0x00000008")
cpp_quote("#define STATE_SYSTEM_CHECKED         0x00000010")
cpp_quote("#define STATE_SYSTEM_MIXED           0x00000020")
cpp_quote("#define STATE_SYSTEM_INDETERMINATE   STATE_SYSTEM_MIXED")
cpp_quote("#define STATE_SYSTEM_READONLY        0x00000040")
cpp_quote("#define STATE_SYSTEM_HOTTRACKED      0x00000080")
cpp_quote("#define STATE_SYSTEM_DEFAULT         0x00000100")
cpp_quote("#define STATE_SYSTEM_EXPANDED        0x00000200")
cpp_quote("#define STATE_SYSTEM_COLLAPSED       0x00000400")
cpp_quote("#define STATE_SYSTEM_BUSY            0x00000800")
cpp_quote("#define STATE_SYSTEM_FLOATING        0x00001000")
cpp_quote("#define STATE_SYSTEM_MARQUEED        0x00002000")
cpp_quote("#define STATE_SYSTEM_ANIMATED        0x00004000")
cpp_quote("#define STATE_SYSTEM_INVISIBLE       0x00008000")
cpp_quote("#define STATE_SYSTEM_OFFSCREEN       0x00010000")
cpp_quote("#define STATE_SYSTEM_SIZEABLE        0x00020000")
cpp_quote("#define STATE_SYSTEM_MOVEABLE        0x00040000")
cpp_quote("#define STATE_SYSTEM_SELFVOICING     0x00080000")
cpp_quote("#define STATE_SYSTEM_FOCUSABLE       0x00100000")
cpp_quote("#define STATE_SYSTEM_SELECTABLE      0x00200000")
cpp_quote("#define STATE_SYSTEM_LINKED          0x00400000")
cpp_quote("#define STATE_SYSTEM_TRAVERSED       0x00800000")
cpp_quote("#define STATE_SYSTEM_MULTISELECTABLE 0x01000000")
cpp_quote("#define STATE_SYSTEM_EXTSELECTABLE   0x02000000")
cpp_quote("#define STATE_SYSTEM_ALERT_LOW       0x04000000")
cpp_quote("#define STATE_SYSTEM_ALERT_MEDIUM    0x08000000")
cpp_quote("#define STATE_SYSTEM_ALERT_HIGH      0x10000000")
cpp_quote("#define STATE_SYSTEM_PROTECTED       0x20000000")
cpp_quote("#define STATE_SYSTEM_VALID           0x7FFFFFFF")
cpp_quote("#endif")
cpp_quote("#ifndef STATE_SYSTEM_HASPOPUP")
cpp_quote("#define STATE_SYSTEM_HASPOPUP        0x40000000")
cpp_quote("#endif")

typedef GUID MSAAPROPID;

typedef enum AnnoScope
{
    ANNO_THIS,
    ANNO_CONTAINER
} AnnoScope;

[
    object,
    hidden,
    dual,
    uuid(618736e0-3c3d-11cf-810c-00aa00389b71),
    pointer_default(unique)
]
interface IAccessible : IDispatch
{
    typedef [unique] IAccessible  *LPACCESSIBLE;
    const long DISPID_ACC_PARENT     = -5000;
    const long DISPID_ACC_CHILDCOUNT = -5001;
    const long DISPID_ACC_CHILD      = -5002;
    const long DISPID_ACC_NAME       = -5003;
    const long DISPID_ACC_VALUE      = -5004;
    const long DISPID_ACC_DESCRIPTION= -5005;
    const long DISPID_ACC_ROLE       = -5006;
    const long DISPID_ACC_STATE      = -5007;
    const long DISPID_ACC_HELP       = -5008;
    const long DISPID_ACC_HELPTOPIC  = -5009;
    const long DISPID_ACC_KEYBOARDSHORTCUT = -5010;
    const long DISPID_ACC_FOCUS      = -5011;
    const long DISPID_ACC_SELECTION  = -5012;
    const long DISPID_ACC_DEFAULTACTION = -5013;
    const long DISPID_ACC_SELECT     = -5014;
    const long DISPID_ACC_LOCATION   = -5015;
    const long DISPID_ACC_NAVIGATE   = -5016;
    const long DISPID_ACC_HITTEST    = -5017;
    const long DISPID_ACC_DODEFAULTACTION = -5018;

    [hidden, propget, id(DISPID_ACC_PARENT)] HRESULT accParent([out, retval]IDispatch** ppdispParent);
    [hidden, propget, id(DISPID_ACC_CHILDCOUNT)] HRESULT accChildCount([out, retval] long* pcountChildren);
    [hidden, propget, id(DISPID_ACC_CHILD)] HRESULT accChild([in] VARIANT varChildID, [out, retval]IDispatch **ppdispChild);
    [hidden, propget, id(DISPID_ACC_NAME)] HRESULT accName([in, optional] VARIANT varID, [out, retval] BSTR* pszName);
    [hidden, propget, id(DISPID_ACC_VALUE)] HRESULT accValue([in, optional] VARIANT varID, [out, retval] BSTR* pszValue);
    [hidden, propget, id(DISPID_ACC_DESCRIPTION)] HRESULT accDescription([in, optional] VARIANT varID, [out, retval] BSTR *description);
    [hidden, propget, id(DISPID_ACC_ROLE)] HRESULT accRole([in, optional] VARIANT varID, [out, retval] VARIANT *role);
    [hidden, propget, id(DISPID_ACC_STATE)] HRESULT accState([in, optional] VARIANT varID, [out, retval] VARIANT *state);
    [hidden, propget, id(DISPID_ACC_HELP)] HRESULT accHelp([in, optional] VARIANT varID, [out, retval] BSTR *help);
    [hidden, propget, id(DISPID_ACC_HELPTOPIC)] HRESULT accHelpTopic([out] BSTR *helpfile, [in, optional] VARIANT varID, [out, retval] long* pidTopic);
    [hidden, propget, id(DISPID_ACC_KEYBOARDSHORTCUT)] HRESULT accKeyboardShortcut([in, optional] VARIANT varID, [out, retval] BSTR *shortcut);
    [hidden, propget, id(DISPID_ACC_FOCUS)] HRESULT accFocus([out, retval] VARIANT* pvarID);
    [hidden, propget, id(DISPID_ACC_SELECTION)] HRESULT accSelection([out, retval] VARIANT* pvarID);
    [hidden, propget, id(DISPID_ACC_DEFAULTACTION)] HRESULT accDefaultAction([in, optional] VARIANT varID, [out, retval] BSTR *action);

    [hidden, id(DISPID_ACC_SELECT)] HRESULT accSelect([in] long flagsSelect, [in, optional] VARIANT varID);
    [hidden, id(DISPID_ACC_LOCATION)] HRESULT accLocation([out] long *left, [out] long *top, [out] long *width, [out] long *height, [in, optional] VARIANT varID);
    [hidden, id(DISPID_ACC_NAVIGATE)] HRESULT accNavigate([in] long dir, [in, optional] VARIANT varStart, [out, retval] VARIANT *pvarEnd);
    [hidden, id(DISPID_ACC_HITTEST)] HRESULT accHitTest([in] long left, [in] long top, [out, retval] VARIANT *pvarID);
    [hidden, id(DISPID_ACC_DODEFAULTACTION)] HRESULT accDoDefaultAction([in, optional] VARIANT varID);

    [hidden, propput, id(DISPID_ACC_NAME)] HRESULT accName([in, optional] VARIANT varID, [in] BSTR name);
    [hidden, propput, id(DISPID_ACC_VALUE)] HRESULT accValue([in, optional] VARIANT varID, [in] BSTR value);
}

[
    object,
    uuid(03022430-ABC4-11d0-BDE2-00AA001A1953),
    hidden,
    oleautomation,
    pointer_default(unique)
]
interface IAccessibleHandler : IUnknown
{
    typedef [unique] IAccessibleHandler *LPACCESSIBLEHANDLER;

    HRESULT AccessibleObjectFromID( [in] long hwnd, [in] long lObjectID, [out] LPACCESSIBLE *pIAccessible );
}

[
    object,
    uuid(7852b78d-1cfd-41c1-a615-9c0c85960b5f),
    pointer_default(unique)
]
interface IAccIdentity : IUnknown
{
    HRESULT GetIdentityString([in] DWORD idchild,
                              [out, size_is(,*string_len)] BYTE **str,
                              [out] DWORD *string_len);
}

[
    object,
    uuid(76c0dbbb-15e0-4e7b-b61b-20eeea2001e0),
    pointer_default(unique)
]
interface IAccPropServer: IUnknown
{
    HRESULT GetPropValue([in, size_is(string_len)] const BYTE *str,
                         [in] DWORD string_len,
                         [in] MSAAPROPID idProp,
                         [out] VARIANT *value,
                         [out] BOOL *has_prop);
}

[
    object,
    uuid(6e26e776-04f0-495d-80e4-3330352e3169),
    pointer_default(unique)
]
interface IAccPropServices : IUnknown
{
    HRESULT SetPropValue([in, size_is(string_len)] const BYTE *str,
                         [in] DWORD string_len,
                         [in] MSAAPROPID idProp,
                         [in] VARIANT var);

    HRESULT SetPropServer([in, size_is(string_len)] const BYTE *str,
                          [in] DWORD string_len,
                          [in, size_is(cProps)] const MSAAPROPID *props,
                          [in] int cProps,
                          [in] IAccPropServer* pServer,
                          [in] AnnoScope AnnoScope);

    HRESULT ClearProps([in, size_is(string_len)] const BYTE *str,
                       [in] DWORD string_len,
                       [in, size_is(cProps)] const MSAAPROPID *props,
                       [in] int cProps);

    HRESULT SetHwndProp([in] HWND hwnd, [in] DWORD idObject, [in] DWORD idChild,
                    [in] MSAAPROPID idProp, [in] VARIANT var);

    HRESULT SetHwndPropStr([in] HWND hwnd, [in] DWORD idObject, [in] DWORD idChild,
                    [in] MSAAPROPID idProp, [in, string] LPWSTR str);

    HRESULT SetHwndPropServer([in] HWND hwnd,
                              [in] DWORD idObject,
                              [in] DWORD idChild,
                              [in, size_is(cProps)] const MSAAPROPID *props,
                              [in] int cProps,
                              [in] IAccPropServer *server,
                              [in] AnnoScope scope);

    HRESULT ClearHwndProps([in] HWND hwnd,
                           [in] DWORD idObject,
                           [in] DWORD idChild,
                           [in, size_is(cProps)] const MSAAPROPID *props,
                           [in] int cProps);

    HRESULT ComposeHwndIdentityString([in] HWND hwnd,
                                      [in] DWORD idObject,
                                      [in] DWORD idChild,
                                      [out, size_is(,*string_len)] BYTE **str,
                                      [out] DWORD *string_len);

    HRESULT DecomposeHwndIdentityString([in, size_is(string_len)] const BYTE *str,
                                        [in] DWORD string_len,
                                        [out] HWND *phwnd,
                                        [out] DWORD *pidObject,
                                        [out] DWORD *pidChild);

    HRESULT SetHmenuProp([in] HMENU hmenu, [in] DWORD idChild, [in] MSAAPROPID idProp, [in] VARIANT var);

    HRESULT SetHmenuPropStr([in] HMENU hmenu, [in] DWORD idChild, [in] MSAAPROPID idProp, [in, string] LPWSTR str);

    HRESULT SetHmenuPropServer([in] HMENU hmenu,
                               [in] DWORD idChild,
                               [in, size_is(cProps)] const MSAAPROPID *props,
                               [in] int cProps,
                               [in] IAccPropServer *server,
                               [in] AnnoScope scope);

    HRESULT ClearHmenuProps([in] HMENU hmenu,
                            [in] DWORD idChild,
                            [in, size_is(cProps)] const MSAAPROPID *props,
                            [in] int cProps);

    HRESULT ComposeHmenuIdentityString([in] HMENU hmenu,
                                       [in] DWORD idChild,
                                       [out, size_is(,*string_len)] BYTE **str,
                                       [out] DWORD *string_len);

    HRESULT DecomposeHmenuIdentityString([in, size_is(string_len)] const BYTE *str,
                                         [in] DWORD string_len,
                                         [out] HMENU *phmenu,
                                         [out] DWORD *pidChild);
}

[
    uuid(1ea4dbf0-3c3b-11cf-810c-00aa00389b71),
    lcid(0),
    version(1.1),
    hidden
]
library Accessibility
{
    importlib ("stdole2.tlb");
    interface IAccessible;
    interface IAccessibleHandler;

    interface IAccIdentity;
    interface IAccPropServer;
    interface IAccPropServices;

    [
        uuid(b5f8350b-0548-48b1-a6ee-88bd00b4a5e7),
        threading(apartment)
    ]
    coclass CAccPropServices
    {
        interface IAccPropServices;
    }
};

cpp_quote("DEFINE_GUID(CLSID_AccPropServices, 0xb5f8350b, 0x0548, 0x48b1, 0xa6, 0xee, 0x88, 0xbd, 0x00, 0xb4, 0xa5, 0xe7);")
cpp_quote("DEFINE_GUID(IIS_IsOleaccProxy, 0x902697fa, 0x80e4, 0x4560, 0x80, 0x2a, 0xa1, 0x3f, 0x22, 0xa6, 0x47, 0x09);")

cpp_quote("LRESULT WINAPI LresultFromObject(REFIID,WPARAM,LPUNKNOWN);")
cpp_quote("HRESULT WINAPI ObjectFromLresult(LRESULT,REFIID,WPARAM,void **);")
cpp_quote("HRESULT WINAPI WindowFromAccessibleObject(IAccessible *,HWND *);")
cpp_quote("HRESULT WINAPI AccessibleObjectFromWindow(HWND,DWORD,REFIID,void **);")
cpp_quote("HRESULT WINAPI AccessibleObjectFromEvent(HWND,DWORD,DWORD,IAccessible **,VARIANT *);")
cpp_quote("HRESULT WINAPI AccessibleObjectFromPoint(POINT,IAccessible **,VARIANT *);")
cpp_quote("HRESULT WINAPI AccessibleChildren(IAccessible *,LONG,LONG,VARIANT *,LONG *);")

cpp_quote("void WINAPI GetOleaccVersionInfo(DWORD *,DWORD *);")
cpp_quote("HRESULT WINAPI CreateStdAccessibleObject(HWND,LONG,REFIID,void **);")
cpp_quote("HRESULT WINAPI CreateStdAccessibleProxyA(HWND,LPCSTR,LONG,REFIID,void **);")
cpp_quote("HRESULT WINAPI CreateStdAccessibleProxyW(HWND,LPCWSTR,LONG,REFIID,void **);")
cpp_quote("#define CreateStdAccessibleProxy WINELIB_NAME_AW(CreateStdAccessibleProxy)")

cpp_quote("UINT WINAPI GetRoleTextA(DWORD,LPSTR,UINT);")
cpp_quote("UINT WINAPI GetRoleTextW(DWORD,LPWSTR,UINT);")
cpp_quote("#define GetRoleText WINELIB_NAME_AW(GetRoleText)")
cpp_quote("UINT WINAPI GetStateTextA(DWORD,LPSTR,UINT);")
cpp_quote("UINT WINAPI GetStateTextW(DWORD,LPWSTR,UINT);")
cpp_quote("#define GetStateText WINELIB_NAME_AW(GetStateText)")
