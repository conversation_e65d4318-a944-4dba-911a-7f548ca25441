LIBRARY SCHANNEL.dll
EXPORTS
SpLsaModeInitialize
AcceptSecurityContext
AcquireCredentialsHandleA
AcquireCredentialsHandleW
ApplyControlToken
CloseSslPerformanceData
CollectSslPerformanceData
CompleteAuthToken
DeleteSecurityContext
EnumerateSecurityPackagesA
EnumerateSecurityPackagesW
FreeContextBuffer
FreeCredentialsHandle
ImpersonateSecurityContext
InitSecurityInterfaceA
InitSecurityInterfaceW
InitializeSecurityContextA
InitializeSecurityContextW
MakeSignature
OpenSslPerformanceData
QueryContextAttributesA
QueryContextAttributesW
QuerySecurityPackageInfoA
QuerySecurityPackageInfoW
RevertSecurityContext
SealMessage
SpUserModeInitialize
SslCrackCertificate
SslEmptyCacheA
SslEmptyCacheW
SslFreeCertificate
SslFreeCustomBuffer
SslGenerateKeyPair
SslGenerateRandomBits
SslGetMaximumKeySize
SslGetServerIdentity
SslLoadCertificate
UnsealMessage
VerifySignature
