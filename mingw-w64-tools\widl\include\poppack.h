/*
 * Copyright (C) 1999 <PERSON><PERSON>l
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#if defined(__WINE_PSHPACK_H15)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H15
#  endif
/* Depth == 15 */

#  if __WINE_PSHPACK_H14 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H14 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H14 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H14)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H14
#  endif
/* Depth == 14 */

#  if __WINE_PSHPACK_H13 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H13 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H13 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H13)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H13
#  endif
/* Depth == 13 */

#  if __WINE_PSHPACK_H12 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H12 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H12 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H12)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H12
#  endif
/* Depth == 12 */

#  if __WINE_PSHPACK_H11 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H11 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H11 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H11)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H11
#  endif
/* Depth == 11 */

#  if __WINE_PSHPACK_H10 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H10 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H10 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H10)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H10
#  endif
/* Depth == 10 */

#  if __WINE_PSHPACK_H9 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H9 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H9 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H9)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H9
#  endif
/* Depth == 9 */

#  if __WINE_PSHPACK_H8 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H8 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H8 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H8)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H8
#  endif
/* Depth == 8 */

#  if __WINE_PSHPACK_H7 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H7 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H7 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H7)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H7
#  endif
/* Depth == 7 */

#  if __WINE_PSHPACK_H6 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H6 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H6 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H6)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H6
#  endif
/* Depth == 6 */

#  if __WINE_PSHPACK_H5 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H5 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H5 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H5)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H5
#  endif
/* Depth == 5 */

#  if __WINE_PSHPACK_H4 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H4 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H4 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H4)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H4
#  endif
/* Depth == 4 */

#  if __WINE_PSHPACK_H3 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H3 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H3 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H3)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H3
#  endif
/* Depth == 3 */

#  if __WINE_PSHPACK_H2 == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H2 == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H2 == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H2)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H2
#  endif
/* Depth == 2 */

#  if __WINE_PSHPACK_H == 1
#    pragma pack(1)
#  elif __WINE_PSHPACK_H == 2
#    pragma pack(2)
#  elif __WINE_PSHPACK_H == 8
#    pragma pack(8)
#  else
#    pragma pack(4)
#  endif

#elif defined(__WINE_PSHPACK_H)
#  ifndef __WINE_INTERNAL_POPPACK
#    undef __WINE_PSHPACK_H
#  endif
/* Depth == 1 */

#  if defined(__SUNPRO_CC)
#    warning "Assuming a default alignment of 4"
#    pragma pack(4)
#  else
#    pragma pack()
#  endif

#else
/* Depth == 0 ! */

#error "Popping alignment isn't possible since no alignment has been pushed"

#endif

#undef __WINE_INTERNAL_POPPACK
