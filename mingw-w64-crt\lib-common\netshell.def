; 
; Exports of file netshell.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY netshell.dll
EXPORTS
DoInitialCleanup
DllCanUnloadNow
DllGetClassObject
DllRegisterServer
DllUnregisterServer
HrCreateDesktopIcon
HrGetAnswerFileParametersForNetCard
HrGetExtendedStatusFromNCS
HrGetIconFromMediaType
HrGetIconFromMediaTypeEx
HrGetInstanceGuidOfPreNT5NetCardInstance
HrGetNetConExtendedStatusFromGuid
HrGetNetConExtendedStatusFromINetConnection
HrGetStatusStringFromNetConExtendedStatus
HrIsIpStateCheckingEnabled
HrLaunchConnection
HrLaunchConnectionEx
HrLaunchNetworkOptionalComponents
HrOemUpgrade
HrRenameConnection
HrRunWizard
InvokeDunFile
NcFreeNetconProperties
NcIsV<PERSON>ConnectionName
NetSetupAddRasConnection
NetSetupFinishInstall
NetSetupInstallSoftware
NetSetupPrepareSysPrep
NetSetupRequestWizardPages
NetSetupSetProgressCallback
NormalizeExtendedStatus
RaiseSupportDialog
RepairConnection
StartNCW
