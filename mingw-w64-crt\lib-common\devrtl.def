;
; Definition file of DEVRTL.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "DEVRTL.dll"
EXPORTS
DevRtlCloseTextLogSection
DevRtlCreateTextLogSectionA
DevRtlCreateTextLogSectionW
DevRtlGetThreadLogToken
DevRtlSetThreadLogToken
DevRtlWriteTextLog
DevRtlWriteTextLogError
NdxTableAddObject
NdxTableAddObjectToList
NdxTableClose
NdxTableFirstObject
NdxTableFirstObjectInList
NdxTableGetObjectName
NdxTableGetObjectType
NdxTableGetObjectTypeCount
NdxTableGetObjectTypeName
NdxTableGetPropertyTypeClass
NdxTableGetPropertyTypeCount
NdxTableGetPropertyTypeName
NdxTableGetPropertyValue
NdxTableNextObject
NdxTableObjectFromName
NdxTableObjectFromP<PERSON>
NdxTableOpen
NdxTableRemoveObject
NdxTableRemoveObjectFromList
NdxTableSetObjectPointer
NdxTableSetPropertyValue
NdxTableSetTypeDefinition
