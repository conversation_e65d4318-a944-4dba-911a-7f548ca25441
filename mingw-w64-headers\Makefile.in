# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(basehead_HEADERS) $(ddkhead_HEADERS) \
	$(gdiplushead_HEADERS) $(glhead_HEADERS) $(idlhead_HEADERS) \
	$(khrhead_HEADERS) $(mingwhelperhead_HEADERS) \
	$(noinst_HEADERS) $(sechead_HEADERS) $(secsyshead_HEADERS) \
	$(syshead_HEADERS) $(wrlhead_HEADERS) \
	$(wrlwrappershead_HEADERS) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = config.h
CONFIG_CLEAN_FILES = crt/_mingw.h
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(baseheaddir)" "$(DESTDIR)$(ddkheaddir)" \
	"$(DESTDIR)$(gdiplusheaddir)" "$(DESTDIR)$(glheaddir)" \
	"$(DESTDIR)$(idlheaddir)" "$(DESTDIR)$(khrheaddir)" \
	"$(DESTDIR)$(mingwhelperheaddir)" "$(DESTDIR)$(sdksheaddir)" \
	"$(DESTDIR)$(secheaddir)" "$(DESTDIR)$(secsysheaddir)" \
	"$(DESTDIR)$(sysheaddir)" "$(DESTDIR)$(wrlheaddir)" \
	"$(DESTDIR)$(wrlwrappersheaddir)"
HEADERS = $(basehead_HEADERS) $(ddkhead_HEADERS) \
	$(gdiplushead_HEADERS) $(glhead_HEADERS) $(idlhead_HEADERS) \
	$(khrhead_HEADERS) $(mingwhelperhead_HEADERS) \
	$(nodist_sdkshead_HEADERS) $(noinst_HEADERS) \
	$(sechead_HEADERS) $(secsyshead_HEADERS) $(syshead_HEADERS) \
	$(wrlhead_HEADERS) $(wrlwrappershead_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP) \
	config.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
AM_RECURSIVE_TARGETS = cscope
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/config.h.in \
	$(top_srcdir)/build-aux/config.guess \
	$(top_srcdir)/build-aux/config.sub \
	$(top_srcdir)/build-aux/install-sh \
	$(top_srcdir)/build-aux/missing $(top_srcdir)/crt/_mingw.h.in \
	ChangeLog build-aux/config.guess build-aux/config.sub \
	build-aux/install-sh build-aux/missing
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
DIST_TARGETS = dist-gzip
# Exists only to be overridden by the user if desired.
AM_DISTCHECK_DVI_TARGET = dvi
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BASEHEAD_LIST = @BASEHEAD_LIST@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DDKHEAD_LIST = @DDKHEAD_LIST@
DEFAULT_MSVCRT_VERSION = @DEFAULT_MSVCRT_VERSION@
DEFAULT_WIN32_WINNT = @DEFAULT_WIN32_WINNT@
DEFS = @DEFS@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
ETAGS = @ETAGS@
GDIPLUSHEAD_LIST = @GDIPLUSHEAD_LIST@
GLHEAD_LIST = @GLHEAD_LIST@
IDLHEAD_LIST = @IDLHEAD_LIST@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
KHRHEAD_LIST = @KHRHEAD_LIST@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MINGWHELPERHEAD_LIST = @MINGWHELPERHEAD_LIST@
MINGW_HAS_DDK = @MINGW_HAS_DDK@
MKDIR_P = @MKDIR_P@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
SECHEAD_LIST = @SECHEAD_LIST@
SECSYSHEAD_LIST = @SECSYSHEAD_LIST@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
SYSHEAD_LIST = @SYSHEAD_LIST@
VERSION = @VERSION@
WIDL = @WIDL@
WRLHEAD_LIST = @WRLHEAD_LIST@
WRLWRAPPERSHEAD_LIST = @WRLWRAPPERSHEAD_LIST@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
am__leading_dot = @am__leading_dot@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
baseheaddir = $(includedir)
sysheaddir = $(baseheaddir)/sys
secheaddir = $(baseheaddir)/sec_api
secsysheaddir = $(baseheaddir)/sec_api/sys
glheaddir = $(baseheaddir)/GL
khrheaddir = $(baseheaddir)/KHR
gdiplusheaddir = $(baseheaddir)/gdiplus
wrlheaddir = $(baseheaddir)/wrl
wrlwrappersheaddir = $(baseheaddir)/wrl/wrappers
mingwhelperheaddir = $(baseheaddir)/psdk_inc
sdksheaddir = $(baseheaddir)/sdks
basehead_HEADERS = @BASEHEAD_LIST@
syshead_HEADERS = @SYSHEAD_LIST@
sechead_HEADERS = @SECHEAD_LIST@
secsyshead_HEADERS = @SECSYSHEAD_LIST@
glhead_HEADERS = @GLHEAD_LIST@
khrhead_HEADERS = @KHRHEAD_LIST@
gdiplushead_HEADERS = @GDIPLUSHEAD_LIST@
wrlhead_HEADERS = @WRLHEAD_LIST@
wrlwrappershead_HEADERS = @WRLWRAPPERSHEAD_LIST@
mingwhelperhead_HEADERS = @MINGWHELPERHEAD_LIST@
nodist_sdkshead_HEADERS = _mingw_ddk.h
noinst_HEADERS = crt/sdks/_mingw_ddk.h.in
ddkheaddir = $(baseheaddir)/ddk
idlheaddir = $(baseheaddir)
ddkhead_HEADERS = @DDKHEAD_LIST@
idlhead_HEADERS = @IDLHEAD_LIST@
CLEANFILES = $(nodist_sdkshead_HEADERS)
DISTCHECK_CONFIGURE_FLAGS = --enable-crt --enable-sdk=all --enable-idl
EXTRA_DIST = $(srcdir)/ChangeLog.* include crt ddk tlb
EXTRA_HEADERS = \
  include/*.c \
  include/*.dlg \
  include/*.h \
  include/*.h16 \
  include/*.hxx \
  include/*.idl \
  include/*.inl \
  include/*.rh \
  include/*.ver \
  include/GL/*.h \
  include/psdk_inc/*.h \
  include/gdiplus/*.h \
  include/wrl/*.h \
  include/wrl/wrappers/*.h \
  crt/*.h \
  crt/*.inl \
  crt/sys/*.h \
  crt/sec_api/*.h \
  crt/sec_api/sys/*.h \
  ddk/include/ddk/*.h \
  tlb/*.tlb

@HAVE_WIDL_TRUE@IDL_SRCS = \
@HAVE_WIDL_TRUE@  include/activation.idl \
@HAVE_WIDL_TRUE@  include/activaut.idl \
@HAVE_WIDL_TRUE@  include/activdbg.idl \
@HAVE_WIDL_TRUE@  include/activdbg100.idl \
@HAVE_WIDL_TRUE@  include/activprof.idl \
@HAVE_WIDL_TRUE@  include/activscp.idl \
@HAVE_WIDL_TRUE@  include/adhoc.idl \
@HAVE_WIDL_TRUE@  include/alg.idl \
@HAVE_WIDL_TRUE@  include/amstream.idl \
@HAVE_WIDL_TRUE@  include/amvideo.idl \
@HAVE_WIDL_TRUE@  include/asyncinfo.idl \
@HAVE_WIDL_TRUE@  include/audioclient.idl \
@HAVE_WIDL_TRUE@  include/audioendpoints.idl \
@HAVE_WIDL_TRUE@  include/audiopolicy.idl \
@HAVE_WIDL_TRUE@  include/austream.idl \
@HAVE_WIDL_TRUE@  include/bdaiface.idl \
@HAVE_WIDL_TRUE@  include/bits.idl \
@HAVE_WIDL_TRUE@  include/bits1_5.idl \
@HAVE_WIDL_TRUE@  include/bits2_0.idl \
@HAVE_WIDL_TRUE@  include/bits2_5.idl \
@HAVE_WIDL_TRUE@  include/bits3_0.idl \
@HAVE_WIDL_TRUE@  include/bits5_0.idl \
@HAVE_WIDL_TRUE@  include/comadmin.idl \
@HAVE_WIDL_TRUE@  include/commoncontrols.idl \
@HAVE_WIDL_TRUE@  include/control.idl \
@HAVE_WIDL_TRUE@  include/credentialprovider.idl \
@HAVE_WIDL_TRUE@  include/ctfutb.idl \
@HAVE_WIDL_TRUE@  include/ctxtcall.idl \
@HAVE_WIDL_TRUE@  include/d3d10.idl \
@HAVE_WIDL_TRUE@  include/d3d10_1.idl \
@HAVE_WIDL_TRUE@  include/d3d10sdklayers.idl \
@HAVE_WIDL_TRUE@  include/d3d11.idl \
@HAVE_WIDL_TRUE@  include/d3d11_1.idl \
@HAVE_WIDL_TRUE@  include/d3d11_2.idl \
@HAVE_WIDL_TRUE@  include/d3d11_3.idl \
@HAVE_WIDL_TRUE@  include/d3d11_4.idl \
@HAVE_WIDL_TRUE@  include/d3d11on12.idl \
@HAVE_WIDL_TRUE@  include/d3d11sdklayers.idl \
@HAVE_WIDL_TRUE@  include/d3d12.idl \
@HAVE_WIDL_TRUE@  include/d3d12sdklayers.idl \
@HAVE_WIDL_TRUE@  include/d3d12shader.idl \
@HAVE_WIDL_TRUE@  include/d3dcommon.idl \
@HAVE_WIDL_TRUE@  include/dbgprop.idl \
@HAVE_WIDL_TRUE@  include/dcommon.idl \
@HAVE_WIDL_TRUE@  include/dcompanimation.idl \
@HAVE_WIDL_TRUE@  include/ddstream.idl \
@HAVE_WIDL_TRUE@  include/dinputd.idl \
@HAVE_WIDL_TRUE@  include/dimm.idl \
@HAVE_WIDL_TRUE@  include/directmanipulation.idl \
@HAVE_WIDL_TRUE@  include/dispex.idl \
@HAVE_WIDL_TRUE@  include/dmodshow.idl \
@HAVE_WIDL_TRUE@  include/docobj.idl \
@HAVE_WIDL_TRUE@  include/docobjectservice.idl \
@HAVE_WIDL_TRUE@  include/documenttarget.idl \
@HAVE_WIDL_TRUE@  include/devicetopology.idl \
@HAVE_WIDL_TRUE@  include/downloadmgr.idl \
@HAVE_WIDL_TRUE@  include/drmexternals.idl \
@HAVE_WIDL_TRUE@  include/dvdif.idl \
@HAVE_WIDL_TRUE@  include/dwrite.idl \
@HAVE_WIDL_TRUE@  include/dwrite_1.idl \
@HAVE_WIDL_TRUE@  include/dwrite_2.idl \
@HAVE_WIDL_TRUE@  include/dwrite_3.idl \
@HAVE_WIDL_TRUE@  include/dxgi.idl \
@HAVE_WIDL_TRUE@  include/dxgi1_2.idl \
@HAVE_WIDL_TRUE@  include/dxgi1_3.idl \
@HAVE_WIDL_TRUE@  include/dxgi1_4.idl \
@HAVE_WIDL_TRUE@  include/dxgi1_5.idl \
@HAVE_WIDL_TRUE@  include/dxgi1_6.idl \
@HAVE_WIDL_TRUE@  include/dxgicommon.idl \
@HAVE_WIDL_TRUE@  include/dxgidebug.idl \
@HAVE_WIDL_TRUE@  include/dxgiformat.idl \
@HAVE_WIDL_TRUE@  include/dxgitype.idl \
@HAVE_WIDL_TRUE@  include/dxva2api.idl \
@HAVE_WIDL_TRUE@  include/dxvahd.idl \
@HAVE_WIDL_TRUE@  include/endpointvolume.idl \
@HAVE_WIDL_TRUE@  include/eventtoken.idl \
@HAVE_WIDL_TRUE@  include/evr.idl \
@HAVE_WIDL_TRUE@  include/evr9.idl \
@HAVE_WIDL_TRUE@  include/exdisp.idl \
@HAVE_WIDL_TRUE@  include/filter.idl \
@HAVE_WIDL_TRUE@  include/fsrm.idl \
@HAVE_WIDL_TRUE@  include/fsrmenums.idl \
@HAVE_WIDL_TRUE@  include/fsrmquota.idl \
@HAVE_WIDL_TRUE@  include/fsrmreports.idl \
@HAVE_WIDL_TRUE@  include/fsrmscreen.idl \
@HAVE_WIDL_TRUE@  include/fusion.idl \
@HAVE_WIDL_TRUE@  include/fwptypes.idl \
@HAVE_WIDL_TRUE@  include/hstring.idl \
@HAVE_WIDL_TRUE@  include/icftypes.idl \
@HAVE_WIDL_TRUE@  include/icodecapi.idl \
@HAVE_WIDL_TRUE@  include/iketypes.idl \
@HAVE_WIDL_TRUE@  include/inputscope.idl \
@HAVE_WIDL_TRUE@  include/inspectable.idl \
@HAVE_WIDL_TRUE@  include/ivectorchangedeventargs.idl \
@HAVE_WIDL_TRUE@  include/locationapi.idl \
@HAVE_WIDL_TRUE@  include/oaidl.idl \
@HAVE_WIDL_TRUE@  include/ocidl.idl \
@HAVE_WIDL_TRUE@  include/comcat.idl \
@HAVE_WIDL_TRUE@  include/mediaobj.idl \
@HAVE_WIDL_TRUE@  include/mfcaptureengine.idl \
@HAVE_WIDL_TRUE@  include/mfd3d12.idl \
@HAVE_WIDL_TRUE@  include/mfidl.idl \
@HAVE_WIDL_TRUE@  include/mfmediacapture.idl \
@HAVE_WIDL_TRUE@  include/mfobjects.idl \
@HAVE_WIDL_TRUE@  include/mfplay.idl \
@HAVE_WIDL_TRUE@  include/mfreadwrite.idl \
@HAVE_WIDL_TRUE@  include/mftransform.idl \
@HAVE_WIDL_TRUE@  include/mmdeviceapi.idl \
@HAVE_WIDL_TRUE@  include/mscoree.idl \
@HAVE_WIDL_TRUE@  include/msctf.idl \
@HAVE_WIDL_TRUE@  include/msinkaut.idl \
@HAVE_WIDL_TRUE@  include/mshtml.idl \
@HAVE_WIDL_TRUE@  include/mshtmhst.idl \
@HAVE_WIDL_TRUE@  include/msopc.idl \
@HAVE_WIDL_TRUE@  include/msxml.idl \
@HAVE_WIDL_TRUE@  include/msxml2.idl \
@HAVE_WIDL_TRUE@  include/msxml6.idl \
@HAVE_WIDL_TRUE@  include/napcertrelyingparty.idl \
@HAVE_WIDL_TRUE@  include/napcommon.idl \
@HAVE_WIDL_TRUE@  include/napenforcementclient.idl \
@HAVE_WIDL_TRUE@  include/napmanagement.idl \
@HAVE_WIDL_TRUE@  include/napprotocol.idl \
@HAVE_WIDL_TRUE@  include/napservermanagement.idl \
@HAVE_WIDL_TRUE@  include/napsystemhealthagent.idl \
@HAVE_WIDL_TRUE@  include/napsystemhealthvalidator.idl \
@HAVE_WIDL_TRUE@  include/naptypes.idl \
@HAVE_WIDL_TRUE@  include/netcfgn.idl \
@HAVE_WIDL_TRUE@  include/netcfgx.idl \
@HAVE_WIDL_TRUE@  include/netfw.idl \
@HAVE_WIDL_TRUE@  include/netlistmgr.idl \
@HAVE_WIDL_TRUE@  include/objectarray.idl \
@HAVE_WIDL_TRUE@  include/objidl.idl \
@HAVE_WIDL_TRUE@  include/objidlbase.idl \
@HAVE_WIDL_TRUE@  include/oleidl.idl \
@HAVE_WIDL_TRUE@  include/optary.idl \
@HAVE_WIDL_TRUE@  include/portabledevicetypes.idl \
@HAVE_WIDL_TRUE@  include/propidl.idl \
@HAVE_WIDL_TRUE@  include/propsys.idl \
@HAVE_WIDL_TRUE@  include/rdpencomapi.idl \
@HAVE_WIDL_TRUE@  include/regbag.idl \
@HAVE_WIDL_TRUE@  include/sapi51.idl \
@HAVE_WIDL_TRUE@  include/sapi53.idl \
@HAVE_WIDL_TRUE@  include/sapi54.idl \
@HAVE_WIDL_TRUE@  include/sensorsapi.idl \
@HAVE_WIDL_TRUE@  include/servprov.idl \
@HAVE_WIDL_TRUE@  include/shldisp.idl \
@HAVE_WIDL_TRUE@  include/shobjidl.idl \
@HAVE_WIDL_TRUE@  include/shtypes.idl \
@HAVE_WIDL_TRUE@  include/spatialaudioclient.idl \
@HAVE_WIDL_TRUE@  include/spellcheck.idl \
@HAVE_WIDL_TRUE@  include/strmif.idl \
@HAVE_WIDL_TRUE@  include/structuredquerycondition.idl \
@HAVE_WIDL_TRUE@  include/systemmediatransportcontrolsinterop.idl \
@HAVE_WIDL_TRUE@  include/taskschd.idl \
@HAVE_WIDL_TRUE@  include/textstor.idl \
@HAVE_WIDL_TRUE@  include/thumbcache.idl \
@HAVE_WIDL_TRUE@  include/tlbref.idl \
@HAVE_WIDL_TRUE@  include/tlogstg.idl \
@HAVE_WIDL_TRUE@  include/tpcshrd.idl \
@HAVE_WIDL_TRUE@  include/tsvirtualchannels.idl \
@HAVE_WIDL_TRUE@  include/tuner.idl \
@HAVE_WIDL_TRUE@  include/uianimation.idl \
@HAVE_WIDL_TRUE@  include/uiautomationclient.idl \
@HAVE_WIDL_TRUE@  include/uiautomationcore.idl \
@HAVE_WIDL_TRUE@  include/uiviewsettingsinterop.idl \
@HAVE_WIDL_TRUE@  include/unknwn.idl \
@HAVE_WIDL_TRUE@  include/unknwnbase.idl \
@HAVE_WIDL_TRUE@  include/urlhist.idl \
@HAVE_WIDL_TRUE@  include/urlmon.idl \
@HAVE_WIDL_TRUE@  include/vdslun.idl \
@HAVE_WIDL_TRUE@  include/vidcap.idl \
@HAVE_WIDL_TRUE@  include/vsadmin.idl \
@HAVE_WIDL_TRUE@  include/vsbackup.idl \
@HAVE_WIDL_TRUE@  include/vsmgmt.idl \
@HAVE_WIDL_TRUE@  include/vsprov.idl \
@HAVE_WIDL_TRUE@  include/vss.idl \
@HAVE_WIDL_TRUE@  include/vswriter.idl \
@HAVE_WIDL_TRUE@  include/wbemads.idl \
@HAVE_WIDL_TRUE@  include/wbemcli.idl \
@HAVE_WIDL_TRUE@  include/wbemdisp.idl \
@HAVE_WIDL_TRUE@  include/wbemprov.idl \
@HAVE_WIDL_TRUE@  include/wbemtran.idl \
@HAVE_WIDL_TRUE@  include/wdstptmgmt.idl \
@HAVE_WIDL_TRUE@  include/mediaobj.idl \
@HAVE_WIDL_TRUE@  include/medparam.idl \
@HAVE_WIDL_TRUE@  include/mmstream.idl \
@HAVE_WIDL_TRUE@  include/proofofpossessioncookieinfo.idl \
@HAVE_WIDL_TRUE@  include/qedit.idl \
@HAVE_WIDL_TRUE@  include/qnetwork.idl \
@HAVE_WIDL_TRUE@  include/relogger.idl \
@HAVE_WIDL_TRUE@  include/robuffer.idl \
@HAVE_WIDL_TRUE@  include/rtworkq.idl \
@HAVE_WIDL_TRUE@  include/vmr9.idl \
@HAVE_WIDL_TRUE@  include/wincodec.idl \
@HAVE_WIDL_TRUE@  include/wincodecsdk.idl \
@HAVE_WIDL_TRUE@  include/wmcontainer.idl \
@HAVE_WIDL_TRUE@  include/wmp.idl \
@HAVE_WIDL_TRUE@  include/wmprealestate.idl \
@HAVE_WIDL_TRUE@  include/wmpservices.idl \
@HAVE_WIDL_TRUE@  include/wmsbuffer.idl \
@HAVE_WIDL_TRUE@  include/wmsdkidl.idl \
@HAVE_WIDL_TRUE@  include/wmsecure.idl \
@HAVE_WIDL_TRUE@  include/wsdattachment.idl \
@HAVE_WIDL_TRUE@  include/wsdbase.idl \
@HAVE_WIDL_TRUE@  include/wsdclient.idl \
@HAVE_WIDL_TRUE@  include/wsddisco.idl \
@HAVE_WIDL_TRUE@  include/wsdhost.idl \
@HAVE_WIDL_TRUE@  include/wsdxml.idl \
@HAVE_WIDL_TRUE@  include/wsmandisp.idl \
@HAVE_WIDL_TRUE@  include/wtypesbase.idl \
@HAVE_WIDL_TRUE@  include/windows.devices.enumeration.idl \
@HAVE_WIDL_TRUE@  include/windows.devices.haptics.idl \
@HAVE_WIDL_TRUE@  include/windows.devices.power.idl \
@HAVE_WIDL_TRUE@  include/windows.foundation.idl \
@HAVE_WIDL_TRUE@  include/windows.foundation.collections.idl \
@HAVE_WIDL_TRUE@  include/windows.foundation.metadata.idl \
@HAVE_WIDL_TRUE@  include/windows.foundation.numerics.idl \
@HAVE_WIDL_TRUE@  include/windows.gaming.input.idl \
@HAVE_WIDL_TRUE@  include/windows.gaming.input.custom.idl \
@HAVE_WIDL_TRUE@  include/windows.gaming.input.forcefeedback.idl \
@HAVE_WIDL_TRUE@  include/windows.gaming.ui.idl \
@HAVE_WIDL_TRUE@  include/windows.globalization.idl \
@HAVE_WIDL_TRUE@  include/windows.graphics.capture.idl \
@HAVE_WIDL_TRUE@  include/windows.graphics.directx.idl \
@HAVE_WIDL_TRUE@  include/windows.graphics.directx.direct3d11.idl \
@HAVE_WIDL_TRUE@  include/windows.graphics.effects.idl \
@HAVE_WIDL_TRUE@  include/windows.graphics.holographic.idl \
@HAVE_WIDL_TRUE@  include/windows.media.idl \
@HAVE_WIDL_TRUE@  include/windows.media.closedcaptioning.idl \
@HAVE_WIDL_TRUE@  include/windows.media.devices.idl \
@HAVE_WIDL_TRUE@  include/windows.media.speechrecognition.idl \
@HAVE_WIDL_TRUE@  include/windows.media.speechsynthesis.idl \
@HAVE_WIDL_TRUE@  include/windows.perception.spatial.idl \
@HAVE_WIDL_TRUE@  include/windows.perception.spatial.surfaces.idl \
@HAVE_WIDL_TRUE@  include/windows.security.credentials.idl \
@HAVE_WIDL_TRUE@  include/windows.security.cryptography.idl \
@HAVE_WIDL_TRUE@  include/windows.storage.idl \
@HAVE_WIDL_TRUE@  include/windows.storage.streams.idl \
@HAVE_WIDL_TRUE@  include/windows.system.idl \
@HAVE_WIDL_TRUE@  include/windows.system.power.idl \
@HAVE_WIDL_TRUE@  include/windows.system.profile.systemmanufacturers.idl \
@HAVE_WIDL_TRUE@  include/windows.system.threading.idl \
@HAVE_WIDL_TRUE@  include/windows.system.userprofile.idl \
@HAVE_WIDL_TRUE@  include/windows.ui.idl \
@HAVE_WIDL_TRUE@  include/windows.ui.composition.idl \
@HAVE_WIDL_TRUE@  include/windows.ui.composition.interop.idl \
@HAVE_WIDL_TRUE@  include/windows.ui.core.idl \
@HAVE_WIDL_TRUE@  include/windows.ui.viewmanagement.idl \
@HAVE_WIDL_TRUE@  include/windowscontracts.idl \
@HAVE_WIDL_TRUE@  include/wmcodecdsp.idl \
@HAVE_WIDL_TRUE@  include/wmdrmsdk.idl \
@HAVE_WIDL_TRUE@  include/wpcapi.idl \
@HAVE_WIDL_TRUE@  include/wtypes.idl \
@HAVE_WIDL_TRUE@  include/wuapi.idl \
@HAVE_WIDL_TRUE@  include/xapo.idl \
@HAVE_WIDL_TRUE@  include/xaudio2.idl \
@HAVE_WIDL_TRUE@  include/xaudio2fx.idl \
@HAVE_WIDL_TRUE@  include/xmllite.idl \
@HAVE_WIDL_TRUE@  include/xpsdigitalsignature.idl \
@HAVE_WIDL_TRUE@  include/xpsobjectmodel_1.idl \
@HAVE_WIDL_TRUE@  include/xpsrassvc.idl \
@HAVE_WIDL_TRUE@  include/xpsobjectmodel.idl \
@HAVE_WIDL_TRUE@  include/xpsprint.idl

@HAVE_WIDL_TRUE@TLB_SRCS = \
@HAVE_WIDL_TRUE@  tlb/oleacc.dll.idl \
@HAVE_WIDL_TRUE@  tlb/stdole2.idl

@HAVE_WIDL_TRUE@BUILT_SOURCES = $(IDL_SRCS:.idl=.h) $(TLB_SRCS:.idl=.tlb)
all: $(BUILT_SOURCES) config.h
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --foreign \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

config.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/config.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status config.h
$(srcdir)/config.h.in: @MAINTAINER_MODE_TRUE@ $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f config.h stamp-h1
crt/_mingw.h: $(top_builddir)/config.status $(top_srcdir)/crt/_mingw.h.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
install-baseheadHEADERS: $(basehead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(basehead_HEADERS)'; test -n "$(baseheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(baseheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(baseheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(baseheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(baseheaddir)" || exit $$?; \
	done

uninstall-baseheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(basehead_HEADERS)'; test -n "$(baseheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(baseheaddir)'; $(am__uninstall_files_from_dir)
install-ddkheadHEADERS: $(ddkhead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(ddkhead_HEADERS)'; test -n "$(ddkheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(ddkheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(ddkheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(ddkheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(ddkheaddir)" || exit $$?; \
	done

uninstall-ddkheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(ddkhead_HEADERS)'; test -n "$(ddkheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(ddkheaddir)'; $(am__uninstall_files_from_dir)
install-gdiplusheadHEADERS: $(gdiplushead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(gdiplushead_HEADERS)'; test -n "$(gdiplusheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(gdiplusheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(gdiplusheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(gdiplusheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(gdiplusheaddir)" || exit $$?; \
	done

uninstall-gdiplusheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(gdiplushead_HEADERS)'; test -n "$(gdiplusheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(gdiplusheaddir)'; $(am__uninstall_files_from_dir)
install-glheadHEADERS: $(glhead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(glhead_HEADERS)'; test -n "$(glheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(glheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(glheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(glheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(glheaddir)" || exit $$?; \
	done

uninstall-glheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(glhead_HEADERS)'; test -n "$(glheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(glheaddir)'; $(am__uninstall_files_from_dir)
install-idlheadHEADERS: $(idlhead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(idlhead_HEADERS)'; test -n "$(idlheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(idlheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(idlheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(idlheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(idlheaddir)" || exit $$?; \
	done

uninstall-idlheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(idlhead_HEADERS)'; test -n "$(idlheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(idlheaddir)'; $(am__uninstall_files_from_dir)
install-khrheadHEADERS: $(khrhead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(khrhead_HEADERS)'; test -n "$(khrheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(khrheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(khrheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(khrheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(khrheaddir)" || exit $$?; \
	done

uninstall-khrheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(khrhead_HEADERS)'; test -n "$(khrheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(khrheaddir)'; $(am__uninstall_files_from_dir)
install-mingwhelperheadHEADERS: $(mingwhelperhead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(mingwhelperhead_HEADERS)'; test -n "$(mingwhelperheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(mingwhelperheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(mingwhelperheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(mingwhelperheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(mingwhelperheaddir)" || exit $$?; \
	done

uninstall-mingwhelperheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(mingwhelperhead_HEADERS)'; test -n "$(mingwhelperheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(mingwhelperheaddir)'; $(am__uninstall_files_from_dir)
install-nodist_sdksheadHEADERS: $(nodist_sdkshead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nodist_sdkshead_HEADERS)'; test -n "$(sdksheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(sdksheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(sdksheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(sdksheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(sdksheaddir)" || exit $$?; \
	done

uninstall-nodist_sdksheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_sdkshead_HEADERS)'; test -n "$(sdksheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(sdksheaddir)'; $(am__uninstall_files_from_dir)
install-secheadHEADERS: $(sechead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(sechead_HEADERS)'; test -n "$(secheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(secheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(secheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(secheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(secheaddir)" || exit $$?; \
	done

uninstall-secheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(sechead_HEADERS)'; test -n "$(secheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(secheaddir)'; $(am__uninstall_files_from_dir)
install-secsysheadHEADERS: $(secsyshead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(secsyshead_HEADERS)'; test -n "$(secsysheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(secsysheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(secsysheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(secsysheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(secsysheaddir)" || exit $$?; \
	done

uninstall-secsysheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(secsyshead_HEADERS)'; test -n "$(secsysheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(secsysheaddir)'; $(am__uninstall_files_from_dir)
install-sysheadHEADERS: $(syshead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(syshead_HEADERS)'; test -n "$(sysheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(sysheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(sysheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(sysheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(sysheaddir)" || exit $$?; \
	done

uninstall-sysheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(syshead_HEADERS)'; test -n "$(sysheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(sysheaddir)'; $(am__uninstall_files_from_dir)
install-wrlheadHEADERS: $(wrlhead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(wrlhead_HEADERS)'; test -n "$(wrlheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(wrlheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(wrlheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(wrlheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(wrlheaddir)" || exit $$?; \
	done

uninstall-wrlheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(wrlhead_HEADERS)'; test -n "$(wrlheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(wrlheaddir)'; $(am__uninstall_files_from_dir)
install-wrlwrappersheadHEADERS: $(wrlwrappershead_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(wrlwrappershead_HEADERS)'; test -n "$(wrlwrappersheaddir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(wrlwrappersheaddir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(wrlwrappersheaddir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(wrlwrappersheaddir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(wrlwrappersheaddir)" || exit $$?; \
	done

uninstall-wrlwrappersheadHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(wrlwrappershead_HEADERS)'; test -n "$(wrlwrappersheaddir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(wrlwrappersheaddir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	$(MAKE) $(AM_MAKEFLAGS) \
	  top_distdir="$(top_distdir)" distdir="$(distdir)" \
	  dist-hook
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-zstd: distdir
	tardir=$(distdir) && $(am__tar) | zstd -c $${ZSTD_CLEVEL-$${ZSTD_OPT--19}} >$(distdir).tar.zst
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	*.tar.zst*) \
	  zstd -dc $(distdir).tar.zst | $(am__untar) ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) $(AM_DISTCHECK_DVI_TARGET) \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(HEADERS) config.h
installdirs:
	for dir in "$(DESTDIR)$(baseheaddir)" "$(DESTDIR)$(ddkheaddir)" "$(DESTDIR)$(gdiplusheaddir)" "$(DESTDIR)$(glheaddir)" "$(DESTDIR)$(idlheaddir)" "$(DESTDIR)$(khrheaddir)" "$(DESTDIR)$(mingwhelperheaddir)" "$(DESTDIR)$(sdksheaddir)" "$(DESTDIR)$(secheaddir)" "$(DESTDIR)$(secsysheaddir)" "$(DESTDIR)$(sysheaddir)" "$(DESTDIR)$(wrlheaddir)" "$(DESTDIR)$(wrlwrappersheaddir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-am

clean-am: clean-generic mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -f Makefile
distclean-am: clean-am distclean-generic distclean-hdr distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-baseheadHEADERS install-ddkheadHEADERS \
	install-gdiplusheadHEADERS install-glheadHEADERS \
	install-idlheadHEADERS install-khrheadHEADERS \
	install-mingwhelperheadHEADERS install-nodist_sdksheadHEADERS \
	install-secheadHEADERS install-secsysheadHEADERS \
	install-sysheadHEADERS install-wrlheadHEADERS \
	install-wrlwrappersheadHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-baseheadHEADERS uninstall-ddkheadHEADERS \
	uninstall-gdiplusheadHEADERS uninstall-glheadHEADERS \
	uninstall-idlheadHEADERS uninstall-khrheadHEADERS \
	uninstall-mingwhelperheadHEADERS \
	uninstall-nodist_sdksheadHEADERS uninstall-secheadHEADERS \
	uninstall-secsysheadHEADERS uninstall-sysheadHEADERS \
	uninstall-wrlheadHEADERS uninstall-wrlwrappersheadHEADERS

.MAKE: all check install install-am install-exec install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--refresh check check-am clean \
	clean-cscope clean-generic cscope cscopelist-am ctags ctags-am \
	dist dist-all dist-bzip2 dist-gzip dist-hook dist-lzip \
	dist-shar dist-tarZ dist-xz dist-zip dist-zstd distcheck \
	distclean distclean-generic distclean-hdr distclean-tags \
	distcleancheck distdir distuninstallcheck dvi dvi-am html \
	html-am info info-am install install-am \
	install-baseheadHEADERS install-data install-data-am \
	install-ddkheadHEADERS install-dvi install-dvi-am install-exec \
	install-exec-am install-gdiplusheadHEADERS \
	install-glheadHEADERS install-html install-html-am \
	install-idlheadHEADERS install-info install-info-am \
	install-khrheadHEADERS install-man \
	install-mingwhelperheadHEADERS install-nodist_sdksheadHEADERS \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-secheadHEADERS install-secsysheadHEADERS install-strip \
	install-sysheadHEADERS install-wrlheadHEADERS \
	install-wrlwrappersheadHEADERS installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-generic pdf pdf-am ps ps-am tags \
	tags-am uninstall uninstall-am uninstall-baseheadHEADERS \
	uninstall-ddkheadHEADERS uninstall-gdiplusheadHEADERS \
	uninstall-glheadHEADERS uninstall-idlheadHEADERS \
	uninstall-khrheadHEADERS uninstall-mingwhelperheadHEADERS \
	uninstall-nodist_sdksheadHEADERS uninstall-secheadHEADERS \
	uninstall-secsysheadHEADERS uninstall-sysheadHEADERS \
	uninstall-wrlheadHEADERS uninstall-wrlwrappersheadHEADERS

.PRECIOUS: Makefile


dist-hook:
	find $(distdir) -name ".svn" -type d -delete

@HAVE_WIDL_TRUE@%.h: %.idl crt/_mingw.h
@HAVE_WIDL_TRUE@	$(WIDL) -DBOOL=WINBOOL -I$(srcdir)/include -Icrt -I$(srcdir)/crt -h -o $@ $<

@HAVE_WIDL_TRUE@%.tlb: %.idl
@HAVE_WIDL_TRUE@	$(WIDL) -I$(srcdir)/include -L$(srcdir)/tlb -t -o $@ $<

@HAVE_WIDL_TRUE@%_i.c: %.idl crt/_mingw.h
@HAVE_WIDL_TRUE@	$(WIDL) -DBOOL=WINBOOL -I$(srcdir)/include -Icrt -I$(srcdir)/crt -u -o $@ $<

# following headers are not meant to be generated from IDLs, so override default rule
@HAVE_WIDL_TRUE@include/prsht.h: ;
@HAVE_WIDL_TRUE@include/wincrypt.h: ;

_mingw_ddk.h: $(srcdir)/crt/sdks/_mingw_ddk.h.in
	$(SED) s/MINGW_HAS_DDK$$/@MINGW_HAS_DDK@/ $< > $@

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
