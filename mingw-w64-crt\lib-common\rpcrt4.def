LIBRARY "RPCRT4.dll"
EXPORTS
CreateProxyFromTypeInfo
CreateStubFromTypeInfo
I_RpcBindingInqCurrentModifiedId
I_RpcFixTransferSyntax
I_RpcFwThisIsTheManager
I_RpcInitFwImports
I_RpcInitHttpImports
I_RpcInitImports
I_RpcInitNdrImports
I_RpcMgmtQueryDedicatedThreadPool
I_RpcOpenClientProcess
I_RpcOpenClientThread
I_RpcServerTurnOnOffKeepalives
I_RpcVerifierCorruptionExpected
NdrFullPointerFree
NdrFullPointerInsertRefId
NdrFullPointerQueryPointer
NdrFullPointerQueryRefId
NdrGetBaseInterfaceFromStub
NdrpClientCall2
RpcCertMatchPrincipalName
pfnFreeRoutines DATA
pfnMarshallRoutines DATA
pfnSizeRoutines DATA
pfnUnmarshallRoutines DATA
CStdStubBuffer_AddRef
CStdStubBuffer_Connect
CStdStubBuffer_CountRefs
CStdStubBuffer_DebugServerQueryInterface
CStdStubBuffer_DebugServerRelease
CStdStubBuffer_Disconnect
CStdStubBuffer_Invoke
CStdStubBuffer_IsIIDSupported
CStdStubBuffer_QueryInterface
DceErrorInqTextA
DceErrorInqTextW
DllGetClassObject
DllInstall
DllRegisterServer
GlobalMutexClearExternal
GlobalMutexRequestExternal
IUnknown_AddRef_Proxy
IUnknown_QueryInterface_Proxy
IUnknown_Release_Proxy
I_RpcAbortAsyncCall
I_RpcAllocate
I_RpcAsyncAbortCall
I_RpcAsyncSetHandle
I_RpcBCacheAllocate
I_RpcBCacheFree
I_RpcBindingCopy
I_RpcBindingCreateNP
I_RpcBindingHandleToAsyncHandle
I_RpcBindingInqClientTokenAttributes
I_RpcBindingInqConnId
I_RpcBindingInqDynamicEndpoint
I_RpcBindingInqDynamicEndpointA
I_RpcBindingInqDynamicEndpointW
I_RpcBindingInqLocalClientPID
I_RpcBindingInqMarshalledTargetInfo
I_RpcBindingInqSecurityContext
I_RpcBindingInqSecurityContextKeyInfo
I_RpcBindingInqTransportType
I_RpcBindingInqWireIdForSnego
I_RpcBindingIsClientLocal
I_RpcBindingIsServerLocal
I_RpcBindingSetPrivateOption
I_RpcBindingToStaticStringBindingW
I_RpcCertProcessAndProvision
I_RpcClearMutex
I_RpcCompleteAndFree
I_RpcConnectionInqSockBuffSize
I_RpcConnectionSetSockBuffSize
I_RpcDeleteMutex
I_RpcEnableWmiTrace
I_RpcExceptionFilter
I_RpcFilterDCOMActivation
I_RpcFree
I_RpcFreeBuffer
I_RpcFreePipeBuffer
I_RpcFreeSystemHandle
I_RpcFreeSystemHandleCollection
I_RpcGetBuffer
I_RpcGetBufferWithObject
I_RpcGetCurrentCallHandle
I_RpcGetDefaultSD
I_RpcGetExtendedError
I_RpcGetPortAllocationData
I_RpcGetSystemHandle
I_RpcIfInqTransferSyntaxes
I_RpcLogEvent
I_RpcMapWin32Status
I_RpcMarshalBindingHandleAndInterfaceForNDF
I_RpcMgmtEnableDedicatedThreadPool
I_RpcNDRCGetWireRepresentation
I_RpcNDRSContextEmergencyCleanup
I_RpcNegotiateTransferSyntax
I_RpcNsBindingSetEntryName
I_RpcNsBindingSetEntryNameA
I_RpcNsBindingSetEntryNameW
I_RpcNsInterfaceExported
I_RpcNsInterfaceUnexported
I_RpcParseSecurity
I_RpcPauseExecution
I_RpcProxyNewConnection
I_RpcReallocPipeBuffer
I_RpcReceive
I_RpcRecordCalloutFailure
I_RpcReplyToClientWithStatus
I_RpcRequestMutex
I_RpcSNCHOption
I_RpcSend
I_RpcSendReceive
I_RpcServerAllocateIpPort
I_RpcServerCheckClientRestriction
I_RpcServerDisableExceptionFilter
I_RpcServerGetAssociationID
I_RpcServerInqAddressChangeFn
I_RpcServerInqLocalConnAddress
I_RpcServerInqRemoteConnAddress
I_RpcServerInqTransportType
I_RpcServerIsClientDisconnected
I_RpcServerRegisterForwardFunction
I_RpcServerSetAddressChangeFn
I_RpcServerStartService
I_RpcServerSubscribeForDisconnectNotification
I_RpcServerSubscribeForDisconnectNotification2
I_RpcServerUnsubscribeForDisconnectNotification
I_RpcServerUseProtseq2A
I_RpcServerUseProtseq2W
I_RpcServerUseProtseqEp2A
I_RpcServerUseProtseqEp2W
I_RpcSessionStrictContextHandle
I_RpcSetAsyncHandle
I_RpcSetDCOMAppId
I_RpcSetSystemHandle
I_RpcSsDontSerializeContext
I_RpcSystemFunction001
I_RpcSystemHandleTypeSpecificWork
I_RpcTransConnectionAllocatePacket
I_RpcTransConnectionFreePacket
I_RpcTransConnectionReallocPacket
I_RpcTransDatagramAllocate
I_RpcTransDatagramAllocate2
I_RpcTransDatagramFree
I_RpcTransGetThreadEvent
I_RpcTransGetThreadEventThreadOptional
I_RpcTransIoCancelled
I_RpcTransServerNewConnection
I_RpcTurnOnEEInfoPropagation
I_UuidCreate
MesBufferHandleReset
MesDecodeBufferHandleCreate
MesDecodeIncrementalHandleCreate
MesEncodeDynBufferHandleCreate
MesEncodeFixedBufferHandleCreate
MesEncodeIncrementalHandleCreate
MesHandleFree
MesIncrementalHandleReset
MesInqProcEncodingId
NDRCContextBinding
NDRCContextMarshall
NDRCContextUnmarshall
NDRSContextMarshall
NDRSContextMarshall2
NDRSContextMarshallEx
NDRSContextUnmarshall
NDRSContextUnmarshall2
NDRSContextUnmarshallEx
Ndr64AsyncClientCall
Ndr64AsyncServerCall64
Ndr64AsyncServerCallAll
Ndr64DcomAsyncClientCall
Ndr64DcomAsyncStubCall
NdrAllocate
NdrAsyncClientCall
NdrAsyncServerCall
NdrByteCountPointerBufferSize
NdrByteCountPointerFree
NdrByteCountPointerMarshall
NdrByteCountPointerUnmarshall
NdrCStdStubBuffer2_Release
NdrCStdStubBuffer_Release
NdrClearOutParameters
NdrClientCall2
NdrClientCall3
NdrClientContextMarshall
NdrClientContextUnmarshall
NdrClientInitialize
NdrClientInitializeNew
NdrComplexArrayBufferSize
NdrComplexArrayFree
NdrComplexArrayMarshall
NdrComplexArrayMemorySize
NdrComplexArrayUnmarshall
NdrComplexStructBufferSize
NdrComplexStructFree
NdrComplexStructMarshall
NdrComplexStructMemorySize
NdrComplexStructUnmarshall
NdrConformantArrayBufferSize
NdrConformantArrayFree
NdrConformantArrayMarshall
NdrConformantArrayMemorySize
NdrConformantArrayUnmarshall
NdrConformantStringBufferSize
NdrConformantStringMarshall
NdrConformantStringMemorySize
NdrConformantStringUnmarshall
NdrConformantStructBufferSize
NdrConformantStructFree
NdrConformantStructMarshall
NdrConformantStructMemorySize
NdrConformantStructUnmarshall
NdrConformantVaryingArrayBufferSize
NdrConformantVaryingArrayFree
NdrConformantVaryingArrayMarshall
NdrConformantVaryingArrayMemorySize
NdrConformantVaryingArrayUnmarshall
NdrConformantVaryingStructBufferSize
NdrConformantVaryingStructFree
NdrConformantVaryingStructMarshall
NdrConformantVaryingStructMemorySize
NdrConformantVaryingStructUnmarshall
NdrContextHandleInitialize
NdrContextHandleSize
NdrConvert
NdrConvert2
NdrCorrelationFree
NdrCorrelationInitialize
NdrCorrelationPass
NdrCreateServerInterfaceFromStub
NdrDcomAsyncClientCall
NdrDcomAsyncStubCall
NdrDllCanUnloadNow
NdrDllGetClassObject
NdrDllRegisterProxy
NdrDllUnregisterProxy
NdrEncapsulatedUnionBufferSize
NdrEncapsulatedUnionFree
NdrEncapsulatedUnionMarshall
NdrEncapsulatedUnionMemorySize
NdrEncapsulatedUnionUnmarshall
NdrFixedArrayBufferSize
NdrFixedArrayFree
NdrFixedArrayMarshall
NdrFixedArrayMemorySize
NdrFixedArrayUnmarshall
NdrFreeBuffer
NdrFullPointerXlatFree
NdrFullPointerXlatInit
NdrGetBuffer
NdrGetDcomProtocolVersion
NdrGetSimpleTypeBufferAlignment
NdrGetSimpleTypeBufferSize
NdrGetSimpleTypeMemorySize
NdrGetTypeFlags
NdrGetUserMarshalInfo
NdrInterfacePointerBufferSize
NdrInterfacePointerFree
NdrInterfacePointerMarshall
NdrInterfacePointerMemorySize
NdrInterfacePointerUnmarshall
NdrMapCommAndFaultStatus
NdrMesProcEncodeDecode
NdrMesProcEncodeDecode2
NdrMesProcEncodeDecode3
NdrMesSimpleTypeAlignSize
NdrMesSimpleTypeAlignSizeAll
NdrMesSimpleTypeDecode
NdrMesSimpleTypeDecodeAll
NdrMesSimpleTypeEncode
NdrMesSimpleTypeEncodeAll
NdrMesTypeAlignSize
NdrMesTypeAlignSize2
NdrMesTypeAlignSize3
NdrMesTypeDecode
NdrMesTypeDecode2
NdrMesTypeDecode3
NdrMesTypeEncode
NdrMesTypeEncode2
NdrMesTypeEncode3
NdrMesTypeFree2
NdrMesTypeFree3
NdrNonConformantStringBufferSize
NdrNonConformantStringMarshall
NdrNonConformantStringMemorySize
NdrNonConformantStringUnmarshall
NdrNonEncapsulatedUnionBufferSize
NdrNonEncapsulatedUnionFree
NdrNonEncapsulatedUnionMarshall
NdrNonEncapsulatedUnionMemorySize
NdrNonEncapsulatedUnionUnmarshall
NdrNsGetBuffer
NdrNsSendReceive
NdrOleAllocate
NdrOleFree
NdrOutInit
NdrPartialIgnoreClientBufferSize
NdrPartialIgnoreClientMarshall
NdrPartialIgnoreServerInitialize
NdrPartialIgnoreServerUnmarshall
NdrPointerBufferSize
NdrPointerFree
NdrPointerMarshall
NdrPointerMemorySize
NdrPointerUnmarshall
NdrProxyErrorHandler
NdrProxyFreeBuffer
NdrProxyGetBuffer
NdrProxyInitialize
NdrProxySendReceive
NdrRangeUnmarshall
NdrRpcSmClientAllocate
NdrRpcSmClientFree
NdrRpcSmSetClientToOsf
NdrRpcSsDefaultAllocate
NdrRpcSsDefaultFree
NdrRpcSsDisableAllocate
NdrRpcSsEnableAllocate
NdrSendReceive
NdrServerCall2
NdrServerCallAll
NdrServerCallNdr64
NdrServerContextMarshall
NdrServerContextNewMarshall
NdrServerContextNewUnmarshall
NdrServerContextUnmarshall
NdrServerInitialize
NdrServerInitializeMarshall
NdrServerInitializeNew
NdrServerInitializePartial
NdrServerInitializeUnmarshall
NdrSimpleStructBufferSize
NdrSimpleStructFree
NdrSimpleStructMarshall
NdrSimpleStructMemorySize
NdrSimpleStructUnmarshall
NdrSimpleTypeMarshall
NdrSimpleTypeUnmarshall
NdrStubCall2
NdrStubCall3
NdrStubForwardingFunction
NdrStubGetBuffer
NdrStubInitialize
NdrStubInitializeMarshall
NdrTypeFlags DATA
NdrTypeFree
NdrTypeMarshall
NdrTypeSize
NdrTypeUnmarshall
NdrUnmarshallBasetypeInline
NdrUserMarshalBufferSize
NdrUserMarshalFree
NdrUserMarshalMarshall
NdrUserMarshalMemorySize
NdrUserMarshalSimpleTypeConvert
NdrUserMarshalUnmarshall
NdrVaryingArrayBufferSize
NdrVaryingArrayFree
NdrVaryingArrayMarshall
NdrVaryingArrayMemorySize
NdrVaryingArrayUnmarshall
NdrXmitOrRepAsBufferSize
NdrXmitOrRepAsFree
NdrXmitOrRepAsMarshall
NdrXmitOrRepAsMemorySize
NdrXmitOrRepAsUnmarshall
NdrpCreateProxy
NdrpCreateStub
NdrpGetProcFormatString
NdrpGetTypeFormatString
NdrpGetTypeGenCookie
NdrpMemoryIncrement
NdrpReleaseTypeFormatString
NdrpReleaseTypeGenCookie
NdrpSetRpcSsDefaults
NdrpVarVtOfTypeDesc
RpcAbortAsyncCall
RpcAsyncAbortCall
RpcAsyncCancelCall
RpcAsyncCompleteCall
RpcAsyncGetCallStatus
RpcAsyncInitializeHandle
RpcAsyncRegisterInfo
RpcBindingBind
RpcBindingCopy
RpcBindingCreateA
RpcBindingCreateW
RpcBindingFree
RpcBindingFromStringBindingA
RpcBindingFromStringBindingW
RpcBindingInqAuthClientA
RpcBindingInqAuthClientExA
RpcBindingInqAuthClientExW
RpcBindingInqAuthClientW
RpcBindingInqAuthInfoA
RpcBindingInqAuthInfoExA
RpcBindingInqAuthInfoExW
RpcBindingInqAuthInfoW
RpcBindingInqObject
RpcBindingInqOption
RpcBindingReset
RpcBindingServerFromClient
RpcBindingSetAuthInfoA
RpcBindingSetAuthInfoExA
RpcBindingSetAuthInfoExW
RpcBindingSetAuthInfoW
RpcBindingSetObject
RpcBindingSetOption
RpcBindingToStringBindingA
RpcBindingToStringBindingW
RpcBindingUnbind
RpcBindingVectorFree
RpcCancelAsyncCall
RpcCancelThread
RpcCancelThreadEx
RpcCertGeneratePrincipalNameA
RpcCertGeneratePrincipalNameW
RpcCompleteAsyncCall
RpcEpRegisterA
RpcEpRegisterNoReplaceA
RpcEpRegisterNoReplaceW
RpcEpRegisterW
RpcEpResolveBinding
RpcEpUnregister
RpcErrorAddRecord
RpcErrorClearInformation
RpcErrorEndEnumeration
RpcErrorGetNextRecord
RpcErrorGetNumberOfRecords
RpcErrorLoadErrorInfo
RpcErrorResetEnumeration
RpcErrorSaveErrorInfo
RpcErrorStartEnumeration
RpcExceptionFilter
RpcFreeAuthorizationContext
RpcGetAsyncCallStatus
RpcGetAuthorizationContextForClient
RpcIfIdVectorFree
RpcIfInqId
RpcImpersonateClient
RpcImpersonateClient2
RpcImpersonateClientContainer
RpcInitializeAsyncHandle
RpcMgmtEnableIdleCleanup
RpcMgmtEpEltInqBegin
RpcMgmtEpEltInqDone
RpcMgmtEpEltInqNextA
RpcMgmtEpEltInqNextW
RpcMgmtEpUnregister
RpcMgmtInqComTimeout
RpcMgmtInqDefaultProtectLevel
RpcMgmtInqIfIds
RpcMgmtInqServerPrincNameA
RpcMgmtInqServerPrincNameW
RpcMgmtInqStats
RpcMgmtIsServerListening
RpcMgmtSetAuthorizationFn
RpcMgmtSetCancelTimeout
RpcMgmtSetComTimeout
RpcMgmtSetServerStackSize
RpcMgmtStatsVectorFree
RpcMgmtStopServerListening
RpcMgmtWaitServerListen
RpcNetworkInqProtseqsA
RpcNetworkInqProtseqsW
RpcNetworkIsProtseqValidA
RpcNetworkIsProtseqValidW
RpcNsBindingInqEntryNameA
RpcNsBindingInqEntryNameW
RpcObjectInqType
RpcObjectSetInqFn
RpcObjectSetType
RpcProtseqVectorFreeA
RpcProtseqVectorFreeW
RpcRaiseException
RpcRegisterAsyncInfo
RpcRevertContainerImpersonation
RpcRevertToSelf
RpcRevertToSelfEx
RpcServerCompleteSecurityCallback
RpcServerInqBindingHandle
RpcServerInqBindings
RpcServerInqBindingsEx
RpcServerInqCallAttributesA
RpcServerInqCallAttributesW
RpcServerInqDefaultPrincNameA
RpcServerInqDefaultPrincNameW
RpcServerInqIf
RpcServerInterfaceGroupActivate
RpcServerInterfaceGroupClose
RpcServerInterfaceGroupCreateA
RpcServerInterfaceGroupCreateW
RpcServerInterfaceGroupDeactivate
RpcServerInterfaceGroupInqBindings
RpcServerListen
RpcServerRegisterAuthInfoA
RpcServerRegisterAuthInfoW
RpcServerRegisterIf
RpcServerRegisterIf2
RpcServerRegisterIf3
RpcServerRegisterIfEx
RpcServerSubscribeForNotification
RpcServerTestCancel
RpcServerUnregisterIf
RpcServerUnregisterIfEx
RpcServerUnsubscribeForNotification
RpcServerUseAllProtseqs
RpcServerUseAllProtseqsEx
RpcServerUseAllProtseqsIf
RpcServerUseAllProtseqsIfEx
RpcServerUseProtseqA
RpcServerUseProtseqEpA
RpcServerUseProtseqEpExA
RpcServerUseProtseqEpExW
RpcServerUseProtseqEpW
RpcServerUseProtseqExA
RpcServerUseProtseqExW
RpcServerUseProtseqIfA
RpcServerUseProtseqIfExA
RpcServerUseProtseqIfExW
RpcServerUseProtseqIfW
RpcServerUseProtseqW
RpcServerYield
RpcSmAllocate
RpcSmClientFree
RpcSmDestroyClientContext
RpcSmDisableAllocate
RpcSmEnableAllocate
RpcSmFree
RpcSmGetThreadHandle
RpcSmSetClientAllocFree
RpcSmSetThreadHandle
RpcSmSwapClientAllocFree
RpcSsAllocate
RpcSsContextLockExclusive
RpcSsContextLockShared
RpcSsDestroyClientContext
RpcSsDisableAllocate
RpcSsDontSerializeContext
RpcSsEnableAllocate
RpcSsFree
RpcSsGetContextBinding
RpcSsGetThreadHandle
RpcSsSetClientAllocFree
RpcSsSetThreadHandle
RpcSsSwapClientAllocFree
RpcStringBindingComposeA
RpcStringBindingComposeW
RpcStringBindingParseA
RpcStringBindingParseW
RpcStringFreeA
RpcStringFreeW
RpcTestCancel
RpcUserFree
SimpleTypeAlignment DATA
SimpleTypeBufferSize DATA
SimpleTypeMemorySize DATA
TowerConstruct
TowerExplode
UuidCompare
UuidCreate
UuidCreateNil
UuidCreateSequential
UuidEqual
UuidFromStringA
UuidFromStringW
UuidHash
UuidIsNil
UuidToStringA
UuidToStringW
