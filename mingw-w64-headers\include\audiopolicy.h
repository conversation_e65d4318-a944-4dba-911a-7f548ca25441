/*** Autogenerated by WIDL 8.5 from include/audiopolicy.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __audiopolicy_h__
#define __audiopolicy_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __IAudioSessionEvents_FWD_DEFINED__
#define __IAudioSessionEvents_FWD_DEFINED__
typedef interface IAudioSessionEvents IAudioSessionEvents;
#ifdef __cplusplus
interface IAudioSessionEvents;
#endif /* __cplusplus */
#endif

#ifndef __IAudioSessionControl_FWD_DEFINED__
#define __IAudioSessionControl_FWD_DEFINED__
typedef interface IAudioSessionControl IAudioSessionControl;
#ifdef __cplusplus
interface IAudioSessionControl;
#endif /* __cplusplus */
#endif

#ifndef __IAudioSessionControl2_FWD_DEFINED__
#define __IAudioSessionControl2_FWD_DEFINED__
typedef interface IAudioSessionControl2 IAudioSessionControl2;
#ifdef __cplusplus
interface IAudioSessionControl2;
#endif /* __cplusplus */
#endif

#ifndef __IAudioSessionManager_FWD_DEFINED__
#define __IAudioSessionManager_FWD_DEFINED__
typedef interface IAudioSessionManager IAudioSessionManager;
#ifdef __cplusplus
interface IAudioSessionManager;
#endif /* __cplusplus */
#endif

#ifndef __IAudioVolumeDuckNotification_FWD_DEFINED__
#define __IAudioVolumeDuckNotification_FWD_DEFINED__
typedef interface IAudioVolumeDuckNotification IAudioVolumeDuckNotification;
#ifdef __cplusplus
interface IAudioVolumeDuckNotification;
#endif /* __cplusplus */
#endif

#ifndef __IAudioSessionNotification_FWD_DEFINED__
#define __IAudioSessionNotification_FWD_DEFINED__
typedef interface IAudioSessionNotification IAudioSessionNotification;
#ifdef __cplusplus
interface IAudioSessionNotification;
#endif /* __cplusplus */
#endif

#ifndef __IAudioSessionEnumerator_FWD_DEFINED__
#define __IAudioSessionEnumerator_FWD_DEFINED__
typedef interface IAudioSessionEnumerator IAudioSessionEnumerator;
#ifdef __cplusplus
interface IAudioSessionEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IAudioSessionManager2_FWD_DEFINED__
#define __IAudioSessionManager2_FWD_DEFINED__
typedef interface IAudioSessionManager2 IAudioSessionManager2;
#ifdef __cplusplus
interface IAudioSessionManager2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <propidl.h>
#include <audiosessiontypes.h>
#include <audioclient.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>


#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum AudioSessionDisconnectReason {
    DisconnectReasonDeviceRemoval = 0,
    DisconnectReasonServerShutdown = 1,
    DisconnectReasonFormatChanged = 2,
    DisconnectReasonSessionLogoff = 3,
    DisconnectReasonSessionDisconnected = 4,
    DisconnectReasonExclusiveModeOverride = 5
} AudioSessionDisconnectReason;

/*****************************************************************************
 * IAudioSessionEvents interface
 */
#ifndef __IAudioSessionEvents_INTERFACE_DEFINED__
#define __IAudioSessionEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionEvents, 0x24918acc, 0x64b3, 0x37c1, 0x8c,0xa9, 0x74,0xa6,0x6e,0x99,0x57,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("24918acc-64b3-37c1-8ca9-74a66e9957a8")
IAudioSessionEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnDisplayNameChanged(
        LPCWSTR NewDisplayName,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnIconPathChanged(
        LPCWSTR NewIconPath,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnSimpleVolumeChanged(
        float NewVolume,
        WINBOOL NewMute,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnChannelVolumeChanged(
        DWORD ChannelCount,
        float NewChannelVolumeArray[],
        DWORD ChangedChannel,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnGroupingParamChanged(
        LPCGUID NewGroupingParam,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStateChanged(
        AudioSessionState NewState) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnSessionDisconnected(
        AudioSessionDisconnectReason DisconnectReason) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionEvents, 0x24918acc, 0x64b3, 0x37c1, 0x8c,0xa9, 0x74,0xa6,0x6e,0x99,0x57,0xa8)
#endif
#else
typedef struct IAudioSessionEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionEvents *This);

    /*** IAudioSessionEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *OnDisplayNameChanged)(
        IAudioSessionEvents *This,
        LPCWSTR NewDisplayName,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *OnIconPathChanged)(
        IAudioSessionEvents *This,
        LPCWSTR NewIconPath,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *OnSimpleVolumeChanged)(
        IAudioSessionEvents *This,
        float NewVolume,
        WINBOOL NewMute,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *OnChannelVolumeChanged)(
        IAudioSessionEvents *This,
        DWORD ChannelCount,
        float NewChannelVolumeArray[],
        DWORD ChangedChannel,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *OnGroupingParamChanged)(
        IAudioSessionEvents *This,
        LPCGUID NewGroupingParam,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *OnStateChanged)(
        IAudioSessionEvents *This,
        AudioSessionState NewState);

    HRESULT (STDMETHODCALLTYPE *OnSessionDisconnected)(
        IAudioSessionEvents *This,
        AudioSessionDisconnectReason DisconnectReason);

    END_INTERFACE
} IAudioSessionEventsVtbl;

interface IAudioSessionEvents {
    CONST_VTBL IAudioSessionEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionEvents methods ***/
#define IAudioSessionEvents_OnDisplayNameChanged(This,NewDisplayName,EventContext) (This)->lpVtbl->OnDisplayNameChanged(This,NewDisplayName,EventContext)
#define IAudioSessionEvents_OnIconPathChanged(This,NewIconPath,EventContext) (This)->lpVtbl->OnIconPathChanged(This,NewIconPath,EventContext)
#define IAudioSessionEvents_OnSimpleVolumeChanged(This,NewVolume,NewMute,EventContext) (This)->lpVtbl->OnSimpleVolumeChanged(This,NewVolume,NewMute,EventContext)
#define IAudioSessionEvents_OnChannelVolumeChanged(This,ChannelCount,NewChannelVolumeArray,ChangedChannel,EventContext) (This)->lpVtbl->OnChannelVolumeChanged(This,ChannelCount,NewChannelVolumeArray,ChangedChannel,EventContext)
#define IAudioSessionEvents_OnGroupingParamChanged(This,NewGroupingParam,EventContext) (This)->lpVtbl->OnGroupingParamChanged(This,NewGroupingParam,EventContext)
#define IAudioSessionEvents_OnStateChanged(This,NewState) (This)->lpVtbl->OnStateChanged(This,NewState)
#define IAudioSessionEvents_OnSessionDisconnected(This,DisconnectReason) (This)->lpVtbl->OnSessionDisconnected(This,DisconnectReason)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionEvents_QueryInterface(IAudioSessionEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionEvents_AddRef(IAudioSessionEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionEvents_Release(IAudioSessionEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionEvents methods ***/
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnDisplayNameChanged(IAudioSessionEvents* This,LPCWSTR NewDisplayName,LPCGUID EventContext) {
    return This->lpVtbl->OnDisplayNameChanged(This,NewDisplayName,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnIconPathChanged(IAudioSessionEvents* This,LPCWSTR NewIconPath,LPCGUID EventContext) {
    return This->lpVtbl->OnIconPathChanged(This,NewIconPath,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnSimpleVolumeChanged(IAudioSessionEvents* This,float NewVolume,WINBOOL NewMute,LPCGUID EventContext) {
    return This->lpVtbl->OnSimpleVolumeChanged(This,NewVolume,NewMute,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnChannelVolumeChanged(IAudioSessionEvents* This,DWORD ChannelCount,float NewChannelVolumeArray[],DWORD ChangedChannel,LPCGUID EventContext) {
    return This->lpVtbl->OnChannelVolumeChanged(This,ChannelCount,NewChannelVolumeArray,ChangedChannel,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnGroupingParamChanged(IAudioSessionEvents* This,LPCGUID NewGroupingParam,LPCGUID EventContext) {
    return This->lpVtbl->OnGroupingParamChanged(This,NewGroupingParam,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnStateChanged(IAudioSessionEvents* This,AudioSessionState NewState) {
    return This->lpVtbl->OnStateChanged(This,NewState);
}
static __WIDL_INLINE HRESULT IAudioSessionEvents_OnSessionDisconnected(IAudioSessionEvents* This,AudioSessionDisconnectReason DisconnectReason) {
    return This->lpVtbl->OnSessionDisconnected(This,DisconnectReason);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionEvents_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioSessionControl interface
 */
#ifndef __IAudioSessionControl_INTERFACE_DEFINED__
#define __IAudioSessionControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionControl, 0xf4b1a599, 0x7266, 0x4319, 0xa8,0xca, 0xe7,0x0a,0xcb,0x11,0xe8,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f4b1a599-7266-4319-a8ca-e70acb11e8cd")
IAudioSessionControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetState(
        AudioSessionState *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayName(
        LPWSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDisplayName(
        LPCWSTR Value,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIconPath(
        LPWSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIconPath(
        LPCWSTR Value,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroupingParam(
        GUID *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGroupingParam(
        LPCGUID Override,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterAudioSessionNotification(
        IAudioSessionEvents *NewNotifications) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterAudioSessionNotification(
        IAudioSessionEvents *NewNotifications) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionControl, 0xf4b1a599, 0x7266, 0x4319, 0xa8,0xca, 0xe7,0x0a,0xcb,0x11,0xe8,0xcd)
#endif
#else
typedef struct IAudioSessionControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionControl *This);

    /*** IAudioSessionControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetState)(
        IAudioSessionControl *This,
        AudioSessionState *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IAudioSessionControl *This,
        LPWSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetDisplayName)(
        IAudioSessionControl *This,
        LPCWSTR Value,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetIconPath)(
        IAudioSessionControl *This,
        LPWSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetIconPath)(
        IAudioSessionControl *This,
        LPCWSTR Value,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetGroupingParam)(
        IAudioSessionControl *This,
        GUID *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetGroupingParam)(
        IAudioSessionControl *This,
        LPCGUID Override,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *RegisterAudioSessionNotification)(
        IAudioSessionControl *This,
        IAudioSessionEvents *NewNotifications);

    HRESULT (STDMETHODCALLTYPE *UnregisterAudioSessionNotification)(
        IAudioSessionControl *This,
        IAudioSessionEvents *NewNotifications);

    END_INTERFACE
} IAudioSessionControlVtbl;

interface IAudioSessionControl {
    CONST_VTBL IAudioSessionControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionControl methods ***/
#define IAudioSessionControl_GetState(This,pRetVal) (This)->lpVtbl->GetState(This,pRetVal)
#define IAudioSessionControl_GetDisplayName(This,pRetVal) (This)->lpVtbl->GetDisplayName(This,pRetVal)
#define IAudioSessionControl_SetDisplayName(This,Value,EventContext) (This)->lpVtbl->SetDisplayName(This,Value,EventContext)
#define IAudioSessionControl_GetIconPath(This,pRetVal) (This)->lpVtbl->GetIconPath(This,pRetVal)
#define IAudioSessionControl_SetIconPath(This,Value,EventContext) (This)->lpVtbl->SetIconPath(This,Value,EventContext)
#define IAudioSessionControl_GetGroupingParam(This,pRetVal) (This)->lpVtbl->GetGroupingParam(This,pRetVal)
#define IAudioSessionControl_SetGroupingParam(This,Override,EventContext) (This)->lpVtbl->SetGroupingParam(This,Override,EventContext)
#define IAudioSessionControl_RegisterAudioSessionNotification(This,NewNotifications) (This)->lpVtbl->RegisterAudioSessionNotification(This,NewNotifications)
#define IAudioSessionControl_UnregisterAudioSessionNotification(This,NewNotifications) (This)->lpVtbl->UnregisterAudioSessionNotification(This,NewNotifications)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionControl_QueryInterface(IAudioSessionControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionControl_AddRef(IAudioSessionControl* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionControl_Release(IAudioSessionControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionControl methods ***/
static __WIDL_INLINE HRESULT IAudioSessionControl_GetState(IAudioSessionControl* This,AudioSessionState *pRetVal) {
    return This->lpVtbl->GetState(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_GetDisplayName(IAudioSessionControl* This,LPWSTR *pRetVal) {
    return This->lpVtbl->GetDisplayName(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_SetDisplayName(IAudioSessionControl* This,LPCWSTR Value,LPCGUID EventContext) {
    return This->lpVtbl->SetDisplayName(This,Value,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_GetIconPath(IAudioSessionControl* This,LPWSTR *pRetVal) {
    return This->lpVtbl->GetIconPath(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_SetIconPath(IAudioSessionControl* This,LPCWSTR Value,LPCGUID EventContext) {
    return This->lpVtbl->SetIconPath(This,Value,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_GetGroupingParam(IAudioSessionControl* This,GUID *pRetVal) {
    return This->lpVtbl->GetGroupingParam(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_SetGroupingParam(IAudioSessionControl* This,LPCGUID Override,LPCGUID EventContext) {
    return This->lpVtbl->SetGroupingParam(This,Override,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_RegisterAudioSessionNotification(IAudioSessionControl* This,IAudioSessionEvents *NewNotifications) {
    return This->lpVtbl->RegisterAudioSessionNotification(This,NewNotifications);
}
static __WIDL_INLINE HRESULT IAudioSessionControl_UnregisterAudioSessionNotification(IAudioSessionControl* This,IAudioSessionEvents *NewNotifications) {
    return This->lpVtbl->UnregisterAudioSessionNotification(This,NewNotifications);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionControl_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IAudioSessionControl2 interface
 */
#ifndef __IAudioSessionControl2_INTERFACE_DEFINED__
#define __IAudioSessionControl2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionControl2, 0xbfb7ff88, 0x7239, 0x4fc9, 0x8f,0xa2, 0x07,0xc9,0x50,0xbe,0x9c,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bfb7ff88-7239-4fc9-8fa2-07c950be9c6d")
IAudioSessionControl2 : public IAudioSessionControl
{
    virtual HRESULT STDMETHODCALLTYPE GetSessionIdentifier(
        LPWSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionInstanceIdentifier(
        LPWSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProcessId(
        DWORD *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSystemSoundsSession(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDuckingPreference(
        WINBOOL optOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionControl2, 0xbfb7ff88, 0x7239, 0x4fc9, 0x8f,0xa2, 0x07,0xc9,0x50,0xbe,0x9c,0x6d)
#endif
#else
typedef struct IAudioSessionControl2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionControl2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionControl2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionControl2 *This);

    /*** IAudioSessionControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetState)(
        IAudioSessionControl2 *This,
        AudioSessionState *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IAudioSessionControl2 *This,
        LPWSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetDisplayName)(
        IAudioSessionControl2 *This,
        LPCWSTR Value,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetIconPath)(
        IAudioSessionControl2 *This,
        LPWSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetIconPath)(
        IAudioSessionControl2 *This,
        LPCWSTR Value,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetGroupingParam)(
        IAudioSessionControl2 *This,
        GUID *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetGroupingParam)(
        IAudioSessionControl2 *This,
        LPCGUID Override,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *RegisterAudioSessionNotification)(
        IAudioSessionControl2 *This,
        IAudioSessionEvents *NewNotifications);

    HRESULT (STDMETHODCALLTYPE *UnregisterAudioSessionNotification)(
        IAudioSessionControl2 *This,
        IAudioSessionEvents *NewNotifications);

    /*** IAudioSessionControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSessionIdentifier)(
        IAudioSessionControl2 *This,
        LPWSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetSessionInstanceIdentifier)(
        IAudioSessionControl2 *This,
        LPWSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetProcessId)(
        IAudioSessionControl2 *This,
        DWORD *pRetVal);

    HRESULT (STDMETHODCALLTYPE *IsSystemSoundsSession)(
        IAudioSessionControl2 *This);

    HRESULT (STDMETHODCALLTYPE *SetDuckingPreference)(
        IAudioSessionControl2 *This,
        WINBOOL optOut);

    END_INTERFACE
} IAudioSessionControl2Vtbl;

interface IAudioSessionControl2 {
    CONST_VTBL IAudioSessionControl2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionControl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionControl2_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionControl methods ***/
#define IAudioSessionControl2_GetState(This,pRetVal) (This)->lpVtbl->GetState(This,pRetVal)
#define IAudioSessionControl2_GetDisplayName(This,pRetVal) (This)->lpVtbl->GetDisplayName(This,pRetVal)
#define IAudioSessionControl2_SetDisplayName(This,Value,EventContext) (This)->lpVtbl->SetDisplayName(This,Value,EventContext)
#define IAudioSessionControl2_GetIconPath(This,pRetVal) (This)->lpVtbl->GetIconPath(This,pRetVal)
#define IAudioSessionControl2_SetIconPath(This,Value,EventContext) (This)->lpVtbl->SetIconPath(This,Value,EventContext)
#define IAudioSessionControl2_GetGroupingParam(This,pRetVal) (This)->lpVtbl->GetGroupingParam(This,pRetVal)
#define IAudioSessionControl2_SetGroupingParam(This,Override,EventContext) (This)->lpVtbl->SetGroupingParam(This,Override,EventContext)
#define IAudioSessionControl2_RegisterAudioSessionNotification(This,NewNotifications) (This)->lpVtbl->RegisterAudioSessionNotification(This,NewNotifications)
#define IAudioSessionControl2_UnregisterAudioSessionNotification(This,NewNotifications) (This)->lpVtbl->UnregisterAudioSessionNotification(This,NewNotifications)
/*** IAudioSessionControl2 methods ***/
#define IAudioSessionControl2_GetSessionIdentifier(This,pRetVal) (This)->lpVtbl->GetSessionIdentifier(This,pRetVal)
#define IAudioSessionControl2_GetSessionInstanceIdentifier(This,pRetVal) (This)->lpVtbl->GetSessionInstanceIdentifier(This,pRetVal)
#define IAudioSessionControl2_GetProcessId(This,pRetVal) (This)->lpVtbl->GetProcessId(This,pRetVal)
#define IAudioSessionControl2_IsSystemSoundsSession(This) (This)->lpVtbl->IsSystemSoundsSession(This)
#define IAudioSessionControl2_SetDuckingPreference(This,optOut) (This)->lpVtbl->SetDuckingPreference(This,optOut)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionControl2_QueryInterface(IAudioSessionControl2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionControl2_AddRef(IAudioSessionControl2* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionControl2_Release(IAudioSessionControl2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionControl methods ***/
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetState(IAudioSessionControl2* This,AudioSessionState *pRetVal) {
    return This->lpVtbl->GetState(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetDisplayName(IAudioSessionControl2* This,LPWSTR *pRetVal) {
    return This->lpVtbl->GetDisplayName(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_SetDisplayName(IAudioSessionControl2* This,LPCWSTR Value,LPCGUID EventContext) {
    return This->lpVtbl->SetDisplayName(This,Value,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetIconPath(IAudioSessionControl2* This,LPWSTR *pRetVal) {
    return This->lpVtbl->GetIconPath(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_SetIconPath(IAudioSessionControl2* This,LPCWSTR Value,LPCGUID EventContext) {
    return This->lpVtbl->SetIconPath(This,Value,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetGroupingParam(IAudioSessionControl2* This,GUID *pRetVal) {
    return This->lpVtbl->GetGroupingParam(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_SetGroupingParam(IAudioSessionControl2* This,LPCGUID Override,LPCGUID EventContext) {
    return This->lpVtbl->SetGroupingParam(This,Override,EventContext);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_RegisterAudioSessionNotification(IAudioSessionControl2* This,IAudioSessionEvents *NewNotifications) {
    return This->lpVtbl->RegisterAudioSessionNotification(This,NewNotifications);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_UnregisterAudioSessionNotification(IAudioSessionControl2* This,IAudioSessionEvents *NewNotifications) {
    return This->lpVtbl->UnregisterAudioSessionNotification(This,NewNotifications);
}
/*** IAudioSessionControl2 methods ***/
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetSessionIdentifier(IAudioSessionControl2* This,LPWSTR *pRetVal) {
    return This->lpVtbl->GetSessionIdentifier(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetSessionInstanceIdentifier(IAudioSessionControl2* This,LPWSTR *pRetVal) {
    return This->lpVtbl->GetSessionInstanceIdentifier(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_GetProcessId(IAudioSessionControl2* This,DWORD *pRetVal) {
    return This->lpVtbl->GetProcessId(This,pRetVal);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_IsSystemSoundsSession(IAudioSessionControl2* This) {
    return This->lpVtbl->IsSystemSoundsSession(This);
}
static __WIDL_INLINE HRESULT IAudioSessionControl2_SetDuckingPreference(IAudioSessionControl2* This,WINBOOL optOut) {
    return This->lpVtbl->SetDuckingPreference(This,optOut);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionControl2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioSessionManager interface
 */
#ifndef __IAudioSessionManager_INTERFACE_DEFINED__
#define __IAudioSessionManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionManager, 0xbfa971f1, 0x4d5e, 0x40bb, 0x93,0x5e, 0x96,0x70,0x39,0xbf,0xbe,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bfa971f1-4d5e-40bb-935e-967039bfbee4")
IAudioSessionManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetAudioSessionControl(
        LPCGUID AudioSessionGuid,
        DWORD StreamFlags,
        IAudioSessionControl **SessionControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSimpleAudioVolume(
        LPCGUID AudioSessionGuid,
        DWORD StreamFlags,
        ISimpleAudioVolume **AudioVolume) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionManager, 0xbfa971f1, 0x4d5e, 0x40bb, 0x93,0x5e, 0x96,0x70,0x39,0xbf,0xbe,0xe4)
#endif
#else
typedef struct IAudioSessionManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionManager *This);

    /*** IAudioSessionManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAudioSessionControl)(
        IAudioSessionManager *This,
        LPCGUID AudioSessionGuid,
        DWORD StreamFlags,
        IAudioSessionControl **SessionControl);

    HRESULT (STDMETHODCALLTYPE *GetSimpleAudioVolume)(
        IAudioSessionManager *This,
        LPCGUID AudioSessionGuid,
        DWORD StreamFlags,
        ISimpleAudioVolume **AudioVolume);

    END_INTERFACE
} IAudioSessionManagerVtbl;

interface IAudioSessionManager {
    CONST_VTBL IAudioSessionManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionManager_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionManager methods ***/
#define IAudioSessionManager_GetAudioSessionControl(This,AudioSessionGuid,StreamFlags,SessionControl) (This)->lpVtbl->GetAudioSessionControl(This,AudioSessionGuid,StreamFlags,SessionControl)
#define IAudioSessionManager_GetSimpleAudioVolume(This,AudioSessionGuid,StreamFlags,AudioVolume) (This)->lpVtbl->GetSimpleAudioVolume(This,AudioSessionGuid,StreamFlags,AudioVolume)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionManager_QueryInterface(IAudioSessionManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionManager_AddRef(IAudioSessionManager* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionManager_Release(IAudioSessionManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionManager methods ***/
static __WIDL_INLINE HRESULT IAudioSessionManager_GetAudioSessionControl(IAudioSessionManager* This,LPCGUID AudioSessionGuid,DWORD StreamFlags,IAudioSessionControl **SessionControl) {
    return This->lpVtbl->GetAudioSessionControl(This,AudioSessionGuid,StreamFlags,SessionControl);
}
static __WIDL_INLINE HRESULT IAudioSessionManager_GetSimpleAudioVolume(IAudioSessionManager* This,LPCGUID AudioSessionGuid,DWORD StreamFlags,ISimpleAudioVolume **AudioVolume) {
    return This->lpVtbl->GetSimpleAudioVolume(This,AudioSessionGuid,StreamFlags,AudioVolume);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioVolumeDuckNotification interface
 */
#ifndef __IAudioVolumeDuckNotification_INTERFACE_DEFINED__
#define __IAudioVolumeDuckNotification_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioVolumeDuckNotification, 0xc3b284d4, 0x6d39, 0x4359, 0xb3,0xcf, 0xb5,0x6d,0xdb,0x3b,0xb3,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c3b284d4-6d39-4359-b3cf-b56ddb3bb39c")
IAudioVolumeDuckNotification : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnVolumeDuckNotification(
        LPCWSTR sessionID,
        UINT32 countCommunicationSessions) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnVolumeUnduckNotification(
        LPCWSTR sessionID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioVolumeDuckNotification, 0xc3b284d4, 0x6d39, 0x4359, 0xb3,0xcf, 0xb5,0x6d,0xdb,0x3b,0xb3,0x9c)
#endif
#else
typedef struct IAudioVolumeDuckNotificationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioVolumeDuckNotification *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioVolumeDuckNotification *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioVolumeDuckNotification *This);

    /*** IAudioVolumeDuckNotification methods ***/
    HRESULT (STDMETHODCALLTYPE *OnVolumeDuckNotification)(
        IAudioVolumeDuckNotification *This,
        LPCWSTR sessionID,
        UINT32 countCommunicationSessions);

    HRESULT (STDMETHODCALLTYPE *OnVolumeUnduckNotification)(
        IAudioVolumeDuckNotification *This,
        LPCWSTR sessionID);

    END_INTERFACE
} IAudioVolumeDuckNotificationVtbl;

interface IAudioVolumeDuckNotification {
    CONST_VTBL IAudioVolumeDuckNotificationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioVolumeDuckNotification_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioVolumeDuckNotification_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioVolumeDuckNotification_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioVolumeDuckNotification methods ***/
#define IAudioVolumeDuckNotification_OnVolumeDuckNotification(This,sessionID,countCommunicationSessions) (This)->lpVtbl->OnVolumeDuckNotification(This,sessionID,countCommunicationSessions)
#define IAudioVolumeDuckNotification_OnVolumeUnduckNotification(This,sessionID) (This)->lpVtbl->OnVolumeUnduckNotification(This,sessionID)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioVolumeDuckNotification_QueryInterface(IAudioVolumeDuckNotification* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioVolumeDuckNotification_AddRef(IAudioVolumeDuckNotification* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioVolumeDuckNotification_Release(IAudioVolumeDuckNotification* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioVolumeDuckNotification methods ***/
static __WIDL_INLINE HRESULT IAudioVolumeDuckNotification_OnVolumeDuckNotification(IAudioVolumeDuckNotification* This,LPCWSTR sessionID,UINT32 countCommunicationSessions) {
    return This->lpVtbl->OnVolumeDuckNotification(This,sessionID,countCommunicationSessions);
}
static __WIDL_INLINE HRESULT IAudioVolumeDuckNotification_OnVolumeUnduckNotification(IAudioVolumeDuckNotification* This,LPCWSTR sessionID) {
    return This->lpVtbl->OnVolumeUnduckNotification(This,sessionID);
}
#endif
#endif

#endif


#endif  /* __IAudioVolumeDuckNotification_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioSessionNotification interface
 */
#ifndef __IAudioSessionNotification_INTERFACE_DEFINED__
#define __IAudioSessionNotification_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionNotification, 0x641dd20b, 0x4d41, 0x49cc, 0xab,0xa3, 0x17,0x4b,0x94,0x77,0xbb,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("641dd20b-4d41-49cc-aba3-174b9477bb08")
IAudioSessionNotification : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnSessionCreated(
        IAudioSessionControl *NewSession) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionNotification, 0x641dd20b, 0x4d41, 0x49cc, 0xab,0xa3, 0x17,0x4b,0x94,0x77,0xbb,0x08)
#endif
#else
typedef struct IAudioSessionNotificationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionNotification *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionNotification *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionNotification *This);

    /*** IAudioSessionNotification methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSessionCreated)(
        IAudioSessionNotification *This,
        IAudioSessionControl *NewSession);

    END_INTERFACE
} IAudioSessionNotificationVtbl;

interface IAudioSessionNotification {
    CONST_VTBL IAudioSessionNotificationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionNotification_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionNotification_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionNotification_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionNotification methods ***/
#define IAudioSessionNotification_OnSessionCreated(This,NewSession) (This)->lpVtbl->OnSessionCreated(This,NewSession)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionNotification_QueryInterface(IAudioSessionNotification* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionNotification_AddRef(IAudioSessionNotification* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionNotification_Release(IAudioSessionNotification* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionNotification methods ***/
static __WIDL_INLINE HRESULT IAudioSessionNotification_OnSessionCreated(IAudioSessionNotification* This,IAudioSessionControl *NewSession) {
    return This->lpVtbl->OnSessionCreated(This,NewSession);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionNotification_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioSessionEnumerator interface
 */
#ifndef __IAudioSessionEnumerator_INTERFACE_DEFINED__
#define __IAudioSessionEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionEnumerator, 0xe2f5bb11, 0x0570, 0x40ca, 0xac,0xdd, 0x3a,0xa0,0x12,0x77,0xde,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e2f5bb11-0570-40ca-acdd-3aa01277dee8")
IAudioSessionEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        int *SessionCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSession(
        int SessionCount,
        IAudioSessionControl **Session) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionEnumerator, 0xe2f5bb11, 0x0570, 0x40ca, 0xac,0xdd, 0x3a,0xa0,0x12,0x77,0xde,0xe8)
#endif
#else
typedef struct IAudioSessionEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionEnumerator *This);

    /*** IAudioSessionEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IAudioSessionEnumerator *This,
        int *SessionCount);

    HRESULT (STDMETHODCALLTYPE *GetSession)(
        IAudioSessionEnumerator *This,
        int SessionCount,
        IAudioSessionControl **Session);

    END_INTERFACE
} IAudioSessionEnumeratorVtbl;

interface IAudioSessionEnumerator {
    CONST_VTBL IAudioSessionEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionEnumerator methods ***/
#define IAudioSessionEnumerator_GetCount(This,SessionCount) (This)->lpVtbl->GetCount(This,SessionCount)
#define IAudioSessionEnumerator_GetSession(This,SessionCount,Session) (This)->lpVtbl->GetSession(This,SessionCount,Session)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionEnumerator_QueryInterface(IAudioSessionEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionEnumerator_AddRef(IAudioSessionEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionEnumerator_Release(IAudioSessionEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionEnumerator methods ***/
static __WIDL_INLINE HRESULT IAudioSessionEnumerator_GetCount(IAudioSessionEnumerator* This,int *SessionCount) {
    return This->lpVtbl->GetCount(This,SessionCount);
}
static __WIDL_INLINE HRESULT IAudioSessionEnumerator_GetSession(IAudioSessionEnumerator* This,int SessionCount,IAudioSessionControl **Session) {
    return This->lpVtbl->GetSession(This,SessionCount,Session);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionEnumerator_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioSessionManager2 interface
 */
#ifndef __IAudioSessionManager2_INTERFACE_DEFINED__
#define __IAudioSessionManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioSessionManager2, 0x77aa99a0, 0x1bd6, 0x484f, 0x8b,0xc7, 0x2c,0x65,0x4c,0x9a,0x9b,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("77aa99a0-1bd6-484f-8bc7-2c654c9a9b6f")
IAudioSessionManager2 : public IAudioSessionManager
{
    virtual HRESULT STDMETHODCALLTYPE GetSessionEnumerator(
        IAudioSessionEnumerator **SessionEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterSessionNotification(
        IAudioSessionNotification *SessionNotification) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterSessionNotification(
        IAudioSessionNotification *SessionNotification) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterDuckNotification(
        LPCWSTR sessionID,
        IAudioVolumeDuckNotification *duckNotification) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterDuckNotification(
        IAudioVolumeDuckNotification *duckNotification) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioSessionManager2, 0x77aa99a0, 0x1bd6, 0x484f, 0x8b,0xc7, 0x2c,0x65,0x4c,0x9a,0x9b,0x6f)
#endif
#else
typedef struct IAudioSessionManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioSessionManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioSessionManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioSessionManager2 *This);

    /*** IAudioSessionManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAudioSessionControl)(
        IAudioSessionManager2 *This,
        LPCGUID AudioSessionGuid,
        DWORD StreamFlags,
        IAudioSessionControl **SessionControl);

    HRESULT (STDMETHODCALLTYPE *GetSimpleAudioVolume)(
        IAudioSessionManager2 *This,
        LPCGUID AudioSessionGuid,
        DWORD StreamFlags,
        ISimpleAudioVolume **AudioVolume);

    /*** IAudioSessionManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSessionEnumerator)(
        IAudioSessionManager2 *This,
        IAudioSessionEnumerator **SessionEnum);

    HRESULT (STDMETHODCALLTYPE *RegisterSessionNotification)(
        IAudioSessionManager2 *This,
        IAudioSessionNotification *SessionNotification);

    HRESULT (STDMETHODCALLTYPE *UnregisterSessionNotification)(
        IAudioSessionManager2 *This,
        IAudioSessionNotification *SessionNotification);

    HRESULT (STDMETHODCALLTYPE *RegisterDuckNotification)(
        IAudioSessionManager2 *This,
        LPCWSTR sessionID,
        IAudioVolumeDuckNotification *duckNotification);

    HRESULT (STDMETHODCALLTYPE *UnregisterDuckNotification)(
        IAudioSessionManager2 *This,
        IAudioVolumeDuckNotification *duckNotification);

    END_INTERFACE
} IAudioSessionManager2Vtbl;

interface IAudioSessionManager2 {
    CONST_VTBL IAudioSessionManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioSessionManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioSessionManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioSessionManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioSessionManager methods ***/
#define IAudioSessionManager2_GetAudioSessionControl(This,AudioSessionGuid,StreamFlags,SessionControl) (This)->lpVtbl->GetAudioSessionControl(This,AudioSessionGuid,StreamFlags,SessionControl)
#define IAudioSessionManager2_GetSimpleAudioVolume(This,AudioSessionGuid,StreamFlags,AudioVolume) (This)->lpVtbl->GetSimpleAudioVolume(This,AudioSessionGuid,StreamFlags,AudioVolume)
/*** IAudioSessionManager2 methods ***/
#define IAudioSessionManager2_GetSessionEnumerator(This,SessionEnum) (This)->lpVtbl->GetSessionEnumerator(This,SessionEnum)
#define IAudioSessionManager2_RegisterSessionNotification(This,SessionNotification) (This)->lpVtbl->RegisterSessionNotification(This,SessionNotification)
#define IAudioSessionManager2_UnregisterSessionNotification(This,SessionNotification) (This)->lpVtbl->UnregisterSessionNotification(This,SessionNotification)
#define IAudioSessionManager2_RegisterDuckNotification(This,sessionID,duckNotification) (This)->lpVtbl->RegisterDuckNotification(This,sessionID,duckNotification)
#define IAudioSessionManager2_UnregisterDuckNotification(This,duckNotification) (This)->lpVtbl->UnregisterDuckNotification(This,duckNotification)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IAudioSessionManager2_QueryInterface(IAudioSessionManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IAudioSessionManager2_AddRef(IAudioSessionManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IAudioSessionManager2_Release(IAudioSessionManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioSessionManager methods ***/
static __WIDL_INLINE HRESULT IAudioSessionManager2_GetAudioSessionControl(IAudioSessionManager2* This,LPCGUID AudioSessionGuid,DWORD StreamFlags,IAudioSessionControl **SessionControl) {
    return This->lpVtbl->GetAudioSessionControl(This,AudioSessionGuid,StreamFlags,SessionControl);
}
static __WIDL_INLINE HRESULT IAudioSessionManager2_GetSimpleAudioVolume(IAudioSessionManager2* This,LPCGUID AudioSessionGuid,DWORD StreamFlags,ISimpleAudioVolume **AudioVolume) {
    return This->lpVtbl->GetSimpleAudioVolume(This,AudioSessionGuid,StreamFlags,AudioVolume);
}
/*** IAudioSessionManager2 methods ***/
static __WIDL_INLINE HRESULT IAudioSessionManager2_GetSessionEnumerator(IAudioSessionManager2* This,IAudioSessionEnumerator **SessionEnum) {
    return This->lpVtbl->GetSessionEnumerator(This,SessionEnum);
}
static __WIDL_INLINE HRESULT IAudioSessionManager2_RegisterSessionNotification(IAudioSessionManager2* This,IAudioSessionNotification *SessionNotification) {
    return This->lpVtbl->RegisterSessionNotification(This,SessionNotification);
}
static __WIDL_INLINE HRESULT IAudioSessionManager2_UnregisterSessionNotification(IAudioSessionManager2* This,IAudioSessionNotification *SessionNotification) {
    return This->lpVtbl->UnregisterSessionNotification(This,SessionNotification);
}
static __WIDL_INLINE HRESULT IAudioSessionManager2_RegisterDuckNotification(IAudioSessionManager2* This,LPCWSTR sessionID,IAudioVolumeDuckNotification *duckNotification) {
    return This->lpVtbl->RegisterDuckNotification(This,sessionID,duckNotification);
}
static __WIDL_INLINE HRESULT IAudioSessionManager2_UnregisterDuckNotification(IAudioSessionManager2* This,IAudioVolumeDuckNotification *duckNotification) {
    return This->lpVtbl->UnregisterDuckNotification(This,duckNotification);
}
#endif
#endif

#endif


#endif  /* __IAudioSessionManager2_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __audiopolicy_h__ */
