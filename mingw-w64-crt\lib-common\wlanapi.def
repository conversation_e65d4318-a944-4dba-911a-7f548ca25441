;
; Definition file of wlanapi.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "wlanapi.dll"
EXPORTS
WFDGetSessionEndpointPairsInt
QueryNetconStatus
QueryNetconVirtualCharacteristic
WFDAcceptConnectRequestAndOpenSessionInt
WFDAcceptGroupRequestAndOpenSessionInt
WFDCancelConnectorPairWithOOB
WFDCancelListenerPairWithOOB
WFDCancelOpenSession
WFDCancelOpenSessionInt
WFDCloseHandle
WFDCloseHandleInt
WFDCloseLegacySessionInt
WFDCloseOOBPairingSession
WFDCloseSession
WFDCloseSessionInt
WFDConfigureFirewallForSessionInt
WFDDeclineConnectRequestInt
WFDDeclineGroupRequestInt
WFDDiscoverDevicesInt
WFDFlushVisibleDeviceListInt
WFDForceDisconnectInt
WFDForceDisconnect<PERSON><PERSON>nt
WFDFreeMemoryInt
WFDGetDefaultGroupProfileInt
WFDGetOOBBlob
WFDGetProfileKeyInfoInt
WFDGetVisibleDevicesInt
WFDIsInterfaceWiFiDirect
WFDIsWiFiDirectRunningOnWiFiAdapter
WFDLowPrivCancelOpenSessionInt
WFDLowPrivCloseHandleInt
WFDLowPrivCloseSessionInt
WFDLowPrivConfigureFirewallForSessionInt
WFDLowPrivGetSessionEndpointPairsInt
WFDLowPrivIsWfdSupportedInt
WFDLowPrivOpenHandleInt
WFDLowPrivRegisterNotificationInt
WFDLowPrivStartOpenSessionByInterfaceIdInt
WFDOpenHandle
WFDOpenHandleInt
WFDOpenLegacySession
WFDOpenLegacySessionInt
WFDPairCancelByDeviceAddressInt
WFDPairCancelInt
WFDPairEnumerateCeremoniesInt
WFDPairSelectCeremonyInt
WFDPairWithDeviceAndOpenSessionExInt
WFDPairWithDeviceAndOpenSessionInt
WFDParseOOBBlob
WFDParseProfileXmlInt
WFDQueryPropertyInt
WFDRegisterNotificationInt
WFDSetAdditionalIEsInt
WFDSetPropertyInt
WFDSetSecondaryDeviceTypeListInt
WFDStartConnectorPairWithOOB
WFDStartListenerPairWithOOB
WFDStartOpenSession
WFDStartOpenSessionInt
WFDStartUsingGroupInt
WFDStopDiscoverDevicesInt
WFDStopUsingGroupInt
WFDUpdateDeviceVisibility
WlanAllocateMemory
WlanCancelPlap
WlanCloseHandle
WlanConnect
WlanConnectEx
WlanConnectWithInput
WlanDeinitPlapParams
WlanDeleteProfile
WlanDisconnect
WlanDoPlap
WlanDoesBssMatchSecurity
WlanEnumAllInterfaces
WlanEnumInterfaces
WlanExtractPsdIEDataList
WlanFreeMemory
WlanGenerateProfileXmlBasicSettings
WlanGetAvailableNetworkList
WlanGetFilterList
WlanGetInterfaceCapability
WlanGetMFPNegotiated
WlanGetNetworkBssList
WlanGetProfile
WlanGetProfileCustomUserData
WlanGetProfileEapUserDataInfo
WlanGetProfileIndex
WlanGetProfileKeyInfo
WlanGetProfileList
WlanGetProfileMetadata
WlanGetProfileSsidList
WlanGetRadioInformation
WlanGetSecuritySettings
WlanGetStoredRadioState
WlanHostedNetworkForceStart
WlanHostedNetworkForceStop
WlanHostedNetworkFreeWCNSettings
WlanHostedNetworkHlpQueryEverUsed
WlanHostedNetworkInitSettings
WlanHostedNetworkQueryProperty
WlanHostedNetworkQuerySecondaryKey
WlanHostedNetworkQueryStatus
WlanHostedNetworkQueryWCNSettings
WlanHostedNetworkRefreshSecuritySettings
WlanHostedNetworkSetProperty
WlanHostedNetworkSetSecondaryKey
WlanHostedNetworkSetWCNSettings
WlanHostedNetworkStartUsing
WlanHostedNetworkStopUsing
WlanIhvControl
WlanInitPlapParams
WlanInternalScan
WlanIsActiveConsoleUser
WlanIsNetworkSuppressed
WlanIsUIRequestPending
WlanLowPrivCloseHandle
WlanLowPrivEnumInterfaces
WlanLowPrivFreeMemory
WlanLowPrivOpenHandle
WlanLowPrivQueryInterface
WlanLowPrivSetInterface
WlanNotifyVsIeProviderInt
WlanOpenHandle
WlanParseProfileXmlBasicSettings
WlanPrivateGetAvailableNetworkList
WlanQueryAutoConfigParameter
WlanQueryCreateAllUserProfileRestricted
WlanQueryInterface
WlanQueryPlapCredentials
WlanQueryPreConnectInput
WlanQueryVirtualInterfaceType
WlanReasonCodeToString
WlanRefreshConnections
WlanRegisterNotification
WlanRegisterVirtualStationNotification
WlanRemoveUIForwardingNetworkList
WlanRenameProfile
WlanSaveTemporaryProfile
WlanScan
WlanSendUIResponse
WlanSetAllUserProfileRestricted
WlanSetAutoConfigParameter
WlanSetFilterList
WlanSetInterface
WlanSetProfile
WlanSetProfileCustomUserData
WlanSetProfileEapUserData
WlanSetProfileEapXmlUserData
WlanSetProfileList
WlanSetProfileMetadata
WlanSetProfilePosition
WlanSetPsdIEDataList
WlanSetSecuritySettings
WlanSetUIForwardingNetworkList
WlanSignalValueToBar
WlanSsidToDisplayName
WlanStartAP
WlanStopAP
WlanStoreRadioStateOnEnteringAirPlaneMode
WlanStringToSsid
WlanTryUpgradeCurrentConnectionAuthCipher
WlanUpdateProfileWithAuthCipher
WlanUtf8SsidToDisplayName
WlanWcmGetInterface
WlanWcmGetProfileList
WlanWcmSetInterface
WlanWfdGOSetWCNSettings
WlanWfdGetPeerInfo
WlanWfdStartGO
WlanWfdStopGO
