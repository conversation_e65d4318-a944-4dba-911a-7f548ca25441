LIBRARY api-ms-win-crt-private-l1-1-0

EXPORTS

#include "func.def.in"

_CreateFrameInfo
F_I386(_CxxThrowException@8)
F_NON_I386(_CxxThrowException)
F_I386(_EH_prolog)
_FindAndUnlinkFrame
_GetImageBase
_GetThrowImageBase
_IsExceptionObjectToBeDestroyed
_NLG_Dispatch2
_NLG_Return
_NLG_Return2
_SetImageBase
_SetThrowImageBase
_SetWinRTOutOfMemoryExceptionCallback
__AdjustPointer
__BuildCatchObject
__BuildCatchObjectHelper
F_NON_I386(__C_specific_handler)
__CxxDetectRethrow
__CxxExceptionFilter
__CxxFrameHandler
__CxxFrameHandler2
__CxxFrameHandler3
F_I386(__CxxLongjmpUnwind@4)
__CxxQueryExceptionSize
__CxxRegisterExceptionObject
__CxxUnregisterExceptionObject
__DestructExceptionObject
__FrameUnwindFilter
__GetPlatformExceptionInfo
__NLG_Dispatch2
__NLG_Return2
__RTCastToVoid
__RTDynamicCast
__RTtypeid
__TypeMatch
__current_exception
__current_exception_context
__dcrt_get_wide_environment_from_os
__dcrt_initial_narrow_environment
__intrinsic_abnormal_termination
__intrinsic_setjmp
F64(__intrinsic_setjmpex)
__processing_throw
__report_gsfailure
__std_exception_copy
__std_exception_destroy
__std_type_info_compare
__std_type_info_destroy_list
__std_type_info_hash
__std_type_info_name
__unDName
__unDNameEx
__uncaught_exception
F_I386(_chkesp)
F_I386(_except_handler2)
F_I386(_except_handler3)
F_I386(_except_handler4_common)
_get_purecall_handler
_get_unexpected
F_I386(_global_unwind2)
_is_exception_typeof
F_X64(_local_unwind)
F_I386(_local_unwind2)
F_I386(_local_unwind4)
F_I386(_longjmpex)
_o__CIacos
_o__CIasin
_o__CIatan
_o__CIatan2
_o__CIcos
_o__CIcosh
_o__CIexp
_o__CIfmod
_o__CIlog
_o__CIlog10
_o__CIpow
_o__CIsin
_o__CIsinh
_o__CIsqrt
_o__CItan
_o__CItanh
_o__Getdays
_o__Getmonths
_o__Gettnames
_o__Strftime
_o__W_Getdays
_o__W_Getmonths
_o__W_Gettnames
_o__Wcsftime
_o___acrt_iob_func
_o___conio_common_vcprintf
_o___conio_common_vcprintf_p
_o___conio_common_vcprintf_s
_o___conio_common_vcscanf
_o___conio_common_vcwprintf
_o___conio_common_vcwprintf_p
_o___conio_common_vcwprintf_s
_o___conio_common_vcwscanf
_o___daylight
_o___dstbias
_o___fpe_flt_rounds
_o___libm_sse2_acos
_o___libm_sse2_acosf
_o___libm_sse2_asin
_o___libm_sse2_asinf
_o___libm_sse2_atan
_o___libm_sse2_atan2
_o___libm_sse2_atanf
_o___libm_sse2_cos
_o___libm_sse2_cosf
_o___libm_sse2_exp
_o___libm_sse2_expf
_o___libm_sse2_log
_o___libm_sse2_log10
_o___libm_sse2_log10f
_o___libm_sse2_logf
_o___libm_sse2_pow
_o___libm_sse2_powf
_o___libm_sse2_sin
_o___libm_sse2_sinf
_o___libm_sse2_tan
_o___libm_sse2_tanf
_o___p___argc
_o___p___argv
_o___p___wargv
_o___p__acmdln
_o___p__commode
_o___p__environ
_o___p__fmode
_o___p__mbcasemap
_o___p__mbctype
_o___p__pgmptr
_o___p__wcmdln
_o___p__wenviron
_o___p__wpgmptr
_o___pctype_func
_o___pwctype_func
_o___stdio_common_vfprintf
_o___stdio_common_vfprintf_p
_o___stdio_common_vfprintf_s
_o___stdio_common_vfscanf
_o___stdio_common_vfwprintf
_o___stdio_common_vfwprintf_p
_o___stdio_common_vfwprintf_s
_o___stdio_common_vfwscanf
_o___stdio_common_vsnprintf_s
_o___stdio_common_vsnwprintf_s
_o___stdio_common_vsprintf
_o___stdio_common_vsprintf_p
_o___stdio_common_vsprintf_s
_o___stdio_common_vsscanf
_o___stdio_common_vswprintf
_o___stdio_common_vswprintf_p
_o___stdio_common_vswprintf_s
_o___stdio_common_vswscanf
_o___timezone
_o___tzname
_o___wcserror
_o__access
_o__access_s
_o__aligned_free
_o__aligned_malloc
_o__aligned_msize
_o__aligned_offset_malloc
_o__aligned_offset_realloc
_o__aligned_offset_recalloc
_o__aligned_realloc
_o__aligned_recalloc
_o__atodbl
_o__atodbl_l
_o__atof_l
_o__atoflt
_o__atoflt_l
_o__atoi64
_o__atoi64_l
_o__atoi_l
_o__atol_l
_o__atoldbl
_o__atoldbl_l
_o__atoll_l
_o__beep
_o__beginthread
_o__beginthreadex
_o__cabs
_o__callnewh
_o__calloc_base
_o__cgets
_o__cgets_s
_o__cgetws
_o__cgetws_s
_o__chdir
_o__chdrive
_o__chmod
_o__chsize
_o__chsize_s
_o__close
_o__commit
_o__configure_wide_argv
_o__cputs
_o__cputws
_o__creat
_o__create_locale
_o__ctime32_s
_o__ctime64_s
_o__cwait
_o__d_int
_o__dclass
_o__difftime32
_o__difftime64
_o__dlog
_o__dnorm
_o__dpcomp
_o__dpoly
_o__dscale
_o__dsign
_o__dsin
_o__dtest
_o__dunscale
_o__dup
_o__dup2
_o__dupenv_s
_o__ecvt
_o__ecvt_s
_o__endthread
_o__endthreadex
_o__eof
_o__errno
_o__except1
_o__execute_onexit_table
_o__execv
_o__execve
_o__execvp
_o__execvpe
_o__expand
_o__fclose_nolock
_o__fcloseall
_o__fcvt
_o__fcvt_s
_o__fd_int
_o__fdclass
_o__fdexp
_o__fdlog
_o__fdopen
_o__fdpcomp
_o__fdpoly
_o__fdscale
_o__fdsign
_o__fdsin
_o__fflush_nolock
_o__fgetc_nolock
_o__fgetchar
_o__fgetwc_nolock
_o__fgetwchar
_o__filelength
_o__filelengthi64
_o__fileno
_o__findclose
_o__findfirst32
_o__findfirst32i64
_o__findfirst64
_o__findfirst64i32
_o__findnext32
_o__findnext32i64
_o__findnext64
_o__findnext64i32
_o__flushall
_o__fpclass
_o__fpclassf
_o__fputc_nolock
_o__fputchar
_o__fputwc_nolock
_o__fputwchar
_o__fread_nolock
_o__fread_nolock_s
_o__free_base
_o__free_locale
_o__fseek_nolock
_o__fseeki64
_o__fseeki64_nolock
_o__fsopen
_o__fstat32
_o__fstat32i64
_o__fstat64
_o__fstat64i32
_o__ftell_nolock
_o__ftelli64
_o__ftelli64_nolock
_o__ftime32
_o__ftime32_s
_o__ftime64
_o__ftime64_s
_o__fullpath
_o__futime32
_o__futime64
_o__fwrite_nolock
_o__gcvt
_o__gcvt_s
_o__get_daylight
_o__get_doserrno
_o__get_dstbias
_o__get_errno
_o__get_fmode
_o__get_heap_handle
_o__get_invalid_parameter_handler
_o__get_narrow_winmain_command_line
_o__get_osfhandle
_o__get_pgmptr
_o__get_stream_buffer_pointers
_o__get_terminate
_o__get_thread_local_invalid_parameter_handler
_o__get_timezone
_o__get_tzname
_o__get_wide_winmain_command_line
_o__get_wpgmptr
_o__getc_nolock
_o__getch
_o__getch_nolock
_o__getche
_o__getche_nolock
_o__getcwd
_o__getdcwd
_o__getdiskfree
_o__getdllprocaddr
_o__getdrive
_o__getdrives
_o__getmbcp
_o__getsystime
_o__getw
_o__getwc_nolock
_o__getwch
_o__getwch_nolock
_o__getwche
_o__getwche_nolock
_o__getws
_o__getws_s
_o__gmtime32
_o__gmtime32_s
_o__gmtime64
_o__gmtime64_s
_o__heapchk
_o__heapmin
_o__hypot
_o__hypotf
_o__i64toa
_o__i64toa_s
_o__i64tow
_o__i64tow_s
_o__initialize_onexit_table
_o__invalid_parameter_noinfo
_o__invalid_parameter_noinfo_noreturn
_o__isatty
_o__isctype
_o__isctype_l
_o__isleadbyte_l
_o__ismbbalnum
_o__ismbbalnum_l
_o__ismbbalpha
_o__ismbbalpha_l
_o__ismbbblank
_o__ismbbblank_l
_o__ismbbgraph
_o__ismbbgraph_l
_o__ismbbkalnum
_o__ismbbkalnum_l
_o__ismbbkana
_o__ismbbkana_l
_o__ismbbkprint
_o__ismbbkprint_l
_o__ismbbkpunct
_o__ismbbkpunct_l
_o__ismbblead
_o__ismbblead_l
_o__ismbbprint
_o__ismbbprint_l
_o__ismbbpunct
_o__ismbbpunct_l
_o__ismbbtrail
_o__ismbbtrail_l
_o__ismbcalnum
_o__ismbcalnum_l
_o__ismbcalpha
_o__ismbcalpha_l
_o__ismbcblank
_o__ismbcblank_l
_o__ismbcdigit
_o__ismbcdigit_l
_o__ismbcgraph
_o__ismbcgraph_l
_o__ismbchira
_o__ismbchira_l
_o__ismbckata
_o__ismbckata_l
_o__ismbcl0
_o__ismbcl0_l
_o__ismbcl1
_o__ismbcl1_l
_o__ismbcl2
_o__ismbcl2_l
_o__ismbclegal
_o__ismbclegal_l
_o__ismbclower
_o__ismbclower_l
_o__ismbcprint
_o__ismbcprint_l
_o__ismbcpunct
_o__ismbcpunct_l
_o__ismbcspace
_o__ismbcspace_l
_o__ismbcsymbol
_o__ismbcsymbol_l
_o__ismbcupper
_o__ismbcupper_l
_o__ismbslead
_o__ismbslead_l
_o__ismbstrail
_o__ismbstrail_l
_o__iswctype_l
_o__itoa
_o__itoa_s
_o__itow
_o__itow_s
_o__j0
_o__j1
_o__jn
_o__kbhit
_o__ld_int
_o__ldclass
_o__ldexp
_o__ldlog
_o__ldpcomp
_o__ldpoly
_o__ldscale
_o__ldsign
_o__ldsin
_o__ldtest
_o__ldunscale
_o__lfind
_o__lfind_s
_o__libm_sse2_acos_precise
_o__libm_sse2_asin_precise
_o__libm_sse2_atan_precise
_o__libm_sse2_cos_precise
_o__libm_sse2_exp_precise
_o__libm_sse2_log10_precise
_o__libm_sse2_log_precise
_o__libm_sse2_pow_precise
_o__libm_sse2_sin_precise
_o__libm_sse2_sqrt_precise
_o__libm_sse2_tan_precise
_o__loaddll
_o__localtime32
_o__localtime32_s
_o__localtime64
_o__localtime64_s
_o__lock_file
_o__locking
_o__logb
_o__logbf
_o__lsearch
_o__lsearch_s
_o__lseek
_o__lseeki64
_o__ltoa
_o__ltoa_s
_o__ltow
_o__ltow_s
_o__makepath
_o__makepath_s
_o__malloc_base
_o__mbbtombc
_o__mbbtombc_l
_o__mbbtype
_o__mbbtype_l
_o__mbccpy
_o__mbccpy_l
_o__mbccpy_s
_o__mbccpy_s_l
_o__mbcjistojms
_o__mbcjistojms_l
_o__mbcjmstojis
_o__mbcjmstojis_l
_o__mbclen
_o__mbclen_l
_o__mbctohira
_o__mbctohira_l
_o__mbctokata
_o__mbctokata_l
_o__mbctolower
_o__mbctolower_l
_o__mbctombb
_o__mbctombb_l
_o__mbctoupper
_o__mbctoupper_l
_o__mblen_l
_o__mbsbtype
_o__mbsbtype_l
_o__mbscat_s
_o__mbscat_s_l
_o__mbschr
_o__mbschr_l
_o__mbscmp
_o__mbscmp_l
_o__mbscoll
_o__mbscoll_l
_o__mbscpy_s
_o__mbscpy_s_l
_o__mbscspn
_o__mbscspn_l
_o__mbsdec
_o__mbsdec_l
_o__mbsicmp
_o__mbsicmp_l
_o__mbsicoll
_o__mbsicoll_l
_o__mbsinc
_o__mbsinc_l
_o__mbslen
_o__mbslen_l
_o__mbslwr
_o__mbslwr_l
_o__mbslwr_s
_o__mbslwr_s_l
_o__mbsnbcat
_o__mbsnbcat_l
_o__mbsnbcat_s
_o__mbsnbcat_s_l
_o__mbsnbcmp
_o__mbsnbcmp_l
_o__mbsnbcnt
_o__mbsnbcnt_l
_o__mbsnbcoll
_o__mbsnbcoll_l
_o__mbsnbcpy
_o__mbsnbcpy_l
_o__mbsnbcpy_s
_o__mbsnbcpy_s_l
_o__mbsnbicmp
_o__mbsnbicmp_l
_o__mbsnbicoll
_o__mbsnbicoll_l
_o__mbsnbset
_o__mbsnbset_l
_o__mbsnbset_s
_o__mbsnbset_s_l
_o__mbsncat
_o__mbsncat_l
_o__mbsncat_s
_o__mbsncat_s_l
_o__mbsnccnt
_o__mbsnccnt_l
_o__mbsncmp
_o__mbsncmp_l
_o__mbsncoll
_o__mbsncoll_l
_o__mbsncpy
_o__mbsncpy_l
_o__mbsncpy_s
_o__mbsncpy_s_l
_o__mbsnextc
_o__mbsnextc_l
_o__mbsnicmp
_o__mbsnicmp_l
_o__mbsnicoll
_o__mbsnicoll_l
_o__mbsninc
_o__mbsninc_l
_o__mbsnlen
_o__mbsnlen_l
_o__mbsnset
_o__mbsnset_l
_o__mbsnset_s
_o__mbsnset_s_l
_o__mbspbrk
_o__mbspbrk_l
_o__mbsrchr
_o__mbsrchr_l
_o__mbsrev
_o__mbsrev_l
_o__mbsset
_o__mbsset_l
_o__mbsset_s
_o__mbsset_s_l
_o__mbsspn
_o__mbsspn_l
_o__mbsspnp
_o__mbsspnp_l
_o__mbsstr
_o__mbsstr_l
_o__mbstok
_o__mbstok_l
_o__mbstok_s
_o__mbstok_s_l
_o__mbstowcs_l
_o__mbstowcs_s_l
_o__mbstrlen
_o__mbstrlen_l
_o__mbstrnlen
_o__mbstrnlen_l
_o__mbsupr
_o__mbsupr_l
_o__mbsupr_s
_o__mbsupr_s_l
_o__mbtowc_l
_o__memicmp
_o__memicmp_l
_o__mkdir
_o__mkgmtime32
_o__mkgmtime64
_o__mktemp
_o__mktemp_s
_o__mktime32
_o__mktime64
_o__msize
_o__nextafter
_o__nextafterf
_o__open_osfhandle
_o__pclose
_o__pipe
_o__popen
_o__putc_nolock
_o__putch
_o__putch_nolock
_o__putenv
_o__putenv_s
_o__putw
_o__putwc_nolock
_o__putwch
_o__putwch_nolock
_o__putws
_o__read
_o__realloc_base
_o__recalloc
_o__register_onexit_function
_o__resetstkoflw
_o__rmdir
_o__rmtmp
_o__scalb
_o__scalbf
_o__searchenv
_o__searchenv_s
_o__set_abort_behavior
_o__set_doserrno
_o__set_errno
_o__set_invalid_parameter_handler
_o__set_new_handler
_o__set_new_mode
_o__set_thread_local_invalid_parameter_handler
_o__seterrormode
_o__setmbcp
_o__setmode
_o__setsystime
_o__sleep
_o__sopen
_o__sopen_dispatch
_o__sopen_s
_o__spawnv
_o__spawnve
_o__spawnvp
_o__spawnvpe
_o__splitpath
_o__splitpath_s
_o__stat32
_o__stat32i64
_o__stat64
_o__stat64i32
_o__strcoll_l
_o__strdate
_o__strdate_s
_o__strdup
_o__strerror
_o__strerror_s
_o__strftime_l
_o__stricmp
_o__stricmp_l
_o__stricoll
_o__stricoll_l
_o__strlwr
_o__strlwr_l
_o__strlwr_s
_o__strlwr_s_l
_o__strncoll
_o__strncoll_l
_o__strnicmp
_o__strnicmp_l
_o__strnicoll
_o__strnicoll_l
_o__strnset_s
_o__strset_s
_o__strtime
_o__strtime_s
_o__strtod_l
_o__strtof_l
_o__strtoi64
_o__strtoi64_l
_o__strtol_l
_o__strtold_l
_o__strtoll_l
_o__strtoui64
_o__strtoui64_l
_o__strtoul_l
_o__strtoull_l
_o__strupr
_o__strupr_l
_o__strupr_s
_o__strupr_s_l
_o__strxfrm_l
_o__swab
_o__tell
_o__telli64
_o__timespec32_get
_o__timespec64_get
_o__tolower
_o__tolower_l
_o__toupper
_o__toupper_l
_o__towlower_l
_o__towupper_l
_o__tzset
_o__ui64toa
_o__ui64toa_s
_o__ui64tow
_o__ui64tow_s
_o__ultoa
_o__ultoa_s
_o__ultow
_o__ultow_s
_o__umask
_o__umask_s
_o__ungetc_nolock
_o__ungetch
_o__ungetch_nolock
_o__ungetwc_nolock
_o__ungetwch
_o__ungetwch_nolock
_o__unlink
_o__unloaddll
_o__unlock_file
_o__utime32
_o__utime64
_o__waccess
_o__waccess_s
_o__wasctime
_o__wasctime_s
_o__wchdir
_o__wchmod
_o__wcreat
_o__wcreate_locale
_o__wcscoll_l
_o__wcsdup
_o__wcserror
_o__wcserror_s
_o__wcsftime_l
_o__wcsicmp
_o__wcsicmp_l
_o__wcsicoll
_o__wcsicoll_l
_o__wcslwr
_o__wcslwr_l
_o__wcslwr_s
_o__wcslwr_s_l
_o__wcsncoll
_o__wcsncoll_l
_o__wcsnicmp
_o__wcsnicmp_l
_o__wcsnicoll
_o__wcsnicoll_l
_o__wcsnset
_o__wcsnset_s
_o__wcsset
_o__wcsset_s
_o__wcstod_l
_o__wcstof_l
_o__wcstoi64
_o__wcstoi64_l
_o__wcstol_l
_o__wcstold_l
_o__wcstoll_l
_o__wcstombs_l
_o__wcstombs_s_l
_o__wcstoui64
_o__wcstoui64_l
_o__wcstoul_l
_o__wcstoull_l
_o__wcsupr
_o__wcsupr_l
_o__wcsupr_s
_o__wcsupr_s_l
_o__wcsxfrm_l
_o__wctime32
_o__wctime32_s
_o__wctime64
_o__wctime64_s
_o__wctomb_l
_o__wctomb_s_l
_o__wdupenv_s
_o__wexecv
_o__wexecve
_o__wexecvp
_o__wexecvpe
_o__wfdopen
_o__wfindfirst32
_o__wfindfirst32i64
_o__wfindfirst64
_o__wfindfirst64i32
_o__wfindnext32
_o__wfindnext32i64
_o__wfindnext64
_o__wfindnext64i32
_o__wfopen
_o__wfopen_s
_o__wfreopen
_o__wfreopen_s
_o__wfsopen
_o__wfullpath
_o__wgetcwd
_o__wgetdcwd
_o__wgetenv
_o__wgetenv_s
_o__wmakepath
_o__wmakepath_s
_o__wmkdir
_o__wmktemp
_o__wmktemp_s
_o__wperror
_o__wpopen
_o__wputenv
_o__wputenv_s
_o__wremove
_o__wrename
_o__write
_o__wrmdir
_o__wsearchenv
_o__wsearchenv_s
_o__wsetlocale
_o__wsopen_dispatch
_o__wsopen_s
_o__wspawnv
_o__wspawnve
_o__wspawnvp
_o__wspawnvpe
_o__wsplitpath
_o__wsplitpath_s
_o__wstat32
_o__wstat32i64
_o__wstat64
_o__wstat64i32
_o__wstrdate
_o__wstrdate_s
_o__wstrtime
_o__wstrtime_s
_o__wsystem
_o__wtmpnam_s
_o__wtof
_o__wtof_l
_o__wtoi
_o__wtoi64
_o__wtoi64_l
_o__wtoi_l
_o__wtol
_o__wtol_l
_o__wtoll
_o__wtoll_l
_o__wunlink
_o__wutime32
_o__wutime64
_o__y0
_o__y1
_o__yn
_o_abort
_o_acos
_o_acosf
_o_acosh
_o_acoshf
_o_acoshl
_o_asctime
_o_asctime_s
_o_asin
_o_asinf
_o_asinh
_o_asinhf
_o_asinhl
_o_atan
_o_atan2
_o_atan2f
_o_atanf
_o_atanh
_o_atanhf
_o_atanhl
_o_atof
_o_atoi
_o_atol
_o_atoll
_o_bsearch
_o_bsearch_s
_o_btowc
_o_calloc
_o_cbrt
_o_cbrtf
_o_ceil
_o_ceilf
_o_clearerr
_o_clearerr_s
_o_cos
_o_cosf
_o_cosh
_o_coshf
_o_erf
_o_erfc
_o_erfcf
_o_erfcl
_o_erff
_o_erfl
_o_exp
_o_exp2
_o_exp2f
_o_exp2l
_o_expf
_o_fabs
_o_fclose
_o_feof
_o_ferror
_o_fflush
_o_fgetc
_o_fgetpos
_o_fgets
_o_fgetwc
_o_fgetws
_o_floor
_o_floorf
_o_fma
_o_fmaf
_o_fmal
_o_fmod
_o_fmodf
_o_fopen
_o_fopen_s
_o_fputc
_o_fputs
_o_fputwc
_o_fputws
_o_fread
_o_fread_s
_o_free
_o_freopen
_o_freopen_s
_o_frexp
_o_fseek
_o_fsetpos
_o_ftell
_o_fwrite
_o_getc
_o_getchar
_o_getenv
_o_getenv_s
_o_gets
_o_gets_s
_o_getwc
_o_getwchar
_o_hypot
_o_is_wctype
_o_isalnum
_o_isalpha
_o_isblank
_o_iscntrl
_o_isdigit
_o_isgraph
_o_isleadbyte
_o_islower
_o_isprint
_o_ispunct
_o_isspace
_o_isupper
_o_iswalnum
_o_iswalpha
_o_iswascii
_o_iswblank
_o_iswcntrl
_o_iswctype
_o_iswdigit
_o_iswgraph
_o_iswlower
_o_iswprint
_o_iswpunct
_o_iswspace
_o_iswupper
_o_iswxdigit
_o_isxdigit
_o_ldexp
_o_lgamma
_o_lgammaf
_o_lgammal
_o_llrint
_o_llrintf
_o_llrintl
_o_llround
_o_llroundf
_o_llroundl
_o_localeconv
_o_log
_o_log10
_o_log10f
_o_log1p
_o_log1pf
_o_log1pl
_o_log2
_o_log2f
_o_log2l
_o_logb
_o_logbf
_o_logbl
_o_logf
_o_lrint
_o_lrintf
_o_lrintl
_o_lround
_o_lroundf
_o_lroundl
_o_malloc
_o_mblen
_o_mbrlen
_o_mbrtoc16
_o_mbrtoc32
_o_mbrtowc
_o_mbsrtowcs
_o_mbsrtowcs_s
_o_mbstowcs
_o_mbstowcs_s
_o_mbtowc
_o_memset
_o_modf
_o_modff
_o_nan
_o_nanf
_o_nanl
_o_nearbyint
_o_nearbyintf
_o_nearbyintl
_o_nextafter
_o_nextafterf
_o_nextafterl
_o_nexttoward
_o_nexttowardf
_o_nexttowardl
_o_pow
_o_powf
_o_putc
_o_putchar
_o_puts
_o_putwc
_o_putwchar
_o_qsort
_o_qsort_s
_o_raise
_o_rand
_o_rand_s
_o_realloc
_o_remainder
_o_remainderf
_o_remainderl
_o_remove
_o_remquo
_o_remquof
_o_remquol
_o_rewind
_o_rint
_o_rintf
_o_rintl
_o_round
_o_roundf
_o_roundl
_o_scalbln
_o_scalblnf
_o_scalblnl
_o_scalbn
_o_scalbnf
_o_scalbnl
_o_set_terminate
_o_setbuf
_o_setvbuf
_o_sin
_o_sinf
_o_sinh
_o_sinhf
_o_sqrt
_o_sqrtf
_o_srand
_o_strcat_s
_o_strcoll
_o_strcpy_s
_o_strerror
_o_strerror_s
_o_strftime
_o_strncat_s
_o_strncpy_s
_o_strtod
_o_strtof
_o_strtok
_o_strtok_s
_o_strtol
_o_strtold
_o_strtoll
_o_strtoul
_o_strtoull
_o_system
_o_tan
_o_tanf
_o_tanh
_o_tanhf
_o_terminate
_o_tgamma
_o_tgammaf
_o_tgammal
_o_tmpfile_s
_o_tmpnam_s
_o_tolower
_o_toupper
_o_towlower
_o_towupper
_o_ungetc
_o_ungetwc
_o_wcrtomb
_o_wcrtomb_s
_o_wcscat_s
_o_wcscoll
_o_wcscpy
_o_wcscpy_s
_o_wcsftime
_o_wcsncat_s
_o_wcsncpy_s
_o_wcsrtombs
_o_wcsrtombs_s
_o_wcstod
_o_wcstof
_o_wcstok
_o_wcstok_s
_o_wcstol
_o_wcstold
_o_wcstoll
_o_wcstombs
_o_wcstombs_s
_o_wcstoul
_o_wcstoull
_o_wctob
_o_wctomb
_o_wctomb_s
_o_wmemcpy_s
_o_wmemmove_s
_purecall
F_I386(_seh_longjmp_unwind@4)
F_I386(_seh_longjmp_unwind4@4)
_set_purecall_handler
_set_se_translator
F_I386(_setjmp3)
longjmp
memchr
memcmp
memcpy
memmove
set_unexpected
F_X64(setjmp)
strchr
strrchr
strstr
unexpected
wcschr
wcsrchr
wcsstr
