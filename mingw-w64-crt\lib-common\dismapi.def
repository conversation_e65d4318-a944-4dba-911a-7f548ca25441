;
; Definition file of Dism<PERSON><PERSON>.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "DismApi.DLL"
EXPORTS
DismAddCapability
DismAddDriver
DismAddPackage
DismApplyUnattend
DismCheckImageHealth
DismCleanupMountpoints
DismCloseSession
DismCommitImage
DismDelete
DismDisableFeature
DismEnableFeature
DismGetCapabilities
DismGetCapabilityInfo
DismGetDriverInfo
DismGetDrivers
DismGetFeatureInfo
DismGetFeatureParent
DismGetFeatures
DismGetImageInfo
DismGetLastErrorMessage
DismGetMountedImageInfo
DismGetPackageInfo
DismGetPackageInfoEx
DismGetPackages
DismGetReservedStorageState
DismInitialize
DismMountImage
DismOpenSession
DismRemountImage
DismRemoveCapability
DismRemoveDriver
DismRemovePackage
DismRestoreI<PERSON>Health
DismSetReservedStorageState
DismShutdown
DismUnmountImage
_DismAddCapabilityEx
_DismAddDriverEx
_DismAddPackageEx
_DismAddPackageFamilyToUninstallBlocklist
_DismAddProvisionedAppxPackage
_DismApplyCustomDataImage
_DismApplyFfuImage
_DismApplyProvisioningPackage
_DismCleanImage
_DismEnableDisableFeature
_DismExportDriver
_DismExportSource
_DismExportSourceEx
_DismGetCapabilitiesEx
_DismGetCapabilityInfoEx
_DismGetCurrentEdition
_DismGetDriversEx
_DismGetEffectiveSystemUILanguage
_DismGetFeaturesEx
_DismGetInstallLanguage
_DismGetKCacheBinaryValue
_DismGetKCacheDwordValue
_DismGetKCacheStringValue
_DismGetLastCBSSessionID
_DismGetNonRemovableAppsPolicy
_DismGetOSUninstallWindow
_DismGetOsInfo
_DismGetProductKeyInfo
_DismGetProvisionedAppxPackages
_DismGetProvisioningPackageInfo
_DismGetRegistryMountPoint
_DismGetStateFromCBSSessionID
_DismGetTargetCompositionEditions
_DismGetTargetEditions
_DismGetTargetVirtualEditions
_DismGetUsedSpace
_DismInitiateOSUninstall
_DismOptimizeImage
_DismOptimizeProvisionedAppxPackages
_DismRemoveOSUninstall
_DismRemovePackageFamilyFromUninstallBlocklist
_DismRemoveProvisionedAppxPackage
_DismRemoveProvisionedAppxPackageAllUsers
_DismRevertPendingActions
_DismSetAllIntlSettings
_DismSetAppXProvisionedDataFile
_DismSetEdition
_DismSetEdition2
_DismSetFirstBootCommandLine
_DismSetMachineName
_DismSetOSUninstallWindow
_DismSetProductKey
_DismSetSkuIntlDefaults
_DismSplitFfuImage
_DismStage
_DismSysprepCleanup
_DismSysprepGeneralize
_DismSysprepSpecialize
_DismValidateProductKey
