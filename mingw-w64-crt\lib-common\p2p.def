;
; Definition file of P2P.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "P2P.dll"
EXPORTS
PeerGroupHandlePowerEvent
PeerCollabAddContact
PeerCollabAsyncInviteContact
PeerCollabAsyncInviteEndpoint
PeerCollabCancelInvitation
PeerCollabCloseHandle
PeerCollabDeleteContact
PeerCollabDeleteEndpointData
PeerCollabDeleteObject
PeerCollabEnumApplicationRegistrationInfo
PeerCollabEnumApplications
PeerCollabEnumContacts
PeerCollabEnumEndpoints
PeerCollabEnumObjects
PeerCollabEnumPeopleNearMe
PeerCollabExportContact
PeerCollabGetAppLaunchInfo
PeerCollabGetApplicationRegistrationInfo
PeerCollabGetContact
PeerCollabGetEndpointName
PeerCollabGetEventData
PeerCollabGetInvitationResponse
PeerCollabGetPres<PERSON><PERSON>n<PERSON>
PeerCollabGetSigninOptions
PeerCollabInviteContact
PeerCollabInviteEndpoint
PeerCollabParseContact
PeerCollabQueryContactData
PeerCollabRefreshEndpointData
PeerCollabRegisterApplication
PeerCollabRegisterEvent
PeerCollabSetEndpointName
PeerCollabSetObject
PeerCollabSetPresenceInfo
PeerCollabShutdown
PeerCollabSignin
PeerCollabSignout
PeerCollabStartup
PeerCollabSubscribeEndpointData
PeerCollabUnregisterApplication
PeerCollabUnregisterEvent
PeerCollabUnsubscribeEndpointData
PeerCollabUpdateContact
PeerCreatePeerName
PeerEndEnumeration
PeerEnumGroups
PeerEnumIdentities
PeerFreeData
PeerGetItemCount
PeerGetNextItem
PeerGroupAddRecord
PeerGroupClose
PeerGroupCloseDirectConnection
PeerGroupConnect
PeerGroupConnectByAddress
PeerGroupCreate
PeerGroupCreateInvitation
PeerGroupCreatePasswordInvitation
PeerGroupDelete
PeerGroupDeleteRecord
PeerGroupEnumConnections
PeerGroupEnumMembers
PeerGroupEnumRecords
PeerGroupExportConfig
PeerGroupExportDatabase
PeerGroupGetEventData
PeerGroupGetProperties
PeerGroupGetRecord
PeerGroupGetStatus
PeerGroupImportConfig
PeerGroupImportDatabase
PeerGroupIssueCredentials
PeerGroupJoin
PeerGroupOpen
PeerGroupOpenDirectConnection
PeerGroupParseInvitation
PeerGroupPasswordJoin
PeerGroupPeerTimeToUniversalTime
PeerGroupRegisterEvent
PeerGroupResumePasswordAuthentication
PeerGroupSearchRecords
PeerGroupSendData
PeerGroupSetProperties
PeerGroupShutdown
PeerGroupStartup
PeerGroupUniversalTimeToPeerTime
PeerGroupUnregisterEvent
PeerGroupUpdateRecord
PeerHostNameToPeerName
PeerIdentityCreate
PeerIdentityDelete
PeerIdentityExport
PeerIdentityGetCert
PeerIdentityGetCryptKey
PeerIdentityGetDefault
PeerIdentityGetFriendlyName
PeerIdentityGetXML
PeerIdentityImport
PeerIdentitySetFriendlyName
PeerNameToPeerHostName
PeerPnrpEndResolve
PeerPnrpGetCloudInfo
PeerPnrpGetEndpoint
PeerPnrpRegister
PeerPnrpResolve
PeerPnrpShutdown
PeerPnrpStartResolve
PeerPnrpStartup
PeerPnrpUnregister
PeerPnrpUpdateRegistration
PeerSSPAddCredentials
PeerSSPRemoveCredentials
