;
; Definition file of USP10.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "USP10.dll"
EXPORTS
LpkPresent
ScriptApplyDigitSubstitution
ScriptApplyLogicalWidth
ScriptBreak
ScriptCPtoX
ScriptCacheGetHeight
ScriptFreeCache
ScriptGetCMap
ScriptGetFontAlternateGlyphs
ScriptGetFontFeatureTags
ScriptGetFontLanguageTags
ScriptGetFontProperties
ScriptGetFontScriptTags
ScriptGetGlyphABCWidth
ScriptGetLogicalWidths
ScriptGetProperties
ScriptIsComplex
ScriptItemize
ScriptItemizeOpenType
ScriptJustify
ScriptLayout
ScriptPlace
ScriptPlaceOpenType
ScriptPositionSingleGlyph
ScriptRecordDigitSubstitution
ScriptShape
ScriptShapeOpenType
ScriptStringAnalyse
ScriptStringCPtoX
ScriptStringFree
ScriptStringGet<PERSON>idths
ScriptStringGetOrder
ScriptStringOut
ScriptStringValidate
ScriptStringXtoCP
ScriptString_pLogAttr
ScriptString_pSize
ScriptString_pcOutChars
ScriptSubstituteSingleGlyph
ScriptTextOut
ScriptXtoCP
UspAllocCache
UspAllocTemp
UspFreeMem
