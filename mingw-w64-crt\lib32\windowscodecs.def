LIBRARY "windowscodecs.dll"
EXPORTS
IEnumString_Next_WIC_Proxy@16
IEnumString_Reset_WIC_Proxy@4
IPropertyBag2_Write_Proxy@16
IWICBitmapClipper_Initialize_Proxy@12
IWICBitmapCodecInfo_DoesSupportAnimation_Proxy@8
IWICBitmapCodecInfo_DoesSupportLossless_Proxy@8
IWICBitmapCodecInfo_DoesSupportMultiframe_Proxy@8
IWICBitmapCodecInfo_GetContainerFormat_Proxy@8
IWICBitmapCodecInfo_GetDeviceManufacturer_Proxy@16
IWICBitmapCodecInfo_GetDeviceModels_Proxy@16
IWICBitmapCodecInfo_GetFileExtensions_Proxy@16
IWICBitmapCodecInfo_GetMimeTypes_Proxy@16
IWICBitmapDecoder_CopyPalette_Proxy@8
IWICBitmapDecoder_GetColorContexts_Proxy@16
IWICBitmapDecoder_GetDecoderInfo_Proxy@8
IWICBitmapDecoder_GetFrameCount_Proxy@8
IWICBitmapDecoder_GetFrame_Proxy@12
IWICBitmapDecoder_GetMetadataQueryReader_Proxy@8
IWICBitmapDecoder_GetPreview_Proxy@8
IWICBitmapDecoder_GetThumbnail_Proxy@8
IWICBitmapEncoder_Commit_Proxy@4
IWICBitmapEncoder_CreateNewFrame_Proxy@12
IWICBitmapEncoder_GetEncoderInfo_Proxy@8
IWICBitmapEncoder_GetMetadataQueryWriter_Proxy@8
IWICBitmapEncoder_Initialize_Proxy@12
IWICBitmapEncoder_SetPalette_Proxy@8
IWICBitmapEncoder_SetThumbnail_Proxy@8
IWICBitmapFlipRotator_Initialize_Proxy@12
IWICBitmapFrameDecode_GetColorContexts_Proxy@16
IWICBitmapFrameDecode_GetMetadataQueryReader_Proxy@8
IWICBitmapFrameDecode_GetThumbnail_Proxy@8
IWICBitmapFrameEncode_Commit_Proxy@4
IWICBitmapFrameEncode_GetMetadataQueryWriter_Proxy@8
IWICBitmapFrameEncode_Initialize_Proxy@8
IWICBitmapFrameEncode_SetColorContexts_Proxy@12
IWICBitmapFrameEncode_SetResolution_Proxy@20
IWICBitmapFrameEncode_SetSize_Proxy@12
IWICBitmapFrameEncode_SetThumbnail_Proxy@8
IWICBitmapFrameEncode_WriteSource_Proxy@12
IWICBitmapLock_GetDataPointer_STA_Proxy@12
IWICBitmapLock_GetStride_Proxy@8
IWICBitmapScaler_Initialize_Proxy@20
IWICBitmapSource_CopyPalette_Proxy@8
IWICBitmapSource_CopyPixels_Proxy@20
IWICBitmapSource_GetPixelFormat_Proxy@8
IWICBitmapSource_GetResolution_Proxy@12
IWICBitmapSource_GetSize_Proxy@12
IWICBitmap_Lock_Proxy@16
IWICBitmap_SetPalette_Proxy@8
IWICBitmap_SetResolution_Proxy@20
IWICColorContext_InitializeFromMemory_Proxy@12
IWICComponentFactory_CreateMetadataWriterFromReader_Proxy@16
IWICComponentFactory_CreateQueryWriterFromBlockWriter_Proxy@12
IWICComponentInfo_GetAuthor_Proxy@16
IWICComponentInfo_GetCLSID_Proxy@8
IWICComponentInfo_GetFriendlyName_Proxy@16
IWICComponentInfo_GetSpecVersion_Proxy@16
IWICComponentInfo_GetVersion_Proxy@16
IWICFastMetadataEncoder_Commit_Proxy@4
IWICFastMetadataEncoder_GetMetadataQueryWriter_Proxy@8
IWICFormatConverter_Initialize_Proxy@32
IWICImagingFactory_CreateBitmapClipper_Proxy@8
IWICImagingFactory_CreateBitmapFlipRotator_Proxy@8
IWICImagingFactory_CreateBitmapFromHBITMAP_Proxy@20
IWICImagingFactory_CreateBitmapFromHICON_Proxy@12
IWICImagingFactory_CreateBitmapFromMemory_Proxy@32
IWICImagingFactory_CreateBitmapFromSource_Proxy@16
IWICImagingFactory_CreateBitmapScaler_Proxy@8
IWICImagingFactory_CreateBitmap_Proxy@24
IWICImagingFactory_CreateComponentInfo_Proxy@12
IWICImagingFactory_CreateDecoderFromFileHandle_Proxy@20
IWICImagingFactory_CreateDecoderFromFilename_Proxy@24
IWICImagingFactory_CreateDecoderFromStream_Proxy@20
IWICImagingFactory_CreateEncoder_Proxy@16
IWICImagingFactory_CreateFastMetadataEncoderFromDecoder_Proxy@12
IWICImagingFactory_CreateFastMetadataEncoderFromFrameDecode_Proxy@12
IWICImagingFactory_CreateFormatConverter_Proxy@8
IWICImagingFactory_CreatePalette_Proxy@8
IWICImagingFactory_CreateQueryWriterFromReader_Proxy@16
IWICImagingFactory_CreateQueryWriter_Proxy@16
IWICImagingFactory_CreateStream_Proxy@8
IWICMetadataBlockReader_GetCount_Proxy@8
IWICMetadataBlockReader_GetReaderByIndex_Proxy@12
IWICMetadataQueryReader_GetContainerFormat_Proxy@8
IWICMetadataQueryReader_GetEnumerator_Proxy@8
IWICMetadataQueryReader_GetLocation_Proxy@16
IWICMetadataQueryReader_GetMetadataByName_Proxy@12
IWICMetadataQueryWriter_RemoveMetadataByName_Proxy@8
IWICMetadataQueryWriter_SetMetadataByName_Proxy@12
IWICPalette_GetColorCount_Proxy@8
IWICPalette_GetColors_Proxy@16
IWICPalette_GetType_Proxy@8
IWICPalette_HasAlpha_Proxy@8
IWICPalette_InitializeCustom_Proxy@12
IWICPalette_InitializeFromBitmap_Proxy@16
IWICPalette_InitializeFromPalette_Proxy@8
IWICPalette_InitializePredefined_Proxy@12
IWICPixelFormatInfo_GetBitsPerPixel_Proxy@8
IWICPixelFormatInfo_GetChannelCount_Proxy@8
IWICPixelFormatInfo_GetChannelMask_Proxy@20
IWICStream_InitializeFromIStream_Proxy@8
IWICStream_InitializeFromMemory_Proxy@12
WICConvertBitmapSource@12
WICCreateBitmapFromSection@28
WICCreateBitmapFromSectionEx@32
WICCreateColorContext_Proxy@8
WICCreateImagingFactory_Proxy@8
WICGetMetadataContentSize@12
WICMapGuidToShortName@16
WICMapSchemaToName@20
WICMapShortNameToGuid@8
WICMatchMetadataContent@16
WICSerializeMetadataContent@16
WICSetEncoderFormat_Proxy@16
