;
; Definition file of DBnetlib.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "DBnetlib.dll"
EXPORTS
ConnectionObjectSize
ConnectionRead
ConnectionWrite
ConnectionTransact
ConnectionWriteOOB
ConnectionMode
ConnectionStatus
ConnectionOpen
ConnectionClose
ConnectionCheckForData
ConnectionError
ConnectionVer
ConnectionSqlVer
ConnectionServerEnum
ConnectionServerEnumW
ConnectionOpenW
ConnectionErrorW
ConnectionOption
ConnectionGetSvrUser
InitEnumServers
GetNextEnumeration
CloseEnumServers
InitSSPIPackage
TermSSPIPackage
InitSession
TermSession
GenClientContext
ConnectionFlushCache
InitSessionEx
TermSessionEx
GenClientContextEx
