;
; Definition file of WINHTTP.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WINHTTP.dll"
EXPORTS
WinHttpPacJsWorkerMain@8
DllCanUnloadNow@0
DllGetClassObject@12
Private1@20
SvchostPushServiceGlobals@4
WinHttpAddRequestHeaders@16
WinHttpAddRequestHeadersEx@32
WinHttpAutoProxySvcMain@8
WinHttpCheckPlatform@0
WinHttpCloseHandle@4
WinHttpConnect@16
WinHttpConnectionDeletePolicyEntries@8
WinHttpConnectionDeleteProxyInfo@8
WinHttpConnectionFreeNameList@4
WinHttpConnectionFreeProxyInfo@4
WinHttpConnectionFreeProxyList@4
WinHttpConnectionGetNameList@4
WinHttpConnectionGetProxyInfo@12
WinHttpConnectionGetProxy<PERSON>ist@8
WinH<PERSON><PERSON><PERSON>onnectionSetPolicyEntries@12
WinHttpConnectionSetProxyInfo@12
WinHttpConnectionUpdateIfIndexTable@8
WinHttpCrackUrl@16
WinHttpCreateProxyResolver@8
WinHttpCreateUrl@16
WinHttpDetectAutoProxyConfigUrl@8
WinHttpFreeProxyResult@4
WinHttpFreeProxyResultEx@4
WinHttpFreeProxySettings@4
WinHttpGetDefaultProxyConfiguration@4
WinHttpGetIEProxyConfigForCurrentUser@4
WinHttpGetProxyForUrl@16
WinHttpGetProxyForUrlEx2@24
WinHttpGetProxyForUrlEx@16
WinHttpGetProxyForUrlHvsi@36
WinHttpGetProxyResult@8
WinHttpGetProxyResultEx@8
WinHttpGetProxySettingsVersion@8
WinHttpGetTunnelSocket@16
WinHttpOpen@20
WinHttpOpenRequest@28
WinHttpProbeConnectivity@24
WinHttpQueryAuthSchemes@16
WinHttpQueryDataAvailable@8
WinHttpQueryHeaders@24
WinHttpQueryOption@16
WinHttpReadData@16
WinHttpReadProxySettings@28
WinHttpReadProxySettingsHvsi@32
WinHttpReceiveResponse@8
WinHttpResetAutoProxy@8
WinHttpSaveProxyCredentials@16
WinHttpSendRequest@28
WinHttpSetCredentials@24
WinHttpSetDefaultProxyConfiguration@4
WinHttpSetOption@16
WinHttpSetProxySettingsPerUser@4
WinHttpSetStatusCallback@16
WinHttpSetTimeouts@20
WinHttpTimeFromSystemTime@8
WinHttpTimeToSystemTime@8
WinHttpWebSocketClose@16
WinHttpWebSocketCompleteUpgrade@8
WinHttpWebSocketQueryCloseStatus@20
WinHttpWebSocketReceive@20
WinHttpWebSocketSend@16
WinHttpWebSocketShutdown@16
WinHttpWriteData@16
WinHttpWriteProxySettings@12
