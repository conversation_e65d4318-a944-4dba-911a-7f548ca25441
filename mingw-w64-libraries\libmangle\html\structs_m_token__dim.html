<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: sMToken_dim Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>sMToken_dim Struct Reference</h1><!-- doxytag: class="sMToken_dim" -->
<p><code>#include &lt;<a class="el" href="m__token_8h_source.html">m_token.h</a>&gt;</code></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__base.html">sMToken_base</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__dim.html#a103ae0105ea54cb4e42960cad17e75b0">base</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">union <a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__dim.html#a5c16c40c478e326a93952cf620b2f99e">value</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">union <a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__dim.html#ac1f77b90776014a1ea3f1920dd00637d">non_tt_param</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__dim.html#a65b1c85b42d9b7870dde3b3bcfa067a3">beNegate</a></td></tr>
</table>
<hr/><a name="_details"></a><h2>Detailed Description</h2>
<p>"dim" token. Contains array-like expressions in decoded names. </p>
<hr/><h2>Field Documentation</h2>
<a class="anchor" id="a103ae0105ea54cb4e42960cad17e75b0"></a><!-- doxytag: member="sMToken_dim::base" ref="a103ae0105ea54cb4e42960cad17e75b0" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__base.html">sMToken_base</a> <a class="el" href="structs_m_token__dim.html#a103ae0105ea54cb4e42960cad17e75b0">sMToken_dim::base</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Base descriptor header. </p>

</div>
</div>
<a class="anchor" id="a65b1c85b42d9b7870dde3b3bcfa067a3"></a><!-- doxytag: member="sMToken_dim::beNegate" ref="a65b1c85b42d9b7870dde3b3bcfa067a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="structs_m_token__dim.html#a65b1c85b42d9b7870dde3b3bcfa067a3">sMToken_dim::beNegate</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>1 for negative "values". </p>

</div>
</div>
<a class="anchor" id="ac1f77b90776014a1ea3f1920dd00637d"></a><!-- doxytag: member="sMToken_dim::non_tt_param" ref="ac1f77b90776014a1ea3f1920dd00637d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union <a class="el" href="unionu_m_token.html">uMToken</a>* <a class="el" href="structs_m_token__dim.html#ac1f77b90776014a1ea3f1920dd00637d">sMToken_dim::non_tt_param</a><code> [write]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Pointer to C++ template name token. </p>

</div>
</div>
<a class="anchor" id="a5c16c40c478e326a93952cf620b2f99e"></a><!-- doxytag: member="sMToken_dim::value" ref="a5c16c40c478e326a93952cf620b2f99e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union <a class="el" href="unionu_m_token.html">uMToken</a>* <a class="el" href="structs_m_token__dim.html#a5c16c40c478e326a93952cf620b2f99e">sMToken_dim::value</a><code> [write]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Pointer to value token. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="m__token_8h_source.html">m_token.h</a></li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
