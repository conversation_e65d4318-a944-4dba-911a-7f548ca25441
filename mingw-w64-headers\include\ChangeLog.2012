2012-12-12  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* winnt.h: Drop inclusion of mmintrin.h, emmintrin.h, and
	pmmintrin.h on Cygwin.

2012-12-07  <PERSON>  <<EMAIL>>

	* winbase.h (LOAD_LIBRARY_AS_IMAGE_RESOURCE): Fix typo.

2012-11-14  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* winternl.h (struct _FILE_FULL_DIR_INFORMATION): Rename from
	_FILE_FULL_DIRECTORY_INFORMATION per WDK documentation.
	(struct _FILE_ID_FULL_DIR_INFORMATION): Rename from
	_FILE_ID_FULL_DIRECTORY_INFORMATION.
	(struct _FILE_BOTH_DIR_INFORMATION): Rename from
	_FILE_BOTH_DIRECTORY_INFORMATION.
	(struct _FILE_ID_BOTH_DIR_INFORMATION): Rename from
	_FILE_ID_BOTH_DIRECTORY_INFORMATION.
	(FILE_FULL_DIRECTORY_INFORMATION, PFILE_FULL_DIRECTORY_INFORMATION,
	FILE_ID_FULL_DIRECTORY_INFORMATION, PFILE_ID_FULL_DIRECTORY_INFORMATION,
	FILE_BOTH_DIRECTORY_INFORMATION, PFILE_BOTH_DIRECTORY_INFORMATION,
	FILE_ID_BOTH_DIRECTORY_INFORMATION,
	PFILE_ID_BOTH_DIRECTORY_INFORMATION): Explicitely typedef in a new
	block.  Add comment to describe as being outdated.

2012-11-13  Corinna Vinschen  <<EMAIL>>

	* winternl.h (SYSTEM_BASIC_INFORMATION): Change type of address
	members to match 64 bit systems.
	(SYSTEM_PAGEFILE_INFORMATION): New type.
	(SYSTEM_INFORMATION_CLASS): Reformat for readability.  Add
	SystemPagefileInformation.

2012-10-11  Corinna Vinschen  <<EMAIL>>

	* winioctl.h (DEVICE_TYPE): Define only if not already defined.
	* winternl.h (DEVICE_TYPE): Define as ULONG if not already defined.

2012-10-11  Corinna Vinschen  <<EMAIL>>

	* winternl.h: Throughout, use NTAPI instead of WINAPI.
	(enum _FSINFOCLASS): Define.
	(struct _FILE_FS_VOLUME_INFORMATION): Define.
	(struct _FILE_FS_LABEL_INFORMATION): Define.
	(struct _FILE_FS_SIZE_INFORMATION): Define.
	(struct _FILE_FS_DEVICE_INFORMATION): Define.
	(struct _FILE_FS_ATTRIBUTE_INFORMATION): Define.
	(struct _FILE_FS_FULL_SIZE_INFORMATION): Define.
	(struct _FILE_FS_OBJECTID_INFORMATION): Define.
	(NtFsControlFile): Declare.
	(NtQueryVolumeInformationFile): Declare.
	(NtSetInformationFile): Declare.
	(NtSetVolumeInformationFile): Declare.
	(RtlDosPathNameToNtPathName_U): Declare.


2012-09-20  Kai Tietz  <<EMAIL>>

	PR 3561209
	* wincrypt.h (szOID_NIST_AES128_CBC): New.
	(szOID_NIST_AES192_CBC): Likewise.
	(szOID_NIST_AES256_CBC): Likewise.

2012-08-29  Yaakov Selkowitz  <<EMAIL>>

	* ntdef.h (_OBJECT_ATTRIBUTES): Guard against duplicate declaration.
	* winnt.h (DECLSPEC_IMPORT): Do not redefine.
	(FILE_SUPERSEDE...FILE_MAXIMUM_DISPOSITION): Copy from ntdef.h.
	(FILE_DIRECTORY_FILE...FILE_OPEN_FOR_FREE_SPACE_QUERY): Ditto.
	(FILE_SHARE_VALID_FLAGS): Ditto.
	* winternl.h (_UNICODE_STRING): Guard against duplicate declaration.
	(_STRING): Ditto.
	(_OBJECT_ATTRIBUTES): Ditto.
	(_FILE_LINK_INFORMATION): Copy from ddk/ntifs.h.

2012-08-29  Yaakov Selkowitz  <<EMAIL>>

	* winternl.h (_FILE_RENAME_INFORMATION): Fix member names per MSDN.
	(NtQueryInformationFile): Change type of len to ULONG per MSDN.

2012-08-29  Ozkan Sezer  <<EMAIL>>

	PR/3561800, patch by drangon zhou:
	* mshtml.h (styleNormal): Add missing closing brace.
	(ELEMENTNAMESPACE_FLAGS): Ditto.

2012-08-14  Kai Tietz  <<EMAIL>>

	* windef.h: Add include of _mingw.h for __LOMG32.

2012-08-07  Kai Tietz  <<EMAIL>>

	* winnt.h (InterlockedIncrement): Add prototype before inline
	declaration.
	(InterlockedDecrement): Likewise.
	(InterlockedExchange): Likewise.

2012-08-06  Corinna Vinschen  <<EMAIL>>

	* strsafe.h: Change long to __LONG32 where appropriate.

2012-08-06  Corinna Vinschen  <<EMAIL>>

	* wbemdisp.h: Change long to __LONG32 where appropriate.
	* windef.h: Ditto.
	* winerror.h: Ditto.
	* wingdi.h: Ditto.
	* wininet.h: Ditto.
	* winnt.h: Ditto.
	* winsmcrd.h: Ditto.
	* winsnmp.h: Ditto.
	* winsock.h: Ditto.
	* winsock2.h: Ditto.
	* winuser.h: Ditto.
	* wmiatlprov.h: Ditto.
	* wmiutils.h: Ditto.
	* ws2spi.h: Ditto.
	* xa.h: Ditto.
	* xcmc.h: Ditto.
	* xmltrnsf.h: Ditto.

2012-08-06  Corinna Vinschen  <<EMAIL>>

	* mlang.h: Change long to __LONG32 where appropriate.
	* mtsadmin.h: Ditto.
	* mtxadmin.h: Ditto.
	* shldisp.h: Ditto.
	* shlobj.h: Ditto.
	* simpdc.h: Ditto.
	* sqltypes.h: Ditto.
	* srv.h: Ditto.
	* sspi.h: Ditto.
	* svrapi.h: Ditto.
	* tapi.h: Ditto.
	* tapi3.h: Ditto.
	* tapi3cc.h: Ditto.
	* tapi3if.h: Ditto.
	* termmgr.h: Ditto.
	* tom.h: Ditto.
	* tspi.h: Ditto.
	* tuner.h: Ditto.
	* txdtc.h: Ditto.
	* usp10.h: Ditto.
	* vfw.h: Ditto.
	* wabcode.h: Ditto.
	* wabdefs.h: Ditto.
	* wbemcli.h: Ditto.
	* wbemprov.h: Ditto.
	* wbemtran.h: Ditto.

2012-08-06  Corinna Vinschen  <<EMAIL>>

	* rtccore.h: Change long to __LONG32 where appropriate.
	* scardssp.h: Ditto.
	* sdoias.h: Ditto.

2012-08-06  Corinna Vinschen  <<EMAIL>>

	* mdhcp.h: Change long to __LONG32 where appropriate.
	* mergemod.h: Ditto.
	* mmc.h: Ditto.
	* mmcobj.h: Ditto.
	* msclus.h: Ditto.
	* mshtml.h: Ditto.
	* mshtmlc.h: Ditto.
	* msdatsrc.h: Ditto.
	* msi.h: Ditto.
	* msimcntl.h: Ditto.
	* msimcsdk.h: Ditto.
	* msp.h: Ditto.
	* mspaddr.h: Ditto.
	* mspcall.h: Ditto.
	* mspcoll.h: Ditto.
	* mspstrm.h: Ditto.
	* mspterm.h: Ditto.
	* msptrmac.h: Ditto.
	* msptrmar.h: Ditto.
	* msxml.h: Ditto.
	* msxml2.h: Ditto.
	* ndattrib.h: Ditto.
	* ndhelper.h: Ditto.
	* ndrtypes.h: Ditto.
	* netcon.h: Ditto.
	* netmon.h: Ditto.
	* nspapi.h: Ditto.
	* ntdef.h: Ditto.
	* odbcss.h: Ditto.
	* oleauto.h: Ditto.
	* olectl.h: Ditto.
	* oledbguid.h: Ditto.
	* rdpencomapi.h: Ditto.
	* rend.h: Ditto.
	* richedit.h: Ditto.
	* rpc.h: Ditto.
	* rpcasync.h: Ditto.
	* rpcdce.h: Ditto.
	* rpcdcep.h: Ditto.
	* rpcndr.h: Ditto.
	* rpcnsi.h: Ditto.
	* rpcproxy.h: Ditto.

2012-08-03  Corinna Vinschen  <<EMAIL>>

	* iads.h: Change long to __LONG32 where appropriate.

2012-08-03  Corinna Vinschen  <<EMAIL>>

	* eventsys.h: Change long to __LONG32 where appropriate.
	* exdisp.h: Ditto.
	* gpmgmt.h: Ditto.
	* iiis.h: Ditto.
	* ipmsp.h: Ditto.

2012-08-03  Corinna Vinschen  <<EMAIL>>

	* comsvcs.h: Change long to __LONG32 where appropriate.
	* control.h: Ditto.
	* dhtmled.h: Ditto.
	* emostore.h: Ditto.

2012-08-03  Corinna Vinschen  <<EMAIL>>

	* cdoex.h: Change long to __LONG32 where appropriate.
	* cdoexm.h: Ditto.
	* cdonts.h: Ditto.
	* cdosys.h: Ditto.
	* cluscfgwizard.h: Ditto.
	* comadmin.h: Ditto.

2012-08-03  Corinna Vinschen  <<EMAIL>>

	* agtsvr.h: Change long to __LONG32 where appropriate.
	* asptlb.h: Ditto.
	* azroles.h: Ditto.

2012-08-02  Corinna Vinschen  <<EMAIL>>

	* activscp.h: Change long to __LONG32 where appropriate.
	* adoctint.h: Ditto.
	* adoint.h: Ditto.
	* adojet.h: Ditto.
	* adomd.h: Ditto.
	* agtctl.h: Ditto.

2012-08-02  Corinna Vinschen  <<EMAIL>>

	* fsrm.h: Change long to __LONG32 where appropriate.
	* fsrmquota.h: Ditto.
	* fsrmreports.h: Ditto.
	* fsrmscreen.h: Ditto.
	* guiddef.h: Ditto.
	* httpfilt.h: Ditto.
	* iimgctx.h: Ditto.
	* iiisext.h: Ditto.
	* ksmedia.h: Ditto.
	* ksproxy.h: Ditto.
	* lmsvc.h: Ditto.
	* mapi.h: Ditto.
	* mapicode.h: Ditto.
	* mapidbg.h: Ditto.
	* lmaccess.h: Ditto.
	* lmstats.h: Ditto.
	* mapidefs.h: Ditto.
	* mapinls.h: Ditto.
	* mfidl.h: Ditto.
	* midles.h: Ditto.
	* mq.h: Ditto.
	* mqoai.h: Ditto.
	* msasn1.h: Ditto.

2012-08-02  Corinna Vinschen  <<EMAIL>>

	* basetsd.h: Change long to __LONG32 where appropriate.
	* basetyps.h: Ditto.
	* certbase.h: Ditto.
	* correg.h: Ditto.
	* dbdaoint.h: Ditto.
	* esent.h: Ditto.
	* fci.h: Ditto.
	* fdi.h: Ditto.
	* filehc.h: Ditto.
	* winbase.h: Ditto.
	* comutil.h: Ditto.
	(_bstr_t::Data_t::AddRef): Call InterlockedIncrement with argument
	casted to LONG *.
	(_bstr_t::Data_t::Release): Call InterlockedDecrement with argument
	casted to LONG *.

2012-08-02  Corinna Vinschen  <<EMAIL>>

	* Throughout, change prototypes of XXX_UserSize, XXX_UserMarshal,
	XXX_UserUnmarshal, and XXX_UserFree functions to use ULONG rather
	than unsigned long.
	* rpcndr.h (NDR_CHAR_REP_MASK): Use __MSABI_LONG rather than
	explicit cast to unsigned long.
	(NDR_INT_REP_MASK): Ditto.
	(NDR_FLOAT_REP_MASK): Ditto.
	(NDR_LITTLE_ENDIAN): Ditto.
	(NDR_BIG_ENDIAN): Ditto.
	(NDR_IEEE_FLOAT): Ditto.
	(NDR_VAX_FLOAT): Ditto.
	(NDR_IBM_FLOAT): Ditto.
	(NDR_ASCII_CHAR): Ditto.
	(NDR_EBCDIC_CHAR): Ditto.
	(NDR_LOCAL_DATA_REPRESENTATION): Ditto.
	(USER_MARSHAL_SIZING_ROUTINE): Use ULONG rather than unsigned long.
	(USER_MARSHAL_MARSHALLING_ROUTINE): Ditto.
	(USER_MARSHAL_UNMARSHALLING_ROUTINE): Ditto.
	(USER_MARSHAL_FREEING_ROUTINE): Ditto.

2012-08-01  Corinna Vinschen  <<EMAIL>>

	* msxml.idl: Throughout, replace long and unsigned long with LONG and
	ULONG.
	* objidl.idl: Ditto.
	* ocidl.idl: Ditto.
	* oleidl.idl: Ditto.
	* propidl.idl: Ditto.
	* shobjidl.idl: Ditto.
	* strmif.idl: Ditto.
	* urlmon.idl: Ditto.
	* wtypes.idl: Ditto.
	* msxml.idl: Ditto.
	* objidl.h: Regenerate.
	* ocidl.h: Regenerate.
	* oleidl.h: Regenerate.
	* propidl.h: Regenerate.
	* shobjidl.h: Regenerate.
	* strmif.h: Regenerate.
	* urlmon.h: Regenerate.
	* wtypes.h: Regenerate.

2012-08-01  Corinna Vinschen  <<EMAIL>>

	* oleacc.idl: Throughout, replace long and unsigned long with LONG and
	ULONG.
	* oleacc.h: Regenerate.

2012-07-31  Corinna Vinschen  <<EMAIL>>

	* windows.h: Don't include stralign.h when build for Cygwin.

2012-07-30  Corinna Vinschen  <<EMAIL>>

	* psdk_inc/_ip_types.h (struct __ms_timeval): Define on LP64 systems.
	(TIMEVAL): Define based on struct __ms_timeval on LP64, based on
	struct timeval otherwise.
	(PTIMEVAL): Ditto.
	(LPTIMEVAL): Ditto.
	* winsock.h: Replace all `struct timeval *' usages with PTIMEVAL.
	* winsock2.h: Ditto.
	* ws2spi.h: Ditto.
	* ws2tcpip.h: Ditto.

2012-07-30  Corinna Vinschen  <<EMAIL>>

	* af_irda.h: Temporarily redefine u_long as __ms_u_long on LP64 systems.
	* in6addr.h: Ditto.
	* inaddr.h: Ditto.
	* winsock.h: Ditto.
	* winsock2.h: Ditto.
	* psdk_inc/_ip_types.h: Ditto.

2012-07-30  Corinna Vinschen  <<EMAIL>>

	* Throughout, use __MSABI_LONG macro rather than 'L' qualifier
	for unsigned numerical constants as well.

2012-07-30  Corinna Vinschen  <<EMAIL>>

	* winnt.h: In case of Cygwin, include gcc-provided intrinsic
	files directly, instead of including intrin.h.

2012-07-30  Corinna Vinschen  <<EMAIL>>

	* ndrtypes.h (NDR_VERSION_1_1): Drop UL qualifier from or'ed
	minor version value.
	(NDR_VERSION_2_0): Ditto.
	(NDR_VERSION_5_0): Ditto.
	(NDR_VERSION_5_2): Ditto.
	(NDR_VERSION_5_3): Ditto.
	(NDR_VERSION_5_4): Ditto.
	* d2d1.h (DXGI_FORMAT_FORCE_UINT): Drop UL qualifier.
	* gdiplus/gdipluscolor.h: Ditto for all ARGB symbolic color
	values.

2012-07-27  Corinna Vinschen  <<EMAIL>>

	* Throughout, use __MSABI_LONG macro rather than 'L' qualifier
	for numerical constants.

2012-07-27  Corinna Vinschen  <<EMAIL>>

	* cdoex.h: Throughout, drop 'L' qualifier from numerical constants
	defining const value.
	* cdosys.h: Ditto.
	* lmaccess.h: Drop 'L' qualifier from numerical constants casted to
	unsigned long.
	* lmstats.h: Ditto.
	* richole.h: Drop 'L' qualifier from numerical constants casted to
	ULONG.
	* dbgeng.h: Drop 'L' qualifier from numerical constants casted to
	HRESULT (via HRESULT_FROM_NT).
	* adserr.h: Throughout, drop 'L' qualifier from numerical
	constants casted to HRESULT (via _HRESULT_TYPEDEF_).
	* advpub.h: Ditto.
	* naperror.h: Ditto.
	* pstore.h: Ditto.
	* subsmgr.h: Ditto.
	* urlmon.h: Ditto.
	* winerror.h: Ditto.
	Define all WSA error codes based on WSABASEERR.
	* netmon.h (MAKE_LONG): Drop unnecessary 'L' qualifier from shift
	value.
	* pdh.h (IsSuccessSeverity): Drop 'L' qualifier from mask value.
	Cast result value to DWORD.
	(IsInformationalSeverity): Ditto.
	(IsWarningSeverity): Ditto.
	(IsErrorSeverity): Ditto.
	* penwin.h (dwDiffAT): Drop unnecessary 'L' qualifier from multiplier.
	* windowsx.h (HANDLE_WM_CREATE): Drop 'L' qualifer from
	constant casted to LRESULT and only cast the endresult once.
	* wingdi.h (HGDI_ERROR): Drop 'L' qualifer.
	* docobj.h (PAGESET_TOLASTPAGE): Ditto.

2012-07-25  Corinna Vinschen  <<EMAIL>>

	* wmiatlprov.h (CInstanceProviderHelper::CheckInstancePath):
	Cast first parameter in call to GetInfo to ULONG.
	* winioctl.h (IsDsmActionNonDestructive): Replace test for == 0L
	with logical negation operator.
	* wininet.h (INTERNET_INVALID_STATUS_CALLBACK): Drop 'L' modifier.
	* lmaccess.h (DEF_MIN_PWAGE): Ditto.
	* mmsystem.h (MEVT_EVENTPARM): Ditto.
	* svrapi.h (FRK_INIT):  Ditto.
	* vfw.h: Throughout, cast all constants in calls to SendMessage and
	ICSendMessage to the expected type.
	* prsht.h: Ditto, in calls to SendMessage and PostMessage.
	* winuser.h: Ditto, in calls to CreateWindowEx, CreateDialogParam,
	CreateDialogIndirectParam, DialogBoxParam, and DialogBoxIndirectParam.
	* commctrl.h: Ditto, in calls to SendMessage.
	(LPSTR_TEXTCALLBACKA): Drop 'L' modifier.
	(LPSTR_TEXTCALLBACKW): Ditto.
	* windowsx.h: Throughout, cast all constants in calls to SendMessage
	and as return value from HANDLE_xxx macro.
	(IsRestored): Replace test for == 0L with logical negation operator.

2012-07-23  Corinna Vinschen  <<EMAIL>>

	* Throughout, drop 'L' modifier from enumeration values.

2012-07-21  Corinna Vinschen  <<EMAIL>>

	* ws2tcpip.h (EAI_NODATA): Define as int value.

2012-07-20  Corinna Vinschen  <<EMAIL>>

	* kcom.h (STATIC_KoCreateObject): Drop  'L' modifier from Data1
	member in GUID definition.

2012-07-20  Corinna Vinschen  <<EMAIL>>

	* directx/dxerr8.h (DXTRACE_MSG): Drop long qualifier from
	empty non-debug definition.
	* directx/dxerr9.h: Ditto.

2012-07-20  Corinna Vinschen  <<EMAIL>>

	* winuser.h: Convert all HELP_xxx command macros to int macros.

2012-07-19  Corinna Vinschen  <<EMAIL>>

	* ksmedia.h: Throughout, drop 'L' modifier from Data1 member in
	GUID definitions.
	* ksproxy.h: Ditto.
	* ksuuids.h: Ditto.
	* uuids.h: Ditto.

2012-07-19  Corinna Vinschen  <<EMAIL>>

	* msdasql.h: Throughout, drop 'L' modifier from Data1 member in
	GUID definitions.
	* ioevent.h: Ditto.
	* msdaguid.h: Ditto.
	* iiis.h: Ditto.
	* usbiodef.h: Ditto.
	* tcguid.h: Ditto.
	* oledb.h: Ditto.
	* recguids.h: Ditto.
	* diskguid.h: Ditto.
	* shlguid.h: Ditto.
	* msdadc.h: Ditto.
	* isguids.h: Ditto.
	* batclass.h: Ditto.
	* shobjidl.h: Ditto.
	* devguid.h: Ditto.
	* ntddscsi.h: Ditto.
	* identitystore.h: Ditto.
	* sti.h: Ditto.
	* sqloledb.h: Ditto.
	* mstask.h: Ditto.
	* fwpmu.h: Ditto.
	* oledbguid.h: Ditto.
	* winioctl.h: Ditto.

2012-07-19  Corinna Vinschen  <<EMAIL>>

	* Throughout, drop long qualifier in all casted numerical constant
	expressions.

2012-07-19  Corinna Vinschen  <<EMAIL>>

	* ks.h: Throughout, drop 'L' modifier from Data1 member in GUID
	definitions.
	* ntddstor.h: Ditto.
	* ntddser.h: Ditto.

2012-07-19  Corinna Vinschen  <<EMAIL>>

	* usb.h: Check for _WIN64 instead of WIN64.

2012-07-16  Corinna Vinschen  <<EMAIL>>

	* winbase.h: Use system types in calls to Interlocked functions.

2012-07-12  Corinna Vinschen  <<EMAIL>>

	* winternl.h (NT_SUCCESS): Define.
	(enum _PROCESSINFOCLASS): Copy from ddk/ntddk.h.
	(NtSetInformationProcess): Declare.

2012-07-11  Ozkan Sezer  <<EMAIL>>

	* tapi.h: Move the unicode A/W macros after the function prototypes.
	(lineAddProvider,lineBlindTransfer,lineConfigDialog,
	lineConfigDialogEdit,lineDial,lineGatherDigits,lineGenerateDigits,
	lineGetAddressID,lineGetAppPriority,lineGetDevConfig,lineGetIcon,
	lineGetID,lineHandoff,lineMakeCall,linePark,linePickup,lineRedirect,
	lineSetAppPriority,lineSetDevConfig,lineSetTollList,
	lineTranslateAddress,lineTranslateDialog,lineUnpark,phoneConfigDialog,
	phoneGetIcon,phoneGetID,tapiGetLocationInfo,tapiRequestMakeCall,
	tapiRequestMediaCall): Define as *A variant only for TAPI2 or newer,
	but always define as *W variant when UNICODE is defined.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* stralign.h (ua_wcscpy): Define as macro when building on Cygwin.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* in6addr.h (s6_addr16): Define when building Cygwin.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* winbase.h (ZAWPROXYAPI): Define as empty if _ZAWPROXY_ is defined.
	* objbase.h (WINOLEAPI): Define as empty if _OLE32_ is defined.
	WINOLEAPI_: Ditto.
	* winnt.h (NTSYSAPI): Define as empty if _NTSYSTEM_ is defined.
	(NTSYSCALLAPI): Ditto.
	* winuser.h (WINUSERAPI): Define as empty if _USER32_ is defined.
	* wingdi.h (WINSPOOLAPI): Define as empty if _SPOOL32_ is defined.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* windows.h: Don't include winsock.h on Cygwin, unless __USE_W32_SOCKETS
	is defined.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* psdk_inc/_ip_types.h (struct sockaddr): Define always.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* ntdef.h (struct _PROCESSOR_NUMBER): Guard definition with
	___PROCESSOR_NUMBER_DEFINED.
	(struct _GROUP_AFFINITY): Guard definition with
	___GROUP_AFFINITY_DEFINED.
	* winnt.h: Ditto.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* in6addr.h (struct in6addr): Add u.__s6_addr32 member when
	building Cygwin.
	(s6_addr32): Define when building Cygwin.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* winsock2.h (gethostname): Drop __INSIDE_CYGWIN__ guard.

2012-07-06  Corinna Vinschen  <<EMAIL>>

	* winnt.h (NtCurrentTeb): Always define inline implementation.
	(GetCurrentFiber): Ditto.
	(GetFiberData): Ditto.
	(MemoryBarrier): Ditto.

2012-07-05  Kai Tietz  <<EMAIL>>

	* ws2tcpip.h: Enable inline-functions always.

2012-06-28  Corinna Vinschen  <<EMAIL>>

	* wincrypt.h (WINBASEAPI): Define as non-import iff
	_KERNEL32_ was defined.

2012-06-28  Corinna Vinschen  <<EMAIL>>

	* psdk_inc/_ip_types.h (sockaddr_in): Define unconditionally.

2012-06-28  Corinna Vinschen  <<EMAIL>>

	* ntdef.h (FILE_ATTRIBUTE_VALID_FLAGS): Define.
	(FILE_SHARE_VALID_FLAGS): Define.
	(FILE_SUPERSEDE, ...): Define native file creation dispositions.
	(FILE_DIRECTORY_FILE, ...): Define native file open options.
	(struct _REPARSE_DATA_BUFFER): Define.

2012-06-28  Corinna Vinschen  <<EMAIL>>

	* winsock2.h (__WSAFDIsSet): Move declaration to psdk_inv/_fd_type.h.
	(FD_CLR): Move definition to psdk_inv/_fd_type.h.
	(FD_ZERO): Ditto.
	(FD_ISSET): Ditto.
	(FD_SET): Ditto.
	(htonl): Don't declare when building Cygwin.
	(htons): Ditto.
	(ntohl): Ditto.
	(ntohs): Ditto.
	(gethostname): Ditto.
	(select): Ditto.
	* winsock.h: Ditto.
	* psdk_inc/_fd_types.h: Add Cygwin build environment magic to avoid
	multiple definition of the select function related datatypes and macros.
	(__WSAFDIsSet): Declare here.
	(FD_CLR): Define here.
	(FD_ZERO): Ditto.
	(FD_ISSET): Ditto.
	(FD_SET): Ditto.  Make sure to undef FD_SET first, if it has been
	defined from winsock.h before.
	* psdk_inc/_ip_types.h: Rearrange slightly and don't define POSIX
	compatible datatypes when building Cygwin.
	* psdk_inc/_ws1_undef.h (FD_CLR): Drop #undef.
	(FD_ZERO): Ditto.
	(FD_ISSET): Ditto.
	(FD_SET): Ditto.

2012-06-28  Rafael Carre  <<EMAIL>>

	* dxva2api.h: Remove DXVA_* GUIDs and structs ...
	* dxva.h: ... And put them here.

2012-06-28  Corinna Vinschen  <<EMAIL>>

	* ntdef.h (PHYSICAL_ADDRESS): Define unconditionally.

2012-06-27  Corinna Vinschen  <<EMAIL>>

	* stralign.h: Fix and add #endif comments.

2012-06-27  Kai Tietz  <<EMAIL>>

	* wincrypt.h (WINADVAPI): Define as non-import iff
	_ADVAPI32_ was defined.
	* wincred.h: Likewise.
	* winsvc.h: Likewise.
	* perflib.h: Likewise.
	* winbase.h: Likewise.

2012-05-10  Kai Tietz  <<EMAIL>>

	PR 3523077
	* tmschema.h: Add warning about obsoleted tmschema.h for
	_WIN32_NT > 0x600.

2012-05-10  Piotr Caban  <<EMAIL>>

	* vsstyle.h: Wine-version relicensed.
	* vssym32.h: Likewise.

2012-05-09  Ozkan Sezer  <<EMAIL>>

	* winuser.h (MAPVK_VK_TO_*): Moved map types for MapVirtualKeyEx
	out of win7 ifdefs. Protected MAPVK_VK_TO_VSC_EX for vista-only.

2012-05-08  Ozkan Sezer  <<EMAIL>>

	* uxtheme.h (MAX_INTLIST_COUNT): Define as 402 instead of 10 for
	vista and newer. (bug #3524667).

2012-05-07  Ozkan Sezer  <<EMAIL>>

	* dwmapi.h: Fixed all function prototypes to be WINAPI.

2012-05-02  Kai Tietz  <<EMAIL>>

	PR 3523072
	* commctrl.h:  Add missing button-styles, and messages.

2012-04-28  Jonathan Liu  <<EMAIL>>

	* wlanapi.h: Fixed incorrectly nested if blocks.

2012-04-23  Stefan Sundin  <<EMAIL>>

	* Fix incorrect references for pVtbl to lpVtbl in these files:
	audioengineendpoint.h, bdaiface.h, dvbsiparser.h, dwrite.h, dxvahd.h,
	evr.h, fsrm.h, fsrmquota.h, fsrmreports.h, fsrmscreen.h,
	functiondiscoveryapi.h, functiondiscoverynotification.h,
	identitystore.h, locationapi.h, mfapi.h, mfidl.h, mfobjects.h,
	mfplay.h, mfreadwrite.h, mftransform.h, mpeg2data.h, mpeg2psiparser.h,
	msrdc.h, ndhelper.h, opmapi.h, portabledeviceconnectapi.h,
	rdpencomapi.h, strmif.h, strmif.idl, tuner.h, vsadmin.h, vsbackup.h,
	vsmgmt.h, vsprov.h, vss.h, vswriter.h, winsync.h, wmcontainer.h,
	wsdattachment.h, wsdbase.h, wsdclient.h, wsddisco.h, wsdhost.h,
	wsdxml.h.
	* adhoc.h (IDot11AdHocInterfaceNotificationSink): Fix
	OnConnectionStatusChange.

2012-04-23  Kai Tietz  <<EMAIL>>

	PR/3520095
	* endpointvolume.h (IAudioEndpointVolume): Fix.

2012-03-07  Corinna Vinschen <<EMAIL>>

	* winternl.h (struct _LDR_DATA_TABLE_ENTRY): Changed type of Reserved1
	from BYTE to LPVOID.
	(InitializeObjectAttributes): New macro.
	(OBJ_INHERIT, OBJ_PERMANENT, OBJ_EXCLUSIVE, OBJ_CASE_INSENSITIVE,
	OBJ_OPENIF, OBJ_OPENLINK, OBJ_KERNEL_HANDLE, OBJ_FORCE_ACCESS_CHECK,
	OBJ_VALID_ATTRIBUTES): New macro-constants.
	* iptypes.h (PIP_ADAPTER_ADDRESSES_LH): Add type as pointer to
	IP_ADAPTER_ADDRESSES_LH.

2012-02-07  Rafaël Carré  <<EMAIL>>

	* dxva2api.h : Add UUIDS and missing error codes

2012-02-04  Rafaël Carré  <<EMAIL>>

	* d2d1.h (ID2D1HwndRenderTarget): Fix interface

2012-02-03  Rafaël Carré  <<EMAIL>>

	* shobjidl.h (ApplicationAssociationRegistrationUI): Add missing interface

2012-02-02  Rafaël Carré  <<EMAIL>>

	* uuids.h (MEDIASUBTYPE_I420): Add GUID.

2012-02-01  Rafaël Carré <<EMAIL>>

	* dxva2api.h (Direct3DDeviceManager9): Fix COBJMACROS to use lpVtbl.
	(DirectXVideoDecoder): Likewise.
	(DirectXVideoAccelerationService): Likewise.
	(DirectXVideoDecoderService): Likewise.

2012-02-01  Kai Tietz  <<EMAIL>>

	* rpcndr.h (small): Define only if RC_INVOKED is defined.

	* shobjidl.h (THBN_CLICKED): Define constant.

2012-01-29  Jonathan Yong  <<EMAIL>>

	* strsafe.h (MPRAPI_OBJECT_HEADER): Fix double extern qualifier in
	_STRSAFE_EXTERN_C. __CRT_INLINE macro already has extern.

2012-01-26  Kai Tietz  <<EMAIL>>

	* nldef.h (NL_INTERFACE_OFFLOAD_ROD): Remove
	TlStreamFastPathCompatible and TlDatagramFastPathCompatible
	members, which are merged into FastPathCompatible.
	(note msdn is wrong here).

2012-01-26  Jonathan Yong  <<EMAIL>>

	* mprapi.h (MPRAPI_OBJECT_HEADER): Fix declaration.
	(MPR_SERVER_EX): New typedef.
	(MPRAPI_ADMIN_DLL_CALLBACKS): Likewise.
	(MprConfigServerGetInfoEx): Declare.
	(MprAdminConnectionEnumEx): Likewise.
	(MprAdminConnectionGetInfoEx): Likewise.
	(MprAdminInitializeDllEx): Likewise.
	(MprAdminIsServiceInitialized): Likewise.
	(MprAdminServerGetInfoEx): Likewise.
	(MprAdminServerSetInfoEx): Likewise.
	* endpointvolume.h (CLSID_MMDeviceEnumerator): Declare.
	(IID_IMMDeviceEnumerator): Likewise.
	(IID_IAudioEndpointVolume): Likewise.
	(IID_IAudioEndpointVolumeCallback): Likewise.
	(IID_IAudioEndpointVolumeEx): Likewise.
	* manipulations.h: New.
	* elscore.h: New.
	* mfapi.h(MT_ARBITRARY_HEADER): New typedef.
	* locationapi.h(LOCATION_DESIRED_ACCURACY): New typedef.
	* lmaccess.h(MSA_INFO_STATE): New typedef.
	(MSA_INFO_0): Likewise.

2012-01-26  Ozkan Sezer  <<EMAIL>>

	* mprapi.h (MprAdminConnectionRemoveQuarantine): Revert
	to old definition which is correct by marking the function
	as WINAPI.

2012-01-25  Kai Tietz  <<EMAIL>>

	* iprtrmib.h (MIB_TCP_STATE): New typedef.
	* mprapi.h (MprAdminConnectionRemoveQuarantine): Remove
	double definition.

2012-01-19  Kai Tietz  <<EMAIL>>

	PR 3474190
	* windef.h (STRICT): Regard definition of NO_STRICT.

2012-01-18  Kai TIetz  <<EMAIL>>

	* unknwn.h: Remove sepecial-case for Obj-C.
	* windows.h: and move it here.
