;
; Definition file of dhcpcsvc.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "dhcpcsvc.DLL"
EXPORTS
DhcpAcquireParameters
DhcpAcquireParametersByBroadcast
DhcpCApiCleanup
DhcpCApiInitialize
DhcpClient_Generalize
DhcpDeRegisterConnectionStateNotification
DhcpDeRegisterOptions
DhcpDeRegisterParamChange
DhcpDelPersistentRequestParams
DhcpEnableDhcp
DhcpEnableTracing
DhcpEnumClasses
DhcpEnumInterfaces
DhcpFallbackRefreshParams
DhcpFreeEnumeratedInterfaces
DhcpFreeLeaseInfo
DhcpFreeLeaseInfoArray
DhcpFreeMem
DhcpGetClassId
DhcpGetClientId
DhcpGetDhcpServicedConnections
DhcpGetFallbackParams
DhcpGetNotificationStatus
DhcpGetOriginalSubnetMask
DhcpGetTraceArray
DhcpGlobalIsShuttingDown DATA
DhcpGlobalServiceSyncEvent DATA
DhcpGlobalTerminateEvent DATA
DhcpHandlePnPEvent
DhcpIsEnabled
DhcpLeaseIpAddress
DhcpLeaseIpAddressEx
DhcpNotifyConfigChange
DhcpNotifyConfigChangeEx
DhcpNotifyMediaReconnected
DhcpOpenGlobalEvent
DhcpPersistentRequestParams
DhcpQueryLeaseInfo
DhcpQueryLeaseInfoArray
DhcpQueryLeaseInfoEx
DhcpRegisterConnectionStateNotification
DhcpRegisterOptions
DhcpRegisterParamChange
DhcpReleaseIpAddressLease
DhcpReleaseIpAddressLeaseEx
DhcpReleaseParameters
DhcpRemoveDNSRegistrations
DhcpRenewIpAddressLease
DhcpRenewIpAddressLeaseEx
DhcpRequestCachedParams
DhcpRequestOptions
DhcpRequestParams
DhcpSetClassId
DhcpSetClientId
DhcpSetFallbackParams
DhcpSetMSFTVendorSpecificOptions
DhcpStaticRefreshParams
DhcpUndoRequestParams
Dhcpv4CheckServerAvailability
Dhcpv4EnableDhcpEx
McastApiCleanup
McastApiStartup
McastEnumerateScopes
McastGenUID
McastReleaseAddress
McastRenewAddress
McastRequestAddress
