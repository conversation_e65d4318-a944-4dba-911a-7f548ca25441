#include "func.def.in"

LIBRARY "WS2_32.dll"
EXPORTS
accept
bind
closesocket
connect
getpeername
getsockname
getsockopt
htonl
htons
ioctlsocket
inet_addr
inet_ntoa
listen
ntohl
ntohs
recv
recvfrom
select
send
sendto
setsockopt
shutdown
socket
WSApSetPostRoutine
FreeAddrInfoEx
FreeAddrInfoExW
FreeAddrInfoW
GetAddrInfoExA
GetAddrInfoExCancel
GetAddrInfoExOverlappedResult
GetAddrInfoExW
GetAddrInfoW
GetHostNameW
GetNameInfoW
InetNtopW
InetPtonW
ProcessSocketNotifications
SetAddrInfoExA
SetAddrInfoExW
WPUCompleteOverlappedRequest
WPUGetProviderPathEx
WSAAccept
WSAAddressToStringA
WSAAddressToStringW
WSAAdvertiseProvider
WSACloseEvent
WSAConnect
WSAConnectByList
WSAConnectByNameA
WSAConnectByNameW
WSACreateEvent
gethostbyaddr
gethostbyname
getprotobyname
getprotobynumber
getservbyname
getservbyport
gethostname
WSADuplicateSocketA
WSADuplicateSocketW
WSAEnumNameSpaceProvidersA
WSAEnumNameSpaceProvidersExA
WSAEnumNameSpaceProvidersExW
WSAEnumNameSpaceProvidersW
WSAEnumNetworkEvents
WSAEnumProtocolsA
WSAEnumProtocolsW
WSAEventSelect
WSAGetOverlappedResult
WSAGetQOSByName
WSAGetServiceClassInfoA
WSAGetServiceClassInfoW
WSAGetServiceClassNameByClassIdA
WSAGetServiceClassNameByClassIdW
WSAHtonl
WSAHtons
WSAInstallServiceClassA
WSAInstallServiceClassW
WSAIoctl
WSAJoinLeaf
WSALookupServiceBeginA
WSALookupServiceBeginW
WSALookupServiceEnd
WSALookupServiceNextA
WSALookupServiceNextW
WSANSPIoctl
WSANtohl
WSANtohs
WSAPoll
WSAProviderCompleteAsyncCall
WSAProviderConfigChange
WSARecv
WSARecvDisconnect
WSARecvFrom
WSARemoveServiceClass
WSAResetEvent
WSASend
WSASendDisconnect
WSASendMsg
WSASendTo
WSASetEvent
WSAAsyncSelect
WSAAsyncGetHostByAddr
WSAAsyncGetHostByName
WSAAsyncGetProtoByNumber
WSAAsyncGetProtoByName
WSAAsyncGetServByPort
WSAAsyncGetServByName
WSACancelAsyncRequest
WSASetBlockingHook
WSAUnhookBlockingHook
WSAGetLastError
WSASetLastError
WSACancelBlockingCall
WSAIsBlocking
WSAStartup
WSACleanup
WSASetServiceA
WSASetServiceW
WSASocketA
WSASocketW
WSAStringToAddressA
WSAStringToAddressW
WSAUnadvertiseProvider
WSAWaitForMultipleEvents
WSCDeinstallProvider
F64(WSCDeinstallProvider32)
WSCDeinstallProviderEx
WSCEnableNSProvider
F64(WSCEnableNSProvider32)
F64(WSCEnumNameSpaceProviders32)
F64(WSCEnumNameSpaceProvidersEx32)
WSCEnumProtocols
WSCEnumProtocolsEx
F64(WSCEnumProtocols32)
WSCGetApplicationCategory
WSCGetApplicationCategoryEx
WSCGetProviderInfo
F64(WSCGetProviderInfo32)
WSCGetProviderPath
F64(WSCGetProviderPath32)
WSCInstallNameSpace
F64(WSCInstallNameSpace32)
WSCInstallNameSpaceEx
WSCInstallNameSpaceEx2
F64(WSCInstallNameSpaceEx32)
WSCInstallProvider
F64(WSCInstallProvider64_32)
WSCInstallProviderAndChains
F64(WSCInstallProviderAndChains64_32)
WSCInstallProviderEx
WSCSetApplicationCategory
WSCSetApplicationCategoryEx
WSCSetProviderInfo
F64(WSCSetProviderInfo32)
WSCUnInstallNameSpace
F64(WSCUnInstallNameSpace32)
WSCUnInstallNameSpaceEx2
WSCUpdateProvider
F64(WSCUpdateProvider32)
WSCUpdateProviderEx
WSCWriteNameSpaceOrder
F64(WSCWriteNameSpaceOrder32)
WSCWriteProviderOrder
F64(WSCWriteProviderOrder32)
WSCWriteProviderOrderEx
WahCloseApcHelper
__WSAFDIsSet
WahCloseHandleHelper
WahCloseNotificationHandleHelper
WahCloseSocketHandle
WahCloseThread
WahCompleteRequest
WahCreateHandleContextTable
WahCreateNotificationHandle
WahCreateSocketHandle
WahDestroyHandleContextTable
WahDisableNonIFSHandleSupport
WahEnableNonIFSHandleSupport
WahEnumerateHandleContexts
WahInsertHandleContext
WahNotifyAllProcesses
WahOpenApcHelper
WahOpenCurrentThread
WahOpenHandleHelper
WahOpenNotificationHandleHelper
WahQueueUserApc
WahReferenceContextByHandle
WahRemoveHandleContext
WahWaitForNotification
WahWriteLSPEvent
freeaddrinfo
getaddrinfo
getnameinfo
inet_ntop
inet_pton
WEP
