# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
bin_PROGRAMS = widl$(EXEEXT)
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_compute_relative_paths.m4 \
	$(top_srcdir)/VERSION $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am__dirstamp = $(am__leading_dot)dirstamp
am_widl_OBJECTS = src/widl-attribute.$(OBJEXT) \
	src/widl-client.$(OBJEXT) src/widl-expr.$(OBJEXT) \
	src/widl-hash.$(OBJEXT) src/widl-header.$(OBJEXT) \
	src/widl-parser.tab.$(OBJEXT) src/widl-parser.yy.$(OBJEXT) \
	src/port/widl-getopt.$(OBJEXT) src/port/widl-port.$(OBJEXT) \
	src/widl-ppl.yy.$(OBJEXT) src/widl-ppy.tab.$(OBJEXT) \
	src/widl-proxy.$(OBJEXT) src/widl-register.$(OBJEXT) \
	src/widl-server.$(OBJEXT) src/widl-typegen.$(OBJEXT) \
	src/widl-typelib.$(OBJEXT) src/widl-typetree.$(OBJEXT) \
	src/widl-utils.$(OBJEXT) src/widl-widl.$(OBJEXT) \
	src/widl-wpp.$(OBJEXT) src/widl-write_msft.$(OBJEXT) \
	src/widl-pathtools.$(OBJEXT)
widl_OBJECTS = $(am_widl_OBJECTS)
widl_LDADD = $(LDADD)
widl_LINK = $(CCLD) $(widl_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) \
	-o $@
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)/include
depcomp = $(SHELL) $(top_srcdir)/build-aux/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = src/$(DEPDIR)/widl-attribute.Po \
	src/$(DEPDIR)/widl-client.Po src/$(DEPDIR)/widl-expr.Po \
	src/$(DEPDIR)/widl-hash.Po src/$(DEPDIR)/widl-header.Po \
	src/$(DEPDIR)/widl-parser.tab.Po \
	src/$(DEPDIR)/widl-parser.yy.Po \
	src/$(DEPDIR)/widl-pathtools.Po src/$(DEPDIR)/widl-ppl.yy.Po \
	src/$(DEPDIR)/widl-ppy.tab.Po src/$(DEPDIR)/widl-proxy.Po \
	src/$(DEPDIR)/widl-register.Po src/$(DEPDIR)/widl-server.Po \
	src/$(DEPDIR)/widl-typegen.Po src/$(DEPDIR)/widl-typelib.Po \
	src/$(DEPDIR)/widl-typetree.Po src/$(DEPDIR)/widl-utils.Po \
	src/$(DEPDIR)/widl-widl.Po src/$(DEPDIR)/widl-wpp.Po \
	src/$(DEPDIR)/widl-write_msft.Po \
	src/port/$(DEPDIR)/widl-getopt.Po \
	src/port/$(DEPDIR)/widl-port.Po
am__mv = mv -f
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(widl_SOURCES)
DIST_SOURCES = $(widl_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
AM_RECURSIVE_TARGETS = cscope
am__DIST_COMMON = $(srcdir)/Makefile.in \
	$(top_srcdir)/build-aux/compile \
	$(top_srcdir)/build-aux/config.guess \
	$(top_srcdir)/build-aux/config.sub \
	$(top_srcdir)/build-aux/depcomp \
	$(top_srcdir)/build-aux/install-sh \
	$(top_srcdir)/build-aux/missing \
	$(top_srcdir)/include/config.h.in build-aux/compile \
	build-aux/config.guess build-aux/config.sub build-aux/depcomp \
	build-aux/install-sh build-aux/missing
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
DIST_TARGETS = dist-gzip
# Exists only to be overridden by the user if desired.
AM_DISTCHECK_DVI_TARGET = dvi
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BIN_TO_INCLUDEDIR = @BIN_TO_INCLUDEDIR@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
WIDL_INCLUDEDIR = @WIDL_INCLUDEDIR@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
widl_SOURCES = src/widl.h \
  src/attribute.c \
  src/expr.h \
  src/hash.h \
  src/header.h \
  src/parser.h \
  src/parser.tab.h \
  src/typegen.h \
  src/typelib.h \
  src/typelib_struct.h \
  src/typetree.h \
  src/utils.h \
  src/widltypes.h \
  src/client.c \
  src/expr.c \
  src/hash.c \
  src/header.c \
  src/parser.tab.c \
  src/parser.yy.c \
  src/port/getopt.c \
  src/port/port.c \
  src/ppy.tab.h \
  src/ppl.yy.c \
  src/ppy.tab.c \
  src/proxy.c \
  src/register.c \
  src/server.c \
  src/typegen.c \
  src/typelib.c \
  src/typetree.c \
  src/utils.c \
  src/widl.c \
  src/wpp_private.h \
  src/wpp.c \
  src/write_msft.c \
  src/pathtools.c \
  include/pshpack1.h \
  include/pshpack2.h \
  include/winbase.h \
  include/pshpack8.h \
  include/basetsd.h \
  include/guiddef.h \
  include/winnt.h \
  include/windef.h \
  include/poppack.h \
  include/config.h.in \
  include/wine/list.h \
  include/wine/port.h \
  include/wine/rpcfc.h \
  include/winerror.h \
  include/pshpack4.h \
  include/winnt.h \
  include/winnls.h \
  include/pathtools.h \
  include/winnt.rh

widl_CPPFLAGS = -I$(top_srcdir)/include -DINCLUDEDIR=\""@WIDL_INCLUDEDIR@"\" -DBIN_TO_INCLUDEDIR=\""@BIN_TO_INCLUDEDIR@"\" -DBIN_TO_DLLDIR=\""@BIN_TO_INCLUDEDIR@"\" -DDLLDIR="\"@prefix@/lib\""
widl_CFLAGS = -O3 -g -Wall -Wformat -Wpacked -Wmissing-declarations -Wimplicit-function-declaration -Wmissing-prototypes -Wstrict-aliasing=2
DISTCHECK_CONFIGURE_FLAGS = --host=$(host) --target=$(target)
all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --foreign \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

include/config.h: include/stamp-h1
	@test -f $@ || rm -f include/stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) include/stamp-h1

include/stamp-h1: $(top_srcdir)/include/config.h.in $(top_builddir)/config.status
	@rm -f include/stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status include/config.h
$(top_srcdir)/include/config.h.in: @MAINTAINER_MODE_TRUE@ $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f include/stamp-h1
	touch $@

distclean-hdr:
	-rm -f include/config.h include/stamp-h1
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	      echo " $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	      $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	-test -z "$(bin_PROGRAMS)" || rm -f $(bin_PROGRAMS)
src/$(am__dirstamp):
	@$(MKDIR_P) src
	@: > src/$(am__dirstamp)
src/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/$(DEPDIR)
	@: > src/$(DEPDIR)/$(am__dirstamp)
src/widl-attribute.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-client.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-expr.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-hash.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-header.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-parser.tab.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-parser.yy.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/port/$(am__dirstamp):
	@$(MKDIR_P) src/port
	@: > src/port/$(am__dirstamp)
src/port/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/port/$(DEPDIR)
	@: > src/port/$(DEPDIR)/$(am__dirstamp)
src/port/widl-getopt.$(OBJEXT): src/port/$(am__dirstamp) \
	src/port/$(DEPDIR)/$(am__dirstamp)
src/port/widl-port.$(OBJEXT): src/port/$(am__dirstamp) \
	src/port/$(DEPDIR)/$(am__dirstamp)
src/widl-ppl.yy.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-ppy.tab.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-proxy.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-register.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-server.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-typegen.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-typelib.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-typetree.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-utils.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-widl.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-wpp.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-write_msft.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/widl-pathtools.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

widl$(EXEEXT): $(widl_OBJECTS) $(widl_DEPENDENCIES) $(EXTRA_widl_DEPENDENCIES) 
	@rm -f widl$(EXEEXT)
	$(AM_V_CCLD)$(widl_LINK) $(widl_OBJECTS) $(widl_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f src/*.$(OBJEXT)
	-rm -f src/port/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-attribute.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-client.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-expr.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-hash.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-header.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-parser.tab.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-parser.yy.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-pathtools.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-ppl.yy.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-ppy.tab.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-proxy.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-register.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-server.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-typegen.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-typelib.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-typetree.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-utils.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-widl.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-wpp.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/widl-write_msft.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/port/$(DEPDIR)/widl-getopt.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/port/$(DEPDIR)/widl-port.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

src/widl-attribute.o: src/attribute.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-attribute.o -MD -MP -MF src/$(DEPDIR)/widl-attribute.Tpo -c -o src/widl-attribute.o `test -f 'src/attribute.c' || echo '$(srcdir)/'`src/attribute.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-attribute.Tpo src/$(DEPDIR)/widl-attribute.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/attribute.c' object='src/widl-attribute.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-attribute.o `test -f 'src/attribute.c' || echo '$(srcdir)/'`src/attribute.c

src/widl-attribute.obj: src/attribute.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-attribute.obj -MD -MP -MF src/$(DEPDIR)/widl-attribute.Tpo -c -o src/widl-attribute.obj `if test -f 'src/attribute.c'; then $(CYGPATH_W) 'src/attribute.c'; else $(CYGPATH_W) '$(srcdir)/src/attribute.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-attribute.Tpo src/$(DEPDIR)/widl-attribute.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/attribute.c' object='src/widl-attribute.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-attribute.obj `if test -f 'src/attribute.c'; then $(CYGPATH_W) 'src/attribute.c'; else $(CYGPATH_W) '$(srcdir)/src/attribute.c'; fi`

src/widl-client.o: src/client.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-client.o -MD -MP -MF src/$(DEPDIR)/widl-client.Tpo -c -o src/widl-client.o `test -f 'src/client.c' || echo '$(srcdir)/'`src/client.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-client.Tpo src/$(DEPDIR)/widl-client.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/client.c' object='src/widl-client.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-client.o `test -f 'src/client.c' || echo '$(srcdir)/'`src/client.c

src/widl-client.obj: src/client.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-client.obj -MD -MP -MF src/$(DEPDIR)/widl-client.Tpo -c -o src/widl-client.obj `if test -f 'src/client.c'; then $(CYGPATH_W) 'src/client.c'; else $(CYGPATH_W) '$(srcdir)/src/client.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-client.Tpo src/$(DEPDIR)/widl-client.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/client.c' object='src/widl-client.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-client.obj `if test -f 'src/client.c'; then $(CYGPATH_W) 'src/client.c'; else $(CYGPATH_W) '$(srcdir)/src/client.c'; fi`

src/widl-expr.o: src/expr.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-expr.o -MD -MP -MF src/$(DEPDIR)/widl-expr.Tpo -c -o src/widl-expr.o `test -f 'src/expr.c' || echo '$(srcdir)/'`src/expr.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-expr.Tpo src/$(DEPDIR)/widl-expr.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/expr.c' object='src/widl-expr.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-expr.o `test -f 'src/expr.c' || echo '$(srcdir)/'`src/expr.c

src/widl-expr.obj: src/expr.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-expr.obj -MD -MP -MF src/$(DEPDIR)/widl-expr.Tpo -c -o src/widl-expr.obj `if test -f 'src/expr.c'; then $(CYGPATH_W) 'src/expr.c'; else $(CYGPATH_W) '$(srcdir)/src/expr.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-expr.Tpo src/$(DEPDIR)/widl-expr.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/expr.c' object='src/widl-expr.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-expr.obj `if test -f 'src/expr.c'; then $(CYGPATH_W) 'src/expr.c'; else $(CYGPATH_W) '$(srcdir)/src/expr.c'; fi`

src/widl-hash.o: src/hash.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-hash.o -MD -MP -MF src/$(DEPDIR)/widl-hash.Tpo -c -o src/widl-hash.o `test -f 'src/hash.c' || echo '$(srcdir)/'`src/hash.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-hash.Tpo src/$(DEPDIR)/widl-hash.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/hash.c' object='src/widl-hash.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-hash.o `test -f 'src/hash.c' || echo '$(srcdir)/'`src/hash.c

src/widl-hash.obj: src/hash.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-hash.obj -MD -MP -MF src/$(DEPDIR)/widl-hash.Tpo -c -o src/widl-hash.obj `if test -f 'src/hash.c'; then $(CYGPATH_W) 'src/hash.c'; else $(CYGPATH_W) '$(srcdir)/src/hash.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-hash.Tpo src/$(DEPDIR)/widl-hash.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/hash.c' object='src/widl-hash.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-hash.obj `if test -f 'src/hash.c'; then $(CYGPATH_W) 'src/hash.c'; else $(CYGPATH_W) '$(srcdir)/src/hash.c'; fi`

src/widl-header.o: src/header.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-header.o -MD -MP -MF src/$(DEPDIR)/widl-header.Tpo -c -o src/widl-header.o `test -f 'src/header.c' || echo '$(srcdir)/'`src/header.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-header.Tpo src/$(DEPDIR)/widl-header.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/header.c' object='src/widl-header.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-header.o `test -f 'src/header.c' || echo '$(srcdir)/'`src/header.c

src/widl-header.obj: src/header.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-header.obj -MD -MP -MF src/$(DEPDIR)/widl-header.Tpo -c -o src/widl-header.obj `if test -f 'src/header.c'; then $(CYGPATH_W) 'src/header.c'; else $(CYGPATH_W) '$(srcdir)/src/header.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-header.Tpo src/$(DEPDIR)/widl-header.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/header.c' object='src/widl-header.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-header.obj `if test -f 'src/header.c'; then $(CYGPATH_W) 'src/header.c'; else $(CYGPATH_W) '$(srcdir)/src/header.c'; fi`

src/widl-parser.tab.o: src/parser.tab.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-parser.tab.o -MD -MP -MF src/$(DEPDIR)/widl-parser.tab.Tpo -c -o src/widl-parser.tab.o `test -f 'src/parser.tab.c' || echo '$(srcdir)/'`src/parser.tab.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-parser.tab.Tpo src/$(DEPDIR)/widl-parser.tab.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/parser.tab.c' object='src/widl-parser.tab.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-parser.tab.o `test -f 'src/parser.tab.c' || echo '$(srcdir)/'`src/parser.tab.c

src/widl-parser.tab.obj: src/parser.tab.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-parser.tab.obj -MD -MP -MF src/$(DEPDIR)/widl-parser.tab.Tpo -c -o src/widl-parser.tab.obj `if test -f 'src/parser.tab.c'; then $(CYGPATH_W) 'src/parser.tab.c'; else $(CYGPATH_W) '$(srcdir)/src/parser.tab.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-parser.tab.Tpo src/$(DEPDIR)/widl-parser.tab.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/parser.tab.c' object='src/widl-parser.tab.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-parser.tab.obj `if test -f 'src/parser.tab.c'; then $(CYGPATH_W) 'src/parser.tab.c'; else $(CYGPATH_W) '$(srcdir)/src/parser.tab.c'; fi`

src/widl-parser.yy.o: src/parser.yy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-parser.yy.o -MD -MP -MF src/$(DEPDIR)/widl-parser.yy.Tpo -c -o src/widl-parser.yy.o `test -f 'src/parser.yy.c' || echo '$(srcdir)/'`src/parser.yy.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-parser.yy.Tpo src/$(DEPDIR)/widl-parser.yy.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/parser.yy.c' object='src/widl-parser.yy.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-parser.yy.o `test -f 'src/parser.yy.c' || echo '$(srcdir)/'`src/parser.yy.c

src/widl-parser.yy.obj: src/parser.yy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-parser.yy.obj -MD -MP -MF src/$(DEPDIR)/widl-parser.yy.Tpo -c -o src/widl-parser.yy.obj `if test -f 'src/parser.yy.c'; then $(CYGPATH_W) 'src/parser.yy.c'; else $(CYGPATH_W) '$(srcdir)/src/parser.yy.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-parser.yy.Tpo src/$(DEPDIR)/widl-parser.yy.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/parser.yy.c' object='src/widl-parser.yy.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-parser.yy.obj `if test -f 'src/parser.yy.c'; then $(CYGPATH_W) 'src/parser.yy.c'; else $(CYGPATH_W) '$(srcdir)/src/parser.yy.c'; fi`

src/port/widl-getopt.o: src/port/getopt.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/port/widl-getopt.o -MD -MP -MF src/port/$(DEPDIR)/widl-getopt.Tpo -c -o src/port/widl-getopt.o `test -f 'src/port/getopt.c' || echo '$(srcdir)/'`src/port/getopt.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/port/$(DEPDIR)/widl-getopt.Tpo src/port/$(DEPDIR)/widl-getopt.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/port/getopt.c' object='src/port/widl-getopt.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/port/widl-getopt.o `test -f 'src/port/getopt.c' || echo '$(srcdir)/'`src/port/getopt.c

src/port/widl-getopt.obj: src/port/getopt.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/port/widl-getopt.obj -MD -MP -MF src/port/$(DEPDIR)/widl-getopt.Tpo -c -o src/port/widl-getopt.obj `if test -f 'src/port/getopt.c'; then $(CYGPATH_W) 'src/port/getopt.c'; else $(CYGPATH_W) '$(srcdir)/src/port/getopt.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/port/$(DEPDIR)/widl-getopt.Tpo src/port/$(DEPDIR)/widl-getopt.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/port/getopt.c' object='src/port/widl-getopt.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/port/widl-getopt.obj `if test -f 'src/port/getopt.c'; then $(CYGPATH_W) 'src/port/getopt.c'; else $(CYGPATH_W) '$(srcdir)/src/port/getopt.c'; fi`

src/port/widl-port.o: src/port/port.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/port/widl-port.o -MD -MP -MF src/port/$(DEPDIR)/widl-port.Tpo -c -o src/port/widl-port.o `test -f 'src/port/port.c' || echo '$(srcdir)/'`src/port/port.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/port/$(DEPDIR)/widl-port.Tpo src/port/$(DEPDIR)/widl-port.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/port/port.c' object='src/port/widl-port.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/port/widl-port.o `test -f 'src/port/port.c' || echo '$(srcdir)/'`src/port/port.c

src/port/widl-port.obj: src/port/port.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/port/widl-port.obj -MD -MP -MF src/port/$(DEPDIR)/widl-port.Tpo -c -o src/port/widl-port.obj `if test -f 'src/port/port.c'; then $(CYGPATH_W) 'src/port/port.c'; else $(CYGPATH_W) '$(srcdir)/src/port/port.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/port/$(DEPDIR)/widl-port.Tpo src/port/$(DEPDIR)/widl-port.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/port/port.c' object='src/port/widl-port.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/port/widl-port.obj `if test -f 'src/port/port.c'; then $(CYGPATH_W) 'src/port/port.c'; else $(CYGPATH_W) '$(srcdir)/src/port/port.c'; fi`

src/widl-ppl.yy.o: src/ppl.yy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-ppl.yy.o -MD -MP -MF src/$(DEPDIR)/widl-ppl.yy.Tpo -c -o src/widl-ppl.yy.o `test -f 'src/ppl.yy.c' || echo '$(srcdir)/'`src/ppl.yy.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-ppl.yy.Tpo src/$(DEPDIR)/widl-ppl.yy.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/ppl.yy.c' object='src/widl-ppl.yy.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-ppl.yy.o `test -f 'src/ppl.yy.c' || echo '$(srcdir)/'`src/ppl.yy.c

src/widl-ppl.yy.obj: src/ppl.yy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-ppl.yy.obj -MD -MP -MF src/$(DEPDIR)/widl-ppl.yy.Tpo -c -o src/widl-ppl.yy.obj `if test -f 'src/ppl.yy.c'; then $(CYGPATH_W) 'src/ppl.yy.c'; else $(CYGPATH_W) '$(srcdir)/src/ppl.yy.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-ppl.yy.Tpo src/$(DEPDIR)/widl-ppl.yy.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/ppl.yy.c' object='src/widl-ppl.yy.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-ppl.yy.obj `if test -f 'src/ppl.yy.c'; then $(CYGPATH_W) 'src/ppl.yy.c'; else $(CYGPATH_W) '$(srcdir)/src/ppl.yy.c'; fi`

src/widl-ppy.tab.o: src/ppy.tab.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-ppy.tab.o -MD -MP -MF src/$(DEPDIR)/widl-ppy.tab.Tpo -c -o src/widl-ppy.tab.o `test -f 'src/ppy.tab.c' || echo '$(srcdir)/'`src/ppy.tab.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-ppy.tab.Tpo src/$(DEPDIR)/widl-ppy.tab.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/ppy.tab.c' object='src/widl-ppy.tab.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-ppy.tab.o `test -f 'src/ppy.tab.c' || echo '$(srcdir)/'`src/ppy.tab.c

src/widl-ppy.tab.obj: src/ppy.tab.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-ppy.tab.obj -MD -MP -MF src/$(DEPDIR)/widl-ppy.tab.Tpo -c -o src/widl-ppy.tab.obj `if test -f 'src/ppy.tab.c'; then $(CYGPATH_W) 'src/ppy.tab.c'; else $(CYGPATH_W) '$(srcdir)/src/ppy.tab.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-ppy.tab.Tpo src/$(DEPDIR)/widl-ppy.tab.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/ppy.tab.c' object='src/widl-ppy.tab.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-ppy.tab.obj `if test -f 'src/ppy.tab.c'; then $(CYGPATH_W) 'src/ppy.tab.c'; else $(CYGPATH_W) '$(srcdir)/src/ppy.tab.c'; fi`

src/widl-proxy.o: src/proxy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-proxy.o -MD -MP -MF src/$(DEPDIR)/widl-proxy.Tpo -c -o src/widl-proxy.o `test -f 'src/proxy.c' || echo '$(srcdir)/'`src/proxy.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-proxy.Tpo src/$(DEPDIR)/widl-proxy.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/proxy.c' object='src/widl-proxy.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-proxy.o `test -f 'src/proxy.c' || echo '$(srcdir)/'`src/proxy.c

src/widl-proxy.obj: src/proxy.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-proxy.obj -MD -MP -MF src/$(DEPDIR)/widl-proxy.Tpo -c -o src/widl-proxy.obj `if test -f 'src/proxy.c'; then $(CYGPATH_W) 'src/proxy.c'; else $(CYGPATH_W) '$(srcdir)/src/proxy.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-proxy.Tpo src/$(DEPDIR)/widl-proxy.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/proxy.c' object='src/widl-proxy.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-proxy.obj `if test -f 'src/proxy.c'; then $(CYGPATH_W) 'src/proxy.c'; else $(CYGPATH_W) '$(srcdir)/src/proxy.c'; fi`

src/widl-register.o: src/register.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-register.o -MD -MP -MF src/$(DEPDIR)/widl-register.Tpo -c -o src/widl-register.o `test -f 'src/register.c' || echo '$(srcdir)/'`src/register.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-register.Tpo src/$(DEPDIR)/widl-register.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/register.c' object='src/widl-register.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-register.o `test -f 'src/register.c' || echo '$(srcdir)/'`src/register.c

src/widl-register.obj: src/register.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-register.obj -MD -MP -MF src/$(DEPDIR)/widl-register.Tpo -c -o src/widl-register.obj `if test -f 'src/register.c'; then $(CYGPATH_W) 'src/register.c'; else $(CYGPATH_W) '$(srcdir)/src/register.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-register.Tpo src/$(DEPDIR)/widl-register.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/register.c' object='src/widl-register.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-register.obj `if test -f 'src/register.c'; then $(CYGPATH_W) 'src/register.c'; else $(CYGPATH_W) '$(srcdir)/src/register.c'; fi`

src/widl-server.o: src/server.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-server.o -MD -MP -MF src/$(DEPDIR)/widl-server.Tpo -c -o src/widl-server.o `test -f 'src/server.c' || echo '$(srcdir)/'`src/server.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-server.Tpo src/$(DEPDIR)/widl-server.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/server.c' object='src/widl-server.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-server.o `test -f 'src/server.c' || echo '$(srcdir)/'`src/server.c

src/widl-server.obj: src/server.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-server.obj -MD -MP -MF src/$(DEPDIR)/widl-server.Tpo -c -o src/widl-server.obj `if test -f 'src/server.c'; then $(CYGPATH_W) 'src/server.c'; else $(CYGPATH_W) '$(srcdir)/src/server.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-server.Tpo src/$(DEPDIR)/widl-server.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/server.c' object='src/widl-server.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-server.obj `if test -f 'src/server.c'; then $(CYGPATH_W) 'src/server.c'; else $(CYGPATH_W) '$(srcdir)/src/server.c'; fi`

src/widl-typegen.o: src/typegen.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-typegen.o -MD -MP -MF src/$(DEPDIR)/widl-typegen.Tpo -c -o src/widl-typegen.o `test -f 'src/typegen.c' || echo '$(srcdir)/'`src/typegen.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-typegen.Tpo src/$(DEPDIR)/widl-typegen.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/typegen.c' object='src/widl-typegen.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-typegen.o `test -f 'src/typegen.c' || echo '$(srcdir)/'`src/typegen.c

src/widl-typegen.obj: src/typegen.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-typegen.obj -MD -MP -MF src/$(DEPDIR)/widl-typegen.Tpo -c -o src/widl-typegen.obj `if test -f 'src/typegen.c'; then $(CYGPATH_W) 'src/typegen.c'; else $(CYGPATH_W) '$(srcdir)/src/typegen.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-typegen.Tpo src/$(DEPDIR)/widl-typegen.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/typegen.c' object='src/widl-typegen.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-typegen.obj `if test -f 'src/typegen.c'; then $(CYGPATH_W) 'src/typegen.c'; else $(CYGPATH_W) '$(srcdir)/src/typegen.c'; fi`

src/widl-typelib.o: src/typelib.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-typelib.o -MD -MP -MF src/$(DEPDIR)/widl-typelib.Tpo -c -o src/widl-typelib.o `test -f 'src/typelib.c' || echo '$(srcdir)/'`src/typelib.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-typelib.Tpo src/$(DEPDIR)/widl-typelib.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/typelib.c' object='src/widl-typelib.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-typelib.o `test -f 'src/typelib.c' || echo '$(srcdir)/'`src/typelib.c

src/widl-typelib.obj: src/typelib.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-typelib.obj -MD -MP -MF src/$(DEPDIR)/widl-typelib.Tpo -c -o src/widl-typelib.obj `if test -f 'src/typelib.c'; then $(CYGPATH_W) 'src/typelib.c'; else $(CYGPATH_W) '$(srcdir)/src/typelib.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-typelib.Tpo src/$(DEPDIR)/widl-typelib.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/typelib.c' object='src/widl-typelib.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-typelib.obj `if test -f 'src/typelib.c'; then $(CYGPATH_W) 'src/typelib.c'; else $(CYGPATH_W) '$(srcdir)/src/typelib.c'; fi`

src/widl-typetree.o: src/typetree.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-typetree.o -MD -MP -MF src/$(DEPDIR)/widl-typetree.Tpo -c -o src/widl-typetree.o `test -f 'src/typetree.c' || echo '$(srcdir)/'`src/typetree.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-typetree.Tpo src/$(DEPDIR)/widl-typetree.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/typetree.c' object='src/widl-typetree.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-typetree.o `test -f 'src/typetree.c' || echo '$(srcdir)/'`src/typetree.c

src/widl-typetree.obj: src/typetree.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-typetree.obj -MD -MP -MF src/$(DEPDIR)/widl-typetree.Tpo -c -o src/widl-typetree.obj `if test -f 'src/typetree.c'; then $(CYGPATH_W) 'src/typetree.c'; else $(CYGPATH_W) '$(srcdir)/src/typetree.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-typetree.Tpo src/$(DEPDIR)/widl-typetree.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/typetree.c' object='src/widl-typetree.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-typetree.obj `if test -f 'src/typetree.c'; then $(CYGPATH_W) 'src/typetree.c'; else $(CYGPATH_W) '$(srcdir)/src/typetree.c'; fi`

src/widl-utils.o: src/utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-utils.o -MD -MP -MF src/$(DEPDIR)/widl-utils.Tpo -c -o src/widl-utils.o `test -f 'src/utils.c' || echo '$(srcdir)/'`src/utils.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-utils.Tpo src/$(DEPDIR)/widl-utils.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/utils.c' object='src/widl-utils.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-utils.o `test -f 'src/utils.c' || echo '$(srcdir)/'`src/utils.c

src/widl-utils.obj: src/utils.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-utils.obj -MD -MP -MF src/$(DEPDIR)/widl-utils.Tpo -c -o src/widl-utils.obj `if test -f 'src/utils.c'; then $(CYGPATH_W) 'src/utils.c'; else $(CYGPATH_W) '$(srcdir)/src/utils.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-utils.Tpo src/$(DEPDIR)/widl-utils.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/utils.c' object='src/widl-utils.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-utils.obj `if test -f 'src/utils.c'; then $(CYGPATH_W) 'src/utils.c'; else $(CYGPATH_W) '$(srcdir)/src/utils.c'; fi`

src/widl-widl.o: src/widl.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-widl.o -MD -MP -MF src/$(DEPDIR)/widl-widl.Tpo -c -o src/widl-widl.o `test -f 'src/widl.c' || echo '$(srcdir)/'`src/widl.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-widl.Tpo src/$(DEPDIR)/widl-widl.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/widl.c' object='src/widl-widl.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-widl.o `test -f 'src/widl.c' || echo '$(srcdir)/'`src/widl.c

src/widl-widl.obj: src/widl.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-widl.obj -MD -MP -MF src/$(DEPDIR)/widl-widl.Tpo -c -o src/widl-widl.obj `if test -f 'src/widl.c'; then $(CYGPATH_W) 'src/widl.c'; else $(CYGPATH_W) '$(srcdir)/src/widl.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-widl.Tpo src/$(DEPDIR)/widl-widl.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/widl.c' object='src/widl-widl.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-widl.obj `if test -f 'src/widl.c'; then $(CYGPATH_W) 'src/widl.c'; else $(CYGPATH_W) '$(srcdir)/src/widl.c'; fi`

src/widl-wpp.o: src/wpp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-wpp.o -MD -MP -MF src/$(DEPDIR)/widl-wpp.Tpo -c -o src/widl-wpp.o `test -f 'src/wpp.c' || echo '$(srcdir)/'`src/wpp.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-wpp.Tpo src/$(DEPDIR)/widl-wpp.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/wpp.c' object='src/widl-wpp.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-wpp.o `test -f 'src/wpp.c' || echo '$(srcdir)/'`src/wpp.c

src/widl-wpp.obj: src/wpp.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-wpp.obj -MD -MP -MF src/$(DEPDIR)/widl-wpp.Tpo -c -o src/widl-wpp.obj `if test -f 'src/wpp.c'; then $(CYGPATH_W) 'src/wpp.c'; else $(CYGPATH_W) '$(srcdir)/src/wpp.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-wpp.Tpo src/$(DEPDIR)/widl-wpp.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/wpp.c' object='src/widl-wpp.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-wpp.obj `if test -f 'src/wpp.c'; then $(CYGPATH_W) 'src/wpp.c'; else $(CYGPATH_W) '$(srcdir)/src/wpp.c'; fi`

src/widl-write_msft.o: src/write_msft.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-write_msft.o -MD -MP -MF src/$(DEPDIR)/widl-write_msft.Tpo -c -o src/widl-write_msft.o `test -f 'src/write_msft.c' || echo '$(srcdir)/'`src/write_msft.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-write_msft.Tpo src/$(DEPDIR)/widl-write_msft.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/write_msft.c' object='src/widl-write_msft.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-write_msft.o `test -f 'src/write_msft.c' || echo '$(srcdir)/'`src/write_msft.c

src/widl-write_msft.obj: src/write_msft.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-write_msft.obj -MD -MP -MF src/$(DEPDIR)/widl-write_msft.Tpo -c -o src/widl-write_msft.obj `if test -f 'src/write_msft.c'; then $(CYGPATH_W) 'src/write_msft.c'; else $(CYGPATH_W) '$(srcdir)/src/write_msft.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-write_msft.Tpo src/$(DEPDIR)/widl-write_msft.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/write_msft.c' object='src/widl-write_msft.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-write_msft.obj `if test -f 'src/write_msft.c'; then $(CYGPATH_W) 'src/write_msft.c'; else $(CYGPATH_W) '$(srcdir)/src/write_msft.c'; fi`

src/widl-pathtools.o: src/pathtools.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-pathtools.o -MD -MP -MF src/$(DEPDIR)/widl-pathtools.Tpo -c -o src/widl-pathtools.o `test -f 'src/pathtools.c' || echo '$(srcdir)/'`src/pathtools.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-pathtools.Tpo src/$(DEPDIR)/widl-pathtools.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/pathtools.c' object='src/widl-pathtools.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-pathtools.o `test -f 'src/pathtools.c' || echo '$(srcdir)/'`src/pathtools.c

src/widl-pathtools.obj: src/pathtools.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -MT src/widl-pathtools.obj -MD -MP -MF src/$(DEPDIR)/widl-pathtools.Tpo -c -o src/widl-pathtools.obj `if test -f 'src/pathtools.c'; then $(CYGPATH_W) 'src/pathtools.c'; else $(CYGPATH_W) '$(srcdir)/src/pathtools.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) src/$(DEPDIR)/widl-pathtools.Tpo src/$(DEPDIR)/widl-pathtools.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='src/pathtools.c' object='src/widl-pathtools.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(widl_CPPFLAGS) $(CPPFLAGS) $(widl_CFLAGS) $(CFLAGS) -c -o src/widl-pathtools.obj `if test -f 'src/pathtools.c'; then $(CYGPATH_W) 'src/pathtools.c'; else $(CYGPATH_W) '$(srcdir)/src/pathtools.c'; fi`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-zstd: distdir
	tardir=$(distdir) && $(am__tar) | zstd -c $${ZSTD_CLEVEL-$${ZSTD_OPT--19}} >$(distdir).tar.zst
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	*.tar.zst*) \
	  zstd -dc $(distdir).tar.zst | $(am__untar) ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) $(AM_DISTCHECK_DVI_TARGET) \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f src/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/$(am__dirstamp)
	-rm -f src/port/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/port/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
		-rm -f src/$(DEPDIR)/widl-attribute.Po
	-rm -f src/$(DEPDIR)/widl-client.Po
	-rm -f src/$(DEPDIR)/widl-expr.Po
	-rm -f src/$(DEPDIR)/widl-hash.Po
	-rm -f src/$(DEPDIR)/widl-header.Po
	-rm -f src/$(DEPDIR)/widl-parser.tab.Po
	-rm -f src/$(DEPDIR)/widl-parser.yy.Po
	-rm -f src/$(DEPDIR)/widl-pathtools.Po
	-rm -f src/$(DEPDIR)/widl-ppl.yy.Po
	-rm -f src/$(DEPDIR)/widl-ppy.tab.Po
	-rm -f src/$(DEPDIR)/widl-proxy.Po
	-rm -f src/$(DEPDIR)/widl-register.Po
	-rm -f src/$(DEPDIR)/widl-server.Po
	-rm -f src/$(DEPDIR)/widl-typegen.Po
	-rm -f src/$(DEPDIR)/widl-typelib.Po
	-rm -f src/$(DEPDIR)/widl-typetree.Po
	-rm -f src/$(DEPDIR)/widl-utils.Po
	-rm -f src/$(DEPDIR)/widl-widl.Po
	-rm -f src/$(DEPDIR)/widl-wpp.Po
	-rm -f src/$(DEPDIR)/widl-write_msft.Po
	-rm -f src/port/$(DEPDIR)/widl-getopt.Po
	-rm -f src/port/$(DEPDIR)/widl-port.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
		-rm -f src/$(DEPDIR)/widl-attribute.Po
	-rm -f src/$(DEPDIR)/widl-client.Po
	-rm -f src/$(DEPDIR)/widl-expr.Po
	-rm -f src/$(DEPDIR)/widl-hash.Po
	-rm -f src/$(DEPDIR)/widl-header.Po
	-rm -f src/$(DEPDIR)/widl-parser.tab.Po
	-rm -f src/$(DEPDIR)/widl-parser.yy.Po
	-rm -f src/$(DEPDIR)/widl-pathtools.Po
	-rm -f src/$(DEPDIR)/widl-ppl.yy.Po
	-rm -f src/$(DEPDIR)/widl-ppy.tab.Po
	-rm -f src/$(DEPDIR)/widl-proxy.Po
	-rm -f src/$(DEPDIR)/widl-register.Po
	-rm -f src/$(DEPDIR)/widl-server.Po
	-rm -f src/$(DEPDIR)/widl-typegen.Po
	-rm -f src/$(DEPDIR)/widl-typelib.Po
	-rm -f src/$(DEPDIR)/widl-typetree.Po
	-rm -f src/$(DEPDIR)/widl-utils.Po
	-rm -f src/$(DEPDIR)/widl-widl.Po
	-rm -f src/$(DEPDIR)/widl-wpp.Po
	-rm -f src/$(DEPDIR)/widl-write_msft.Po
	-rm -f src/port/$(DEPDIR)/widl-getopt.Po
	-rm -f src/port/$(DEPDIR)/widl-port.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles am--refresh check \
	check-am clean clean-binPROGRAMS clean-cscope clean-generic \
	cscope cscopelist-am ctags ctags-am dist dist-all dist-bzip2 \
	dist-gzip dist-lzip dist-shar dist-tarZ dist-xz dist-zip \
	dist-zstd distcheck distclean distclean-compile \
	distclean-generic distclean-hdr distclean-tags distcleancheck \
	distdir distuninstallcheck dvi dvi-am html html-am info \
	info-am install install-am install-binPROGRAMS install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-binPROGRAMS

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
