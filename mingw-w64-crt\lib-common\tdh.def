;
; Definition file of tdh.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "tdh.dll"
EXPORTS
TdhAggregatePayloadFilters
TdhApplyPayloadFilter
TdhCleanupPayloadEventFilterDescriptor
TdhCloseDecodingHandle
TdhCreatePayloadFilter
TdhDeletePayloadFilter
TdhEnumerateManifestProviderEvents
TdhEnumerateProviderFieldInformation
TdhEnumerateProviderFilters
TdhEnumerateProviders
TdhEnumerateRemoteWBEMProviderFieldInformation
TdhEnumerateRemoteWBEMProviders
TdhFormatProperty
TdhGetAllEventsInformation
TdhGetDecodingParameter
TdhGetEventInformation
TdhGetEventMapInformation
TdhGetManifestEventInformation
TdhGetProperty
TdhGetPropertyOffsetAndSize
TdhGetPropertySize
TdhGetWppMessage
TdhGetWppProperty
TdhLoadManifest
<PERSON>oa<PERSON>anifestFromBinary
TdhLoadManifestFromMemory
TdhOpenDecodingHandle
TdhQueryProviderFieldInformation
TdhQueryRemoteWBEMProviderFieldInformation
TdhSetDecodingParameter
TdhUnloadManifest
TdhUnloadManifestFromMemory
TdhValidatePayloadFilter
TdhpFindMatchClassFromWBEM
TdhpGetBestTraceEventInfoWBEM
TdhpGetEventMapInfoWBEM
