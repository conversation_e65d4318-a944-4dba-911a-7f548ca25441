LIBRARY "NETIO.SYS"
EXPORTS
AgileVPNDispatchTableInit
AgileVPNFindCompartmentIdFromTunnelId
AgileVPNFindTunnelInfoFromInterfaceIndex
CancelMibChangeNotify2
CloseCompartment
ConvertCompartmentGuidToId
ConvertCompartmentIdToGuid
ConvertInterfaceAliasToLuid
ConvertInterfaceGuidToLuid
ConvertInterfaceIndexToLuid
ConvertInterfaceLuidToAlias
ConvertInterfaceLuidToGuid
ConvertInterfaceLuidToIndex
ConvertInterfaceLuidToNameA
ConvertInterfaceLuidToNameW
ConvertInterfaceNameToLuidA
ConvertInterfaceNameToLuidW
ConvertInterfacePhysicalAddressToLuid
ConvertIpv4MaskToLength
ConvertLengthToIpv4Mask
ConvertStringToInterfacePhysicalAddress
CreateAnycastIpAddressEntry
CreateCompartment
CreateIpForwardEntry2
CreateIpNetEntry2
CreateSortedAddressPairs
CreateUnicastIpAddressEntry
DeleteAnycastIpAddressEntry
DeleteCompartment
DeleteIpForwardEntry2
DeleteIpNetEntry2
DeleteUnicastIpAddressEntry
FeAcquireClassifyHandle
FeAcquireWritableLayerDataPointer
FeApplyModifiedLayerData
FeCompleteClassify
FeCopyIncomingValues
FeGetWfpGlobalPtr
FePendClassify
FeReleaseCalloutContextList
FeReleaseClassifyHandle
FlushIpNetTable2
FlushIpPathTable
FreeDnsSettings
FreeInterfaceDnsSettings
FreeMibTable
FsbAllocate
FsbAllocateAtDpcLevel
FsbCreatePool
FsbDestroyPool
FsbFree
FwpmEventProviderCreate0
FwpmEventProviderDestroy0
FwpmEventProviderFireNetEvent0
FwpmEventProviderIsNetEventTypeEnabled0
FwppAdvanceStreamDataPastOffset
FwppCopyStreamDataToBuffer
FwppLogVpnEvent
FwppStreamContinue
FwppStreamDeleteDpcQueue
FwppStreamInject
FwppTruncateStreamDataAfterOffset
GetAnycastIpAddressEntry
GetAnycastIpAddressTable
GetBestInterface
GetBestInterfaceEx
GetBestRoute2
GetDefaultCompartmentId
GetDnsSettings
GetIfEntry2
GetIfEntry2Ex
GetIfStackTable
GetIfTable2
GetIfTable2Ex
GetInterfaceCompartmentId
GetInterfaceDnsSettings
GetInvertedIfStackTable
GetIpForwardEntry2
GetIpForwardTable2
GetIpInterfaceEntry
GetIpInterfaceTable
GetIpNetEntry2
GetIpNetTable2
GetIpNetworkConnectionBandwidthEstimates
GetIpPathEntry
GetIpPathTable
GetMulticastIpAddressEntry
GetMulticastIpAddressTable
GetTeredoPort
GetUnicastIpAddressEntry
GetUnicastIpAddressTable
HfAllocateHandle32
HfCreateFactory
HfDestroyFactory
HfFreeHandle32
HfGetPointerFromHandle32
HfResumeHandle32
HfSuspendHandle32
IPsecGwDispatchTableInit
IPsecGwGetTunnelInfoFromIPInformation
IPsecGwIsUdpEspPacket
IPsecGwProcessSecureNbl
IPsecGwSetCallbackDispatch
IPsecGwTransformClearTextPacket
InitializeCompartmentEntry
InitializeIpForwardEntry
InitializeIpInterfaceEntry
InitializeUnicastIpAddressEntry
InternalCleanupPersistentStore
InternalCreateAnycastIpAddressEntry
InternalCreateIpForwardEntry2
InternalCreateIpNetEntry2
InternalCreateUnicastIpAddressEntry
InternalDeleteAnycastIpAddressEntry
InternalDeleteIpForwardEntry2
InternalDeleteIpNetEntry2
InternalDeleteUnicastIpAddressEntry
InternalFindInterfaceByAddress
InternalGetAnycastIpAddressEntry
InternalGetAnycastIpAddressTable
InternalGetForwardIpTable2
InternalGetIfEntry2
InternalGetIfTable2
InternalGetIpForwardEntry2
InternalGetIpInterfaceEntry
InternalGetIpInterfaceTable
InternalGetIpNetEntry2
InternalGetIpNetTable2
InternalGetMulticastIpAddressEntry
InternalGetMulticastIpAddressTable
InternalGetUnicastIpAddressEntry
InternalGetUnicastIpAddressTable
InternalSetIpForwardEntry2
InternalSetIpInterfaceEntry
InternalSetIpNetEntry2
InternalSetTeredoPort
InternalSetUnicastIpAddressEntry
IoctlKfdAbortTransaction
IoctlKfdAddCache
IoctlKfdAddIndex
IoctlKfdBatchUpdate
IoctlKfdBeginEnumFilters
IoctlKfdCommitTransaction
IoctlKfdDeleteCache
IoctlKfdDeleteIndex
IoctlKfdEndEnumFilters
IoctlKfdMoveFilter
IoctlKfdQueryEnumFilters
IoctlKfdQueryLayerStatistics
IoctlKfdResetState
IoctlKfdSetBfeEngineSd
KfdAddCalloutEntry
KfdAleAcquireEndpointContextFromFlow
KfdAleAcquireFlowHandleForFlow
KfdAleGetTableFromHandle
KfdAleInitializeFlowHandles
KfdAleInitializeFlowTable
KfdAleNotifyFlowDeletion
KfdAleReleaseFlowHandleForFlow
KfdAleRemoveFlowContextTable
KfdAleUninitializeFlowHandles
KfdAleUpdateEndpointContextStatus
KfdAuditEvent
KfdBfeEngineAccessCheck
KfdCheckAcceptBypass
KfdCheckAndCacheAcceptBypass
KfdCheckAndCacheConnectBypass
KfdCheckClassifyNeededAndUpdateEpoch
KfdCheckConnectBypass
KfdCheckOffloadFastLayers
KfdClassify
KfdClassify2
KfdDeRefCallout
KfdDeleteCalloutEntry
KfdDerefFilterContext
KfdDeregisterLayerChangeCallback2
KfdDeregisterLayerEventNotify
KfdDiagnoseEvent
KfdDirectClassify
KfdEnumLayer
KfdFindFilterById
KfdFreeEnumHandle
KfdGetLayerActionFromEnumTemplate
KfdGetLayerCacheEpoch
KfdGetLayerPreclassifyEpoch
KfdGetNextFilter
KfdGetOffloadEpoch
KfdGetRefCallout
KfdIsActiveCallout
KfdIsDiagnoseEventEnabled
KfdIsLayerEmpty
KfdIsLsoOffloadPossibleV4
KfdIsLsoOffloadPossibleV6
KfdIsTfoIncompatibleFilterPresent
KfdIsV4InTransportFastEmpty
KfdIsV4OutTransportFastEmpty
KfdIsV6InTransportFastEmpty
KfdIsV6OutTransportFastEmpty
KfdNotifyFlowDeletion
KfdPreClassify
KfdQueryLayerStats
KfdQueueLruCleanupWorkItem
KfdRegisterLayerChangeCallback2
KfdRegisterLayerEventNotify
KfdRegisterLayerEventNotifyEx
KfdRegisterRscIncompatCalloutNotify
KfdRegisterUsoIncompatCalloutNotify
KfdReleaseCachedFilters
KfdReleaseFilterContext
KfdReleaseTerminatingFilters
KfdSetWfpPerProcContextPtr
KfdToggleFilterActivation
MatchCondition
MdpAllocate
MdpAllocateAtDpcLevel
MdpCreatePool
MdpDestroyPool
MdpFree
NetioAdvanceNetBufferList
NetioAdvanceToLocationInNetBuffer
NetioAllocateAndInitializeStackBlock
NetioAllocateAndReferenceCloneNetBufferList
NetioAllocateAndReferenceCloneNetBufferListEx
NetioAllocateAndReferenceCopyNetBufferListEx
NetioAllocateAndReferenceFragmentNetBufferList
NetioAllocateAndReferenceNetBufferAndNetBufferList
NetioAllocateAndReferenceNetBufferList
NetioAllocateAndReferenceNetBufferListNetBufferMdlAndData
NetioAllocateAndReferenceReassembledNetBufferList
NetioAllocateAndReferenceVacantNetBufferList
NetioAllocateAndReferenceVacantNetBufferListEx
NetioAllocateMdl
NetioAllocateNetBuffer
NetioAllocateNetBufferListNetBufferMdlAndDataPool
NetioAllocateNetBufferMdlAndData
NetioAllocateNetBufferMdlAndDataPool
NetioAllocateOpaquePerProcessorContext
NetioAssociateQoSFlowWithNbl
NetioCleanupNetBufferListInformation
NetioCloseKey
NetioCompleteCloneNetBufferListChain
NetioCompleteCopyNetBufferListChain
NetioCompleteNetBufferAndNetBufferListChain
NetioCompleteNetBufferListChain
NetioCopyNetBufferListInformation
NetioCreateForwardFlow
NetioCreateKey
NetioCreateQoSFlow
NetioCreatevSwitchForwardFlow
NetioDeleteQoSFlow
NetioDereferenceNetBufferList
NetioDereferenceNetBufferListChain
NetioExpandNetBuffer
NetioExtendNetBuffer
NetioFlowAssociateContext
NetioFlowRemoveContext
NetioFlowRetrieveContext
NetioFreeCloneNetBufferList
NetioFreeCopyNetBufferList
NetioFreeMdl
NetioFreeNetBuffer
NetioFreeNetBufferAndNetBufferList
NetioFreeNetBufferList
NetioFreeNetBufferListNetBufferMdlAndDataPool
NetioFreeNetBufferMdlAndDataPool
NetioFreeOpaquePerProcessorContext
NetioFreeStackBlock
NetioGetStatsForQoSFlow
NetioGetSuperTriageBlock
NetioInitNetworkRegistry
NetioInitializeFlowsManager
NetioInitializeMdl
NetioInitializeNetBufferListAndFirstNetBufferContext
NetioInitializeNetBufferListContext
NetioInitializeNetBufferListContextPrimitive
NetioInitializeNetBufferListLibrary
NetioInitializeWorkQueue
NetioInsertWorkQueue
NetioLookupForwardFlow
NetioLookupvSwitchForwardFlow
NetioNcmActiveReferenceRequest
NetioNcmCleanupState
NetioNcmFastActiveReferenceRequest
NetioNcmFastCheckAreAoAcPatternsSupported
NetioNcmFastCheckIsAoAcCapable
NetioNcmFastCheckIsMobileCore
NetioNcmGetAllNotificationChannelContextParameters
NetioNcmHandlePatternEviction
NetioNcmInitializeState
NetioNcmIsOwningProcessRtcApp
NetioNcmNotificationChannelContextRequest
NetioNcmNotifyRedirectOnInterface
NetioNcmPatternCoalescingRequired
NetioNcmQueryRtcPortHint
NetioNcmQueryRtcPortRange
NetioNcmSignalNcContextWorkQueueRoutine
NetioNcmStoreBaseSupportedSlots
NetioNcmStoreRtcPortHint
NetioNcmStoreRtcPortRange
NetioNcmTlObjectRequest
NetioNcmTrackIsLegitimateWake
NetioNrtAssociateContext
NetioNrtDereferenceRecord
NetioNrtDisassociateContext
NetioNrtDispatch
NetioNrtFindAndReferenceRecordByHandle
NetioNrtFindAndReferenceRecordById
NetioNrtFindOrCreateRecord
NetioNrtGetIfIndex
NetioNrtIsIpInRecord
NetioNrtIsPktTaggingEnabled
NetioNrtIsProxyInRecord
NetioNrtIsTrackerDevice
NetioNrtJoinRecords
NetioNrtReferenceRecord
NetioNrtStart
NetioNrtStop
NetioNrtWppLogRecord
NetioOpenKey
NetioPdcActivateNetwork
NetioPdcDeactivateNetwork
NetioPhClampMssOnIpPkt
NetioPhClampMssOnTcpPkt
NetioPhClampMssOnTcpSyn
NetioPhFindTcpOption
NetioPhGetIpUlProtocol
NetioPhIsIcmpErrorForIcmpMessage
NetioPhSkipIpv6ExtHdr
NetioPhSkipToTransHdr
NetioPhUpdateTcpChecksum
NetioQueryNetBufferListTrafficClass
NetioQueryValueKey
NetioReferenceNetBufferList
NetioReferenceNetBufferListChain
NetioRefreshFlow
NetioRegSyncDefaultChangeHandler
NetioRegSyncInterface
NetioRegSyncQueryAndUpdateKeyValue
NetioRegisterProcessorAddCallback
NetioReleaseFlow
NetioRetreatNetBuffer
NetioRetreatNetBufferList
NetioSetTriageBlock
NetioShutdownWorkQueue
NetioStackBlockProcessorAddHandler
NetioUnInitializeFlowsManager
NetioUnInitializeNetBufferListLibrary
NetioUnRegisterProcessorAddCallback
NetioUpdateNetBufferListContext
NetioValidateNetBuffer
NetioValidateNetBufferList
NetioWriteKey
NmrClientAttachProvider
NmrClientDetachProviderComplete
NmrDeregisterClient
NmrDeregisterProvider
NmrProviderDetachClientComplete
NmrRegisterClient
NmrRegisterProvider
NmrWaitForClientDeregisterComplete
NmrWaitForProviderDeregisterComplete
NotifyCompartmentChange
NotifyIpInterfaceChange
NotifyRouteChange2
NotifyStableUnicastIpAddressTable
NotifyTeredoPortChange
NotifyUnicastIpAddressChange
NsiAllocateAndGetTable
NsiClearPersistentSetting
NsiDeregisterChangeNotification
NsiDeregisterChangeNotificationEx
NsiDeregisterLegacyHandler
NsiEnumerateObjectsAllParameters
NsiEnumerateObjectsAllParametersEx
NsiEnumerateObjectsAllPersistentParametersWithMask
NsiFreeTable
NsiGetAllParameters
NsiGetAllParametersEx
NsiGetAllPersistentParametersWithMask
NsiGetModuleHandle
NsiGetObjectSecurity
NsiGetParameter
NsiGetParameterEx
NsiReferenceDefaultObjectSecurity
NsiRegisterChangeNotification
NsiRegisterChangeNotificationEx
NsiRegisterLegacyHandler
NsiResetPersistentSetting
NsiSetAllParameters
NsiSetAllParametersEx
NsiSetAllPersistentParametersWithMask
NsiSetObjectSecurity
NsiSetParameter
NsiSetParameterEx
OpenCompartment
PtCheckTable
PtCreateTable
PtDeleteEntry
PtDestroyTable
PtEnumOverTable
PtGetData
PtGetExactMatch
PtGetKey
PtGetLongestMatch
PtGetNextShorterMatch
PtGetNumNodes
PtInsertEntry
PtSetData
ResolveIpNetEntry2
RtlAllocateDummyMdlChain
RtlCleanupTimerWheel
RtlCleanupTimerWheelEntry
RtlCleanupToeplitzHash
RtlCompute37Hash
RtlComputeToeplitzHash
RtlCopyBufferToMdl
RtlCopyMdlToBuffer
RtlCopyMdlToMdl
RtlCopyMdlToMdlIndirect
RtlDeleteElementGenericTableBasicAvl
RtlEndTimerWheelEnumeration
RtlEnumerateNextTimerWheelEntry
RtlFreeDummyMdlChain
RtlGetNextExpirationTimerWheelTick
RtlGetNextExpiredTimerWheelEntry
RtlIndicateTimerWheelEntryTimerStart
RtlInitializeTimerWheel
RtlInitializeTimerWheelEntry
RtlInitializeTimerWheelEnumeration
RtlInitializeToeplitzHash
RtlInsertElementGenericTableBasicAvl
RtlInvokeStartRoutines
RtlInvokeStopRoutines
RtlIsTimerWheelSuspended
RtlReinitializeToeplitzHash
RtlResumeTimerWheel
RtlReturnTimerWheelEntry
RtlSuspendTimerWheel
RtlUpdateCurrentTimerWheelTick
SetDnsSettings
SetInterfaceDnsSettings
SetIpForwardEntry2
SetIpInterfaceEntry
SetIpNetEntry2
SetUnicastIpAddressEntry
SetWfpDeviceObject
TlDefaultEventAbort
TlDefaultEventConnect
TlDefaultEventDisconnect
TlDefaultEventError
TlDefaultEventInspect
TlDefaultEventNotify
TlDefaultEventReceive
TlDefaultEventReceiveMessages
TlDefaultEventSendBacklog
TlDefaultRequestCancel
TlDefaultRequestCloseEndpoint
TlDefaultRequestConnect
TlDefaultRequestDisconnect
TlDefaultRequestEndpoint
TlDefaultRequestIoControl
TlDefaultRequestIoControlEndpoint
TlDefaultRequestListen
TlDefaultRequestMessage
TlDefaultRequestQueryDispatch
TlDefaultRequestQueryDispatchEndpoint
TlDefaultRequestReceive
TlDefaultRequestReleaseIndicationList
TlDefaultRequestResume
TlDefaultRequestSend
TlDefaultRequestSendMessages
WfpAssociateContextToFlow
WfpAssociateContextToFlowFast
WfpCreateReassemblyContext
WfpDecodedBufferFreeHelper
WfpDeleteEntryLru
WfpExpireEntryLru
WfpFlowToEndpoint
WfpFreeReassemblyContext
WfpGetPacketTagCount
WfpInitializeLeastRecentlyUsedList
WfpInsertEntryLru
WfpLruProcessExpiredEndpoint
WfpLruQueueLruCleanupWorkItemForContext
WfpNblInfoAlloc
WfpNblInfoCleanup
WfpNblInfoClearFlags
WfpNblInfoClone
WfpNblInfoDestroyIfUnused
WfpNblInfoDispatchTableClear
WfpNblInfoDispatchTableSet
WfpNblInfoGet
WfpNblInfoGetFlags
WfpNblInfoInit
WfpNblInfoSet
WfpNblInfoSetFlags
WfpNrptTriggerDecodeHelper
WfpPacketTagCountIncrement
WfpProcessFlowDelete
WfpRefreshEntryLru
WfpReleaseFlowLocation
WfpRemoveContextFromFlow
WfpRemoveContextFromFlowFast
WfpReserveFlowLocation
WfpScavangeLeastRecentlyUsedList
WfpSetBucketsToEmptyLru
WfpSetConfigureParametersDecodeHelper
WfpSetDisconnectDecodeHelper
WfpSetVpnTriggerFilePathsDecodeHelper
WfpSetVpnTriggerSecurityDescriptorDecodeHelper
WfpSetVpnTriggerSidsDecodeHelper
WfpStartStreamShim
WfpStopStreamShim
WfpStreamEndpointCleanupBegin
WfpStreamInspectDisconnect
WfpStreamInspectReceive
WfpStreamInspectRemoteDisconnect
WfpStreamInspectSend
WfpStreamIsFilterPresent
WfpTransferReassemblyContextForFragments
WfpTransferReassemblyContextUponCompletion
WfpUninitializeLeastRecentlyUsedList
WskCaptureProviderNPI
WskDeregister
WskQueryProviderCharacteristics
WskRegister
WskReleaseProviderNPI
if_indextoname
if_nametoindex
