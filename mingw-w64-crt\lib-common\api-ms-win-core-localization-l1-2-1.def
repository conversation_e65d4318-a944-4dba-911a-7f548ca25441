LIBRARY api-ms-win-core-localization-l1-2-1

EXPORTS

EnumSystemGeoID
EnumSystemLocalesA
EnumSystemLocalesEx
EnumSystemLocalesW
FindNLSStringEx
FormatMessageA
FormatMessageW
GetACP
GetCalendarInfoEx
GetCPInfo
GetCPInfoExW
GetGeoInfoW
GetLocaleInfoA
GetLocaleInfoEx
GetLocaleInfoW
GetNLSVersionEx
GetOEMCP
GetSystemDefaultLangID
GetSystemDefaultLCID
GetThreadLocale
GetUserDefaultLangID
GetUserDefaultLCID
GetUserDefaultLocaleName
GetUserGeoID
IdnToAscii
IdnToUnicode
IsDBCSLeadByte
IsDBCSLeadByteEx
IsNLSDefinedString
IsValidCodePage
IsValidLocale
IsValidLocaleName
IsValidNLSVersion
LCMapStringA
LCMapStringEx
LCMapStringW
LocaleNameToLCID
ResolveLocaleName
VerLanguageNameA
VerLanguageNameW
