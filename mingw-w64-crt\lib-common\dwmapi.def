;
; Definition file of dwmapi.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "dwmapi.dll"
EXPORTS
DwmpDxGetWindowSharedSurface
DwmpDxUpdateWindowSharedSurface
DwmEnableComposition
DwmAttachMilContent
DwmDefWindowProc
DwmDetachMilContent
DwmEnableBlurBehindWindow
DwmEnableMMCSS
DwmExtendFrameIntoClientArea
DwmFlush
DwmGetColorizationColor
DwmpDxBindSwapChain
DwmpDxUnbindSwapChain
DwmpDxgiIsThreadDesktopComposited
DwmGetCompositionTimingInfo
DwmGetGraphicsStreamClient
DwmpDxUpdateWindowRedirectionBltSurface
DwmpRenderFlick
DwmpAllocateSecurityDescriptor
DwmpFreeSecurityDescriptor
DwmpEnableDDASupport
Dwm<PERSON>treamTransformHint
DwmTetherTextContact
DwmGetTransportAttributes
DwmGetWindowAttribute
DwmInvalidateIconicBitmaps
DwmIsCompositionEnabled
DwmModifyPreviousDxFrameDuration
DwmQueryThumbnailSourceSize
DwmRegisterThumbnail
DwmRenderGesture
DwmSetDxFrameDuration
DwmSetIconicLivePreviewBitmap
DwmSetIconicThumbnail
DwmSetPresentParameters
DwmSetWindowAttribute
DwmShowContact
DwmTetherContact
DwmTransitionOwnedWindow
DwmUnregisterThumbnail
DwmUpdateThumbnailProperties
