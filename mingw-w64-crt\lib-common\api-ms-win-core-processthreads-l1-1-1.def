LIBRARY api-ms-win-core-processthreads-l1-1-1

EXPORTS

CreateProcessA
CreateProcessAsUserW
CreateProcessW
CreateThread
ExitProcess
ExitThread
FlushInstructionCache
FlushProcessWriteBuffers
GetCurrentProcess
GetCurrentProcessId
GetCurrentProcessorNumber
GetCurrentProcessorNumberEx
GetCurrentThread
GetCurrentThreadId
GetCurrentThreadStackLimits
GetExitCodeProcess
GetExitCodeThread
GetPriorityClass
GetProcessId
GetProcessMitigationPolicy
GetProcessTimes
GetStartupInfoW
GetThreadContext
GetThreadId
GetThreadIdealProcessorEx
GetThreadPriority
GetThreadPriorityBoost
GetThreadTimes
IsProcessorFeaturePresent
OpenProcess
OpenProcessToken
OpenThread
OpenThreadToken
ProcessIdToSessionId
QueueUserAPC
ResumeThread
SetPriorityClass
SetProcessMitigationPolicy
SetThreadContext
SetThreadIdealProcessorEx
SetThreadPriority
SetThreadPriorityBoost
SetThreadStackGuarantee
SetThreadToken
SuspendThread
SwitchToThread
TerminateProcess
TerminateThread
TlsAlloc
TlsFree
TlsGetValue
TlsSetValue
