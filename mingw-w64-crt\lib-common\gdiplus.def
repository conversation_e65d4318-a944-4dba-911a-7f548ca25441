;
; Definition file of gdiplus.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "gdiplus.dll"
EXPORTS
GdipAddPathArc
GdipAddPathArcI
GdipAddPathBezier
GdipAddPathBezierI
GdipAddPathBeziers
GdipAddPathBeziersI
GdipAddPathClosedCurve
GdipAddPathClosedCurve2
GdipAddPathClosedCurve2I
GdipAddPathClosedCurveI
GdipAddPathCurve
GdipAddPathCurve2
GdipAddPathCurve2I
GdipAddPathCurve3
GdipAddPathCurve3I
GdipAddPathCurveI
GdipAddPathEllipse
GdipAddPathEllipseI
GdipAddPathLine
GdipAddPathLine2
GdipAddPathLine2I
GdipAddPathLineI
GdipAddPathPath
GdipAddPathPie
GdipAddPathPieI
Gdi<PERSON>Polygon
GdipAddPathPolygonI
GdipAddPathRectangle
GdipAddPathRectangleI
GdipAddPathRectangles
GdipAddPathRectanglesI
GdipAddPathString
GdipAddPathStringI
GdipAlloc
GdipBeginContainer
GdipBeginContainer2
GdipBeginContainerI
GdipBitmapGetPixel
GdipBitmapLockBits
GdipBitmapSetPixel
GdipBitmapSetResolution
GdipBitmapUnlockBits
GdipClearPathMarkers
GdipCloneBitmapArea
GdipCloneBitmapAreaI
GdipCloneBrush
GdipCloneCustomLineCap
GdipCloneFont
GdipCloneFontFamily
GdipCloneImage
GdipCloneImageAttributes
GdipCloneMatrix
GdipClonePath
GdipClonePen
GdipCloneRegion
GdipCloneStringFormat
GdipClosePathFigure
GdipClosePathFigures
GdipCombineRegionPath
GdipCombineRegionRect
GdipCombineRegionRectI
GdipCombineRegionRegion
GdipComment
GdipCreateAdjustableArrowCap
GdipCreateBitmapFromDirectDrawSurface
GdipCreateBitmapFromFile
GdipCreateBitmapFromFileICM
GdipCreateBitmapFromGdiDib
GdipCreateBitmapFromGraphics
GdipCreateBitmapFromHBITMAP
GdipCreateBitmapFromHICON
GdipCreateBitmapFromResource
GdipCreateBitmapFromScan0
GdipCreateBitmapFromStream
GdipCreateBitmapFromStreamICM
GdipCreateCachedBitmap
GdipCreateCustomLineCap
GdipCreateFont
GdipCreateFontFamilyFromName
GdipCreateFontFromDC
GdipCreateFontFromLogfontA
GdipCreateFontFromLogfontW
GdipCreateFromHDC
GdipCreateFromHDC2
GdipCreateFromHWND
GdipCreateFromHWNDICM
GdipCreateHBITMAPFromBitmap
GdipCreateHICONFromBitmap
GdipCreateHalftonePalette
GdipCreateHatchBrush
GdipCreateImageAttributes
GdipCreateLineBrush
GdipCreateLineBrushFromRect
GdipCreateLineBrushFromRectI
GdipCreateLineBrushFromRectWithAngle
GdipCreateLineBrushFromRectWithAngleI
GdipCreateLineBrushI
GdipCreateMatrix
GdipCreateMatrix2
GdipCreateMatrix3
GdipCreateMatrix3I
GdipCreateMetafileFromEmf
GdipCreateMetafileFromFile
GdipCreateMetafileFromStream
GdipCreateMetafileFromWmf
GdipCreateMetafileFromWmfFile
GdipCreatePath
GdipCreatePath2
GdipCreatePath2I
GdipCreatePathGradient
GdipCreatePathGradientFromPath
GdipCreatePathGradientI
GdipCreatePathIter
GdipCreatePen1
GdipCreatePen2
GdipCreateRegion
GdipCreateRegionHrgn
GdipCreateRegionPath
GdipCreateRegionRect
GdipCreateRegionRectI
GdipCreateRegionRgnData
GdipCreateSolidFill
GdipCreateStreamOnFile
GdipCreateStringFormat
GdipCreateTexture
GdipCreateTexture2
GdipCreateTexture2I
GdipCreateTextureIA
GdipCreateTextureIAI
GdipDeleteBrush
GdipDeleteCachedBitmap
GdipDeleteCustomLineCap
GdipDeleteFont
GdipDeleteFontFamily
GdipDeleteGraphics
GdipDeleteMatrix
GdipDeletePath
GdipDeletePathIter
GdipDeletePen
GdipDeletePrivateFontCollection
GdipDeleteRegion
GdipDeleteStringFormat
GdipDisposeImage
GdipDisposeImageAttributes
GdipDrawArc
GdipDrawArcI
GdipDrawBezier
GdipDrawBezierI
GdipDrawBeziers
GdipDrawBeziersI
GdipDrawCachedBitmap
GdipDrawClosedCurve
GdipDrawClosedCurve2
GdipDrawClosedCurve2I
GdipDrawClosedCurveI
GdipDrawCurve
GdipDrawCurve2
GdipDrawCurve2I
GdipDrawCurve3
GdipDrawCurve3I
GdipDrawCurveI
GdipDrawDriverString
GdipDrawEllipse
GdipDrawEllipseI
GdipDrawImage
GdipDrawImageI
GdipDrawImagePointRect
GdipDrawImagePointRectI
GdipDrawImagePoints
GdipDrawImagePointsI
GdipDrawImagePointsRect
GdipDrawImagePointsRectI
GdipDrawImageRect
GdipDrawImageRectI
GdipDrawImageRectRect
GdipDrawImageRectRectI
GdipDrawLine
GdipDrawLineI
GdipDrawLines
GdipDrawLinesI
GdipDrawPath
GdipDrawPie
GdipDrawPieI
GdipDrawPolygon
GdipDrawPolygonI
GdipDrawRectangle
GdipDrawRectangleI
GdipDrawRectangles
GdipDrawRectanglesI
GdipDrawString
GdipEmfToWmfBits
GdipEndContainer
GdipEnumerateMetafileDestPoint
GdipEnumerateMetafileDestPointI
GdipEnumerateMetafileDestPoints
GdipEnumerateMetafileDestPointsI
GdipEnumerateMetafileDestRect
GdipEnumerateMetafileDestRectI
GdipEnumerateMetafileSrcRectDestPoint
GdipEnumerateMetafileSrcRectDestPointI
GdipEnumerateMetafileSrcRectDestPoints
GdipEnumerateMetafileSrcRectDestPointsI
GdipEnumerateMetafileSrcRectDestRect
GdipEnumerateMetafileSrcRectDestRectI
GdipFillClosedCurve
GdipFillClosedCurve2
GdipFillClosedCurve2I
GdipFillClosedCurveI
GdipFillEllipse
GdipFillEllipseI
GdipFillPath
GdipFillPie
GdipFillPieI
GdipFillPolygon
GdipFillPolygon2
GdipFillPolygon2I
GdipFillPolygonI
GdipFillRectangle
GdipFillRectangleI
GdipFillRectangles
GdipFillRectanglesI
GdipFillRegion
GdipFlattenPath
GdipFlush
GdipFree
GdipGetAdjustableArrowCapFillState
GdipGetAdjustableArrowCapHeight
GdipGetAdjustableArrowCapMiddleInset
GdipGetAdjustableArrowCapWidth
GdipGetAllPropertyItems
GdipGetBrushType
GdipGetCellAscent
GdipGetCellDescent
GdipGetClip
GdipGetClipBounds
GdipGetClipBoundsI
GdipGetCompositingMode
GdipGetCompositingQuality
GdipGetCustomLineCapBaseCap
GdipGetCustomLineCapBaseInset
GdipGetCustomLineCapStrokeCaps
GdipGetCustomLineCapStrokeJoin
GdipGetCustomLineCapType
GdipGetCustomLineCapWidthScale
GdipGetDC
GdipGetDpiX
GdipGetDpiY
GdipGetEmHeight
GdipGetEncoderParameterList
GdipGetEncoderParameterListSize
GdipGetFamily
GdipGetFamilyName
GdipGetFontCollectionFamilyCount
GdipGetFontCollectionFamilyList
GdipGetFontHeight
GdipGetFontHeightGivenDPI
GdipGetFontSize
GdipGetFontStyle
GdipGetFontUnit
GdipGetGenericFontFamilyMonospace
GdipGetGenericFontFamilySansSerif
GdipGetGenericFontFamilySerif
GdipGetHatchBackgroundColor
GdipGetHatchForegroundColor
GdipGetHatchStyle
GdipGetHemfFromMetafile
GdipGetImageAttributesAdjustedPalette
GdipGetImageBounds
GdipGetImageDecoders
GdipGetImageDecodersSize
GdipGetImageDimension
GdipGetImageEncoders
GdipGetImageEncodersSize
GdipGetImageFlags
GdipGetImageGraphicsContext
GdipGetImageHeight
GdipGetImageHorizontalResolution
GdipGetImagePalette
GdipGetImagePaletteSize
GdipGetImagePixelFormat
GdipGetImageRawFormat
GdipGetImageThumbnail
GdipGetImageType
GdipGetImageVerticalResolution
GdipGetImageWidth
GdipGetInterpolationMode
GdipGetLineBlend
GdipGetLineBlendCount
GdipGetLineColors
GdipGetLineGammaCorrection
GdipGetLinePresetBlend
GdipGetLinePresetBlendCount
GdipGetLineRect
GdipGetLineRectI
GdipGetLineSpacing
GdipGetLineTransform
GdipGetLineWrapMode
GdipGetLogFontA
GdipGetLogFontW
GdipGetMatrixElements
GdipGetMetafileDownLevelRasterizationLimit
GdipGetMetafileHeaderFromEmf
GdipGetMetafileHeaderFromFile
GdipGetMetafileHeaderFromMetafile
GdipGetMetafileHeaderFromStream
GdipGetMetafileHeaderFromWmf
GdipGetNearestColor
GdipGetPageScale
GdipGetPageUnit
GdipGetPathData
GdipGetPathFillMode
GdipGetPathGradientBlend
GdipGetPathGradientBlendCount
GdipGetPathGradientCenterColor
GdipGetPathGradientCenterPoint
GdipGetPathGradientCenterPointI
GdipGetPathGradientFocusScales
GdipGetPathGradientGammaCorrection
GdipGetPathGradientPath
GdipGetPathGradientPointCount
GdipGetPathGradientPresetBlend
GdipGetPathGradientPresetBlendCount
GdipGetPathGradientRect
GdipGetPathGradientRectI
GdipGetPathGradientSurroundColorCount
GdipGetPathGradientSurroundColorsWithCount
GdipGetPathGradientTransform
GdipGetPathGradientWrapMode
GdipGetPathLastPoint
GdipGetPathPoints
GdipGetPathPointsI
GdipGetPathTypes
GdipGetPathWorldBounds
GdipGetPathWorldBoundsI
GdipGetPenBrushFill
GdipGetPenColor
GdipGetPenCompoundArray
GdipGetPenCompoundCount
GdipGetPenCustomEndCap
GdipGetPenCustomStartCap
GdipGetPenDashArray
GdipGetPenDashCap197819
GdipGetPenDashCount
GdipGetPenDashOffset
GdipGetPenDashStyle
GdipGetPenEndCap
GdipGetPenFillType
GdipGetPenLineJoin
GdipGetPenMiterLimit
GdipGetPenMode
GdipGetPenStartCap
GdipGetPenTransform
GdipGetPenUnit
GdipGetPenWidth
GdipGetPixelOffsetMode
GdipGetPointCount
GdipGetPropertyCount
GdipGetPropertyIdList
GdipGetPropertyItem
GdipGetPropertyItemSize
GdipGetPropertySize
GdipGetRegionBounds
GdipGetRegionBoundsI
GdipGetRegionData
GdipGetRegionDataSize
GdipGetRegionHRgn
GdipGetRegionScans
GdipGetRegionScansCount
GdipGetRegionScansI
GdipGetRenderingOrigin
GdipGetSmoothingMode
GdipGetSolidFillColor
GdipGetStringFormatAlign
GdipGetStringFormatDigitSubstitution
GdipGetStringFormatFlags
GdipGetStringFormatHotkeyPrefix
GdipGetStringFormatLineAlign
GdipGetStringFormatMeasurableCharacterRangeCount
GdipGetStringFormatTabStopCount
GdipGetStringFormatTabStops
GdipGetStringFormatTrimming
GdipGetTextContrast
GdipGetTextRenderingHint
GdipGetTextureImage
GdipGetTextureTransform
GdipGetTextureWrapMode
GdipGetVisibleClipBounds
GdipGetVisibleClipBoundsI
GdipGetWorldTransform
GdipGraphicsClear
GdipImageForceValidation
GdipImageGetFrameCount
GdipImageGetFrameDimensionsCount
GdipImageGetFrameDimensionsList
GdipImageRotateFlip
GdipImageSelectActiveFrame
GdipInvertMatrix
GdipIsClipEmpty
GdipIsEmptyRegion
GdipIsEqualRegion
GdipIsInfiniteRegion
GdipIsMatrixEqual
GdipIsMatrixIdentity
GdipIsMatrixInvertible
GdipIsOutlineVisiblePathPoint
GdipIsOutlineVisiblePathPointI
GdipIsStyleAvailable
GdipIsVisibleClipEmpty
GdipIsVisiblePathPoint
GdipIsVisiblePathPointI
GdipIsVisiblePoint
GdipIsVisiblePointI
GdipIsVisibleRect
GdipIsVisibleRectI
GdipIsVisibleRegionPoint
GdipIsVisibleRegionPointI
GdipIsVisibleRegionRect
GdipIsVisibleRegionRectI
GdipLoadImageFromFile
GdipLoadImageFromFileICM
GdipLoadImageFromStream
GdipLoadImageFromStreamICM
GdipMeasureCharacterRanges
GdipMeasureDriverString
GdipMeasureString
GdipMultiplyLineTransform
GdipMultiplyMatrix
GdipMultiplyPathGradientTransform
GdipMultiplyPenTransform
GdipMultiplyTextureTransform
GdipMultiplyWorldTransform
GdipNewInstalledFontCollection
GdipNewPrivateFontCollection
GdipPathIterCopyData
GdipPathIterEnumerate
GdipPathIterGetCount
GdipPathIterGetSubpathCount
GdipPathIterHasCurve
GdipPathIterIsValid
GdipPathIterNextMarker
GdipPathIterNextMarkerPath
GdipPathIterNextPathType
GdipPathIterNextSubpath
GdipPathIterNextSubpathPath
GdipPathIterRewind
GdipPlayMetafileRecord
GdipPrivateAddFontFile
GdipPrivateAddMemoryFont
GdipRecordMetafile
GdipRecordMetafileFileName
GdipRecordMetafileFileNameI
GdipRecordMetafileI
GdipRecordMetafileStream
GdipRecordMetafileStreamI
GdipReleaseDC
GdipRemovePropertyItem
GdipResetClip
GdipResetImageAttributes
GdipResetLineTransform
GdipResetPageTransform
GdipResetPath
GdipResetPathGradientTransform
GdipResetPenTransform
GdipResetTextureTransform
GdipResetWorldTransform
GdipRestoreGraphics
GdipReversePath
GdipRotateLineTransform
GdipRotateMatrix
GdipRotatePathGradientTransform
GdipRotatePenTransform
GdipRotateTextureTransform
GdipRotateWorldTransform
GdipSaveAdd
GdipSaveAddImage
GdipSaveGraphics
GdipSaveImageToFile
GdipSaveImageToStream
GdipScaleLineTransform
GdipScaleMatrix
GdipScalePathGradientTransform
GdipScalePenTransform
GdipScaleTextureTransform
GdipScaleWorldTransform
GdipSetAdjustableArrowCapFillState
GdipSetAdjustableArrowCapHeight
GdipSetAdjustableArrowCapMiddleInset
GdipSetAdjustableArrowCapWidth
GdipSetClipGraphics
GdipSetClipHrgn
GdipSetClipPath
GdipSetClipRect
GdipSetClipRectI
GdipSetClipRegion
GdipSetCompositingMode
GdipSetCompositingQuality
GdipSetCustomLineCapBaseCap
GdipSetCustomLineCapBaseInset
GdipSetCustomLineCapStrokeCaps
GdipSetCustomLineCapStrokeJoin
GdipSetCustomLineCapWidthScale
GdipSetEmpty
GdipSetImageAttributesCachedBackground
GdipSetImageAttributesColorKeys
GdipSetImageAttributesColorMatrix
GdipSetImageAttributesGamma
GdipSetImageAttributesNoOp
GdipSetImageAttributesOutputChannel
GdipSetImageAttributesOutputChannelColorProfile
GdipSetImageAttributesRemapTable
GdipSetImageAttributesThreshold
GdipSetImageAttributesToIdentity
GdipSetImageAttributesWrapMode
GdipSetImagePalette
GdipSetInfinite
GdipSetInterpolationMode
GdipSetLineBlend
GdipSetLineColors
GdipSetLineGammaCorrection
GdipSetLineLinearBlend
GdipSetLinePresetBlend
GdipSetLineSigmaBlend
GdipSetLineTransform
GdipSetLineWrapMode
GdipSetMatrixElements
GdipSetMetafileDownLevelRasterizationLimit
GdipSetPageScale
GdipSetPageUnit
GdipSetPathFillMode
GdipSetPathGradientBlend
GdipSetPathGradientCenterColor
GdipSetPathGradientCenterPoint
GdipSetPathGradientCenterPointI
GdipSetPathGradientFocusScales
GdipSetPathGradientGammaCorrection
GdipSetPathGradientLinearBlend
GdipSetPathGradientPath
GdipSetPathGradientPresetBlend
GdipSetPathGradientSigmaBlend
GdipSetPathGradientSurroundColorsWithCount
GdipSetPathGradientTransform
GdipSetPathGradientWrapMode
GdipSetPathMarker
GdipSetPenBrushFill
GdipSetPenColor
GdipSetPenCompoundArray
GdipSetPenCustomEndCap
GdipSetPenCustomStartCap
GdipSetPenDashArray
GdipSetPenDashCap197819
GdipSetPenDashOffset
GdipSetPenDashStyle
GdipSetPenEndCap
GdipSetPenLineCap197819
GdipSetPenLineJoin
GdipSetPenMiterLimit
GdipSetPenMode
GdipSetPenStartCap
GdipSetPenTransform
GdipSetPenUnit
GdipSetPenWidth
GdipSetPixelOffsetMode
GdipSetPropertyItem
GdipSetRenderingOrigin
GdipSetSmoothingMode
GdipSetSolidFillColor
GdipSetStringFormatAlign
GdipSetStringFormatDigitSubstitution
GdipSetStringFormatFlags
GdipSetStringFormatHotkeyPrefix
GdipSetStringFormatLineAlign
GdipSetStringFormatMeasurableCharacterRanges
GdipSetStringFormatTabStops
GdipSetStringFormatTrimming
GdipSetTextContrast
GdipSetTextRenderingHint
GdipSetTextureTransform
GdipSetTextureWrapMode
GdipSetWorldTransform
GdipShearMatrix
GdipStartPathFigure
GdipStringFormatGetGenericDefault
GdipStringFormatGetGenericTypographic
GdipTestControl
GdipTransformMatrixPoints
GdipTransformMatrixPointsI
GdipTransformPath
GdipTransformPoints
GdipTransformPointsI
GdipTransformRegion
GdipTranslateClip
GdipTranslateClipI
GdipTranslateLineTransform
GdipTranslateMatrix
GdipTranslatePathGradientTransform
GdipTranslatePenTransform
GdipTranslateRegion
GdipTranslateRegionI
GdipTranslateTextureTransform
GdipTranslateWorldTransform
GdipVectorTransformMatrixPoints
GdipVectorTransformMatrixPointsI
GdipWarpPath
GdipWidenPath
GdipWindingModeOutline
GdiplusNotificationHook
GdiplusNotificationUnhook
GdiplusShutdown
GdiplusStartup
GdipFindFirstImageItem
GdipFindNextImageItem
GdipGetImageItemData
GdipCreateEffect
GdipDeleteEffect
GdipGetEffectParameterSize
GdipGetEffectParameters
GdipSetEffectParameters
GdipBitmapCreateApplyEffect
GdipBitmapApplyEffect
GdipBitmapGetHistogram
GdipBitmapGetHistogramSize
GdipBitmapConvertFormat
GdipInitializePalette
GdipImageSetAbort
GdipGraphicsSetAbort
GdipDrawImageFX
GdipConvertToEmfPlus
GdipConvertToEmfPlusToFile
GdipConvertToEmfPlusToStream
GdipPlayTSClientRecord
