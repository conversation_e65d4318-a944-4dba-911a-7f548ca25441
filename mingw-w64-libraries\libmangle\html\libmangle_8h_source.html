<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: include/libmangle.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<h1>include/libmangle.h</h1><a href="libmangle_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">/*</span>
<a name="l00002"></a>00002 <span class="comment">   Copyright (c) 2009, 2010 mingw-w64 project</span>
<a name="l00003"></a>00003 <span class="comment"></span>
<a name="l00004"></a>00004 <span class="comment">   Contributing authors: Kai Tietz, Jonathan Yong</span>
<a name="l00005"></a>00005 <span class="comment"></span>
<a name="l00006"></a>00006 <span class="comment">   Permission is hereby granted, free of charge, to any person obtaining a</span>
<a name="l00007"></a>00007 <span class="comment">   copy of this software and associated documentation files (the &quot;Software&quot;),</span>
<a name="l00008"></a>00008 <span class="comment">   to deal in the Software without restriction, including without limitation</span>
<a name="l00009"></a>00009 <span class="comment">   the rights to use, copy, modify, merge, publish, distribute, sublicense,</span>
<a name="l00010"></a>00010 <span class="comment">   and/or sell copies of the Software, and to permit persons to whom the</span>
<a name="l00011"></a>00011 <span class="comment">   Software is furnished to do so, subject to the following conditions:</span>
<a name="l00012"></a>00012 <span class="comment"></span>
<a name="l00013"></a>00013 <span class="comment">   The above copyright notice and this permission notice shall be included in</span>
<a name="l00014"></a>00014 <span class="comment">   all copies or substantial portions of the Software.</span>
<a name="l00015"></a>00015 <span class="comment"></span>
<a name="l00016"></a>00016 <span class="comment">   THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR</span>
<a name="l00017"></a>00017 <span class="comment">   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,</span>
<a name="l00018"></a>00018 <span class="comment">   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE</span>
<a name="l00019"></a>00019 <span class="comment">   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER</span>
<a name="l00020"></a>00020 <span class="comment">   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING</span>
<a name="l00021"></a>00021 <span class="comment">   FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER</span>
<a name="l00022"></a>00022 <span class="comment">   DEALINGS IN THE SOFTWARE.</span>
<a name="l00023"></a>00023 <span class="comment">*/</span>
<a name="l00024"></a>00024 
<a name="l00025"></a>00025 <span class="preprocessor">#ifndef _LIBMANGLE_HXX</span>
<a name="l00026"></a>00026 <span class="preprocessor"></span><span class="preprocessor">#define _LIBMANGLE_HXX</span>
<a name="l00027"></a>00027 <span class="preprocessor"></span>
<a name="l00028"></a>00028 <span class="preprocessor">#ifdef __cplusplus</span>
<a name="l00029"></a>00029 <span class="preprocessor"></span><span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {
<a name="l00030"></a>00030 <span class="preprocessor">#endif</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00038"></a><a class="code" href="libmangle_8h.html#af17e2fe323e27ccf4827813ee0c8612e">00038</a> <span class="keyword">typedef</span> <span class="keywordtype">void</span> *<a class="code" href="libmangle_8h.html#af17e2fe323e27ccf4827813ee0c8612e">libmangle_gc_t</a>;
<a name="l00039"></a>00039 
<a name="l00046"></a><a class="code" href="structlibmangle__gc__context__t.html">00046</a> <span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> {
<a name="l00047"></a><a class="code" href="structlibmangle__gc__context__t.html#a87a971b39a14440678c40974f56bbf08">00047</a>   libmangle_gc_t <a class="code" href="structlibmangle__gc__context__t.html#a87a971b39a14440678c40974f56bbf08">head</a>;                
<a name="l00048"></a><a class="code" href="structlibmangle__gc__context__t.html#a26e6a7692028d660e4a8224e14f0c3f6">00048</a>   libmangle_gc_t <a class="code" href="structlibmangle__gc__context__t.html#a26e6a7692028d660e4a8224e14f0c3f6">tail</a>;                
<a name="l00049"></a>00049 } <a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a>;
<a name="l00050"></a>00050 
<a name="l00057"></a><a class="code" href="libmangle_8h.html#a7c9c7d368eb1f52cac14457766a01cc7">00057</a> <span class="keyword">typedef</span> <span class="keywordtype">void</span> *<a class="code" href="libmangle_8h.html#a7c9c7d368eb1f52cac14457766a01cc7">libmangle_tokens_t</a>;
<a name="l00058"></a>00058 
<a name="l00064"></a>00064 <span class="keywordtype">void</span> <a class="code" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc);
<a name="l00065"></a>00065 
<a name="l00071"></a>00071 <a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *<a class="code" href="libmangle_8h.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle_generate_gc</a> (<span class="keywordtype">void</span>);
<a name="l00072"></a>00072 
<a name="l00078"></a>00078 <span class="keywordtype">void</span> <a class="code" href="libmangle_8h.html#ab22601869037438e47eca7186a4cef65">libmangle_dump_tok</a> (FILE *fp, libmangle_tokens_t p);
<a name="l00079"></a>00079 
<a name="l00086"></a>00086 <span class="keywordtype">void</span> <a class="code" href="libmangle_8h.html#a2c4d83f71d35e434250eb2779e29ef29">libmangle_print_decl</a> (FILE *fp, libmangle_tokens_t p);
<a name="l00087"></a>00087 
<a name="l00095"></a>00095 <span class="keywordtype">char</span> *<a class="code" href="libmangle_8h.html#abf4af1b2e483a32beb147474853e696b">libmangle_sprint_decl</a> (libmangle_tokens_t r);
<a name="l00096"></a>00096 
<a name="l00107"></a>00107 libmangle_tokens_t <a class="code" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle_decode_ms_name</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <span class="keyword">const</span> <span class="keywordtype">char</span> *name);
<a name="l00108"></a>00108 <span class="keywordtype">char</span> *<a class="code" href="libmangle_8h.html#ad6e58fecfca8cc312a2b09a44e3748fb">libmangle_encode_ms_name</a> (<a class="code" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, libmangle_tokens_t tok);
<a name="l00109"></a>00109 
<a name="l00110"></a>00110 <span class="preprocessor">#ifdef __cplusplus</span>
<a name="l00111"></a>00111 <span class="preprocessor"></span>}
<a name="l00112"></a>00112 <span class="preprocessor">#endif</span>
<a name="l00113"></a>00113 <span class="preprocessor"></span>
<a name="l00114"></a>00114 <span class="preprocessor">#endif</span>
</pre></div></div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
