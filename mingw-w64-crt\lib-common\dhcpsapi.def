;
; Definition file of DHCPSAPI.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "DHCPSAPI.DLL"
EXPORTS
DhcpAddFilterV4
DhcpAddMScopeElement
DhcpAddSecurityGroup
DhcpAddServer
DhcpAddSubnetElement
DhcpAddSubnetElementV4
DhcpAddSubnetElementV5
DhcpAddSubnetElementV6
DhcpAuditLogGetParams
DhcpAuditLogSetParams
DhcpCreateClass
DhcpCreateClassV6
DhcpCreateClientInfo
DhcpCreateClientInfoV4
DhcpCreateClientInfoVQ
DhcpCreateOption
DhcpCreateOptionV5
DhcpCreateOptionV6
DhcpCreateSubnet
DhcpCreateSubnetV6
DhcpCreateSubnetVQ
DhcpDeleteClass
DhcpDeleteClassV6
DhcpDeleteClientInfo
DhcpDeleteClientInfoV6
DhcpDeleteFilterV4
DhcpDeleteMClientInfo
DhcpDeleteMScope
DhcpDeleteServer
DhcpDeleteSubnet
DhcpDeleteSubnetV6
DhcpDeleteSuperScopeV4
DhcpDsCleanup
DhcpDsClearHostServerEntries
DhcpDsInit
DhcpEnumClasses
DhcpEnumClassesV6
DhcpEnumFilterV4
DhcpEnumMScopeClients
DhcpEnumMScopeElements
DhcpEnumMScopes
DhcpEnumOptionValues
DhcpEnumOptionValuesV5
DhcpEnumOptionValuesV6
DhcpEnumOptions
DhcpEnumOptionsV5
DhcpEnumOptionsV6
DhcpEnumServers
DhcpEnumSubnetClients
DhcpEnumSubnetClientsFilterStatusInfo
DhcpEnumSubnetClientsV4
DhcpEnumSubnetClientsV5
DhcpEnumSubnetClientsV6
DhcpEnumSubnetClientsVQ
DhcpEnumSubnetElements
DhcpEnumSubnetElementsV4
DhcpEnumSubnetElementsV5
DhcpEnumSubnetElementsV6
DhcpEnumSubnets
DhcpEnumSubnetsV6
DhcpGetAllOptionValues
DhcpGetAllOptionValuesV6
DhcpGetAllOptions
DhcpGetAllOptionsV6
DhcpGetClassInfo
DhcpGetClientInfo
DhcpGetClientInfoV4
DhcpGetClientInfoV6
DhcpGetClientInfoVQ
DhcpGetClientOptions
DhcpGetFilterV4
DhcpGetMCastMibInfo
DhcpGetMScopeInfo
DhcpGetMibInfo
DhcpGetMibInfoV5
DhcpGetMibInfoV6
DhcpGetMibInfoVQ
DhcpGetOptionInfo
DhcpGetOptionInfoV5
DhcpGetOptionInfoV6
DhcpGetOptionValue
DhcpGetOptionValueV5
DhcpGetOptionValueV6
DhcpGetServerBindingInfo
DhcpGetServerBindingInfoV6
DhcpGetServerSpecificStrings
DhcpGetSubnetDelayOffer
DhcpGetSubnetInfo
DhcpGetSubnetInfoV6
DhcpGetSubnetInfoVQ
DhcpGetSuperScopeInfoV4
DhcpGetThreadOptions
DhcpGetVersion
DhcpHlprAddV4PolicyCondition
DhcpHlprAddV4PolicyExpr
DhcpHlprAddV4PolicyRange
DhcpHlprCreateV4Policy
DhcpHlprCreateV4PolicyEx
DhcpHlprFindV4DhcpProperty
DhcpHlprFreeV4DhcpProperty
DhcpHlprFreeV4DhcpPropertyArray
DhcpHlprFreeV4Policy
DhcpHlprFreeV4PolicyArray
DhcpHlprFreeV4PolicyEx
DhcpHlprFreeV4PolicyExArray
DhcpHlprIsV4PolicySingleUC
DhcpHlprIsV4PolicyValid
DhcpHlprIsV4PolicyWellFormed
DhcpHlprModifyV4PolicyExpr
DhcpHlprResetV4PolicyExpr
DhcpModifyClass
DhcpModifyClassV6
DhcpRemoveMScopeElement
DhcpRemoveOption
DhcpRemoveOptionV5
DhcpRemoveOptionV6
DhcpRemoveOptionValue
DhcpRemoveOptionValueV5
DhcpRemoveOptionValueV6
DhcpRemoveSubnetElement
DhcpRemoveSubnetElementV4
DhcpRemoveSubnetElementV5
DhcpRemoveSubnetElementV6
DhcpRpcFreeMemory
DhcpScanDatabase
DhcpScanMDatabase
DhcpServerAuditlogParamsFree
DhcpServerBackupDatabase
DhcpServerGetConfig
DhcpServerGetConfigV4
DhcpServerGetConfigV6
DhcpServerGetConfigVQ
DhcpServerQueryAttribute
DhcpServerQueryAttributes
DhcpServerQueryDnsRegCredentials
DhcpServerRedoAuthorization
DhcpServerRestoreDatabase
DhcpServerSetConfig
DhcpServerSetConfigV4
DhcpServerSetConfigV6
DhcpServerSetConfigVQ
DhcpServerSetDnsRegCredentials
DhcpServerSetDnsRegCredentialsV5
DhcpSetClientInfo
DhcpSetClientInfoV4
DhcpSetClientInfoV6
DhcpSetClientInfoVQ
DhcpSetFilterV4
DhcpSetMScopeInfo
DhcpSetOptionInfo
DhcpSetOptionInfoV5
DhcpSetOptionInfoV6
DhcpSetOptionValue
DhcpSetOptionValueV5
DhcpSetOptionValueV6
DhcpSetOptionValues
DhcpSetOptionValuesV5
DhcpSetServerBindingInfo
DhcpSetServerBindingInfoV6
DhcpSetSubnetDelayOffer
DhcpSetSubnetInfo
DhcpSetSubnetInfoV6
DhcpSetSubnetInfoVQ
DhcpSetSuperScopeV4
DhcpSetThreadOptions
DhcpV4AddPolicyRange
DhcpV4CreateClientInfo
DhcpV4CreateClientInfoEx
DhcpV4CreatePolicy
DhcpV4CreatePolicyEx
DhcpV4DeletePolicy
DhcpV4EnumPolicies
DhcpV4EnumPoliciesEx
DhcpV4EnumSubnetClients
DhcpV4EnumSubnetClientsEx
DhcpV4EnumSubnetReservations
DhcpV4FailoverAddScopeToRelationship
DhcpV4FailoverCreateRelationship
DhcpV4FailoverDeleteRelationship
DhcpV4FailoverDeleteScopeFromRelationship
DhcpV4FailoverEnumRelationship
DhcpV4FailoverGetAddressStatus
DhcpV4FailoverGetClientInfo
DhcpV4FailoverGetRelationship
DhcpV4FailoverGetScopeRelationship
DhcpV4FailoverGetScopeStatistics
DhcpV4FailoverGetSystemTime
DhcpV4FailoverSetRelationship
DhcpV4FailoverTriggerAddrAllocation
DhcpV4GetAllOptionValues
DhcpV4GetClientInfo
DhcpV4GetClientInfoEx
DhcpV4GetFreeIPAddress
DhcpV4GetOptionValue
DhcpV4GetPolicy
DhcpV4GetPolicyEx
DhcpV4QueryPolicyEnforcement
DhcpV4RemoveOptionValue
DhcpV4RemovePolicyRange
DhcpV4SetOptionValue
DhcpV4SetOptionValues
DhcpV4SetPolicy
DhcpV4SetPolicyEnforcement
DhcpV4SetPolicyEx
DhcpV6CreateClientInfo
DhcpV6GetFreeIPAddress
DhcpV6GetStatelessStatistics
DhcpV6GetStatelessStoreParams
DhcpV6SetStatelessStoreParams
