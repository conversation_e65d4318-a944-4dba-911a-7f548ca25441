LIBRARY COMCTL32.dll
EXPORTS
MenuHelp
ShowHideMenuCtl
GetEffectiveClientRect
DrawStatusTextA
CreateStatusWindowA
CreateToolbar
CreateMappedBitmap
DPA_LoadStream
DPA_SaveStream
DPA_Merge
CreatePropertySheetPage
MakeDragList
LBItemFromPt
DrawInsert
CreateUpDownControl
InitCommonControls
CreatePropertySheetPageA
CreatePropertySheetPageW
CreateStatusWindow
CreateStatusWindowW
CreateToolbarEx
DestroyPropertySheetPage
DllGetVersion
DllInstall
DrawShadowText
DrawStatusText
DrawStatusTextW
FlatSB_EnableScrollBar
FlatSB_GetScrollInfo
FlatSB_GetScrollPos
FlatSB_GetScrollProp
FlatSB_GetScrollPropPtr
FlatSB_GetScrollRange
FlatSB_SetScrollInfo
FlatSB_SetScrollPos
FlatSB_SetScrollProp
FlatSB_SetScrollRange
FlatSB_ShowScrollBar
GetMUILanguage
HIMAGELIST_QueryInterface
ImageList_Add
ImageList_AddIcon
ImageList_AddMasked
ImageList_BeginDrag
ImageList_CoCreateInstance
ImageList_Copy
ImageList_Create
ImageList_Destroy
ImageList_DestroyShared
ImageList_DragEnter
ImageList_DragLeave
ImageList_DragMove
ImageList_DragShowNolock
ImageList_Draw
ImageList_DrawEx
ImageList_DrawIndirect
ImageList_Duplicate
ImageList_EndDrag
ImageList_GetBkColor
ImageList_GetDragImage
ImageList_GetFlags
ImageList_GetIcon
ImageList_GetIconSize
ImageList_GetImageCount
ImageList_GetImageInfo
ImageList_GetImageRect
ImageList_LoadImage
ImageList_LoadImageA
ImageList_LoadImageW
ImageList_Merge
ImageList_Read
ImageList_ReadEx
ImageList_Remove
ImageList_Replace
ImageList_ReplaceIcon
ImageList_Resize
ImageList_SetBkColor
ImageList_SetDragCursorImage
ImageList_SetFilter
ImageList_SetFlags
ImageList_SetIconSize
ImageList_SetImageCount
ImageList_SetOverlayImage
ImageList_Write
ImageList_WriteEx
InitCommonControlsEx
InitMUILanguage
InitializeFlatSB
LoadIconMetric
PropertySheet
PropertySheetA
PropertySheetW
RegisterClassNameW
UninitializeFlatSB
_TrackMouseEvent
FreeMRUList
DrawSizeBox
DrawScrollBar
SizeBoxHwnd
ScrollBar_MouseMove
ScrollBar_Menu
HandleScrollCmd
DetachScrollBars
AttachScrollBars
CCSetScrollInfo
CCGetScrollInfo
CCEnableScrollBar
Str_SetPtrW
DSA_Create
DSA_Destroy
DSA_GetItem
DSA_GetItemPtr
DSA_InsertItem
DSA_SetItem
DSA_DeleteItem
DSA_DeleteAllItems
DPA_Create
DPA_Destroy
DPA_Grow
DPA_Clone
DPA_GetPtr
DPA_GetPtrIndex
DPA_InsertPtr
DPA_SetPtr
DPA_DeletePtr
DPA_DeleteAllPtrs
DPA_Sort
DPA_Search
DPA_CreateEx
DSA_Clone
DSA_Sort
DPA_GetSize
DSA_GetSize
LoadIconWithScaleDown
DPA_EnumCallback
DPA_DestroyCallback
DSA_EnumCallback
DSA_DestroyCallback
QuerySystemGestureStatus
CreateMRUListW
AddMRUStringW
EnumMRUListW
SetWindowSubclass
GetWindowSubclass
RemoveWindowSubclass
DefSubclassProc
TaskDialog
TaskDialogIndirect
