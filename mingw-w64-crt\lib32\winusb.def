;
; Definition file of WINUSB.DLL
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WINUSB.DLL"
EXPORTS
WinUsb_AbortPipe@8
WinUsb_AbortPipeAsync@12
WinUsb_ControlTransfer@28
WinUsb_FlushPipe@8
WinUsb_Free@4
WinUsb_GetAdjustedFrameNumber@12
WinUsb_GetAssociatedInterface@12
WinUsb_GetCurrentAlternateSetting@8
WinUsb_GetCurrentFrameNumber@12
WinUsb_GetDescriptor@28
WinUsb_GetOverlappedResult@16
WinUsb_GetPipePolicy@20
WinUsb_GetPowerPolicy@16
WinUsb_Initialize@8
WinUsb_ParseConfigurationDescriptor@28
WinUsb_ParseDescriptors@16
WinUsb_QueryDeviceInformation@16
WinUsb_QueryInterfaceSettings@12
WinUsb_QueryPipe@16
WinUsb_QueryP<PERSON><PERSON><PERSON><PERSON>@16
WinUsb_ReadIsochPipe@28
WinUsb_ReadIsochPipeAsap@28
WinUsb_ReadPipe@24
WinUsb_RegisterIsochBuffer@20
WinUsb_ResetPipe@8
WinUsb_ResetPipeAsync@12
WinUsb_SetCurrentAlternateSetting@8
WinUsb_SetCurrentAlternateSettingAsync@12
WinUsb_SetPipePolicy@20
WinUsb_SetPowerPolicy@16
WinUsb_UnregisterIsochBuffer@4
WinUsb_WriteIsochPipe@20
WinUsb_WriteIsochPipeAsap@20
WinUsb_WritePipe@24
