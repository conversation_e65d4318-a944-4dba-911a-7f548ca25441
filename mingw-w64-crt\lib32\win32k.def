LIBRARY win32k.sys
EXPORTS
BRUSHOBJ_hGetColorTransform@4
BRUSHOBJ_pvAlloc<PERSON><PERSON>@8
BRUSHOBJ_pvGetRbrush@4
BRUSHOBJ_ulGetBrushColor@4
CLIPOBJ_bEnum@12
CLIPOBJ_cEnumStart@20
CLIPOBJ_ppoGetPath@4
EngAcquireSemaphore@4
EngAllocMem@12
EngAllocPrivateUserMem@12
;EngAllocSectionMem@16
EngAllocUserMem@8
EngAlphaBlend@28
EngAssociateSurface@12
EngBitBlt@44
EngCheckAbort@4
EngClearEvent@4
EngComputeGlyphSet@12
EngControlSprites@8
EngCopyBits@24
EngCreateBitmap@24
EngCreateClip@0
EngCreateDeviceBitmap@16
EngCreateDeviceSurface@16
;EngCreateDriverObj@12
EngCreateEvent@4
EngCreatePalette@24
EngCreatePath@0
EngCreateSemaphore@0
EngCreateWnd@20
EngDebugBreak@0
EngDebugPrint@12
EngDeleteClip@4
EngDeleteDriverObj@12
EngDeleteEvent@4
EngDeleteFile@4
EngDeletePalette@4
EngDeletePath@4
EngDeleteSafeSemaphore@4
EngDeleteSemaphore@4
EngDeleteSurface@4
EngDeleteWnd@4
EngDeviceIoControl@28
EngDitherColor@16
;EngDxIoctl@12
EngEnumForms@24
EngEraseSurface@12
;EngFileIoControl@28
;EngFileWrite@16
EngFillPath@28
EngFindImageProcAddress@8
EngFindResource@16
EngFntCacheAlloc@8
EngFntCacheFault@8
EngFntCacheLookUp@8
EngFreeMem@4
EngFreeModule@4
EngFreePrivateUserMem@8
;EngFreeSectionMem@8
EngFreeUserMem@4
EngGetCurrentCodePage@8
EngGetCurrentProcessId@0
EngGetCurrentThreadId@0
EngGetDriverName@4
EngGetFileChangeTime@8
EngGetFilePath@8
EngGetForm@24
EngGetLastError@0
EngGetPrinter@20
EngGetPrinterData@24
EngGetPrinterDataFileName@4
EngGetPrinterDriver@24
EngGetProcessHandle@0
;EngGetTickCount@0
EngGetType1FontList@24
EngGradientFill@40
EngHangNotification@8
EngInitializeSafeSemaphore@4
EngIsSemaphoreOwned@4
EngIsSemaphoreOwnedByCurrentThread@4
EngLineTo@36
EngLoadImage@4
EngLoadModule@4
EngLoadModuleForWrite@8
EngLockDirectDrawSurface@4
;EngLockDriverObj@4
EngLockSurface@4
EngLpkInstalled@0
EngMapEvent@20
EngMapFile@12
EngMapFontFile@12
EngMapFontFileFD@12
EngMapModule@8
;EngMapSection@16
EngMarkBandingSurface@4
EngModifySurface@32
EngMovePointer@16
EngMulDiv@12
EngMultiByteToUnicodeN@20
EngMultiByteToWideChar@20
;EngNineGrid@36
EngPaint@20
EngPlgBlt@44
EngProbeForRead@12
EngProbeForReadAndWrite@12
EngQueryDeviceAttribute@24
EngQueryLocalTime@4
EngQueryPalette@16
EngQueryPerformanceCounter@4
EngQueryPerformanceFrequency@4
EngQuerySystemAttribute@8
EngReadStateEvent@4
EngReleaseSemaphore@4
EngRestoreFloatingPointState@4
EngSaveFloatingPointState@8
EngSecureMem@8
EngSetEvent@4
EngSetLastError@4
EngSetPointerShape@40
EngSetPointerTag@20
EngSetPrinterData@20
EngSort@16
EngStretchBlt@44
EngStretchBltROP@52
EngStrokeAndFillPath@40
EngStrokePath@32
EngTextOut@40
EngTransparentBlt@32
EngUnicodeToMultiByteN@20
EngUnloadImage@4
EngUnlockDirectDrawSurface@4
EngUnlockDriverObj@4
EngUnlockSurface@4
EngUnmapEvent@4
EngUnmapFile@4
EngUnmapFontFile@4
EngUnmapFontFileFD@4
EngUnsecureMem@4
EngWaitForSingleObject@8
EngWideCharToMultiByte@20
EngWritePrinter@16
FLOATOBJ_Add@8
FLOATOBJ_AddFloat@8
;FLOATOBJ_AddFloatObj
FLOATOBJ_AddLong@8
FLOATOBJ_Div@8
FLOATOBJ_DivFloat@8
;FLOATOBJ_DivFloatObj
FLOATOBJ_DivLong@8
FLOATOBJ_Equal@8
FLOATOBJ_EqualLong@8
FLOATOBJ_GetFloat@4
FLOATOBJ_GetLong@4
FLOATOBJ_GreaterThan@8
FLOATOBJ_GreaterThanLong@8
FLOATOBJ_LessThan@8
FLOATOBJ_LessThanLong@8
FLOATOBJ_Mul@8
FLOATOBJ_MulFloat@8
;FLOATOBJ_MulFloatObj
FLOATOBJ_MulLong@8
FLOATOBJ_Neg@4
FLOATOBJ_SetFloat@8
FLOATOBJ_SetLong@8
FLOATOBJ_Sub@8
FLOATOBJ_SubFloat@8
;FLOATOBJ_SubFloatObj
FLOATOBJ_SubLong@8
FONTOBJ_cGetAllGlyphHandles@8
FONTOBJ_cGetGlyphs@20
FONTOBJ_pQueryGlyphAttrs@8
FONTOBJ_pfdg@4
FONTOBJ_pifi@4
FONTOBJ_pjOpenTypeTablePointer@12
FONTOBJ_pvTrueTypeFontFile@8
FONTOBJ_pwszFontFilePaths@8
FONTOBJ_pxoGetXform@4
FONTOBJ_vGetInfo@12
HT_ComputeRGBGammaTable@24
HT_Get8BPPFormatPalette@16
HT_Get8BPPMaskPalette@24
HeapVidMemAllocAligned@20
PALOBJ_cGetColors@16
PATHOBJ_bCloseFigure@4
PATHOBJ_bEnum@8
PATHOBJ_bEnumClipLines@12
PATHOBJ_bMoveTo@12
PATHOBJ_bPolyBezierTo@12
PATHOBJ_bPolyLineTo@12
PATHOBJ_vEnumStart@4
PATHOBJ_vEnumStartClipLines@16
PATHOBJ_vGetBounds@8
;RtlAnsiCharToUnicodeChar@4
;RtlMultiByteToUnicodeN@20
;RtlRaiseException
;RtlUnicodeToMultiByteN@20
;RtlUnicodeToMultiByteSize@12
;RtlUnwind@16
RtlUpcaseUnicodeChar@4
;RtlUpcaseUnicodeToMultiByteN@20
STROBJ_bEnum@12
STROBJ_bEnumPositionsOnly@12
STROBJ_bGetAdvanceWidths@16
STROBJ_dwGetCodePage@4
STROBJ_fxBreakExtra@4
STROBJ_fxCharacterExtra@4
STROBJ_vEnumStart@4
VidMemFree@8
WNDOBJ_bEnum@12
WNDOBJ_cEnumStart@16
WNDOBJ_vSetConsumer@8
XFORMOBJ_bApplyXform@20
XFORMOBJ_iGetFloatObjXform@8
XFORMOBJ_iGetXform@8
XLATEOBJ_cGetPalette@16
XLATEOBJ_hGetColorTransform@4
XLATEOBJ_iXlate@8
XLATEOBJ_piVector@4
;_abnormal_termination
;_except_handler2
;_global_unwind2
;_itoa
;_itow
;_local_unwind2
