/*
   Copyright (c) 2011 mingw-w64 project

   Permission is hereby granted, free of charge, to any person obtaining a
   copy of this software and associated documentation files (the "Software"),
   to deal in the Software without restriction, including without limitation
   the rights to use, copy, modify, merge, publish, distribute, sublicense,
   and/or sell copies of the Software, and to permit persons to whom the
   Software is furnished to do so, subject to the following conditions:

   The above copyright notice and this permission notice shall be included in
   all copies or substantial portions of the Software.

   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
   FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
   DEALINGS IN THE SOFTWARE.
*/

#include <winver.h>
#include "wpth_ver.h"

#define WPTH_VERSIONINFO_NAME "WinPthreadGC\0"
#ifdef _WIN64
#define WPTH_VERSIONINFO_COMMENT "GNU C build -- MinGW-w64 64-bit\0"
#else
#define WPTH_VERSIONINFO_COMMENT "GNU C build -- MinGW-w64 32-bit\0"
#endif

VS_VERSION_INFO VERSIONINFO
  FILEVERSION    WPTH_VERSION
  PRODUCTVERSION WPTH_VERSION
  FILEFLAGSMASK  VS_FFI_FILEFLAGSMASK
  FILEFLAGS      0
  FILEOS         VOS__WINDOWS32
  FILETYPE       VFT_DLL
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "FileDescription", "POSIX WinThreads for Windows\0"
            VALUE "ProductVersion", WPTH_VERSION_STRING
            VALUE "FileVersion", WPTH_VERSION_STRING
            VALUE "InternalName", WPTH_VERSIONINFO_NAME
            VALUE "OriginalFilename", WPTH_VERSIONINFO_NAME
            VALUE "CompanyName", "MingW-W64 Project. All rights reserved.\0"
            VALUE "LegalCopyright", "Copyright (C) MingW-W64 Project Members 2010-2011\0"
            VALUE "Licence", "ZPL\0"
            VALUE "Info", "http://mingw-w64.sourceforge.net/\0"
	    VALUE "Comment", WPTH_VERSIONINFO_COMMENT
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

