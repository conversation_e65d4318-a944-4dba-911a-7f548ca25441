;
; Definition file of msi.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "msi.dll"
EXPORTS
MsiAdvertiseProductA
MsiAdvertiseProductW
MsiCloseAllHandles
MsiCloseHandle
MsiCollectUserInfoA
MsiCollectUserInfoW
MsiConfigureFeatureA
MsiConfigureFeatureFromDescriptorA
MsiConfigureFeatureFromDescriptorW
MsiConfigureFeatureW
MsiConfigureProductA
MsiConfigureProductW
MsiCreateRecord
MsiDatabaseApplyTransformA
MsiDatabaseApplyTransformW
MsiDatabaseCommit
MsiDatabaseExportA
MsiDatabaseExportW
MsiDatabaseGenerateTransformA
MsiDatabaseGenerateTransformW
MsiDatabaseGetPrimaryKeysA
MsiDatabaseGetPrimaryKeysW
MsiDatabaseImportA
MsiDatabaseImportW
MsiD<PERSON>
MsiDatabaseMergeW
MsiDatabaseOpenViewA
MsiDatabaseOpenViewW
MsiDoActionA
MsiDoActionW
MsiEnableUIPreview
MsiEnumClientsA
MsiEnumClientsW
MsiEnumComponentQualifiersA
MsiEnumComponentQualifiersW
MsiEnumComponentsA
MsiEnumComponentsW
MsiEnumFeaturesA
MsiEnumFeaturesW
MsiEnumProductsA
MsiEnumProductsW
MsiEvaluateConditionA
MsiEvaluateConditionW
MsiGetLastErrorRecord
MsiGetActiveDatabase
MsiGetComponentStateA
MsiGetComponentStateW
MsiGetDatabaseState
MsiGetFeatureCostA
MsiGetFeatureCostW
MsiGetFeatureInfoA
MsiGetFeatureInfoW
MsiGetFeatureStateA
MsiGetFeatureStateW
MsiGetFeatureUsageA
MsiGetFeatureUsageW
MsiGetFeatureValidStatesA
MsiGetFeatureValidStatesW
MsiGetLanguage
MsiGetMode
MsiGetProductCodeA
MsiGetProductCodeW
MsiGetProductInfoA
MsiGetProductInfoFromScriptA
MsiGetProductInfoFromScriptW
MsiGetProductInfoW
MsiGetProductPropertyA
MsiGetProductPropertyW
MsiGetPropertyA
MsiGetPropertyW
MsiGetSourcePathA
MsiGetSourcePathW
MsiGetSummaryInformationA
MsiGetSummaryInformationW
MsiGetTargetPathA
MsiGetTargetPathW
MsiGetUserInfoA
MsiGetUserInfoW
MsiInstallMissingComponentA
MsiInstallMissingComponentW
MsiInstallMissingFileA
MsiInstallMissingFileW
MsiInstallProductA
MsiInstallProductW
MsiLocateComponentA
MsiLocateComponentW
MsiOpenDatabaseA
MsiOpenDatabaseW
MsiOpenPackageA
MsiOpenPackageW
MsiOpenProductA
MsiOpenProductW
MsiPreviewBillboardA
MsiPreviewBillboardW
MsiPreviewDialogA
MsiPreviewDialogW
MsiProcessAdvertiseScriptA
MsiProcessAdvertiseScriptW
MsiProcessMessage
MsiProvideComponentA
MsiProvideComponentFromDescriptorA
MsiProvideComponentFromDescriptorW
MsiProvideComponentW
MsiProvideQualifiedComponentA
MsiProvideQualifiedComponentW
MsiQueryFeatureStateA
MsiQueryFeatureStateW
MsiQueryProductStateA
MsiQueryProductStateW
MsiRecordDataSize
MsiRecordGetFieldCount
MsiRecordGetInteger
MsiRecordGetStringA
MsiRecordGetStringW
MsiRecordIsNull
MsiRecordReadStream
MsiRecordSetInteger
MsiRecordSetStreamA
MsiRecordSetStreamW
MsiRecordSetStringA
MsiRecordSetStringW
MsiReinstallFeatureA
MsiReinstallFeatureFromDescriptorA
MsiReinstallFeatureFromDescriptorW
MsiReinstallFeatureW
MsiReinstallProductA
MsiReinstallProductW
MsiSequenceA
MsiSequenceW
MsiSetComponentStateA
MsiSetComponentStateW
MsiSetExternalUIA
MsiSetExternalUIW
MsiSetFeatureStateA
MsiSetFeatureStateW
MsiSetInstallLevel
MsiSetInternalUI
MsiVerifyDiskSpace
MsiSetMode
MsiSetPropertyA
MsiSetPropertyW
MsiSetTargetPathA
MsiSetTargetPathW
MsiSummaryInfoGetPropertyA
MsiSummaryInfoGetPropertyCount
MsiSummaryInfoGetPropertyW
MsiSummaryInfoPersist
MsiSummaryInfoSetPropertyA
MsiSummaryInfoSetPropertyW
MsiUseFeatureA
MsiUseFeatureW
MsiVerifyPackageA
MsiVerifyPackageW
MsiViewClose
MsiViewExecute
MsiViewFetch
MsiViewGetErrorA
MsiViewGetErrorW
MsiViewModify
MsiDatabaseIsTablePersistentA
MsiDatabaseIsTablePersistentW
MsiViewGetColumnInfo
MsiRecordClearData
MsiEnableLogA
MsiEnableLogW
MsiFormatRecordA
MsiFormatRecordW
MsiGetComponentPathA
MsiGetComponentPathW
MsiApplyPatchA
MsiApplyPatchW
MsiAdvertiseScriptA
MsiAdvertiseScriptW
MsiGetPatchInfoA
MsiGetPatchInfoW
MsiEnumPatchesA
MsiEnumPatchesW
MsiGetProductCodeFromPackageCodeA
MsiGetProductCodeFromPackageCodeW
MsiCreateTransformSummaryInfoA
MsiCreateTransformSummaryInfoW
MsiQueryFeatureStateFromDescriptorA
MsiQueryFeatureStateFromDescriptorW
MsiConfigureProductExA
MsiConfigureProductExW
MsiInvalidateFeatureCache
MsiUseFeatureExA
MsiUseFeatureExW
MsiGetFileVersionA
MsiGetFileVersionW
MsiLoadStringA
MsiLoadStringW
MsiMessageBoxA
MsiMessageBoxW
MsiDecomposeDescriptorA
MsiDecomposeDescriptorW
MsiProvideQualifiedComponentExA
MsiProvideQualifiedComponentExW
MsiEnumRelatedProductsA
MsiEnumRelatedProductsW
MsiSetFeatureAttributesA
MsiSetFeatureAttributesW
MsiSourceListClearAllA
MsiSourceListClearAllW
MsiSourceListAddSourceA
MsiSourceListAddSourceW
MsiSourceListForceResolutionA
MsiSourceListForceResolutionW
MsiIsProductElevatedA
MsiIsProductElevatedW
MsiGetShortcutTargetA
MsiGetShortcutTargetW
MsiGetFileHashA
MsiGetFileHashW
MsiEnumComponentCostsA
MsiEnumComponentCostsW
MsiCreateAndVerifyInstallerDirectory
MsiGetFileSignatureInformationA
MsiGetFileSignatureInformationW
MsiProvideAssemblyA
MsiProvideAssemblyW
MsiAdvertiseProductExA
MsiAdvertiseProductExW
MsiNotifySidChangeA
MsiNotifySidChangeW
MsiOpenPackageExA
MsiOpenPackageExW
MsiDeleteUserDataA
MsiDeleteUserDataW
Migrate10CachedPackagesA
Migrate10CachedPackagesW
MsiRemovePatchesA
MsiRemovePatchesW
MsiApplyMultiplePatchesA
MsiApplyMultiplePatchesW
MsiExtractPatchXMLDataA
MsiExtractPatchXMLDataW
MsiGetPatchInfoExA
MsiGetPatchInfoExW
MsiEnumProductsExA
MsiEnumProductsExW
MsiGetProductInfoExA
MsiGetProductInfoExW
MsiQueryComponentStateA
MsiQueryComponentStateW
MsiQueryFeatureStateExA
MsiQueryFeatureStateExW
MsiDeterminePatchSequenceA
MsiDeterminePatchSequenceW
MsiSourceListAddSourceExA
MsiSourceListAddSourceExW
MsiSourceListClearSourceA
MsiSourceListClearSourceW
MsiSourceListClearAllExA
MsiSourceListClearAllExW
MsiSourceListForceResolutionExA
MsiSourceListForceResolutionExW
MsiSourceListEnumSourcesA
MsiSourceListEnumSourcesW
MsiSourceListGetInfoA
MsiSourceListGetInfoW
MsiSourceListSetInfoA
MsiSourceListSetInfoW
MsiEnumPatchesExA
MsiEnumPatchesExW
MsiSourceListEnumMediaDisksA
MsiSourceListEnumMediaDisksW
MsiSourceListAddMediaDiskA
MsiSourceListAddMediaDiskW
MsiSourceListClearMediaDiskA
MsiSourceListClearMediaDiskW
MsiDetermineApplicablePatchesA
MsiDetermineApplicablePatchesW
MsiMessageBoxExA
MsiMessageBoxExW
MsiSetExternalUIRecord
MsiGetPatchFileListA
MsiGetPatchFileListW
MsiBeginTransactionA
MsiBeginTransactionW
MsiEndTransaction
MsiJoinTransaction
MsiSetOfflineContextW
MsiEnumComponentsExA
MsiEnumComponentsExW
MsiEnumClientsExA
MsiEnumClientsExW
MsiGetComponentPathExA
MsiGetComponentPathExW
QueryInstanceCount
