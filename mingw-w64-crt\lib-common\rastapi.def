;
; Definition file of rastapi.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "rastapi.dll"
EXPORTS
AddPorts
CheckRasmanDependency
DeviceConnect
DeviceDone
DeviceEnum
DeviceGetDevConfig
DeviceGetDevConfigEx
DeviceGetInfo
DeviceListen
DeviceSetDevConfig
DeviceSetInfo
DeviceWork
EnableDeviceForDialIn
GetConnectInfo
GetZeroDeviceInfo
InitializeDriverIoControl
PortChangeCallback
PortClearStatistics
PortClose
PortCompressionSetInfo
PortConnect
PortDisconnect
PortEnum
PortGetIOHandle
PortGetInfo
PortGetPortState
PortGetStatistics
PortInit
PortOpen
PortOpenExternal
PortReceive
PortReceiveComplete
PortSend
PortSetFraming
PortSetInfo
PortSetIoCompletionPort
PortTestSignalState
RasTapiIsPulseDial
RastapiGetCalledID
RastapiSetCalle<PERSON>ID
<PERSON>freshDevices
RemovePort
SetCommSettings
UnloadRastapiDll
UpdateTapiService
