;
; Definition file of AUTHZ.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "AUTHZ.dll"
EXPORTS
AuthzAccessCheck
AuthzAddSidsToContext
AuthzCachedAccessCheck
AuthzComputeEffectivePermission
AuthzEnumerateSecurityEventSources
AuthzEvaluateSacl
AuthzFreeAuditEvent
AuthzFreeCentralAccessPolicyCache
AuthzFreeContext
AuthzFreeHandle
AuthzFreeResourceManager
AuthzGetInformationFromContext
AuthzInitializeCompoundContext
AuthzInitializeContextFromAuthzContext
AuthzInitializeContextFromSid
AuthzInitializeContextFromToken
AuthzInitializeObjectAccessAuditEvent
AuthzInitializeObjectAccessAuditEvent2
AuthzInitializeRemoteAccessCheck
AuthzInitializeRemoteResourceManager
Auth<PERSON>I<PERSON>R<PERSON>ourceManager
AuthzInitializeResourceManagerEx
AuthzInstallSecurityEventSource
AuthzModifyClaims
AuthzModifySecurityAttributes
AuthzModifySids
AuthzOpenObjectAudit
AuthzRegisterCapChangeNotification
AuthzRegisterSecurityEventSource
AuthzReportSecurityEvent
AuthzReportSecurityEventFromParams
AuthzSetAppContainerInformation
AuthzShutdownRemoteAccessCheck
AuthzUninstallSecurityEventSource
AuthzUnregisterCapChangeNotification
AuthzUnregisterSecurityEventSource
AuthziAccessCheckEx
AuthziAllocateAuditParams
AuthziCheckContextMembership
AuthziFreeAuditEventType
AuthziFreeAuditParams
AuthziFreeAuditQueue
AuthziGenerateAdminAlertAuditW
AuthziInitializeAuditEvent
AuthziInitializeAuditEventType
AuthziInitializeAuditParams
AuthziInitializeAuditParamsFromArray
AuthziInitializeAuditParamsWithRM
AuthziInitializeAuditQueue
AuthziInitializeContextFromSid
AuthziLogAuditEvent
AuthziModifyAuditEvent
AuthziModifyAuditEvent2
AuthziModifyAuditEventType
AuthziModifyAuditQueue
AuthziQueryAuditPolicy
AuthziSetAuditPolicy
AuthziModifySecurityAttributes
AuthziQuerySecurityAttributes
AuthziSourceAudit
FreeClaimDefinitions
FreeClaimDictionary
GenerateNewCAPID
GetCentralAccessPoliciesByCapID
GetCentralAccessPoliciesByDN
GetClaimDefinitions
GetClaimDomainInfo
GetDefaultCAPESecurityDescriptor
InitializeClaimDictionary
RefreshClaimDictionary
