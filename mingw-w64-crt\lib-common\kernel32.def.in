#include "func.def.in"

LIBRARY "KERNEL32.dll"
EXPORTS
AcquireSRWLockExclusive
AcquireSRWLockShared
ActivateActCtx
ActivateActCtxWorker
AddAtomA
AddAtomW
AddConsoleAliasA
AddConsoleAliasW
AddDllDirectory
AddIntegrityLabelToBoundaryDescriptor
AddLocalAlternateComputerNameA
AddLocalAlternateComputerNameW
AddRefActCtx
AddRefActCtxWorker
AddResourceAttributeAce
AddSIDToBoundaryDescriptor
AddScopedPolicyIDAce
AddSecureMemoryCacheCallback
AddVectoredContinueHandler
AddVectoredExceptionHandler
AdjustCalendarDate
AllocConsole
AllocateUserPhysicalPages
AllocateUserPhysicalPagesNuma
AppPolicyGetClrCompat
AppPolicyGetCreateFileAccess
AppPolicyGetLifecycleManagement
AppPolicyGetMediaFoundationCodecLoading
AppPolicyGetProcessTerminationMethod
AppPolicyGetShowDeveloperDiagnostic
AppPolicyGetThreadInitializationType
AppPolicyGetWindowingModel
AppXGetOSMaxVersionTested
ApplicationRecoveryFinished
ApplicationRecoveryInProgress
AreFileApisANSI
AssignProcessToJobObject
AttachConsole
BackupRead
BackupSeek
BackupWrite
BaseCheckAppcompatCache
BaseCheckAppcompatCacheEx
BaseCheckAppcompatCacheExWorker
BaseCheckAppcompatCacheWorker
BaseCheckElevation
BaseCheckRunApp
BaseCleanupAppcompatCacheSupport
BaseCleanupAppcompatCacheSupportWorker
BaseDestroyVDMEnvironment
BaseDllReadWriteIniFile
BaseDumpAppcompatCache
BaseDumpAppcompatCacheWorker
BaseElevationPostProcessing
BaseFlushAppcompatCache
BaseFlushAppcompatCacheWorker
BaseFormatObjectAttributes
BaseFormatTimeOut
BaseFreeAppCompatDataForProcessWorker
BaseGenerateAppCompatData
BaseGetNamedObjectDirectory
BaseInitAppcompatCacheSupport
BaseInitAppcompatCacheSupportWorker
BaseIsAppcompatInfrastructureDisabled
BaseIsAppcompatInfrastructureDisabledWorker
BaseIsDosApplication
BaseProcessInitPostImport
BaseProcessStart
BaseQueryModuleData
BaseReadAppCompatDataForProcessWorker
BaseThreadStart
BaseSetLastNTError
BaseThreadInitThunk
BaseUpdateAppcompatCache
BaseUpdateAppcompatCacheWorker
BaseUpdateVDMEntry
BaseVerifyUnicodeString
BaseWriteErrorElevationRequiredEvent
Basep8BitStringToDynamicUnicodeString
BasepAllocateActivationContextActivationBlock
BasepAnsiStringToDynamicUnicodeString
BasepAppContainerEnvironmentExtension
BasepAppXExtension
BasepCheckAppCompat
BasepCheckBadapp
BasepCheckWebBladeHashes
BasepCheckWinSaferRestrictions
BasepConstructSxsCreateProcessMessage
BasepCopyEncryption
BasepFreeActivationContextActivationBlock
BasepFreeAppCompatData
BasepGetAppCompatData
BasepGetComputerNameFromNtPath
BasepGetExeArchType
BasepInitAppCompatData
BasepIsProcessAllowed
BasepMapModuleHandle
BasepNotifyLoadStringResource
BasepPostSuccessAppXExtension
BasepProcessInvalidImage
BasepQueryAppCompat
BasepQueryModuleChpeSettings
BasepReleaseAppXContext
BasepReleaseSxsCreateProcessUtilityStruct
BasepReportFault
BasepSetFileEncryptionCompression
Beep
BeginUpdateResourceA
BeginUpdateResourceW
BindIoCompletionCallback
BuildCommDCBA
BuildCommDCBAndTimeoutsA
BuildCommDCBAndTimeoutsW
BuildCommDCBW
CallNamedPipeA
CallNamedPipeW
CallbackMayRunLong
CalloutOnFiberStack
CancelDeviceWakeupRequest
CancelIo
CancelIoEx
CancelSynchronousIo
CancelThreadpoolIo
CancelTimerQueueTimer
CancelWaitableTimer
CeipIsOptedIn
ChangeTimerQueueTimer
CheckAllowDecryptedRemoteDestinationPolicy
CheckElevation
CheckElevationEnabled
CheckForReadOnlyResource
CheckForReadOnlyResourceFilter
CheckNameLegalDOS8Dot3A
CheckNameLegalDOS8Dot3W
CheckRemoteDebuggerPresent
CheckTokenCapability
CheckTokenMembershipEx
ClearCommBreak
ClearCommError
CloseConsoleHandle
CloseHandle
ClosePackageInfo
ClosePrivateNamespace
CloseProfileUserMapping
ClosePseudoConsole
CloseState
CloseThreadpool
CloseThreadpoolCleanupGroup
CloseThreadpoolCleanupGroupMembers
CloseThreadpoolIo
CloseThreadpoolTimer
CloseThreadpoolWait
CloseThreadpoolWork
CmdBatNotification
CommConfigDialogA
CommConfigDialogW
CompareCalendarDates
CompareFileTime
CompareStringA
CompareStringEx
CompareStringOrdinal
CompareStringW
ConnectNamedPipe
ConsoleIMERoutine
ConsoleMenuControl
ContinueDebugEvent
ConvertCalDateTimeToSystemTime
ConvertDefaultLocale
ConvertFiberToThread
ConvertNLSDayOfWeekToWin32DayOfWeek
ConvertSystemTimeToCalDateTime
ConvertThreadToFiber
ConvertThreadToFiberEx
CopyContext
CopyExtendedContext
CopyFile2
CopyFileA
CopyFileExA
CopyFileExW
CopyFileTransactedA
CopyFileTransactedW
CopyFileW
CopyLZFile
CreateActCtxA
CreateActCtxW
CreateActCtxWWorker
CreateBoundaryDescriptorA
CreateBoundaryDescriptorW
CreateConsoleScreenBuffer
CreateDirectoryA
CreateDirectoryExA
CreateDirectoryExW
CreateDirectoryTransactedA
CreateDirectoryTransactedW
CreateDirectoryW
CreateEnclave
CreateEventA
CreateEventExA
CreateEventExW
CreateEventW
CreateFiber
CreateFiberEx
CreateFile2
CreateFileA
CreateFileMappingA
CreateFileMappingFromApp
CreateFileMappingNumaA
CreateFileMappingNumaW
CreateFileMappingW
CreateFileTransactedA
CreateFileTransactedW
CreateFileW
CreateHardLinkA
CreateHardLinkTransactedA
CreateHardLinkTransactedW
CreateHardLinkW
CreateIoCompletionPort
CreateJobObjectA
CreateJobObjectW
CreateJobSet
CreateMailslotA
CreateMailslotW
CreateMemoryResourceNotification
CreateMutexA
CreateMutexExA
CreateMutexExW
CreateMutexW
CreateNamedPipeA
CreateNamedPipeW
CreateNlsSecurityDescriptor
CreatePipe
CreatePrivateNamespaceA
CreatePrivateNamespaceW
CreateProcessA
; MSDN says these are exported from ADVAPI32.DLL.
; CreateProcessAsUserA
; CreateProcessAsUserW
CreateProcessInternalA
CreateProcessInternalW
CreateProcessW
CreatePseudoConsole
CreateRemoteThread
CreateRemoteThreadEx
CreateSemaphoreA
CreateSemaphoreExA
CreateSemaphoreExW
CreateSemaphoreW
CreateSymbolicLinkA
CreateSymbolicLinkTransactedA
CreateSymbolicLinkTransactedW
CreateSymbolicLinkW
CreateTapePartition
CreateThread
CreateThreadpool
CreateThreadpoolCleanupGroup
CreateThreadpoolIo
CreateThreadpoolTimer
CreateThreadpoolWait
CreateThreadpoolWork
CreateTimerQueue
CreateTimerQueueTimer
CreateToolhelp32Snapshot
F_X64(CreateUmsCompletionList)
F_X64(CreateUmsThreadContext)
CreateWaitableTimerA
CreateWaitableTimerExA
CreateWaitableTimerExW
CreateWaitableTimerW
CtrlRoutine
DeactivateActCtx
DeactivateActCtxWorker
DebugActiveProcess
DebugActiveProcessStop
DebugBreak
DebugBreakProcess
DebugSetProcessKillOnExit
DecodePointer
DecodeSystemPointer
DefineDosDeviceA
DefineDosDeviceW
DelayLoadFailureHook
DeleteAtom
DeleteBoundaryDescriptor
DeleteCriticalSection
DeleteFiber
DeleteFileA
DeleteFileTransactedA
DeleteFileTransactedW
DeleteFileW
DeleteProcThreadAttributeList
DeleteSynchronizationBarrier
DeleteTimerQueue
DeleteTimerQueueEx
DeleteTimerQueueTimer
F_X64(DeleteUmsCompletionList)
F_X64(DeleteUmsThreadContext)
DeleteVolumeMountPointA
DeleteVolumeMountPointW
F_X64(DequeueUmsCompletionListItems)
DeviceIoControl
DisableThreadLibraryCalls
DisableThreadProfiling
DisassociateCurrentThreadFromCallback
DiscardVirtualMemory
DisconnectNamedPipe
DnsHostnameToComputerNameA
DnsHostnameToComputerNameExW
DnsHostnameToComputerNameW
DosDateTimeToFileTime
DosPathToSessionPathA
DosPathToSessionPathW
DuplicateConsoleHandle
DuplicateEncryptionInfoFileExt
DuplicateHandle
EnableThreadProfiling
EncodePointer
EncodeSystemPointer
EndUpdateResourceA
EndUpdateResourceW
EnterCriticalSection
F_X64(EnterUmsSchedulingMode)
EnterSynchronizationBarrier
EnumCalendarInfoA
EnumCalendarInfoExA
EnumCalendarInfoExEx
EnumCalendarInfoExW
EnumCalendarInfoW
EnumDateFormatsA
EnumDateFormatsExA
EnumDateFormatsExEx
EnumDateFormatsExW
EnumDateFormatsW
EnumLanguageGroupLocalesA
EnumLanguageGroupLocalesW
EnumResourceLanguagesA
EnumResourceLanguagesExA
EnumResourceLanguagesExW
EnumResourceLanguagesW
EnumResourceNamesA
EnumResourceNamesExA
EnumResourceNamesExW
EnumResourceNamesW
EnumResourceTypesA
EnumResourceTypesExA
EnumResourceTypesExW
EnumResourceTypesW
EnumSystemCodePagesA
EnumSystemCodePagesW
EnumSystemFirmwareTables
EnumSystemGeoID
EnumSystemGeoNames
EnumSystemLanguageGroupsA
EnumSystemLanguageGroupsW
EnumSystemLocalesA
EnumSystemLocalesEx
EnumSystemLocalesW
EnumTimeFormatsA
EnumTimeFormatsEx
EnumTimeFormatsW
EnumUILanguagesA
EnumUILanguagesW
EnumerateLocalComputerNamesA
EnumerateLocalComputerNamesW
EraseTape
EscapeCommFunction
F_X64(ExecuteUmsThread)
ExitProcess
ExitThread
ExitVDM
ExpandEnvironmentStringsA
ExpandEnvironmentStringsW
ExpungeConsoleCommandHistoryA
ExpungeConsoleCommandHistoryW
FatalAppExitA
FatalAppExitW
FatalExit
FileTimeToDosDateTime
FileTimeToLocalFileTime
FileTimeToSystemTime
FillConsoleOutputAttribute
FillConsoleOutputCharacterA
FillConsoleOutputCharacterW
FindActCtxSectionGuid
FindActCtxSectionGuidWorker
FindActCtxSectionStringA
FindActCtxSectionStringW
FindActCtxSectionStringWWorker
FindAtomA
FindAtomW
FindClose
FindCloseChangeNotification
FindFirstChangeNotificationA
FindFirstChangeNotificationW
FindFirstFileA
FindFirstFileExA
FindFirstFileExW
FindFirstFileNameTransactedW
FindFirstFileNameW
FindFirstFileTransactedA
FindFirstFileTransactedW
FindFirstFileW
FindFirstStreamTransactedW
FindFirstStreamW
FindFirstVolumeA
FindFirstVolumeMountPointA
FindFirstVolumeMountPointW
FindFirstVolumeW
FindNLSString
FindNLSStringEx
FindNextChangeNotification
FindNextFileA
FindNextFileNameW
FindNextFileW
FindNextStreamW
FindNextVolumeA
FindNextVolumeMountPointA
FindNextVolumeMountPointW
FindNextVolumeW
FindPackagesByPackageFamily
FindResourceA
FindResourceExA
FindResourceExW
FindResourceW
FindStringOrdinal
FindVolumeClose
FindVolumeMountPointClose
FlsAlloc
FlsFree
FlsGetValue
FlsSetValue
FlushConsoleInputBuffer
FlushFileBuffers
FlushInstructionCache
FlushProcessWriteBuffers
FlushViewOfFile
FoldStringA
FoldStringW
FormatApplicationUserModelId
FormatMessageA
FormatMessageW
FreeConsole
FreeEnvironmentStringsA
FreeEnvironmentStringsW
FreeLibrary
FreeLibraryAndExitThread
FreeLibraryWhenCallbackReturns
FreeMemoryJobObject
FreeResource
FreeUserPhysicalPages
GenerateConsoleCtrlEvent
GetACP
GetActiveProcessorCount
GetActiveProcessorGroupCount
GetAppContainerAce
GetAppContainerNamedObjectPath
GetApplicationRecoveryCallback
GetApplicationRecoveryCallbackWorker
GetApplicationRestartSettings
GetApplicationRestartSettingsWorker
GetApplicationUserModelId
GetAtomNameA
GetAtomNameW
GetBinaryType
GetBinaryTypeA
GetBinaryTypeW
GetCPFileNameFromRegistry
GetCPInfo
GetCPInfoExA
GetCPInfoExW
GetCachedSigningLevel
GetCalendarDateFormat
GetCalendarDateFormatEx
GetCalendarDaysInMonth
GetCalendarDifferenceInDays
GetCalendarInfoA
GetCalendarInfoEx
GetCalendarInfoW
GetCalendarMonthsInYear
GetCalendarSupportedDateRange
GetCalendarWeekNumber
GetComPlusPackageInstallStatus
GetCommConfig
GetCommMask
GetCommModemStatus
GetCommProperties
GetCommState
GetCommTimeouts
GetCommandLineA
GetCommandLineW
GetCompressedFileSizeA
GetCompressedFileSizeTransactedA
GetCompressedFileSizeTransactedW
GetCompressedFileSizeW
GetComputerNameA
GetComputerNameExA
GetComputerNameExW
GetComputerNameW
GetConsoleAliasA
GetConsoleAliasExesA
GetConsoleAliasExesLengthA
GetConsoleAliasExesLengthW
GetConsoleAliasExesW
GetConsoleAliasW
GetConsoleAliasesA
GetConsoleAliasesLengthA
GetConsoleAliasesLengthW
GetConsoleAliasesW
GetConsoleCP
GetConsoleCharType
GetConsoleCommandHistoryA
GetConsoleCommandHistoryLengthA
GetConsoleCommandHistoryLengthW
GetConsoleCommandHistoryW
GetConsoleCursorInfo
GetConsoleCursorMode
GetConsoleDisplayMode
GetConsoleFontInfo
GetConsoleFontSize
GetConsoleHardwareState
GetConsoleHistoryInfo
GetConsoleInputExeNameA
GetConsoleInputExeNameW
GetConsoleInputWaitHandle
GetConsoleKeyboardLayoutNameA
GetConsoleKeyboardLayoutNameW
GetConsoleMode
GetConsoleNlsMode
GetConsoleOriginalTitleA
GetConsoleOriginalTitleW
GetConsoleOutputCP
GetConsoleProcessList
GetConsoleScreenBufferInfo
GetConsoleScreenBufferInfoEx
GetConsoleSelectionInfo
GetConsoleTitleA
GetConsoleTitleW
GetConsoleWindow
GetCurrencyFormatA
GetCurrencyFormatEx
GetCurrencyFormatW
GetCurrentActCtx
GetCurrentActCtxWorker
GetCurrentApplicationUserModelId
GetCurrentConsoleFont
GetCurrentConsoleFontEx
GetCurrentDirectoryA
GetCurrentDirectoryW
GetCurrentPackageFamilyName
GetCurrentPackageFullName
GetCurrentPackageId
GetCurrentPackageInfo
GetCurrentPackagePath
GetCurrentProcess
GetCurrentProcessId
GetCurrentProcessorNumber
GetCurrentProcessorNumberEx
GetCurrentThread
GetCurrentThreadId
GetCurrentThreadStackLimits
F_X64(GetCurrentUmsThread)
GetDateFormatA
GetDateFormatAWorker
GetDateFormatEx
GetDateFormatW
GetDateFormatWWorker
GetDefaultCommConfigA
GetDefaultCommConfigW
GetDefaultSortkeySize
GetDevicePowerState
GetDiskFreeSpaceA
GetDiskFreeSpaceExA
GetDiskFreeSpaceExW
GetDiskFreeSpaceW
GetDiskSpaceInformationA
GetDiskSpaceInformationW
GetDllDirectoryA
GetDllDirectoryW
GetDriveTypeA
GetDriveTypeW
GetDurationFormat
GetDurationFormatEx
GetDynamicTimeZoneInformation
GetEnabledExtendedFeatures
GetEnabledXStateFeatures
GetEncryptedFileVersionExt
GetEnvironmentStrings
GetEnvironmentStringsA
GetEnvironmentStringsW
GetEnvironmentVariableA
GetEnvironmentVariableW
GetEraNameCountedString
GetErrorMode
GetExitCodeProcess
GetExitCodeThread
GetExpandedNameA
GetExpandedNameW
GetExtendedContextLength
GetExtendedFeaturesMask
GetFileAttributesA
GetFileAttributesExA
GetFileAttributesExW
GetFileAttributesTransactedA
GetFileAttributesTransactedW
GetFileAttributesW
GetFileBandwidthReservation
GetFileInformationByHandle
GetFileInformationByHandleEx
GetFileMUIInfo
GetFileMUIPath
GetFileSize
GetFileSizeEx
GetFileTime
GetFileType
GetFinalPathNameByHandleA
GetFinalPathNameByHandleW
GetFirmwareEnvironmentVariableA
GetFirmwareEnvironmentVariableExA
GetFirmwareEnvironmentVariableExW
GetFirmwareEnvironmentVariableW
GetFirmwareType
GetFullPathNameA
GetFullPathNameTransactedA
GetFullPathNameTransactedW
GetFullPathNameW
GetGeoInfoA
GetGeoInfoW
GetGeoInfoEx
GetHandleInformation
GetLargePageMinimum
GetLargestConsoleWindowSize
GetLastError
GetLinguistLangSize
GetLocalTime
GetLocaleInfoA
GetLocaleInfoEx
GetLocaleInfoW
GetLogicalDriveStringsA
GetLogicalDriveStringsW
GetLogicalDrives
GetLogicalProcessorInformation
GetLogicalProcessorInformationEx
GetLongPathNameA
GetLongPathNameTransactedA
GetLongPathNameTransactedW
GetLongPathNameW
GetMailslotInfo
GetMaximumProcessorCount
GetMaximumProcessorGroupCount
GetMemoryErrorHandlingCapabilities
GetModuleFileNameA
GetModuleFileNameW
GetModuleHandleA
GetModuleHandleExA
GetModuleHandleExW
GetModuleHandleW
GetNLSVersion
GetNLSVersionEx
GetNamedPipeAttribute
GetNamedPipeClientComputerNameA
GetNamedPipeClientComputerNameW
GetNamedPipeClientProcessId
GetNamedPipeClientSessionId
GetNamedPipeHandleStateA
GetNamedPipeHandleStateW
GetNamedPipeInfo
GetNamedPipeServerProcessId
GetNamedPipeServerSessionId
GetNativeSystemInfo
F_X64(GetNextUmsListItem)
GetNextVDMCommand
GetNlsSectionName
GetNumaAvailableMemoryNode
GetNumaAvailableMemoryNodeEx
GetNumaHighestNodeNumber
GetNumaNodeNumberFromHandle
GetNumaNodeProcessorMask
GetNumaNodeProcessorMaskEx
GetNumaProcessorNode
GetNumaProcessorNodeEx
GetNumaProximityNode
GetNumaProximityNodeEx
GetNumberFormatA
GetNumberFormatEx
GetNumberFormatW
GetNumberOfConsoleFonts
GetNumberOfConsoleInputEvents
GetNumberOfConsoleMouseButtons
GetOEMCP
GetOverlappedResult
GetOverlappedResultEx
GetPackageApplicationIds
GetPackageFamilyName
GetPackageFullName
GetPackageId
GetPackageInfo
GetPackagePath
GetPackagePathByFullName
GetPackagesByPackageFamily
GetPhysicallyInstalledSystemMemory
GetPriorityClass
GetPrivateProfileIntA
GetPrivateProfileIntW
GetPrivateProfileSectionA
GetPrivateProfileSectionNamesA
GetPrivateProfileSectionNamesW
GetPrivateProfileSectionW
GetPrivateProfileStringA
GetPrivateProfileStringW
GetPrivateProfileStructA
GetPrivateProfileStructW
GetProcAddress
GetProcessAffinityMask
GetProcessDefaultCpuSets
GetProcessDEPPolicy
GetProcessGroupAffinity
GetProcessHandleCount
GetProcessHeap
GetProcessHeaps
GetProcessId
GetProcessIdOfThread
GetProcessInformation
GetProcessIoCounters
GetProcessMitigationPolicy
GetProcessPreferredUILanguages
GetProcessPriorityBoost
GetProcessShutdownParameters
GetProcessTimes
GetProcessVersion
GetProcessWorkingSetSize
GetProcessWorkingSetSizeEx
GetProcessorSystemCycleTime
GetProductInfo
GetProfileIntA
GetProfileIntW
GetProfileSectionA
GetProfileSectionW
GetProfileStringA
GetProfileStringW
GetQueuedCompletionStatus
GetQueuedCompletionStatusEx
GetShortPathNameA
GetShortPathNameW
GetStagedPackagePathByFullName
GetStartupInfoA
GetStartupInfoW
GetStateFolder
GetStdHandle
GetStringScripts
GetStringTypeA
GetStringTypeExA
GetStringTypeExW
GetStringTypeW
GetSystemAppDataKey
GetSystemCpuSetInformation
GetSystemDEPPolicy
GetSystemDefaultLCID
GetSystemDefaultLangID
GetSystemDefaultLocaleName
GetSystemDefaultUILanguage
GetSystemDirectoryA
GetSystemDirectoryW
GetSystemFileCacheSize
GetSystemFirmwareTable
GetSystemInfo
GetSystemPowerStatus
GetSystemPreferredUILanguages
GetSystemRegistryQuota
GetSystemTime
GetSystemTimeAdjustment
GetSystemTimeAsFileTime
GetSystemTimePreciseAsFileTime
GetSystemTimes
GetSystemWindowsDirectoryA
GetSystemWindowsDirectoryW
GetSystemWow64DirectoryA
GetSystemWow64DirectoryW
GetTapeParameters
GetTapePosition
GetTapeStatus
GetTempFileNameA
GetTempFileNameW
GetTempPathA
GetTempPathW
GetThreadContext
GetThreadDescription
GetThreadErrorMode
GetThreadGroupAffinity
GetThreadIOPendingFlag
GetThreadId
GetThreadIdealProcessorEx
GetThreadInformation
GetThreadLocale
GetThreadPreferredUILanguages
GetThreadPriority
GetThreadPriorityBoost
GetThreadSelectedCpuSets
GetThreadSelectorEntry
GetThreadTimes
GetThreadUILanguage
GetTickCount
GetTickCount64
GetTimeFormatA
GetTimeFormatAWorker
GetTimeFormatEx
GetTimeFormatW
GetTimeFormatWWorker
GetTimeZoneInformation
GetTimeZoneInformationForYear
GetUILanguageInfo
F_X64(GetUmsCompletionListEvent)
GetUmsSystemThreadInformation
GetUserDefaultGeoName
GetUserDefaultLCID
GetUserDefaultLangID
GetUserDefaultLocaleName
GetUserDefaultUILanguage
GetUserGeoID
GetUserPreferredUILanguages
GetVDMCurrentDirectories
GetVersion
GetVersionExA
GetVersionExW
GetVolumeInformationA
GetVolumeInformationByHandleW
GetVolumeInformationW
GetVolumeNameForVolumeMountPointA
GetVolumeNameForVolumeMountPointW
GetVolumePathNameA
GetVolumePathNameW
GetVolumePathNamesForVolumeNameA
GetVolumePathNamesForVolumeNameW
GetWindowsDirectoryA
GetWindowsDirectoryW
GetWriteWatch
GetXStateFeaturesMask
GlobalAddAtomA
GlobalAddAtomExA
GlobalAddAtomExW
GlobalAddAtomW
GlobalAlloc
GlobalCompact
GlobalDeleteAtom
GlobalFindAtomA
GlobalFindAtomW
GlobalFix
GlobalFlags
GlobalFree
GlobalGetAtomNameA
GlobalGetAtomNameW
GlobalHandle
GlobalLock
GlobalMemoryStatus
GlobalMemoryStatusEx
GlobalReAlloc
GlobalSize
GlobalUnWire
GlobalUnfix
GlobalUnlock
GlobalWire
Heap32First
Heap32ListFirst
Heap32ListNext
Heap32Next
HeapAlloc
HeapCompact
HeapCreate
HeapCreateTagsW
HeapDestroy
HeapExtend
HeapFree
HeapLock
HeapQueryInformation
HeapQueryTagW
HeapReAlloc
HeapSetInformation
HeapSize
HeapSummary
HeapUnlock
HeapUsage
HeapValidate
HeapWalk
IdnToAscii
IdnToNameprepUnicode
IdnToUnicode
InitAtomTable
InitOnceBeginInitialize
InitOnceComplete
InitOnceExecuteOnce
InitOnceInitialize
InitializeConditionVariable
InitializeContext
InitializeContext2
InitializeCriticalSection
InitializeCriticalSectionAndSpinCount
InitializeCriticalSectionEx
InitializeEnclave
InitializeExtendedContext
InitializeProcThreadAttributeList
InitializeSListHead
InitializeSRWLock
InitializeSynchronizationBarrier
InstallELAMCertificateInfo
InterlockedFlushSList
InterlockedPopEntrySList
InterlockedPushEntrySList
InterlockedPushListSList
InterlockedPushListSListEx
InvalidateConsoleDIBits
IsBadCodePtr
IsBadHugeReadPtr
IsBadHugeWritePtr
IsBadReadPtr
IsBadStringPtrA
IsBadStringPtrW
IsBadWritePtr
IsCalendarLeapDay
IsCalendarLeapMonth
IsCalendarLeapYear
IsDBCSLeadByte
IsDBCSLeadByteEx
IsDebuggerPresent
IsEnclaveTypeSupported
IsNLSDefinedString
IsNativeVhdBoot
IsNormalizedString
IsProcessCritical
IsProcessInJob
IsProcessorFeaturePresent
IsSystemResumeAutomatic
IsThreadAFiber
IsThreadpoolTimerSet
IsTimeZoneRedirectionEnabled
IsValidCalDateTime
IsValidCodePage
IsValidLanguageGroup
IsValidLocale
IsValidUILanguage
IsValidLocaleName
IsValidNLSVersion
IsWow64GuestMachineSupported
IsWow64Process
IsWow64Process2
K32EmptyWorkingSet
K32EnumDeviceDrivers
K32EnumPageFilesA
K32EnumPageFilesW
K32EnumProcessModules
K32EnumProcessModulesEx
K32EnumProcesses
K32GetDeviceDriverBaseNameA
K32GetDeviceDriverBaseNameW
K32GetDeviceDriverFileNameA
K32GetDeviceDriverFileNameW
K32GetMappedFileNameA
K32GetMappedFileNameW
K32GetModuleBaseNameA
K32GetModuleBaseNameW
K32GetModuleFileNameExA
K32GetModuleFileNameExW
K32GetModuleInformation
K32GetPerformanceInfo
K32GetProcessImageFileNameA
K32GetProcessImageFileNameW
K32GetProcessMemoryInfo
K32GetWsChanges
K32GetWsChangesEx
K32InitializeProcessForWsWatch
K32QueryWorkingSet
K32QueryWorkingSetEx
LCIDToLocaleName
LCMapStringA
LCMapStringEx
LCMapStringW
LZClose
LZCloseFile
LZCopy
LZCreateFileW
LZDone
LZInit
LZOpenFileA
LZOpenFileW
LZRead
LZSeek
LZStart
LeaveCriticalSection
LeaveCriticalSectionWhenCallbackReturns
LoadAppInitDlls
LoadEnclaveData
LoadLibraryA
LoadLibraryExA
LoadLibraryExW
LoadLibraryW
LoadModule
LoadPackagedLibrary
LoadResource
LoadStringBaseExW
LoadStringBaseW
LocalAlloc
LocalCompact
LocalFileTimeToFileTime
LocalFileTimeToLocalSystemTime
LocalFlags
LocalFree
LocalHandle
LocalLock
LocalReAlloc
LocalShrink
LocalSize
LocalSystemTimeToLocalFileTime
LocalUnlock
LocaleNameToLCID
LocateExtendedFeature
LocateLegacyContext
LocateXStateFeature
LockFile
LockFileEx
LockResource
MapUserPhysicalPages
MapUserPhysicalPagesScatter
MapViewOfFile
MapViewOfFileEx
MapViewOfFileExNuma
MapViewOfFileFromApp
Module32First
Module32FirstW
Module32Next
Module32NextW
MoveFileA
MoveFileExA
MoveFileExW
MoveFileTransactedA
MoveFileTransactedW
MoveFileW
MoveFileWithProgressA
MoveFileWithProgressW
MulDiv
MultiByteToWideChar
NeedCurrentDirectoryForExePathA
NeedCurrentDirectoryForExePathW
NlsConvertIntegerToString
NlsCheckPolicy
NlsEventDataDescCreate
NlsGetCacheUpdateCount
NlsUpdateLocale
NlsUpdateSystemLocale
NlsResetProcessLocale
NlsWriteEtwEvent
NormalizeString
NotifyMountMgr
NotifyUILanguageChange
NtVdm64CreateProcessInternalW
OOBEComplete
OfferVirtualMemory
OpenConsoleW
OpenConsoleWStub
OpenDataFile
OpenEventA
OpenEventW
OpenFile
OpenFileById
OpenFileMappingA
OpenFileMappingW
OpenJobObjectA
OpenJobObjectW
OpenMutexA
OpenMutexW
OpenPackageInfoByFullName
OpenPrivateNamespaceA
OpenPrivateNamespaceW
OpenProcess
; MSDN says OpenProcessToken is from Advapi32.dll, not Kernel32.dll
; OpenProcessToken
OpenProfileUserMapping
OpenSemaphoreA
OpenSemaphoreW
OpenState
OpenStateExplicit
OpenThread
; MSDN says this is exported from ADVAPI32.DLL.
; OpenThreadToken
OpenWaitableTimerA
OpenWaitableTimerW
OutputDebugStringA
OutputDebugStringW
PackageFamilyNameFromFullName
PackageFamilyNameFromId
PackageFullNameFromId
PackageIdFromFullName
PackageNameAndPublisherIdFromFamilyName
ParseApplicationUserModelId
PeekConsoleInputA
PeekConsoleInputW
PeekNamedPipe
PostQueuedCompletionStatus
PowerClearRequest
PowerCreateRequest
PowerSetRequest
PrefetchVirtualMemory
PrepareTape
PrivCopyFileExW
PrivMoveFileIdentityW
Process32First
Process32FirstW
Process32Next
Process32NextW
ProcessIdToSessionId
PssCaptureSnapshot
PssDuplicateSnapshot
PssFreeSnapshot
PssQuerySnapshot
PssWalkMarkerCreate
PssWalkMarkerFree
PssWalkMarkerGetPosition
PssWalkMarkerRewind
PssWalkMarkerSeek
PssWalkMarkerSeekToBeginning
PssWalkMarkerSetPosition
PssWalkMarkerTell
PssWalkSnapshot
PulseEvent
PurgeComm
QueryActCtxSettingsW
QueryActCtxSettingsWWorker
QueryActCtxW
QueryActCtxWWorker
QueryDepthSList
QueryDosDeviceA
QueryDosDeviceW
QueryFullProcessImageNameA
QueryFullProcessImageNameW
QueryIdleProcessorCycleTime
QueryIdleProcessorCycleTimeEx
QueryInformationJobObject
QueryIoRateControlInformationJobObject
QueryMemoryResourceNotification
QueryPerformanceCounter
QueryPerformanceFrequency
QueryProcessAffinityUpdateMode
QueryProcessCycleTime
QueryProtectedPolicy
QueryThreadCycleTime
QueryThreadProfiling
QueryThreadpoolStackInformation
F_X64(QueryUmsThreadInformation)
QueryUnbiasedInterruptTime
QueueUserAPC
QueueUserWorkItem
QuirkGetData2Worker
QuirkGetDataWorker
QuirkIsEnabled2Worker
QuirkIsEnabled3Worker
QuirkIsEnabledForPackage2Worker
QuirkIsEnabledForPackage3Worker
QuirkIsEnabledForPackage4Worker
QuirkIsEnabledForPackageWorker
QuirkIsEnabledForProcessWorker
QuirkIsEnabledWorker
RaiseException
RaiseFailFastException
RaiseInvalid16BitExeError
ReOpenFile
ReclaimVirtualMemory
ReadConsoleA
ReadConsoleInputA
ReadConsoleInputExA
ReadConsoleInputExW
ReadConsoleInputW
ReadConsoleOutputA
ReadConsoleOutputAttribute
ReadConsoleOutputCharacterA
ReadConsoleOutputCharacterW
ReadConsoleOutputW
ReadConsoleW
ReadDirectoryChangesExW
ReadDirectoryChangesW
ReadFile
ReadFileEx
ReadFileScatter
ReadProcessMemory
ReadThreadProfilingData
;
; MSDN says these functions are exported
; from advapi32.dll. Commented out for
; compatibility with older versions of
; Windows.
;
; RegKrnGetGlobalState and RegKrnInitialize
; are known exceptions.
;
;RegCloseKey
;RegCopyTreeW
;RegCreateKeyExA
;RegCreateKeyExW
;RegDeleteKeyExA
;RegDeleteKeyExW
;RegDeleteTreeA
;RegDeleteTreeW
;RegDeleteValueA
;RegDeleteValueW
;RegDisablePredefinedCacheEx
;RegEnumKeyExA
;RegEnumKeyExW
;RegEnumValueA
;RegEnumValueW
;RegFlushKey
;RegGetKeySecurity
;RegGetValueA
;RegGetValueW
RegKrnGetGlobalState
RegKrnInitialize
;RegLoadKeyA
;RegLoadKeyW
;RegLoadMUIStringA
;RegLoadMUIStringW
;RegNotifyChangeKeyValue
;RegOpenCurrentUser
;RegOpenKeyExA
;RegOpenKeyExW
;RegOpenUserClassesRoot
;RegQueryInfoKeyA
;RegQueryInfoKeyW
;RegQueryValueExA
;RegQueryValueExW
;RegRestoreKeyA
;RegRestoreKeyW
;RegSaveKeyExA
;RegSaveKeyExW
;RegSetKeySecurity
;RegSetValueExA
;RegSetValueExW
;RegUnLoadKeyA
;RegUnLoadKeyW
RegisterApplicationRecoveryCallback
RegisterApplicationRestart
RegisterBadMemoryNotification
RegisterConsoleIME
RegisterConsoleOS2
RegisterConsoleVDM
RegisterWaitForInputIdle
RegisterWaitForSingleObject
RegisterWaitForSingleObjectEx
RegisterWaitUntilOOBECompleted
RegisterWowBaseHandlers
RegisterWowExec
ReleaseActCtx
ReleaseActCtxWorker
ReleaseMutex
ReleaseMutexWhenCallbackReturns
ReleaseSRWLockExclusive
ReleaseSRWLockShared
ReleaseSemaphore
ReleaseSemaphoreWhenCallbackReturns
RemoveDirectoryA
RemoveDirectoryTransactedA
RemoveDirectoryTransactedW
RemoveDirectoryW
RemoveDllDirectory
RemoveLocalAlternateComputerNameA
RemoveLocalAlternateComputerNameW
RemoveSecureMemoryCacheCallback
RemoveVectoredContinueHandler
RemoveVectoredExceptionHandler
ReplaceFile
ReplaceFileA
ReplaceFileW
ReplacePartitionUnit
RequestDeviceWakeup
RequestWakeupLatency
ResetEvent
ResetWriteWatch
ResizePseudoConsole
ResolveDelayLoadedAPI
ResolveDelayLoadsFromDll
ResolveLocaleName
RestoreLastError
ResumeThread
RtlAddFunctionTable
RtlCaptureContext
RtlCaptureStackBackTrace
RtlCompareMemory
RtlCopyMemory
RtlDeleteFunctionTable
RtlFillMemory
RtlInstallFunctionTableCallback
RtlLookupFunctionEntry
RtlMoveMemory
RtlPcToFileHeader
RtlRaiseException
RtlRestoreContext
RtlUnwind
RtlUnwindEx
RtlVirtualUnwind
RtlZeroMemory
ScrollConsoleScreenBufferA
ScrollConsoleScreenBufferW
SearchPathA
SearchPathW
SetCachedSigningLevel
SetCPGlobal
SetCalendarInfoA
SetCalendarInfoW
SetComPlusPackageInstallStatus
SetCommBreak
SetCommConfig
SetCommMask
SetCommState
SetCommTimeouts
SetComputerNameA
SetComputerNameEx2W
SetComputerNameExA
SetComputerNameExW
SetComputerNameW
SetConsoleActiveScreenBuffer
SetConsoleCP
SetConsoleCommandHistoryMode
SetConsoleCtrlHandler
SetConsoleCursor
SetConsoleCursorInfo
SetConsoleCursorMode
SetConsoleCursorPosition
SetConsoleDisplayMode
SetConsoleFont
SetConsoleHardwareState
SetConsoleHistoryInfo
SetConsoleIcon
SetConsoleInputExeNameA
SetConsoleInputExeNameW
SetConsoleKeyShortcuts
SetConsoleLocalEUDC
SetConsoleMaximumWindowSize
SetConsoleMenuClose
SetConsoleMode
SetConsoleNlsMode
SetConsoleNumberOfCommandsA
SetConsoleNumberOfCommandsW
SetConsoleOS2OemFormat
SetConsoleOutputCP
SetConsolePalette
SetConsoleScreenBufferInfoEx
SetConsoleScreenBufferSize
SetConsoleTextAttribute
SetConsoleTitleA
SetConsoleTitleW
SetConsoleWindowInfo
SetCriticalSectionSpinCount
SetCurrentConsoleFontEx
SetCurrentDirectoryA
SetCurrentDirectoryW
SetDefaultCommConfigA
SetDefaultCommConfigW
SetDefaultDllDirectories
SetDllDirectoryA
SetDllDirectoryW
SetDynamicTimeZoneInformation
SetEndOfFile
SetEnvironmentStringsA
SetEnvironmentStringsW
SetEnvironmentVariableA
SetEnvironmentVariableW
SetErrorMode
SetEvent
SetEventWhenCallbackReturns
SetExtendedFeaturesMask
SetFileApisToANSI
SetFileApisToOEM
SetFileAttributesA
SetFileAttributesTransactedA
SetFileAttributesTransactedW
SetFileAttributesW
SetFileBandwidthReservation
SetFileCompletionNotificationModes
SetFileInformationByHandle
SetFileIoOverlappedRange
SetFilePointer
SetFilePointerEx
SetFileShortNameA
SetFileShortNameW
SetFileTime
SetFileValidData
SetFirmwareEnvironmentVariableA
SetFirmwareEnvironmentVariableExA
SetFirmwareEnvironmentVariableExW
SetFirmwareEnvironmentVariableW
SetHandleCount
SetHandleInformation
SetInformationJobObject
SetIoRateControlInformationJobObject
SetLastConsoleEventActive
SetLastError
SetLocalPrimaryComputerNameA
SetLocalPrimaryComputerNameW
SetLocalTime
SetLocaleInfoA
SetLocaleInfoW
SetMailslotInfo
SetMessageWaitingIndicator
SetNamedPipeAttribute
SetNamedPipeHandleState
SetPriorityClass
SetProcessAffinityMask
SetProcessAffinityUpdateMode
SetProcessDEPPolicy
SetProcessDefaultCpuSets
SetProcessInformation
SetProcessMitigationPolicy
SetProcessPreferredUILanguages
SetProcessPriorityBoost
SetProcessShutdownParameters
SetProcessWorkingSetSize
SetProcessWorkingSetSizeEx
SetProtectedPolicy
SetSearchPathMode
SetStdHandle
SetStdHandleEx
SetSystemFileCacheSize
SetSystemPowerState
SetSystemTime
SetSystemTimeAdjustment
SetTapeParameters
SetTapePosition
SetTermsrvAppInstallMode
SetThreadAffinityMask
SetThreadContext
SetThreadDescription
SetThreadErrorMode
SetThreadExecutionState
SetThreadGroupAffinity
SetThreadIdealProcessor
SetThreadIdealProcessorEx
SetThreadInformation
SetThreadLocale
SetThreadPreferredUILanguages
SetThreadPriority
SetThreadPriorityBoost
SetThreadSelectedCpuSets
SetThreadStackGuarantee
; MSDN says this is exported from ADVAPI32.DLL.
; SetThreadToken
SetThreadUILanguage
SetThreadpoolStackInformation
SetThreadpoolThreadMaximum
SetThreadpoolThreadMinimum
SetThreadpoolTimer
SetThreadpoolTimerEx
SetThreadpoolWait
SetThreadpoolWaitEx
SetTimeZoneInformation
SetTimerQueueTimer
F_X64(SetUmsThreadInformation)
SetUnhandledExceptionFilter
SetUserGeoID
SetUserGeoName
SetVDMCurrentDirectories
SetVolumeLabelA
SetVolumeLabelW
SetVolumeMountPointA
SetVolumeMountPointW
SetVolumeMountPointWStub
SetWaitableTimer
SetWaitableTimerEx
SetXStateFeaturesMask
SetupComm
ShowConsoleCursor
SignalObjectAndWait
SizeofResource
Sleep
SleepConditionVariableCS
SleepConditionVariableSRW
SleepEx
SortCloseHandle
SortGetHandle
StartThreadpoolIo
SubmitThreadpoolWork
SuspendThread
SwitchToFiber
SwitchToThread
SystemTimeToFileTime
SystemTimeToTzSpecificLocalTime
SystemTimeToTzSpecificLocalTimeEx
TerminateJobObject
TerminateProcess
TerminateThread
TermsrvAppInstallMode
TermsrvConvertSysRootToUserDir
TermsrvCreateRegEntry
TermsrvDeleteKey
TermsrvDeleteValue
TermsrvGetPreSetValue
TermsrvGetWindowsDirectoryA
TermsrvGetWindowsDirectoryW
TermsrvOpenRegEntry
TermsrvOpenUserClasses
TermsrvRestoreKey
TermsrvSetKeySecurity
TermsrvSetValueKey
TermsrvSyncUserIniFileExt
Thread32First
Thread32Next
TlsAlloc
TlsFree
TlsGetValue
TlsSetValue
Toolhelp32ReadProcessMemory
TransactNamedPipe
TransmitCommChar
TryAcquireSRWLockExclusive
TryAcquireSRWLockShared
TryEnterCriticalSection
TrySubmitThreadpoolCallback
TzSpecificLocalTimeToSystemTime
TzSpecificLocalTimeToSystemTimeEx
UTRegister
UTUnRegister
F_X64(UmsThreadYield)
UnhandledExceptionFilter
UnlockFile
UnlockFileEx
UnmapViewOfFile
UnmapViewOfFileEx
UnregisterApplicationRecoveryCallback
UnregisterApplicationRestart
UnregisterBadMemoryNotification
UnregisterConsoleIME
UnregisterWait
UnregisterWaitEx
UnregisterWaitUntilOOBECompleted
UpdateCalendarDayOfWeek
UpdateProcThreadAttribute
UpdateResourceA
UpdateResourceW
VDMConsoleOperation
VDMOperationStarted
ValidateLCType
ValidateLocale
VerLanguageNameA
VerLanguageNameW
VerSetConditionMask
VerifyConsoleIoHandle
VerifyScripts
VerifyVersionInfoA
VerifyVersionInfoW
VirtualAlloc
VirtualAllocEx
VirtualAllocExNuma
VirtualFree
VirtualFreeEx
VirtualLock
VirtualProtect
VirtualProtectEx
VirtualQuery
VirtualQueryEx
VirtualUnlock
WTSGetActiveConsoleSessionId
WaitCommEvent
WaitForDebugEvent
WaitForDebugEventEx
WaitForMultipleObjects
WaitForMultipleObjectsEx
WaitForSingleObject
WaitForSingleObjectEx
WaitForThreadpoolIoCallbacks
WaitForThreadpoolTimerCallbacks
WaitForThreadpoolWaitCallbacks
WaitForThreadpoolWorkCallbacks
WaitNamedPipeA
WaitNamedPipeW
WakeAllConditionVariable
; MSDN says it's in Kernel32.dll but it's not.
; Link with libsynchronization.a instead.
; Commented out for compatibility with older 
; versions of Windows.
;WaitOnAddress
;WakeByAddressSingle
;WakeByAddressAll
WakeConditionVariable
WerGetFlags
WerGetFlagsWorker
WerRegisterAdditionalProcess
WerRegisterAppLocalDump
WerRegisterCustomMetadata
WerRegisterExcludedMemoryBlock
WerRegisterFile
WerRegisterFileWorker
WerRegisterMemoryBlock
WerRegisterMemoryBlockWorker
WerRegisterRuntimeExceptionModule
WerRegisterRuntimeExceptionModuleWorker
WerSetFlags
WerSetFlagsWorker
WerUnregisterAdditionalProcess
WerUnregisterAppLocalDump
WerUnregisterCustomMetadata
WerUnregisterExcludedMemoryBlock
WerUnregisterFile
WerUnregisterFileWorker
WerUnregisterMemoryBlock
WerUnregisterMemoryBlockWorker
WerUnregisterRuntimeExceptionModule
WerUnregisterRuntimeExceptionModuleWorker
WerpCleanupMessageMapping
WerpGetDebugger
WerpInitiateRemoteRecovery
WerpLaunchAeDebug
WerpNotifyLoadStringResource
WerpNotifyLoadStringResourceEx
WerpNotifyLoadStringResourceWorker
WerpNotifyUseStringResource
WerpNotifyUseStringResourceWorker
WerpStringLookup
WideCharToMultiByte
WinExec
Wow64DisableWow64FsRedirection
Wow64EnableWow64FsRedirection
Wow64GetThreadContext
Wow64GetThreadSelectorEntry
Wow64RevertWow64FsRedirection
Wow64SetThreadContext
Wow64SuspendThread
WriteConsoleA
WriteConsoleInputA
WriteConsoleInputVDMA
WriteConsoleInputVDMW
WriteConsoleInputW
WriteConsoleOutputA
WriteConsoleOutputAttribute
WriteConsoleOutputCharacterA
WriteConsoleOutputCharacterW
WriteConsoleOutputW
WriteConsoleW
WriteFile
WriteFileEx
WriteFileGather
WritePrivateProfileSectionA
WritePrivateProfileSectionW
WritePrivateProfileStringA
WritePrivateProfileStringW
WritePrivateProfileStructA
WritePrivateProfileStructW
WriteProcessMemory
WriteProfileSectionA
WriteProfileSectionW
WriteProfileStringA
WriteProfileStringW
WriteTapemark
ZombifyActCtx
ZombifyActCtxWorker
F_X64(__C_specific_handler)
F_ARM32(__C_specific_handler)
; This isn't always available and shouldn't be linked from here, but should
; be statically linked from the compiler support library.
;F_ARM32(__chkstk)
F_X64(__misaligned_access)
_hread
_hwrite
_lclose
_lcreat
_llseek
_local_unwind
_lopen
_lread
_lwrite
lstrcat
lstrcatA
lstrcatW
lstrcmp
lstrcmpA
lstrcmpW
lstrcmpi
lstrcmpiA
lstrcmpiW
lstrcpy
lstrcpyA
lstrcpyW
lstrcpyn
lstrcpynA
lstrcpynW
lstrlen
lstrlenA
lstrlenW
;
; MSDN says these functions are exported
; from winmm.dll. Commented out for
; compatibility with older versions of
; Windows.
;
;timeBeginPeriod
;timeEndPeriod
;timeGetDevCaps
;timeGetSystemTime
;timeGetTime
uaw_lstrcmpW
uaw_lstrcmpiW
uaw_lstrlenW
uaw_wcschr
uaw_wcscpy
uaw_wcsicmp
uaw_wcslen
uaw_wcsrchr
