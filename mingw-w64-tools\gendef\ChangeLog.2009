2009-12-24  <PERSON><PERSON> Bar-Lev  <<EMAIL>>

	* Makefile.am (gendef_SOURCES): Add src/compat_string.c file.
	* configure.ac (AC_CHECK_FUNCS): Check for strlwr.
	* src/compat-string.h: New header.
	* sre/compat_string.c: New source-file.
	* Makefile.in: Regenerated.
	* config.h.in: Likewise.
	* configure: Likewise.

2009-12-23  <PERSON>  <<EMAIL>>

	* src/gendef.c: Avoid warnings by building without libmangle.

2009-12-13  <PERSON>  <<EMAIL>>

	* src/gendef.c: Change include path to -I/--include-def-path.
	Add "Built on __DATE__".

2009-12-13  Kai <PERSON>  <<EMAIL>>

	* src/gendef.c: Add option -p/-path-defs.
	* src/gendef_def.c: Add search-paths for def file seeking.

2009-12-12  <PERSON>  <<EMAIL>>

	* src/gendef.c (show_usage): Improve help message about -a.

2009-12-12  <PERSON>  <<EMAIL>>

	* src/gendef_def.c: New file.
	* src/gendef.c: Add config.h header.
	Add optional use of libmangle.
	Improved output of .def files.
	Ouput forwarders, too.

2009-12-12  Jonathan Yong  <<EMAIL>>

	* configure.ac: Check for libmangle install.
	* Makefile.am: Add libmangle dependencies.

	* Makefile.am: Add gendef_def.c dependencies.
	* configure: Regenerate.
	* Makefile.in: Ditto.
	* config.h.in: Ditto.

2009-12-11  Kai Tietz  <<EMAIL>>

	* src/gendef.c (assume_stdcall): New variable.
	(opt_chain): Check for -a and --assume-stdcall option.
	(disassembleRetIntern): Use assume_stdcall for ret.

2009-12-10  Kai Tietz  <<EMAIL>>

	* src/gendef.c (decodeMemonic): Fixed typo.
	(Additionally added more debug output)

2009-12-01  Jonathan Yong  <<EMAIL>>

	* src/gendef.c (show_usage): New.
	(main): Use show_usage.
	(opt_chain): use show_usage.

2009-09-02  Kai Tietz  <<EMAIL>>

	* src/gendef.c (disassembleRetIntern): Pre-initialize local
	variables by zero.

2009-08-17  Kai Tietz  <<EMAIL>>

	* gendef.h, gendef.c: Add BSD license.
	* COPYING: New file.

2009-02-18  Kai Tietz  <<EMAIL>>

	* gendef.c (main): Check if gPEDta or gPEPDta is
	valid, before trying to dump.

2009-01-15  Kai Tietz  <<EMAIL>>

	* gendef.c (is_reloc): New.
	(is_data): Check if va is relocated.

2009-01-14  Kai Tietz  <<EMAIL>>

	* gendef.h (IMAGE_FIRST_SECTION): Added.
	(Gendefopts): Added.
	(sExportName): Moved from gendef.c.
	(eOpCodeKind): Likewise.
	(sAddresses): Likewise.
	* gendef.c: Remove unused nacro,
	(IMAGE_DIRECTORY_ENTRY_EXPORT): Removed.
	(sExportName): Moved to header.
	(eOpCodeKind): Likewise.
	(sAddresses): Likewise.

2009-01-07  jonY  <<EMAIL>>

	* gendef.c (struct gendefopts): New.
	(opt_chain): New.
	(std_output): New.
	(main): Use opt_chain.
	(dump_def): Treat std_output.
	(load_pep): Adjust error messages.

2009-01-07  Kai Tietz  <<EMAIL>>

	* gendef.c: New.
	* gendef.h: New.

