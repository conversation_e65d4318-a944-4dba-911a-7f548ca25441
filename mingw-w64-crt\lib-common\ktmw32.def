;
; Definition file of ktmw32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "ktmw32.dll"
EXPORTS
CommitComplete
CommitEnlistment
CommitTransaction
CommitTransactionAsync
CreateEnlistment
CreateResourceManager
CreateTransaction
CreateTransactionManager
GetCurrentClockTransactionManager
GetEnlistmentId
GetEnlistmentRecoveryInformation
GetNotificationResourceManager
GetNotificationResourceManagerAsync
GetTransactionId
GetTransactionInformation
GetTransactionManagerId
OpenEnlistment
OpenResourceManager
OpenTransaction
OpenTransactionManager
OpenTransactionManagerById
PrePrepareComplete
PrePrepareEnlistment
PrepareComplete
PrepareEnlistment
PrivCreateTransaction
PrivIsLogWritableTransactionManager
PrivPropagationComplete
PrivPropagationFailed
PrivRegisterProtocolAddressInformation
ReadOn<PERSON>Enlistment
RecoverEnlistment
RecoverResourceManager
RecoverTransactionManager
RenameTransactionManager
RollbackComplete
RollbackEnlistment
RollbackTransaction
RollbackTransactionAsync
RollforwardTransactionManager
SetEnlistmentRecoveryInformation
SetResourceManagerCompletionPort
SetTransactionInformation
SinglePhaseReject
