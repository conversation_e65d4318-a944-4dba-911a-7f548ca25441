;
; Definition file of MF.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "MF.dll"
EXPORTS
AppendPropVariant
ConvertPropVariant
CopyPropertyStore
CreateNamedPropertyStore
; DllCanUnloadNow
; DllGetActivationFactory
; DllGetClassObject
; DllRegisterServer
; DllUnregisterServer
ExtractPropVariant
MFCreate3GPMediaSink
MFCreateAC3MediaSink
MFCreateADTSMediaSink
MFCreateASFByteStreamPlugin
MFCreateASFContentInfo
MFCreateASFIndexer
MFCreateASFIndexerByteStream
MFCreateASFMediaSink
MFCreateASFMediaSinkActivate
MFCreateASFMultiplexer
MFCreateASFProfile
MFCreateASFProfileFromPresentationDescriptor
MFCreateASFSplitter
MFCreateASFStreamSelector
MFCreateASFStreamingMediaSink
MFCreateASFStreamingMediaSinkActivate
MFCreateAggregateSource
MFCreateAppSourceP<PERSON>oRenderer
MFCreateAudioRendererActivate
MFCreateByteCacheFile
MFCreateCacheManager
MFCreateCredentialCache
MFCreateDeviceSource
MFCreateDeviceSourceActivate
MFCreateDrmNetNDSchemePlugin
MFCreateEncryptedMediaExtensionsStoreActivate
MFCreateFMPEG4MediaSink
MFCreateFileBlockMap
MFCreateFileSchemePlugin
MFCreateHttpSchemePlugin
MFCreateLPCMByteStreamPlugin
MFCreateMP3ByteStreamPlugin
MFCreateMP3MediaSink
MFCreateMPEG4MediaSink
MFCreateMediaProcessor
MFCreateMediaSession
MFCreateMuxSink
MFCreateNSCByteStreamPlugin
MFCreateNetSchemePlugin
MFCreatePMPHost
MFCreatePMPMediaSession
MFCreatePMPServer
MFCreatePresentationClock
MFCreatePresentationDescriptorFromASFProfile
MFCreateProtectedEnvironmentAccess
MFCreateProxyLocator
MFCreateRemoteDesktopPlugin
MFCreateSAMIByteStreamPlugin
MFCreateSampleCopierMFT
MFCreateSampleGrabberSinkActivate
MFCreateSecureHttpSchemePlugin
MFCreateSequencerSegmentOffset
MFCreateSequencerSource
MFCreateSequencerSourceRemoteStream
MFCreateSimpleTypeHandler
MFCreateSoundEventSchemePlugin
MFCreateSourceResolver
MFCreateStandardQualityManager
MFCreateTopoLoader
MFCreateTopology
MFCreateTopologyNode
MFCreateTranscodeProfile
MFCreateTranscodeSinkActivate
MFCreateTranscodeTopology
MFCreateTranscodeTopologyFromByteStream
MFCreateUrlmonSchemePlugin
MFCreateVideoRenderer
MFCreateVideoRendererActivate
MFCreateWMAEncoderActivate
MFCreateWMVEncoderActivate
MFEnumDeviceSources
MFGetLocalId
MFGetMultipleServiceProviders
MFGetService
MFGetSupportedMimeTypes
MFGetSupportedSchemes
MFGetSystemId
MFGetTopoNodeCurrentType
MFLoadSignedLibrary
MFRR_CreateActivate
MFReadSequencerSegmentOffset
MFRequireProtectedEnvironment
MFShutdownObject
MFTranscodeGetAudioOutputAvailableTypes
MergePropertyStore
