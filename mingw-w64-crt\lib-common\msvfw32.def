; 
; Exports of file MSVFW32.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY MSVFW32.dll
EXPORTS
VideoForWindowsVersion
DrawDibBegin
DrawDibChangePalette
DrawDibClose
DrawDibDraw
DrawDibEnd
DrawDibGetBuffer
DrawDibGetPalette
DrawDibOpen
DrawDibProfileDisplay
DrawDibRealize
DrawDibSetPalette
DrawDibStart
DrawDibStop
DrawDibTime
GetOpenFileNamePreview
GetOpenFileNamePreviewA
GetOpenFileNamePreviewW
GetSaveFileNamePreviewA
GetSaveFileNamePreviewW
ICClose
ICCompress
ICCompressorChoose
ICCompressorFree
ICDecompress
ICDraw
ICDrawBegin
ICGetDisplayFormat
ICGetInfo
ICImageCompress
ICImageDecompress
ICInfo
ICInstall
ICLocate
ICMThunk32
ICOpen
ICOpenFunction
ICRemove
ICSendMessage
ICSeqCompressFrame
ICSeqCompressFrameEnd
ICSeqCompressFrameStart
MCIWndCreate
MCIWndCreateA
MCIWndCreateW
MCIWndRegisterClass
StretchDIB
