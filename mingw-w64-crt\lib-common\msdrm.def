;
; Definition file of msdrm.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "msdrm.dll"
EXPORTS
DRMAcquireAdvisories
DRMAcquireIssuanceLicenseTemplate
DRMAcquireLicense
DRMActivate
DRMAddLicense
DRMAddRightWithUser
DRMAttest
DRMCheckSecurity
DRMClearAllRights
DRMCloseEnvironmentHandle
DRMCloseHandle
DRMClosePubHandle
DRMCloseQueryHandle
DRMCloseSession
DRMConstructCertificateChain
DRMCreateBoundLicense
DRMCreateClientSession
DRMCreateEnablingBitsDecryptor
DRMCreateEnablingBitsEncryptor
DRMCreateEnablingPrincipal
DRMCreateIssuanceLicense
DRMCreateLicenseStorageSession
DRMCreateRight
DRMCreateUser
DRMDecode
DRMDeconstructCertificateChain
DRMDecrypt
DRMDeleteLicense
DRMDuplicateEnvironmentHandle
DRMDuplicateHandle
DRMDuplicatePubHandle
DRMDuplicateSession
DRMEnco<PERSON>rypt
DRMEnumerateLicense
DRMGetApplicationSpecificData
DRMGetBoundLicenseAttribute
DRMGetBoundLicenseAttributeCount
DRMGetBoundLicenseObject
DRMGetBoundLicenseObjectCount
DRMGetCertificateChainCount
DRMGetClientVersion
DRMGetEnvironmentInfo
DRMGetInfo
DRMGetIntervalTime
DRMGetIssuanceLicenseInfo
DRMGetIssuanceLicenseTemplate
DRMGetMetaData
DRMGetNameAndDescription
DRMGetOwnerLicense
DRMGetProcAddress
DRMGetRevocationPoint
DRMGetRightExtendedInfo
DRMGetRightInfo
DRMGetSecurityProvider
DRMGetServiceLocation
DRMGetSignedIssuanceLicense
DRMGetSignedIssuanceLicenseEx
DRMGetTime
DRMGetUnboundLicenseAttribute
DRMGetUnboundLicenseAttributeCount
DRMGetUnboundLicenseObject
DRMGetUnboundLicenseObjectCount
DRMGetUsagePolicy
DRMGetUserInfo
DRMGetUserRights
DRMGetUsers
DRMInitEnvironment
DRMIsActivated
DRMIsWindowProtected
DRMLoadLibrary
DRMParseUnboundLicense
DRMRegisterContent
DRMRegisterProtectedWindow
DRMRegisterRevocationList
DRMRepair
DRMSetApplicationSpecificData
DRMSetGlobalOptions
DRMSetIntervalTime
DRMSetMetaData
DRMSetNameAndDescription
DRMSetRevocationPoint
DRMSetUsagePolicy
DRMVerify
DRMpCloseFile
DRMpFileInitialize
DRMpFileIsProtected
DRMpFileProtect
DRMpFileUnprotect
DRMpFreeMemory
__AddMachineCertToLicenseStore
