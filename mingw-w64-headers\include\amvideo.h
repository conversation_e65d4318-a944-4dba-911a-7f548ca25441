/*** Autogenerated by WIDL 8.5 from include/amvideo.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __amvideo_h__
#define __amvideo_h__

#ifndef __WIDL_INLINE
#if defined(__cplusplus) || defined(_MSC_VER)
#define __WIDL_INLINE inline
#elif defined(__GNUC__)
#define __WIDL_INLINE __inline__
#endif
#endif

/* Forward declarations */

#ifndef __IDirectDrawVideo_FWD_DEFINED__
#define __IDirectDrawVideo_FWD_DEFINED__
typedef interface IDirectDrawVideo IDirectDrawVideo;
#ifdef __cplusplus
interface IDirectDrawVideo;
#endif /* __cplusplus */
#endif

#ifndef __IQualProp_FWD_DEFINED__
#define __IQualProp_FWD_DEFINED__
typedef interface IQualProp IQualProp;
#ifdef __cplusplus
interface IQualProp;
#endif /* __cplusplus */
#endif

#ifndef __IFullScreenVideo_FWD_DEFINED__
#define __IFullScreenVideo_FWD_DEFINED__
typedef interface IFullScreenVideo IFullScreenVideo;
#ifdef __cplusplus
interface IFullScreenVideo;
#endif /* __cplusplus */
#endif

#ifndef __IFullScreenVideoEx_FWD_DEFINED__
#define __IFullScreenVideoEx_FWD_DEFINED__
typedef interface IFullScreenVideoEx IFullScreenVideoEx;
#ifdef __cplusplus
interface IFullScreenVideoEx;
#endif /* __cplusplus */
#endif

#ifndef __IBaseVideoMixer_FWD_DEFINED__
#define __IBaseVideoMixer_FWD_DEFINED__
typedef interface IBaseVideoMixer IBaseVideoMixer;
#ifdef __cplusplus
interface IBaseVideoMixer;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <objidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#if 0
#ifndef __IDirectDraw_FWD_DEFINED__
#define __IDirectDraw_FWD_DEFINED__
typedef interface IDirectDraw IDirectDraw;
#ifdef __cplusplus
interface IDirectDraw;
#endif /* __cplusplus */
#endif

typedef void DDSURFACEDESC;
typedef void DDCAPS;
typedef DWORD RGBQUAD;
typedef LONGLONG REFERENCE_TIME;
typedef struct __WIDL_amvideo_generated_name_0000000E {
    DWORD biSize;
    LONG biWidth;
    LONG biHeight;
    WORD biPlanes;
    WORD biBitCount;
    DWORD biCompression;
    DWORD biSizeImage;
    LONG biXPelsPerMeter;
    LONG biYPelsPerMeter;
    DWORD biClrUsed;
    DWORD biClrImportant;
} BITMAPINFOHEADER;
typedef struct __WIDL_amvideo_generated_name_0000000E *PBITMAPINFOHEADER;
typedef struct __WIDL_amvideo_generated_name_0000000E *LPBITMAPINFOHEADER;
#endif
#include <ddraw.h>
#define AMDDS_NONE 0x00
#define AMDDS_DCIPS 0x01
#define AMDDS_PS 0x02
#define AMDDS_RGBOVR 0x04
#define AMDDS_YUVOVR 0x08
#define AMDDS_RGBOFF 0x10
#define AMDDS_YUVOFF 0x20
#define AMDDS_RGBFLP 0x40
#define AMDDS_YUVFLP 0x80
#define AMDDS_ALL 0xFF
#define AMDDS_DEFAULT AMDDS_ALL
#define AMDDS_YUV (AMDDS_YUVOFF | AMDDS_YUVOVR | AMDDS_YUVFLP)
#define AMDDS_RGB (AMDDS_RGBOFF | AMDDS_RGBOVR | AMDDS_RGBFLP)
#define AMDSS_PRIMARY (AMDDS_DCIPS | AMDDS_PS)
/*****************************************************************************
 * IDirectDrawVideo interface
 */
#ifndef __IDirectDrawVideo_INTERFACE_DEFINED__
#define __IDirectDrawVideo_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IDirectDrawVideo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSwitches(
        DWORD *pSwitches) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSwitches(
        DWORD Switches) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCaps(
        DDCAPS *pCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEmulatedCaps(
        DDCAPS *pCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSurfaceDesc(
        DDSURFACEDESC *pSurfaceDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFourCCCodes(
        DWORD *pCount,
        DWORD *pCodes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDirectDraw(
        IDirectDraw *ddraw) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDirectDraw(
        IDirectDraw **ddraw) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSurfaceType(
        DWORD *pSurfaceType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefault(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UseScanLine(
        LONG UseScanLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanUseScanLine(
        LONG *UseScanLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE UseOverlayStretch(
        LONG UseOverlayStretch) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanUseOverlayStretch(
        LONG *UseOverlayStretch) = 0;

    virtual HRESULT STDMETHODCALLTYPE UseWhenFullScreen(
        LONG UseWhenFullScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE WillUseFullScreen(
        LONG *UseWhenFullScreen) = 0;

};
#else
typedef struct IDirectDrawVideoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectDrawVideo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectDrawVideo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectDrawVideo *This);

    /*** IDirectDrawVideo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSwitches)(
        IDirectDrawVideo *This,
        DWORD *pSwitches);

    HRESULT (STDMETHODCALLTYPE *SetSwitches)(
        IDirectDrawVideo *This,
        DWORD Switches);

    HRESULT (STDMETHODCALLTYPE *GetCaps)(
        IDirectDrawVideo *This,
        DDCAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *GetEmulatedCaps)(
        IDirectDrawVideo *This,
        DDCAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *GetSurfaceDesc)(
        IDirectDrawVideo *This,
        DDSURFACEDESC *pSurfaceDesc);

    HRESULT (STDMETHODCALLTYPE *GetFourCCCodes)(
        IDirectDrawVideo *This,
        DWORD *pCount,
        DWORD *pCodes);

    HRESULT (STDMETHODCALLTYPE *SetDirectDraw)(
        IDirectDrawVideo *This,
        IDirectDraw *ddraw);

    HRESULT (STDMETHODCALLTYPE *GetDirectDraw)(
        IDirectDrawVideo *This,
        IDirectDraw **ddraw);

    HRESULT (STDMETHODCALLTYPE *GetSurfaceType)(
        IDirectDrawVideo *This,
        DWORD *pSurfaceType);

    HRESULT (STDMETHODCALLTYPE *SetDefault)(
        IDirectDrawVideo *This);

    HRESULT (STDMETHODCALLTYPE *UseScanLine)(
        IDirectDrawVideo *This,
        LONG UseScanLine);

    HRESULT (STDMETHODCALLTYPE *CanUseScanLine)(
        IDirectDrawVideo *This,
        LONG *UseScanLine);

    HRESULT (STDMETHODCALLTYPE *UseOverlayStretch)(
        IDirectDrawVideo *This,
        LONG UseOverlayStretch);

    HRESULT (STDMETHODCALLTYPE *CanUseOverlayStretch)(
        IDirectDrawVideo *This,
        LONG *UseOverlayStretch);

    HRESULT (STDMETHODCALLTYPE *UseWhenFullScreen)(
        IDirectDrawVideo *This,
        LONG UseWhenFullScreen);

    HRESULT (STDMETHODCALLTYPE *WillUseFullScreen)(
        IDirectDrawVideo *This,
        LONG *UseWhenFullScreen);

    END_INTERFACE
} IDirectDrawVideoVtbl;

interface IDirectDrawVideo {
    CONST_VTBL IDirectDrawVideoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectDrawVideo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectDrawVideo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectDrawVideo_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectDrawVideo methods ***/
#define IDirectDrawVideo_GetSwitches(This,pSwitches) (This)->lpVtbl->GetSwitches(This,pSwitches)
#define IDirectDrawVideo_SetSwitches(This,Switches) (This)->lpVtbl->SetSwitches(This,Switches)
#define IDirectDrawVideo_GetCaps(This,pCaps) (This)->lpVtbl->GetCaps(This,pCaps)
#define IDirectDrawVideo_GetEmulatedCaps(This,pCaps) (This)->lpVtbl->GetEmulatedCaps(This,pCaps)
#define IDirectDrawVideo_GetSurfaceDesc(This,pSurfaceDesc) (This)->lpVtbl->GetSurfaceDesc(This,pSurfaceDesc)
#define IDirectDrawVideo_GetFourCCCodes(This,pCount,pCodes) (This)->lpVtbl->GetFourCCCodes(This,pCount,pCodes)
#define IDirectDrawVideo_SetDirectDraw(This,ddraw) (This)->lpVtbl->SetDirectDraw(This,ddraw)
#define IDirectDrawVideo_GetDirectDraw(This,ddraw) (This)->lpVtbl->GetDirectDraw(This,ddraw)
#define IDirectDrawVideo_GetSurfaceType(This,pSurfaceType) (This)->lpVtbl->GetSurfaceType(This,pSurfaceType)
#define IDirectDrawVideo_SetDefault(This) (This)->lpVtbl->SetDefault(This)
#define IDirectDrawVideo_UseScanLine(This,UseScanLine) (This)->lpVtbl->UseScanLine(This,UseScanLine)
#define IDirectDrawVideo_CanUseScanLine(This,UseScanLine) (This)->lpVtbl->CanUseScanLine(This,UseScanLine)
#define IDirectDrawVideo_UseOverlayStretch(This,UseOverlayStretch) (This)->lpVtbl->UseOverlayStretch(This,UseOverlayStretch)
#define IDirectDrawVideo_CanUseOverlayStretch(This,UseOverlayStretch) (This)->lpVtbl->CanUseOverlayStretch(This,UseOverlayStretch)
#define IDirectDrawVideo_UseWhenFullScreen(This,UseWhenFullScreen) (This)->lpVtbl->UseWhenFullScreen(This,UseWhenFullScreen)
#define IDirectDrawVideo_WillUseFullScreen(This,UseWhenFullScreen) (This)->lpVtbl->WillUseFullScreen(This,UseWhenFullScreen)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IDirectDrawVideo_QueryInterface(IDirectDrawVideo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IDirectDrawVideo_AddRef(IDirectDrawVideo* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IDirectDrawVideo_Release(IDirectDrawVideo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectDrawVideo methods ***/
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetSwitches(IDirectDrawVideo* This,DWORD *pSwitches) {
    return This->lpVtbl->GetSwitches(This,pSwitches);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_SetSwitches(IDirectDrawVideo* This,DWORD Switches) {
    return This->lpVtbl->SetSwitches(This,Switches);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetCaps(IDirectDrawVideo* This,DDCAPS *pCaps) {
    return This->lpVtbl->GetCaps(This,pCaps);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetEmulatedCaps(IDirectDrawVideo* This,DDCAPS *pCaps) {
    return This->lpVtbl->GetEmulatedCaps(This,pCaps);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetSurfaceDesc(IDirectDrawVideo* This,DDSURFACEDESC *pSurfaceDesc) {
    return This->lpVtbl->GetSurfaceDesc(This,pSurfaceDesc);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetFourCCCodes(IDirectDrawVideo* This,DWORD *pCount,DWORD *pCodes) {
    return This->lpVtbl->GetFourCCCodes(This,pCount,pCodes);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_SetDirectDraw(IDirectDrawVideo* This,IDirectDraw *ddraw) {
    return This->lpVtbl->SetDirectDraw(This,ddraw);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetDirectDraw(IDirectDrawVideo* This,IDirectDraw **ddraw) {
    return This->lpVtbl->GetDirectDraw(This,ddraw);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_GetSurfaceType(IDirectDrawVideo* This,DWORD *pSurfaceType) {
    return This->lpVtbl->GetSurfaceType(This,pSurfaceType);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_SetDefault(IDirectDrawVideo* This) {
    return This->lpVtbl->SetDefault(This);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_UseScanLine(IDirectDrawVideo* This,LONG UseScanLine) {
    return This->lpVtbl->UseScanLine(This,UseScanLine);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_CanUseScanLine(IDirectDrawVideo* This,LONG *UseScanLine) {
    return This->lpVtbl->CanUseScanLine(This,UseScanLine);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_UseOverlayStretch(IDirectDrawVideo* This,LONG UseOverlayStretch) {
    return This->lpVtbl->UseOverlayStretch(This,UseOverlayStretch);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_CanUseOverlayStretch(IDirectDrawVideo* This,LONG *UseOverlayStretch) {
    return This->lpVtbl->CanUseOverlayStretch(This,UseOverlayStretch);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_UseWhenFullScreen(IDirectDrawVideo* This,LONG UseWhenFullScreen) {
    return This->lpVtbl->UseWhenFullScreen(This,UseWhenFullScreen);
}
static __WIDL_INLINE HRESULT IDirectDrawVideo_WillUseFullScreen(IDirectDrawVideo* This,LONG *UseWhenFullScreen) {
    return This->lpVtbl->WillUseFullScreen(This,UseWhenFullScreen);
}
#endif
#endif

#endif


#endif  /* __IDirectDrawVideo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IQualProp interface
 */
#ifndef __IQualProp_INTERFACE_DEFINED__
#define __IQualProp_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IQualProp : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_FramesDroppedInRenderer(
        int *pcFrames) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FramesDrawn(
        int *pcFramesDrawn) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AvgFrameRate(
        int *piAvgFrameRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Jitter(
        int *iJitter) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AvgSyncOffset(
        int *piAvg) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DevSyncOffset(
        int *piDev) = 0;

};
#else
typedef struct IQualPropVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IQualProp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IQualProp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IQualProp *This);

    /*** IQualProp methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FramesDroppedInRenderer)(
        IQualProp *This,
        int *pcFrames);

    HRESULT (STDMETHODCALLTYPE *get_FramesDrawn)(
        IQualProp *This,
        int *pcFramesDrawn);

    HRESULT (STDMETHODCALLTYPE *get_AvgFrameRate)(
        IQualProp *This,
        int *piAvgFrameRate);

    HRESULT (STDMETHODCALLTYPE *get_Jitter)(
        IQualProp *This,
        int *iJitter);

    HRESULT (STDMETHODCALLTYPE *get_AvgSyncOffset)(
        IQualProp *This,
        int *piAvg);

    HRESULT (STDMETHODCALLTYPE *get_DevSyncOffset)(
        IQualProp *This,
        int *piDev);

    END_INTERFACE
} IQualPropVtbl;

interface IQualProp {
    CONST_VTBL IQualPropVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IQualProp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IQualProp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IQualProp_Release(This) (This)->lpVtbl->Release(This)
/*** IQualProp methods ***/
#define IQualProp_get_FramesDroppedInRenderer(This,pcFrames) (This)->lpVtbl->get_FramesDroppedInRenderer(This,pcFrames)
#define IQualProp_get_FramesDrawn(This,pcFramesDrawn) (This)->lpVtbl->get_FramesDrawn(This,pcFramesDrawn)
#define IQualProp_get_AvgFrameRate(This,piAvgFrameRate) (This)->lpVtbl->get_AvgFrameRate(This,piAvgFrameRate)
#define IQualProp_get_Jitter(This,iJitter) (This)->lpVtbl->get_Jitter(This,iJitter)
#define IQualProp_get_AvgSyncOffset(This,piAvg) (This)->lpVtbl->get_AvgSyncOffset(This,piAvg)
#define IQualProp_get_DevSyncOffset(This,piDev) (This)->lpVtbl->get_DevSyncOffset(This,piDev)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IQualProp_QueryInterface(IQualProp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IQualProp_AddRef(IQualProp* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IQualProp_Release(IQualProp* This) {
    return This->lpVtbl->Release(This);
}
/*** IQualProp methods ***/
static __WIDL_INLINE HRESULT IQualProp_get_FramesDroppedInRenderer(IQualProp* This,int *pcFrames) {
    return This->lpVtbl->get_FramesDroppedInRenderer(This,pcFrames);
}
static __WIDL_INLINE HRESULT IQualProp_get_FramesDrawn(IQualProp* This,int *pcFramesDrawn) {
    return This->lpVtbl->get_FramesDrawn(This,pcFramesDrawn);
}
static __WIDL_INLINE HRESULT IQualProp_get_AvgFrameRate(IQualProp* This,int *piAvgFrameRate) {
    return This->lpVtbl->get_AvgFrameRate(This,piAvgFrameRate);
}
static __WIDL_INLINE HRESULT IQualProp_get_Jitter(IQualProp* This,int *iJitter) {
    return This->lpVtbl->get_Jitter(This,iJitter);
}
static __WIDL_INLINE HRESULT IQualProp_get_AvgSyncOffset(IQualProp* This,int *piAvg) {
    return This->lpVtbl->get_AvgSyncOffset(This,piAvg);
}
static __WIDL_INLINE HRESULT IQualProp_get_DevSyncOffset(IQualProp* This,int *piDev) {
    return This->lpVtbl->get_DevSyncOffset(This,piDev);
}
#endif
#endif

#endif


#endif  /* __IQualProp_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFullScreenVideo interface
 */
#ifndef __IFullScreenVideo_INTERFACE_DEFINED__
#define __IFullScreenVideo_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IFullScreenVideo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CountModes(
        LONG *pModes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetModeInfo(
        LONG Mode,
        LONG *pWidth,
        LONG *pHeight,
        LONG *pDepth) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentMode(
        LONG *pMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsModeAvailable(
        LONG Mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsModeEnabled(
        LONG Mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnabled(
        LONG Mode,
        LONG bEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClipFactor(
        LONG *pClipFactor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClipFactor(
        LONG ClipFactor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMessageDrain(
        HWND hwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMessageDrain(
        HWND *hwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMonitor(
        LONG Monitor) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMonitor(
        LONG *Monitor) = 0;

    virtual HRESULT STDMETHODCALLTYPE HideOnDeactivate(
        LONG Hide) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsHideOnDeactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCaption(
        BSTR strCaption) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCaption(
        BSTR *pstrCaption) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefault(
        ) = 0;

};
#else
typedef struct IFullScreenVideoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFullScreenVideo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFullScreenVideo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFullScreenVideo *This);

    /*** IFullScreenVideo methods ***/
    HRESULT (STDMETHODCALLTYPE *CountModes)(
        IFullScreenVideo *This,
        LONG *pModes);

    HRESULT (STDMETHODCALLTYPE *GetModeInfo)(
        IFullScreenVideo *This,
        LONG Mode,
        LONG *pWidth,
        LONG *pHeight,
        LONG *pDepth);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMode)(
        IFullScreenVideo *This,
        LONG *pMode);

    HRESULT (STDMETHODCALLTYPE *IsModeAvailable)(
        IFullScreenVideo *This,
        LONG Mode);

    HRESULT (STDMETHODCALLTYPE *IsModeEnabled)(
        IFullScreenVideo *This,
        LONG Mode);

    HRESULT (STDMETHODCALLTYPE *SetEnabled)(
        IFullScreenVideo *This,
        LONG Mode,
        LONG bEnabled);

    HRESULT (STDMETHODCALLTYPE *GetClipFactor)(
        IFullScreenVideo *This,
        LONG *pClipFactor);

    HRESULT (STDMETHODCALLTYPE *SetClipFactor)(
        IFullScreenVideo *This,
        LONG ClipFactor);

    HRESULT (STDMETHODCALLTYPE *SetMessageDrain)(
        IFullScreenVideo *This,
        HWND hwnd);

    HRESULT (STDMETHODCALLTYPE *GetMessageDrain)(
        IFullScreenVideo *This,
        HWND *hwnd);

    HRESULT (STDMETHODCALLTYPE *SetMonitor)(
        IFullScreenVideo *This,
        LONG Monitor);

    HRESULT (STDMETHODCALLTYPE *GetMonitor)(
        IFullScreenVideo *This,
        LONG *Monitor);

    HRESULT (STDMETHODCALLTYPE *HideOnDeactivate)(
        IFullScreenVideo *This,
        LONG Hide);

    HRESULT (STDMETHODCALLTYPE *IsHideOnDeactivate)(
        IFullScreenVideo *This);

    HRESULT (STDMETHODCALLTYPE *SetCaption)(
        IFullScreenVideo *This,
        BSTR strCaption);

    HRESULT (STDMETHODCALLTYPE *GetCaption)(
        IFullScreenVideo *This,
        BSTR *pstrCaption);

    HRESULT (STDMETHODCALLTYPE *SetDefault)(
        IFullScreenVideo *This);

    END_INTERFACE
} IFullScreenVideoVtbl;

interface IFullScreenVideo {
    CONST_VTBL IFullScreenVideoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFullScreenVideo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFullScreenVideo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFullScreenVideo_Release(This) (This)->lpVtbl->Release(This)
/*** IFullScreenVideo methods ***/
#define IFullScreenVideo_CountModes(This,pModes) (This)->lpVtbl->CountModes(This,pModes)
#define IFullScreenVideo_GetModeInfo(This,Mode,pWidth,pHeight,pDepth) (This)->lpVtbl->GetModeInfo(This,Mode,pWidth,pHeight,pDepth)
#define IFullScreenVideo_GetCurrentMode(This,pMode) (This)->lpVtbl->GetCurrentMode(This,pMode)
#define IFullScreenVideo_IsModeAvailable(This,Mode) (This)->lpVtbl->IsModeAvailable(This,Mode)
#define IFullScreenVideo_IsModeEnabled(This,Mode) (This)->lpVtbl->IsModeEnabled(This,Mode)
#define IFullScreenVideo_SetEnabled(This,Mode,bEnabled) (This)->lpVtbl->SetEnabled(This,Mode,bEnabled)
#define IFullScreenVideo_GetClipFactor(This,pClipFactor) (This)->lpVtbl->GetClipFactor(This,pClipFactor)
#define IFullScreenVideo_SetClipFactor(This,ClipFactor) (This)->lpVtbl->SetClipFactor(This,ClipFactor)
#define IFullScreenVideo_SetMessageDrain(This,hwnd) (This)->lpVtbl->SetMessageDrain(This,hwnd)
#define IFullScreenVideo_GetMessageDrain(This,hwnd) (This)->lpVtbl->GetMessageDrain(This,hwnd)
#define IFullScreenVideo_SetMonitor(This,Monitor) (This)->lpVtbl->SetMonitor(This,Monitor)
#define IFullScreenVideo_GetMonitor(This,Monitor) (This)->lpVtbl->GetMonitor(This,Monitor)
#define IFullScreenVideo_HideOnDeactivate(This,Hide) (This)->lpVtbl->HideOnDeactivate(This,Hide)
#define IFullScreenVideo_IsHideOnDeactivate(This) (This)->lpVtbl->IsHideOnDeactivate(This)
#define IFullScreenVideo_SetCaption(This,strCaption) (This)->lpVtbl->SetCaption(This,strCaption)
#define IFullScreenVideo_GetCaption(This,pstrCaption) (This)->lpVtbl->GetCaption(This,pstrCaption)
#define IFullScreenVideo_SetDefault(This) (This)->lpVtbl->SetDefault(This)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IFullScreenVideo_QueryInterface(IFullScreenVideo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IFullScreenVideo_AddRef(IFullScreenVideo* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IFullScreenVideo_Release(IFullScreenVideo* This) {
    return This->lpVtbl->Release(This);
}
/*** IFullScreenVideo methods ***/
static __WIDL_INLINE HRESULT IFullScreenVideo_CountModes(IFullScreenVideo* This,LONG *pModes) {
    return This->lpVtbl->CountModes(This,pModes);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_GetModeInfo(IFullScreenVideo* This,LONG Mode,LONG *pWidth,LONG *pHeight,LONG *pDepth) {
    return This->lpVtbl->GetModeInfo(This,Mode,pWidth,pHeight,pDepth);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_GetCurrentMode(IFullScreenVideo* This,LONG *pMode) {
    return This->lpVtbl->GetCurrentMode(This,pMode);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_IsModeAvailable(IFullScreenVideo* This,LONG Mode) {
    return This->lpVtbl->IsModeAvailable(This,Mode);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_IsModeEnabled(IFullScreenVideo* This,LONG Mode) {
    return This->lpVtbl->IsModeEnabled(This,Mode);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_SetEnabled(IFullScreenVideo* This,LONG Mode,LONG bEnabled) {
    return This->lpVtbl->SetEnabled(This,Mode,bEnabled);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_GetClipFactor(IFullScreenVideo* This,LONG *pClipFactor) {
    return This->lpVtbl->GetClipFactor(This,pClipFactor);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_SetClipFactor(IFullScreenVideo* This,LONG ClipFactor) {
    return This->lpVtbl->SetClipFactor(This,ClipFactor);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_SetMessageDrain(IFullScreenVideo* This,HWND hwnd) {
    return This->lpVtbl->SetMessageDrain(This,hwnd);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_GetMessageDrain(IFullScreenVideo* This,HWND *hwnd) {
    return This->lpVtbl->GetMessageDrain(This,hwnd);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_SetMonitor(IFullScreenVideo* This,LONG Monitor) {
    return This->lpVtbl->SetMonitor(This,Monitor);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_GetMonitor(IFullScreenVideo* This,LONG *Monitor) {
    return This->lpVtbl->GetMonitor(This,Monitor);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_HideOnDeactivate(IFullScreenVideo* This,LONG Hide) {
    return This->lpVtbl->HideOnDeactivate(This,Hide);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_IsHideOnDeactivate(IFullScreenVideo* This) {
    return This->lpVtbl->IsHideOnDeactivate(This);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_SetCaption(IFullScreenVideo* This,BSTR strCaption) {
    return This->lpVtbl->SetCaption(This,strCaption);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_GetCaption(IFullScreenVideo* This,BSTR *pstrCaption) {
    return This->lpVtbl->GetCaption(This,pstrCaption);
}
static __WIDL_INLINE HRESULT IFullScreenVideo_SetDefault(IFullScreenVideo* This) {
    return This->lpVtbl->SetDefault(This);
}
#endif
#endif

#endif


#endif  /* __IFullScreenVideo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFullScreenVideoEx interface
 */
#ifndef __IFullScreenVideoEx_INTERFACE_DEFINED__
#define __IFullScreenVideoEx_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IFullScreenVideoEx : public IFullScreenVideo
{
    virtual HRESULT STDMETHODCALLTYPE SetAcceleratorTable(
        HWND hwnd,
        HACCEL hAccel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAcceleratorTable(
        HWND *phwnd,
        HACCEL *phAccel) = 0;

    virtual HRESULT STDMETHODCALLTYPE KeepPixelAspectRatio(
        LONG KeepAspect) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsKeepPixelAspectRatio(
        LONG *pKeepAspect) = 0;

};
#else
typedef struct IFullScreenVideoExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFullScreenVideoEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFullScreenVideoEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFullScreenVideoEx *This);

    /*** IFullScreenVideo methods ***/
    HRESULT (STDMETHODCALLTYPE *CountModes)(
        IFullScreenVideoEx *This,
        LONG *pModes);

    HRESULT (STDMETHODCALLTYPE *GetModeInfo)(
        IFullScreenVideoEx *This,
        LONG Mode,
        LONG *pWidth,
        LONG *pHeight,
        LONG *pDepth);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMode)(
        IFullScreenVideoEx *This,
        LONG *pMode);

    HRESULT (STDMETHODCALLTYPE *IsModeAvailable)(
        IFullScreenVideoEx *This,
        LONG Mode);

    HRESULT (STDMETHODCALLTYPE *IsModeEnabled)(
        IFullScreenVideoEx *This,
        LONG Mode);

    HRESULT (STDMETHODCALLTYPE *SetEnabled)(
        IFullScreenVideoEx *This,
        LONG Mode,
        LONG bEnabled);

    HRESULT (STDMETHODCALLTYPE *GetClipFactor)(
        IFullScreenVideoEx *This,
        LONG *pClipFactor);

    HRESULT (STDMETHODCALLTYPE *SetClipFactor)(
        IFullScreenVideoEx *This,
        LONG ClipFactor);

    HRESULT (STDMETHODCALLTYPE *SetMessageDrain)(
        IFullScreenVideoEx *This,
        HWND hwnd);

    HRESULT (STDMETHODCALLTYPE *GetMessageDrain)(
        IFullScreenVideoEx *This,
        HWND *hwnd);

    HRESULT (STDMETHODCALLTYPE *SetMonitor)(
        IFullScreenVideoEx *This,
        LONG Monitor);

    HRESULT (STDMETHODCALLTYPE *GetMonitor)(
        IFullScreenVideoEx *This,
        LONG *Monitor);

    HRESULT (STDMETHODCALLTYPE *HideOnDeactivate)(
        IFullScreenVideoEx *This,
        LONG Hide);

    HRESULT (STDMETHODCALLTYPE *IsHideOnDeactivate)(
        IFullScreenVideoEx *This);

    HRESULT (STDMETHODCALLTYPE *SetCaption)(
        IFullScreenVideoEx *This,
        BSTR strCaption);

    HRESULT (STDMETHODCALLTYPE *GetCaption)(
        IFullScreenVideoEx *This,
        BSTR *pstrCaption);

    HRESULT (STDMETHODCALLTYPE *SetDefault)(
        IFullScreenVideoEx *This);

    /*** IFullScreenVideoEx methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAcceleratorTable)(
        IFullScreenVideoEx *This,
        HWND hwnd,
        HACCEL hAccel);

    HRESULT (STDMETHODCALLTYPE *GetAcceleratorTable)(
        IFullScreenVideoEx *This,
        HWND *phwnd,
        HACCEL *phAccel);

    HRESULT (STDMETHODCALLTYPE *KeepPixelAspectRatio)(
        IFullScreenVideoEx *This,
        LONG KeepAspect);

    HRESULT (STDMETHODCALLTYPE *IsKeepPixelAspectRatio)(
        IFullScreenVideoEx *This,
        LONG *pKeepAspect);

    END_INTERFACE
} IFullScreenVideoExVtbl;

interface IFullScreenVideoEx {
    CONST_VTBL IFullScreenVideoExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFullScreenVideoEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFullScreenVideoEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFullScreenVideoEx_Release(This) (This)->lpVtbl->Release(This)
/*** IFullScreenVideo methods ***/
#define IFullScreenVideoEx_CountModes(This,pModes) (This)->lpVtbl->CountModes(This,pModes)
#define IFullScreenVideoEx_GetModeInfo(This,Mode,pWidth,pHeight,pDepth) (This)->lpVtbl->GetModeInfo(This,Mode,pWidth,pHeight,pDepth)
#define IFullScreenVideoEx_GetCurrentMode(This,pMode) (This)->lpVtbl->GetCurrentMode(This,pMode)
#define IFullScreenVideoEx_IsModeAvailable(This,Mode) (This)->lpVtbl->IsModeAvailable(This,Mode)
#define IFullScreenVideoEx_IsModeEnabled(This,Mode) (This)->lpVtbl->IsModeEnabled(This,Mode)
#define IFullScreenVideoEx_SetEnabled(This,Mode,bEnabled) (This)->lpVtbl->SetEnabled(This,Mode,bEnabled)
#define IFullScreenVideoEx_GetClipFactor(This,pClipFactor) (This)->lpVtbl->GetClipFactor(This,pClipFactor)
#define IFullScreenVideoEx_SetClipFactor(This,ClipFactor) (This)->lpVtbl->SetClipFactor(This,ClipFactor)
#define IFullScreenVideoEx_SetMessageDrain(This,hwnd) (This)->lpVtbl->SetMessageDrain(This,hwnd)
#define IFullScreenVideoEx_GetMessageDrain(This,hwnd) (This)->lpVtbl->GetMessageDrain(This,hwnd)
#define IFullScreenVideoEx_SetMonitor(This,Monitor) (This)->lpVtbl->SetMonitor(This,Monitor)
#define IFullScreenVideoEx_GetMonitor(This,Monitor) (This)->lpVtbl->GetMonitor(This,Monitor)
#define IFullScreenVideoEx_HideOnDeactivate(This,Hide) (This)->lpVtbl->HideOnDeactivate(This,Hide)
#define IFullScreenVideoEx_IsHideOnDeactivate(This) (This)->lpVtbl->IsHideOnDeactivate(This)
#define IFullScreenVideoEx_SetCaption(This,strCaption) (This)->lpVtbl->SetCaption(This,strCaption)
#define IFullScreenVideoEx_GetCaption(This,pstrCaption) (This)->lpVtbl->GetCaption(This,pstrCaption)
#define IFullScreenVideoEx_SetDefault(This) (This)->lpVtbl->SetDefault(This)
/*** IFullScreenVideoEx methods ***/
#define IFullScreenVideoEx_SetAcceleratorTable(This,hwnd,hAccel) (This)->lpVtbl->SetAcceleratorTable(This,hwnd,hAccel)
#define IFullScreenVideoEx_GetAcceleratorTable(This,phwnd,phAccel) (This)->lpVtbl->GetAcceleratorTable(This,phwnd,phAccel)
#define IFullScreenVideoEx_KeepPixelAspectRatio(This,KeepAspect) (This)->lpVtbl->KeepPixelAspectRatio(This,KeepAspect)
#define IFullScreenVideoEx_IsKeepPixelAspectRatio(This,pKeepAspect) (This)->lpVtbl->IsKeepPixelAspectRatio(This,pKeepAspect)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IFullScreenVideoEx_QueryInterface(IFullScreenVideoEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IFullScreenVideoEx_AddRef(IFullScreenVideoEx* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IFullScreenVideoEx_Release(IFullScreenVideoEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IFullScreenVideo methods ***/
static __WIDL_INLINE HRESULT IFullScreenVideoEx_CountModes(IFullScreenVideoEx* This,LONG *pModes) {
    return This->lpVtbl->CountModes(This,pModes);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetModeInfo(IFullScreenVideoEx* This,LONG Mode,LONG *pWidth,LONG *pHeight,LONG *pDepth) {
    return This->lpVtbl->GetModeInfo(This,Mode,pWidth,pHeight,pDepth);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetCurrentMode(IFullScreenVideoEx* This,LONG *pMode) {
    return This->lpVtbl->GetCurrentMode(This,pMode);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_IsModeAvailable(IFullScreenVideoEx* This,LONG Mode) {
    return This->lpVtbl->IsModeAvailable(This,Mode);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_IsModeEnabled(IFullScreenVideoEx* This,LONG Mode) {
    return This->lpVtbl->IsModeEnabled(This,Mode);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetEnabled(IFullScreenVideoEx* This,LONG Mode,LONG bEnabled) {
    return This->lpVtbl->SetEnabled(This,Mode,bEnabled);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetClipFactor(IFullScreenVideoEx* This,LONG *pClipFactor) {
    return This->lpVtbl->GetClipFactor(This,pClipFactor);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetClipFactor(IFullScreenVideoEx* This,LONG ClipFactor) {
    return This->lpVtbl->SetClipFactor(This,ClipFactor);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetMessageDrain(IFullScreenVideoEx* This,HWND hwnd) {
    return This->lpVtbl->SetMessageDrain(This,hwnd);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetMessageDrain(IFullScreenVideoEx* This,HWND *hwnd) {
    return This->lpVtbl->GetMessageDrain(This,hwnd);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetMonitor(IFullScreenVideoEx* This,LONG Monitor) {
    return This->lpVtbl->SetMonitor(This,Monitor);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetMonitor(IFullScreenVideoEx* This,LONG *Monitor) {
    return This->lpVtbl->GetMonitor(This,Monitor);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_HideOnDeactivate(IFullScreenVideoEx* This,LONG Hide) {
    return This->lpVtbl->HideOnDeactivate(This,Hide);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_IsHideOnDeactivate(IFullScreenVideoEx* This) {
    return This->lpVtbl->IsHideOnDeactivate(This);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetCaption(IFullScreenVideoEx* This,BSTR strCaption) {
    return This->lpVtbl->SetCaption(This,strCaption);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetCaption(IFullScreenVideoEx* This,BSTR *pstrCaption) {
    return This->lpVtbl->GetCaption(This,pstrCaption);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetDefault(IFullScreenVideoEx* This) {
    return This->lpVtbl->SetDefault(This);
}
/*** IFullScreenVideoEx methods ***/
static __WIDL_INLINE HRESULT IFullScreenVideoEx_SetAcceleratorTable(IFullScreenVideoEx* This,HWND hwnd,HACCEL hAccel) {
    return This->lpVtbl->SetAcceleratorTable(This,hwnd,hAccel);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_GetAcceleratorTable(IFullScreenVideoEx* This,HWND *phwnd,HACCEL *phAccel) {
    return This->lpVtbl->GetAcceleratorTable(This,phwnd,phAccel);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_KeepPixelAspectRatio(IFullScreenVideoEx* This,LONG KeepAspect) {
    return This->lpVtbl->KeepPixelAspectRatio(This,KeepAspect);
}
static __WIDL_INLINE HRESULT IFullScreenVideoEx_IsKeepPixelAspectRatio(IFullScreenVideoEx* This,LONG *pKeepAspect) {
    return This->lpVtbl->IsKeepPixelAspectRatio(This,pKeepAspect);
}
#endif
#endif

#endif


#endif  /* __IFullScreenVideoEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBaseVideoMixer interface
 */
#ifndef __IBaseVideoMixer_INTERFACE_DEFINED__
#define __IBaseVideoMixer_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IBaseVideoMixer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetLeadPin(
        int iPin) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLeadPin(
        int *piPin) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputPinCount(
        int *piPinCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUsingClock(
        int *pbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUsingClock(
        int bValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClockPeriod(
        int *pbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClockPeriod(
        int bValue) = 0;

};
#else
typedef struct IBaseVideoMixerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBaseVideoMixer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBaseVideoMixer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBaseVideoMixer *This);

    /*** IBaseVideoMixer methods ***/
    HRESULT (STDMETHODCALLTYPE *SetLeadPin)(
        IBaseVideoMixer *This,
        int iPin);

    HRESULT (STDMETHODCALLTYPE *GetLeadPin)(
        IBaseVideoMixer *This,
        int *piPin);

    HRESULT (STDMETHODCALLTYPE *GetInputPinCount)(
        IBaseVideoMixer *This,
        int *piPinCount);

    HRESULT (STDMETHODCALLTYPE *IsUsingClock)(
        IBaseVideoMixer *This,
        int *pbValue);

    HRESULT (STDMETHODCALLTYPE *SetUsingClock)(
        IBaseVideoMixer *This,
        int bValue);

    HRESULT (STDMETHODCALLTYPE *GetClockPeriod)(
        IBaseVideoMixer *This,
        int *pbValue);

    HRESULT (STDMETHODCALLTYPE *SetClockPeriod)(
        IBaseVideoMixer *This,
        int bValue);

    END_INTERFACE
} IBaseVideoMixerVtbl;

interface IBaseVideoMixer {
    CONST_VTBL IBaseVideoMixerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBaseVideoMixer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBaseVideoMixer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBaseVideoMixer_Release(This) (This)->lpVtbl->Release(This)
/*** IBaseVideoMixer methods ***/
#define IBaseVideoMixer_SetLeadPin(This,iPin) (This)->lpVtbl->SetLeadPin(This,iPin)
#define IBaseVideoMixer_GetLeadPin(This,piPin) (This)->lpVtbl->GetLeadPin(This,piPin)
#define IBaseVideoMixer_GetInputPinCount(This,piPinCount) (This)->lpVtbl->GetInputPinCount(This,piPinCount)
#define IBaseVideoMixer_IsUsingClock(This,pbValue) (This)->lpVtbl->IsUsingClock(This,pbValue)
#define IBaseVideoMixer_SetUsingClock(This,bValue) (This)->lpVtbl->SetUsingClock(This,bValue)
#define IBaseVideoMixer_GetClockPeriod(This,pbValue) (This)->lpVtbl->GetClockPeriod(This,pbValue)
#define IBaseVideoMixer_SetClockPeriod(This,bValue) (This)->lpVtbl->SetClockPeriod(This,bValue)
#else
/*** IUnknown methods ***/
static __WIDL_INLINE HRESULT IBaseVideoMixer_QueryInterface(IBaseVideoMixer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static __WIDL_INLINE ULONG IBaseVideoMixer_AddRef(IBaseVideoMixer* This) {
    return This->lpVtbl->AddRef(This);
}
static __WIDL_INLINE ULONG IBaseVideoMixer_Release(IBaseVideoMixer* This) {
    return This->lpVtbl->Release(This);
}
/*** IBaseVideoMixer methods ***/
static __WIDL_INLINE HRESULT IBaseVideoMixer_SetLeadPin(IBaseVideoMixer* This,int iPin) {
    return This->lpVtbl->SetLeadPin(This,iPin);
}
static __WIDL_INLINE HRESULT IBaseVideoMixer_GetLeadPin(IBaseVideoMixer* This,int *piPin) {
    return This->lpVtbl->GetLeadPin(This,piPin);
}
static __WIDL_INLINE HRESULT IBaseVideoMixer_GetInputPinCount(IBaseVideoMixer* This,int *piPinCount) {
    return This->lpVtbl->GetInputPinCount(This,piPinCount);
}
static __WIDL_INLINE HRESULT IBaseVideoMixer_IsUsingClock(IBaseVideoMixer* This,int *pbValue) {
    return This->lpVtbl->IsUsingClock(This,pbValue);
}
static __WIDL_INLINE HRESULT IBaseVideoMixer_SetUsingClock(IBaseVideoMixer* This,int bValue) {
    return This->lpVtbl->SetUsingClock(This,bValue);
}
static __WIDL_INLINE HRESULT IBaseVideoMixer_GetClockPeriod(IBaseVideoMixer* This,int *pbValue) {
    return This->lpVtbl->GetClockPeriod(This,pbValue);
}
static __WIDL_INLINE HRESULT IBaseVideoMixer_SetClockPeriod(IBaseVideoMixer* This,int bValue) {
    return This->lpVtbl->SetClockPeriod(This,bValue);
}
#endif
#endif

#endif


#endif  /* __IBaseVideoMixer_INTERFACE_DEFINED__ */

#define iPALETTE_COLORS 256
#define iEGA_COLORS 16
#define iMASK_COLORS 3
#define iTRUECOLOR 16
#define iRED 0
#define iGREEN 1
#define iBLUE 2
#define iPALETTE 8
#define iMAXBITS 8
typedef struct tag_TRUECOLORINFO {
    DWORD dwBitMasks[3];
    RGBQUAD bmiColors[256];
} TRUECOLORINFO;
typedef struct tagVIDEOINFOHEADER {
    RECT rcSource;
    RECT rcTarget;
    DWORD dwBitRate;
    DWORD dwBitErrorRate;
    REFERENCE_TIME AvgTimePerFrame;
    BITMAPINFOHEADER bmiHeader;
} VIDEOINFOHEADER;
typedef struct tagVIDEOINFO {
    RECT rcSource;
    RECT rcTarget;
    DWORD dwBitRate;
    DWORD dwBitErrorRate;
    REFERENCE_TIME AvgTimePerFrame;
    BITMAPINFOHEADER bmiHeader;
    __C89_NAMELESS union {
        RGBQUAD bmiColors[256];
        DWORD dwBitMasks[3];
        TRUECOLORINFO TrueColorInfo;
    } __C89_NAMELESSUNIONNAME;
} VIDEOINFO;
typedef struct tagMPEG1VIDEOINFO {
    VIDEOINFOHEADER hdr;
    DWORD dwStartTimeCode;
    DWORD cbSequenceHeader;
    BYTE bSequenceHeader[1];
} MPEG1VIDEOINFO;
#define MAX_SIZE_MPEG1_SEQUENCE_INFO 140
#define MPEG1_SEQUENCE_INFO(pv) ((const BYTE *)(pv)->bSequenceHeader)
typedef struct tagAnalogVideoInfo {
    RECT rcSource;
    RECT rcTarget;
    DWORD dwActiveWidth;
    DWORD dwActiveHeight;
    REFERENCE_TIME AvgTimePerFrame;
} ANALOGVIDEOINFO;
typedef enum __WIDL_amvideo_generated_name_0000000F {
    AM_PROPERTY_FRAMESTEP_STEP = 0x1,
    AM_PROPERTY_FRAMESTEP_CANCEL = 0x2,
    AM_PROPERTY_FRAMESTEP_CANSTEP = 0x3,
    AM_PROPERTY_FRAMESTEP_CANSTEPMULTIPLE = 0x4
} AM_PROPERTY_FRAMESTEP;
typedef struct _AM_FRAMESTEP_STEP {
    DWORD dwFramesToStep;
} AM_FRAMESTEP_STEP;
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __amvideo_h__ */
