; 
; Exports of file Security.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY Security.dll
EXPORTS
AcceptSecurityContext
AcquireCredentialsHandleA
AcquireCredentialsHandleW
AddSecurityPackageA
AddSecurityPackageW
ApplyControlToken
CompleteAuthToken
DecryptMessage
DeleteSecurityContext
DeleteSecurityPackageA
DeleteSecurityPackageW
EncryptMessage
EnumerateSecurityPackagesA
EnumerateSecurityPackagesW
ExportSecurityContext
FreeContextBuffer
FreeCredentialsHandle
ImpersonateSecurityContext
ImportSecurityContextA
ImportSecurityContextW
InitSecurityInterfaceA
InitSecurityInterfaceW
InitializeSecurityContextA
InitializeSecurityContextW
MakeSignature
QueryContextAttributesA
QueryContextAttributesW
QueryCredentialsAttributesA
QueryCredentialsAttributesW
QuerySecurityContextToken
QuerySecurityPackageInfoA
QuerySecurityPackageInfoW
RevertSecurityContext
SealMessage
UnsealMessage
VerifySignature
