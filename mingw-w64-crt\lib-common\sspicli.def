;
; Definition file of SspiCli.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "SspiCli.dll"
EXPORTS
SecDeleteUserModeContext
SecInitUserModeContext
SspiUnmarshalAuthIdentityInternal
AcceptSecurityContext
AcquireCredentialsHandleA
AcquireCredentialsHandleW
AddCredentialsA
AddCredentialsW
AddSecurityPackageA
AddSecurityPackageW
ApplyControlToken
ChangeAccountPasswordA
ChangeAccountPasswordW
CompleteAuthToken
CredMarshalTargetInfo
CredUnmarshalTargetInfo
DecryptMessage
DeleteSecurityContext
DeleteSecurityPackageA
DeleteSecurityPackageW
EncryptMessage
EnumerateSecurityPackagesA
EnumerateSecurityPackagesW
ExportSecurityContext
FreeContextBuffer
FreeCredentialsHandle
GetSecurityUserInfo
GetUserNameExA
GetUserNameExW
ImpersonateSecurityContext
ImportSecurityContextA
ImportSecurityContextW
InitSecurityInterfaceA
InitSecurityInterfaceW
InitializeSecurityContextA
InitializeSecurityContextW
LogonUserExExW
LsaCallAuthenticationPackage
LsaConnectUntrusted
LsaDeregisterLogonProcess
LsaEnumerateLogonSessions
LsaFreeReturnBuffer
LsaGetLogonSessionData
LsaLogonUser
LsaLookupAuthenticationPackage
LsaRegisterLogonProcess
LsaRegisterPolicyChangeNotification
LsaUnregisterPolicyChangeNotification
MakeSignature
QueryContextAttributesA
QueryContextAttributesW
QueryCredentialsAttributesA
QueryCredentialsAttributesW
QuerySecurityContextToken
QuerySecurityPackageInfoA
QuerySecurityPackageInfoW
RevertSecurityContext
SaslAcceptSecurityContext
SaslEnumerateProfilesA
SaslEnumerateProfilesW
SaslGetContextOption
SaslGetProfilePackageA
SaslGetProfilePackageW
SaslIdentifyPackageA
SaslIdentifyPackageW
SaslInitializeSecurityContextA
SaslInitializeSecurityContextW
SaslSetContextOption
SealMessage
SecCacheSspiPackages
SeciAllocateAndSetCallFlags
SeciAllocateAndSetIPAddress
SeciFreeCallContext
SeciIsProtectedUser
SetContextAttributesA
SetContextAttributesW
SetCredentialsAttributesA
SetCredentialsAttributesW
SspiCompareAuthIdentities
SspiCopyAuthIdentity
SspiDecryptAuthIdentity
SspiDecryptAuthIdentityEx
SspiEncodeAuthIdentityAsStrings
SspiEncodeStringsAsAuthIdentity
SspiEncryptAuthIdentity
SspiEncryptAuthIdentityEx
SspiExcludePackage
SspiFreeAuthIdentity
SspiGetComputerNameForSPN
SspiGetTargetHostName
SspiIsAuthIdentityEncrypted
SspiLocalFree
SspiMarshalAuthIdentity
SspiPrepareForCredRead
SspiPrepareForCredWrite
SspiUnmarshalAuthIdentity
SspiValidateAuthIdentity
SspiZeroAuthIdentity
UnsealMessage
VerifySignature
