/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _CIERROR_H_
#define _CIERROR_H_
#ifndef FACILITY_WINDOWS

#define FACILITY_WINDOWS 0x8
#define FACILITY_NULL 0x0
#define FACILITY_ITF 0x4

#define STATUS_SEVERITY_SUCCESS 0x0
#define STATUS_SEVERITY_COFAIL 0x3
#define STATUS_SEVERITY_COERROR 0x2

#define NOT_AN_ERROR1 ((HRESULT)0x00081600)
#endif

#define QUERY_E_FAILED ((HRESULT)0x80041600)
#define QUERY_E_INVALIDQUERY ((HRESULT)0x80041601)
#define QUERY_E_INVALIDRESTRICTION ((HRESULT)0x80041602)
#define QUERY_E_INVALIDSORT ((HRESULT)0x80041603)
#define QUERY_E_INVALIDCATEGORIZE ((HRESULT)0x80041604)
#define QUERY_E_ALLNOISE ((HRESULT)0x80041605)
#define QUERY_E_TOOCOMPLEX ((HRESULT)0x80041606)
#define QUERY_E_TIMEDOUT ((HRESULT)0x80041607)
#define QUERY_E_DUPLICATE_OUTPUT_COLUMN ((HRESULT)0x80041608)
#define QUERY_E_INVALID_OUTPUT_COLUMN ((HRESULT)0x80041609)
#define QUERY_E_INVALID_DIRECTORY ((HRESULT)0x8004160A)
#define QUERY_E_DIR_ON_REMOVABLE_DRIVE ((HRESULT)0x8004160B)
#define QUERY_S_NO_QUERY ((HRESULT)0x8004160C)
#define QPLIST_E_CANT_OPEN_FILE ((HRESULT)0x80041651)
#define QPLIST_E_READ_ERROR ((HRESULT)0x80041652)
#define QPLIST_E_EXPECTING_NAME ((HRESULT)0x80041653)
#define QPLIST_E_EXPECTING_TYPE ((HRESULT)0x80041654)
#define QPLIST_E_UNRECOGNIZED_TYPE ((HRESULT)0x80041655)
#define QPLIST_E_EXPECTING_INTEGER ((HRESULT)0x80041656)
#define QPLIST_E_EXPECTING_CLOSE_PAREN ((HRESULT)0x80041657)
#define QPLIST_E_EXPECTING_GUID ((HRESULT)0x80041658)
#define QPLIST_E_BAD_GUID ((HRESULT)0x80041659)
#define QPLIST_E_EXPECTING_PROP_SPEC ((HRESULT)0x8004165A)
#define QPLIST_E_CANT_SET_PROPERTY ((HRESULT)0x8004165B)
#define QPLIST_E_DUPLICATE ((HRESULT)0x8004165C)
#define QPLIST_E_VECTORBYREF_USED_ALONE ((HRESULT)0x8004165D)
#define QPLIST_E_BYREF_USED_WITHOUT_PTRTYPE ((HRESULT)0x8004165E)
#define QPARSE_E_UNEXPECTED_NOT ((HRESULT)0x80041660)
#define QPARSE_E_EXPECTING_INTEGER ((HRESULT)0x80041661)
#define QPARSE_E_EXPECTING_REAL ((HRESULT)0x80041662)
#define QPARSE_E_EXPECTING_DATE ((HRESULT)0x80041663)
#define QPARSE_E_EXPECTING_CURRENCY ((HRESULT)0x80041664)
#define QPARSE_E_EXPECTING_GUID ((HRESULT)0x80041665)
#define QPARSE_E_EXPECTING_BRACE ((HRESULT)0x80041666)
#define QPARSE_E_EXPECTING_PAREN ((HRESULT)0x80041667)
#define QPARSE_E_EXPECTING_PROPERTY ((HRESULT)0x80041668)
#define QPARSE_E_NOT_YET_IMPLEMENTED ((HRESULT)0x80041669)
#define QPARSE_E_EXPECTING_PHRASE ((HRESULT)0x8004166A)
#define QPARSE_E_UNSUPPORTED_PROPERTY_TYPE ((HRESULT)0x8004166B)
#define QPARSE_E_EXPECTING_REGEX ((HRESULT)0x8004166C)
#define QPARSE_E_EXPECTING_REGEX_PROPERTY ((HRESULT)0x8004166D)
#define QPARSE_E_INVALID_LITERAL ((HRESULT)0x8004166E)
#define QPARSE_E_NO_SUCH_PROPERTY ((HRESULT)0x8004166F)
#define QPARSE_E_EXPECTING_EOS ((HRESULT)0x80041670)
#define QPARSE_E_EXPECTING_COMMA ((HRESULT)0x80041671)
#define QPARSE_E_UNEXPECTED_EOS ((HRESULT)0x80041672)
#define QPARSE_E_WEIGHT_OUT_OF_RANGE ((HRESULT)0x80041673)
#define QPARSE_E_NO_SUCH_SORT_PROPERTY ((HRESULT)0x80041674)
#define QPARSE_E_INVALID_SORT_ORDER ((HRESULT)0x80041675)
#define QUTIL_E_CANT_CONVERT_VROOT ((HRESULT)0x80041676)
#define QPARSE_E_INVALID_GROUPING ((HRESULT)0x80041677)
#define QUTIL_E_INVALID_CODEPAGE ((HRESULT)0xC0041678)
#define QPLIST_S_DUPLICATE ((HRESULT)0x00041679)
#define QPARSE_E_INVALID_QUERY ((HRESULT)0x8004167A)
#define QPARSE_E_INVALID_RANKMETHOD ((HRESULT)0x8004167B)
#define FDAEMON_W_WORDLISTFULL ((HRESULT)0x00041680)
#define FDAEMON_E_LOWRESOURCE ((HRESULT)0x80041681)
#define FDAEMON_E_FATALERROR ((HRESULT)0x80041682)
#define FDAEMON_E_PARTITIONDELETED ((HRESULT)0x80041683)
#define FDAEMON_E_CHANGEUPDATEFAILED ((HRESULT)0x80041684)
#define FDAEMON_W_EMPTYWORDLIST ((HRESULT)0x00041685)
#define FDAEMON_E_WORDLISTCOMMITFAILED ((HRESULT)0x80041686)
#define FDAEMON_E_NOWORDLIST ((HRESULT)0x80041687)
#define FDAEMON_E_TOOMANYFILTEREDBLOCKS ((HRESULT)0x80041688)
#define SEARCH_S_NOMOREHITS ((HRESULT)0x000416A0)
#define SEARCH_E_NOMONIKER ((HRESULT)0x800416A1)
#define SEARCH_E_NOREGION ((HRESULT)0x800416A2)
#define FILTER_E_TOO_BIG ((HRESULT)0x80041730)
#define FILTER_S_PARTIAL_CONTENTSCAN_IMMEDIATE ((HRESULT)0x00041731)
#define FILTER_S_FULL_CONTENTSCAN_IMMEDIATE ((HRESULT)0x00041732)
#define FILTER_S_CONTENTSCAN_DELAYED ((HRESULT)0x00041733)
#define FILTER_E_CONTENTINDEXCORRUPT ((HRESULT)0xC0041734)
#define FILTER_S_DISK_FULL ((HRESULT)0x00041735)
#define FILTER_E_ALREADY_OPEN ((HRESULT)0x80041736)
#define FILTER_E_UNREACHABLE ((HRESULT)0x80041737)
#define FILTER_E_IN_USE ((HRESULT)0x80041738)
#define FILTER_E_NOT_OPEN ((HRESULT)0x80041739)
#define FILTER_S_NO_PROPSETS ((HRESULT)0x0004173A)
#define FILTER_E_NO_SUCH_PROPERTY ((HRESULT)0x8004173B)
#define FILTER_S_NO_SECURITY_DESCRIPTOR ((HRESULT)0x0004173C)
#define FILTER_E_OFFLINE ((HRESULT)0x8004173D)
#define FILTER_E_PARTIALLY_FILTERED ((HRESULT)0x8004173E)
#define WBREAK_E_END_OF_TEXT ((HRESULT)0x80041780)
#define LANGUAGE_S_LARGE_WORD ((HRESULT)0x00041781)
#define WBREAK_E_QUERY_ONLY ((HRESULT)0x80041782)
#define WBREAK_E_BUFFER_TOO_SMALL ((HRESULT)0x80041783)
#define LANGUAGE_E_DATABASE_NOT_FOUND ((HRESULT)0x80041784)
#define WBREAK_E_INIT_FAILED ((HRESULT)0x80041785)
#define PSINK_E_QUERY_ONLY ((HRESULT)0x80041790)
#define PSINK_E_INDEX_ONLY ((HRESULT)0x80041791)
#define PSINK_E_LARGE_ATTACHMENT ((HRESULT)0x80041792)
#define PSINK_S_LARGE_WORD ((HRESULT)0x00041793)
#define CI_CORRUPT_DATABASE ((HRESULT)0xC0041800)
#define CI_CORRUPT_CATALOG ((HRESULT)0xC0041801)
#define CI_INVALID_PARTITION ((HRESULT)0xC0041802)
#define CI_INVALID_PRIORITY ((HRESULT)0xC0041803)
#define CI_NO_STARTING_KEY ((HRESULT)0xC0041804)
#define CI_OUT_OF_INDEX_IDS ((HRESULT)0xC0041805)
#define CI_NO_CATALOG ((HRESULT)0xC0041806)
#define CI_CORRUPT_FILTER_BUFFER ((HRESULT)0xC0041807)
#define CI_INVALID_INDEX ((HRESULT)0xC0041808)
#define CI_PROPSTORE_INCONSISTENCY ((HRESULT)0xC0041809)
#define CI_E_ALREADY_INITIALIZED ((HRESULT)0x8004180A)
#define CI_E_NOT_INITIALIZED ((HRESULT)0x8004180B)
#define CI_E_BUFFERTOOSMALL ((HRESULT)0x8004180C)
#define CI_E_PROPERTY_NOT_CACHED ((HRESULT)0x8004180D)
#define CI_S_WORKID_DELETED ((HRESULT)0x0004180E)
#define CI_E_INVALID_STATE ((HRESULT)0x8004180F)
#define CI_E_FILTERING_DISABLED ((HRESULT)0x80041810)
#define CI_E_DISK_FULL ((HRESULT)0x80041811)
#define CI_E_SHUTDOWN ((HRESULT)0x80041812)
#define CI_E_WORKID_NOTVALID ((HRESULT)0x80041813)
#define CI_S_END_OF_ENUMERATION ((HRESULT)0x00041814)
#define CI_E_NOT_FOUND ((HRESULT)0x80041815)
#define CI_E_USE_DEFAULT_PID ((HRESULT)0x80041816)
#define CI_E_DUPLICATE_NOTIFICATION ((HRESULT)0x80041817)
#define CI_E_UPDATES_DISABLED ((HRESULT)0x80041818)
#define CI_E_INVALID_FLAGS_COMBINATION ((HRESULT)0x80041819)
#define CI_E_OUTOFSEQ_INCREMENT_DATA ((HRESULT)0x8004181A)
#define CI_E_SHARING_VIOLATION ((HRESULT)0x8004181B)
#define CI_E_LOGON_FAILURE ((HRESULT)0x8004181C)
#define CI_E_NO_CATALOG ((HRESULT)0x8004181D)
#define CI_E_STRANGE_PAGEORSECTOR_SIZE ((HRESULT)0x8004181E)
#define CI_E_TIMEOUT ((HRESULT)0x8004181F)
#define CI_E_NOT_RUNNING ((HRESULT)0x80041820)
#define CI_INCORRECT_VERSION ((HRESULT)0xC0041821)
#define CI_E_ENUMERATION_STARTED ((HRESULT)0xC0041822)
#define CI_E_PROPERTY_TOOLARGE ((HRESULT)0xC0041823)
#define CI_E_CLIENT_FILTER_ABORT ((HRESULT)0xC0041824)
#define CI_S_NO_DOCSTORE ((HRESULT)0x00041825)
#define CI_S_CAT_STOPPED ((HRESULT)0x00041826)
#define CI_E_CARDINALITY_MISMATCH ((HRESULT)0x80041827)
#define CI_E_CONFIG_DISK_FULL ((HRESULT)0x80041828)

#endif
