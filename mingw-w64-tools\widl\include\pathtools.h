/*
      .Some useful path tools.
        .ASCII only for now.
   .Written by <PERSON> in 2014.
   .Licensed under CC0 (and anything.
  .else you need to license it under).
      .No warranties whatsoever.
  .email: <<EMAIL>>.
 */

#ifndef PATHTOOLS_H
#define PATHTOOLS_H

/* Uses a host OS specific function to determine the path of the executable,
   if IMPLEMENT_SYS_GET_EXECUTABLE_PATH is defined, otherwise uses argv0. */
int get_executable_path(char const * argv0, char * result, ssize_t max_size);

#endif /* PATHTOOLS_H */
