; 
; Exports of file WMI.dll
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY WMI.dll
EXPORTS
CloseTrace
ControlTraceA
ControlTraceW
CreateTraceInstanceId
EnableTrace
GetTraceEnableFlags
GetTraceEnableLevel
GetTraceLoggerHandle
OpenTraceA
OpenTraceW
ProcessTrace
QueryAllTracesA
QueryAllTracesW
RegisterTraceGuidsA
RegisterTraceGuidsW
RemoveTraceCallback
SetTraceCallback
StartTraceA
StartTraceW
TraceEvent
TraceEventInstance
UnregisterTraceGuids
WmiCloseBlock
WmiDevInstToInstanceNameA
WmiDevInstToInstanceNameW
WmiEnumerateGuids
WmiExecuteMethodA
WmiExecuteMethodW
WmiFileHandleToInstanceNameA
WmiFileHandleToInstanceNameW
WmiFreeBuffer
WmiMofEnumerateResourcesA
WmiMofEnumerateResourcesW
WmiNotificationRegistrationA
WmiNotificationRegistrationW
WmiO<PERSON>lock
WmiQueryAllDataA
WmiQueryAllDataW
WmiQueryGuidInformation
WmiQuerySingleInstanceA
WmiQuerySingleInstanceW
WmiSetSingleInstanceA
WmiSetSingleInstanceW
WmiSetSingleItemA
WmiSetSingleItemW
