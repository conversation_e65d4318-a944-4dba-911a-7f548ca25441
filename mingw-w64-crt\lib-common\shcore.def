LIBRARY "SHCORE.dll"
EXPORTS
CommandLineToArgvW
CreateRandomAccessStreamOnFile
CreateRandomAccessStreamOverStream
CreateStreamOverRandomAccessStream
DllCanUnloadNow
DllGetActivationFactory
DllGetClassObject
GetCurrentProcessExplicitAppUserModelID
GetDpiForMonitor
GetDpiForShellUIComponent
GetFeatureEnabledState
GetFeatureVariant
GetProcessDpiAwareness
GetProcessReference
GetScaleFactorForDevice
GetScaleFactorForMonitor
IStream_Copy
IStream_Read
IStream_ReadStr
IStream_Reset
IStream_Size
IStream_Write
IStream_WriteStr
IUnknown_AtomicRelease
IUnknown_GetSite
IUnknown_QueryService
IUnknown_Set
IUnknown_SetSite
IsOS
IsProcessInIsolatedContainer
IsProcessInWDAGContainer
RecordFeatureError
RecordFeatureUsage
RegisterScaleChangeEvent
RegisterScaleChangeNotifications
RevokeScaleChangeNotifications
SHAnsiToAnsi
SHAnsiToUnicode
SHCopyKeyA
SHCopyKeyW
SHCreateMemStream
SHCreateStreamOnFileA
SHCreateStreamOnFileEx
SHCreateStreamOnFileW
SHCreateThread
SHCreateThreadRef
SHCreateThreadWithHandle
SHDeleteEmptyKeyA
SHDeleteEmptyKeyW
SHDeleteKeyA
SHDeleteKeyW
SHDeleteValueA
SHDeleteValueW
SHEnumKeyExA
SHEnumKeyExW
SHEnumValueA
SHEnumValueW
SHGetThreadRef
SHGetValueA
SHGetValueW
SHOpenRegStream2A
SHOpenRegStream2W
SHOpenRegStreamA
SHOpenRegStreamW
SHQueryInfoKeyA
SHQueryInfoKeyW
SHQueryValueExA
SHQueryValueExW
SHRegDuplicateHKey
SHRegGetIntW
SHRegGetPathA
SHRegGetPathW
SHRegGetValueA
SHRegGetValueW
SHRegSetPathA
SHRegSetPathW
SHReleaseThreadRef
SHSetThreadRef
SHSetValueA
SHSetValueW
SHStrDupA
SHStrDupW
SHTaskPoolAllowThreadReuse
SHTaskPoolDoNotWaitForMoreTasks
SHTaskPoolGetCurrentThreadLifetime
SHTaskPoolGetUniqueContext
SHTaskPoolQueueTask
SHTaskPoolSetThreadReuseAllowed
SHUnicodeToAnsi
SHUnicodeToUnicode
SetCurrentProcessExplicitAppUserModelID
SetProcessDpiAwareness
SetProcessReference
SubscribeFeatureStateChangeNotification
UnregisterScaleChangeEvent
UnsubscribeFeatureStateChangeNotification
SHRegGetValueFromHKCUHKLM
