;
; Definition file of WTSAPI32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "WTSAPI32.dll"
EXPORTS
WTSCloseServer@4
WTSConnectSessionA@16
WTSConnectSessionW@16
WTSDisconnectSession@12
WTSEnumerateProcessesA@20
WTSEnumerateProcessesW@20
WTSEnumerateServersA@20
WTSEnumerateServersW@20
WTSEnumerateSessionsA@20
WTSEnumerateSessionsW@20
WTSEnumerateSessionsExA@20
WTSEnumerateSessionsExW@20
WTSFreeMemory@4
WTSFreeMemoryExA@12
WTSFreeMemoryExW@12
WTSLogoffSession@12
WTSOpenServerA@4
WTSOpenServerW@4
WTSQuerySessionInformationA@20
WTSQuerySessionInformationW@20
WTSQueryUserConfigA@20
WTSQueryUserConfigW@20
WTSQueryUserToken@8
WTSRegisterSessionNotification@8
WTSRegisterSessionNotificationEx@12
WTSSendMessageA@40
WTSSendMessageW@40
WTSSetSessionInformationA@20
WTSSetSessionInformationW@20
WTSSetUserConfigA@20
WTSSetUserConfigW@20
WTSShutdownSystem@8
WTSStartRemoteControlSessionA@16
WTSStartRemoteControlSessionW@16
WTSStopRemoteControlSession@4
WTSTerminateProcess@12
WTSUnRegisterSessionNotification@4
WTSUnRegisterSessionNotificationEx@8
WTSVirtualChannelClose@4
WTSVirtualChannelOpen@12
WTSVirtualChannelOpenEx@12
WTSVirtualChannelPurgeInput@4
WTSVirtualChannelPurgeOutput@4
WTSVirtualChannelQuery@16
WTSVirtualChannelRead@20
WTSVirtualChannelWrite@16
WTSWaitSystemEvent@12
