2014-05-20  <PERSON>  <<EMAIL>>

	* opmapi.h (OPM_GET_CODEC_INFO_INFORMATION): Define.
	* (OPM_GET_CODEC_INFO_PARAMETERS): Ditto.

2014-05-08  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* lmaccess.h (struct _USER_INFO_24): Define.

2014-04-24  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* mstcpip.h: Add LP64 u_long override.
	* ws2ipdef.h: Ditto.
	* ws2tcpip.h: Ditto.

2014-03-24  <PERSON>  <<EMAIL>>

	* nap*: Add NAP API.
	* naperror.h: Move content into winerror.h and remove.
	* winerror.h: Add content of naperror.h header.
