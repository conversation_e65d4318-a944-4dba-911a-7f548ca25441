;
; Definition file of WTSAPI32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "WTSAPI32.dll"
EXPORTS
QueryActiveSession
QueryUserToken
RegisterUsertokenForNoWinlogon
WTSCloseServer
WTSConnectSessionA
WTSConnectSessionW
WTSCreateListenerA
WTSCreateListenerW
WTSDisconnectSession
WTSEnableChildSessions
WTSEnumerateListenersA
WTSEnumerateListenersW
WTSEnumerateProcessesA
WTSEnumerateProcessesExA
WTSEnumerateProcessesExW
WTSEnumerateProcessesW
WTSEnumerateServersA
WTSEnumerateServersW
WTSEnumerateSessionsA
WTSEnumerateSessionsExA
WTSEnumerateSessionsExW
WTSEnumerateSessionsW
WTSFreeMemory
WTSFreeMemoryExA
WTSFreeMemoryExW
WTSGetChildSessionId
WTSGetListenerSecurityA
WTS<PERSON><PERSON>enerSecurityW
WTSIsChildSessionsEnabled
WTSLogoffSession
WTSOpenServerA
WTSOpenServerExA
WTSOpenServerExW
WTSOpenServerW
WTSQueryListenerConfigA
WTSQueryListenerConfigW
WTSQuerySessionInformationA
WTSQuerySessionInformationW
WTSQueryUserConfigA
WTSQueryUserConfigW
WTSQueryUserToken
WTSRegisterSessionNotification
WTSRegisterSessionNotificationEx
WTSSendMessageA
WTSSendMessageW
WTSSetListenerSecurityA
WTSSetListenerSecurityW
WTSSetRenderHint
WTSSetSessionInformationA
WTSSetSessionInformationW
WTSSetUserConfigA
WTSSetUserConfigW
WTSShutdownSystem
WTSStartRemoteControlSessionA
WTSStartRemoteControlSessionW
WTSStopRemoteControlSession
WTSTerminateProcess
WTSUnRegisterSessionNotification
WTSUnRegisterSessionNotificationEx
WTSVirtualChannelClose
WTSVirtualChannelOpen
WTSVirtualChannelOpenEx
WTSVirtualChannelPurgeInput
WTSVirtualChannelPurgeOutput
WTSVirtualChannelQuery
WTSVirtualChannelRead
WTSVirtualChannelWrite
WTSWaitSystemEvent
