<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: sMToken_value Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&nbsp;Fields</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>sMToken_value Struct Reference</h1><!-- doxytag: class="sMToken_value" -->
<p><code>#include &lt;<a class="el" href="m__token_8h_source.html">m_token.h</a>&gt;</code></p>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Data Fields</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="structs_m_token__base.html">sMToken_base</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__value.html#af2e6b79bc6f420cfe90256a8527c0e1e">base</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__value.html#a2f802692b54a6c65c1845939d0dbb202">value</a></td></tr>
<tr><td class="memItemLeft" align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__value.html#a6e11527994c9fcff6f3afecd3b8f3059">size</a>: 5</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structs_m_token__value.html#a63ebab037c42f8740600cdec41b92407">is_signed</a>: 1</td></tr>
</table>
<hr/><a name="_details"></a><h2>Detailed Description</h2>
<p>"value" token. Contains numerical expressions for decoded names. </p>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="structs_m_token__dim.html">sMToken_dim</a> </dd></dl>
<hr/><h2>Field Documentation</h2>
<a class="anchor" id="af2e6b79bc6f420cfe90256a8527c0e1e"></a><!-- doxytag: member="sMToken_value::base" ref="af2e6b79bc6f420cfe90256a8527c0e1e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structs_m_token__base.html">sMToken_base</a> <a class="el" href="structs_m_token__value.html#af2e6b79bc6f420cfe90256a8527c0e1e">sMToken_value::base</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Base descriptor header. </p>

</div>
</div>
<a class="anchor" id="a63ebab037c42f8740600cdec41b92407"></a><!-- doxytag: member="sMToken_value::is_signed" ref="a63ebab037c42f8740600cdec41b92407" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structs_m_token__value.html#a63ebab037c42f8740600cdec41b92407">sMToken_value::is_signed</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Value signed bit </p>

</div>
</div>
<a class="anchor" id="a6e11527994c9fcff6f3afecd3b8f3059"></a><!-- doxytag: member="sMToken_value::size" ref="a6e11527994c9fcff6f3afecd3b8f3059" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structs_m_token__value.html#a6e11527994c9fcff6f3afecd3b8f3059">sMToken_value::size</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Byte width of value. </p>

</div>
</div>
<a class="anchor" id="a2f802692b54a6c65c1845939d0dbb202"></a><!-- doxytag: member="sMToken_value::value" ref="a2f802692b54a6c65c1845939d0dbb202" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structs_m_token__value.html#a2f802692b54a6c65c1845939d0dbb202">sMToken_value::value</a></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Integer value. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="m__token_8h_source.html">m_token.h</a></li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
