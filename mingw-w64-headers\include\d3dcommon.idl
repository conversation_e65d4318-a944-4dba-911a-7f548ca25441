/*
 * Copyright 2010 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";

typedef struct _D3D_SHADER_MACRO
{
    const char *Name;
    const char *Definition;
} D3D_SHADER_MACRO;

typedef struct _D3D_SHADER_MACRO* LPD3D_SHADER_MACRO;

[
    object,
    local,
    uuid(8ba5fb08-5195-40e2-ac58-0d989c3a0102)
]
interface ID3D10Blob : IUnknown
{
    void *GetBufferPointer();
    SIZE_T GetBufferSize();
}

typedef ID3D10Blob* LPD3D10BLOB;
typedef ID3D10Blob ID3DBlob;
typedef ID3DBlob* LPD3DBLOB;
cpp_quote("#define IID_ID3DBlob IID_ID3D10Blob")

typedef void(__stdcall *PFN_DESTRUCTION_CALLBACK)(void *data);

[
    object,
    local,
    uuid(a06eb39a-50da-425b-8c31-4eecd6c270f3),
    pointer_default(unique)
]
interface ID3DDestructionNotifier : IUnknown
{
    HRESULT RegisterDestructionCallback(
        [in] PFN_DESTRUCTION_CALLBACK callback_func,
        [in] void *data,
        [out] UINT *callback_id
    );
    HRESULT UnregisterDestructionCallback(
        [in] UINT callback_id
    );
}

typedef enum _D3D_INCLUDE_TYPE
{
    D3D_INCLUDE_LOCAL = 0,
    D3D_INCLUDE_SYSTEM,
    D3D10_INCLUDE_LOCAL = D3D_INCLUDE_LOCAL,
    D3D10_INCLUDE_SYSTEM = D3D_INCLUDE_SYSTEM,
    D3D_INCLUDE_FORCE_DWORD = 0x7fffffff
} D3D_INCLUDE_TYPE;

[
    object,
    local,
]
interface ID3DInclude
{
    HRESULT Open(D3D_INCLUDE_TYPE include_type,
                 const char *filename,
                 const void *parent_data,
                 const void **data,
                 UINT *bytes);
    HRESULT Close(const void *data);
}

typedef ID3DInclude* LPD3DINCLUDE;

typedef enum D3D_DRIVER_TYPE
{
    D3D_DRIVER_TYPE_UNKNOWN,
    D3D_DRIVER_TYPE_HARDWARE,
    D3D_DRIVER_TYPE_REFERENCE,
    D3D_DRIVER_TYPE_NULL,
    D3D_DRIVER_TYPE_SOFTWARE,
    D3D_DRIVER_TYPE_WARP,
} D3D_DRIVER_TYPE;

typedef enum D3D_FEATURE_LEVEL
{
    D3D_FEATURE_LEVEL_9_1 = 0x9100,
    D3D_FEATURE_LEVEL_9_2 = 0x9200,
    D3D_FEATURE_LEVEL_9_3 = 0x9300,
    D3D_FEATURE_LEVEL_10_0 = 0xa000,
    D3D_FEATURE_LEVEL_10_1 = 0xa100,
    D3D_FEATURE_LEVEL_11_0 = 0xb000,
    D3D_FEATURE_LEVEL_11_1 = 0xb100,
    D3D_FEATURE_LEVEL_12_0 = 0xc000,
    D3D_FEATURE_LEVEL_12_1 = 0xc100,
} D3D_FEATURE_LEVEL;

cpp_quote("#define D3D_FL9_1_REQ_TEXTURE1D_U_DIMENSION          2048")
cpp_quote("#define D3D_FL9_3_REQ_TEXTURE1D_U_DIMENSION          4096")
cpp_quote("#define D3D_FL9_1_REQ_TEXTURE2D_U_OR_V_DIMENSION     2048")
cpp_quote("#define D3D_FL9_3_REQ_TEXTURE2D_U_OR_V_DIMENSION     4096")
cpp_quote("#define D3D_FL9_1_REQ_TEXTURECUBE_DIMENSION          512")
cpp_quote("#define D3D_FL9_3_REQ_TEXTURECUBE_DIMENSION          4096")
cpp_quote("#define D3D_FL9_1_REQ_TEXTURE3D_U_V_OR_W_DIMENSION   256")
cpp_quote("#define D3D_FL9_1_DEFAULT_MAX_ANISOTROPY             2")
cpp_quote("#define D3D_FL9_1_IA_PRIMITIVE_MAX_COUNT             65535")
cpp_quote("#define D3D_FL9_2_IA_PRIMITIVE_MAX_COUNT             1048575")
cpp_quote("#define D3D_FL9_1_SIMULTANEOUS_RENDER_TARGET_COUNT   1")
cpp_quote("#define D3D_FL9_3_SIMULTANEOUS_RENDER_TARGET_COUNT   4")
cpp_quote("#define D3D_FL9_1_MAX_TEXTURE_REPEAT                 128")
cpp_quote("#define D3D_FL9_2_MAX_TEXTURE_REPEAT                 2048")
cpp_quote("#define D3D_FL9_3_MAX_TEXTURE_REPEAT                 8192")

typedef enum _D3D_SHADER_VARIABLE_CLASS
{
    D3D_SVC_SCALAR,
    D3D_SVC_VECTOR,
    D3D_SVC_MATRIX_ROWS,
    D3D_SVC_MATRIX_COLUMNS,
    D3D_SVC_OBJECT,
    D3D_SVC_STRUCT,
    D3D_SVC_INTERFACE_CLASS,
    D3D_SVC_INTERFACE_POINTER,
    D3D10_SVC_SCALAR = 0,
    D3D10_SVC_VECTOR,
    D3D10_SVC_MATRIX_ROWS,
    D3D10_SVC_MATRIX_COLUMNS,
    D3D10_SVC_OBJECT,
    D3D10_SVC_STRUCT,
    D3D11_SVC_INTERFACE_CLASS,
    D3D11_SVC_INTERFACE_POINTER,
    D3D_SVC_FORCE_DWORD = 0x7fffffff,
} D3D_SHADER_VARIABLE_CLASS;

typedef enum _D3D_SHADER_VARIABLE_FLAGS
{
    D3D_SVF_USERPACKED            = 0x01,
    D3D_SVF_USED                  = 0x02,
    D3D_SVF_INTERFACE_POINTER     = 0x04,
    D3D_SVF_INTERFACE_PARAMETER   = 0x08,
    D3D10_SVF_USERPACKED          = D3D_SVF_USERPACKED,
    D3D10_SVF_USED                = D3D_SVF_USED,
    D3D11_SVF_INTERFACE_POINTER   = D3D_SVF_INTERFACE_POINTER,
    D3D11_SVF_INTERFACE_PARAMETER = D3D_SVF_INTERFACE_PARAMETER,
    D3D_SVF_FORCE_DWORD           = 0x7fffffff
} D3D_SHADER_VARIABLE_FLAGS;

typedef enum _D3D_SHADER_VARIABLE_TYPE
{
    D3D_SVT_VOID,
    D3D_SVT_BOOL,
    D3D_SVT_INT,
    D3D_SVT_FLOAT,
    D3D_SVT_STRING,
    D3D_SVT_TEXTURE,
    D3D_SVT_TEXTURE1D,
    D3D_SVT_TEXTURE2D,
    D3D_SVT_TEXTURE3D,
    D3D_SVT_TEXTURECUBE,
    D3D_SVT_SAMPLER,
    D3D_SVT_SAMPLER1D,
    D3D_SVT_SAMPLER2D,
    D3D_SVT_SAMPLER3D,
    D3D_SVT_SAMPLERCUBE,
    D3D_SVT_PIXELSHADER,
    D3D_SVT_VERTEXSHADER,
    D3D_SVT_PIXELFRAGMENT,
    D3D_SVT_VERTEXFRAGMENT,
    D3D_SVT_UINT,
    D3D_SVT_UINT8,
    D3D_SVT_GEOMETRYSHADER,
    D3D_SVT_RASTERIZER,
    D3D_SVT_DEPTHSTENCIL,
    D3D_SVT_BLEND,
    D3D_SVT_BUFFER,
    D3D_SVT_CBUFFER,
    D3D_SVT_TBUFFER,
    D3D_SVT_TEXTURE1DARRAY,
    D3D_SVT_TEXTURE2DARRAY,
    D3D_SVT_RENDERTARGETVIEW,
    D3D_SVT_DEPTHSTENCILVIEW,
    D3D_SVT_TEXTURE2DMS,
    D3D_SVT_TEXTURE2DMSARRAY,
    D3D_SVT_TEXTURECUBEARRAY,
    D3D_SVT_HULLSHADER,
    D3D_SVT_DOMAINSHADER,
    D3D_SVT_INTERFACE_POINTER,
    D3D_SVT_COMPUTESHADER,
    D3D_SVT_DOUBLE,
    D3D_SVT_RWTEXTURE1D,
    D3D_SVT_RWTEXTURE1DARRAY,
    D3D_SVT_RWTEXTURE2D,
    D3D_SVT_RWTEXTURE2DARRAY,
    D3D_SVT_RWTEXTURE3D,
    D3D_SVT_RWBUFFER,
    D3D_SVT_BYTEADDRESS_BUFFER,
    D3D_SVT_RWBYTEADDRESS_BUFFER,
    D3D_SVT_STRUCTURED_BUFFER,
    D3D_SVT_RWSTRUCTURED_BUFFER,
    D3D_SVT_APPEND_STRUCTURED_BUFFER,
    D3D_SVT_CONSUME_STRUCTURED_BUFFER,
    D3D_SVT_MIN8FLOAT,
    D3D_SVT_MIN10FLOAT,
    D3D_SVT_MIN16FLOAT,
    D3D_SVT_MIN12INT,
    D3D_SVT_MIN16INT,
    D3D_SVT_MIN16UINT,
    D3D10_SVT_VOID = 0,
    D3D10_SVT_BOOL,
    D3D10_SVT_INT,
    D3D10_SVT_FLOAT,
    D3D10_SVT_STRING,
    D3D10_SVT_TEXTURE,
    D3D10_SVT_TEXTURE1D,
    D3D10_SVT_TEXTURE2D,
    D3D10_SVT_TEXTURE3D,
    D3D10_SVT_TEXTURECUBE,
    D3D10_SVT_SAMPLER,
    D3D10_SVT_SAMPLER1D,
    D3D10_SVT_SAMPLER2D,
    D3D10_SVT_SAMPLER3D,
    D3D10_SVT_SAMPLERCUBE,
    D3D10_SVT_PIXELSHADER,
    D3D10_SVT_VERTEXSHADER,
    D3D10_SVT_PIXELFRAGMENT,
    D3D10_SVT_VERTEXFRAGMENT,
    D3D10_SVT_UINT,
    D3D10_SVT_UINT8,
    D3D10_SVT_GEOMETRYSHADER,
    D3D10_SVT_RASTERIZER,
    D3D10_SVT_DEPTHSTENCIL,
    D3D10_SVT_BLEND,
    D3D10_SVT_BUFFER,
    D3D10_SVT_CBUFFER,
    D3D10_SVT_TBUFFER,
    D3D10_SVT_TEXTURE1DARRAY,
    D3D10_SVT_TEXTURE2DARRAY,
    D3D10_SVT_RENDERTARGETVIEW,
    D3D10_SVT_DEPTHSTENCILVIEW,
    D3D10_SVT_TEXTURE2DMS,
    D3D10_SVT_TEXTURE2DMSARRAY,
    D3D10_SVT_TEXTURECUBEARRAY,
    D3D11_SVT_HULLSHADER,
    D3D11_SVT_DOMAINSHADER,
    D3D11_SVT_INTERFACE_POINTER,
    D3D11_SVT_COMPUTESHADER,
    D3D11_SVT_DOUBLE,
    D3D11_SVT_RWTEXTURE1D,
    D3D11_SVT_RWTEXTURE1DARRAY,
    D3D11_SVT_RWTEXTURE2D,
    D3D11_SVT_RWTEXTURE2DARRAY,
    D3D11_SVT_RWTEXTURE3D,
    D3D11_SVT_RWBUFFER,
    D3D11_SVT_BYTEADDRESS_BUFFER,
    D3D11_SVT_RWBYTEADDRESS_BUFFER,
    D3D11_SVT_STRUCTURED_BUFFER,
    D3D11_SVT_RWSTRUCTURED_BUFFER,
    D3D11_SVT_APPEND_STRUCTURED_BUFFER,
    D3D11_SVT_CONSUME_STRUCTURED_BUFFER,
    D3D_SVT_FORCE_DWORD = 0x7fffffff,
} D3D_SHADER_VARIABLE_TYPE;

typedef enum _D3D_SHADER_INPUT_FLAGS
{
    D3D_SIF_USERPACKED            = 0x01,
    D3D_SIF_COMPARISON_SAMPLER    = 0x02,
    D3D_SIF_TEXTURE_COMPONENT_0   = 0x04,
    D3D_SIF_TEXTURE_COMPONENT_1   = 0x08,
    D3D_SIF_TEXTURE_COMPONENTS    = 0x0C,
    D3D_SIF_UNUSED                = 0x10,
    D3D10_SIF_USERPACKED          = D3D_SIF_USERPACKED,
    D3D10_SIF_COMPARISON_SAMPLER  = D3D_SIF_COMPARISON_SAMPLER,
    D3D10_SIF_TEXTURE_COMPONENT_0 = D3D_SIF_TEXTURE_COMPONENT_0,
    D3D10_SIF_TEXTURE_COMPONENT_1 = D3D_SIF_TEXTURE_COMPONENT_1,
    D3D10_SIF_TEXTURE_COMPONENTS  = D3D_SIF_TEXTURE_COMPONENTS,
    D3D_SIF_FORCE_DWORD           = 0x7fffffff
} D3D_SHADER_INPUT_FLAGS;

typedef enum D3D_PRIMITIVE
{
    D3D_PRIMITIVE_UNDEFINED,
    D3D_PRIMITIVE_POINT,
    D3D_PRIMITIVE_LINE,
    D3D_PRIMITIVE_TRIANGLE,
    D3D_PRIMITIVE_LINE_ADJ = 6,
    D3D_PRIMITIVE_TRIANGLE_ADJ,
    D3D_PRIMITIVE_1_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_2_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_3_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_4_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_5_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_6_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_7_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_8_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_9_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_10_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_11_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_12_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_13_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_14_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_15_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_16_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_17_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_18_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_19_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_20_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_21_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_22_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_23_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_24_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_26_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_27_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_28_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_29_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_30_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_31_CONTROL_POINT_PATCH,
    D3D_PRIMITIVE_32_CONTROL_POINT_PATCH,
    D3D10_PRIMITIVE_UNDEFINED = 0,
    D3D10_PRIMITIVE_POINT,
    D3D10_PRIMITIVE_LINE,
    D3D10_PRIMITIVE_TRIANGLE,
    D3D10_PRIMITIVE_LINE_ADJ = 6,
    D3D10_PRIMITIVE_TRIANGLE_ADJ,
    D3D11_PRIMITIVE_UNDEFINED = 0,
    D3D11_PRIMITIVE_POINT,
    D3D11_PRIMITIVE_LINE,
    D3D11_PRIMITIVE_TRIANGLE,
    D3D11_PRIMITIVE_LINE_ADJ = 6,
    D3D11_PRIMITIVE_TRIANGLE_ADJ,
    D3D11_PRIMITIVE_1_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_2_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_3_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_4_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_5_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_6_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_7_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_8_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_9_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_10_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_11_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_12_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_13_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_14_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_15_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_16_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_17_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_18_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_19_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_20_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_21_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_22_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_23_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_24_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_25_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_26_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_27_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_28_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_29_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_30_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_31_CONTROL_POINT_PATCH,
    D3D11_PRIMITIVE_32_CONTROL_POINT_PATCH,
} D3D_PRIMITIVE;

typedef enum D3D_PRIMITIVE_TOPOLOGY
{
    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED,
    D3D_PRIMITIVE_TOPOLOGY_POINTLIST,
    D3D_PRIMITIVE_TOPOLOGY_LINELIST,
    D3D_PRIMITIVE_TOPOLOGY_LINESTRIP,
    D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST,
    D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP,
    D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ = 10,
    D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ,
    D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ,
    D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ,
    D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST = 33,
    D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST,
    D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST,
    D3D10_PRIMITIVE_TOPOLOGY_UNDEFINED = 0,
    D3D10_PRIMITIVE_TOPOLOGY_POINTLIST,
    D3D10_PRIMITIVE_TOPOLOGY_LINELIST,
    D3D10_PRIMITIVE_TOPOLOGY_LINESTRIP,
    D3D10_PRIMITIVE_TOPOLOGY_TRIANGLELIST,
    D3D10_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP,
    D3D10_PRIMITIVE_TOPOLOGY_LINELIST_ADJ = 10,
    D3D10_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ,
    D3D10_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ,
    D3D10_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ,
    D3D11_PRIMITIVE_TOPOLOGY_UNDEFINED = 0,
    D3D11_PRIMITIVE_TOPOLOGY_POINTLIST,
    D3D11_PRIMITIVE_TOPOLOGY_LINELIST,
    D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP,
    D3D11_PRIMITIVE_TOPOLOGY_LINELIST_ADJ = 10,
    D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ,
    D3D11_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST = 33,
    D3D11_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST,
    D3D11_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST,
} D3D_PRIMITIVE_TOPOLOGY;

typedef enum D3D_TESSELLATOR_DOMAIN
{
    D3D_TESSELLATOR_DOMAIN_UNDEFINED,
    D3D_TESSELLATOR_DOMAIN_ISOLINE,
    D3D_TESSELLATOR_DOMAIN_TRI,
    D3D_TESSELLATOR_DOMAIN_QUAD,
    D3D11_TESSELLATOR_DOMAIN_UNDEFINED = 0,
    D3D11_TESSELLATOR_DOMAIN_ISOLINE,
    D3D11_TESSELLATOR_DOMAIN_TRI,
    D3D11_TESSELLATOR_DOMAIN_QUAD,
} D3D_TESSELLATOR_DOMAIN;

typedef enum D3D_TESSELLATOR_PARTITIONING
{
    D3D_TESSELLATOR_PARTITIONING_UNDEFINED,
    D3D_TESSELLATOR_PARTITIONING_INTEGER,
    D3D_TESSELLATOR_PARTITIONING_POW2,
    D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD,
    D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN,
    D3D11_TESSELLATOR_PARTITIONING_UNDEFINED = 0,
    D3D11_TESSELLATOR_PARTITIONING_INTEGER,
    D3D11_TESSELLATOR_PARTITIONING_POW2,
    D3D11_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD,
    D3D11_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN,
} D3D_TESSELLATOR_PARTITIONING;

typedef enum D3D_TESSELLATOR_OUTPUT_PRIMITIVE
{
    D3D_TESSELLATOR_OUTPUT_UNDEFINED,
    D3D_TESSELLATOR_OUTPUT_POINT,
    D3D_TESSELLATOR_OUTPUT_LINE,
    D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW,
    D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW,
    D3D11_TESSELLATOR_OUTPUT_UNDEFINED = 0,
    D3D11_TESSELLATOR_OUTPUT_POINT,
    D3D11_TESSELLATOR_OUTPUT_LINE,
    D3D11_TESSELLATOR_OUTPUT_TRIANGLE_CW,
    D3D11_TESSELLATOR_OUTPUT_TRIANGLE_CCW,
} D3D_TESSELLATOR_OUTPUT_PRIMITIVE;

typedef enum D3D_MIN_PRECISION
{
    D3D_MIN_PRECISION_DEFAULT = 0,
    D3D_MIN_PRECISION_FLOAT_16 = 1,
    D3D_MIN_PRECISION_FLOAT_2_8 = 2,
    D3D_MIN_PRECISION_RESERVED = 3,
    D3D_MIN_PRECISION_SINT_16 = 4,
    D3D_MIN_PRECISION_UINT_16 = 5,
    D3D_MIN_PRECISION_ANY_16 = 0xf0,
    D3D_MIN_PRECISION_ANY_10 = 0xf1,
} D3D_MIN_PRECISION;

typedef enum D3D_CBUFFER_TYPE
{
    D3D_CT_CBUFFER,
    D3D_CT_TBUFFER,
    D3D_CT_INTERFACE_POINTERS,
    D3D_CT_RESOURCE_BIND_INFO,
    D3D10_CT_CBUFFER = 0,
    D3D10_CT_TBUFFER,
    D3D11_CT_CBUFFER = 0,
    D3D11_CT_TBUFFER,
    D3D11_CT_INTERFACE_POINTERS,
    D3D11_CT_RESOURCE_BIND_INFO,
} D3D_CBUFFER_TYPE;

typedef enum D3D_SRV_DIMENSION
{
    D3D_SRV_DIMENSION_UNKNOWN,
    D3D_SRV_DIMENSION_BUFFER,
    D3D_SRV_DIMENSION_TEXTURE1D,
    D3D_SRV_DIMENSION_TEXTURE1DARRAY,
    D3D_SRV_DIMENSION_TEXTURE2D,
    D3D_SRV_DIMENSION_TEXTURE2DARRAY,
    D3D_SRV_DIMENSION_TEXTURE2DMS,
    D3D_SRV_DIMENSION_TEXTURE2DMSARRAY,
    D3D_SRV_DIMENSION_TEXTURE3D,
    D3D_SRV_DIMENSION_TEXTURECUBE,
    D3D_SRV_DIMENSION_TEXTURECUBEARRAY,
    D3D_SRV_DIMENSION_BUFFEREX,
    D3D10_SRV_DIMENSION_UNKNOWN = 0,
    D3D10_SRV_DIMENSION_BUFFER,
    D3D10_SRV_DIMENSION_TEXTURE1D,
    D3D10_SRV_DIMENSION_TEXTURE1DARRAY,
    D3D10_SRV_DIMENSION_TEXTURE2D,
    D3D10_SRV_DIMENSION_TEXTURE2DARRAY,
    D3D10_SRV_DIMENSION_TEXTURE2DMS,
    D3D10_SRV_DIMENSION_TEXTURE2DMSARRAY,
    D3D10_SRV_DIMENSION_TEXTURE3D,
    D3D10_SRV_DIMENSION_TEXTURECUBE,
    D3D10_1_SRV_DIMENSION_UNKNOWN = 0,
    D3D10_1_SRV_DIMENSION_BUFFER,
    D3D10_1_SRV_DIMENSION_TEXTURE1D,
    D3D10_1_SRV_DIMENSION_TEXTURE1DARRAY,
    D3D10_1_SRV_DIMENSION_TEXTURE2D,
    D3D10_1_SRV_DIMENSION_TEXTURE2DARRAY,
    D3D10_1_SRV_DIMENSION_TEXTURE2DMS,
    D3D10_1_SRV_DIMENSION_TEXTURE2DMSARRAY,
    D3D10_1_SRV_DIMENSION_TEXTURE3D,
    D3D10_1_SRV_DIMENSION_TEXTURECUBE,
    D3D10_1_SRV_DIMENSION_TEXTURECUBEARRAY,
    D3D11_SRV_DIMENSION_UNKNOWN = 0,
    D3D11_SRV_DIMENSION_BUFFER,
    D3D11_SRV_DIMENSION_TEXTURE1D,
    D3D11_SRV_DIMENSION_TEXTURE1DARRAY,
    D3D11_SRV_DIMENSION_TEXTURE2D,
    D3D11_SRV_DIMENSION_TEXTURE2DARRAY,
    D3D11_SRV_DIMENSION_TEXTURE2DMS,
    D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY,
    D3D11_SRV_DIMENSION_TEXTURE3D,
    D3D11_SRV_DIMENSION_TEXTURECUBE,
    D3D11_SRV_DIMENSION_TEXTURECUBEARRAY,
    D3D11_SRV_DIMENSION_BUFFEREX,
} D3D_SRV_DIMENSION;

typedef enum D3D_REGISTER_COMPONENT_TYPE
{
    D3D_REGISTER_COMPONENT_UNKNOWN,
    D3D_REGISTER_COMPONENT_UINT32,
    D3D_REGISTER_COMPONENT_SINT32,
    D3D_REGISTER_COMPONENT_FLOAT32,
    D3D10_REGISTER_COMPONENT_UNKNOWN = 0,
    D3D10_REGISTER_COMPONENT_UINT32,
    D3D10_REGISTER_COMPONENT_SINT32,
    D3D10_REGISTER_COMPONENT_FLOAT32,
} D3D_REGISTER_COMPONENT_TYPE;

typedef enum D3D_RESOURCE_RETURN_TYPE
{
    D3D_RETURN_TYPE_UNORM = 1,
    D3D_RETURN_TYPE_SNORM,
    D3D_RETURN_TYPE_SINT,
    D3D_RETURN_TYPE_UINT,
    D3D_RETURN_TYPE_FLOAT,
    D3D_RETURN_TYPE_MIXED,
    D3D_RETURN_TYPE_DOUBLE,
    D3D_RETURN_TYPE_CONTINUED,
    D3D10_RETURN_TYPE_UNORM = 1,
    D3D10_RETURN_TYPE_SNORM,
    D3D10_RETURN_TYPE_SINT,
    D3D10_RETURN_TYPE_UINT,
    D3D10_RETURN_TYPE_FLOAT,
    D3D10_RETURN_TYPE_MIXED,
    D3D11_RETURN_TYPE_UNORM = 1,
    D3D11_RETURN_TYPE_SNORM,
    D3D11_RETURN_TYPE_SINT,
    D3D11_RETURN_TYPE_UINT,
    D3D11_RETURN_TYPE_FLOAT,
    D3D11_RETURN_TYPE_MIXED,
    D3D11_RETURN_TYPE_DOUBLE,
    D3D11_RETURN_TYPE_CONTINUED,
} D3D_RESOURCE_RETURN_TYPE;

typedef enum D3D_NAME
{
    D3D_NAME_UNDEFINED,
    D3D_NAME_POSITION,
    D3D_NAME_CLIP_DISTANCE,
    D3D_NAME_CULL_DISTANCE,
    D3D_NAME_RENDER_TARGET_ARRAY_INDEX,
    D3D_NAME_VIEWPORT_ARRAY_INDEX,
    D3D_NAME_VERTEX_ID,
    D3D_NAME_PRIMITIVE_ID,
    D3D_NAME_INSTANCE_ID,
    D3D_NAME_IS_FRONT_FACE,
    D3D_NAME_SAMPLE_INDEX,
    D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR,
    D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR,
    D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR,
    D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR,
    D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR,
    D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR,
    D3D_NAME_BARYCENTRICS = 23,
    D3D_NAME_SHADINGRATE,
    D3D_NAME_CULLPRIMITIVE,
    D3D_NAME_TARGET = 64,
    D3D_NAME_DEPTH,
    D3D_NAME_COVERAGE,
    D3D_NAME_DEPTH_GREATER_EQUAL,
    D3D_NAME_DEPTH_LESS_EQUAL,
    D3D10_NAME_UNDEFINED = 0,
    D3D10_NAME_POSITION,
    D3D10_NAME_CLIP_DISTANCE,
    D3D10_NAME_CULL_DISTANCE,
    D3D10_NAME_RENDER_TARGET_ARRAY_INDEX,
    D3D10_NAME_VIEWPORT_ARRAY_INDEX,
    D3D10_NAME_VERTEX_ID,
    D3D10_NAME_PRIMITIVE_ID,
    D3D10_NAME_INSTANCE_ID,
    D3D10_NAME_IS_FRONT_FACE,
    D3D10_NAME_SAMPLE_INDEX,
    D3D11_NAME_FINAL_QUAD_EDGE_TESSFACTOR,
    D3D11_NAME_FINAL_QUAD_INSIDE_TESSFACTOR,
    D3D11_NAME_FINAL_TRI_EDGE_TESSFACTOR,
    D3D11_NAME_FINAL_TRI_INSIDE_TESSFACTOR,
    D3D11_NAME_FINAL_LINE_DETAIL_TESSFACTOR,
    D3D11_NAME_FINAL_LINE_DENSITY_TESSFACTOR,
    D3D10_NAME_TARGET = 64,
    D3D10_NAME_DEPTH,
    D3D10_NAME_COVERAGE,
    D3D11_NAME_DEPTH_GREATER_EQUAL,
    D3D11_NAME_DEPTH_LESS_EQUAL,
} D3D_NAME;

typedef enum _D3D_SHADER_INPUT_TYPE
{
    D3D_SIT_CBUFFER,
    D3D_SIT_TBUFFER,
    D3D_SIT_TEXTURE,
    D3D_SIT_SAMPLER,
    D3D_SIT_UAV_RWTYPED,
    D3D_SIT_STRUCTURED,
    D3D_SIT_UAV_RWSTRUCTURED,
    D3D_SIT_BYTEADDRESS,
    D3D_SIT_UAV_RWBYTEADDRESS,
    D3D_SIT_UAV_APPEND_STRUCTURED,
    D3D_SIT_UAV_CONSUME_STRUCTURED,
    D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER,
    D3D_SIT_RTACCELERATIONSTRUCTURE,
    D3D_SIT_UAV_FEEDBACKTEXTURE,
    D3D10_SIT_CBUFFER = 0,
    D3D10_SIT_TBUFFER,
    D3D10_SIT_TEXTURE,
    D3D10_SIT_SAMPLER,
    D3D11_SIT_UAV_RWTYPED,
    D3D11_SIT_STRUCTURED,
    D3D11_SIT_UAV_RWSTRUCTURED,
    D3D11_SIT_BYTEADDRESS,
    D3D11_SIT_UAV_RWBYTEADDRESS,
    D3D11_SIT_UAV_APPEND_STRUCTURED,
    D3D11_SIT_UAV_CONSUME_STRUCTURED,
    D3D11_SIT_UAV_RWSTRUCTURED_WITH_COUNTER,
} D3D_SHADER_INPUT_TYPE;

typedef enum _D3D_SHADER_CBUFFER_FLAGS
{
    D3D_CBF_USERPACKED      = 0x01,
    D3D10_CBF_USERPACKED    = D3D_CBF_USERPACKED,
    D3D_CBF_FORCE_DWORD     = 0x7fffffff
} D3D_SHADER_CBUFFER_FLAGS;

typedef enum _D3D_PARAMETER_FLAGS
{
    D3D_PF_NONE,
    D3D_PF_IN,
    D3D_PF_OUT,
    D3D_PF_FORCE_DWORD = 0x7fffffff
} D3D_PARAMETER_FLAGS;

typedef enum _D3D_INTERPOLATION_MODE
{
    D3D_INTERPOLATION_UNDEFINED,
    D3D_INTERPOLATION_CONSTANT,
    D3D_INTERPOLATION_LINEAR,
    D3D_INTERPOLATION_LINEAR_CENTROID,
    D3D_INTERPOLATION_LINEAR_NOPERSPECTIVE,
    D3D_INTERPOLATION_LINEAR_NOPERSPECTIVE_CENTROID,
    D3D_INTERPOLATION_LINEAR_SAMPLE,
    D3D_INTERPOLATION_LINEAR_NOPERSPECTIVE_SAMPLE,
} D3D_INTERPOLATION_MODE;

cpp_quote("DEFINE_GUID(WKPDID_D3DDebugObjectName,0x429b8c22,0x9188,0x4b0c,0x87,0x42,0xac,0xb0,0xbf,0x85,0xc2,0x00);")
