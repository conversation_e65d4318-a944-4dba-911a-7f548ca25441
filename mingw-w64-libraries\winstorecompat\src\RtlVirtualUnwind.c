/*
    Copyright (c) 2018 mingw-w64 project

    Contributing authors: <PERSON> is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
*/

#define RtlVirtualUnwind __RtlVirtualUnwind
#include <windows.h>
#undef RtlVirtualUnwind

#ifdef _AMD64_
PEXCEPTION_ROUTINE NTAPI RtlVirtualUnwind(DWORD HandlerType, DWORD64 ImageBase,
                                          DWORD64 ControlPc,
                                          PRUNTIME_FUNCTION FunctionEntry,
                                          PCONTEXT ContextRecord,
                                          PVOID *HandlerData,
                                          PDWORD64 EstablisherFrame,
                                          PKNONVOLATILE_CONTEXT_POINTERS ContextPointers)
{
    return NULL;
}

PEXCEPTION_ROUTINE (NTAPI *__MINGW_IMP_SYMBOL(RtlVirtualUnwind))(DWORD HandlerType, DWORD64 ImageBase, DWORD64 ControlPc, PRUNTIME_FUNCTION FunctionEntry, PCONTEXT ContextRecord, PVOID *HandlerData, PDWORD64 EstablisherFrame, PKNONVOLATILE_CONTEXT_POINTERS ContextPointers) __asm__("__imp_RtlVirtualUnwind") = RtlVirtualUnwind;
#endif
