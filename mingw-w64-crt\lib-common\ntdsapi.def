;
; Definition file of NTDSAPI.dll
; Automatic generated by g<PERSON><PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "NTDSAPI.dll"
EXPORTS
DsAddCloneDCW
DsAddSidHistoryA
DsAddSidHistoryW
DsBindA
DsBindByInstanceA
DsBindByInstanceW
DsBindToISTGA
DsBindToISTGW
DsBindW
DsBindWithCredA
DsBindWithCredW
DsBindWithSpnA
DsBindWithSpnExA
DsBindWithSpnExW
DsBindWithSpnExWWorker
DsBindWithSpnW
DsBindingSetTimeout
DsClientMakeSpnForTargetServerA
DsClientMakeSpnForTargetServerW
DsCrackNamesA
DsCrackNamesW
DsCrackNamesWWorker
DsCrackSpn2A
DsCrackSpn2W
DsCrackSpn3W
DsCrackSpn4W
DsCrackSpnA
DsCrack<PERSON>pnW
DsCrackUnquotedMangledRdnA
DsCrackUnquotedMangledRdnW
DsFinishDemotionW
DsFreeCloneDcResult
DsFreeDomainControllerInfoA
DsFreeDomainControllerInfoW
DsFreeDomainControllerInfoWWorker
DsFreeNameResultA
DsFreeNameResultW
DsFreeNameResultWWorker
DsFreePasswordCredentials
DsFreePasswordCredentialsWorker
DsFreeSchemaGuidMapA
DsFreeSchemaGuidMapW
DsFreeSpnArrayA
DsFreeSpnArrayW
DsGetBindAddrW
DsGetBindAnnotW
DsGetBindInstGuid
DsGetDomainControllerInfoA
DsGetDomainControllerInfoW
DsGetDomainControllerInfoWWorker
DsGetRdnW
DsGetSpnA
DsGetSpnW
DsInheritSecurityIdentityA
DsInheritSecurityIdentityW
DsInitDemotionW
DsIsMangledDnA
DsIsMangledDnW
DsIsMangledRdnValueA
DsIsMangledRdnValueW
DsListDomainsInSiteA
DsListDomainsInSiteW
DsListInfoForServerA
DsListInfoForServerW
DsListRolesA
DsListRolesW
DsListServersForDomainInSiteA
DsListServersForDomainInSiteW
DsListServersInSiteA
DsListServersInSiteW
DsListSitesA
DsListSitesW
DsLogEntry
DsMakePasswordCredentialsA
DsMakePasswordCredentialsW
DsMakePasswordCredentialsWWorker
DsMakeSpnA
DsMakeSpnW
DsMapSchemaGuidsA
DsMapSchemaGuidsW
DsQuerySitesByCostA
DsQuerySitesByCostW
DsQuerySitesFree
DsQuoteRdnValueA
DsQuoteRdnValueW
DsRemoveDsDomainA
DsRemoveDsDomainW
DsRemoveDsServerA
DsRemoveDsServerW
DsReplicaAddA
DsReplicaAddW
DsReplicaConsistencyCheck
DsReplicaDelA
DsReplicaDelW
DsReplicaDemotionW
DsReplicaFreeInfo
DsReplicaGetInfo2W
DsReplicaGetInfoW
DsReplicaModifyA
DsReplicaModifyW
DsReplicaSyncA
DsReplicaSyncAllA
DsReplicaSyncAllW
DsReplicaSyncW
DsReplicaUpdateRefsA
DsReplicaUpdateRefsW
DsReplicaVerifyObjectsA
DsReplicaVerifyObjectsW
DsServerRegisterSpnA
DsServerRegisterSpnW
DsUnBindA
DsUnBindW
DsUnBindWWorker
DsUnquoteRdnValueA
DsUnquoteRdnValueW
DsWriteAccountSpnA
DsWriteAccountSpnW
DsaopBind
DsaopBindWithCred
DsaopBindWithSpn
DsaopExecuteScript
DsaopPrepareScript
DsaopUnBind
