LIBRARY api-ms-win-core-processthreads-l1-1-3

EXPORTS

CreateProcessA
CreateProcessAsUserA
CreateProcessAsUserW
CreateProcessW
CreateThread
ExitProcess
ExitThread
FlushInstructionCache
FlushProcessWriteBuffers
GetCurrentProcess
GetCurrentProcessId
GetCurrentProcessorNumber
GetCurrentProcessorNumberEx
GetCurrentThread
GetCurrentThreadId
GetCurrentThreadStackLimits
GetExitCodeProcess
GetExitCodeThread
GetPriorityClass
GetProcessDefaultCpuSets
GetProcessId
GetProcessInformation
GetProcessMitigationPolicy
GetProcessPriorityBoost
GetProcessTimes
GetStartupInfoW
GetSystemCpuSetInformation
GetSystemTimes
GetThreadContext
GetThreadDescription
GetThreadId
GetThreadIdealProcessorEx
GetThreadPriority
GetThreadPriorityBoost
GetThreadSelectedCpuSets
GetThreadTimes
IsProcessorFeaturePresent
OpenProcess
OpenProcessToken
OpenThread
OpenThreadToken
ProcessIdToSessionId
QueueUserAPC
ResumeThread
SetPriorityClass
SetProcessDefaultCpuSets
SetProcessInformation
SetProcessMitigationPolicy
SetProcessPriorityBoost
SetThreadContext
SetThreadDescription
SetThreadIdealProcessor
SetThreadIdealProcessorEx
SetThreadInformation
SetThreadPriority
SetThreadPriorityBoost
SetThreadSelectedCpuSets
SetThreadStackGuarantee
SetThreadToken
SuspendThread
SwitchToThread
TerminateProcess
TerminateThread
TlsAlloc
TlsFree
TlsGetValue
TlsSetValue
