;
; Definition file of mscms.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "mscms.dll"
EXPORTS
AssociateColorProfileWithDeviceA
AssociateColorProfileWithDeviceW
CheckBitmapBits
CheckColors
CloseColorProfile
CloseDisplay
ColorCplGetDefaultProfileScope
ColorCplGetDefaultRenderingIntentScope
ColorCplGetProfileProperties
ColorCplHasSystemWideAssociationListChanged
ColorCplInitialize
ColorCplLoadAssociationList
ColorCplMergeAssociationLists
ColorCplOverwritePerUserAssociationList
ColorCplReleaseProfileProperties
ColorCplResetSystemWideAssociationListChangedWarning
ColorCplSaveAssociationList
ColorCplSetUsePerUserProfiles
ColorCplUninitialize
ConvertColorNameToIndex
ConvertIndexToColorName
CreateColorTransformA
CreateColo<PERSON><PERSON>formW
CreateDeviceLinkProfile
CreateMultiProfileTransform
CreateProfileFromLogColorSpaceA
CreateProfileFromLogColorSpaceW
DccwCreateDisplayProfileAssociationList
DccwGetDisplayProfileAssociationList
DccwGetGamutSize
DccwReleaseDisplayProfileAssociationList
DccwSetDisplayProfileAssociationList
DeleteColorTransform
DeviceRenameEvent
DisassociateColorProfileFromDeviceA
DisassociateColorProfileFromDeviceW
; DllCanUnloadNow
; DllGetClassObject
EnumColorProfilesA
EnumColorProfilesW
GenerateCopyFilePaths
GetCMMInfo
GetColorDirectoryA
GetColorDirectoryW
GetColorProfileElement
GetColorProfileElementTag
GetColorProfileFromHandle
GetColorProfileHeader
GetCountColorProfileElements
GetNamedProfileInfo
GetPS2ColorRenderingDictionary
GetPS2ColorRenderingIntent
GetPS2ColorSpaceArray
GetStandardColorSpaceProfileA
GetStandardColorSpaceProfileW
InstallColorProfileA
InstallColorProfileW
InternalGetDeviceConfig
InternalGetPS2CSAFromLCS
InternalGetPS2ColorRenderingDictionary
InternalGetPS2ColorSpaceArray
InternalGetPS2PreviewCRD
InternalRefreshCalibration
InternalSetDeviceConfig
InternalWcsAssociateColorProfileWithDevice
InternalWcsDisassociateColorProfileWithDevice
IsColorProfileTagPresent
IsColorProfileValid
OpenColorProfileA
OpenColorProfileW
OpenDisplay
RegisterCMMA
RegisterCMMW
SelectCMM
SetColorProfileElement
SetColorProfileElementReference
SetColorProfileElementSize
SetColorProfileHeader
SetStandardColorSpaceProfileA
SetStandardColorSpaceProfileW
SpoolerCopyFileEvent
TranslateBitmapBits
TranslateColors
UninstallColorProfileA
UninstallColorProfileW
UnregisterCMMA
UnregisterCMMW
WcsAssociateColorProfileWithDevice
WcsCheckColors
WcsCreateIccProfile
WcsDisassociateColorProfileFromDevice
WcsEnumColorProfiles
WcsEnumColorProfilesSize
WcsGetCalibrationManagementState
WcsGetDefaultColorProfile
WcsGetDefaultColorProfileSize
WcsGetDefaultRenderingIntent
WcsGetUsePerUserProfiles
WcsGpCanInstallOrUninstallProfiles
WcsGpCanModifyDeviceAssociationList
WcsOpenColorProfileA
WcsOpenColorProfileW
WcsSetCalibrationManagementState
WcsSetDefaultColorProfile
WcsSetDefaultRenderingIntent
WcsSetUsePerUserProfiles
WcsTranslateColors
InternalGetPS2ColorRenderingDictionary2
InternalGetPS2PreviewCRD2
InternalGetPS2ColorSpaceArray2
InternalSetDeviceGammaRamp
InternalSetDeviceTemperature
InternalGetAppliedGammaRamp
InternalGetDeviceGammaCapability
InternalGetAppliedGDIGammaRamp
InternalSetDeviceGDIGammaRamp
ColorAdapterGetSystemModifyWhitePointCaps
ColorAdapterGetDisplayCurrentStateID
ColorAdapterUpdateDisplayGamma
ColorAdapterUpdateDeviceProfile
ColorAdapterGetDisplayTransformData
ColorAdapterGetDisplayTargetWhitePoint
ColorAdapterGetDisplayProfile
ColorAdapterGetCurrentProfileCalibration
ColorAdapterRegisterOEMColorService
ColorAdapterUnregisterOEMColorService
ColorProfileAddDisplayAssociation
ColorProfileRemoveDisplayAssociation
ColorProfileSetDisplayDefaultAssociation
ColorProfileGetDisplayList
ColorProfileGetDisplayDefault
ColorProfileGetDisplayUserScope
