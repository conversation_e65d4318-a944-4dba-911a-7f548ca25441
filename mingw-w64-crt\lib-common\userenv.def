;
; Definition file of USERENV.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "USERENV.dll"
EXPORTS
RsopLoggingEnabled
AreThereVisibleLogoffScripts
AreThereVisibleShutdownScripts
CreateAppContainerProfile
CreateEnvironmentBlock
CreateProfile
DeleteAppContainerProfile
DeleteProfileA
DeleteProfileW
DeriveAppContainerSidFromAppContainerName
DestroyEnvironmentBlock
DllGetContractDescription
EnterCriticalPolicySection
ExpandEnvironmentStringsForUserA
ExpandEnvironmentStringsForUserW
ForceSyncFgPolicy
FreeGPOListA
FreeGPOListW
GenerateRsopPolicy
GenerateGPNotification
GetAllUsersProfileDirectoryA
GetAllUsersProfileDirectoryW
GetAppContainerFolderPath
GetAppContainerRegistryLocation
GetAppliedGPOListA
GetAppliedGPOListW
GetDefaultU<PERSON><PERSON>ro<PERSON>DirectoryA
GetDefaultUserProfileDirectoryW
GetGPOListA
GetGPOListW
GetNextFgPolicyRefreshInfo
GetPreviousFgPolicyRefreshInfo
GetProfileType
GetProfilesDirectoryA
GetProfilesDirectoryW
GetUserProfileDirectoryA
GetUserProfileDirectoryW
HasPolicyForegroundProcessingCompleted
LeaveCriticalPolicySection
LoadUserProfileA
LoadUserProfileW
ProcessGroupPolicyCompleted
ProcessGroupPolicyCompletedEx
RefreshPolicy
RefreshPolicyEx
RegisterGPNotification
RsopAccessCheckByType
RsopFileAccessCheck
RsopResetPolicySettingStatus
RsopSetPolicySettingStatus
UnloadUserProfile
UnregisterGPNotification
WaitForMachinePolicyForegroundProcessing
WaitForUserPolicyForegroundProcessing
