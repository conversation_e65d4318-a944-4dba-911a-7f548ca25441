;
; Definition file of ncrypt.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "ncrypt.dll"
EXPORTS
BCryptAddContextFunction
BCryptAddContextFunctionProvider
BCryptCloseAlgorithmProvider
BCryptConfigureContext
BCryptConfigureContextFunction
BCryptCreateContext
BCryptCreateHash
BCryptDecrypt
BCryptDeleteContext
BCryptDeriveKey
BCryptDeriveKeyCapi
BCryptDeriveKeyPBKDF2
BCryptDestroyHash
BCryptDestroyKey
BCryptDestroySecret
BCryptDuplicateHash
BCryptDuplicateKey
BCryptEncrypt
BCryptEnumAlgorithms
BCryptEnumContextFunctionProviders
BCryptEnumContextFunctions
BCryptEnumContexts
BCryptEnumProviders
BCryptEnumRegisteredProviders
BCryptExportKey
BCryptFinalizeKeyPair
BCryptFini<PERSON>Hash
BCryptFreeBuffer
BCryptGenRandom
BCryptGenerateKeyPair
BCryptGenerateSymmetricKey
BCryptGetFipsAlgorithmMode
BCryptGetProperty
BCryptHash
BCryptHashData
BCryptImportKey
BCryptImportKeyPair
BCryptKeyDerivation
BCryptOpenAlgorithmProvider
BCryptQueryContextConfiguration
BCryptQueryContextFunctionConfiguration
BCryptQueryContextFunctionProperty
BCryptQueryProviderRegistration
BCryptRegisterConfigChangeNotify
BCryptRegisterProvider
BCryptRemoveContextFunction
BCryptRemoveContextFunctionProvider
BCryptResolveProviders
BCryptSecretAgreement
BCryptSetAuditingInterface
BCryptSetContextFunctionProperty
BCryptSetProperty
BCryptSignHash
BCryptUnregisterConfigChangeNotify
BCryptUnregisterProvider
BCryptVerifySignature
GetIsolationServerInterface
GetKeyStorageInterface
GetSChannelInterface
NCryptCloseKeyProtector
NCryptCloseProtectionDescriptor
NCryptCreateClaim
NCryptCreatePersistedKey
NCryptCreateProtectionDescriptor
NCryptDecrypt
NCryptDeleteKey
NCryptDeriveKey
NCryptDuplicateKeyProtectorHandle
NCryptEncrypt
NCryptEnumAlgorithms
NCryptEnumKeys
NCryptEnumStorageProviders
NCryptExportKey
NCryptFinalizeKey
NCryptFreeBuffer
NCryptFreeObject
NCryptGetProperty
NCryptGetProtectionDescriptorInfo
NCryptImportKey
NCryptIsAlgSupported
NCryptIsKeyHandle
NCryptKeyDerivation
NCryptNotifyChangeKey
NCryptOpenKey
NCryptOpenKeyProtector
NCryptOpenStorageProvider
NCryptProtectKey
NCryptProtectSecret
NCryptQueryProtectionDescriptorName
NCryptRegisterProtectionDescriptorName
NCryptSecretAgreement
NCryptSetAuditingInterface
NCryptSetProperty
NCryptSignHash
NCryptStreamClose
NCryptStreamOpenToProtect
NCryptStreamOpenToUnprotect
NCryptStreamOpenToUnprotectEx
NCryptStreamUpdate
NCryptTranslateHandle
NCryptUnprotectKey
NCryptUnprotectSecret
NCryptVerifyClaim
NCryptVerifySignature
SslChangeNotify
SslComputeClientAuthHash
SslComputeEapKeyBlock
SslComputeFinishedHash
SslComputeSessionHash
SslCreateClientAuthHash
SslCreateEphemeralKey
SslCreateHandshakeHash
SslDecrementProviderReferenceCount
SslDecryptPacket
SslDuplicateTranscriptHash
SslEncryptPacket
SslEnumCipherSuites
SslEnumCipherSuitesEx
SslEnumEccCurves
SslEnumProtocolProviders
SslExpandBinderKey
SslExpandExporterMasterKey
SslExpandNextGenTrafficKey
SslExpandPreSharedKey
SslExpandResumptionMasterKey
SslExpandTrafficKeys
SslExpandWriteKey
SslExportKey
SslExportKeyingMaterial
SslExtractEarlyKey
SslExtractHandshakeKey
SslExtractMasterKey
SslFreeBuffer
SslFreeObject
SslGenerateMasterKey
SslGeneratePreMasterKey
SslGenerateSessionKeys
SslGetCipherSuitePRFHashAlgorithm
SslGetKeyProperty
SslGetProviderProperty
SslHashHandshake
SslImportKey
SslImportMasterKey
SslIncrementProviderReferenceCount
SslLookupCipherLengths
SslLookupCipherSuiteInfo
SslOpenPrivateKey
SslOpenProvider
SslSignHash
SslVerifySignature
