;
; Definition file of w32time.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "w32time.dll"
EXPORTS
fnW32TmI_ScSetServiceBits DATA
fnW32TmRegisterServiceCtrlHandlerEx DATA
fnW32TmSetServiceStatus DATA
SvchostEntry_W32Time
SvchostPushServiceGlobals
TimeProvClose
TimeProvCommand
TimeProvOpen
W32TimeBufferFree
W32TimeDcPromo
W32TimeDeleteConfig
W32TimeGetNetlogonServiceBits
W32TimeLog
W32TimeQueryConfig
W32TimeQueryConfiguration
W32TimeQueryHardwareProviderStatus
W32TimeQueryNTPProviderStatus
W32TimeQueryNtpProviderConfiguration
W32TimeQuerySource
W32TimeQueryStatus
W32TimeSetConfig
W32TimeSyncNow
W32TimeVerifyJoinConfig
W32TimeVeri<PERSON>UnjoinConfig
W32TmServiceMain
