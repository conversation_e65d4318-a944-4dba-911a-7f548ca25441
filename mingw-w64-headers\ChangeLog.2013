2013-12-13  <PERSON> <<EMAIL>>

	* include/ntsecapi.h: Add definition of LSA_LAST_INTER_LOGON_INFO
	and some missing members in SECURITY_LOGON_SESSION_DATA. 

2013-07-09  <PERSON> <<EMAIL>>

	* include/wincon.h: Replace BOOL by WINBOOL in
	struct _CONSOLE_SCREEN_BUFFER_INFOEX.

2013-04-05  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* configure.ac (BASEHEAD_LIST): Add crt/intrin.h.
	* configure: Regenerate.

2013-03-01  <PERSON> <<EMAIL>>

	* include/usp10.h: Updated SCRIPT_CONTROL struct.

2013-01-30  <PERSON>  <frank<PERSON>@computer.org>

	* include/winsvc.h (SERVICE_CONFIG_DELAYED_AUTO_START_INFO): New define
	for _WIN32_WINNT >= 0x0600.  The related struct is already present.
	(SERVICE_CONFIG_FAILURE_ACTIONS_FLAG): Likewise.
	(SERVICE_CONFIG_SERVICE_SID_INFO): Likewise.
	(SERVICE_CONFIG_REQUIRED_PRIVILEGES_INFO): Likewise.
	(SERVICE_CONFIG_PRESHUTDOWN_INFO): Likewise.
