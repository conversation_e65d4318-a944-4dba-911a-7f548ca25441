LIBRARY "dxgi.dll"
EXPORTS
ApplyCompatResolutionQuirking
CompatString
CompatValue
D3DKMTCloseAdapter
D3DKMTDestroyAllocation
D3DKMTDestroyContext
D3DKMTDestroyDevice
D3DKMTDestroySynchronizationObject
D3DKMTQueryAdapterInfo
D3DKMTSetDisplayPrivateDriverFormat
D3DKMTSignalSynchronizationObject
D3DKMTUnlock
D3DKMTWaitForSynchronizationObject
DXGIDumpJournal
PIXBeginCapture
PIXEndCapture
PIXGetCaptureState
DXGIRevertToSxS
OpenAdapter10
OpenAdapter10_2
SetAppCompatStringPointer
UpdateHMDEmulationStatus
CreateDXGIFactory
CreateDXGIFactory1
CreateDXGIFactory2
D3DKMTCreateAllocation
D3DKMTCreateContext
D3DKMTCreateDevice
D3DKMTCreateSynchronizationObject
D3DKMTEscape
D3DKMTGetContextSchedulingPriority
D3DKMTGetDeviceState
D3DKMTGetDisplayModeList
D3DKMTGetMultisampleMethodList
D3DKMTGetRuntimeData
D3DKMTGetSharedPrimaryHandle
D3DKMTLock
D3DKMTOpenAdapterFromHdc
D3DKMTOpenResource
D3DKMTPresent
D3DKMTQueryAllocationResidency
D3DKMTQueryResourceInfo
D3DKMTRender
D3DKMTSetAllocationPriority
D3DKMTSetContextSchedulingPriority
D3DKMTSetDisplayMode
D3DKMTSetGammaRamp
D3DKMTSetVidPnSourceOwner
D3DKMTWaitForVerticalBlankEvent
DXGID3D10CreateDevice
DXGID3D10CreateLayeredDevice
DXGID3D10ETWRundown
DXGID3D10GetLayeredDeviceSize
DXGID3D10RegisterLayers
DXGIDeclareAdapterRemovalSupport
DXGIGetDebugInterface1
DXGIReportAdapterConfiguration
