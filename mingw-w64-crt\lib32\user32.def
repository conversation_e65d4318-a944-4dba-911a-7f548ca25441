LIBRARY USER32.dll
EXPORTS
GetPointerFrameArrivalTimes@12
Wow64Transition DATA
ActivateKeyboardLayout@8
AddClipboardFormatListener@4
AddVisualIdentifier@8
AdjustWindowRect@12
AdjustWindowRectEx@16
AdjustWindowRectExForDpi@20
AlignRects@16
AllowForegroundActivation@0
AllowSetForegroundWindow@4
AnimateWindow@12
AnyPopup@0
AppendMenuA@16
AppendMenuW@16
AreDpiAwarenessContextsEqual@8
ArrangeIconicWindows@4
AttachThreadInput@12
BeginDeferWindowPos@4
BeginPaint@8
BlockInput@4
BringWindowToTop@4
BroadcastSystemMessage@20
BroadcastSystemMessageA@20
BroadcastSystemMessageExA@24
BroadcastSystemMessageExW@24
BroadcastSystemMessageW@20
BuildReasonArray@12
CalcChild<PERSON>croll@8
CalcMenuBar@20
CalculatePopupWindowPosition@20
CallMsgFilter@8
CallMsgFilterA@8
CallMsgFilterW@8
CallNextHookEx@16
CallWindowProcA@20
CallWindowProcW@20
CancelShutdown@0
CascadeChildWindows@8
CascadeWindows@20
ChangeClipboardChain@8
ChangeDisplaySettingsA@8
ChangeDisplaySettingsExA@20
ChangeDisplaySettingsExW@20
ChangeDisplaySettingsW@8
ChangeMenuA@20
ChangeMenuW@20
ChangeWindowMessageFilter@8
DwmGetDxRgn@12
ChangeWindowMessageFilterEx@16
CharLowerA@4
CharLowerBuffA@8
CharLowerBuffW@8
CharLowerW@4
CharNextA@4
CharNextExA@12
CharNextW@4
CharPrevA@8
CharPrevExA@16
CharPrevW@8
CharToOemA@8
CharToOemBuffA@12
CharToOemBuffW@12
CharToOemW@8
CharUpperA@4
CharUpperBuffA@8
CharUpperBuffW@8
CharUpperW@4
CheckDesktopByThreadId@4
CheckBannedOneCoreTransformApi@4
CheckDBCSEnabledExt@0
CheckDlgButton@12
CheckMenuItem@12
CheckMenuRadioItem@20
CheckProcessForClipboardAccess@8
CheckProcessSession@4
CheckRadioButton@16
CheckWindowThreadDesktop@8
ChildWindowFromPoint@12
ChildWindowFromPointEx@16
CliImmSetHotKey@16
ClientThreadSetup@0
ClientToScreen@8
ClipCursor@4
CloseClipboard@0
CloseDesktop@4
CloseGestureInfoHandle@4
CloseTouchInputHandle@4
CloseWindow@4
CloseWindowStation@4
ConsoleControl@12
ControlMagnification@8
CopyAcceleratorTableA@12
CopyAcceleratorTableW@12
CopyIcon@4
CopyImage@20
CopyRect@8
CountClipboardFormats@0
CreateAcceleratorTableA@8
CreateAcceleratorTableW@8
CreateCaret@16
CreateCursor@28
CreateDCompositionHwndTarget@12
CreateDesktopA@24
CreateDesktopExA@32
CreateDesktopExW@32
CreateDesktopW@24
CreateDialogIndirectParamA@20
CreateDialogIndirectParamAorW@24
CreateDialogIndirectParamW@20
CreateDialogParamA@20
CreateDialogParamW@20
CreateIcon@28
CreateIconFromResource@16
CreateIconFromResourceEx@28
CreateIconIndirect@4
CreateMDIWindowA@40
CreateMDIWindowW@40
CreateMenu@0
CreatePopupMenu@0
CreateSyntheticPointerDevice@12
CreateSystemThreads@16 ; ReactOS has the @8 variant
CreateWindowExA@48
CreateWindowExW@48
CreateWindowInBand@52
CreateWindowInBandEx@56
CreateWindowIndirect@4
CreateWindowStationA@16
CreateWindowStationW@16
CsrBroadcastSystemMessageExW@24
CtxInitUser32@0
DdeAbandonTransaction@12
DdeAccessData@8
DdeAddData@16
DdeClientTransaction@32
DdeCmpStringHandles@8
DdeConnect@16
DdeConnectList@20
DdeCreateDataHandle@28
DdeCreateStringHandleA@12
DdeCreateStringHandleW@12
DdeDisconnect@4
DdeDisconnectList@4
DdeEnableCallback@12
DdeFreeDataHandle@4
DdeFreeStringHandle@8
DdeGetData@16
DdeGetLastError@4
DdeGetQualityOfService@12
DdeImpersonateClient@4
DdeInitializeA@16
DdeInitializeW@16
DdeKeepStringHandle@8
DdeNameService@16
DdePostAdvise@12
DdeQueryConvInfo@12
DdeQueryNextServer@8
DdeQueryStringA@20
DdeQueryStringW@20
DdeReconnect@4
DdeSetQualityOfService@12
DdeSetUserHandle@12
DdeUnaccessData@4
DdeUninitialize@4
DefDlgProcA@16
DefDlgProcW@16
DefFrameProcA@20
DefFrameProcW@20
DefMDIChildProcA@16
DefMDIChildProcW@16
DefRawInputProc@12
DefWindowProcA@16
DefWindowProcW@16
DeferWindowPos@32
DeferWindowPosAndBand@36
DeleteMenu@12
DeregisterShellHookWindow@4
DestroyAcceleratorTable@4
DestroyCaret@0
DestroyCursor@4
DestroyDCompositionHwndTarget@8
DestroyIcon@4
DestroyMenu@4
DestroyReasons@4
DestroySyntheticPointerDevice@4
DestroyWindow@4
DeviceEventWorker@24 ; No documentation whatsoever, ReactOS has a stub with @20 - https://www.reactos.org/archives/public/ros-diffs/2011-February/040308.html
DialogBoxIndirectParamA@20
DialogBoxIndirectParamAorW@24
DialogBoxIndirectParamW@20
DialogBoxParamA@20
DialogBoxParamW@20
DisableProcessWindowsGhosting@0
DispatchMessageA@4
DispatchMessageW@4
DisplayConfigGetDeviceInfo@4
DisplayConfigSetDeviceInfo@4
DisplayExitWindowsWarnings@4
DlgDirListA@20
DlgDirListComboBoxA@20
DlgDirListComboBoxW@20
DlgDirListW@20
DlgDirSelectComboBoxExA@16
DlgDirSelectComboBoxExW@16
DlgDirSelectExA@16
DlgDirSelectExW@16
DoSoundConnect@0
DoSoundDisconnect@0
DragDetect@12
DragObject@20
DrawAnimatedRects@16
DrawCaption@16
DrawCaptionTempA@28
DrawCaptionTempW@28
DrawEdge@16
DrawFocusRect@8
DrawFrame@16
DrawFrameControl@16
DrawIcon@16
DrawIconEx@36
DrawMenuBar@4
DrawMenuBarTemp@20
DrawStateA@40
DrawStateW@40
DrawTextA@20
DrawTextExA@24
DrawTextExW@24
DrawTextW@20
DwmGetDxSharedSurface@24
DwmGetRemoteSessionOcclusionEvent@0
DwmGetRemoteSessionOcclusionState@0
DwmKernelShutdown@0
DwmKernelStartup@0
DwmLockScreenUpdates@4
DwmStartRedirection@8 ; Mentioned on http://habrahabr.ru/post/145174/ , enables GDI virtualization (for security purposes)
DwmStopRedirection@0
DwmValidateWindow@8
EditWndProc@20
EmptyClipboard@0
EnableMenuItem@12
EnableMouseInPointer@4
EnableNonClientDpiScaling@4
EnableOneCoreTransformMode@0
EnableScrollBar@12
EnableSessionForMMCSS@4
EnableWindow@8
EndDeferWindowPos@4
EndDeferWindowPosEx@8
EndDialog@8
EndMenu@0
EndPaint@8
EndTask@12
EnterReaderModeHelper@4
EnumChildWindows@12
EnumClipboardFormats@4
EnumDesktopWindows@12
EnumDesktopsA@12
EnumDesktopsW@12
EnumDisplayDevicesA@16
EnumDisplayDevicesW@16
EnumDisplayMonitors@16
EnumDisplaySettingsA@12
EnumDisplaySettingsExA@16
EnumDisplaySettingsExW@16
EnumDisplaySettingsW@12
EnumPropsA@8
EnumPropsExA@12
EnumPropsExW@12
EnumPropsW@8
EnumThreadWindows@12
EnumWindowStationsA@8
EnumWindowStationsW@8
EnumWindows@8
EqualRect@8
EvaluateProximityToPolygon@16
EvaluateProximityToRect@12
ExcludeUpdateRgn@8
ExitWindowsEx@8
FillRect@12
FindWindowA@8
FindWindowExA@16
FindWindowExW@16
FindWindowW@8
FlashWindow@8
FlashWindowEx@4
FrameRect@12
FreeDDElParam@8
FrostCrashedWindow@8
GetActiveWindow@0
GetAltTabInfo@20
GetAltTabInfoA@20
GetAltTabInfoW@20
GetAncestor@8
GetAppCompatFlags2@4
GetAppCompatFlags@8 ; ReactOS has @4 version http://doxygen.reactos.org/d9/d71/undocuser_8h_a9b76cdc68c523a061c86a40367049ed2.html
GetAsyncKeyState@4
GetAutoRotationState@4
GetAwarenessFromDpiAwarenessContext@4
GetCIMSSM@4
GetCapture@0
GetCaretBlinkTime@0
GetCaretPos@4
GetClassInfoA@12
GetClassInfoExA@12
GetClassInfoExW@12
GetClassInfoW@12
GetClassLongA@8
GetClassLongW@8
GetClassNameA@12
GetClassNameW@12
GetClassWord@8
GetClientRect@8
GetClipCursor@4
GetClipboardAccessToken@8
GetClipboardData@4
GetClipboardFormatNameA@12
GetClipboardFormatNameW@12
GetClipboardOwner@0
GetClipboardSequenceNumber@0
GetClipboardViewer@0
GetComboBoxInfo@8
GetCurrentInputMessageSource@4
GetCursor@0
GetCursorFrameInfo@20
GetCursorInfo@4
GetCursorPos@4
GetDC@4
GetDCEx@12
GetDesktopID@8
GetDesktopWindow@0
GetDialogBaseUnits@0
GetDialogControlDpiChangeBehavior@4
GetDialogDpiChangeBehavior@4
GetDisplayAutoRotationPreferences@4
GetDisplayConfigBufferSizes@12
GetDlgCtrlID@4
GetDlgItem@8
GetDlgItemInt@16
GetDlgItemTextA@16
GetDlgItemTextW@16
GetDoubleClickTime@0
GetDpiAwarenessContextForProcess@4
GetDpiForMonitorInternal@16
GetDpiForSystem@0
GetDpiForWindow@4
GetDpiFromDpiAwarenessContext@4
GetExtendedPointerDeviceProperty@8
GetFocus@0
GetForegroundWindow@0
GetGUIThreadInfo@8
GetGestureConfig@24
GetGestureExtraArgs@12
GetGestureInfo@8
GetGuiResources@8
GetIconInfo@8
GetIconInfoExA@8
GetIconInfoExW@8
GetInputDesktop@0
GetInputLocaleInfo@8
GetInputState@0
GetInternalWindowPos@12
GetKBCodePage@0
GetKeyNameTextA@12
GetKeyNameTextW@12
GetKeyState@4
GetKeyboardLayout@4
GetKeyboardLayoutList@8
GetKeyboardLayoutNameA@4
GetKeyboardLayoutNameW@4
GetKeyboardState@4
GetKeyboardType@4
GetLastActivePopup@4
GetLastInputInfo@4
GetLayeredWindowAttributes@16
GetListBoxInfo@4
GetMagnificationDesktopColorEffect@4
GetMagnificationDesktopMagnification@12
GetMagnificationDesktopSamplingMode@4
GetMagnificationLensCtxInformation@16
GetMenu@4
GetMenuBarInfo@16
GetMenuCheckMarkDimensions@0
GetMenuContextHelpId@4
GetMenuDefaultItem@12
GetMenuInfo@8
GetMenuItemCount@4
GetMenuItemID@8
GetMenuItemInfoA@16
GetMenuItemInfoW@16
GetMenuItemRect@16
GetMenuState@12
GetMenuStringA@20
GetMenuStringW@20
GetMessageA@16
GetMessageExtraInfo@0
GetMessagePos@0
GetMessageTime@0
GetMessageW@16
GetMonitorInfoA@8
GetMonitorInfoW@8
GetMouseMovePointsEx@20
GetNextDlgGroupItem@12
GetNextDlgTabItem@12
GetOpenClipboardWindow@0
GetParent@4
GetPhysicalCursorPos@4
GetPointerCursorId@8
GetPointerDevice@8
GetPointerDeviceCursors@12
GetPointerDeviceInputSpace@8
GetPointerDeviceOrientation@8
GetPointerDeviceProperties@12
GetPointerDeviceRects@12
GetPointerDevices@8
GetPointerFrameInfo@12
GetPointerFrameInfoHistory@16
GetPointerFramePenInfo@12
GetPointerFramePenInfoHistory@16
GetPointerFrameTimes@12
GetPointerFrameTouchInfo@12
GetPointerFrameTouchInfoHistory@16
GetPointerInfo@8
GetPointerInfoHistory@12
GetPointerInputTransform@12
GetPointerPenInfo@8
GetPointerPenInfoHistory@12
GetPointerTouchInfo@8
GetPointerTouchInfoHistory@12
GetPointerType@8
GetPriorityClipboardFormat@8
GetProcessDefaultLayout@4
GetProcessDpiAwarenessInternal@8
GetProcessWindowStation@0
GetProgmanWindow@0
GetPropA@8
GetPropW@8
GetQueueStatus@4
GetRawInputBuffer@12
GetRawInputData@20
GetRawInputDeviceInfoA@16
GetRawInputDeviceInfoW@16
GetRawInputDeviceList@12
GetRawPointerDeviceData@20
GetReasonTitleFromReasonCode@12
GetRegisteredRawInputDevices@12
GetScrollBarInfo@12
GetScrollInfo@12
GetScrollPos@8
GetScrollRange@16
GetSendMessageReceiver@4
GetShellChangeNotifyWindow@0
GetShellWindow@0
GetSubMenu@8
GetSysColor@4
GetSysColorBrush@4
GetSystemDpiForProcess@4
GetSystemMenu@8
GetSystemMetrics@4
GetSystemMetricsForDpi@8
GetTabbedTextExtentA@20
GetTabbedTextExtentW@20
GetTaskmanWindow@0
GetThreadDesktop@4
GetThreadDpiAwarenessContext@0
GetThreadDpiHostingBehavior@0
GetTitleBarInfo@8
GetTopLevelWindow@4
GetTopWindow@4
GetTouchInputInfo@16
GetUnpredictedMessagePos@0
GetUpdateRect@12
GetUpdateRgn@12
GetUpdatedClipboardFormats@12
GetUserObjectInformationA@20
GetUserObjectInformationW@20
GetUserObjectSecurity@20
GetWinStationInfo@4
GetWindow@8
GetWindowBand@8
GetWindowCompositionAttribute@8
GetWindowCompositionInfo@8
GetWindowContextHelpId@4
GetWindowDC@4
GetWindowDisplayAffinity@8
GetWindowDpiAwarenessContext@4
GetWindowDpiHostingBehavior@4
GetWindowFeedbackSetting@20
GetWindowInfo@8
GetWindowLongA@8
GetWindowLongW@8
GetWindowMinimizeRect@8
GetWindowModuleFileName@12
GetWindowModuleFileNameA@12
GetWindowModuleFileNameW@12
GetWindowPlacement@8
GetWindowProcessHandle@8
GetWindowRect@8
GetWindowRgn@8
GetWindowRgnBox@8
GetWindowRgnEx@12
GetWindowTextA@12
GetWindowTextLengthA@4
GetWindowTextLengthW@4
GetWindowTextW@12
GetWindowThreadProcessId@8
GetWindowWord@8
GhostWindowFromHungWindow@4
GrayStringA@36
GrayStringW@36
HideCaret@4
HiliteMenuItem@16
HungWindowFromGhostWindow@4
IMPGetIMEA@8
IMPGetIMEW@8
IMPQueryIMEA@4
IMPQueryIMEW@4
IMPSetIMEA@8
IMPSetIMEW@8
ImpersonateDdeClientWindow@8
InSendMessage@0
InSendMessageEx@4
InflateRect@12
InheritWindowMonitor@8
InitDManipHook@0
InitializeGenericHidInjection@8
InitializeInputDeviceInjection@28
InitializeLpkHooks@4
InitializeWin32EntryTable@4
InitializePointerDeviceInjection@20
InitializePointerDeviceInjectionEx@24
InitializeTouchInjection@8
InjectDeviceInput@12
InjectGenericHidInput@12
InjectKeyboardInput@8
InjectMouseInput@8
InjectPointerInput@12
InjectSyntheticPointerInput@12
InjectTouchInput@8
InputSpaceRegionFromPoint@20
InsertMenuA@20
InsertMenuItemA@16
InsertMenuItemW@16
InsertMenuW@20
InternalGetWindowIcon@8
InternalGetWindowText@12
IntersectRect@12
InvalidateRect@12
InvalidateRgn@12
InvertRect@8
IsCharAlphaA@4
IsCharAlphaNumericA@4
IsCharAlphaNumericW@4
IsCharAlphaW@4
IsCharLowerA@4
IsCharLowerW@4
IsCharUpperA@4
IsCharUpperW@4
IsChild@8
IsClipboardFormatAvailable@4
IsDialogMessage@8
IsDialogMessageA@8
IsDialogMessageW@8
IsDlgButtonChecked@8
IsGUIThread@4
IsHungAppWindow@4
IsIconic@4
IsImmersiveProcess@4
IsInDesktopWindowBand@4
IsMenu@4
IsProcess16Bit@0
IsMouseInPointerEnabled@0
IsOneCoreTransformMode@0
IsProcessDPIAware@0
IsQueueAttached@0
IsRectEmpty@4
IsSETEnabled@0
IsServerSideWindow@4
IsThreadDesktopComposited@0
IsThreadTSFEventAware@4
IsTopLevelWindow@4
IsTouchWindow@8
IsValidDpiAwarenessContext@4
IsWinEventHookInstalled@4
IsWindow@4
IsWindowArranged@4
IsWindowEnabled@4
IsWindowInDestroy@4
IsWindowRedirectedForPrint@4
IsWindowUnicode@4
IsWindowVisible@4
IsWow64Message@0
IsZoomed@4
KillSystemTimer@8
KillTimer@8
LoadAcceleratorsA@8
LoadAcceleratorsW@8
LoadBitmapA@8
LoadBitmapW@8
LoadCursorA@8
LoadCursorFromFileA@4
LoadCursorFromFileW@4
LoadCursorW@8
LoadIconA@8
LoadIconW@8
LoadImageA@24
LoadImageW@24
LoadKeyboardLayoutA@8
LoadKeyboardLayoutEx@12
LoadKeyboardLayoutW@8
LoadLocalFonts@0
LoadMenuA@8
LoadMenuIndirectA@4
LoadMenuIndirectW@4
LoadMenuW@8
LoadRemoteFonts@0
LoadStringA@16
LoadStringW@16
LockSetForegroundWindow@4
LockWindowStation@4
LockWindowUpdate@4
LockWorkStation@0
LogicalToPhysicalPoint@8
LogicalToPhysicalPointForPerMonitorDPI@8
LookupIconIdFromDirectory@8
LookupIconIdFromDirectoryEx@20
MBToWCSEx@24
MBToWCSExt@20
MB_GetString@4
MITGetCursorUpdateHandle@0
MITSetForegroundRoutingInfo@8
MITSetInputDelegationMode@8
MITSetLastInputRecipient@4
MITSynthesizeTouchInput@4
MakeThreadTSFEventAware@4
MapDialogRect@8
MapPointsByVisualIdentifier@20
MapVirtualKeyA@8
MapVirtualKeyExA@12
MapVirtualKeyExW@12
MapVirtualKeyW@8
MapVisualRelativePoints@28
MapWindowPoints@16
MenuItemFromPoint@16
MenuWindowProcA@20
MenuWindowProcW@20
MessageBeep@4
MessageBoxA@16
MessageBoxExA@20
MessageBoxExW@20
MessageBoxIndirectA@4
MessageBoxIndirectW@4
MessageBoxTimeoutA@24
MessageBoxTimeoutW@24
MessageBoxW@16
ModifyMenuA@20
ModifyMenuW@20
MonitorFromPoint@12
MonitorFromRect@8
MonitorFromWindow@8
MoveWindow@24
MsgWaitForMultipleObjects@20
MsgWaitForMultipleObjectsEx@20
NotifyOverlayWindow@8
NotifyWinEvent@16
OemKeyScan@4
OemToCharA@8
OemToCharBuffA@12
OemToCharBuffW@12
OemToCharW@8
OffsetRect@12
OpenClipboard@4
OpenDesktopA@16
OpenDesktopW@16
OpenIcon@4
OpenInputDesktop@12
OpenThreadDesktop@16
OpenWindowStationA@12
OpenWindowStationW@12
PackDDElParam@12
PackTouchHitTestingProximityEvaluation@8
PaintDesktop@4
PaintMenuBar@24
PaintMonitor@12
PeekMessageA@20
PeekMessageW@20
PhysicalToLogicalPoint@8
PhysicalToLogicalPointForPerMonitorDPI@8
PostMessageA@16
PostMessageW@16
PostQuitMessage@4
PostThreadMessageA@16
PostThreadMessageW@16
PrintWindow@12
PrivateExtractIconExA@20
PrivateExtractIconExW@20
PrivateExtractIconsA@32
PrivateExtractIconsW@32
PrivateSetDbgTag@8
PrivateSetRipFlags@8
PrivateRegisterICSProc@4
PtInRect@12
QueryBSDRWindow@0
QueryDisplayConfig@24
QuerySendMessage@4
QueryUserCounters@20
RIMAddInputObserver@32
RIMAreSiblingDevices@12
RIMDeviceIoControl@36
RIMEnableMonitorMappingForDevice@12
RIMFreeInputBuffer@8
RIMGetDevicePreparsedData@16
RIMGetDevicePreparsedDataLockfree@12
RIMGetDeviceProperties@12
RIMGetDevicePropertiesLockfree@8
RIMGetPhysicalDeviceRect@12
RIMGetSourceProcessId@12
RIMObserveNextInput@4
RIMOnPnpNotification@4
RIMOnTimerNotification@8
RIMQueryDevicePath@8
RIMReadInput@28
RIMRegisterForInput@40
RIMRemoveInputObserver@4
RIMSetExtendedDeviceProperty@12
RIMSetTestModeStatus@4
RIMUnregisterForInput@4
RIMUpdateInputObserverRegistration@16
RealChildWindowFromPoint@12
RealGetWindowClass@12
RealGetWindowClassA@12
RealGetWindowClassW@12
ReasonCodeNeedsBugID@4
ReasonCodeNeedsComment@4
RecordShutdownReason@4
RedrawWindow@16
RegisterBSDRWindow@8
RegisterClassA@4
RegisterClassExA@4
RegisterClassExW@4
RegisterClassW@4
RegisterClipboardFormatA@4
RegisterClipboardFormatW@4
RegisterDManipHook@0
RegisterDeviceNotificationA@12
RegisterDeviceNotificationW@12
RegisterErrorReportingDialog@8
RegisterFrostWindow@8
RegisterGhostWindow@8
RegisterHotKey@16
RegisterLogonProcess@8
RegisterMessagePumpHook@4
RegisterPointerDeviceNotifications@8
RegisterPointerInputTarget@8
RegisterPointerInputTargetEx@12
RegisterPowerSettingNotification@12
RegisterRawInputDevices@12
RegisterServicesProcess@4
RegisterSessionPort@4 ; Undocumented, rumored to be related to ALPC - http://blogs.msdn.com/b/ntdebugging/archive/2007/07/26/lpc-local-procedure-calls-part-1-architecture.aspx
RegisterShellHookWindow@4
RegisterSuspendResumeNotification@8
RegisterSystemThread@8
RegisterTasklist@4
RegisterTouchHitTestingWindow@8
RegisterTouchWindow@8
RegisterUserApiHook@4 ; Prototype changed in 2003 - https://www.reactos.org/wiki/Techwiki:RegisterUserApiHook
RegisterWindowMessageA@4
RegisterWindowMessageW@4
ReleaseCapture@0
ReleaseDC@8
ReleaseDwmHitTestWaiters@0
RemoveClipboardFormatListener@4
RemoveInjectionDevice@4
RemoveMenu@12
RemovePropA@8
RemovePropW@8
RemoveThreadTSFEventAwareness@4
RemoveVisualIdentifier@4
ReplyMessage@4
ResolveDesktopForWOW@4
ReuseDDElParam@20
ScreenToClient@8
ScrollChildren@12
ScrollDC@28
ScrollWindow@20
ScrollWindowEx@32
SendDlgItemMessageA@20
SendDlgItemMessageW@20
SendIMEMessageExA@8
SendIMEMessageExW@8
SendInput@12
SendMessageA@16
SendMessageCallbackA@24
SendMessageCallbackW@24
SendMessageTimeoutA@28
SendMessageTimeoutW@28
SendMessageW@16
SendNotifyMessageA@16
SendNotifyMessageW@16
SetActiveWindow@4
SetCapture@4
SetCaretBlinkTime@4
SetCaretPos@8
SetClassLongA@12
SetClassLongW@12
SetClassWord@12
SetClipboardData@8
SetClipboardViewer@4
SetConsoleReserveKeys@8
SetCoalescableTimer@20
SetCursor@4
SetCursorContents@8
SetCursorPos@8
SetDebugErrorLevel@4
SetDeskWallpaper@4
SetDesktopColorTransform@4
SetDialogControlDpiChangeBehavior@12
SetDialogDpiChangeBehavior@12
SetDisplayAutoRotationPreferences@4
SetDisplayConfig@20
SetDlgItemInt@16
SetDlgItemTextA@12
SetDlgItemTextW@12
SetDoubleClickTime@4
SetFeatureReportResponse@12
SetFocus@4
SetForegroundWindow@4
SetFullscreenMagnifierOffsetsDWMUpdated@12
SetGestureConfig@20
SetImmersiveBackgroundWindow@4
SetInternalWindowPos@16
SetKeyboardState@4
SetLastErrorEx@8
SetLayeredWindowAttributes@16
SetLogonNotifyWindow@4
SetMagnificationDesktopColorEffect@4
SetMagnificationDesktopMagnification@16
SetMagnificationDesktopMagnifierOffsetsDWMUpdated@4
SetMagnificationDesktopSamplingMode@4
SetMagnificationLensCtxInformation@16
SetMenu@8
SetMenuContextHelpId@8
SetMenuDefaultItem@12
SetMenuInfo@8
SetMenuItemBitmaps@20
SetMenuItemInfoA@16
SetMenuItemInfoW@16
SetMessageExtraInfo@4
SetMessageQueue@4
SetMirrorRendering@8
SetParent@8
SetPhysicalCursorPos@8
SetPointerDeviceInputSpace@12
SetProcessDPIAware@0
SetProcessDefaultLayout@4
SetProcessDpiAwarenessContext@4
SetProcessDpiAwarenessInternal@4
SetProcessRestrictionExemption@4
SetProcessWindowStation@4
SetProgmanWindow@4
SetPropA@12
SetPropW@12
SetRect@20
SetRectEmpty@4
SetScrollInfo@16
SetScrollPos@16
SetScrollRange@20
SetShellChangeNotifyWindow@4
SetShellWindow@4
SetShellWindowEx@8
SetSysColors@12
SetSysColorsTemp@12
SetSystemCursor@8
SetSystemMenu@8
SetSystemTimer@16
SetTaskmanWindow@4
SetThreadDesktop@4
SetThreadDpiAwarenessContext@4
SetThreadDpiHostingBehavior@4
SetThreadInputBlocked@8
SetTimer@16
SetUserObjectInformationA@16
SetUserObjectInformationW@16
SetUserObjectSecurity@12
SetWinEventHook@28
SetWindowBand@12
SetWindowCompositionAttribute@8
SetWindowCompositionTransition@28
SetWindowContextHelpId@8
SetWindowDisplayAffinity@8
SetWindowFeedbackSetting@20
SetWindowLongA@12
SetWindowLongW@12
SetWindowPlacement@8
SetWindowPos@28
SetWindowRgn@12
SetWindowRgnEx@12
SetWindowStationUser@16
SetWindowTextA@8
SetWindowTextW@8
SetWindowWord@12
SetWindowsHookA@8
SetWindowsHookExA@16
SetWindowsHookExAW@20
SetWindowsHookExW@16
SetWindowsHookW@8
SfmDxBindSwapChain@12
SfmDxGetSwapChainStats@8
SfmDxOpenSwapChain@16
SfmDxQuerySwapChainBindingStatus@12
SfmDxReleaseSwapChain@8
SfmDxReportPendingBindingsToDwm@0
SfmDxSetSwapChainBindingStatus@8
SfmDxSetSwapChainStats@8
ShowCaret@4
ShowCursor@4
ShowOwnedPopups@8
ShowScrollBar@12
ShowStartGlass@4
ShowSystemCursor@4
ShowWindow@8
ShowWindowAsync@8
ShutdownBlockReasonCreate@8
ShutdownBlockReasonDestroy@4
ShutdownBlockReasonQuery@12
SignalRedirectionStartComplete@0
SkipPointerFrameMessages@4
SoftModalMessageBox@4
SoundSentry@0
SubtractRect@12
SwapMouseButton@4
SwitchDesktop@4
SwitchDesktopWithFade@12 ; Same as SwithDesktop(), only with fade (done at log-in), only usable by winlogon - http://blog.airesoft.co.uk/2010/08/things-microsoft-can-do-that-you-cant/
SwitchToThisWindow@8
SystemParametersInfoA@16
SystemParametersInfoForDpi@20
SystemParametersInfoW@16
TabbedTextOutA@32
TabbedTextOutW@32
TileChildWindows@8
TileWindows@20
ToAscii@20
ToAsciiEx@24
ToUnicode@24
ToUnicodeEx@28
TrackMouseEvent@4
TrackPopupMenu@28
TrackPopupMenuEx@24
TranslateAccelerator@12
TranslateAcceleratorA@12
TranslateAcceleratorW@12
TranslateMDISysAccel@8
TranslateMessage@4
TranslateMessageEx@8
UnhookWinEvent@4
UnhookWindowsHook@8
UnhookWindowsHookEx@4
UnionRect@12
UnloadKeyboardLayout@4
UnlockWindowStation@4
UnpackDDElParam@16
UnregisterClassA@8
UnregisterClassW@8
UnregisterDeviceNotification@4
UnregisterHotKey@8
UnregisterMessagePumpHook@0
UnregisterPointerInputTarget@8
UnregisterPointerInputTargetEx@8
UnregisterPowerSettingNotification@4
UnregisterSessionPort@0
UnregisterSuspendResumeNotification@4
UnregisterTouchWindow@4
UnregisterUserApiHook@0
UpdateDefaultDesktopThumbnail@20
UpdateLayeredWindow@36
UpdateLayeredWindowIndirect@8
UpdatePerUserSystemParameters@4 ; Undocumented, seems to apply certain registry settings to desktop, etc. ReactOS has @8 version - http://doxygen.reactos.org/d0/d92/win32ss_2user_2user32_2misc_2misc_8c_a1ff565f0af6bac6dce604f9f4473fe79.html ;  @4 is rumored to be without the first DWORD
UpdateWindow@4
UpdateWindowInputSinkHints@8
UpdateWindowTransform@12
User32InitializeImmEntryTable@4
UserClientDllInitialize@12
UserHandleGrantAccess@12
UserLpkPSMTextOut@24
UserLpkTabbedTextOut@48
UserRealizePalette@4
UserRegisterWowHandlers@8
VRipOutput@0
VTagOutput@0
ValidateRect@8
ValidateRgn@8
VkKeyScanA@4
VkKeyScanExA@8
VkKeyScanExW@8
VkKeyScanW@4
WCSToMBEx@24
WINNLSEnableIME@8
WINNLSGetEnableStatus@4
WINNLSGetIMEHotkey@4
WaitForInputIdle@8
WaitForRedirectionStartComplete@0
WaitMessage@0
Win32PoolAllocationStats@24
WinHelpA@16
WinHelpW@16
WindowFromDC@4
WindowFromPhysicalPoint@8
WindowFromPoint@8
_UserTestTokenForInteractive@8
gSharedInfo DATA
gapfnScSendMessage DATA
keybd_event@16
mouse_event@20
wsprintfA
wsprintfW
wvsprintfA@12
wvsprintfW@12
DelegateInput@24
UndelegateInput@8
HandleDelegatedInput@8
GetProcessUIContextInformation@8
IsThreadMessageQueueAttached@4
ReportInertia@20
SetCoreWindow@8
