<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: src/m_ms.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>src/m_ms.c File Reference</h1><code>#include &lt;stdio.h&gt;</code><br/>
<code>#include &lt;stdlib.h&gt;</code><br/>
<code>#include &lt;malloc.h&gt;</code><br/>
<code>#include &lt;string.h&gt;</code><br/>
<code>#include &lt;inttypes.h&gt;</code><br/>
<code>#include &lt;stdint.h&gt;</code><br/>
<code>#include &quot;<a class="el" href="m__token_8h_source.html">m_token.h</a>&quot;</code><br/>
<code>#include &quot;<a class="el" href="m__ms_8h_source.html">m_ms.h</a>&quot;</code><br/>
<table border="0" cellpadding="0" cellspacing="0">
<tr><td colspan="2"><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" align="right" valign="top"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8c.html#a53be44f77ef7b80bfc16250da927a99e">libmangle_decode_ms_name</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, const char *name)</td></tr>
<tr><td class="memItemLeft" align="right" valign="top">char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="m__ms_8c.html#a0872a8e6f16a49ccfc3e8663ed003354">libmangle_encode_ms_name</a> (<a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *gc, <a class="el" href="unionu_m_token.html">uMToken</a> *tok)</td></tr>
</table>
<hr/><h2>Function Documentation</h2>
<a class="anchor" id="a53be44f77ef7b80bfc16250da927a99e"></a><!-- doxytag: member="m_ms.c::libmangle_decode_ms_name" ref="a53be44f77ef7b80bfc16250da927a99e" args="(libmangle_gc_context_t *gc, const char *name)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="unionu_m_token.html">uMToken</a>* libmangle_decode_ms_name </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>name</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">
<p>Decodes an MSVC export name. </p>
<dl><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>gc</em>&nbsp;</td><td><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> pointer for collecting memory allocations. </td></tr>
    <tr><td valign="top"><tt>[in]</tt>&nbsp;</td><td valign="top"><em>name</em>&nbsp;</td><td>MSVC C++ mangled export string. </td></tr>
  </table>
  </dd>
</dl>
<dl class="see"><dt><b>See also:</b></dt><dd><a class="el" href="libmangle_8h.html#abf4af1b2e483a32beb147474853e696b">libmangle_sprint_decl()</a> </dd>
<dd>
<a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> </dd>
<dd>
<a class="el" href="libmangle_8h.html#a7c9c7d368eb1f52cac14457766a01cc7">libmangle_tokens_t</a> </dd></dl>
<dl class="return"><dt><b>Returns:</b></dt><dd>Token containing information about the mangled string, use <a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle_release_gc()</a> to free after use. </dd></dl>

</div>
</div>
<a class="anchor" id="a0872a8e6f16a49ccfc3e8663ed003354"></a><!-- doxytag: member="m_ms.c::libmangle_encode_ms_name" ref="a0872a8e6f16a49ccfc3e8663ed003354" args="(libmangle_gc_context_t *gc, uMToken *tok)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* libmangle_encode_ms_name </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlibmangle__gc__context__t.html">libmangle_gc_context_t</a> *&nbsp;</td>
          <td class="paramname"> <em>gc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionu_m_token.html">uMToken</a> *&nbsp;</td>
          <td class="paramname"> <em>tok</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

</div>
</div>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
