<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
      <li class="current"><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Defines</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="#index_c"><span>c</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_e"><span>e</span></a></li>
      <li><a href="#index_g"><span>g</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_l"><span>l</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all functions, variables, defines, enums, and typedefs with links to the files they belong to:

<h3><a class="anchor" id="index_c">- c -</a></h3><ul>
<li>chain_tok()
: <a class="el" href="m__token_8c.html#a50ef074a3d1cf22f842abd4df7081743">m_token.c</a>
, <a class="el" href="m__token_8h.html#a50ef074a3d1cf22f842abd4df7081743">m_token.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_d">- d -</a></h3><ul>
<li>DEC_CHAR
: <a class="el" href="m__ms_8h.html#aac4ed973666f5f09c3e866666326fb05">m_ms.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_e">- e -</a></h3><ul>
<li>eMST_array
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac94af9ef7756a7c82213991ed795600a">m_token.h</a>
</li>
<li>eMST_assign
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a921c684ed8d84a713967491a27abc903">m_token.h</a>
</li>
<li>eMST_based
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d78963c03a17b55f921b31419f83d56">m_token.h</a>
</li>
<li>eMST_colon
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a92a6f2d7b92b576b9b64565f40cb8267">m_token.h</a>
</li>
<li>eMST_colonarray
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a17a05df86d0d2e85f841baaccc2ca5c2">m_token.h</a>
</li>
<li>eMST_coloncolon
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a90545698adebd8bbce09897217bd9695">m_token.h</a>
</li>
<li>eMST_combine
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae947463010b4b392684e86e14337d61b">m_token.h</a>
</li>
<li>eMST_cv
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a35df6016f35b2a5e8df1d01f477f9e17">m_token.h</a>
</li>
<li>eMST_destructor
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ab87ff64219a845b0674207f9352895ee">m_token.h</a>
</li>
<li>eMST_dim
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a7d3878378f218ed88b37f69ccc8b212a">m_token.h</a>
</li>
<li>eMST_ecsu
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acc1c13446a5ec298703df9bd94c6f20e">m_token.h</a>
</li>
<li>eMST_element
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a376aea6022acbe69f38de3c9ea784484">m_token.h</a>
</li>
<li>eMST_exp
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af8e71df660ac1c8cf469ca3ddbbd0477">m_token.h</a>
</li>
<li>eMST_frame
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ac7455cb6f9f1dc1c495178075f5e617f">m_token.h</a>
</li>
<li>eMST_gcarray
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a0f681303ab234dbd4f348e4f4ca2d3fb">m_token.h</a>
</li>
<li>eMST_lexical_frame
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae271da23693826e5cacab27fd56779dc">m_token.h</a>
</li>
<li>eMST_ltgt
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ad08a86d5932f63c1a505083332ba1f2b">m_token.h</a>
</li>
<li>eMST_name
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aab3ff1160a7b9f50b2734b0263482589">m_token.h</a>
</li>
<li>eMST_nonetypetemplateparam
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321acf92fa1bb19698734ba3e697b96ce9e5">m_token.h</a>
</li>
<li>eMST_nttp
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ace877760d7bed370bfdd6570130878bc">m_token.h</a>
</li>
<li>eMST_oper
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a04c4d8922a43196bcbf80ef96ad3c5f8">m_token.h</a>
</li>
<li>eMST_opname
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a56c4f2ba212e690d3862736cefc9e9d1">m_token.h</a>
</li>
<li>eMST_rframe
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a6726a799efbe0d2d722fc83e3a04ca05">m_token.h</a>
</li>
<li>eMST_rtti
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc536c03cd872d606af6a4749c7a84e8">m_token.h</a>
</li>
<li>eMST_scope
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a4c46333751fc7ce9d5e6e89e82abeef4">m_token.h</a>
</li>
<li>eMST_slashed
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a795ccccfc527f0ab6cfe6c372c47cbe6">m_token.h</a>
</li>
<li>eMST_templargname
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a02c0ba2ae316a629377149299fa60732">m_token.h</a>
</li>
<li>eMST_template_argument_list
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afcc464362c6d0c42ce707a51a7fbb9ae">m_token.h</a>
</li>
<li>eMST_templateparam
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321afa6fba3d6a855d55277e3a8b74f4cb16">m_token.h</a>
</li>
<li>eMST_throw
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321ae1a781af8dddef1a996e911152071d2f">m_token.h</a>
</li>
<li>eMST_type
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a1c6d0872377629404a837b33093e4c20">m_token.h</a>
</li>
<li>eMST_udt_returning
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a3f4d325424d8fd3976f2dfb834cd145d">m_token.h</a>
</li>
<li>eMST_unmangled
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aeba2165312b4788cea28a9edd3011eb1">m_token.h</a>
</li>
<li>eMST_val
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321af0a43b0c61ff4f5379159b46e21ca8c1">m_token.h</a>
</li>
<li>eMST_vbtable
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321adc20bcf98832c814c92709447c1db811">m_token.h</a>
</li>
<li>eMST_vcall
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321a9d8acdbf8ea203bad5af3728e8ee5e43">m_token.h</a>
</li>
<li>eMST_vftable
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321aacf994772d1d924f2213dfd8d224b1fe">m_token.h</a>
</li>
<li>eMSToken
: <a class="el" href="m__token_8h.html#ad211982ef565f0550b5f86e4d15c6321">m_token.h</a>
</li>
<li>eMToken
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebd">m_token.h</a>
</li>
<li>eMToken_binary
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda9e20893db7dcbd0e2173b4f559cbb363">m_token.h</a>
</li>
<li>eMToken_dim
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda7cb7b1dd2515e39563cac546fb6ab68c">m_token.h</a>
</li>
<li>eMToken_MAX
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda66baeee525a866cdabe57f4e248b0f1a">m_token.h</a>
</li>
<li>eMToken_name
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdae4b3f564d86b8e05792093eceba0612e">m_token.h</a>
</li>
<li>eMToken_none
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebdaab5d1cb176ec018a7dd2e1debed34b98">m_token.h</a>
</li>
<li>eMToken_unary
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda22e1257761823f14db1c71ae94e7af8b">m_token.h</a>
</li>
<li>eMToken_value
: <a class="el" href="m__token_8h.html#a50bee8455836804dd921dd275b0bcebda8e367c0d5a2c127b0f1ae55573e92fcd">m_token.h</a>
</li>
<li>ENCODING_TYPE_MS
: <a class="el" href="m__ms_8h.html#aef5f6ad4353a2cf2321c074dbfaa9aac">m_ms.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_g">- g -</a></h3><ul>
<li>gen_binary()
: <a class="el" href="m__token_8c.html#a0deb555c3210a60f0b5189ae462ed620">m_token.c</a>
, <a class="el" href="m__token_8h.html#a0deb555c3210a60f0b5189ae462ed620">m_token.h</a>
</li>
<li>gen_dim()
: <a class="el" href="m__token_8h.html#aced2d2323162ca5846cdb13d631169d6">m_token.h</a>
, <a class="el" href="m__token_8c.html#aced2d2323162ca5846cdb13d631169d6">m_token.c</a>
</li>
<li>gen_name()
: <a class="el" href="m__token_8c.html#ac1a6fe5d506c4fd78650742da8d9e669">m_token.c</a>
, <a class="el" href="m__token_8h.html#ac1a6fe5d506c4fd78650742da8d9e669">m_token.h</a>
</li>
<li>gen_tok()
: <a class="el" href="m__token_8c.html#a3d90ad7945dc89f63c39837ee512fd85">m_token.c</a>
</li>
<li>gen_unary()
: <a class="el" href="m__token_8h.html#a8c630a1c57e3d4f5009448af0d43fbb8">m_token.h</a>
, <a class="el" href="m__token_8c.html#a8c630a1c57e3d4f5009448af0d43fbb8">m_token.c</a>
</li>
<li>gen_value()
: <a class="el" href="m__token_8h.html#a5e98df3f83afcc6e9e1f079091c0e567">m_token.h</a>
, <a class="el" href="m__token_8c.html#a5e98df3f83afcc6e9e1f079091c0e567">m_token.c</a>
</li>
<li>GET_CHAR
: <a class="el" href="m__ms_8h.html#a0abbf2a725f45243f4292bf3e764973c">m_ms.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_i">- i -</a></h3><ul>
<li>INC_CHAR
: <a class="el" href="m__ms_8h.html#adfca56cc6bed709fa84cc0b26430100d">m_ms.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_l">- l -</a></h3><ul>
<li>libmangle_decode_ms_name()
: <a class="el" href="libmangle_8h.html#a14ff3e5c3309017dc99459ef1ffef582">libmangle.h</a>
, <a class="el" href="m__ms_8c.html#a53be44f77ef7b80bfc16250da927a99e">m_ms.c</a>
, <a class="el" href="m__ms_8h.html#a53be44f77ef7b80bfc16250da927a99e">m_ms.h</a>
</li>
<li>libmangle_dump_tok()
: <a class="el" href="libmangle_8h.html#ab22601869037438e47eca7186a4cef65">libmangle.h</a>
, <a class="el" href="m__token_8c.html#abf3eb472b66b477d0165a31437d35c09">m_token.c</a>
, <a class="el" href="m__token_8h.html#abf3eb472b66b477d0165a31437d35c09">m_token.h</a>
</li>
<li>libmangle_encode_ms_name()
: <a class="el" href="m__ms_8c.html#a0872a8e6f16a49ccfc3e8663ed003354">m_ms.c</a>
, <a class="el" href="m__ms_8h.html#a0872a8e6f16a49ccfc3e8663ed003354">m_ms.h</a>
, <a class="el" href="libmangle_8h.html#ad6e58fecfca8cc312a2b09a44e3748fb">libmangle.h</a>
</li>
<li>libmangle_gc_t
: <a class="el" href="libmangle_8h.html#af17e2fe323e27ccf4827813ee0c8612e">libmangle.h</a>
</li>
<li>libmangle_gen_tok()
: <a class="el" href="m__token_8h.html#abeb019f98a7616488287af32a6f9e51b">m_token.h</a>
</li>
<li>libmangle_generate_gc()
: <a class="el" href="libmangle_8h.html#a54257a43469abe9c5f9556a1913bbf2f">libmangle.h</a>
, <a class="el" href="m__token_8c.html#a54257a43469abe9c5f9556a1913bbf2f">m_token.c</a>
, <a class="el" href="m__token_8h.html#a54257a43469abe9c5f9556a1913bbf2f">m_token.h</a>
</li>
<li>libmangle_print_decl()
: <a class="el" href="libmangle_8h.html#a2c4d83f71d35e434250eb2779e29ef29">libmangle.h</a>
, <a class="el" href="m__token_8c.html#afb0d47109f166db3186774ddfcb994be">m_token.c</a>
, <a class="el" href="m__token_8h.html#a278a5859cf0ffb4e32fd2ad4cb2584de">m_token.h</a>
</li>
<li>libmangle_release_gc()
: <a class="el" href="libmangle_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">libmangle.h</a>
, <a class="el" href="m__token_8h.html#ac6f10b5d722b67adc42b2efaf4683dc1">m_token.h</a>
, <a class="el" href="m__token_8c.html#ac6f10b5d722b67adc42b2efaf4683dc1">m_token.c</a>
</li>
<li>libmangle_sprint_decl()
: <a class="el" href="m__token_8c.html#ac0f7cf41cc7c3e9c57dd94ed318dd5a4">m_token.c</a>
, <a class="el" href="libmangle_8h.html#abf4af1b2e483a32beb147474853e696b">libmangle.h</a>
, <a class="el" href="m__token_8h.html#ac0f7cf41cc7c3e9c57dd94ed318dd5a4">m_token.h</a>
</li>
<li>libmangle_tokens_t
: <a class="el" href="libmangle_8h.html#a7c9c7d368eb1f52cac14457766a01cc7">libmangle.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_m">- m -</a></h3><ul>
<li>MTOKEN_BINARY_LEFT
: <a class="el" href="m__token_8h.html#ae7a7881952af6eea195152209a4166d8">m_token.h</a>
</li>
<li>MTOKEN_BINARY_RIGHT
: <a class="el" href="m__token_8h.html#a158648c39041985090c587f092b38316">m_token.h</a>
</li>
<li>MTOKEN_CHAIN
: <a class="el" href="m__token_8h.html#a22776018ac7fe7e7c0aa47cfe5f473a8">m_token.h</a>
</li>
<li>MTOKEN_DIM_NEGATE
: <a class="el" href="m__token_8h.html#abe961c81235d1d263052dd61439dbcf3">m_token.h</a>
</li>
<li>MTOKEN_DIM_NTTP
: <a class="el" href="m__token_8h.html#a5ded8e065363aa57a8c5ecb0e4b3a0f7">m_token.h</a>
</li>
<li>MTOKEN_DIM_VALUE
: <a class="el" href="m__token_8h.html#a519b6bd0fb1c60d1077842bddeb731c0">m_token.h</a>
</li>
<li>MTOKEN_FLAGS
: <a class="el" href="m__token_8h.html#ac080f6582086796b1ede7f1f65ae9fcf">m_token.h</a>
</li>
<li>MTOKEN_FLAGS_ARRAY
: <a class="el" href="m__token_8h.html#a707505a9dd27394e28326b9e24b8a0e4">m_token.h</a>
</li>
<li>MTOKEN_FLAGS_NOTE
: <a class="el" href="m__token_8h.html#adde521240f7b6401ffb3954772cfdb30">m_token.h</a>
</li>
<li>MTOKEN_FLAGS_PTRREF
: <a class="el" href="m__token_8h.html#aa5b2060375f2aa1caa8995fb9e3fe8c2">m_token.h</a>
</li>
<li>MTOKEN_FLAGS_UDC
: <a class="el" href="m__token_8h.html#a1163ae872e9f50ddae2aeb936fc4d5e6">m_token.h</a>
</li>
<li>MTOKEN_KIND
: <a class="el" href="m__token_8h.html#a9c7f6053956c20047da91268be3e6a47">m_token.h</a>
</li>
<li>MTOKEN_NAME
: <a class="el" href="m__token_8h.html#ae3b0c2bd397aa5acede119bad863c8f8">m_token.h</a>
</li>
<li>MTOKEN_SUBKIND
: <a class="el" href="m__token_8h.html#a5753385eac52aad6be25ae37f0ea5d6a">m_token.h</a>
</li>
<li>MTOKEN_UNARY
: <a class="el" href="m__token_8h.html#a362970fb206c74f30355356570000221">m_token.h</a>
</li>
<li>MTOKEN_VALUE
: <a class="el" href="m__token_8h.html#a0d7b7e44c99e08fe263ea15190ceeee1">m_token.h</a>
</li>
<li>MTOKEN_VALUE_SIGNED
: <a class="el" href="m__token_8h.html#a92051c626009297e17ff622b77e809f7">m_token.h</a>
</li>
<li>MTOKEN_VALUE_SIZE
: <a class="el" href="m__token_8h.html#a0912420c7697d7824cb9ce3761e999ac">m_token.h</a>
</li>
<li>MY_LL
: <a class="el" href="m__token_8c.html#a7defdcab1465fcfa706d66f1572a08d5">m_token.c</a>
</li>
</ul>


<h3><a class="anchor" id="index_s">- s -</a></h3><ul>
<li>SKIP_CHAR
: <a class="el" href="m__ms_8h.html#a1a18b928484e6e93526a252d4b7e3532">m_ms.h</a>
</li>
</ul>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
