;
; Definition file of MSSIGN32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "MSSIGN32.dll"
EXPORTS
FreeCryptProvFromCert
FreeCryptProvFromCertEx
GetCryptProvFromCert
GetCryptProvFromCertEx
PvkFreeCryptProv
PvkGetCryptProv
PvkPrivateKeyAcquireContext
PvkPrivateKeyAcquireContextA
PvkPrivateKeyAcquireContextFromMemory
PvkPrivateKeyAcquireContextFromMemoryA
PvkPrivateKeyLoad
PvkPrivateKeyLoadA
PvkPrivateKeyLoadFromMemory
PvkPrivateKeyLoadFromMemoryA
PvkPrivateKeyReleaseContext
PvkPrivateKeyReleaseContextA
PvkPrivateKeySave
PvkPrivateKeySaveA
PvkPrivateKeySaveToMemory
PvkPrivateKeySaveToMemoryA
SignError
SignerAddTimeStampResponse
SignerAddTimeStampResponseEx
SignerCreateTimeStampRequest
SignerFreeSignerContext
SignerSign
SignerSignEx
SignerSignEx2
SignerTimeStamp
SignerTimeStampEx
SignerTimeStampEx2
SignerTimeStampEx3
SpcGetCertFromKey
