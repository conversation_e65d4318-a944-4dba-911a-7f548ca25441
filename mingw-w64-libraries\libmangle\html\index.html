<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<title>libmangle: Libmangle</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="doxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- Generated by Doxygen 1.6.1 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li class="current"><a href="index.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&nbsp;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Libmangle</h1><h3><dl class="rcs"><dt><b>Revision</b></dt><dd>2283</dd></dl>
</h3><dl class="author"><dt><b>Author:</b></dt><dd>The mingw-w64 Dev Team </dd></dl>
<dl class="version"><dt><b>Version:</b></dt><dd>1.0 </dd></dl>
<h2><a class="anchor" id="intro_sec">
Introduction</a></h2>
<p>Libmangle is library for translating C++ symbols produced by Microsoft Visual Studio C++ suite of tools into human readable names. </p>
<h3><a class="anchor" id="info_subsec">
Name Mangling</a></h3>
<p>Name mangling is a technique used by modern compilers to add addition information about a function, a structure or a datatype to a symbol name. Information can include call parameter type and return type, symbol namespace and etc. Often, the way the information is encoded in specific to a compiler vendor. </p>
<h3><a class="anchor" id="cpp_subsec">
C++ and Name Mangling</a></h3>
<p>Languages such as the C++ language do not define a standard name decoration scheme, most often code produced by one compiler is also incompatible with another C++ compiler ABI wise. Name mangling prevents accidentally linking to code meant for another compiler. </p>
<h3><a class="anchor" id="decde_subsec">
Deciphering Mangled Names</a></h3>
<p>Libmangle allows symbol names on objects created by Microsoft Visual C++ tools to be decoded into human readable names. In addition to easing debugging, it also allows the possibility of non-MSVC tools to examine the objects. </p>
<h3><a class="anchor" id="external_subsec">
External Links</a></h3>
<p>For more information, see: <a href="http://en.wikipedia.org/wiki/Name_mangling">http://en.wikipedia.org/wiki/Name_mangling</a> </p>
<h2><a class="anchor" id="install_sec">
Installation</a></h2>
<p>Use the shell script "configure" and pass "--help" for library build and install options. </p>
</div>
<hr size="1"/><address style="text-align: right;"><small>Generated on 23 Jul 2010 for libmangle by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.6.1 </small></address>
</body>
</html>
