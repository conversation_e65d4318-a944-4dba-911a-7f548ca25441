LIBRARY OPENDS60.dll
EXPORTS
ODS_init
srv_thread
int_getpOAInfo
int_setpOAInfo
srv_IgnoreAnsiToOem
srv_ackattention
srv_alloc
srv_ansi_describe
srv_ansi_paramdata
srv_ansi_sendmsg
srv_ansi_sendrow
srv_bmove
srv_bzero
srv_clearstatistics
srv_config
srv_config_alloc
srv_convert
srv_describe
srv_errhandle
srv_event
srv_eventdata
srv_flush
srv_free
srv_get_text
srv_getbindtoken
srv_getconfig
srv_getdtcxact
srv_getserver
srv_getuserdata
srv_got_attention
srv_handle
srv_impersonate_client
srv_init
srv_iodead
srv_langcpy
srv_langlen
srv_langptr
srv_log
srv_message_handler
srv_paramdata
srv_paraminfo
srv_paramlen
srv_parammaxlen
srv_paramname
srv_paramnumber
srv_paramset
srv_paramsetoutput
srv_paramstatus
srv_paramtype
srv_pfield
srv_pfieldex
srv_post_completion_queue
srv_post_handle
srv_pre_handle
srv_returnval
srv_revert_to_self
srv_rpcdb
srv_rpcname
srv_rpcnumber
srv_rpcoptions
srv_rpcowner
srv_rpcparams
srv_run
srv_senddone
srv_sendmsg
srv_sendrow
srv_sendstatistics
srv_sendstatus
srv_setcoldata
srv_setcollen
srv_setevent
srv_setuserdata
srv_setutype
srv_sfield
srv_symbol
srv_tdsversion
srv_terminatethread
srv_willconvert
srv_writebuf
srv_wsendmsg
