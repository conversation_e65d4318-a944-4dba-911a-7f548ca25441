LIBRARY "DUser.dll"
EXPORTS
DUserCastHandle
DUserDeleteGadget
GetStdColorBrushF
GetStdColorF
GetStdColorPenF
UtilDrawOutlineRect
AddGadgetMessageHandler
AddLayeredRef
AdjustClipInsideRef
AttachWndProcA
AttachWndProcW
AutoTrace
BeginTransition
BeginHideInputPaneAnimation
BeginShowInputPaneAnimation
BuildAnimation
BuildDropTarget
BuildInterpolation
CacheDWriteRenderTarget
ChangeCurrentAnimationScenario
ClearPushedOpacitiesFromGadgetTree
ClearTopmostVisual
CreateAction
CreateGadget
CreateTransition
CustomGadgetHitTestQuery
DUserBuildGadget
DUserCastClass
DUserCastDirect
DUserFindClass
DUserFlushDeferredMessages
DUserFlushMessages
DUserGetAlphaPRID
DUserGetGutsData
DUserGetRectPRID
DUserGetRotatePRID
DUserGetScalePRID
DUserInstanceOf
DUserPostEvent
DUserPostMethod
DUserRegisterGuts
DUserRegisterStub
DUserRegisterSuper
DUserSendEvent
DUserSendMethod
DUserStopAnimation
DUserStopPVLAnimation
DeleteHandle
DestroyPendingDCVisuals
DetachGadgetVisuals
DetachWndProc
DisableContainerHwnd
DrawGadgetTree
EndInputPaneAnimation
EndTransition
EnsureAnimationsEnabled
EnsureGadgetTransInitialized
EnumGadgets
FindGadgetFromPoint
FindGadgetMessages
FindGadgetTargetingInfo
FindStdColor
FireGadgetMessages
ForwardGadgetMessage
GadgetTransCompositionChanged
GadgetTransSettingChanged
GetActionTimeslice
GetCachedDWriteRenderTarget
GetDUserModule
GetDebug
GetFinalAnimatingPosition
GetGadget
GetGadgetAnimation
GetGadgetBitmap
GetGadgetBufferInfo
GetGadgetCenterPoint
GetGadgetFlags
GetGadgetFocus
GetGadgetLayerInfo
GetGadgetMessageFilter
GetGadgetProperty
GetGadgetRect
GetGadgetRgn
GetGadgetRootInfo
GetGadgetRotation
GetGadgetScale
GetGadgetSize
GetGadgetStyle
GetGadgetTicket
GetGadgetVisual
GetMessageExA
GetMessageExW
GetStdColorBrushI
GetStdColorI
GetStdColorName
GetStdColorPenI
GetStdPalette
GetTransitionInterface
InitGadgetComponent
InitGadgets
InvalidateGadget
InvalidateLayeredDescendants
IsGadgetParentChainStyle
IsInsideContext
IsStartDelete
LookupGadgetTicket
MapGadgetPoints
PeekMessageExA
PeekMessageExW
PlayTransition
PrintTransition
RegisterGadgetMessage
RegisterGadgetMessageString
RegisterGadgetProperty
ReleaseDetachedObjects
ReleaseLayeredRef
ReleaseMouseCapture
RemoveClippingImmunityFromVisual
RemoveGadgetMessageHandler
RemoveGadgetProperty
ResetDUserDevice
ScheduleGadgetTransitions
SetActionTimeslice
SetAtlasingHints
SetGadgetBufferInfo
SetGadgetCenterPoint
SetGadgetFillF
SetGadgetFillI
SetGadgetFlags
SetGadgetFocus
SetGadgetFocusEx
SetGadgetLayerInfo
SetGadgetMessageFilter
SetGadgetOrder
SetGadgetParent
SetGadgetProperty
SetGadgetRect
SetGadgetRootInfo
SetGadgetRotation
SetGadgetScale
SetGadgetStyle
SetHardwareDeviceUsage
SetMinimumDCompVersion
SetRestoreCachedLayeredRefFlag
SetTransitionVisualProperties
SetWindowResizeFlag
UninitGadgetComponent
UnregisterGadgetMessage
UnregisterGadgetMessageString
UnregisterGadgetProperty
UtilBuildFont
UtilDrawBlendRect
UtilGetColor
UtilSetBackground
WaitMessageEx
