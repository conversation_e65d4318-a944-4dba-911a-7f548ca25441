;
; Definition file of Secur32.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008
;
LIBRARY "Secur32.dll"
EXPORTS
SecDeleteUserModeContext
SecInitUserModeContext
CloseLsaPerformanceData
CollectLsaPerformanceData
OpenLsaPerformanceData
AcceptSecurityContext
AcquireCredentialsHandleA
AcquireCredentialsHandleW
AddCredentialsA
AddCredentialsW
AddSecurityPackageA
AddSecurityPackageW
ApplyControlToken
ChangeAccountPasswordA
ChangeAccountPasswordW
CompleteAuthToken
CredMarshalTargetInfo
CredParseUserNameWithType
CredUnmarshalTargetInfo
DecryptMessage
DeleteSecurityContext
DeleteSecurityPackageA
DeleteSecurityPackageW
EncryptMessage
EnumerateSecurityPackagesA
EnumerateSecurityPackagesW
ExportSecurityContext
FreeContextBuffer
FreeCredentialsHandle
GetComputerObject<PERSON>A
GetComputerObjectNameW
GetSecurityUserInfo
GetUserNameExA
GetUserNameExW
ImpersonateSecurityContext
ImportSecurityContextA
ImportSecurityContextW
InitSecurityInterfaceA
InitSecurityInterfaceW
InitializeSecurityContextA
InitializeSecurityContextW
LsaCallAuthenticationPackage
LsaConnectUntrusted
LsaDeregisterLogonProcess
LsaEnumerateLogonSessions
LsaFreeReturnBuffer
LsaGetLogonSessionData
LsaLogonUser
LsaLookupAuthenticationPackage
LsaRegisterLogonProcess
LsaRegisterPolicyChangeNotification
LsaUnregisterPolicyChangeNotification
MakeSignature
QueryContextAttributesA
QueryContextAttributesW
QueryCredentialsAttributesA
QueryCredentialsAttributesW
QuerySecurityContextToken
QuerySecurityPackageInfoA
QuerySecurityPackageInfoW
RevertSecurityContext
SaslAcceptSecurityContext
SaslEnumerateProfilesA
SaslEnumerateProfilesW
SaslGetContextOption
SaslGetProfilePackageA
SaslGetProfilePackageW
SaslIdentifyPackageA
SaslIdentifyPackageW
SaslInitializeSecurityContextA
SaslInitializeSecurityContextW
SaslSetContextOption
SealMessage
SecCacheSspiPackages
SeciAllocateAndSetCallFlags
SeciAllocateAndSetIPAddress
SeciFreeCallContext
SecpFreeMemory
SecpSetIPAddress
SecpTranslateName
SecpTranslateNameEx
SetContextAttributesA
SetContextAttributesW
SetCredentialsAttributesA
SetCredentialsAttributesW
SspiCompareAuthIdentities
SspiCopyAuthIdentity
SspiDecryptAuthIdentity
SspiEncodeAuthIdentityAsStrings
SspiEncodeStringsAsAuthIdentity
SspiEncryptAuthIdentity
SspiExcludePackage
SspiFreeAuthIdentity
SspiGetTargetHostName
SspiIsAuthIdentityEncrypted
SspiLocalFree
SspiMarshalAuthIdentity
SspiPrepareForCredRead
SspiPrepareForCredWrite
SspiUnmarshalAuthIdentity
SspiValidateAuthIdentity
SspiZeroAuthIdentity
TranslateNameA
TranslateNameW
UnsealMessage
VerifySignature
