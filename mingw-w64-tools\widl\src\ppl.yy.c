#line 1 "tools/wrc/ppl.yy.c"
#include "config.h"

#line 4 "tools/wrc/ppl.yy.c"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define yy_create_buffer ppy__create_buffer
#define yy_delete_buffer ppy__delete_buffer
#define yy_scan_buffer ppy__scan_buffer
#define yy_scan_string ppy__scan_string
#define yy_scan_bytes ppy__scan_bytes
#define yy_init_buffer ppy__init_buffer
#define yy_flush_buffer ppy__flush_buffer
#define yy_load_buffer_state ppy__load_buffer_state
#define yy_switch_to_buffer ppy__switch_to_buffer
#define yypush_buffer_state ppy_push_buffer_state
#define yypop_buffer_state ppy_pop_buffer_state
#define yyensure_buffer_stack ppy_ensure_buffer_stack
#define yy_flex_debug ppy__flex_debug
#define yyin ppy_in
#define yyleng ppy_leng
#define yylex ppy_lex
#define yylineno ppy_lineno
#define yyout ppy_out
#define yyrestart ppy_restart
#define yytext ppy_text
#define yywrap ppy_wrap
#define yyalloc ppy_alloc
#define yyrealloc ppy_realloc
#define yyfree ppy_free

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yy_create_buffer
#define ppy__create_buffer_ALREADY_DEFINED
#else
#define yy_create_buffer ppy__create_buffer
#endif

#ifdef yy_delete_buffer
#define ppy__delete_buffer_ALREADY_DEFINED
#else
#define yy_delete_buffer ppy__delete_buffer
#endif

#ifdef yy_scan_buffer
#define ppy__scan_buffer_ALREADY_DEFINED
#else
#define yy_scan_buffer ppy__scan_buffer
#endif

#ifdef yy_scan_string
#define ppy__scan_string_ALREADY_DEFINED
#else
#define yy_scan_string ppy__scan_string
#endif

#ifdef yy_scan_bytes
#define ppy__scan_bytes_ALREADY_DEFINED
#else
#define yy_scan_bytes ppy__scan_bytes
#endif

#ifdef yy_init_buffer
#define ppy__init_buffer_ALREADY_DEFINED
#else
#define yy_init_buffer ppy__init_buffer
#endif

#ifdef yy_flush_buffer
#define ppy__flush_buffer_ALREADY_DEFINED
#else
#define yy_flush_buffer ppy__flush_buffer
#endif

#ifdef yy_load_buffer_state
#define ppy__load_buffer_state_ALREADY_DEFINED
#else
#define yy_load_buffer_state ppy__load_buffer_state
#endif

#ifdef yy_switch_to_buffer
#define ppy__switch_to_buffer_ALREADY_DEFINED
#else
#define yy_switch_to_buffer ppy__switch_to_buffer
#endif

#ifdef yypush_buffer_state
#define ppy_push_buffer_state_ALREADY_DEFINED
#else
#define yypush_buffer_state ppy_push_buffer_state
#endif

#ifdef yypop_buffer_state
#define ppy_pop_buffer_state_ALREADY_DEFINED
#else
#define yypop_buffer_state ppy_pop_buffer_state
#endif

#ifdef yyensure_buffer_stack
#define ppy_ensure_buffer_stack_ALREADY_DEFINED
#else
#define yyensure_buffer_stack ppy_ensure_buffer_stack
#endif

#ifdef yylex
#define ppy_lex_ALREADY_DEFINED
#else
#define yylex ppy_lex
#endif

#ifdef yyrestart
#define ppy_restart_ALREADY_DEFINED
#else
#define yyrestart ppy_restart
#endif

#ifdef yylex_init
#define ppy_lex_init_ALREADY_DEFINED
#else
#define yylex_init ppy_lex_init
#endif

#ifdef yylex_init_extra
#define ppy_lex_init_extra_ALREADY_DEFINED
#else
#define yylex_init_extra ppy_lex_init_extra
#endif

#ifdef yylex_destroy
#define ppy_lex_destroy_ALREADY_DEFINED
#else
#define yylex_destroy ppy_lex_destroy
#endif

#ifdef yyget_debug
#define ppy_get_debug_ALREADY_DEFINED
#else
#define yyget_debug ppy_get_debug
#endif

#ifdef yyset_debug
#define ppy_set_debug_ALREADY_DEFINED
#else
#define yyset_debug ppy_set_debug
#endif

#ifdef yyget_extra
#define ppy_get_extra_ALREADY_DEFINED
#else
#define yyget_extra ppy_get_extra
#endif

#ifdef yyset_extra
#define ppy_set_extra_ALREADY_DEFINED
#else
#define yyset_extra ppy_set_extra
#endif

#ifdef yyget_in
#define ppy_get_in_ALREADY_DEFINED
#else
#define yyget_in ppy_get_in
#endif

#ifdef yyset_in
#define ppy_set_in_ALREADY_DEFINED
#else
#define yyset_in ppy_set_in
#endif

#ifdef yyget_out
#define ppy_get_out_ALREADY_DEFINED
#else
#define yyget_out ppy_get_out
#endif

#ifdef yyset_out
#define ppy_set_out_ALREADY_DEFINED
#else
#define yyset_out ppy_set_out
#endif

#ifdef yyget_leng
#define ppy_get_leng_ALREADY_DEFINED
#else
#define yyget_leng ppy_get_leng
#endif

#ifdef yyget_text
#define ppy_get_text_ALREADY_DEFINED
#else
#define yyget_text ppy_get_text
#endif

#ifdef yyget_lineno
#define ppy_get_lineno_ALREADY_DEFINED
#else
#define yyget_lineno ppy_get_lineno
#endif

#ifdef yyset_lineno
#define ppy_set_lineno_ALREADY_DEFINED
#else
#define yyset_lineno ppy_set_lineno
#endif

#ifdef yywrap
#define ppy_wrap_ALREADY_DEFINED
#else
#define yywrap ppy_wrap
#endif

#ifdef yyalloc
#define ppy_alloc_ALREADY_DEFINED
#else
#define yyalloc ppy_alloc
#endif

#ifdef yyrealloc
#define ppy_realloc_ALREADY_DEFINED
#else
#define yyrealloc ppy_realloc
#endif

#ifdef yyfree
#define ppy_free_ALREADY_DEFINED
#else
#define yyfree ppy_free
#endif

#ifdef yytext
#define ppy_text_ALREADY_DEFINED
#else
#define yytext ppy_text
#endif

#ifdef yyleng
#define ppy_leng_ALREADY_DEFINED
#else
#define yyleng ppy_leng
#endif

#ifdef yyin
#define ppy_in_ALREADY_DEFINED
#else
#define yyin ppy_in
#endif

#ifdef yyout
#define ppy_out_ALREADY_DEFINED
#else
#define yyout ppy_out
#endif

#ifdef yy_flex_debug
#define ppy__flex_debug_ALREADY_DEFINED
#else
#define yy_flex_debug ppy__flex_debug
#endif

#ifdef yylineno
#define ppy_lineno_ALREADY_DEFINED
#else
#define yylineno ppy_lineno
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern int yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = NULL; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static int yy_n_chars;		/* number of characters read into yy_ch_buf */
int yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = NULL;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart ( FILE *input_file  );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size  );
void yy_delete_buffer ( YY_BUFFER_STATE b  );
void yy_flush_buffer ( YY_BUFFER_STATE b  );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer  );
void yypop_buffer_state ( void );

static void yyensure_buffer_stack ( void );
static void yy_load_buffer_state ( void );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file  );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size  );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len  );

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */
typedef flex_uint8_t YY_CHAR;

FILE *yyin = NULL, *yyout = NULL;

typedef int yy_state_type;

extern int yylineno;
int yylineno = 1;

extern char *yytext;
#ifdef yytext_ptr
#undef yytext_ptr
#endif
#define yytext_ptr yytext

static yy_state_type yy_get_previous_state ( void );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  );
static int yy_get_next_buffer ( void );
static void yynoreturn yy_fatal_error ( const char* msg  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;
#define YY_NUM_RULES 145
#define YY_END_OF_BUFFER 146
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[421] =
    {   0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,  119,  119,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,  146,  135,  136,  137,
      123,  144,  124,  135,  134,  135,  136,    1,   21,   16,
       18,   16,   21,   20,   17,   17,   17,   17,   17,   17,
       17,   17,   70,   73,   71,   72,   28,   25,   26,   24,
       28,   23,   28,   28,  125,  133,  126,  144,  127,  128,
      144,  129,  130,  119,  120,  119,   79,   77,   79,   79,

       76,   79,   80,   84,   86,   85,  144,   81,   92,   88,
       92,   87,   90,   92,   92,   89,   92,   94,  102,  104,
       97,  103,   99,   98,   95,   99,   94,  108,  108,  106,
      105,  108,  116,  115,  112,  113,  109,  110,  111,  116,
      116,  135,  136,  140,  123,  140,  124,  135,  134,  135,
       53,   50,   47,   53,   52,   53,   51,   53,   33,   35,
       53,   53,   53,   53,   49,   53,   53,   60,   57,   58,
       60,   56,   60,   54,   55,   64,   61,   62,   64,   64,
       22,   69,   66,   69,   67,   69,   65,   69,   29,   30,
       32,   32,   32,   29,    1,  142,  145,  143,  141,  141,

      135,  136,  135,  118,  122,  134,  138,  139,  136,    1,
       16,   17,   17,   17,   17,   17,   17,   17,   17,   18,
       19,   20,   17,   17,   17,   17,   17,   11,   17,   17,
       17,   17,   17,   70,   71,   72,   74,   72,   25,   27,
       28,  125,  132,  131,  132,  127,  129,  119,  119,  119,
      121,   77,   75,   76,   78,   79,   80,   81,   83,   81,
       88,   87,   92,   89,   93,   92,   94,   96,   99,   98,
       98,   95,  101,   99,   94,  108,  106,  105,  108,  107,
      108,  116,  116,  114,  117,  116,  135,  140,  140,   50,
       44,   41,   33,   34,   33,   33,   37,   35,   35,   35,

       39,   45,   43,   46,   40,   48,   49,  134,   42,   57,
       56,   59,   60,   54,   61,   63,    0,   22,   66,   65,
       69,   68,   69,   29,   31,   32,   29,  142,  141,  122,
      122,   17,   17,   17,   17,   17,   17,   11,   17,   17,
       17,   17,   17,   17,   17,   71,   82,   91,  100,   34,
       34,   33,   33,   33,   36,   35,   35,   35,  134,  122,
       17,   12,   13,   17,   17,   17,   17,   17,   17,   15,
       17,   17,   17,   34,   34,   34,   33,   36,   36,   35,
      134,   17,   12,   13,   14,    4,    7,    9,   17,   17,
       15,   17,    8,   17,   34,   36,   36,   36,  134,    3,

       14,    4,    7,    9,   10,   17,    6,    8,   17,   36,
      134,    3,   10,    2,    6,    5,   38,    2,    5,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        2,    2,    4,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    5,    6,    7,    1,    1,    8,    9,   10,
       11,   12,    1,   13,    1,   14,   15,   16,   17,   17,
       17,   17,   17,   17,   17,   18,   18,    1,    1,   19,
       20,   21,    1,    1,   22,   22,   22,   22,   22,   22,
       23,   23,   23,   23,   23,   24,   23,   23,   23,   23,
       23,   23,   23,   23,   25,   23,   23,   26,   23,   23,
        1,   27,    1,    1,   28,    1,   29,   30,   31,   32,

       33,   34,   35,   36,   37,   36,   36,   38,   39,   40,
       41,   42,   36,   43,   44,   45,   46,   36,   47,   48,
       36,   36,    1,   49,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[50] =
    {   0,
        1,    2,    3,    2,    1,    4,    5,    1,    6,    7,
        8,    9,    8,   10,   11,   12,   12,   12,    1,    1,
       13,   14,   15,   15,   15,   15,   16,   17,   14,   14,
       14,   14,   14,   14,   15,   15,   15,   15,   15,   15,
       15,   15,   15,   15,   15,   15,   15,   15,    1
    } ;

static const flex_int16_t yy_base[467] =
    {   0,
        0,   48,   55,    0,  102,  103,  130,    0,  177,  178,
      179,  182,  186,  187,  104,  107,  214,    0,  261,  262,
      289,    0,  338,    0,  386,  389,  414,  439,  466,    0,
      515,    0,  564,    0, 1129, 1128,  399,  403,  612,  638,
      665,    0,  428,  618,  714,  763, 1130,    0,   49, 1920,
     1920, 1920, 1920,   96,    0,  109,  118, 1920, 1920,  811,
     1920, 1126,  111,  111,  858, 1095,  154,  161, 1090, 1083,
     1085, 1095,    0, 1920,  184,  124, 1920,  196, 1920, 1920,
      254, 1920,    0,  199,    0, 1920, 1920,  269,    0, 1920,
     1120,    0, 1920,    0, 1920,  263, 1920,  208, 1920,  267,

     1112,  277,    0, 1920, 1920, 1920,  271,  281, 1920,  393,
     1920,  396, 1920, 1107,  396,    0,  406,    0, 1920, 1920,
     1113, 1920,  400,  905,    0,  418,  954, 1920,  430, 1920,
     1920,  433,    0, 1920, 1920, 1920, 1920, 1920, 1920,  432,
      435,    0,  449, 1920, 1920, 1920, 1920,  444,    0, 1003,
     1920,  456, 1920, 1099, 1920, 1110, 1920,  449, 1037,  714,
      443, 1097,  603,    0,  623, 1083, 1066, 1920,  615, 1920,
      619,    0,  632,    0, 1920, 1920,  644, 1920,  635,  634,
      635, 1920,  655, 1920, 1920,  646,    0,  659,    0, 1920,
     1920,  725,  738,  741, 1920,  742, 1920, 1920,    0,  735,

        0,  747,  741, 1920,    0,    0, 1920, 1111,  755, 1920,
        0,    0, 1079,  747,  754, 1074, 1067, 1069, 1079, 1920,
     1920, 1104, 1069,  717, 1070, 1058, 1067,  777, 1063, 1052,
     1062, 1056, 1041,    0,    0, 1920, 1920, 1079,  787, 1920,
     1078,    0, 1920, 1920, 1077,    0,    0,    0,  743,  765,
     1920,  791, 1920, 1069, 1920, 1075,    0, 1920,  794, 1074,
      795,  798, 1062,    0, 1920, 1071,    0, 1920,  789,    0,
     1085,    0,  801, 1070,    0,  804, 1920, 1920,  807, 1920,
     1069,    0,  804, 1920, 1920, 1068,  806, 1920,    0,  818,
     1920, 1920,  847,  843,  874,  799,    0,  905,  903,  800,

     1920, 1920, 1920, 1920, 1920, 1920, 1066, 1026, 1920,  823,
        0, 1920, 1048,    0,  824, 1920, 1047,  816,  871,    0,
     1920, 1920, 1027,    0, 1920, 1006,  872,  876,    0, 1134,
        0,  969,  971,  948,  943,  906,  897,  880,  875,  871,
      864,  844,  835,  833,  799,    0,  884, 1920,  890,  907,
      845,  618,  863,  873,  914,  889,  910,  912,  799, 1183,
      795,  938,  942,  796,  786,  747,  702,  695,  614,  959,
      610,  598,  428,  932,  945,  946, 1920,  947,  952, 1920,
      406,  395,  975,  984,  985,  990,  993,  994,  385,  372,
      997,  365,  998,  247, 1920,  966,  991,  994,  241, 1023,

     1024, 1029, 1032, 1033, 1036,  153, 1037, 1040,   74, 1920,
       22, 1041, 1044, 1045, 1054, 1055,    0, 1062, 1063, 1920,
     1232, 1249, 1266, 1283, 1300, 1317, 1334, 1351, 1368, 1385,
     1402, 1419, 1436, 1453, 1466, 1468, 1485, 1502, 1519, 1536,
     1553, 1570, 1587, 1604, 1615, 1632, 1649, 1655, 1672, 1689,
     1706, 1712, 1729, 1746, 1763, 1769, 1786, 1792, 1809, 1826,
     1834, 1851, 1868, 1885, 1056, 1902
    } ;

static const flex_int16_t yy_def[467] =
    {   0,
      420,    1,  420,    3,  421,  421,  420,    7,  422,  422,
      423,  423,  424,  424,  425,  425,  420,   17,  426,  426,
      420,   21,  420,   23,  427,  427,  428,  428,  420,   29,
      420,   31,  420,   33,  429,  429,  430,  430,  431,  431,
      420,   41,  432,  432,  433,  433,  420,  434,  420,  420,
      420,  420,  420,  435,  436,  435,  420,  420,  420,  420,
      420,   60,  420,  420,   60,   65,   65,   65,   65,   65,
       65,   65,  437,  420,  438,  420,  420,  420,  420,  420,
      420,  420,  436,  420,  439,  420,  420,  440,  441,  420,
      440,  442,  420,  443,  420,  444,  420,  420,  420,  420,

      445,  420,  446,  420,  420,  420,  447,  420,  420,  420,
      420,  420,  420,  420,  420,  448,  420,  449,  420,  420,
      420,  420,  450,  451,  452,  420,  452,  420,  420,  420,
      420,  420,  453,  420,  420,  420,  420,  420,  420,  454,
      420,  434,  420,  420,  420,  420,  420,  435,  436,  455,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  436,  420,  436,  420,  420,  420,  420,
      420,  456,  420,  457,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  458,  459,  460,  420,
      420,  420,  420,  460,  420,  420,  420,  420,  461,  461,

      434,  420,  435,  420,  462,  436,  420,  420,  420,  420,
       60,   65,   65,   65,   65,   65,   65,   65,   65,  420,
      420,  420,   65,   65,   65,   65,   65,   65,   65,   65,
       65,   65,   65,  437,  463,  420,  420,  420,  420,  420,
      420,  439,  420,  420,  420,  441,  442,  443,  444,  444,
      420,  420,  420,  445,  420,  420,  446,  420,  420,  420,
      420,  420,  420,  448,  420,  420,  449,  420,  450,  464,
      451,  452,  420,  420,  127,  420,  420,  420,  420,  420,
      420,  453,  454,  420,  420,  420,  435,  420,  455,  420,
      420,  420,  420,  420,  420,  420,  465,  420,  420,  420,

      420,  420,  420,  420,  420,  420,  420,  436,  420,  420,
      456,  420,  420,  457,  420,  420,  420,  420,  420,  458,
      420,  420,  420,  460,  420,  420,  460,  420,  461,  466,
      462,   65,   65,   65,   65,   65,   65,  420,   65,   65,
       65,   65,   65,   65,   65,  463,  420,  420,  420,  420,
      420,  420,  420,  420,  465,  420,  420,  420,  436,  466,
       65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
       65,   65,   65,  420,  420,  420,  420,  420,  420,  420,
      436,   65,  420,  420,   65,   65,   65,   65,   65,   65,
      420,   65,   65,   65,  420,  420,  420,  420,  436,   65,

      420,  420,  420,  420,   65,   65,   65,  420,   65,  420,
      436,  420,  420,   65,  420,   65,  436,  420,  420,    0,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420
    } ;

static const flex_int16_t yy_nxt[1970] =
    {   0,
       48,   49,   50,   49,   48,   51,   52,   48,   53,   48,
       48,   48,   48,   48,   54,   48,   48,   48,   48,   48,
       48,   55,   55,   55,   55,   55,   56,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   48,   57,
      202,   57,  202,  417,   58,   59,   60,   61,   62,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   63,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   64,   59,   65,   65,   65,   66,   67,   65,   65,
       65,   68,   69,   65,   65,   65,   70,   65,   65,   65,

       71,   72,   65,   59,   74,   74,   95,  204,  416,   95,
      205,  207,  208,  221,  222,   96,   75,   75,   96,  209,
      420,  209,  204,  420,  210,  205,  237,  238,   76,   76,
       77,   78,   79,   78,   77,   80,   77,   77,   77,   77,
       77,   77,   77,   77,   81,   77,   77,   77,   82,   77,
       77,   83,   83,   83,   83,   83,   84,   83,   83,   83,
       83,   83,   83,   83,   83,   83,   83,   83,   83,   83,
       83,   83,   83,   83,   83,   83,   83,   83,   77,   86,
       86,   86,   87,   87,   86,  414,  236,   90,   86,   86,
       90,  224,  227,  225,  228,  204,  226,  239,  205,  239,

      229,  240,  241,   88,   88,   91,   93,   93,   91,  252,
      236,  252,   91,   91,   97,   98,   99,   98,   97,   97,
       97,   97,   97,   97,   97,   97,   97,   97,  100,   97,
       97,   97,   97,   97,   97,  101,  101,  101,  101,  101,
      102,  101,  101,  101,  101,  101,  101,  101,  101,  101,
      101,  101,  101,  101,  101,  101,  101,  101,  101,  101,
      101,  101,   97,  104,  104,  204,  105,  105,  205,  106,
      106,  244,  245,  411,  250,  107,  107,  251,  204,  255,
      256,  205,  204,  259,  260,  205,  409,  108,  108,  109,
      110,  111,  110,  109,  109,  109,  109,  109,  109,  112,

      109,  113,  114,  115,  109,  109,  109,  109,  109,  109,
      116,  116,  116,  116,  116,  117,  116,  116,  116,  116,
      116,  116,  116,  116,  116,  116,  116,  116,  116,  116,
      116,  116,  116,  116,  116,  116,  116,  109,  118,  118,
      119,  118,  118,  120,  121,  118,  122,  118,  118,  118,
      118,  118,  123,  124,  124,  124,  118,  118,  118,  125,
      125,  125,  125,  125,  126,  127,  125,  125,  125,  125,
      125,  125,  125,  125,  125,  125,  125,  125,  125,  125,
      125,  125,  125,  125,  125,  125,  118,  129,  130,  129,
      129,  130,  129,  407,  261,  131,  261,  262,  131,  262,

      177,  178,  177,  406,  177,  178,  177,  204,  265,  266,
      205,  204,  132,  179,  205,  132,  134,  179,  405,  135,
      273,  274,  136,  137,  138,  180,  139,  400,  140,  180,
      190,  276,  277,  276,  191,  280,  281,  285,  286,  278,
      141,  134,  192,  284,  135,  399,  205,  136,  137,  138,
      202,  139,  202,  140,  193,  420,  279,  290,  420,  290,
      204,  301,  302,  205,  394,  141,  142,  143,  144,  143,
      142,  145,  146,  142,  147,  142,  142,  142,  142,  142,
      148,  142,  142,  142,  142,  142,  142,  149,  149,  149,
      149,  149,  150,  149,  149,  149,  149,  149,  149,  149,

      149,  149,  149,  149,  149,  149,  149,  149,  149,  149,
      149,  149,  149,  149,  142,  151,  152,  153,  152,  154,
      155,  151,  156,  157,  151,  151,  151,  151,  151,  158,
      159,  160,  160,  161,  162,  163,  164,  164,  164,  164,
      164,  165,  164,  164,  164,  164,  166,  164,  164,  164,
      164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
      164,  164,  164,  167,  168,  169,  170,  169,  168,  168,
      168,  168,  168,  168,  168,  168,  168,  168,  171,  168,
      168,  168,  168,  168,  168,  172,  172,  172,  172,  172,
      173,  172,  172,  172,  172,  172,  172,  172,  172,  172,

      172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
      172,  172,  168,   78,   79,   78,  310,   80,  310,  194,
      190,  194,  304,  305,  195,  306,  307,  181,  181,  181,
      204,  393,  192,  205,  312,  313,  316,  317,   84,   78,
       79,   78,  377,   80,  193,  315,  204,  315,  392,  205,
      318,  318,  318,  181,  181,  181,  319,  204,  319,  390,
      205,  322,  323,  377,   84,  182,  183,  184,  183,  182,
      182,  182,  182,  182,  185,  185,  182,  182,  182,  186,
      182,  182,  182,  182,  182,  182,  187,  187,  187,  187,
      187,  188,  187,  187,  187,  187,  187,  187,  187,  187,

      187,  187,  187,  187,  187,  187,  187,  187,  187,  187,
      187,  187,  187,  182,   52,  196,  197,  196,   52,  198,
       52,   52,   52,   52,   52,   52,   52,  389,  200,  298,
      298,  298,   52,   52,   52,  388,  204,  299,  300,  205,
      325,  326,  327,  328,  327,  328,  204,  210,  202,  330,
      202,  299,  420,  333,  420,  420,  209,  420,  209,  300,
      334,  210,   52,   52,  196,  197,  196,   52,  198,   52,
       52,   52,   52,   52,   52,   52,  250,  200,  338,  251,
      338,   52,   52,   52,  224,  227,  225,  228,  239,  226,
      239,  387,  252,  229,  252,  347,  261,  347,  261,  262,

      420,  262,  349,  420,  349,  276,  277,  276,  339,  280,
      281,   52,  211,  278,  211,  420,  340,  420,  420,  290,
      420,  290,  354,  358,  310,  315,  310,  315,  386,  385,
      279,  318,  318,  318,  382,  381,  354,  358,  373,  212,
      212,  212,  213,  214,  212,  212,  212,  215,  216,  212,
      212,  212,  217,  212,  212,  212,  218,  219,  212,  420,
      294,  420,  293,  293,  294,  372,  350,  351,  376,  371,
      295,  296,  319,  327,  319,  327,  370,  328,  210,  328,
      350,  338,  376,  338,  295,  347,  377,  347,  351,  212,
      212,  349,  296,  349,  212,  212,  377,  352,  353,  212,

      377,  369,  368,  212,  212,  270,  270,  367,  270,  270,
      377,  352,  270,  380,  270,  270,  270,  270,  270,  353,
      298,  298,  298,  270,  270,  270,  356,  357,  299,  300,
      374,  375,  270,  380,  380,  380,  366,  378,  379,  383,
      356,  383,  299,  384,  374,  384,  365,  380,  357,  380,
      300,  378,  375,  270,  267,  267,  395,  267,  267,  379,
      391,  267,  391,  267,  267,  267,  267,  267,  395,  395,
      396,  397,  267,  267,  267,  398,  383,  395,  383,  364,
      363,  275,  395,  395,  396,  384,  401,  384,  401,  398,
      410,  402,  397,  402,  403,  404,  403,  404,  391,  408,

      391,  408,  267,  287,  362,  361,  289,  287,  325,  287,
      287,  410,  287,  287,  410,  287,  287,  410,  287,  287,
      287,  287,  287,  287,  412,  401,  412,  401,  410,  322,
      402,  410,  402,  403,  404,  403,  404,  413,  415,  413,
      415,  408,  412,  408,  412,  413,  418,  413,  418,  316,
      312,  287,  293,  293,  294,  415,  419,  415,  419,  359,
      295,  296,  297,  418,  419,  418,  419,  355,  306,  355,
      285,  280,  273,  265,  295,  348,  259,  255,  253,  244,
      240,  237,  296,  345,  297,  270,  270,  344,  270,  270,
      343,  342,  270,  341,  270,  270,  270,  270,  270,  337,

      336,  335,  332,  270,  270,  270,  221,  233,  232,  231,
      230,  223,  270,  207,  309,  308,  303,  292,  291,  268,
      263,  253,  420,  233,  232,  231,  230,  223,  220,  420,
      175,  175,  420,  270,  331,  331,  420,  331,  331,  331,
      331,  331,  331,  331,  331,  331,  331,  420,  420,  420,
      420,  420,  331,  331,  331,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  331,  331,  331,  420,  331,  331,  331,  331,
      331,  331,  331,  331,  331,  331,  420,  420,  420,  420,

      420,  331,  331,  331,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  331,   73,   73,   73,   73,   73,   73,   73,   73,
       73,   73,   73,   73,   73,   73,   73,   73,   73,   85,
       85,   85,   85,   85,   85,   85,   85,   85,   85,   85,
       85,   85,   85,   85,   85,   85,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   92,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,

       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,  103,  103,  103,
      103,  103,  103,  103,  103,  103,  103,  103,  103,  103,
      103,  103,  103,  103,  128,  128,  128,  128,  128,  128,
      128,  128,  128,  128,  128,  128,  128,  128,  128,  128,
      128,  133,  133,  133,  133,  133,  133,  133,  133,  133,
      133,  133,  133,  133,  133,  133,  133,  133,  174,  174,
      174,  174,  174,  174,  174,  174,  174,  174,  174,  174,
      174,  174,  174,  174,  174,  176,  176,  176,  176,  176,
      176,  176,  176,  176,  176,  176,  176,  176,  176,  176,

      176,  176,   77,   77,   77,   77,   77,   77,   77,   77,
       77,   77,   77,   77,   77,   77,   77,   77,   77,  189,
      189,  189,  189,  189,  189,  189,  189,  189,  189,  189,
      189,  189,  189,  189,  189,  189,  199,  199,  199,  199,
      199,  199,  199,  199,  199,  199,  199,  199,  199,  199,
      199,  199,  199,  201,  420,  420,  420,  420,  420,  201,
      201,  201,  201,  420,  201,  201,  203,  420,  420,  420,
      203,  420,  203,  203,  203,  203,  203,  203,  203,  206,
      420,  206,  206,  420,  206,  234,  234,  420,  234,  234,
      234,  234,  234,  234,  234,  420,  234,  234,  234,  234,

      420,  234,  235,  235,  235,  235,  235,  235,  235,  235,
      235,  235,  235,  235,  235,  235,  235,  235,  235,  242,
      242,  420,  420,  242,  242,  242,  242,  242,  242,  242,
      242,  242,  242,  242,  420,  242,  243,  243,  243,  243,
      243,  243,  243,  243,  243,  243,  243,  243,  243,  243,
      243,  243,  243,  246,  246,  420,  246,  246,  420,  246,
      246,  246,  246,  246,  246,  246,  246,  246,  420,  246,
      247,  247,  420,  247,  247,  247,  247,  247,  247,  247,
      247,  247,  420,  247,  247,  420,  247,  248,  248,  420,
      248,  248,  248,  248,  248,  420,  248,  248,  248,  248,

      248,  248,  248,  248,  249,  249,  420,  249,  249,  249,
      249,  249,  249,  249,  249,  249,  249,  249,  249,  249,
      249,  254,  420,  420,  420,  420,  254,  420,  254,  254,
      420,  254,  257,  257,  420,  420,  257,  420,  257,  257,
      257,  257,  420,  257,  257,  257,  257,  420,  257,  258,
      258,  258,  258,  258,  258,  258,  258,  258,  258,  258,
      258,  258,  258,  258,  258,  258,  264,  420,  264,  264,
      420,  264,  267,  267,  420,  420,  420,  420,  267,  267,
      267,  267,  420,  420,  267,  420,  420,  420,  267,  269,
      269,  420,  420,  420,  420,  269,  269,  269,  269,  269,

      269,  269,  269,  269,  420,  269,  271,  271,  420,  420,
      420,  420,  271,  271,  271,  271,  420,  271,  271,  271,
      271,  420,  271,  272,  420,  272,  272,  420,  272,  282,
      282,  420,  420,  282,  420,  420,  420,  282,  282,  420,
      282,  282,  282,  282,  420,  282,  283,  283,  420,  420,
      283,  420,  420,  420,  283,  283,  283,  283,  283,  283,
      283,  420,  283,  288,  288,  288,  288,  288,  288,  288,
      288,  288,  288,  288,  288,  288,  288,  288,  288,  288,
      311,  420,  311,  311,  420,  311,  314,  314,  420,  314,
      314,  314,  314,  314,  314,  314,  314,  314,  314,  314,

      314,  314,  314,  320,  420,  320,  320,  420,  320,  321,
      321,  321,  321,  321,  321,  321,  321,  321,  321,  321,
      321,  321,  321,  321,  321,  321,  324,  324,  420,  324,
      420,  324,  324,  324,  324,  324,  420,  324,  324,  324,
      324,  420,  324,  329,  329,  329,  420,  329,  329,  329,
      329,  331,  331,  420,  331,  331,  331,  331,  331,  331,
      331,  331,  331,  331,  331,  331,  331,  331,  346,  346,
      420,  346,  346,  346,  346,  346,  420,  346,  420,  346,
      346,  346,  346,  420,  346,  270,  270,  420,  420,  420,
      420,  270,  270,  270,  270,  420,  420,  270,  420,  420,

      420,  270,  360,  360,  420,  360,  360,  360,  360,  360,
      360,  360,  360,  360,  360,  360,  360,  360,  360,   47,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420
    } ;

static const flex_int16_t yy_chk[1970] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    2,
       49,    2,   49,  411,    2,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,    3,    3,    3,    3,

        3,    3,    3,    3,    5,    6,   15,   54,  409,   16,
       54,   56,   56,   64,   64,   15,    5,    6,   16,   57,
       56,   57,   63,   56,   57,   63,   76,   76,    5,    6,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    9,
       10,   11,    9,   10,   12,  406,   75,   11,   13,   14,
       12,   67,   68,   67,   68,   75,   67,   78,   75,   78,

       68,   84,   84,    9,   10,   11,   13,   14,   12,   98,
       75,   98,   13,   14,   17,   17,   17,   17,   17,   17,
       17,   17,   17,   17,   17,   17,   17,   17,   17,   17,
       17,   17,   17,   17,   17,   17,   17,   17,   17,   17,
       17,   17,   17,   17,   17,   17,   17,   17,   17,   17,
       17,   17,   17,   17,   17,   17,   17,   17,   17,   17,
       17,   17,   17,   19,   20,   81,   19,   20,   81,   19,
       20,   88,   88,  399,   96,   19,   20,   96,  100,  102,
      102,  100,  107,  108,  108,  107,  394,   19,   20,   21,
       21,   21,   21,   21,   21,   21,   21,   21,   21,   21,

       21,   21,   21,   21,   21,   21,   21,   21,   21,   21,
       21,   21,   21,   21,   21,   21,   21,   21,   21,   21,
       21,   21,   21,   21,   21,   21,   21,   21,   21,   21,
       21,   21,   21,   21,   21,   21,   21,   21,   23,   23,
       23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
       23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
       23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
       23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
       23,   23,   23,   23,   23,   23,   23,   25,   25,   25,
       26,   26,   26,  392,  110,   25,  110,  112,   26,  112,

       37,   37,   37,  390,   38,   38,   38,  115,  117,  117,
      115,  123,   25,   37,  123,   26,   27,   38,  389,   27,
      126,  126,   27,   27,   27,   37,   27,  382,   27,   38,
       43,  129,  129,  129,   43,  132,  132,  141,  141,  129,
       27,   28,   43,  140,   28,  381,  140,   28,   28,   28,
      143,   28,  143,   28,   43,  148,  129,  152,  148,  152,
      158,  161,  161,  158,  373,   28,   29,   29,   29,   29,
       29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
       29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
       29,   29,   29,   29,   29,   29,   29,   29,   29,   29,

       29,   29,   29,   29,   29,   29,   29,   29,   29,   29,
       29,   29,   29,   29,   29,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   33,   33,   33,   33,   33,   33,
       33,   33,   33,   33,   33,   33,   33,   33,   33,   33,
       33,   33,   33,   33,   33,   33,   33,   33,   33,   33,
       33,   33,   33,   33,   33,   33,   33,   33,   33,   33,

       33,   33,   33,   33,   33,   33,   33,   33,   33,   33,
       33,   33,   33,   39,   39,   39,  169,   39,  169,   44,
       44,   44,  163,  163,   44,  165,  165,   39,   39,   39,
      171,  372,   44,  171,  173,  173,  180,  180,   39,   40,
       40,   40,  352,   40,   44,  177,  179,  177,  371,  179,
      181,  181,  181,   40,   40,   40,  183,  186,  183,  369,
      186,  188,  188,  352,   40,   41,   41,   41,   41,   41,
       41,   41,   41,   41,   41,   41,   41,   41,   41,   41,
       41,   41,   41,   41,   41,   41,   41,   41,   41,   41,
       41,   41,   41,   41,   41,   41,   41,   41,   41,   41,

       41,   41,   41,   41,   41,   41,   41,   41,   41,   41,
       41,   41,   41,   41,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,  368,   45,  160,
      160,  160,   45,   45,   45,  367,  192,  160,  160,  192,
      193,  193,  194,  196,  194,  196,  200,  194,  202,  200,
      202,  160,  203,  224,  249,  203,  209,  249,  209,  160,
      224,  209,   45,   46,   46,   46,   46,   46,   46,   46,
       46,   46,   46,   46,   46,   46,  250,   46,  228,  250,
      228,   46,   46,   46,  214,  215,  214,  215,  239,  214,
      239,  366,  252,  215,  252,  259,  261,  259,  261,  262,

      269,  262,  273,  269,  273,  276,  276,  276,  228,  279,
      279,   46,   60,  276,   60,  283,  228,  287,  283,  290,
      287,  290,  296,  300,  310,  315,  310,  315,  365,  364,
      276,  318,  318,  318,  361,  359,  296,  300,  345,   60,
       60,   60,   60,   60,   60,   60,   60,   60,   60,   60,
       60,   60,   60,   60,   60,   60,   60,   60,   60,   65,
      294,   65,  293,  293,  293,  344,  294,  294,  351,  343,
      293,  293,  319,  327,  319,  327,  342,  328,  327,  328,
      294,  338,  351,  338,  293,  347,  353,  347,  294,   65,
       65,  349,  293,  349,   65,   65,  354,  295,  295,   65,

      353,  341,  340,   65,   65,  124,  124,  339,  124,  124,
      354,  295,  124,  356,  124,  124,  124,  124,  124,  295,
      298,  298,  298,  124,  124,  124,  299,  299,  298,  298,
      350,  350,  124,  357,  356,  358,  337,  355,  355,  362,
      299,  362,  298,  363,  350,  363,  336,  357,  299,  358,
      298,  355,  350,  124,  127,  127,  374,  127,  127,  355,
      370,  127,  370,  127,  127,  127,  127,  127,  375,  376,
      378,  378,  127,  127,  127,  379,  383,  374,  383,  335,
      334,  127,  375,  376,  378,  384,  385,  384,  385,  379,
      396,  386,  378,  386,  387,  388,  387,  388,  391,  393,

      391,  393,  127,  150,  333,  332,  150,  150,  326,  150,
      150,  396,  150,  150,  397,  150,  150,  398,  150,  150,
      150,  150,  150,  150,  400,  401,  400,  401,  397,  323,
      402,  398,  402,  403,  404,  403,  404,  405,  407,  405,
      407,  408,  412,  408,  412,  413,  414,  413,  414,  317,
      313,  150,  159,  159,  159,  415,  416,  415,  416,  308,
      159,  159,  159,  418,  419,  418,  419,  465,  307,  465,
      286,  281,  274,  266,  159,  263,  260,  256,  254,  245,
      241,  238,  159,  233,  159,  271,  271,  232,  271,  271,
      231,  230,  271,  229,  271,  271,  271,  271,  271,  227,

      226,  225,  223,  271,  271,  271,  222,  219,  218,  217,
      216,  213,  271,  208,  167,  166,  162,  156,  154,  121,
      114,  101,   91,   72,   71,   70,   69,   66,   62,   47,
       36,   35,    0,  271,  330,  330,    0,  330,  330,  330,
      330,  330,  330,  330,  330,  330,  330,    0,    0,    0,
        0,    0,  330,  330,  330,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,  330,  360,  360,    0,  360,  360,  360,  360,
      360,  360,  360,  360,  360,  360,    0,    0,    0,    0,

        0,  360,  360,  360,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,  360,  421,  421,  421,  421,  421,  421,  421,  421,
      421,  421,  421,  421,  421,  421,  421,  421,  421,  422,
      422,  422,  422,  422,  422,  422,  422,  422,  422,  422,
      422,  422,  422,  422,  422,  422,  423,  423,  423,  423,
      423,  423,  423,  423,  423,  423,  423,  423,  423,  423,
      423,  423,  423,  424,  424,  424,  424,  424,  424,  424,
      424,  424,  424,  424,  424,  424,  424,  424,  424,  424,

      425,  425,  425,  425,  425,  425,  425,  425,  425,  425,
      425,  425,  425,  425,  425,  425,  425,  426,  426,  426,
      426,  426,  426,  426,  426,  426,  426,  426,  426,  426,
      426,  426,  426,  426,  427,  427,  427,  427,  427,  427,
      427,  427,  427,  427,  427,  427,  427,  427,  427,  427,
      427,  428,  428,  428,  428,  428,  428,  428,  428,  428,
      428,  428,  428,  428,  428,  428,  428,  428,  429,  429,
      429,  429,  429,  429,  429,  429,  429,  429,  429,  429,
      429,  429,  429,  429,  429,  430,  430,  430,  430,  430,
      430,  430,  430,  430,  430,  430,  430,  430,  430,  430,

      430,  430,  431,  431,  431,  431,  431,  431,  431,  431,
      431,  431,  431,  431,  431,  431,  431,  431,  431,  432,
      432,  432,  432,  432,  432,  432,  432,  432,  432,  432,
      432,  432,  432,  432,  432,  432,  433,  433,  433,  433,
      433,  433,  433,  433,  433,  433,  433,  433,  433,  433,
      433,  433,  433,  434,    0,    0,    0,    0,    0,  434,
      434,  434,  434,    0,  434,  434,  435,    0,    0,    0,
      435,    0,  435,  435,  435,  435,  435,  435,  435,  436,
        0,  436,  436,    0,  436,  437,  437,    0,  437,  437,
      437,  437,  437,  437,  437,    0,  437,  437,  437,  437,

        0,  437,  438,  438,  438,  438,  438,  438,  438,  438,
      438,  438,  438,  438,  438,  438,  438,  438,  438,  439,
      439,    0,    0,  439,  439,  439,  439,  439,  439,  439,
      439,  439,  439,  439,    0,  439,  440,  440,  440,  440,
      440,  440,  440,  440,  440,  440,  440,  440,  440,  440,
      440,  440,  440,  441,  441,    0,  441,  441,    0,  441,
      441,  441,  441,  441,  441,  441,  441,  441,    0,  441,
      442,  442,    0,  442,  442,  442,  442,  442,  442,  442,
      442,  442,    0,  442,  442,    0,  442,  443,  443,    0,
      443,  443,  443,  443,  443,    0,  443,  443,  443,  443,

      443,  443,  443,  443,  444,  444,    0,  444,  444,  444,
      444,  444,  444,  444,  444,  444,  444,  444,  444,  444,
      444,  445,    0,    0,    0,    0,  445,    0,  445,  445,
        0,  445,  446,  446,    0,    0,  446,    0,  446,  446,
      446,  446,    0,  446,  446,  446,  446,    0,  446,  447,
      447,  447,  447,  447,  447,  447,  447,  447,  447,  447,
      447,  447,  447,  447,  447,  447,  448,    0,  448,  448,
        0,  448,  449,  449,    0,    0,    0,    0,  449,  449,
      449,  449,    0,    0,  449,    0,    0,    0,  449,  450,
      450,    0,    0,    0,    0,  450,  450,  450,  450,  450,

      450,  450,  450,  450,    0,  450,  451,  451,    0,    0,
        0,    0,  451,  451,  451,  451,    0,  451,  451,  451,
      451,    0,  451,  452,    0,  452,  452,    0,  452,  453,
      453,    0,    0,  453,    0,    0,    0,  453,  453,    0,
      453,  453,  453,  453,    0,  453,  454,  454,    0,    0,
      454,    0,    0,    0,  454,  454,  454,  454,  454,  454,
      454,    0,  454,  455,  455,  455,  455,  455,  455,  455,
      455,  455,  455,  455,  455,  455,  455,  455,  455,  455,
      456,    0,  456,  456,    0,  456,  457,  457,    0,  457,
      457,  457,  457,  457,  457,  457,  457,  457,  457,  457,

      457,  457,  457,  458,    0,  458,  458,    0,  458,  459,
      459,  459,  459,  459,  459,  459,  459,  459,  459,  459,
      459,  459,  459,  459,  459,  459,  460,  460,    0,  460,
        0,  460,  460,  460,  460,  460,    0,  460,  460,  460,
      460,    0,  460,  461,  461,  461,    0,  461,  461,  461,
      461,  462,  462,    0,  462,  462,  462,  462,  462,  462,
      462,  462,  462,  462,  462,  462,  462,  462,  463,  463,
        0,  463,  463,  463,  463,  463,    0,  463,    0,  463,
      463,  463,  463,    0,  463,  464,  464,    0,    0,    0,
        0,  464,  464,  464,  464,    0,    0,  464,    0,    0,

        0,  464,  466,  466,    0,  466,  466,  466,  466,  466,
      466,  466,  466,  466,  466,  466,  466,  466,  466,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420,  420,
      420,  420,  420,  420,  420,  420,  420,  420,  420
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
#line 1 "tools/wrc/ppl.l"
/* -*-C-*-
 * Wrc preprocessor lexical analysis
 *
 * Copyright 1999-2000	Bertho A. Stultiens (BS)
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 *-------------------------------------------------------------------------
 * The preprocessor's lexographical grammar (approximately):
 *
 * pp		:= {ws} # {ws} if {ws} {expr} {ws} \n
 *		|  {ws} # {ws} ifdef {ws} {id} {ws} \n
 *		|  {ws} # {ws} ifndef {ws} {id} {ws} \n
 *		|  {ws} # {ws} elif {ws} {expr} {ws} \n
 *		|  {ws} # {ws} else {ws} \n
 *		|  {ws} # {ws} endif {ws} \n
 *		|  {ws} # {ws} include {ws} < {anytext} > \n
 *		|  {ws} # {ws} include {ws} " {anytext} " \n
 *		|  {ws} # {ws} define {ws} {anytext} \n
 *		|  {ws} # {ws} define( {arglist} ) {ws} {expansion} \n
 *		|  {ws} # {ws} pragma {ws} {anytext} \n
 *		|  {ws} # {ws} ident {ws} {anytext} \n
 *		|  {ws} # {ws} error {ws} {anytext} \n
 *		|  {ws} # {ws} warning {ws} {anytext} \n
 *		|  {ws} # {ws} line {ws} " {anytext} " {number} \n
 *		|  {ws} # {ws} {number} " {anytext} " {number} [ {number} [{number}] ] \n
 *		|  {ws} # {ws} \n
 *
 * ws		:= [ \t\r\f\v]*
 *
 * expr		:= {expr} [+-*%^/|&] {expr}
 *		|  {expr} {logor|logand} {expr}
 *		|  [!~+-] {expr}
 *		|  {expr} ? {expr} : {expr}
 *
 * logor	:= ||
 *
 * logand	:= &&
 *
 * id		:= [a-zA-Z_][a-zA-Z0-9_]*
 *
 * anytext	:= [^\n]*	(see note)
 *
 * arglist	:=
 *		|  {id}
 *		|  {arglist} , {id}
 *		|  {arglist} , {id} ...
 *
 * expansion	:= {id}
 *		|  # {id}
 *		|  {anytext}
 *		|  {anytext} ## {anytext}
 *
 * number	:= [0-9]+
 *
 * Note: "anytext" is not always "[^\n]*". This is because the
 *	 trailing context must be considered as well.
 *
 * The only certain assumption for the preprocessor to make is that
 * directives start at the beginning of the line, followed by a '#'
 * and end with a newline.
 * Any directive may be suffixed with a line-continuation. Also
 * classical comment / *...* / (note: no comments within comments,
 * therefore spaces) is considered to be a line-continuation
 * (according to gcc and egcs AFAIK, ANSI is a bit vague).
 * Comments have not been added to the above grammar for simplicity
 * reasons. However, it is allowed to enter comment anywhere within
 * the directives as long as they do not interfere with the context.
 * All comments are considered to be deletable whitespace (both
 * classical form "/ *...* /" and C++ form "//...\n").
 *
 * All recursive scans, except for macro-expansion, are done by the
 * parser, whereas the simple state transitions of non-recursive
 * directives are done in the scanner. This results in the many
 * exclusive start-conditions of the scanner.
 *
 * Macro expansions are slightly more difficult because they have to
 * prescan the arguments. Parameter substitution is literal if the
 * substitution is # or ## (either side). This enables new identifiers
 * to be created (see 'info cpp' node Macro|Pitfalls|Prescan for more
 * information).
 *
 * FIXME: Variable macro parameters is recognized, but not yet
 * expanded. I have to reread the ANSI standard on the subject (yes,
 * ANSI defines it).
 *
 * The following special defines are supported:
 * __FILE__	-> "thissource.c"
 * __LINE__	-> 123
 * __DATE__	-> "May  1 2000"
 * __TIME__	-> "23:59:59"
 * These macros expand, as expected, into their ANSI defined values.
 *
 * The same include prevention is implemented as gcc and egcs does.
 * This results in faster processing because we do not read the text
 * at all. Some wine-sources attempt to include the same file 4 or 5
 * times. This strategy also saves a lot blank output-lines, which in
 * its turn improves the real resource scanner/parser.
 *
 */

/*
 * Special flex options and exclusive scanner start-conditions
 */
#define YY_NO_INPUT 1






















#line 154 "tools/wrc/ppl.l"
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <assert.h>
#include <errno.h>
#include <limits.h>

#ifndef LLONG_MAX
# define LLONG_MAX  ((__int64)0x7fffffff << 32 | 0xffffffff)
# define LLONG_MIN  (-LLONG_MAX - 1)
#endif
#ifndef ULLONG_MAX
# define ULLONG_MAX ((__int64)0xffffffff << 32 | 0xffffffff)
#endif

#define YY_NO_UNISTD_H

#include "../tools.h"
#include "utils.h"
#include "wpp_private.h"
#include "ppy.tab.h"

/*
 * Make sure that we are running an appropriate version of flex.
 */
#if !defined(YY_FLEX_MAJOR_VERSION) || (1000 * YY_FLEX_MAJOR_VERSION + YY_FLEX_MINOR_VERSION < 2005)
#error Must use flex version 2.5.1 or higher (yy_scan_* routines are required).
#endif

#define YY_READ_BUF_SIZE	65536		/* So we read most of a file at once */

#define yy_current_state()	YY_START
#define yy_pp_state(x)		yy_pop_state(); yy_push_state(x)

/*
 * Always update the current character position within a line
 */
#define YY_USER_ACTION	pp_status.char_number+=ppy_leng;

/*
 * Buffer management for includes and expansions
 */
#define MAXBUFFERSTACK	128	/* Nesting more than 128 includes or macro expansion textss is insane */

typedef struct bufferstackentry {
	YY_BUFFER_STATE	bufferstate;	/* Buffer to switch back to */
	FILE		*file;          /* File handle */
	pp_entry_t	*define;	/* Points to expanding define or NULL if handling includes */
	int		line_number;	/* Line that we were handling */
	int		char_number;	/* The current position on that line */
	char		*filename;	/* Filename that we were handling */
	int		if_depth;	/* How many #if:s deep to check matching #endif:s */
	int		ncontinuations;	/* Remember the continuation state */
	int		should_pop;	/* Set if we must pop the start-state on EOF */
	/* Include management */
        include_state_t incl;
	char 		*include_filename;
} bufferstackentry_t;

#define ALLOCBLOCKSIZE	(1 << 10)	/* Allocate these chunks at a time for string-buffers */

/*
 * Macro expansion nesting
 * We need the stack to handle expansions while scanning
 * a macro's arguments. The TOS must always be the macro
 * that receives the current expansion from the scanner.
 */
#define MAXMACEXPSTACK	128	/* Nesting more than 128 macro expansions is insane */

typedef struct macexpstackentry {
	pp_entry_t	*ppp;		/* This macro we are scanning */
	char		**args;		/* With these arguments */
	char		**ppargs;	/* Resulting in these preprocessed arguments */
	int		*nnls;		/* Number of newlines per argument */
	int		nargs;		/* And this many arguments scanned */
	int		parentheses;	/* Nesting level of () */
	int		curargsize;	/* Current scanning argument's size */
	int		curargalloc;	/* Current scanning argument's block allocated */
	char		*curarg;	/* Current scanning argument's content */
} macexpstackentry_t;

#define MACROPARENTHESES()	(top_macro()->parentheses)

/*
 * Prototypes
 */
static void newline(int);
static int make_number(int radix, PPY_STYPE *val, const char *str, int len);
static void put_buffer(const char *s, int len);
/* Buffer management */
static void push_buffer(pp_entry_t *ppp, char *filename, char *incname, int pop);
static bufferstackentry_t *pop_buffer(void);
/* String functions */
static void new_string(void);
static void add_string(const char *str, int len);
static char *get_string(void);
static void put_string(void);
static int string_start(void);
/* Macro functions */
static void push_macro(pp_entry_t *ppp);
static macexpstackentry_t *top_macro(void);
static macexpstackentry_t *pop_macro(void);
static void free_macro(macexpstackentry_t *mep);
static void add_text_to_macro(const char *text, int len);
static void macro_add_arg(int last);
static void macro_add_expansion(void);
/* Expansion */
static void expand_special(pp_entry_t *ppp);
static void expand_define(pp_entry_t *ppp);
static void expand_macro(macexpstackentry_t *mep);

/*
 * Local variables
 */
static int ncontinuations;

static int strbuf_idx = 0;
static int strbuf_alloc = 0;
static char *strbuffer = NULL;
static int str_startline;

static macexpstackentry_t *macexpstack[MAXMACEXPSTACK];
static int macexpstackidx = 0;

static bufferstackentry_t bufferstack[MAXBUFFERSTACK];
static int bufferstackidx = 0;

/*
 * Global variables
 */
include_state_t pp_incl_state =
{
    -1,    /* state */
    NULL,  /* ppp */
    0,     /* ifdepth */
    0      /* seen_junk */
};

static struct list pp_includelogiclist = LIST_INIT( pp_includelogiclist );

#define YY_INPUT(buf,result,max_size)					     \
	{								     \
		result = fread(buf, 1, max_size, pp_status.file);	     \
	}

#line 1567 "tools/wrc/ppl.yy.c"
/*
 **************************************************************************
 * The scanner starts here
 **************************************************************************
 */
#line 1573 "tools/wrc/ppl.yy.c"

#define INITIAL 0
#define pp_pp 1
#define pp_eol 2
#define pp_inc 3
#define pp_dqs 4
#define pp_sqs 5
#define pp_iqs 6
#define pp_comment 7
#define pp_def 8
#define pp_define 9
#define pp_macro 10
#define pp_mbody 11
#define pp_macign 12
#define pp_macscan 13
#define pp_macexp 14
#define pp_if 15
#define pp_ifd 16
#define pp_ifignored 17
#define pp_endif 18
#define pp_line 19
#define pp_defined 20
#define pp_ignore 21
#define RCINCL 22

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals ( void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( void );

int yyget_debug ( void );

void yyset_debug ( int debug_flag  );

YY_EXTRA_TYPE yyget_extra ( void );

void yyset_extra ( YY_EXTRA_TYPE user_defined  );

FILE *yyget_in ( void );

void yyset_in  ( FILE * _in_str  );

FILE *yyget_out ( void );

void yyset_out  ( FILE * _out_str  );

			int yyget_leng ( void );

char *yyget_text ( void );

int yyget_lineno ( void );

void yyset_lineno ( int _line_number  );

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( void );
#else
extern int yywrap ( void );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( void );
#else
static int input ( void );
#endif

#endif

        static int yy_start_stack_ptr = 0;
        static int yy_start_stack_depth = 0;
        static int *yy_start_stack = NULL;
    
    static void yy_push_state ( int _new_state );
    
    static void yy_pop_state ( void );
    
    static int yy_top_state ( void );
    
/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex (void);

#define YY_DECL int yylex (void)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	if ( yyleng > 0 ) \
		YY_CURRENT_BUFFER_LVALUE->yy_at_bol = \
				(yytext[yyleng - 1] == '\n'); \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
#line 309 "tools/wrc/ppl.l"

#line 311 "tools/wrc/ppl.l"
	/*
	 * Catch line-continuations.
	 * Note: Gcc keeps the line-continuations in, for example, strings
	 * intact. However, I prefer to remove them all so that the next
	 * scanner will not need to reduce the continuation state.
	 *
	 * <*>\\\n		newline(0);
	 */

	/*
	 * Detect the leading # of a preprocessor directive.
	 */
#line 1838 "tools/wrc/ppl.yy.c"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);
		yy_current_state += YY_AT_BOL();
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 421 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_current_state != 420 );
		yy_cp = (yy_last_accepting_cpos);
		yy_current_state = (yy_last_accepting_state);

yy_find_action:
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 323 "tools/wrc/ppl.l"
pp_incl_state.seen_junk++; yy_push_state(pp_pp);
	YY_BREAK
/*
	 * Scan for the preprocessor directives
	 */
case 2:
YY_RULE_SETUP
#line 328 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) {yy_pp_state(pp_inc); return tINCLUDE;} else {yy_pp_state(pp_eol);}
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 329 "tools/wrc/ppl.l"
yy_pp_state(yy_current_state() != pp_ignore ? pp_def : pp_eol);
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 330 "tools/wrc/ppl.l"
yy_pp_state(pp_eol);	if(yy_top_state() != pp_ignore) return tERROR;
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 331 "tools/wrc/ppl.l"
yy_pp_state(pp_eol);	if(yy_top_state() != pp_ignore) return tWARNING;
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 332 "tools/wrc/ppl.l"
yy_pp_state(pp_eol);	if(yy_top_state() != pp_ignore) return tPRAGMA;
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 333 "tools/wrc/ppl.l"
yy_pp_state(pp_eol);	if(yy_top_state() != pp_ignore) return tPPIDENT;
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 334 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) {yy_pp_state(pp_ifd); return tUNDEF;} else {yy_pp_state(pp_eol);}
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 335 "tools/wrc/ppl.l"
yy_pp_state(pp_ifd);	return tIFDEF;
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 336 "tools/wrc/ppl.l"
pp_incl_state.seen_junk--; yy_pp_state(pp_ifd);	return tIFNDEF;
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 337 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) {yy_pp_state(pp_if);} else {yy_pp_state(pp_ifignored);} return tIF;
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 338 "tools/wrc/ppl.l"
yy_pp_state(pp_if);	return tELIF;
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 339 "tools/wrc/ppl.l"
yy_pp_state(pp_endif);  return tELSE;
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 340 "tools/wrc/ppl.l"
yy_pp_state(pp_endif);  return tENDIF;
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 341 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) {yy_pp_state(pp_line); return tLINE;} else {yy_pp_state(pp_eol);}
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 342 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) {yy_pp_state(pp_line); return tGCCLINE;} else {yy_pp_state(pp_eol);}
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 343 "tools/wrc/ppl.l"
ppy_error("Invalid preprocessor token '%s'", ppy_text);
	YY_BREAK
case 18:
/* rule 18 can match eol */
YY_RULE_SETUP
#line 344 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;	/* This could be the null-token */
	YY_BREAK
case 19:
/* rule 19 can match eol */
YY_RULE_SETUP
#line 345 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 346 "tools/wrc/ppl.l"
ppy_error("Preprocessor junk '%s'", ppy_text);
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 347 "tools/wrc/ppl.l"
return *ppy_text;
	YY_BREAK
/*
	 * Handle #include and #line
	 */
case 22:
YY_RULE_SETUP
#line 352 "tools/wrc/ppl.l"
return make_number(10, &ppy_lval, ppy_text, ppy_leng);
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 353 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_iqs);
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 354 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_dqs);
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 355 "tools/wrc/ppl.l"
;
	YY_BREAK
case 26:
/* rule 26 can match eol */
YY_RULE_SETUP
#line 356 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
case 27:
/* rule 27 can match eol */
YY_RULE_SETUP
#line 357 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 358 "tools/wrc/ppl.l"
ppy_error(yy_current_state() == pp_inc ? "Trailing junk in #include" : "Trailing junk in #line");
	YY_BREAK
/*
	 * Ignore all input when a false clause is parsed
	 */
case 29:
YY_RULE_SETUP
#line 363 "tools/wrc/ppl.l"
;
	YY_BREAK
case 30:
/* rule 30 can match eol */
YY_RULE_SETUP
#line 364 "tools/wrc/ppl.l"
newline(1);
	YY_BREAK
case 31:
/* rule 31 can match eol */
YY_RULE_SETUP
#line 365 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 366 "tools/wrc/ppl.l"
;
	YY_BREAK
/*
	 * Handle #if and #elif.
	 * These require conditionals to be evaluated, but we do not
	 * want to jam the scanner normally when we see these tokens.
	 * Note: tIDENT is handled below.
	 */
case 33:
YY_RULE_SETUP
#line 375 "tools/wrc/ppl.l"
return make_number(8, &ppy_lval, ppy_text, ppy_leng);
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 376 "tools/wrc/ppl.l"
ppy_error("Invalid octal digit");
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 377 "tools/wrc/ppl.l"
return make_number(10, &ppy_lval, ppy_text, ppy_leng);
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 378 "tools/wrc/ppl.l"
return make_number(16, &ppy_lval, ppy_text, ppy_leng);
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 379 "tools/wrc/ppl.l"
ppy_error("Invalid hex number");
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 380 "tools/wrc/ppl.l"
yy_push_state(pp_defined); return tDEFINED;
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 381 "tools/wrc/ppl.l"
return tLSHIFT;
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 382 "tools/wrc/ppl.l"
return tRSHIFT;
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 383 "tools/wrc/ppl.l"
return tLOGAND;
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 384 "tools/wrc/ppl.l"
return tLOGOR;
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 385 "tools/wrc/ppl.l"
return tEQ;
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 386 "tools/wrc/ppl.l"
return tNE;
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 387 "tools/wrc/ppl.l"
return tLTE;
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 388 "tools/wrc/ppl.l"
return tGTE;
	YY_BREAK
case 47:
/* rule 47 can match eol */
YY_RULE_SETUP
#line 389 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
case 48:
/* rule 48 can match eol */
YY_RULE_SETUP
#line 390 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 391 "tools/wrc/ppl.l"
ppy_error("Junk in conditional expression");
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 392 "tools/wrc/ppl.l"
;
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 393 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_sqs);
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 394 "tools/wrc/ppl.l"
ppy_error("String constants not allowed in conditionals");
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 395 "tools/wrc/ppl.l"
return *ppy_text;
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 397 "tools/wrc/ppl.l"
ppy_lval.sint = 0; return tSINT;
	YY_BREAK
case 55:
/* rule 55 can match eol */
YY_RULE_SETUP
#line 398 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
/*
	 * Handle #ifdef, #ifndef and #undef
	 * to get only an untranslated/unexpanded identifier
	 */
case 56:
YY_RULE_SETUP
#line 404 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tIDENT;
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 405 "tools/wrc/ppl.l"
;
	YY_BREAK
case 58:
/* rule 58 can match eol */
YY_RULE_SETUP
#line 406 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
case 59:
/* rule 59 can match eol */
YY_RULE_SETUP
#line 407 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 408 "tools/wrc/ppl.l"
ppy_error("Identifier expected");
	YY_BREAK
/*
	 * Handle #else and #endif.
	 */
case 61:
YY_RULE_SETUP
#line 413 "tools/wrc/ppl.l"
;
	YY_BREAK
case 62:
/* rule 62 can match eol */
YY_RULE_SETUP
#line 414 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
case 63:
/* rule 63 can match eol */
YY_RULE_SETUP
#line 415 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 64:
YY_RULE_SETUP
#line 416 "tools/wrc/ppl.l"
ppy_error("Garbage after #else or #endif.");
	YY_BREAK
/*
	 * Handle the special 'defined' keyword.
	 * This is necessary to get the identifier prior to any
	 * substitutions.
	 */
case 65:
YY_RULE_SETUP
#line 423 "tools/wrc/ppl.l"
yy_pop_state(); ppy_lval.cptr = xstrdup(ppy_text); return tIDENT;
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 424 "tools/wrc/ppl.l"
;
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 425 "tools/wrc/ppl.l"
return *ppy_text;
	YY_BREAK
case 68:
/* rule 68 can match eol */
YY_RULE_SETUP
#line 426 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 69:
/* rule 69 can match eol */
YY_RULE_SETUP
#line 427 "tools/wrc/ppl.l"
ppy_error("Identifier expected");
	YY_BREAK
/*
	 * Handle #error, #warning, #pragma and #ident.
	 * Pass everything literally to the parser, which
	 * will act appropriately.
	 * Comments are stripped from the literal text.
	 */
case 70:
YY_RULE_SETUP
#line 435 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) { ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL; }
	YY_BREAK
case 71:
YY_RULE_SETUP
#line 436 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) { ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL; }
	YY_BREAK
case 72:
/* rule 72 can match eol */
YY_RULE_SETUP
#line 437 "tools/wrc/ppl.l"
if(yy_top_state() != pp_ignore) { ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL; }
	YY_BREAK
case 73:
/* rule 73 can match eol */
YY_RULE_SETUP
#line 438 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); if(yy_current_state() != pp_ignore) { return tNL; }
	YY_BREAK
case 74:
/* rule 74 can match eol */
YY_RULE_SETUP
#line 439 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
/*
	 * Handle left side of #define
	 */
case 75:
YY_RULE_SETUP
#line 444 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); ppy_lval.cptr[ppy_leng-1] = '\0'; yy_pp_state(pp_macro);  return tMACRO;
	YY_BREAK
case 76:
YY_RULE_SETUP
#line 445 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); yy_pp_state(pp_define); return tDEFINE;
	YY_BREAK
case 77:
YY_RULE_SETUP
#line 446 "tools/wrc/ppl.l"
;
	YY_BREAK
case 78:
/* rule 78 can match eol */
YY_RULE_SETUP
#line 447 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 79:
/* rule 79 can match eol */
YY_RULE_SETUP
#line 448 "tools/wrc/ppl.l"
perror("Identifier expected");
	YY_BREAK
/*
	 * Scan the substitution of a define
	 */
case 80:
YY_RULE_SETUP
#line 453 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL;
	YY_BREAK
case 81:
/* rule 81 can match eol */
YY_RULE_SETUP
#line 454 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL;
	YY_BREAK
case 82:
/* rule 82 can match eol */
YY_RULE_SETUP
#line 455 "tools/wrc/ppl.l"
newline(0); ppy_lval.cptr = xstrdup(" "); return tLITERAL;
	YY_BREAK
case 83:
/* rule 83 can match eol */
YY_RULE_SETUP
#line 456 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 84:
/* rule 84 can match eol */
YY_RULE_SETUP
#line 457 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
case 85:
YY_RULE_SETUP
#line 458 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_sqs);
	YY_BREAK
case 86:
YY_RULE_SETUP
#line 459 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_dqs);
	YY_BREAK
/*
	 * Scan the definition macro arguments
	 */
case 87:
YY_RULE_SETUP
#line 464 "tools/wrc/ppl.l"
yy_pp_state(pp_mbody); return tMACROEND;
	YY_BREAK
case 88:
YY_RULE_SETUP
#line 465 "tools/wrc/ppl.l"
;
	YY_BREAK
case 89:
YY_RULE_SETUP
#line 466 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tIDENT;
	YY_BREAK
case 90:
YY_RULE_SETUP
#line 467 "tools/wrc/ppl.l"
return ',';
	YY_BREAK
case 91:
YY_RULE_SETUP
#line 468 "tools/wrc/ppl.l"
return tELLIPSIS;
	YY_BREAK
case 92:
/* rule 92 can match eol */
YY_RULE_SETUP
#line 469 "tools/wrc/ppl.l"
ppy_error("Argument identifier expected");
	YY_BREAK
case 93:
/* rule 93 can match eol */
YY_RULE_SETUP
#line 470 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
/*
	 * Scan the substitution of a macro
	 */
case 94:
YY_RULE_SETUP
#line 475 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL;
	YY_BREAK
case 95:
YY_RULE_SETUP
#line 476 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tIDENT;
	YY_BREAK
case 96:
YY_RULE_SETUP
#line 477 "tools/wrc/ppl.l"
return tCONCAT;
	YY_BREAK
case 97:
YY_RULE_SETUP
#line 478 "tools/wrc/ppl.l"
return tSTRINGIZE;
	YY_BREAK
case 98:
YY_RULE_SETUP
#line 479 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL;
	YY_BREAK
case 99:
YY_RULE_SETUP
#line 480 "tools/wrc/ppl.l"
ppy_lval.cptr = xstrdup(ppy_text); return tLITERAL;
	YY_BREAK
case 100:
/* rule 100 can match eol */
YY_RULE_SETUP
#line 481 "tools/wrc/ppl.l"
newline(0); ppy_lval.cptr = xstrdup(" "); return tLITERAL;
	YY_BREAK
case 101:
/* rule 101 can match eol */
YY_RULE_SETUP
#line 482 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 102:
/* rule 102 can match eol */
YY_RULE_SETUP
#line 483 "tools/wrc/ppl.l"
newline(1); yy_pop_state(); return tNL;
	YY_BREAK
case 103:
YY_RULE_SETUP
#line 484 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_sqs);
	YY_BREAK
case 104:
YY_RULE_SETUP
#line 485 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_dqs);
	YY_BREAK
/*
	 * Macro expansion text scanning.
	 * This state is active just after the identifier is scanned
	 * that triggers an expansion. We *must* delete the leading
	 * whitespace before we can start scanning for arguments.
	 *
	 * If we do not see a '(' as next trailing token, then we have
	 * a false alarm. We just continue with a nose-bleed...
	 */
case 105:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 496 "tools/wrc/ppl.l"
yy_pp_state(pp_macscan);
	YY_BREAK
case 106:
/* rule 106 can match eol */
YY_RULE_SETUP
#line 497 "tools/wrc/ppl.l"
{
		if(yy_top_state() != pp_macscan)
			newline(0);
	}
	YY_BREAK
case 107:
/* rule 107 can match eol */
YY_RULE_SETUP
#line 501 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 108:
YY_RULE_SETUP
#line 502 "tools/wrc/ppl.l"
{
		macexpstackentry_t *mac = pop_macro();
		yy_pop_state();
		put_buffer(mac->ppp->ident, strlen(mac->ppp->ident));
		put_buffer(ppy_text, ppy_leng);
		free_macro(mac);
	}
	YY_BREAK
/*
	 * Macro expansion argument text scanning.
	 * This state is active when a macro's arguments are being read for expansion.
	 */
case 109:
YY_RULE_SETUP
#line 514 "tools/wrc/ppl.l"
{
		if(++MACROPARENTHESES() > 1)
			add_text_to_macro(ppy_text, ppy_leng);
	}
	YY_BREAK
case 110:
YY_RULE_SETUP
#line 518 "tools/wrc/ppl.l"
{
		if(--MACROPARENTHESES() == 0)
		{
			yy_pop_state();
			macro_add_arg(1);
		}
		else
			add_text_to_macro(ppy_text, ppy_leng);
	}
	YY_BREAK
case 111:
YY_RULE_SETUP
#line 527 "tools/wrc/ppl.l"
{
		if(MACROPARENTHESES() > 1)
			add_text_to_macro(ppy_text, ppy_leng);
		else
			macro_add_arg(0);
	}
	YY_BREAK
case 112:
YY_RULE_SETUP
#line 533 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_dqs);
	YY_BREAK
case 113:
YY_RULE_SETUP
#line 534 "tools/wrc/ppl.l"
new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_sqs);
	YY_BREAK
case 114:
YY_RULE_SETUP
#line 535 "tools/wrc/ppl.l"
yy_push_state(pp_comment); add_text_to_macro(" ", 1);
	YY_BREAK
case 115:
/* rule 115 can match eol */
YY_RULE_SETUP
#line 536 "tools/wrc/ppl.l"
pp_status.line_number++; pp_status.char_number = 1; add_text_to_macro(ppy_text, ppy_leng);
	YY_BREAK
case 116:
YY_RULE_SETUP
#line 537 "tools/wrc/ppl.l"
add_text_to_macro(ppy_text, ppy_leng);
	YY_BREAK
case 117:
/* rule 117 can match eol */
YY_RULE_SETUP
#line 538 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
/*
	 * Comment handling (almost all start-conditions)
	 */
case 118:
YY_RULE_SETUP
#line 543 "tools/wrc/ppl.l"
yy_push_state(pp_comment);
	YY_BREAK
case 119:
YY_RULE_SETUP
#line 544 "tools/wrc/ppl.l"
;
	YY_BREAK
case 120:
/* rule 120 can match eol */
YY_RULE_SETUP
#line 545 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 121:
YY_RULE_SETUP
#line 546 "tools/wrc/ppl.l"
yy_pop_state();
	YY_BREAK
/*
	 * Remove C++ style comment (almost all start-conditions)
	 */
case 122:
YY_RULE_SETUP
#line 551 "tools/wrc/ppl.l"
{
		if(ppy_text[ppy_leng-1] == '\\')
			ppy_warning("C++ style comment ends with an escaped newline (escape ignored)");
	}
	YY_BREAK
/*
	 * Single, double and <> quoted constants
	 */
case 123:
YY_RULE_SETUP
#line 559 "tools/wrc/ppl.l"
pp_incl_state.seen_junk++; new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_dqs);
	YY_BREAK
case 124:
YY_RULE_SETUP
#line 560 "tools/wrc/ppl.l"
pp_incl_state.seen_junk++; new_string(); add_string(ppy_text, ppy_leng); yy_push_state(pp_sqs);
	YY_BREAK
case 125:
YY_RULE_SETUP
#line 561 "tools/wrc/ppl.l"
add_string(ppy_text, ppy_leng);
	YY_BREAK
case 126:
YY_RULE_SETUP
#line 562 "tools/wrc/ppl.l"
{
		add_string(ppy_text, ppy_leng);
		yy_pop_state();
		switch(yy_current_state())
		{
		case pp_pp:
		case pp_define:
		case pp_mbody:
		case pp_inc:
		case RCINCL:
			if (yy_current_state()==RCINCL) yy_pop_state();
			ppy_lval.cptr = get_string();
			return tDQSTRING;
		case pp_line:
			ppy_lval.cptr = get_string();
			return tDQSTRING;
		default:
			put_string();
		}
	}
	YY_BREAK
case 127:
YY_RULE_SETUP
#line 582 "tools/wrc/ppl.l"
add_string(ppy_text, ppy_leng);
	YY_BREAK
case 128:
YY_RULE_SETUP
#line 583 "tools/wrc/ppl.l"
{
		add_string(ppy_text, ppy_leng);
		yy_pop_state();
		switch(yy_current_state())
		{
		case pp_if:
		case pp_define:
		case pp_mbody:
			ppy_lval.cptr = get_string();
			return tSQSTRING;
		default:
			put_string();
		}
	}
	YY_BREAK
case 129:
YY_RULE_SETUP
#line 597 "tools/wrc/ppl.l"
add_string(ppy_text, ppy_leng);
	YY_BREAK
case 130:
YY_RULE_SETUP
#line 598 "tools/wrc/ppl.l"
{
		add_string(ppy_text, ppy_leng);
		yy_pop_state();
		ppy_lval.cptr = get_string();
		return tIQSTRING;
	}
	YY_BREAK
case 131:
/* rule 131 can match eol */
YY_RULE_SETUP
#line 604 "tools/wrc/ppl.l"
{
		/*
		 * This is tricky; we need to remove the line-continuation
		 * from preprocessor strings, but OTOH retain them in all
		 * other strings. This is because the resource grammar is
		 * even more braindead than initially analysed and line-
		 * continuations in strings introduce, sigh, newlines in
		 * the output. There goes the concept of non-breaking, non-
		 * spacing whitespace.
		 */
		switch(yy_top_state())
		{
		case pp_pp:
		case pp_define:
		case pp_mbody:
		case pp_inc:
		case pp_line:
			newline(0);
			break;
		default:
			add_string(ppy_text, ppy_leng);
			newline(-1);
		}
	}
	YY_BREAK
case 132:
YY_RULE_SETUP
#line 628 "tools/wrc/ppl.l"
add_string(ppy_text, ppy_leng);
	YY_BREAK
case 133:
/* rule 133 can match eol */
YY_RULE_SETUP
#line 629 "tools/wrc/ppl.l"
{
		newline(1);
		add_string(ppy_text, ppy_leng);
		ppy_warning("Newline in string constant encountered (started line %d)", string_start());
	}
	YY_BREAK
/*
	 * Identifier scanning
	 */
case 134:
YY_RULE_SETUP
#line 638 "tools/wrc/ppl.l"
{
		pp_entry_t *ppp;
		pp_incl_state.seen_junk++;
		if(!(ppp = pplookup(ppy_text)))
		{
			if(yy_current_state() == pp_inc)
				ppy_error("Expected include filename");

			else if(yy_current_state() == pp_if)
			{
				ppy_lval.cptr = xstrdup(ppy_text);
				return tIDENT;
			}
			else {
				if((yy_current_state()==INITIAL) && (strcasecmp(ppy_text,"RCINCLUDE")==0)){
					yy_push_state(RCINCL);
					return tRCINCLUDE;
				}
				else put_buffer(ppy_text, ppy_leng);
			}
		}
		else if(!ppp->expanding)
		{
			switch(ppp->type)
			{
			case def_special:
				expand_special(ppp);
				break;
			case def_define:
				expand_define(ppp);
				break;
			case def_macro:
				yy_push_state(pp_macign);
				push_macro(ppp);
				break;
			default:
				assert(0);
			}
		}
		else put_buffer(ppy_text, ppy_leng);
	}
	YY_BREAK
/*
	 * Everything else that needs to be passed and
	 * newline and continuation handling
	 */
case 135:
YY_RULE_SETUP
#line 684 "tools/wrc/ppl.l"
pp_incl_state.seen_junk++; put_buffer(ppy_text, ppy_leng);
	YY_BREAK
case 136:
YY_RULE_SETUP
#line 685 "tools/wrc/ppl.l"
put_buffer(ppy_text, ppy_leng);
	YY_BREAK
case 137:
/* rule 137 can match eol */
YY_RULE_SETUP
#line 686 "tools/wrc/ppl.l"
newline(1);
	YY_BREAK
case 138:
/* rule 138 can match eol */
YY_RULE_SETUP
#line 687 "tools/wrc/ppl.l"
newline(0);
	YY_BREAK
case 139:
YY_RULE_SETUP
#line 688 "tools/wrc/ppl.l"
pp_incl_state.seen_junk++; put_buffer(ppy_text, ppy_leng);
	YY_BREAK
/*
	 * Special catcher for macro argmument expansion to prevent
	 * newlines to propagate to the output or admin.
	 */
case 140:
/* rule 140 can match eol */
YY_RULE_SETUP
#line 694 "tools/wrc/ppl.l"
put_buffer(ppy_text, ppy_leng);
	YY_BREAK
case 141:
YY_RULE_SETUP
#line 696 "tools/wrc/ppl.l"
{
		ppy_lval.cptr = xstrdup(ppy_text);
		yy_pop_state();
		return tRCINCLUDEPATH;
	}
	YY_BREAK
case 142:
YY_RULE_SETUP
#line 702 "tools/wrc/ppl.l"
;
	YY_BREAK
case 143:
YY_RULE_SETUP
#line 704 "tools/wrc/ppl.l"
{
		new_string(); add_string(ppy_text,ppy_leng);yy_push_state(pp_dqs);
	}
	YY_BREAK
/*
	 * This is a 'catch-all' rule to discover errors in the scanner
	 * in an orderly manner.
	 */
case 144:
YY_RULE_SETUP
#line 712 "tools/wrc/ppl.l"
pp_incl_state.seen_junk++; ppy_warning("Unmatched text '%c' (0x%02x); please report\n", isprint(*ppy_text & 0xff) ? *ppy_text : ' ', *ppy_text);
	YY_BREAK
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(pp_pp):
case YY_STATE_EOF(pp_eol):
case YY_STATE_EOF(pp_inc):
case YY_STATE_EOF(pp_dqs):
case YY_STATE_EOF(pp_sqs):
case YY_STATE_EOF(pp_iqs):
case YY_STATE_EOF(pp_comment):
case YY_STATE_EOF(pp_def):
case YY_STATE_EOF(pp_define):
case YY_STATE_EOF(pp_macro):
case YY_STATE_EOF(pp_mbody):
case YY_STATE_EOF(pp_macign):
case YY_STATE_EOF(pp_macscan):
case YY_STATE_EOF(pp_macexp):
case YY_STATE_EOF(pp_if):
case YY_STATE_EOF(pp_ifd):
case YY_STATE_EOF(pp_ifignored):
case YY_STATE_EOF(pp_endif):
case YY_STATE_EOF(pp_line):
case YY_STATE_EOF(pp_defined):
case YY_STATE_EOF(pp_ignore):
case YY_STATE_EOF(RCINCL):
#line 714 "tools/wrc/ppl.l"
{
		YY_BUFFER_STATE b = YY_CURRENT_BUFFER;
		bufferstackentry_t *bep = pop_buffer();

		if((!bep && pp_get_if_depth()) || (bep && pp_get_if_depth() != bep->if_depth))
			ppy_warning("Unmatched #if/#endif at end of file");

		if(!bep)
		{
			if(YY_START != INITIAL)
			{
				ppy_error("Unexpected end of file during preprocessing");
				BEGIN(INITIAL);
			}
			yyterminate();
		}
		else if(bep->should_pop == 2)
		{
			macexpstackentry_t *mac;
			mac = pop_macro();
			expand_macro(mac);
		}
		ppy__delete_buffer(b);
	}
	YY_BREAK
case 145:
YY_RULE_SETUP
#line 739 "tools/wrc/ppl.l"
ECHO;
	YY_BREAK
#line 2928 "tools/wrc/ppl.yy.c"

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_last_accepting_cpos);
				yy_current_state = (yy_last_accepting_state);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
	yy_current_state = (yy_start);
	yy_current_state += YY_AT_BOL();

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 421 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	int yy_is_jam;
    	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 421 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 420);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = (c == '\n');

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void yypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr )
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n  );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

    static void yy_push_state (int  _new_state )
{
    	if ( (yy_start_stack_ptr) >= (yy_start_stack_depth) )
		{
		yy_size_t new_size;

		(yy_start_stack_depth) += YY_START_STACK_INCR;
		new_size = (yy_size_t) (yy_start_stack_depth) * sizeof( int );

		if ( ! (yy_start_stack) )
			(yy_start_stack) = (int *) yyalloc( new_size  );

		else
			(yy_start_stack) = (int *) yyrealloc(
					(void *) (yy_start_stack), new_size  );

		if ( ! (yy_start_stack) )
			YY_FATAL_ERROR( "out of memory expanding start-condition stack" );
		}

	(yy_start_stack)[(yy_start_stack_ptr)++] = YY_START;

	BEGIN(_new_state);
}

    static void yy_pop_state  (void)
{
    	if ( --(yy_start_stack_ptr) < 0 )
		YY_FATAL_ERROR( "start-condition stack underflow" );

	BEGIN((yy_start_stack)[(yy_start_stack_ptr)]);
}

    static int yy_top_state  (void)
{
    	return (yy_start_stack)[(yy_start_stack_ptr) - 1];
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg )
{
			fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int yyget_lineno  (void)
{
    
    return yylineno;
}

/** Get the input stream.
 * 
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
int yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param _line_number line number
 * 
 */
void yyset_lineno (int  _line_number )
{
    
    yylineno = _line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str )
{
        yyin = _in_str ;
}

void yyset_out (FILE *  _out_str )
{
        yyout = _out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  _bdebug )
{
        yy_flex_debug = _bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = NULL;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = NULL;
    (yy_init) = 0;
    (yy_start) = 0;

    (yy_start_stack_ptr) = 0;
    (yy_start_stack_depth) = 0;
    (yy_start_stack) =  NULL;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state();
	}

	/* Destroy the stack itself. */
	yyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    /* Destroy the start condition stack. */
        yyfree( (yy_start_stack)  );
        (yy_start_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 739 "tools/wrc/ppl.l"

/*
 **************************************************************************
 * Support functions
 **************************************************************************
 */

#ifndef ppy_wrap
int ppy_wrap(void)
{
	return 1;
}
#endif


/*
 *-------------------------------------------------------------------------
 * Output newlines or set them as continuations
 *
 * Input: -1 - Don't count this one, but update local position (see pp_dqs)
 *	   0 - Line-continuation seen and cache output
 *	   1 - Newline seen and flush output
 *-------------------------------------------------------------------------
 */
static void newline(int dowrite)
{
	pp_status.line_number++;
	pp_status.char_number = 1;

	if(dowrite == -1)
		return;

	ncontinuations++;
	if(dowrite)
	{
		for(;ncontinuations; ncontinuations--)
			put_buffer("\n", 1);
	}
}


/*
 *-------------------------------------------------------------------------
 * Make a number out of an any-base and suffixed string
 *
 * Possible number extensions:
 * - ""		int
 * - "L"	long int
 * - "LL"	long long int
 * - "U"	unsigned int
 * - "UL"	unsigned long int
 * - "ULL"	unsigned long long int
 * - "LU"	unsigned long int
 * - "LLU"	unsigned long long int
 * - "LUL"	invalid
 *
 * FIXME:
 * The sizes of resulting 'int' and 'long' are compiler specific.
 *
 *-------------------------------------------------------------------------
 */
static int make_number(int radix, PPY_STYPE *val, const char *str, int len)
{
	int is_l  = 0;
	int is_ll = 0;
	int is_u  = 0;
	char ext[4];
	long l;

	ext[3] = '\0';
	ext[2] = toupper(str[len-1]);
	ext[1] = len > 1 ? toupper(str[len-2]) : ' ';
	ext[0] = len > 2 ? toupper(str[len-3]) : ' ';

	if(!strcmp(ext, "LUL"))
	{
		ppy_error("Invalid constant suffix");
		return 0;
	}
	else if(!strcmp(ext, "LLU") || !strcmp(ext, "ULL"))
	{
		is_ll++;
		is_u++;
	}
	else if(!strcmp(ext+1, "LU") || !strcmp(ext+1, "UL"))
	{
		is_l++;
		is_u++;
	}
	else if(!strcmp(ext+1, "LL"))
	{
		is_ll++;
	}
	else if(!strcmp(ext+2, "L"))
	{
		is_l++;
	}
	else if(!strcmp(ext+2, "U"))
	{
		is_u++;
	}

	if(is_u && is_ll)
	{
		errno = 0;
		val->ull = strtoull(str, NULL, radix);
		if (val->ull == ULLONG_MAX && errno == ERANGE)
		    ppy_error("integer constant %s is too large\n", str);
		return tULONGLONG;
	}
	else if(!is_u && is_ll)
	{
		errno = 0;
		val->sll = strtoll(str, NULL, radix);
		if ((val->sll == LLONG_MIN || val->sll == LLONG_MAX) && errno == ERANGE)
		    ppy_error("integer constant %s is too large\n", str);
		return tSLONGLONG;
	}
	else if(is_u && is_l)
	{
		errno = 0;
		val->ulong = strtoul(str, NULL, radix);
		if (val->ulong == ULONG_MAX && errno == ERANGE)
			ppy_error("integer constant %s is too large\n", str);
		return tULONG;
	}
	else if(!is_u && is_l)
	{
		errno = 0;
		val->slong = strtol(str, NULL, radix);
		if ((val->slong == LONG_MIN || val->slong == LONG_MAX) && errno == ERANGE)
			ppy_error("integer constant %s is too large\n", str);
		return tSLONG;
	}
	else if(is_u && !is_l)
	{
		unsigned long ul;
		errno = 0;
		ul = strtoul(str, NULL, radix);
		if ((ul == ULONG_MAX && errno == ERANGE) || (ul > UINT_MAX))
			ppy_error("integer constant %s is too large\n", str);
		val->uint = (unsigned int)ul;
		return tUINT;
	}

	/* Else it must be an int... */
	errno = 0;
	l = strtol(str, NULL, radix);
	if (((l == LONG_MIN || l == LONG_MAX) && errno == ERANGE) ||
		(l > INT_MAX) || (l < INT_MIN))
		ppy_error("integer constant %s is too large\n", str);
	val->sint = (int)l;
	return tSINT;
}


/*
 *-------------------------------------------------------------------------
 * Macro and define expansion support
 *
 * FIXME: Variable macro arguments.
 *-------------------------------------------------------------------------
 */
static void expand_special(pp_entry_t *ppp)
{
	static char *buf = NULL;

	assert(ppp->type == def_special);

	if(!strcmp(ppp->ident, "__LINE__"))
	{
		buf = xrealloc(buf, 32);
		sprintf(buf, "%d", pp_status.line_number);
	}
	else if(!strcmp(ppp->ident, "__FILE__"))
	{
		buf = xrealloc(buf, strlen(pp_status.input) + 3);
		sprintf(buf, "\"%s\"", pp_status.input);
	}

	if(pp_flex_debug)
		fprintf(stderr, "expand_special(%d): %s:%d: '%s' -> '%s'\n",
			macexpstackidx,
			pp_status.input,
			pp_status.line_number,
			ppp->ident,
			buf ? buf : "");

	if(buf && buf[0])
	{
		push_buffer(ppp, NULL, NULL, 0);
		yy_scan_string(buf);
	}
}

static void expand_define(pp_entry_t *ppp)
{
	assert(ppp->type == def_define);

	if(pp_flex_debug)
		fprintf(stderr, "expand_define(%d): %s:%d: '%s' -> '%s'\n",
			macexpstackidx,
			pp_status.input,
			pp_status.line_number,
			ppp->ident,
			ppp->subst.text);
	if(ppp->subst.text && ppp->subst.text[0])
	{
		push_buffer(ppp, NULL, NULL, 0);
		yy_scan_string(ppp->subst.text);
	}
}

static int curdef_idx = 0;
static int curdef_alloc = 0;
static char *curdef_text = NULL;

static void add_text(const char *str, int len)
{
	if(len == 0)
		return;
	if(curdef_idx >= curdef_alloc || curdef_alloc - curdef_idx < len)
	{
		curdef_alloc += (len + ALLOCBLOCKSIZE-1) & ~(ALLOCBLOCKSIZE-1);
		curdef_text = xrealloc(curdef_text, curdef_alloc * sizeof(curdef_text[0]));
	}
	memcpy(&curdef_text[curdef_idx], str, len);
	curdef_idx += len;
}

static mtext_t *add_expand_text(mtext_t *mtp, macexpstackentry_t *mep, int *nnl)
{
	char *cptr;
	char *exp;
	int tag;
	int n;

	if(mtp == NULL)
		return NULL;

	switch(mtp->type)
	{
	case exp_text:
		if(pp_flex_debug)
			fprintf(stderr, "add_expand_text: exp_text: '%s'\n", mtp->subst.text);
		add_text(mtp->subst.text, strlen(mtp->subst.text));
		break;

	case exp_stringize:
		if(pp_flex_debug)
			fprintf(stderr, "add_expand_text: exp_stringize(%d): '%s'\n",
				mtp->subst.argidx,
				mep->args[mtp->subst.argidx]);
		cptr = mep->args[mtp->subst.argidx];
		add_text("\"", 1);
		while(*cptr)
		{
			if(*cptr == '"' || *cptr == '\\')
				add_text("\\", 1);
			add_text(cptr, 1);
			cptr++;
		}
		add_text("\"", 1);
		break;

	case exp_concat:
		if(pp_flex_debug)
			fprintf(stderr, "add_expand_text: exp_concat\n");
		/* Remove trailing whitespace from current expansion text */
		while(curdef_idx)
		{
			if(isspace(curdef_text[curdef_idx-1] & 0xff))
				curdef_idx--;
			else
				break;
		}
		/* tag current position and recursively expand the next part */
		tag = curdef_idx;
		mtp = add_expand_text(mtp->next, mep, nnl);

		/* Now get rid of the leading space of the expansion */
		cptr = &curdef_text[tag];
		n = curdef_idx - tag;
		while(n)
		{
			if(isspace(*cptr & 0xff))
			{
				cptr++;
				n--;
			}
			else
				break;
		}
		if(cptr != &curdef_text[tag])
		{
			memmove(&curdef_text[tag], cptr, n);
			curdef_idx -= (curdef_idx - tag) - n;
		}
		break;

	case exp_subst:
		if((mtp->next && mtp->next->type == exp_concat) || (mtp->prev && mtp->prev->type == exp_concat))
			exp = mep->args[mtp->subst.argidx];
		else
			exp = mep->ppargs[mtp->subst.argidx];
		if(exp)
		{
			add_text(exp, strlen(exp));
			*nnl -= mep->nnls[mtp->subst.argidx];
			cptr = strchr(exp, '\n');
			while(cptr)
			{
				*cptr = ' ';
				cptr = strchr(cptr+1, '\n');
			}
			mep->nnls[mtp->subst.argidx] = 0;
		}
		if(pp_flex_debug)
			fprintf(stderr, "add_expand_text: exp_subst(%d): '%s'\n", mtp->subst.argidx, exp);
		break;
	}
	return mtp;
}

static void expand_macro(macexpstackentry_t *mep)
{
	mtext_t *mtp;
	int n, k;
	char *cptr;
	int nnl = 0;
	pp_entry_t *ppp = mep->ppp;
	int nargs = mep->nargs;

	assert(ppp->type == def_macro);
	assert(ppp->expanding == 0);

	if((!ppp->variadic && nargs != ppp->nargs) || (ppp->variadic && nargs < ppp->nargs))
	{
		ppy_error("Too %s macro arguments (%d)", nargs < ppp->nargs ? "few" : "many", nargs);
		return;
	}

	for(n = 0; n < nargs; n++)
		nnl += mep->nnls[n];

	if(pp_flex_debug)
		fprintf(stderr, "expand_macro(%d): %s:%d: '%s'(%d,%d) -> ...\n",
			macexpstackidx,
			pp_status.input,
			pp_status.line_number,
			ppp->ident,
			mep->nargs,
			nnl);

	curdef_idx = 0;

	for(mtp = ppp->subst.mtext; mtp; mtp = mtp->next)
	{
		if(!(mtp = add_expand_text(mtp, mep, &nnl)))
			break;
	}

	for(n = 0; n < nnl; n++)
		add_text("\n", 1);

	/* To make sure there is room and termination (see below) */
	add_text(" \0", 2);

	/* Strip trailing whitespace from expansion */
	for(k = curdef_idx, cptr = &curdef_text[curdef_idx-1]; k > 0; k--, cptr--)
	{
		if(!isspace(*cptr & 0xff))
			break;
	}

	/*
	 * We must add *one* whitespace to make sure that there
	 * is a token-separation after the expansion.
	 */
	*(++cptr) = ' ';
	*(++cptr) = '\0';
	k++;

	/* Strip leading whitespace from expansion */
	for(n = 0, cptr = curdef_text; n < k; n++, cptr++)
	{
		if(!isspace(*cptr & 0xff))
			break;
	}

	if(k - n > 0)
	{
		if(pp_flex_debug)
			fprintf(stderr, "expand_text: '%s'\n", curdef_text + n);
		push_buffer(ppp, NULL, NULL, 0);
		/*yy_scan_bytes(curdef_text + n, k - n);*/
		yy_scan_string(curdef_text + n);
	}
}

/*
 *-------------------------------------------------------------------------
 * String collection routines
 *-------------------------------------------------------------------------
 */
static void new_string(void)
{
	strbuf_idx = 0;
	str_startline = pp_status.line_number;
}

static void add_string(const char *str, int len)
{
	if(len == 0)
		return;
	if(strbuf_idx >= strbuf_alloc || strbuf_alloc - strbuf_idx < len)
	{
		strbuf_alloc += (len + ALLOCBLOCKSIZE-1) & ~(ALLOCBLOCKSIZE-1);
		strbuffer = xrealloc(strbuffer, strbuf_alloc * sizeof(strbuffer[0]));
	}
	memcpy(&strbuffer[strbuf_idx], str, len);
	strbuf_idx += len;
}

static char *get_string(void)
{
	char *str = xmalloc(strbuf_idx + 1);

	memcpy(str, strbuffer, strbuf_idx);
	str[strbuf_idx] = '\0';
	return str;
}

static void put_string(void)
{
	put_buffer(strbuffer, strbuf_idx);
}

static int string_start(void)
{
	return str_startline;
}


/*
 *-------------------------------------------------------------------------
 * Buffer management
 *-------------------------------------------------------------------------
 */
static void push_buffer(pp_entry_t *ppp, char *filename, char *incname, int pop)
{
	if(ppy_debug)
		printf("push_buffer(%d): %p %p %p %d\n", bufferstackidx, ppp, filename, incname, pop);
	if(bufferstackidx >= MAXBUFFERSTACK)
		error("Buffer stack overflow\n");

	memset(&bufferstack[bufferstackidx], 0, sizeof(bufferstack[0]));
	bufferstack[bufferstackidx].bufferstate	= YY_CURRENT_BUFFER;
	bufferstack[bufferstackidx].file        = pp_status.file;
	bufferstack[bufferstackidx].define	= ppp;
	bufferstack[bufferstackidx].line_number	= pp_status.line_number;
	bufferstack[bufferstackidx].char_number	= pp_status.char_number;
	bufferstack[bufferstackidx].if_depth	= pp_get_if_depth();
	bufferstack[bufferstackidx].should_pop	= pop;
	bufferstack[bufferstackidx].filename	= pp_status.input;
	bufferstack[bufferstackidx].ncontinuations	= ncontinuations;
	bufferstack[bufferstackidx].incl		= pp_incl_state;
	bufferstack[bufferstackidx].include_filename	= incname;

	if(ppp)
		ppp->expanding = 1;
	else if(filename)
	{
		/* These will track the ppy_error to the correct file and line */
		pp_status.line_number = 1;
		pp_status.char_number = 1;
		pp_status.input  = filename;
		ncontinuations = 0;
	}
	bufferstackidx++;
}

static bufferstackentry_t *pop_buffer(void)
{
	if(bufferstackidx == 0)
		return NULL;

	bufferstackidx--;

	if(bufferstack[bufferstackidx].define)
		bufferstack[bufferstackidx].define->expanding = 0;
	else
	{
		includelogicentry_t *iep = NULL;

		if(!bufferstack[bufferstackidx].should_pop)
		{
			fclose(pp_status.file);
			fprintf(ppy_out, "# %d \"%s\" 2\n", bufferstack[bufferstackidx].line_number, bufferstack[bufferstackidx].filename);

			/* We have EOF, check the include logic */
			if(pp_incl_state.state == 2 && !pp_incl_state.seen_junk && pp_incl_state.ppp)
			{
				pp_entry_t *ppp = pplookup(pp_incl_state.ppp);
				if(ppp)
				{
					iep = xmalloc(sizeof(includelogicentry_t));
					iep->ppp = ppp;
					ppp->iep = iep;
					iep->filename = bufferstack[bufferstackidx].include_filename;
                                        list_add_head( &pp_includelogiclist, &iep->entry );
					if(pp_status.debug)
						fprintf(stderr, "pop_buffer: %s:%d: includelogic added, include_ppp='%s', file='%s'\n",
                                                        bufferstack[bufferstackidx].filename, bufferstack[bufferstackidx].line_number, pp_incl_state.ppp, iep->filename);
				}
			}
			free(pp_incl_state.ppp);
			pp_incl_state	= bufferstack[bufferstackidx].incl;

		}
		if (bufferstack[bufferstackidx].include_filename)
		{
			free(pp_status.input);
			pp_status.input = bufferstack[bufferstackidx].filename;
		}
		pp_status.line_number = bufferstack[bufferstackidx].line_number;
		pp_status.char_number = bufferstack[bufferstackidx].char_number;
		ncontinuations = bufferstack[bufferstackidx].ncontinuations;
		if (!iep)
			free(bufferstack[bufferstackidx].include_filename);
	}

	if(ppy_debug)
		printf("pop_buffer(%d): %p %p (%d, %d, %d) %p %d\n",
			bufferstackidx,
			bufferstack[bufferstackidx].bufferstate,
			bufferstack[bufferstackidx].define,
			bufferstack[bufferstackidx].line_number,
			bufferstack[bufferstackidx].char_number,
			bufferstack[bufferstackidx].if_depth,
			bufferstack[bufferstackidx].filename,
			bufferstack[bufferstackidx].should_pop);

	pp_status.file = bufferstack[bufferstackidx].file;
	ppy__switch_to_buffer(bufferstack[bufferstackidx].bufferstate);

	if(bufferstack[bufferstackidx].should_pop)
	{
		assert( yy_current_state() == pp_macexp );
		macro_add_expansion();
		yy_pop_state();
	}

	return &bufferstack[bufferstackidx];
}


/*
 *-------------------------------------------------------------------------
 * Macro nestng support
 *-------------------------------------------------------------------------
 */
static void push_macro(pp_entry_t *ppp)
{
	if(macexpstackidx >= MAXMACEXPSTACK)
	{
		ppy_error("Too many nested macros");
		return;
	}

	macexpstack[macexpstackidx] = xmalloc(sizeof(macexpstack[0][0]));
        memset( macexpstack[macexpstackidx], 0, sizeof(macexpstack[0][0]));
	macexpstack[macexpstackidx]->ppp = ppp;
	macexpstackidx++;
}

static macexpstackentry_t *top_macro(void)
{
	return macexpstackidx > 0 ? macexpstack[macexpstackidx-1] : NULL;
}

static macexpstackentry_t *pop_macro(void)
{
	assert(macexpstackidx > 0);
	return macexpstack[--macexpstackidx];
}

static void free_macro(macexpstackentry_t *mep)
{
	int i;

	for(i = 0; i < mep->nargs; i++)
		free(mep->args[i]);
	free(mep->args);
	free(mep->nnls);
	free(mep->curarg);
	free(mep);
}

static void add_text_to_macro(const char *text, int len)
{
	macexpstackentry_t *mep = top_macro();

	assert(mep->ppp->expanding == 0);

	if(mep->curargalloc - mep->curargsize <= len+1)	/* +1 for '\0' */
	{
		mep->curargalloc += (ALLOCBLOCKSIZE > len+1) ? ALLOCBLOCKSIZE : len+1;
		mep->curarg = xrealloc(mep->curarg, mep->curargalloc * sizeof(mep->curarg[0]));
	}
	memcpy(mep->curarg + mep->curargsize, text, len);
	mep->curargsize += len;
	mep->curarg[mep->curargsize] = '\0';
}

static void macro_add_arg(int last)
{
	int nnl = 0;
	char *cptr;
	macexpstackentry_t *mep = top_macro();

	assert(mep->ppp->expanding == 0);

	mep->args = xrealloc(mep->args, (mep->nargs+1) * sizeof(mep->args[0]));
	mep->ppargs = xrealloc(mep->ppargs, (mep->nargs+1) * sizeof(mep->ppargs[0]));
	mep->nnls = xrealloc(mep->nnls, (mep->nargs+1) * sizeof(mep->nnls[0]));

	mep->args[mep->nargs] = xstrdup(mep->curarg ? mep->curarg : "");
	cptr = mep->args[mep->nargs]-1;
	while((cptr = strchr(cptr+1, '\n')))
	{
		nnl++;
	}
	mep->nnls[mep->nargs] = nnl;
	mep->nargs++;
	free(mep->curarg);
	mep->curargalloc = mep->curargsize = 0;
	mep->curarg = NULL;

	if(pp_flex_debug)
		fprintf(stderr, "macro_add_arg: %s:%d: %d -> '%s'\n",
			pp_status.input,
			pp_status.line_number,
			mep->nargs-1,
			mep->args[mep->nargs-1]);

	/* Each macro argument must be expanded to cope with stingize */
	if(last || mep->args[mep->nargs-1][0])
	{
		yy_push_state(pp_macexp);
		push_buffer(NULL, NULL, NULL, last ? 2 : 1);
		yy_scan_string(mep->args[mep->nargs-1]);
		/*mep->bufferstackidx = bufferstackidx;	 But not nested! */
	}
}

static void macro_add_expansion(void)
{
	macexpstackentry_t *mep = top_macro();

	assert(mep->ppp->expanding == 0);

	mep->ppargs[mep->nargs-1] = xstrdup(mep->curarg ? mep->curarg : "");
	free(mep->curarg);
	mep->curargalloc = mep->curargsize = 0;
	mep->curarg = NULL;

	if(pp_flex_debug)
		fprintf(stderr, "macro_add_expansion: %s:%d: %d -> '%s'\n",
			pp_status.input,
			pp_status.line_number,
			mep->nargs-1,
			mep->ppargs[mep->nargs-1] ? mep->ppargs[mep->nargs-1] : "");
}


/*
 *-------------------------------------------------------------------------
 * Output management
 *-------------------------------------------------------------------------
 */
static void put_buffer(const char *s, int len)
{
	if(top_macro())
		add_text_to_macro(s, len);
	else
		fwrite(s, 1, len, ppy_out);
}


/*
 *-------------------------------------------------------------------------
 * Include management
 *-------------------------------------------------------------------------
 */
void pp_do_include(char *fname, int type)
{
	char *newpath;
	int n;
	includelogicentry_t *iep;
	void *fp;

	if(!fname)
		return;

	LIST_FOR_EACH_ENTRY( iep, &pp_includelogiclist, includelogicentry_t, entry )
	{
		if(!strcmp(iep->filename, fname))
		{
			/*
			 * We are done. The file was included before.
			 * If the define was deleted, then this entry would have
			 * been deleted too.
			 */
			free(fname);
			return;
		}
	}

	n = strlen(fname);

	if(n <= 2)
	{
		ppy_error("Empty include filename");
		free(fname);
		return;
	}

	/* Undo the effect of the quotation */
	fname[n-1] = '\0';

	if((fp = pp_open_include(fname+1, type, pp_status.input, &newpath)) == NULL)
	{
		ppy_error("Unable to open include file %s", fname+1);
		free(fname);
		return;
	}

	fname[n-1] = *fname;	/* Redo the quotes */
	push_buffer(NULL, newpath, fname, 0);
	pp_incl_state.seen_junk = 0;
	pp_incl_state.state = 0;
	pp_incl_state.ppp = NULL;

	if(pp_status.debug)
		fprintf(stderr, "pp_do_include: %s:%d: include_state=%d, include_ifdepth=%d\n",
                        pp_status.input, pp_status.line_number, pp_incl_state.state, pp_incl_state.ifdepth);
	pp_status.file = fp;
	ppy__switch_to_buffer(ppy__create_buffer(NULL, YY_BUF_SIZE));

	fprintf(ppy_out, "# 1 \"%s\" 1%s\n", newpath, type ? "" : " 3");
}

/*
 *-------------------------------------------------------------------------
 * Push/pop preprocessor ignore state when processing conditionals
 * which are false.
 *-------------------------------------------------------------------------
 */
void pp_push_ignore_state(void)
{
	yy_push_state(pp_ignore);
}

void pp_pop_ignore_state(void)
{
	yy_pop_state();
}

