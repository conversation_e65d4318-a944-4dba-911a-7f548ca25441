; 
; Exports of file azroles.DLL
;
; Autogenerated by gen_exportdef
; Written by <PERSON>, 2007
;
LIBRARY azroles.DLL
EXPORTS
AzAddPropertyItem
AzApplicationClose
AzApplicationCreate
AzApplicationDelete
AzApplicationEnum
AzApplicationOpen
AzAuthorizationStoreDelete
AzCloseHandle
AzContextAccessCheck
AzContextGetAssignedScopesPage
AzContextGetRoles
AzFreeMemory
AzGetProperty
AzGroupCreate
AzGroupDelete
AzGroupEnum
AzGroupOpen
AzInitialize
AzInitializeContextFromName
AzInitializeContextFromToken
AzOperationCreate
AzOperationDelete
AzOperationEnum
AzOperationOpen
AzRemovePropertyItem
AzRoleCreate
AzRoleDelete
AzRoleEnum
AzRoleOpen
AzScopeCreate
AzScopeDelete
AzScopeEnum
AzScopeOpen
AzSetProperty
AzSubmit
AzTaskCreate
AzTaskDelete
AzTaskEnum
AzTaskOpen
AzUpdateCache
DllCanUnloadNow
DllGetClassObject
DllRegisterServer
DllUnregisterServer
