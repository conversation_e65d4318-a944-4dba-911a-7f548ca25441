LIBRARY vcruntime140_app

EXPORTS

#include "func.def.in"

_CreateFrameInfo
F_I386(_CxxThrowException@8)
F_NON_I386(_CxxThrowException)
F_I386(_EH_prolog)
_FindAndUnlinkFrame
_IsExceptionObjectToBeDestroyed
F_I386(_NLG_Dispatch2)
F_I386(_NLG_Return)
F_I386(_NLG_Return2)
_SetWinRTOutOfMemoryExceptionCallback
__AdjustPointer
__BuildCatchObject
__BuildCatchObjectHelper
F_NON_I386(__C_specific_handler)
F_NON_I386(__C_specific_handler_noexcept)
__CxxDetectRethrow
__CxxExceptionFilter
__CxxFrameHandler
__CxxFrameHandler2
__CxxFrameHandler3
F_I386(__CxxLongjmpUnwind@4)
__CxxQueryExceptionSize
__CxxRegisterExceptionObject
__CxxUnregisterExceptionObject
__DestructExceptionObject
__FrameUnwindFilter
__GetPlatformExceptionInfo
F_NON_I386(__NLG_Dispatch2)
F_NON_I386(__NLG_Return2)
__RTCastToVoid
__RTDynamicCast
__RTtypeid
__TypeMatch
__current_exception
__current_exception_context
F_X86_ANY(__intrinsic_setjmp)
F_ARM32(__intrinsic_setjmp)
F_NON_I386(__intrinsic_setjmpex)
F_ARM32(__jump_unwind)
__processing_throw
__report_gsfailure
__std_exception_copy
__std_exception_destroy
__std_terminate
__std_type_info_compare
__std_type_info_destroy_list
__std_type_info_hash
__std_type_info_name
__telemetry_main_invoke_trigger
__telemetry_main_return_trigger
__unDName
__unDNameEx
__uncaught_exception
__uncaught_exceptions
__vcrt_GetModuleFileNameW
__vcrt_GetModuleHandleW
__vcrt_InitializeCriticalSectionEx
__vcrt_LoadLibraryExW
F_I386(_chkesp)
F_I386(_except_handler2)
F_I386(_except_handler3)
F_I386(_except_handler4_common)
_get_purecall_handler
_get_unexpected
F_I386(_global_unwind2)
_is_exception_typeof
F_I386(_local_unwind2)
F_I386(_local_unwind4)
F_I386(_longjmpex)
F64(_local_unwind)
_purecall
F_I386(_seh_longjmp_unwind4@4)
F_I386(_seh_longjmp_unwind@4)
_set_purecall_handler
_set_se_translator
F_I386(_setjmp3)
longjmp
memchr
memcmp
memcpy
memmove
memset
set_unexpected
strchr
strrchr
strstr
unexpected
wcschr
wcsrchr
wcsstr
