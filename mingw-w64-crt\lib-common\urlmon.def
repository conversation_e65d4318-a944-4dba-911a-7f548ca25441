;
; Definition file of urlmon.dll
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "urlmon.dll"
EXPORTS
FileBearsMarkOfTheWeb
GetPortFromUrlScheme
GetPropertyFromName
GetPropertyName
IsDWORDProperty
IsStringProperty
AsyncGetClassBits
AsyncInstallDistributionUnit
BindAsyncMoniker
CAuthenticateHostUI_CreateInstance
CDLGetLongPathNameA
CDLGetLongPathNameW
CORPolicyProvider
CoGetClassObjectFromURL
CoInstall
CoInternetCanonicalizeIUri
CoInternetCombineIUri
CoInternetCombineUrl
CoInternetCombineUrlEx
CoInternetCompareUrl
CoInternetCreateSecurityManager
CoInternetCreateZoneManager
CoInternetFeatureSettingsChanged
CoInternetGetMobileBrowserAppCompatMode
CoInternetGetMobileBrowserForceDesktopMode
CoInternetGetProtocolFlag<PERSON>etSecurityUrl
CoInternetGetSecurityUrlEx
CoInternetGetSession
CoInternetIsFeatureEnabled
CoInternetIsFeatureEnabledForIUri
CoInternetIsFeatureEnabledForUrl
CoInternetIsFeatureZoneElevationEnabled
CoInternetParseIUri
CoInternetParseUrl
CoInternetQueryInfo
CoInternetSetFeatureEnabled
CoInternetSetMobileBrowserAppCompatMode
CoInternetSetMobileBrowserForceDesktopMode
CompareSecurityIds
CompatFlagsFromClsid
CopyBindInfo
CopyStgMedium
CreateAsyncBindCtx
CreateAsyncBindCtxEx
CreateFormatEnumerator
CreateIUriBuilder
CreateURLMoniker
CreateURLMonikerEx
CreateURLMonikerEx2
CreateUri
CreateUriFromMultiByteString
CreateUriPriv
CreateUriWithFragment
DllCanUnloadNow
DllGetClassObject
DllInstall
DllRegisterServer
DllRegisterServerEx
DllUnregisterServer
Extract
FaultInIEFeature
FindMediaType
FindMediaTypeClass
FindMimeFromData
GetAddSitesFileUrl
GetClassFileOrMime
GetClassURL
GetComponentIDFromCLSSPEC
GetIDNFlagsForUri
GetIUriPriv
GetIUriPriv2
GetLabelsFromNamedHost
GetMarkOfTheWeb
GetSoftwareUpdateInfo
GetUrlmonThreadNotificationHwnd
GetZoneFromAlternateDataStreamEx
HlinkGoBack
HlinkGoForward
HlinkNavigateMoniker
HlinkNavigateString
HlinkSimpleNavigateToMoniker
HlinkSimpleNavigateToString
IEGetUserPrivateNamespaceName
IEInstallScope
InstallFlash
IntlPercentEncodeNormalize
IsAsyncMoniker
IsIntranetAvailable
IsJITInProgress
IsLoggingEnabledA
IsLoggingEnabledW
IsValidURL
MkParseDisplayNameEx
ObtainUserAgentString
PrivateCoInstall
QueryAssociations
QueryClsidAssociation
RegisterBindStatusCallback
RegisterFormatEnumerator
RegisterMediaTypeClass
RegisterMediaTypes
RegisterWebPlatformPermanentSecurityManager
ReleaseBindInfo
RestrictHTTP2
RevokeBindStatusCallback
RevokeFormatEnumerator
SetAccessForIEAppContainer
SetSoftwareUpdateAdvertisementState
ShouldDisplayPunycodeForUri
ShouldShowIntranetWarningSecband
ShowTrustAlertDialog
URLDownloadA
URLDownloadToCacheFileA
URLDownloadToCacheFileW
URLDownloadToFileA
URLDownloadToFileW
URLDownloadW
URLOpenBlockingStreamA
URLOpenBlockingStreamW
URLOpenPullStreamA
URLOpenPullStreamW
URLOpenStreamA
URLOpenStreamW
UnregisterWebPlatformPermanentSecurityManager
UrlMkBuildVersion
UrlMkGetSessionOption
UrlMkSetSessionOption
UrlmonCleanupCurrentThread
WriteHitLogging
ZonesReInit
IECompatLogCSSFix
