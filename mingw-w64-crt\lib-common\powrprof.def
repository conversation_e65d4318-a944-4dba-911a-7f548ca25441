;
; Definition file of POWRPROF.dll
; Automatic generated by gend<PERSON>
; written by <PERSON> 2008-2014
;
LIBRARY "POWRPROF.dll"
EXPORTS
CallNtPowerInformation
CanUserWritePwrScheme
DeletePwrScheme
DevicePowerClose
DevicePowerEnumDevices
DevicePowerOpen
DevicePowerSetDeviceState
EnumPwrSchemes
GUIDFormatToGlobalPowerPolicy
GUIDFormatToPowerPolicy
GetActivePwrScheme
GetCurrentPowerPolicies
GetPwrCapabilities
GetPwrDiskSpindownRange
IsAdminOverrideActive
IsPwrHibernateAllowed
IsPwrShutdownAllowed
IsPwrSuspendAllowed
LoadCurrentPwrScheme
MergeLegacyPwrScheme
PowerApplyPowerRequestOverride
PowerApplySettingChanges
PowerCanRestoreIndividualDefaultPowerScheme
PowerCleanupOverrides
PowerClearUserAwayPrediction
PowerCloseEnvironmentalMonitor
PowerCloseLimitsMitigation
PowerCloseLimitsPolicy
PowerCreatePossibleSetting
PowerCreateSetting
PowerCustomizePlatformPowerSettings
PowerDebugDifPowerPolicies
PowerDebugDifSystemPowerPolicies
PowerDebugDumpPowerPolicy
PowerDebugDumpPowerScheme
PowerDebugDumpSystemPowerCapabilities
PowerDebugDumpSystemPowerPolicy
PowerDeleteScheme
PowerDeterminePlatformRole
PowerDeterminePlatformRoleEx
PowerDuplicateScheme
PowerEnumerate
PowerEnumerateSettings
PowerGetActiveScheme
PowerGetActualOverlayScheme
PowerGetAdaptiveStandbyDiagnostics
PowerGetEffectiveOverlayScheme
PowerGetOverlaySchemes
PowerGetProfiles
PowerGetUserAwayMinPredictionConfidence
PowerImportPowerScheme
PowerInternalDeleteScheme
PowerInternalDuplicateScheme
PowerInternalImportPowerScheme
PowerInternalRestoreDefaultPowerSchemes
PowerInternalRestoreIndividualDefaultPowerScheme
PowerInternalSetActiveScheme
PowerInternalWriteToUserPowerKey
PowerInformationWithPrivileges
PowerIsSettingRangeDefined
PowerOpenSystemPowerKey
PowerOpenUserPowerKey
PowerPolicyToGUIDFormat
PowerReadACDefaultIndex
PowerReadACValue
PowerReadACValueIndex
PowerReadACValueIndexEx
PowerReadDCDefaultIndex
PowerReadDCValue
PowerReadDCValueIndex
PowerReadDCValueIndexEx
PowerReadDescription
PowerReadFriendlyName
PowerReadIconResourceSpecifier
PowerReadPossibleDescription
PowerReadPossibleFriendlyName
PowerReadPossibleValue
PowerReadProfileAlias
PowerReadSecurityDescriptor
PowerReadSettingAttributes
PowerReadValueIncrement
PowerReadValueMax
PowerReadValueMin
PowerReadValueUnitsSpecifier
PowerReapplyActiveScheme
PowerRegisterEnvironmentalMonitor
PowerRegisterForEffectivePowerModeNotifications
PowerRegisterLimitsMitigation
PowerRegisterLimitsPolicy
PowerRegisterSuspendResumeNotification
PowerRemovePowerSetting
PowerReplaceDefaultPowerSchemes
PowerReportLimitsEvent
PowerReportThermalEvent
PowerRestoreACDefaultIndex
PowerRestoreDCDefaultIndex
PowerRestoreDefaultPowerSchemes
PowerRestoreIndividualDefaultPowerScheme
PowerSetActiveOverlayScheme
PowerSetActiveScheme
PowerSetAlsBrightnessOffset
PowerSetBrightnessAndTransitionTimes
PowerSetUserAwayPrediction
PowerSettingAccessCheck
PowerSettingAccessCheckEx
PowerSettingRegisterNotification
PowerSettingRegisterNotificationEx
PowerSettingUnregisterNotification
PowerUnregisterFromEffectivePowerModeNotifications
PowerUnregisterSuspendResumeNotification
PowerUpdateEnvironmentalMonitorState
PowerUpdateEnvironmentalMonitorThresholds
PowerUpdateLimitsMitigation
PowerWriteACDefaultIndex
PowerWriteACProfileIndex
PowerWriteACValueIndex
PowerWriteDCDefaultIndex
PowerWriteDCProfileIndex
PowerWriteDCValueIndex
PowerWriteDescription
PowerWriteFriendlyName
PowerWriteIconResourceSpecifier
PowerWritePossibleDescription
PowerWritePossibleFriendlyName
PowerWritePossibleValue
PowerWriteSecurityDescriptor
PowerWriteSettingAttributes
PowerWriteValueIncrement
PowerWriteValueMax
PowerWriteValueMin
PowerWriteValueUnitsSpecifier
ReadGlobalPwrPolicy
ReadProcessorPwrScheme
ReadPwrScheme
SetActivePwrScheme
SetSuspendState
Sysprep_Generalize_Power
ValidatePowerPolicies
WriteGlobalPwrPolicy
WriteProcessorPwrScheme
WritePwrScheme
