LIBRARY "SHELL32.dll"
EXPORTS
SHChangeNotifyRegister
SHDefExtractIconA
SHChangeNotifyDeregister
SHDefExtractIconW
PifMgr_OpenProperties
PifMgr_GetProperties
PifMgr_SetProperties
PifMgr_CloseProperties
SHStartNetConnectionDialogW
ILFindLastID
ILRemoveLastID
ILClone
ILCloneFirst
ILIsEqual
DAD_DragEnterEx2
ILIsParent
ILFindChild
ILCombine
ILLoadFromStream
ILSaveToStream
SHILCreateFromPath
IsLFNDriveA
IsLFNDriveW
PathIsExe
PathMakeUniqueName
PathQualify
PathResolve
RestartDialog
PickIconDlg
GetFileNameFromBrowse
DriveType
IsNetDrive
Shell_MergeMenus
SHGetSetSettings
Shell_GetImageLists
Shell_GetCachedImageIndex
SHShellFolderView_Message
SHCreateStdEnumFmtEtc
PathYetAnotherMakeUniqueName
SHMapPIDLToSystemImageListIndex
SHOpenPropSheetW
OpenAs_RunDLL
CIDLData_CreateFromIDArray
OpenRegStream
SHDoDragDrop
SHCloneSpecialIDList
SHFindFiles
PathGetShortPath
SHGetRealIDL
SHRestricted
SHCoCreateInstance
SignalFileOpen
Activate_RunDLL
CheckEscapesA
CheckEscapesW
Control_FillCache_RunDLL
Control_FillCache_RunDLLA
Control_FillCache_RunDLLW
IsLFNDrive
SHFlushClipboard
OpenAs_RunDLLA
DAD_AutoScroll
DAD_DragEnterEx
DAD_DragLeave
OpenAs_RunDLLW
DAD_DragMove
PrepareDiscForBurnRunDllW
DAD_SetDragImage
DAD_ShowDragImage
PrintersGetCommand_RunDLL
PrintersGetCommand_RunDLLA
SHCLSIDFromString
SHFind_InitMenuPopup
SHMapIDListToImageListIndexAsync
PrintersGetCommand_RunDLLW
ILGetSize
ILGetNext
ILAppendID
ILFree
ILCreateFromPath
SHRunControlPanel
SHSimpleIDListFromPath
Win32DeleteFile
SHCreateDirectory
CallCPLEntry16
SHAddFromPropSheetExtArray
SHCreatePropSheetExtArray
SHDestroyPropSheetExtArray
SHReplaceFromPropSheetExtArray
PathCleanupSpec
SHValidateUNC
SHCreateShellFolderViewEx
SHSetInstanceExplorer
SHObjectProperties
SHGetNewLinkInfoA
SHGetNewLinkInfoW
ShellMessageBoxW
ShellMessageBoxA
ILCreateFromPathA
ILCreateFromPathW
SHUpdateImageA
SHUpdateImageW
SHHandleUpdateImage
SHFree
SHAlloc
RealShellExecuteA
RealShellExecuteExA
RealShellExecuteExW
RealShellExecuteW
SHHelpShortcuts_RunDLL
SHHelpShortcuts_RunDLLA
SHSetFolderPathA
SHSetFolderPathW
SHHelpShortcuts_RunDLLW
PathIsSlowW
PathIsSlowA
SHTestTokenMembership
AppCompat_RunDLLW
SHCreateShellFolderView
AssocCreateForClasses
AssocGetDetailsOfPropKey
CommandLineToArgvW
Control_RunDLL
Control_RunDLLA
Control_RunDLLAsUserW
Control_RunDLLW
DoEnvironmentSubstA
DoEnvironmentSubstW
DragAcceptFiles
DragFinish
DragQueryFile
DragQueryFileA
DragQueryFileAorW
DragQueryFileW
DragQueryPoint
DuplicateIcon
ExtractAssociatedIconA
ExtractAssociatedIconExA
ExtractAssociatedIconExW
ExtractAssociatedIconW
ExtractIconA
ExtractIconEx
ExtractIconExA
ExtractIconExW
ExtractIconResInfoA
ExtractIconResInfoW
ExtractIconW
ExtractVersionResource16W
FindExeDlgProc
FindExecutableA
FindExecutableW
FreeIconList
GetCurrentProcessExplicitAppUserModelID
InitNetworkAddressControl
InternalExtractIconListA
InternalExtractIconListW
LaunchMSHelp_RunDLLW
Options_RunDLL
Options_RunDLLA
Options_RunDLLW
PathCleanupSpecWorker
PathIsExeWorker
RegenerateUserEnvironment
RunAsNewUser_RunDLLW
SHAddDefaultPropertiesByExt
SHAddToRecentDocs
SHAppBarMessage
SHAssocEnumHandlers
SHAssocEnumHandlersForProtocolByApplication
SHBindToFolderIDListParent
SHBindToFolderIDListParentEx
SHBindToObject
SHBindToParent
SHBrowseForFolder
SHBrowseForFolderA
SHBrowseForFolderW
SHChangeNotify
SHChangeNotifyRegisterThread
SHChangeNotifySuspendResume
SHCoCreateInstanceWorker
SHCreateAssociationRegistration
SHCreateDataObject
SHCreateDefaultContextMenu
SHCreateDefaultExtractIcon
SHCreateDefaultPropertiesOp
SHCreateDirectoryExA
SHCreateDirectoryExW
SHCreateDirectoryExWWorker
SHCreateItemFromIDList
SHCreateItemFromParsingName
SHCreateItemFromRelativeName
SHCreateItemInKnownFolder
SHCreateItemWithParent
SHCreateLocalServerRunDll
SHCreateProcessAsUserW
SHCreateQueryCancelAutoPlayMoniker
SHCreateShellItem
SHCreateShellItemArray
SHCreateShellItemArrayFromDataObject
SHCreateShellItemArrayFromIDLists
SHCreateShellItemArrayFromShellItem
SHEmptyRecycleBinA
SHEmptyRecycleBinW
SHEnableServiceObject
SHEnumerateUnreadMailAccountsW
SHEvaluateSystemCommandTemplate
SHExtractIconsW
SHFileOperation
SHFileOperationA
SHFileOperationW
SHFormatDrive
SHFreeNameMappings
SHGetDataFromIDListA
SHGetDataFromIDListW
SHGetDesktopFolder
SHGetDesktopFolderWorker
SHGetDiskFreeSpaceA
SHGetDiskFreeSpaceExA
SHGetDiskFreeSpaceExW
SHGetDriveMedia
SHGetFileInfo
SHGetFileInfoA
SHGetFileInfoW
SHGetFileInfoWWorker
SHGetFolderLocation
SHGetFolderLocationWorker
SHGetFolderPathA
SHGetFolderPathAWorker
SHGetFolderPathAndSubDirA
SHGetFolderPathAndSubDirW
SHGetFolderPathAndSubDirWWorker
SHGetFolderPathEx
SHGetFolderPathW
SHGetFolderPathWWorker
SHGetIDListFromObject
SHGetIconOverlayIndexA
SHGetIconOverlayIndexW
SHGetInstanceExplorer
SHGetInstanceExplorerWorker
SHGetItemFromDataObject
SHGetItemFromObject
SHGetKnownFolderIDList
SHGetKnownFolderItem
SHGetKnownFolderPath
SHGetKnownFolderPathWorker
SHGetLocalizedName
SHGetMalloc
SHGetNameFromIDList
SHGetNewLinkInfo
SHGetPathFromIDList
SHGetPathFromIDListA
SHGetPathFromIDListEx
SHGetPathFromIDListW
SHGetPropertyStoreForWindow
SHGetPropertyStoreFromIDList
SHGetPropertyStoreFromParsingName
SHGetSettings
SHGetSpecialFolderLocation
SHGetSpecialFolderPathA
SHGetSpecialFolderPathAWorker
SHGetSpecialFolderPathW
SHGetSpecialFolderPathWWorker
SHGetStockIconInfo
SHGetTemporaryPropertyForItem
SHGetUnreadMailCountW
SHInvokePrinterCommandA
SHInvokePrinterCommandW
SHIsFileAvailableOffline
SHLoadInProc
SHLoadNonloadedIconOverlayIdentifiers
SHOpenFolderAndSelectItems
SHOpenWithDialog
SHParseDisplayName
SHPathPrepareForWriteA
SHPathPrepareForWriteW
SHQueryRecycleBinA
SHQueryRecycleBinW
SHQueryUserNotificationState
SHRemoveLocalizedName
SHResolveLibrary
SHSetDefaultProperties
SHSetKnownFolderPath
SHSetKnownFolderPathWorker
SHSetLocalizedName
SHSetTemporaryPropertyForItem
SHSetUnreadMailCountW
SHShowManageLibraryUI
SHUpdateRecycleBinIcon
SetCurrentProcessExplicitAppUserModelID
SheChangeDirA
SheChangeDirExA
SheChangeDirExW
SheChangeDirW
SheConvertPathW
SheFullPathA
SheFullPathW
SheGetCurDrive
SheShortenPathA
SheShortenPathW
SheGetDirA
SheGetDirExW
SheGetDirW
SheGetPathOffsetW
SheRemoveQuotesA
SheRemoveQuotesW
SheSetCurDrive
ShellAboutA
ShellAboutW
ShellExec_RunDLL
ShellExec_RunDLLA
ShellExec_RunDLLW
ShellExecuteA
ShellExecuteEx
ShellExecuteExA
ShellExecuteExW
ShellExecuteW
ShellHookProc
Shell_GetCachedImageIndexA
Shell_GetCachedImageIndexW
Shell_NotifyIcon
Shell_NotifyIconA
Shell_NotifyIconGetRect
Shell_NotifyIconW
StrChrA
StrChrIA
StrChrIW
StrChrW
StrCmpNA
StrCmpNIA
StrCmpNIW
StrCmpNW
StrCpyNA
StrCpyNW
StrNCmpA
StrNCmpIA
StrNCmpIW
StrNCmpW
StrNCpyA
StrNCpyW
StrRChrA
StrRChrIA
StrRChrIW
StrRChrW
StrRStrA
StrRStrIA
StrRStrIW
StrRStrW
StrStrA
StrStrIA
StrStrIW
StrStrW
WOWShellExecute
SHAllocShared
SHLockShared
SHUnlockShared
SHFreeShared
WaitForExplorerRestartW
RealDriveType
SHFlushSFCache
SHChangeNotification_Lock
SHChangeNotification_Unlock
WriteCabinetState
PathProcessCommand
ReadCabinetState
IsUserAnAdmin
StgMakeUniqueName
SHPropStgCreate
SHPropStgReadMultiple
SHPropStgWriteMultiple
CDefFolderMenu_Create
CDefFolderMenu_Create2
SHGetSetFolderCustomSettingsW
SHGetSetFolderCustomSettings
SHMultiFileProperties
SHGetImageList
RestartDialogEx
SHCreateFileExtractIconW
SHLimitInputEdit
SHGetShellStyleHInstance
SHGetAttributesFromDataObject
ILLoadFromStreamEx
GetSystemPersistedStorageItemList
CreateStorageItemFromShellItem_FullTrustCaller
CreateStorageItemFromShellItem_FullTrustCaller_ForPackage
CreateStorageItemFromShellItem_FullTrustCaller_ForPackage_WithProcessHandle
CreateStorageItemFromShellItem_FullTrustCaller_UseImplicitFlagsAndPackage
