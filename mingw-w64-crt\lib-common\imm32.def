;
; Definition file of IMM32.dll
; Automatic generated by gendef
; written by <PERSON> 2008-2014
;
LIBRARY "IMM32.dll"
EXPORTS
CtfImmAppCompatEnableIMEonProtectedCode
CtfImmCoUninitialize
CtfImmDispatchDefImeMessage
CtfImmEnterCoInitCountSkipMode
CtfImmGenerateMessage
CtfImmGetCompatibleKeyboardLayout
CtfImmGetGlobalIMEStatus
CtfImmGetGuidAtom
CtfImmGetIMEFileName
CtfImmGetTMAEFlags
CtfImmHideToolbarWnd
CtfImmIsCiceroEnabled
CtfImmIsCiceroStartedInThread
CtfImmIsComStartedInThread
CtfImmIsGuidMapEnable
CtfImmIsTextFrameServiceDisabled
CtfImmLastEnabledWndDestroy
CtfImmLeaveCoInitCountSkipMode
C<PERSON>fImmNotify
CtfImmRestoreToolbarWnd
CtfImmSetAppCompatFlags
CtfImmSetCiceroStartInThread
CtfImmSetDefaultRemoteKeyboardLayout
CtfImmTIMActivate
GetKeyboardLayoutCP
ImmActivateLayout
ImmAssociateContext
ImmAssociateContextEx
ImmCallImeConsoleIME
ImmConfigureIMEA
ImmConfigureIMEW
ImmCreateContext
ImmCreateIMCC
ImmCreateSoftKeyboard
ImmDestroyContext
ImmDestroyIMCC
ImmDestroySoftKeyboard
ImmDisableIME
ImmDisableIme
ImmDisableLegacyIME
ImmDisableTextFrameService
ImmEnumInputContext
ImmEnumRegisterWordA
ImmEnumRegisterWordW
ImmEscapeA
ImmEscapeW
ImmFreeLayout
ImmGenerateMessage
ImmGetAppCompatFlags
ImmGetCandidateListA
ImmGetCandidateListCountA
ImmGetCandidateListCountW
ImmGetCandidateListW
ImmGetCandidateWindow
ImmGetCompositionFontA
ImmGetCompositionFontW
ImmGetCompositionStringA
ImmGetCompositionStringW
ImmGetCompositionWindow
ImmGetContext
ImmGetConversionListA
ImmGetConversionListW
ImmGetConversionStatus
ImmGetDefaultIMEWnd
ImmGetDescriptionA
ImmGetDescriptionW
ImmGetGuideLineA
ImmGetGuideLineW
ImmGetHotKey
ImmGetIMCCLockCount
ImmGetIMCCSize
ImmGetIMCLockCount
ImmGetIMEFileNameA
ImmGetIMEFileNameW
ImmGetImeInfoEx
ImmGetImeMenuItemsA
ImmGetImeMenuItemsW
ImmGetOpenStatus
ImmGetProperty
ImmGetRegisterWordStyleA
ImmGetRegisterWordStyleW
ImmGetStatusWindowPos
ImmGetVirtualKey
ImmIMPGetIMEA
ImmIMPGetIMEW
ImmIMPQueryIMEA
ImmIMPQueryIMEW
ImmIMPSetIMEA
ImmIMPSetIMEW
ImmInstallIMEA
ImmInstallIMEW
ImmIsIME
ImmIsUIMessageA
ImmIsUIMessageW
ImmLoadIME
ImmLoadLayout
ImmLockClientImc
ImmLockIMC
ImmLockIMCC
ImmLockImeDpi
ImmNotifyIME
ImmProcessKey
ImmPutImeMenuItemsIntoMappedFile
ImmReSizeIMCC
ImmRegisterClient
ImmRegisterWordA
ImmRegisterWordW
ImmReleaseContext
ImmRequestMessageA
ImmRequestMessageW
ImmSendIMEMessageExA
ImmSendIMEMessageExW
ImmSetActiveContext
ImmSetActiveContextConsoleIME
ImmSetCandidateWindow
ImmSetCompositionFontA
ImmSetCompositionFontW
ImmSetCompositionStringA
ImmSetCompositionStringW
ImmSetCompositionWindow
ImmSetConversionStatus
ImmSetHotKey
ImmSetOpenStatus
ImmSetStatusWindowPos
ImmShowSoftKeyboard
ImmSimulateHotKey
ImmSystemHandler
ImmTranslateMessage
ImmUnlockClientImc
ImmUnlockIMC
ImmUnlockIMCC
ImmUnlockImeDpi
ImmUnregisterWordA
ImmUnregisterWordW
ImmWINNLSEnableIME
ImmWINNLSGetEnableStatus
ImmWINNLSGetIMEHotkey
