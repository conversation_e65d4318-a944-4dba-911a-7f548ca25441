;
; Definition file of bthprops.cpl
; Automatic generated by gendef
; written by <PERSON> 2008
;
LIBRARY "bthprops.cpl"
EXPORTS
ord_103 @103
BluetoothAddressToString
BluetoothAuthenticateDevice
BluetoothAuthenticateDeviceEx
BluetoothAuthenticateMultipleDevices
BluetoothAuthenticationAgent
BluetoothDisconnectDevice
BluetoothDisplayDeviceProperties
BluetoothEnableDiscovery
BluetoothEnableIncomingConnections
BluetoothEnumerateInstalledServices
BluetoothFindBrowseGroupClose
BluetoothFindClassIdClose
BluetoothFindDeviceClose
BluetoothFindFirstBrowseGroup
BluetoothFindFirstClassId
BluetoothFindFirstDevice
BluetoothFindFirstProfileDescriptor
BluetoothFindFirstProtocolDescriptorStack
BluetoothFindFirstProtocolEntry
BluetoothFindFirstRadio
BluetoothFindFirstService
BluetoothFindFirstServiceEx
BluetoothFindNextBrowseGroup
BluetoothFindNex<PERSON>ClassId
BluetoothFindNextDevice
BluetoothFindNextProfileDescriptor
BluetoothFindNextProtocolDescriptorStack
BluetoothFindNextProtocolEntry
BluetoothFindNextRadio
BluetoothFindNextService
BluetoothFindProfileDescriptorClose
BluetoothFindProtocolDescriptorStackClose
BluetoothFindProtocolEntryClose
BluetoothFindRadioClose
BluetoothFindServiceClose
BluetoothGetDeviceInfo
BluetoothGetRadioInfo
BluetoothIsConnectable
BluetoothIsDiscoverable
BluetoothIsVersionAvailable
BluetoothMapClassOfDeviceToImageIndex
BluetoothMapClassOfDeviceToString
BluetoothRegisterForAuthentication
BluetoothRegisterForAuthenticationEx
BluetoothRemoveDevice
BluetoothSdpEnumAttributes
BluetoothSdpGetAttributeValue
BluetoothSdpGetContainerElementData
BluetoothSdpGetElementData
BluetoothSdpGetString
BluetoothSelectDevices
BluetoothSelectDevicesFree
BluetoothSendAuthenticationResponse
BluetoothSendAuthenticationResponseEx
BluetoothSetLocalServiceInfo
BluetoothSetServiceState
BluetoothUnregisterAuthentication
BluetoothUpdateDeviceRecord
BthpEnableAllServices
BthpFindPnpInfo
BthpMapStatusToErr
CPlApplet
