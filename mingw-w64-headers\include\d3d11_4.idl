/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
*/

import "oaidl.idl";
import "ocidl.idl";
import "dxgi1_5.idl";
import "d3dcommon.idl";
import "d3d11_3.idl";

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS4
{
    BOOL ExtendedNV12SharedTextureSupported;
} D3D11_FEATURE_DATA_D3D11_OPTIONS4;

[
    uuid(8992ab71-02e6-4b8d-ba48-b056dcda42c4),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Device4 : ID3D11Device3
{
    HRESULT RegisterDeviceRemovedEvent(
        [in] HANDLE event,
        [out] DWORD *cookie
    );
    void UnregisterDeviceRemoved(
        [in] DWORD cookie
    );
}

[
    uuid(8ffde202-a0e7-45df-9e01-e837801b5ea0),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Device5 : ID3D11Device4
{
    HRESULT OpenSharedFence(
        [in] HANDLE handle,
        [in] REFIID iid,
        [out] void **fence
    );
    HRESULT CreateFence(
        [in] UINT64 initial_value,
        [in] D3D11_FENCE_FLAG flags,
        [in] REFIID iid,
        [out] void **fence
    );
}

[
    uuid(9b7e4e00-342c-4106-a19f-4f2704f689f0),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11Multithread : IUnknown
{
    void Enter();
    void Leave();
    BOOL SetMultithreadProtected(
        [in] BOOL enable
    );
    BOOL GetMultithreadProtected();
}

[
    uuid(c4e7374c-6243-4d1b-ae87-52b4f740e261),
    object,
    local,
    pointer_default(unique)
]
interface ID3D11VideoContext2 : ID3D11VideoContext1
{
    void VideoProcessorSetOutputHDRMetaData(
        [in] ID3D11VideoProcessor *processor,
        [in] DXGI_HDR_METADATA_TYPE type,
        [in] UINT size,
        [in] const void *meta_data
    );
    void VideoProcessorGetOutputHDRMetaData(
        [in] ID3D11VideoProcessor *processor,
        [out] DXGI_HDR_METADATA_TYPE *type,
        [in] UINT size,
        [out] void *meta_data
    );

    void VideoProcessorSetStreamHDRMetaData(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_index,
        [in] DXGI_HDR_METADATA_TYPE type,
        [in] UINT size,
        [in] const void *meta_data
    );

    void VideoProcessorGetStreamHDRMetaData(
        [in] ID3D11VideoProcessor *processor,
        [in] UINT stream_index,
        [out] DXGI_HDR_METADATA_TYPE *type,
        [in] UINT size,
        [out] void *meta_data
    );
}
